<!--
 * @Description: layout
 * @Version: 1.0
 * @Autor: zhao<PERSON><PERSON>
 * @Date: 2022-12-02 17:41:37
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-03-05 14:16:40
-->
<template>
	<div
		:class="classObj"
		class="app-wrapper"
		:style="topBg ? `backgroundImage:url(${urlHttp(topBg)})` : 'background: #edf1fa'"
	>
		<component :is="componentsName" :page-key="pageKey"></component>
		<flotAi v-if="!isHideFloatAi"></flotAi>
		<password ref="password" />
	</div>
</template>

<script>
import { modelOne, modelTwo, modelThree, modelOmip, modelFive } from './components';
import ResizeMixin from './mixin/resize-handler';
import { mapGetters, mapState } from 'vuex';
import flotAi from '@/components/flot-ai';
import Password from '@/layout/components/setting/password.vue';
import { CoosEventTypes } from '@/utils/bus';
import { getListHeader } from '@/api/modules/coos';
import md5 from 'crypto-js/md5';
import { getItem } from '@/wile-fire/ui/util/storageHelper';
import { getDictionary } from '@/utils/data-dictionary';
import { urlHttp } from '@/utils';
import { getSessionItem } from '@/utils/localstorage'; // 所有智能模式的快捷入口
export default {
	name: 'Layout',
	components: {
		modelOne,
		modelTwo,
		modelThree,
		modelOmip,
		modelFive,
		Password,
		flotAi
	},
	mixins: [ResizeMixin],
	data() {
		return {
			pageKey: 0
		};
	},
	computed: {
		...mapGetters([
			'clientSystemMode',
			'sidebar',
			'menuList',
			'currentLevelMenu',
			'systemInfo',
			'userInfo'
		]),
		...mapState('app', ['sidebar', 'device']),
		/**顶部背景图片*/
		topBg() {
			return this.systemInfo.main.topBg || '';
		},
		componentsName() {
			let mode = 'modelOne';

			switch (this.clientSystemMode) {
				case '1':
					mode = 'modelOne';
					break;
				case '2':
					mode = 'modelTwo';
					break;
				case '3':
					mode = 'modelThree';
					break;
				case '4':
					mode = 'modelOmip';
					break;
				case '5':
					mode = 'modelFive';
					break;
			}
			return mode;
		},
		classObj() {
			return {
				hideSidebar: !this.sidebar.opened,
				openSidebar: this.sidebar.opened,
				withoutAnimation: this.sidebar.withoutAnimation,
				mobile: this.device === 'mobile'
			};
		},
		// 是否隐藏悬浮按钮
		isHideFloatAi() {
			return !!this.$route.query.hideFloatAi || getSessionItem('hideFloatAi');
		}
	},
	watch: {
		$route(newVal) {
			if (newVal.query.chatId) {
				this.init();
			} else if (newVal.query.aiType === getDictionary('AI编码/智答私库')) {
				this._BUS.$emit(CoosEventTypes.aiSessionParams, {
					coosType: 'space',
					coosTypeCode: getDictionary('AI编码/智答私库'),
					applicationId: getDictionary('应用ID/文档'),
					modeType: 'zdbk',
					canDbClick: true
				});
			}
		}
	},
	mounted() {
		//  强制修改密码
		let forceEditInitPwd = localStorage.getItem('forceEditInitPwd');
		if (forceEditInitPwd && forceEditInitPwd == 'true') {
			this.$nextTick(() => {
				this.$refs.password.open();
			});
		}
		if (this.$route.query.chatId) {
			this.init();
		}
		this._BUS.$on(CoosEventTypes.updatePage, this.updatePage);
	},
	destroyed() {
		this._BUS.$off(CoosEventTypes.updatePage, this.updatePage);
	},
	methods: {
		urlHttp,
		/**切换租户等的时候更新数据*/
		updatePage() {
			this.pageKey += 1;
		},
		init() {
			let ids = this.$route.query.chatId ? this.$route.query.chatId.split(',') : []; // 有表id解析成数组
			this.initListData(ids);
		},
		/**初始化对话的列表结构*/
		async initListData(ids) {
			try {
				let res = await getListHeader(ids);
				if (res.code !== 200) {
					throw new Error(res.message);
				}
				let arr = [];
				let relation = '';
				res.result.forEach(item => {
					relation += item.relation || '';
					arr.push({
						tableName: item.tableName || '',
						alias: item.alias || '',
						fields: item.fieldsDesc || ''
					});
				});
				let url = decodeURIComponent(this.$route.fullPath);
				let moduleId = md5(url).toString();
				let chatObjectId = moduleId;
				let listParams = {
					tableConfig: JSON.stringify(arr), // 表结构
					relation, // 表关系
					sessionId: moduleId,
					moduleId, // 保存会话记录的依据
					createBy: this.userInfo.id, // 创建人
					tenantId: getItem('tenantId') // 租户
				};
				let otherParams = {
					type: this.$route.query.type || 1, //动态表单固定是2
					applicationId: this.$route.query.applicationId || '',
					params: {
						userId: this.userInfo.id,
						userName: this.userInfo.realname,
						tenantId: getItem('tenantId')
					}
				}; // 其它参数
				this._BUS.$emit(CoosEventTypes.aiSessionParams, {
					chatObjectId,
					listParams,
					otherParams,
					ids,
					modeType: 'zklh',
					componentType: 'other-system',
					prologue: '你好，我是COOS助手，可以为你检索事务细节，助你快速决策。',
					clickEvent: 'dbClick',
					showCoos: true
				});
			} catch (err) {
				console.log(err);
				this.$message.error(err.message || '请求超时');
			}
		}
	}
};
</script>

<style lang="scss" scoped>
.app-wrapper {
	@include clearfix;
	position: relative;
	height: 100%;
	width: 100%;
	background-size: 100% 100%;
	&.mobile.openSidebar {
		position: fixed;
		top: 0;
	}
}
</style>
