<template>
	<div class="navbar">
		<Pop :dialog-visible.sync="dialogVisible" />
	</div>
</template>

<script>
import Pop from '@/layout/components/search/pop.vue';
export default {
	components: { Pop },
	data() {
		return {
			keywords: '',
			dialogVisible: false,
			downLoaded: true
		};
	},
	computed: {},
	methods: {
		search() {
			this.dialogVisible = true;
		},
		loaded(downLoaded) {
			this.downLoaded = downLoaded;
		}
	}
};
</script>

<style lang="scss" scoped>
.navbar {
	height: 12px;
	margin-left: 64px;
	width: calc(100% - 64px);
	//background: #ffffff;
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
	&-search {
		height: 30px;
		width: 300px;
		display: flex;
		// align-items: center; icon特殊性 不加反而在视觉上与文字对齐
		padding: 7px 12px;
		background: #f3f5f6;
		border-radius: 6px;
		&-icon {
			flex-shrink: 0;
			width: 16px;
			height: 16px;
			cursor: pointer;
		}
		&-input {
			flex: 1;
			height: 100%;
			width: 100%;
			border-radius: 6px;
			::v-deep .el-input__inner {
				height: 100%;
				width: 100%;
				background: #f3f5f6;
				border-radius: 6px;
				border: none;
				outline: none;
				padding: 0 0 0 4px;
				&:focus {
					box-shadow: none;
				}
				&::placeholder {
					font-size: 14px;
					font-weight: 400;
					color: $disabledTextColor;
					line-height: 22px;
				}
			}
		}
	}
}
.download-text {
	cursor: pointer;
	position: absolute;
	top: calc(50% - 10px);
	line-height: 20px;
	right: 20px;
	color: var(--brand-6);
	overflow: hidden;
}
@keyframes toBottom {
	from {
		transform: translateY(-14px);
	}
	to {
		transform: translateY(14px);
	}
}
.down-icon {
	color: var(--brand-6);
	animation: toBottom 1s infinite;
}
</style>
