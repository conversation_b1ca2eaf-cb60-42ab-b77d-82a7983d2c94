<template>
	<div
		class="main-container"
		:style="{
			marginLeft: isHideLayout ? '0px' : '88px',
			padding: isHideLayout ? '0' : '12px 12px 12px 0'
		}"
	>
		<div v-if="!isHideLayout">
			<navbar :class="{ 'fixed-header': fixedHeader }" />
			<sidebar />
		</div>
		<!-- 菜单拖动时，给内容内容区域元素不可点击，解决鼠标移入iframe时监听事件丢失或其他文字被选中问题 -->
		<AppMain
			:key="pageKey"
			:class="{
				'sidebar-drag': sidebarDrag
			}"
		/>
	</div>
</template>
<script>
import navbar from './components/navbar';
import sidebar from './components/sidebar';
import AppMain from '@/layout/components/app-main';
import { mapState } from 'vuex';
import { getSessionItem } from '@/utils/localstorage';

export default {
	name: 'ModelOne',
	components: { AppMain, sidebar, navbar },
	props: {
		pageKey: {
			type: [String, Number],
			default: () => {
				return 0;
			}
		}
	},
	data() {
		return {};
	},
	computed: {
		...mapState('app', ['sidebarDrag']),
		...mapState('settings', ['fixedHeader']),
		isHideLayout() {
			return !!this.$route.query.hideLayout || getSessionItem('hideLayout');
		}
	}
};
</script>

<style scoped lang="scss">
.sidebar-drag {
	pointer-events: none;
}
.fixed-header {
	position: fixed;
	top: 0;
	right: 0;
	z-index: 9;
	width: 100%;
	transition: width 0.1s;
}
.el-popup-parent--hidden .fixed-header {
	padding-right: 0;
}
.hideSidebar .fixed-header {
	width: 100%;
}

.mobile .fixed-header {
	width: 100%;
}
</style>
