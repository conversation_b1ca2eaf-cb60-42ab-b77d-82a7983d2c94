<!--
 * @Description: Logo
 * @Version: 1.0
 * @Autor: zhaodongming
 * @Date: 2022-12-02 17:41:37
 * @LastEditors: zhaodongming
 * @LastEditTime: 2022-12-05 15:23:02
-->
<template>
	<div class="sidebar-logo-container">
		<transition name="sidebarLogoFade">
			<router-link key="expand" class="sidebar-logo-link" to="/">
				<!--				<svg-icon icon-class="default-icon" class-name="sidebar-logo"></svg-icon>-->
				<!--				<div class="sidebar-text">{{ title }}</div>-->
				<img class="img" :src="require(`@/assets/${rentThem}/home/<USER>" alt="" />
			</router-link>
		</transition>
	</div>
</template>

<script>
export default {
	name: 'SidebarLogo',
	data() {
		return {
			title: '业务操作系统管理后台'
		};
	}
};
</script>

<style lang="scss" scoped>
.sidebar-logo-container {
	position: relative;
	width: 200px;
	height: 60px;
	line-height: 45px;
	//padding: 16px 0 16px 8px;
	padding: 19px 4px;
	text-align: center;
	overflow: hidden;
	& .sidebar-logo-link {
		display: flex;
		align-items: center;
		& .img {
			width: 100%;
			height: 22px;
		}
		& .sidebar-logo {
			width: 28px;
			height: 28px;
			margin-right: 5px;
		}
		& .sidebar-text {
			font-size: 12px;
			font-family: DOUYU, DOUYU;
			font-weight: normal;
			color: #293243;
			line-height: 22px;
			margin-top: 4px;
		}
	}
}
</style>
