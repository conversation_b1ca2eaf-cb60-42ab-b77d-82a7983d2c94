<template>
	<div
		:class="{ navbar: true, lightNavbar: systemInfo.main.mode === 'light' }"
		:style="
			systemInfo.main.topBg
				? `backgroundImage: url(${systemInfo.main.topBg})`
				: 'background: linear-gradient(90deg, #1D55E7 0%, #0086FF 100%)'
		"
	>
		<div class="navbar-left" @click="openMore">
			<FileById
				:more-style="{ borderRadius: '3px', marginRight: '7px' }"
				:size="24"
				:value="rentInfo.logoUrl"
				:default-font-icon="rentInfo.name.slice(0, 1)"
			></FileById>
			<div class="navbar-title">{{ rentInfo.name }}</div>
		</div>
		<div class="navbar-right">
			<div
				v-for="(item, index) of mainMenuList"
				:key="index"
				:class="{ select: currentMenu === index }"
				class="navbar-item"
				@click="changeMenu(index)"
			>
				<el-tooltip :open-delay="1000" effect="dark" :content="item.meta.title" placement="bottom">
					<div style="position: relative">
						<div v-if="item.iconType === 'image'" class="sidebar-item-img">
							<img :src="item.logoUrlPath" alt="" srcset="" />
						</div>
						<svg-icon v-else class="navbar-item-icon" :icon-class="item.meta.icon"></svg-icon>
						<em v-if="item.meta.title === '消息' && unread() > 0" class="badge">
							{{ unread() }}
						</em>
						<em v-if="item.meta.title === '待办' && waitDoneCount" class="badge">
							{{ waitDoneCount }}
						</em>
					</div>
				</el-tooltip>
			</div>
			<el-tooltip
				v-if="systemInfo.main.showGuidePage === 'true'"
				:open-delay="1000"
				effect="dark"
				content="回到引导页"
				placement="bottom"
			>
				<i class="coos-iconfont icon-home backGuide" @click="toGuide"></i>
			</el-tooltip>
			<div class="search" @click="search">
				<i class="coos-iconfont icon-search search-icon"></i>
			</div>
			<FileById
				:more-style="{ borderRadius: '6px', marginRight: 0 }"
				:size="28"
				class="navbar-avatar"
				:value="userInfo.avatarUrl"
				:default-font-icon="userInfo.realname.slice(-1) || '用户'"
				@click.native="changeSetting"
			></FileById>
			<div v-show="!downLoaded" class="download-text" @click="openDownload">
				<i class="el-icon-bottom down-icon"></i>
				下载列表
			</div>
			<!--  点击头像的弹窗  -->
			<setting ref="setting" :more-style="{ top: '40px', right: '10px' }"></setting>
			<!--  点击企业图标的弹窗  -->
			<rentList
				ref="rentList"
				:more-style="{
					top: '44px',
					left: '0'
				}"
			></rentList>
			<Pop :dialog-visible.sync="dialogVisible" />
			<downloadContent ref="downloadContent" @loaded="loaded"></downloadContent>
		</div>
	</div>
</template>

<script>
import Pop from '@/layout/components/search/pop.vue';
import { mapGetters, mapState } from 'vuex';
import LayoutMixin from '@/layout/mixin/share-variable';
import setting from '@/layout/components/setting/index.vue';
import rentList from '@/layout/components/rent-list/index.vue';
import downloadContent from '@/components/download-content/index.vue';
import store from '@/wile-fire/store';

export default {
	components: {
		setting,
		rentList,
		Pop,
		downloadContent
	},
	mixins: [LayoutMixin],
	data() {
		return {
			sharedConversationState: store.state.conversation,
			downLoaded: true,
			keywords: '',
			dialogVisible: false
		};
	},
	computed: {
		currentMenu() {
			let index = this.mainMenuList.findIndex(item => {
				let path = item.path;
				if (path.indexOf('/') === -1) {
					path = '/' + path;
				}
				if (this.$route.path.match('/other-system')) {
					// 其他应用时根据应用名称匹配
					return item.name === this.$route.query.name;
				}
				return path === this.$route.path || path === this.$route.fullPath;
			});
			return index;
		},
		...mapGetters(['rentInfo', 'userInfo', 'systemInfo', 'openIm']),
		...mapState('app', ['waitDoneCount', 'imCount'])
	},
	methods: {
		unread() {
			let count = 0;
			if (this.openIm === 'true') {
				this.sharedConversationState.conversationInfoList.forEach(info => {
					if (info.isSilent) {
						return;
					}
					let unreadCount = info.unreadCount;
					count += unreadCount.unread;
				});
			} else {
				count = this.imCount;
			}
			// side
			return count;
		},
		/**回到引导页*/
		toGuide() {
			this.$router.push(this.systemInfo.main.guidePageUrl);
		},
		/**打开下载*/
		openDownload() {
			this.$refs.downloadContent.open();
		},
		loaded(downLoaded) {
			this.downLoaded = downLoaded;
		},
		search() {
			this.dialogVisible = true;
		},
		/**切换菜单*/
		// changeMenu(i) {
		// 	this.currentMenu = i;
		// 	this.$router.push(
		// 		`/${this.mainMenuList[i].path}?${qs.stringify(this.mainMenuList[i].meta.query)}`
		// 	);
		// },
		/**打开设置*/
		changeSetting() {
			this.$refs.setting.open();
		},
		/**打开更多*/
		openMore() {
			this.$refs.rentList.open();
		}
	}
};
</script>

<style lang="scss" scoped>
.navbar {
	height: 44px;
	width: 100%;
	background: #ffffff;
	background-size: cover;
	@include flexBox(space-between);
	border: 1px solid #f0f0f0;
	padding: 0 20px 0 16px;

	&-left {
		@include flexBox();
		cursor: pointer;
	}

	&-right {
		@include flexBox();
	}

	&-title {
		font-size: 14px;
		font-family: Source Han Sans SC, Source Han Sans SC;
		font-weight: 500;
		color: #2c3853;
		line-height: 22px;
		white-space: nowrap;
	}

	&-center {
		@include flexBox();
	}

	.select {
		background: #f7f7f7;
	}

	&-item {
		width: 32px;
		height: 32px;
		color: $textColor;
		border-radius: 6px;
		@include flexBox();
		margin-right: 20px;
		cursor: pointer;

		&-icon {
			width: 16px;
			height: 16px;
		}
	}

	&-avatar {
		width: 28px;
		height: 28px;
		border-radius: 6px;
		cursor: pointer;
	}
}

.lightNavbar {
	// color: #ffffff;
	.navbar-title {
		color: #ffffff;
	}

	.select {
		background-color: var(--brand-2, #cfeaff);
	}

	.navbar-item {
		color: #ffffff;
	}
}

.download-text {
	cursor: pointer;
	line-height: 20px;
	color: #ffffff;
	overflow: hidden;
	margin-left: 12px;
}

@keyframes toBottom {
	from {
		transform: translateY(-14px);
	}

	to {
		transform: translateY(14px);
	}
}

.down-icon {
	color: #ffffff;
	animation: toBottom 1s infinite;
}

.badge {
	position: absolute;
	color: white;
	font-size: 10px;
	background-color: red;
	border-radius: 8px;
	min-width: 16px;
	height: 16px;
	padding: 0 5px;
	line-height: 16px;
	font-style: normal;
	text-align: center;
	right: -10px;
	top: -10px;
	vertical-align: center;
}

.search {
	width: 32px;
	height: 32px;
	margin-right: 20px;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	&-icon {
		color: #ffffff;
		font-size: 20px;
	}
}
.backGuide {
	font-size: 20px;
	margin: 0 20px 4px 0;
	cursor: pointer;
	color: #ffffff;
}
.sidebar-item-img {
	width: 20px;
	height: 20px;
	margin: 0 auto 8px auto;
	img {
		width: 100%;
		height: 100%;
	}
}
</style>
