<script>
import { CoosEventTypes } from '@/utils/bus';

export default {
	name: 'MenuItem',
	functional: true,
	props: {
		icon: {
			type: String,
			default: ''
		},
		title: {
			type: String,
			default: ''
		},
		endIcon: {
			type: String,
			default: ''
		},
		role: {
			type: Object,
			default: () => {
				return {};
			}
		}
	},
	methods: {
		click() {
			console.log(666);
		}
	},
	render(h, context) {
		const { icon, title, endIcon, role } = context.props;
		const vnodes = [];
		if (icon) {
			if (icon.includes('el-icon')) {
				vnodes.push(<i class={[icon, 'sub-el-icon']} />); // elementICON
			} else if (icon.includes('icon-')) {
				vnodes.push(<i class={['coos-iconfont', icon]} />); // aliICON
			} else if (icon.includes('ant-design:')) {
				vnodes.push(<svg-icon icon-class="default-icon" />); // antDesignICON
			} else {
				vnodes.push(<svg-icon icon-class={icon} />); // svgICON
			}
		}

		if (title) {
			vnodes.push(
				// <el-tooltip content={title} placement="right">
				<span class="menu-title">{title}</span>
				// </el-tooltip>
			);
		}

		if (endIcon) {
			const clickEndIcon = () => {
				context.parent._BUS.$emit(CoosEventTypes.clickEndIcon, title);
			};
			vnodes.push(
				<i v-role={role} class={[endIcon, 'coos-iconfont', 'icon-font']} onclick={clickEndIcon} />
			);
		}
		return vnodes;
	}
};
</script>

<style scoped lang="scss">
.sub-el-icon {
	color: currentColor;
	width: 1em;
	height: 1em;
}
.icon-font {
	width: 16px;
	height: 16px;
	position: absolute;
	top: 10px;
	right: 6px;
}
.menu-title {
	display: inline-block;
	max-width: 100%;
	@include aLineEllipse;
}
</style>
