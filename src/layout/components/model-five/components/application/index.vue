<template>
	<el-popover
		v-model="visible"
		placement="right-end"
		width="685"
		trigger="hover"
		popper-class="applications-popper"
		:visible-arrow="false"
	>
		<div class="applications">
			<ul v-for="item in list" :key="item.id" class="items" @click="open(item)">
				<img v-if="item.logoUrlPath" :src="item.logoUrlPath" alt="" />
				<svg-icon v-else icon-class="default-icon" class="list-item-top-img"></svg-icon>
				<div>{{ item.name }}</div>
			</ul>
		</div>
		<svg-icon
			slot="reference"
			class="more-menu"
			:icon-class="iconName"
			@mouseover="iconName = 'dashboard-s'"
			@mouseleave="iconName = 'dashboard'"
		></svg-icon>
	</el-popover>
</template>
<script>
import { applications } from '@/api/modules/login';
import { CoosEventTypes } from '@/utils/bus';
import { preUrl } from '@/config';
export default {
	name: 'Applications',
	data() {
		return {
			list: [],
			assetsUrlPre: preUrl,
			visible: false,
			iconName: 'dashboard'
		};
	},
	created() {
		this.getList();
	},
	mounted() {
		this._BUS.$on(CoosEventTypes.reloadProject, this.getList);
	},
	destroyed() {
		this._BUS.$off(CoosEventTypes.reloadProject, this.getList);
	},
	methods: {
		getList() {
			applications().then(res => {
				this.list = res.result;
			});
		},
		open(data) {
			if (data.openType === 1) {
				// 外部跳转
				if (data.isExternal) {
					let mainUrl = /http/gi.test(data.mainUrl)
						? data.mainUrl
						: (this.assetsUrlPre || window.location.origin) + data.mainUrl;
					this.$router.push({
						path: `/other-system?url=${encodeURIComponent(mainUrl)}`,
						query: {
							name: data.name,
							logoUrlPath: data.logoUrlPath,
							isMenuTab: true // 缩略图标在左侧菜单栏
						}
					});
				} else {
					this.$router.push({
						path: data.mainUrl,
						query: {
							name: data.name,
							logoUrlPath: data.logoUrlPath,
							isMenuTab: true // 缩略图标在左侧菜单栏
						}
					});
				}
			} else {
				window.open(data.mainUrl);
			}
			this.visible = false;
		},
		goApp(data) {
			console.log(data);
			if (this.startsWithHttp(data.mainUrl)) {
				window.open(data.mainUrl, '_blank');
			} else {
				this.$router.push({ path: data.mainUrl });
			}
		},
		startsWithHttp(str) {
			return /^http/.test(str);
		}
	}
};
</script>
<style lang="scss">
.el-popover[x-placement^='right'].applications-popper {
	min-height: 100px;
	background: linear-gradient(var(--brand-1) 0%, rgb(250, 252, 255) 100%);
	margin-left: 40px;
}
</style>
<style lang="scss" scoped>
.more-menu {
	margin: 15px 40px 0;
	cursor: pointer;
	fill: currentColor;
	overflow: hidden;
	color: var(--brand-3);
	&:hover {
		color: var(--brand-6);
	}
}

.applications {
	display: flex;
	flex-wrap: wrap;
	position: relative;
	z-index: 8;
	.items {
		width: 110px;
		text-align: center;
		padding: 10px;
		border-radius: 9px;
		cursor: pointer;
		&:hover {
			background-color: var(--brand-3);
		}
		div {
			margin-top: 10px;
			font-size: 14px;
			color: rgba(21, 34, 76, 0.9);
		}
		img {
			width: 40px;
			height: 40px;
		}
		.list-item-top-img {
			width: 40px;
			height: 40px;
			border-radius: 5px;
			margin-bottom: -3px;
		}
	}
}
</style>
