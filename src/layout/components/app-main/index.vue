<template>
	<section
		class="app-main"
		:style="{ borderRadius: clientSystemMode === '1' || !showMenuTab ? '12px' : '0 0 12px 12px' }"
	>
		<!--  单体模式并且需要加入标签的路由  -->
		<menuTabs v-if="clientSystemMode === '2' && showMenuTab && !isHideLayout"></menuTabs>
		<pageTabs v-if="showPageTab && !isHideLayout"></pageTabs>
		<div
			:key="updatePageKey"
			class="app-main-page"
			:style="clientSystemMode !== '1' && !showPageTab ? 'border-radius:12px' : ''"
		>
			<!--   一个过渡动画标签下面只能有一个节点，所以这里有两个
             v-if加在keep-alive上会导致缓存的路由直接被杀掉
             v-show会渲染出两个相同路由
                -->
			<transition :appear="true" name="fade-transform" mode="out-in">
				<keep-alive>
					<router-view v-if="$route.meta && $route.meta.keepAlive" :key="key" />
				</keep-alive>
			</transition>
			<transition
				v-if="!$route.meta || !$route.meta.keepAlive"
				:appear="true"
				name="fade-transform"
				mode="out-in"
			>
				<router-view />
			</transition>
		</div>
	</section>
</template>

<script>
import pageTabs from '@/components/page-tabs';
import menuTabs from '@/components/menu-tabs';
import { mapGetters } from 'vuex';
import { CoosEventTypes } from '@/utils/bus';
import { getSessionItem } from '@/utils/localstorage';
export default {
	name: 'AppMain',
	components: {
		pageTabs,
		menuTabs
	},
	data() {
		return {
			updatePageKey: 0
		};
	},
	computed: {
		...mapGetters(['clientSystemMode']),
		key() {
			return this.$route.path;
		},
		isHideLayout() {
			return !!this.$route.query.hideLayout || getSessionItem('hideLayout');
		},
		showPageTab() {
			return !!this.$route.meta.isPageTab;
		},
		showMenuTab() {
			return !!this.$route.meta.isMenuTab;
		}
	},
	mounted() {
		this._BUS.$on(CoosEventTypes.updateCurrentPage, this.updateCurrentPage);
	},
	destroyed() {
		this._BUS.$off(CoosEventTypes.updateCurrentPage, this.updateCurrentPage);
	},
	methods: {
		updateCurrentPage() {
			this.updatePageKey += 1;
		}
	}
};
</script>

<style scoped>
.app-main {
	/*50 = navbar  */
	height: 100%;
	width: 100%;
	position: relative;
	overflow: hidden;
	display: flex;
	flex-direction: column;
}
.app-main-page {
	flex: 1;
	overflow: hidden;
}
.fixed-header + .app-main {
	padding-top: 60px;
}
</style>

<style lang="scss">
// fix css style bug in open el-dialog
.el-popup-parent--hidden {
	.fixed-header {
		padding-right: 15px;
	}
}
</style>
