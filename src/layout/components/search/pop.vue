<template>
	<div>
		<el-dialog
			:visible="dialogVisible"
			top="44px"
			width="80%"
			:close-on-click-modal="false"
			class="search-dialog"
			:modal-append-to-body="false"
			:before-close="beforeclose"
		>
			<div class="search-dialog-navbar">
				<i class="coos-iconfont icon-search search-dialog-navbar-icon" />
				<el-input
					ref="global-search"
					v-model="keywords"
					class="search-dialog-navbar-input"
					placeholder="请输入关键字搜索"
					@input="searchResult()"
				/>
				<i
					class="coos-iconfont icon-guanbi1 search-dialog-navbar-icon"
					style="color: #b9bdc9"
					@click="clearSearch()"
				/>
			</div>
			<div class="search-dialog-body">
				<div class="tabs">
					<span
						class="tabs-item"
						:class="{ clicked: activeName == defaultName }"
						@click="handleChoice(defaultName)"
					>
						{{ defaultName }}
					</span>
					<span
						v-for="item in tabsList"
						:key="item.accessName"
						class="tabs-item"
						:class="{ clicked: activeName == item.accessName }"
						@click="handleChoice(item.accessName, item.id)"
					>
						{{ item.accessName }}
					</span>
				</div>
				<div v-loading="loading">
					<div ref="container" class="list" @scroll="handleScroll">
						<!-- 综合 -->
						<comprehensive
							v-if="activeName == '综合'"
							:comprehensive-list="comprehensiveList"
							:keywords="keywords"
							:icon-type="iconType"
							@handleChoice="handleChoice"
							@beforeclose="beforeclose"
						/>
						<!-- v-if="activeName == '文档'" -->
						<document
							v-else
							:category-list="categoryList"
							:keyword="categoryPageInfo.keyword"
							:keywords="keywords"
							:total-num="totalNum"
							:icon-type="iconType"
							@beforeclose="beforeclose"
						/>
						<!--						<message v-if="activeName == '消息'" />-->
					</div>
				</div>
			</div>
		</el-dialog>
	</div>
</template>
<script>
import comprehensive from './comprehensive.vue';
// import message from './message.vue';
import document from './document.vue';
import { debounce } from '@/utils';
import { queryCategory, comprehensiveSearch, categorySearch } from '@/api/modules/search';
export default {
	components: { comprehensive, document }, //message,
	props: {
		dialogVisible: {
			type: Boolean,
			default: () => false
		}
	},
	data() {
		return {
			loading: false,
			keywords: '',
			totalNum: '',
			activeName: '综合',
			defaultName: '综合',
			accessApplicationId: '',
			//标签列表
			tabsList: [],
			//综合数据
			comprehensiveList: [],
			//分类列表
			categoryList: [],
			//分类搜索参数
			categoryPageInfo: {
				keyword: '',
				pageNo: 1,
				pageSize: 10
			},
			iconType: [
				'folder',
				'doc',
				'image',
				'pp',
				'excel',
				'pdf',
				'text',
				'word',
				'unknown',
				'zip',
				'video'
			]
		};
	},
	watch: {
		dialogVisible(newVal) {
			if (newVal) {
				this.$nextTick(() => {
					this.$refs['global-search'].focus();
				});
			}
		}
	},
	mounted() {
		this.getTabsList();
		// this.getComprehensiveList();
	},
	methods: {
		//关闭弹窗
		beforeclose() {
			this.$emit('update:dialogVisible', false);
		},
		/** 清空搜索内容 */
		clearSearch() {
			this.keywords = '';
			if (this.activeName == '综合') {
				// this.getComprehensiveList();
				this.comprehensiveList = [];
			} else {
				// this.getAppointCategory(this.accessApplicationId);
				this.categoryList = [];
			}
		},
		/** tabs选择 */
		handleChoice(name, accessApplicationId) {
			this.categoryPageInfo.pageNo = 1;
			this.$refs.container.scrollTop = 0;
			this.activeName = name;
			this.categoryList = [];
			// this.keywords = '';
			this.accessApplicationId = accessApplicationId;
			if (accessApplicationId && this.keywords) {
				this.getAppointCategory(accessApplicationId);
			} else {
				this.categoryList = [];
			}
			if (this.activeName == '综合') {
				this.getComprehensiveList();
			}
		},
		/** tabs列表 */
		getTabsList() {
			queryCategory().then(res => {
				this.tabsList = res.result;
			});
		},
		/** 综合搜索 */
		getComprehensiveList() {
			this.loading = true;
			if (this.keywords) {
				comprehensiveSearch({ keyword: this.keywords }).then(res => {
					let arr = res.result;
					this.comprehensiveList = arr.map(event => {
						const { footer, records } = event;

						// 缓存转义关键词和正则表达式
						let regex;
						if (this.keywords) {
							const escapedKeywords = this.keywords.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
							regex = new RegExp(`(${escapedKeywords})`, 'g');
						}
						// 高亮替换函数
						const highlight = text => {
							if (!regex) return text;
							return text.replace(regex, '<span style="color:#0f45ea">$1</span>');
						};
						// 处理 records 数组中的 footer 字段
						const processedRecords =
							records?.map(record => {
								if (record.footer?.length) {
									const updatedFooter = record.footer.map(element => {
										return highlight(element);
									});
									return {
										...record,
										footer: updatedFooter
									};
								}
								return record;
							}) || [];
						// 处理顶层 footer 数组
						const processedFooter = footer?.map(item => highlight(item)) || [];
						return {
							...event,
							records: processedRecords,
							footer: processedFooter
						};
					});
					this.loading = false;
				});
			} else {
				this.comprehensiveList = [];
				this.loading = false;
			}
		},
		/**指定分类搜索 */
		getAppointCategory(applicationId) {
			this.loading = true;
			this.categoryPageInfo.keyword = this.keywords;
			categorySearch(applicationId, { ...this.categoryPageInfo }).then(res => {
				let arr = this.categoryList.concat(res.result.records);
				this.categoryList = arr.map(event => {
					const { footer, records } = event;

					// 缓存转义关键词和正则表达式
					let regex;
					if (this.keywords) {
						const escapedKeywords = this.keywords.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
						regex = new RegExp(`(${escapedKeywords})`, 'g');
					}
					// 高亮替换函数
					const highlight = text => {
						if (!regex) return text;
						return text.replace(regex, '<span style="color:#0f45ea">$1</span>');
					};
					// 处理 records 数组中的 footer 字段
					const processedRecords =
						records?.map(record => {
							if (record.footer?.length) {
								const updatedFooter = record.footer.map(element => {
									return highlight(element);
								});
								return {
									...record,
									footer: updatedFooter
								};
							}
							return record;
						}) || [];
					// 处理顶层 footer 数组
					const processedFooter = footer?.map(item => highlight(item)) || [];
					return {
						...event,
						records: processedRecords,
						footer: processedFooter
					};
				});
				this.totalNum = res.result.total;
				this.loading = false;
			});
		},
		/** 搜索事件 */
		// searchResult() {
		// 	if (this.keywords) {
		// 		if (this.activeName == '综合') {
		// 			this.getComprehensiveList();
		// 		} else {
		// 			this.getAppointCategory(this.accessApplicationId);
		// 		}
		// 	} else {
		// 		this.comprehensiveList = [];
		// 		this.categoryList = [];
		// 	}
		// },
		searchResult: debounce(
			function () {
				this.categoryList = [];
				this.categoryPageInfo.pageNo = 1;
				if (this.keywords) {
					if (this.activeName == '综合') {
						this.getComprehensiveList();
					} else {
						this.getAppointCategory(this.accessApplicationId);
					}
				} else {
					this.comprehensiveList = [];
				}
			},
			500,
			false
		),

		/** 滚动条触底加载更多 */
		// handleScroll() {
		// 	const container = this.$refs.container;
		// 	if (
		// 		container.clientHeight + container.scrollTop + 2 >= container.scrollHeight &&
		// 		this.activeName != '综合' &&
		// 		this.categoryList.length < this.totalNum
		// 	) {
		// 		this.categoryPageInfo.pageSize += 10;
		// 		this.getAppointCategory(this.accessApplicationId);
		// 	}
		// }
		/** 滚动条触底加载更多 */
		handleScroll: debounce(
			function () {
				const container = this.$refs.container;
				if (
					container.clientHeight + container.scrollTop + 2 >= container.scrollHeight &&
					this.activeName != '综合' &&
					this.categoryList.length < this.totalNum
				) {
					this.categoryPageInfo.pageNo += 1;
					this.getAppointCategory(this.accessApplicationId);
				}
			},
			500,
			false
		)
	}
};
</script>
<style lang="scss" scoped>
.search-dialog {
	width: 100%;
	z-index: 100000;
	::v-deep .el-dialog {
		border-radius: 16px;
		min-width: 530px;
		background: linear-gradient(180deg, #e5f3ff 0%, #ffffff 100%),
			linear-gradient(148deg, #afcaff 0%, rgba(175, 202, 255, 0) 100%),
			linear-gradient(203deg, #deffe7 0%, rgba(222, 255, 231, 0) 100%) rgba(255, 255, 255, 0.2);
		opacity: 1;
		.el-dialog__header {
			padding: 17px;
		}
		.el-dialog__body {
			padding: 37px 0px 15px 0px;
		}
	}
	&-navbar {
		display: flex;
		align-items: center;
		display: flex;
		align-items: center;
		height: 40px;
		background: #ffffff;
		border-radius: 32px;
		margin: 0px 30px;
		border: 1px solid #dce3e7;
		padding: 9px 16px;
		&-icon {
			flex-shrink: 0;
			width: 16px;
			height: 16px;
			color: var(--brand-6);
			cursor: pointer;
		}
		&-input {
			::v-deep .el-input__inner {
				height: 38px;
				font-size: 14px;
				font-weight: 400;
				// color: $holderTextColor;
				line-height: 22px;
				border: none;
				&:focus {
					box-shadow: none;
				}
			}
		}
	}
	&-body {
		margin-top: 20px;
		width: 100%;
		.tabs {
			width: 100%;
			line-height: 24px;
			color: $textColor;
			font-weight: 500;
			font-size: 16px;
			border-bottom: 1px solid #dce3e7;
			padding: 12px 37px;
			.tabs-item {
				cursor: pointer;
				margin-right: 37px;
				padding: 2px;
			}
			.clicked {
				padding-bottom: 13px;
				color: var(--brand-6);
				border-bottom: 2px solid var(--brand-6);
			}
		}
		.list {
			height: 452px;
			overflow-y: auto;
			@include scrollBar;
		}
	}
}
</style>
