<template>
	<div class="message">
		<div class="message-list">
			<div
				v-for="(item, index) in messageList"
				:key="index"
				class="message-list-item"
				:class="{ clicked: handleName == item.name }"
				@click="handleMessage(item.name)"
			>
				<div class="img">川</div>
				<div class="info">
					<div class="info-title">{{ item.name }}</div>
					<div class="info-msg">{{ item.info }}</div>
				</div>
			</div>
		</div>
		<div class="message-info">
			<div class="messgaeNum">
				<div>
					55条与"
					<span style="color: #0f45ea">开会</span>
					"相关的搜索结果
				</div>
				<div>
					进入聊天
					<i class="el-icon-arrow-right"></i>
				</div>
			</div>
			<div class="messageInfo">
				<div class="messageInfoItem">
					<div class="img">川</div>
					<div class="info">
						<div class="info-title">王宣川</div>
						<div class="info-msg">蓝湖估计得开个会员才会好点</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	props: {},
	data() {
		return {
			handleName: '王宣川',
			messageList: [
				{
					name: '王宣川',
					info: '22条相关记录'
				},
				{
					name: '黄丽',
					info: '21条相关记录'
				},
				{
					name: '研发中心沟通交流群',
					info: '19条相关记录'
				}
			]
		};
	},
	mounted() {
		this.messageList.forEach(item => {});
	},
	methods: {
		handleMessage(name) {
			this.handleName = name;
		}
	}
};
</script>

<style lang="scss" scoped>
.message {
	.img {
		width: 40px;
		height: 40px;
		background: var(--brand-6);
		border-radius: 6px;
		margin-right: 3px;
		color: #fff;
		text-align: center;
		line-height: 40px;
	}
	.info {
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		font-size: 14px;
		font-weight: 500;
		color: $primaryTextColor;
		line-height: 22px;
		.info-msg {
			font-size: 12px;
			font-weight: 400;
			color: $textColor;
			line-height: 20px;
		}
	}
	display: flex;
	width: 100%;
	height: 454px;
	&-list {
		width: 35%;
		overflow-y: auto;
		@include scrollBar;
		padding: 16px;
		border-right: 1px solid #dce3e7;

		&-item {
			border-radius: 6px;
			padding: 15px 8px;
			width: 100%;
			display: flex;
		}
		&-item:hover {
			background: linear-gradient(90deg, #d5e3ff 0%, #e8f4ff 100%, #e5f3ff 100%);
		}
		.clicked {
			background: linear-gradient(90deg, #d5e3ff 0%, #e8f4ff 100%, #e5f3ff 100%);
		}
	}
	&-info {
		width: 65%;
	}
	.message-info {
		padding: 4px 23px 20px 12px;
		.messgaeNum {
			display: flex;
			justify-content: space-between;
			padding: 12px 7px;
			font-size: 14px;
			font-weight: 500;
			color: $primaryTextColor;
			line-height: 22px;
			margin-bottom: 12px;
		}
		.messageInfo {
			.messageInfoItem {
				display: flex;
				padding: 15px 10px;
			}
		}
	}
}
</style>
