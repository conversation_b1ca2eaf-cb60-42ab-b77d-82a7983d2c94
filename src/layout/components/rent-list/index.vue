<template>
	<!--  点击企业图标的弹窗  -->
	<div
		v-show="showRentList"
		v-loading="changeRentLoading"
		class="sidebar-rentlist"
		:style="{ backgroundImage: `url(${assetsUrl}/desk-app/list-bg.png)`, ...moreStyle }"
	>
		<div
			v-for="(item, index) of rentList"
			:key="index"
			class="sidebar-rentlist-item"
			:class="{ selectRent: rentInfo.id === item.id }"
			@click="changeRent(item)"
		>
			<FileById
				:value="item.logoUrl"
				:size="26"
				:default-font-icon="item.name.slice(0, 1)"
				:more-style="{ borderRadius: '50%', border: '1px solid #F0F0F0' }"
			></FileById>
			<div class="text">{{ item.name }}</div>
		</div>
	</div>
</template>

<script>
import { mapActions, mapGetters, mapMutations } from 'vuex';
import { changeRent } from '@/api/modules/login';
import { logoutApps } from '@/utils/app-logout';
import { get_token, getToken, setCoosRentApplicationRelyToken } from '@/utils/auth';
import { relyToken } from '@/api/modules/login';
import { assetsUrl } from '@/config';
import { CoosEventTypes } from '@/utils/bus';

export default {
	name: 'Index',
	props: {
		moreStyle: {
			type: Object,
			default: () => {
				return {
					bottom: '20px',
					left: '70px'
				};
			}
		}
	},
	data() {
		return {
			assetsUrl,
			showRentList: false, // 显示隐藏租户列表
			cancelRentList: true, // 是否点击空白处关闭列表
			changeRentLoading: false
		};
	},
	computed: {
		...mapGetters(['rentList', 'rentInfo', 'userInfo', 'systemInfo'])
	},
	watch: {
		showRentList(newVal) {
			this._BUS.$emit(CoosEventTypes.changeMask, newVal);
		}
	},
	mounted() {
		window.addEventListener('click', this.clickEmpty);
	},
	beforeDestroy() {
		window.removeEventListener('click', this.clickEmpty);
	},
	methods: {
		...mapMutations('user', [
			'SET_OPEN_IM',
			'SET_RENT_INFO',
			'SET_USER_VERSION',
			'UPDATE_USERINFO'
		]),
		...mapMutations('settings', ['SET_MODE']),
		...mapMutations('app', ['SET_SYSTEM_INFO']),
		...mapActions('settings', ['GET_MENU', 'GET_APP_ROLES']),
		open() {
			this.cancelRentList = false;
			this.showRentList = true;
			setTimeout(() => {
				this.cancelRentList = true;
			}, 0);
		},
		/**点击空白处取消*/
		clickEmpty() {
			if (this.cancelRentList) {
				this.showRentList = false;
			}
		},
		/**切换租户*/
		changeRent(item) {
			if (this.rentInfo.id === item.id) return;
			this.changeRentLoading = true;
			this.cancelRentList = false;
			this.$confirm('确定切换租户吗?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			})
				.then(async () => {
					logoutApps({
						userId: this.userInfo.id,
						token: getToken(),
						tenantId: get_token('X-Coos-Client-Tenant-Id')
					});
					changeRent(item.id).then(async res => {
						this.changeRentLoading = false;
						if (res.code === 200) {
							this.showRentList = false;
							this.SET_RENT_INFO(item);
							this.UPDATE_USERINFO(res.result.userInfo);
							this.SET_USER_VERSION(res.result.version); // 更新用户版本
							this.SET_MODE(res.result.clientSystemMode); // 存储框架模式
							this.SET_SYSTEM_INFO(JSON.parse(res.result.tenantExtend).clientBasic); // 存储系统信息
							await this.GET_APP_ROLES(); // 获取权限
							this.SET_OPEN_IM(res.result.openIm); // 存储是否打开im
							await this.getRToken(res.result?.userInfo?.loginTenantId || '');
							this.cancelRentList = true;
							// 如果有引导页就跳转引导页
							if (this.systemInfo?.main?.showGuidePage === 'true') {
								let url = this.systemInfo?.main?.guidePageUrl;
								url = url
									? url + `?redirect=${encodeURIComponent(this.$route.fullPath)}&&noLogin=${true}`
									: '/';
								this.$router.replace(url);
							} else {
								await this.GET_MENU(); // 没有引导页的时候正常获取菜单
								// window.location.reload();// 以前是重载页面进行数据更新
								this._BUS.$emit(CoosEventTypes.reloadProject, true); // 优化重载带来的等待交互
							}
						} else {
							this.$message.error(res.message);
						}
					});
				})
				.catch(() => {
					this.changeRentLoading = false;
					this.cancelRentList = true;
				});
		},
		// 获取token
		async getRToken(tenantId) {
			let res = await relyToken(tenantId);
			if (res.code === 200) {
				setCoosRentApplicationRelyToken(res.result);
			} else {
				this.$message.error(res.message);
			}
		}
	}
};
</script>

<style scoped lang="scss">
//租户列表弹窗
.sidebar-rentlist {
	width: 246px;
	padding: 10px 11px;
	background-repeat: no-repeat;
	background-size: 100% 98px;
	background-color: #ffffff;
	box-shadow: 0px 2px 6px -1px rgba(0, 0, 0, 0.05), 0px 4px 8px 0px rgba(0, 0, 0, 0.06),
		0px -4px 14px 0px rgba(0, 0, 0, 0.03);
	border-radius: 9px;
	position: absolute;
	z-index: 999;
	&-item {
		cursor: pointer;
		width: 100%;
		padding: 5px 6px;
		background: rgba(255, 255, 255, 0.47);
		border-radius: 6px;
		font-size: 12px;
		font-weight: 400;
		color: #000000;
		line-height: 20px;
		margin-bottom: 4px;
		@include flexBox(flex-start);
		.text {
			@include aLineEllipse;
		}
	}
}
.selectRent {
	background: var(--brand-1);
}
</style>
