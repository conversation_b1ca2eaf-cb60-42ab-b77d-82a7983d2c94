<template>
	<el-dialog
		title="修改头像"
		:visible.sync="editAvatarDialog"
		:close-on-click-modal="false"
		class="desk-el-custom-dialog"
		:modal-append-to-body="false"
		top="0"
		width="40%"
	>
		<el-row type="flex" justify="center">
			<div class="cropper">
				<vueCropper
					ref="cropper"
					:img="option.img"
					:output-size="option.size"
					:output-type="option.outputType"
					:info="option.info"
					:full="option.full"
					:can-move="option.canMove"
					:can-move-box="option.canMoveBox"
					:original="option.original"
					:auto-crop="option.autoCrop"
					:auto-crop-width="option.autoCropWidth"
					:auto-crop-height="option.autoCropHeight"
					:fixed-box="option.fixedBox"
					:fixed="option.fixed"
					@realTime="realTime"
				></vueCropper>
			</div>
			<div class="previewBox">
				<div class="previewImgBox">
					<div
						v-if="previews.div && previews.url"
						:style="{
							...previews.div,
							transform: `scale(${150 / parseInt(previews.div.height.replace('px', ''))})`
						}"
						class="preview"
					>
						<img :src="previews.url" :style="previews.img" />
					</div>
					<FileById
						v-else
						:default-font-icon="userInfo.realname.slice(-1) || '用户'"
						:more-style="{ marginRight: 0, borderRadius: '9px', fontSize: '36px' }"
						:size="150"
					></FileById>
				</div>
				<el-row class="change-button" type="flex" justify="center">
					<el-upload action="" :show-file-list="false" :auto-upload="false" :on-change="uploadImg">
						<el-button size="mini" type="primary">更换头像</el-button>
					</el-upload>
				</el-row>
				<br />
				<el-row>
					<el-button icon="el-icon-plus" circle size="mini" @click="changeScale(1)"></el-button>
					<el-button icon="el-icon-minus" circle size="mini" @click="changeScale(-1)"></el-button>
					<el-button icon="el-icon-download" circle size="mini" @click="down('blob')"></el-button>
					<el-button icon="el-icon-refresh-left" circle size="mini" @click="rotateLeft"></el-button>
					<el-button
						icon="el-icon-refresh-right"
						circle
						size="mini"
						@click="rotateRight"
					></el-button>
				</el-row>
			</div>
		</el-row>
		<span slot="footer" class="dialog-footer">
			<el-button @click="cale">取 消</el-button>
			<el-button v-loading="sureLoading" type="primary" @click="saveEditAvatar">确 定</el-button>
		</span>
	</el-dialog>
</template>

<script>
import { VueCropper } from 'vue-cropper';
import { mapGetters, mapMutations } from 'vuex';
import { customUploadFile } from '@/api/modules/component';
import { updateInfo } from '@/api/modules/login';
import { serveUrl, proxy } from '@/config';
const { customUploadFileHandler } = customUploadFile();
export default {
	name: 'Avatar',
	components: {
		VueCropper
	},
	data() {
		return {
			editAvatarDialog: false,
			avatarURL: '',
			previews: {},
			sureLoading: false,
			option: {
				img: null, // 裁剪图片的地址
				info: true, // 裁剪框的大小信息
				outputSize: 1, // 剪切后的图片质量（0.1-1）
				full: true, // 输出原图比例截图 props名full
				outputType: 'png', // 裁剪生成额图片的格式
				canMove: true, // 能否拖动图片
				original: false, // 上传图片是否显示原始宽高
				canMoveBox: true, // 能否拖动截图框
				autoCrop: true, // 是否默认生成截图框
				autoCropWidth: 150,
				autoCropHeight: 150,
				fixed: true, // 截图框固定比例
				fixedBox: false // 截图框固定大小
			}
		};
	},
	computed: {
		...mapGetters(['userInfo'])
	},
	watch: {
		editAvatarDialog(newVal) {
			if (newVal) {
				this.initAvatar();
			}
		}
	},
	methods: {
		...mapMutations('user', ['UPDATE_USERINFO']),
		initAvatar() {
			this.$set(
				this.option,
				'img',
				this.userInfo.avatarUrl ? this.userInfo.avatarUrl.replace(serveUrl, proxy) : ''
			);
		},
		/**保存头像修改*/
		saveEditAvatar() {
			this.finish('blob');
		},
		/**放大/缩小*/
		changeScale(num) {
			num = num || 1;
			this.$refs.cropper.changeScale(num);
		},
		/**左旋转*/
		rotateLeft() {
			this.$refs.cropper.rotateLeft();
		},
		/**右旋转*/
		rotateRight() {
			this.$refs.cropper.rotateRight();
		},
		/**保存上传图片*/
		finish(type) {
			if (this.sureLoading) return;
			this.sureLoading = true;
			if (type === 'blob') {
				this.$refs.cropper.getCropBlob(data => {
					console.log(data);
					this.avatarURL = window.URL.createObjectURL(data);
					console.log('data.type', data.type);
					let file = new File([data], 'avatar.png', { type: data.type });
					// 上传头像
					customUploadFileHandler({ file }).then(res => {
						if (res.code === 200) {
							// 上传成功后更新用户信息
							updateInfo({ avatar: res.result.fileId }).then(r => {
								this.sureLoading = false;
								if (r.code === 200) {
									this.$message.success('更新成功！');
									let newInfo = { avatar: res.result.fileId, avatarUrl: res.result.fileUrl };
									this.UPDATE_USERINFO(newInfo);
									this.editAvatarDialog = false;
								} else {
									this.$message.error(res.message);
								}
							});
						} else {
							this.$message.error(res.message);
						}
					});
					//访问接口保存到数据库写这儿!
				});
			} else {
				this.$refs.cropper.getCropData(data => {
					//访问接口保存到数据库写这儿!
				});
			}
		},
		/**实时预览函数*/
		realTime(data) {
			this.previews = data;
			this.loading = false;
		},
		/**下载图片*/
		down(type) {
			let aLink = document.createElement('a');
			aLink.download = 'author-img';
			if (type === 'blob') {
				this.$refs.cropper.getCropBlob(data => {
					aLink.href = window.URL.createObjectURL(data);
					aLink.click();
				});
			} else {
				this.$refs.cropper.getCropData(data => {
					aLink.href = data;
					aLink.click();
				});
			}
		},
		/**更换头像--上传本地图片*/
		uploadImg(file) {
			let _this = this;
			let reader = new FileReader();
			reader.onload = e => {
				let data;
				if (typeof e.target.result === 'object') {
					// 把Array Buffer转化为blob 如果是base64不需要
					data = window.URL.createObjectURL(new Blob([e.target.result]));
				} else {
					data = e.target.result;
				}
				_this.option.img = data;
			};
			// 转化为base64
			// reader.readAsDataURL(file.raw)
			// 转化为blob
			reader.readAsArrayBuffer(file.raw);
		},
		/**打开弹窗*/
		open() {
			this.editAvatarDialog = true;
		},
		/**关闭弹窗*/
		cale() {
			this.editAvatarDialog = false;
		}
	}
};
</script>

<style scoped lang="scss">
.previewBox {
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: center;
}

.previewImgBox {
	width: 150px;
	height: 150px;
	overflow: hidden;
}

.preview {
	border-radius: 9px;
	border: 1px solid #ccc;
	background-color: #ccc;
	overflow: hidden;
	transform-origin: 0 0;
}

.change-button {
	margin: 20px 0 10px;
}

.cropper {
	width: 260px;
	height: 260px;
	flex-shrink: 0;
}
</style>
