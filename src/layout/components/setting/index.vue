<template>
	<div v-loading="switchIdentityLoading" class="content" :style="moreStyle">
		<div
			v-show="showSetting"
			v-loading="logoutLoading"
			class="sidebar-user-more"
			:style="{ backgroundImage: `url(${assetsUrl}/desk-app/list-bg.png)` }"
			@click.stop
		>
			<div class="userinfo">
				<div class="userinfo-name">
					<FileById
						:value="userInfo.avatarUrl"
						:default-font-icon="userInfo.realname.slice(-1) || '用户'"
						:more-style="{ borderRadius: '6px' }"
						:size="60"
					></FileById>
					<div style="flex: 1; overflow: hidden">
						<el-tooltip
							:open-delay="1000"
							effect="dark"
							:content="userInfo.realname"
							placement="bottom"
						>
							<div class="name">{{ userInfo.realname }}</div>
						</el-tooltip>
						<div class="depart">{{ rentInfo.departNames || rentInfo.name }}</div>
						<!--身份切换 START-->

						<div v-if="userVersion === 'v2'" class="switch-identity">
							<el-tooltip
								class="item"
								effect="dark"
								:content="setText()"
								placement="top"
								:disabled="setText().length < 10"
							>
								<el-select
									v-model="currentIdentity"
									style="width: 100%"
									size="small"
									class="switch-identity-select"
									@change="handleSwitchIdentify"
								>
									<template #prefix>
										<i class="coos-iconfont icon-shenfen"></i>
									</template>
									<el-option
										v-for="item in userInfo.userIdentities"
										:key="item.id"
										:label="`${item.orgShort ? `${item.orgShort}-` : `${item.orgName}-`}${
											item.departName ? `${item.departShort}` : `${item.departName}`
										}${item.jobTitle ? `-${item.jobTitle}` : ''}`"
										:value="item.id"
									/>
								</el-select>
							</el-tooltip>
						</div>

						<!--身份切换 END-->
					</div>
				</div>
				<!--				<div class="userinfo-status">-->
				<!--					<i class="coos-iconfont icon-gongzuotai icon"></i>-->
				<!--					<div class="text">会议中</div>-->
				<!--					<div class="con">持续至12：00</div>-->
				<!--					<i class="coos-iconfont icon-nav-right toIcon"></i>-->
				<!--				</div>-->
			</div>
			<!-- <div class="line"></div> -->

			<div
				v-for="item of setting1"
				:key="item.type"
				class="setting"
				@click="handleButton(item.type)"
			>
				{{ item.name }}
			</div>
			<div v-if="isWxMpSubscribeCheck != 0" class="setting setting-flex">
				<div>关注公众号</div>
				<div class="link" @click="openWxDialog">
					{{ isWxMpSubscribeCheck === 1 ? '去关注' : '变更/取消关注' }}
					<i class="el-icon-arrow-right link-icon"></i>
				</div>
			</div>
			<div class="line"></div>
			<div
				v-for="item of setting2"
				v-show="item.show"
				:key="item.type"
				class="setting"
				@click="handleButton(item.type)"
			>
				{{ item.name }}
			</div>
			<div class="line"></div>
			<div
				v-for="item of setting3"
				:key="item.type"
				class="setting"
				@click="handleButton(item.type)"
			>
				<div>{{ item.name }}</div>
				<!--				<i class="coos-iconfont icon-nav-right icon"></i>-->
			</div>
		</div>
		<updateAvatar ref="updateAvatar"></updateAvatar>
		<updatePassword
			:key="'updatePassword' + updatePasswordKey"
			ref="updatePassword"
			@cale="cale('updatePassword')"
		></updatePassword>
		<el-dialog
			:title="isWxMpSubscribeCheck === 1 ? '扫一扫完成关注' : '扫一扫进行变更/取消关注'"
			:visible.sync="dialogVisible"
			width="530px"
			:before-close="handleClose"
			append-to-body
		>
			<div v-loading="qrLoading" class="box">
				<div class="box-header">
					<!-- &#xe6df; -->
					<div class="box-header-left"></div>
					<div class="box-header-title">
						<i class="coos-iconfont icon-weixin"></i>
						{{ isWxMpSubscribeCheck === 1 ? '关注公众号' : '变更/取消关注' }}
					</div>
					<div class="box-header-right"></div>
				</div>
				<div class="box-qr">
					<el-image :src="wxMpSubscribeQrCodeUrl"></el-image>
					<div v-if="unActive" class="un-active" @click="openWxDialog">
						<img class="un-active-img" src="../../../assets/images/common/update.png" alt="" />
						<span class="un-active-button">点击刷新</span>
					</div>
				</div>
			</div>
		</el-dialog>
	</div>
</template>

<script>
import { otherSystem, assetsUrl } from '@/config';
import { get_token, getToken } from '@/utils/auth';
import { switchIdentity, wxMpSubscribeCheck, wxMpSubscribeQrCode } from '@/api/modules/login';
import { mapActions, mapGetters, mapMutations } from 'vuex';
import updateAvatar from './avatar';
import updatePassword from './password';
import { logoutApps } from '@/utils/app-logout';
import { getItem } from '@/utils/localstorage';
import { CoosEventTypes } from '@/utils/bus';

export default {
	name: 'Index',
	components: {
		updateAvatar,
		updatePassword
	},
	props: {
		moreStyle: {
			type: Object,
			default: () => {
				return {
					top: '28px',
					left: '70px'
				};
			}
		}
	},
	data() {
		return {
			assetsUrl,
			showSetting: false,
			updatePasswordKey: 0, // 弹窗的实例key
			logoutLoading: false,
			setting1: [
				// { name: '我的个人名片', type: 'myCard' },
				{ name: '修改头像', type: 'updateAvatar' },
				{ name: '修改密码', type: 'updatePassword' }
			],
			setting3: [
				// { name: '设置', type: 'setting' },
				{ name: '退出登录', type: 'logout' }
			],
			cancelSetting: true,
			currentIdentity: '', // 当前身份
			switchIdentityLoading: false, // 切换身份loading
			dialogVisible: false,
			wxMpSubscribeQrCodeUrl: '',
			qrLoading: false,
			unActive: false,
			isWxMpSubscribeCheck: 0 //微信公众号关注可操作情况(0.不需关注、1.去关注、2.更新/取消关注)
		};
	},
	computed: {
		...mapGetters(['rentInfo', 'userInfo', 'userVersion']),
		setting2() {
			return [
				{ name: '管理后台', type: 'admin', show: this.userInfo.haveAdminIdent }
				// { name: '下载手机版', type: 'download', show: true }
			];
		}
	},
	watch: {
		showSetting(newVal) {
			this._BUS.$emit(CoosEventTypes.changeMask, newVal);
		}
	},
	mounted() {
		window.addEventListener('click', this.clickEmpty);
		this.currentIdentity = this.userInfo.currentIdentityId;
		this.wxMpSubscribeCheck();
	},
	beforeDestroy() {
		window.removeEventListener('click', this.clickEmpty);
	},
	methods: {
		...mapMutations('user', ['SET_USERINFO']),
		...mapActions('user', ['LOGIN_OUT']),
		...mapActions('settings', ['GET_MENU', 'GET_APP_ROLES']),
		open() {
			this.cancelSetting = false;
			this.showSetting = true;
			setTimeout(() => {
				this.cancelSetting = true;
			}, 0);
		},
		clickEmpty() {
			if (this.cancelSetting) {
				this.showSetting = false;
			}
		},
		// 处理展示类tooltip
		setText() {
			let str = '';
			if (this.userInfo.userIdentities) {
				this.userInfo.userIdentities.forEach(item => {
					if (item.id === this.currentIdentity) {
						str = `${item.orgShort ? `${item.orgShort}-` : `${item.orgName}-`}${
							item.departName ? `${item.departShort}` : `${item.departName}`
						}${item.jobTitle ? `-${item.jobTitle}` : ''}`;
					}
				});
			}
			return str;
		},
		/**关闭弹窗*/
		cale(type) {
			this[type + 'Key'] += 1;
		},
		/**处理设置弹窗中的按钮*/
		handleButton(type) {
			switch (type) {
				case 'logout': {
					this.logout();
					break;
				}
				case 'updateAvatar': {
					this.showSetting = false;
					this.$refs.updateAvatar.open();
					break;
				}
				case 'updatePassword': {
					this.showSetting = false;
					this.$refs.updatePassword.open();
					break;
				}
				case 'admin': {
					this.showSetting = false;
					let obj = otherSystem.find(item => {
						return item.name === 'COOS租户端';
					});
					console.log(obj.url);
					window.open(`${obj.url}?token=${getToken()}`);
					break;
				}
				default: {
					console.log(type);
				}
			}
		},
		// 关闭微信绑定弹窗
		handleClose() {
			this.dialogVisible = false;
		},
		// 检查是否有公众号关注功能
		wxMpSubscribeCheck() {
			wxMpSubscribeCheck()
				.then(res => {
					this.isWxMpSubscribeCheck = res?.result || 0;
				})
				.catch(e => {
					this.isWxMpSubscribeCheck = 0;
				});
		},
		openWxDialog() {
			this.dialogVisible = true;
			this.qrLoading = true;
			this.unActive = false;
			wxMpSubscribeQrCode()
				.then(res => {
					if (res.code === 200) {
						this.wxMpSubscribeQrCodeUrl = res.result || '';
						setTimeout(() => {
							this.unActive = true;
						}, 60000);
					} else {
						this.$message.warning(res.message || '获取二维码失败');
					}
					this.qrLoading = false;
				})
				.catch(e => {
					this.qrLoading = false;
					this.wxMpSubscribeQrCodeUrl = '';
				});
		},
		/**退出*/
		logout() {
			this.cancelSetting = false;
			this.$confirm('确定退出登录吗?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			})
				.then(async () => {
					let rentId = this.rentInfo.id;
					this.logoutLoading = true;
					let res = await this.LOGIN_OUT();
					this.logoutLoading = false;
					this.cancelSetting = true;
					if (res.code === 200) {
						let haveAppOut = res.result?.haveAppOut || false;
						if (!haveAppOut) {
							//没有应用系统同步退出，则不拼接用户退出登录码
							this.$router.push(
								`/login?redirect=${encodeURIComponent(this.$route.fullPath)}&tenantId=${rentId}`
							);
							return;
						}
						this.$router.push(
							`/login?code=${res.result.code}&redirect=${encodeURIComponent(
								this.$route.fullPath
							)}&tenantId=${rentId}`
						);
					}
				})
				.catch(() => {
					this.cancelSetting = true;
				});
		},
		// 切换身份
		handleSwitchIdentify() {
			this.$confirm('确定切换身份吗?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			})
				.then(async () => {
					logoutApps({
						userId: this.userInfo.id,
						token: getToken(),
						tenantId: get_token('X-Coos-Client-Tenant-Id')
					});
					switchIdentity(this.currentIdentity)
						.then(async ({ code, message, result }) => {
							this.switchIdentityLoading = false;
							if (code === 200) {
								this.SET_USERINFO(result.userInfo);
								await this.GET_MENU(getItem('currentTheme') || ''); // 获取菜单
								await this.GET_APP_ROLES(); // 获取权限
								// window.location.reload(); // 以前是重载页面进行数据更新
								this._BUS.$emit(CoosEventTypes.reloadProject, true); // 优化重载带来的等待交互
							} else {
								this.$message.error(message);
								this.switchIdentityLoading = false;
								this.currentIdentity = this.userInfo.currentIdentityId;
							}
						})
						.catch(err => {
							this.switchIdentityLoading = false;
							this.currentIdentity = this.userInfo.currentIdentityId;
						});
				})
				.catch(() => {
					this.switchIdentityLoading = false;
					this.currentIdentity = this.userInfo.currentIdentityId;
				});
		}
	}
};
</script>

<style scoped lang="scss">
.content {
	position: absolute;
	z-index: 887;
}
.switch-identity {
	margin-top: 3px;

	::v-deep {
		.el-input__prefix {
			display: flex;
			align-items: center;
			left: 8px;
			.coos-iconfont {
				color: #ad7a04;
				font-size: 12px;
			}
		}

		.el-select .el-input .el-select__caret {
			color: #ad7a04;
			line-height: 22px;
			width: 18px;
		}
		.el-input__inner {
			height: 22px;
			padding-left: 25px;
			padding-right: 25px;
			border-radius: 3px;
			border: 1px solid transparent;
			color: #ad7a04;
			background: rgba(255, 246, 223, 0.6);
		}
	}
}
.sidebar-user-more {
	width: 280px;
	padding: 18px 12px 12px;
	border-radius: 9px;
	background-repeat: no-repeat;
	background-size: 100% 98px;
	background-color: #ffffff;
	box-shadow: 0px 2px 6px -1px rgba(0, 0, 0, 0.05), 0px 4px 8px 0px rgba(0, 0, 0, 0.06),
		0px -4px 14px 0px rgba(0, 0, 0, 0.03);

	.userinfo {
		&-name {
			@include flexBox(flex-start);

			.name {
				width: 170px;
				font-size: 16px;
				font-weight: 800;
				color: $primaryTextColor;
				line-height: 22px;
				@include aLineEllipse;
			}

			.depart {
				margin-top: 2px;
				font-size: 12px;
				font-weight: 400;
				color: $subTextColor;
				line-height: 20px;
				@include aLineEllipse;
			}
		}

		&-status {
			width: 100%;
			height: 24px;
			background: linear-gradient(97deg, #e3f8ff 0%, #f2f3ff 100%);
			border-radius: 3px;
			margin: 6px 0 3px;
			padding: 5px 25px 5px 5px;
			position: relative;
			@include flexBox(flex-start);

			.icon {
				font-size: 12px;
				color: #52c41a;
				margin-right: 5px;
			}

			.text {
				font-size: 12px;
				font-weight: 400;
				color: #52c41a;
				line-height: 20px;
				margin-right: 17px;
			}

			.con {
				font-size: 12px;
				font-weight: 400;
				color: $textColor;
				line-height: 20px;
				flex: 1;
			}

			.toIcon {
				color: #c5c5c5;
				font-size: 16px;
				position: absolute;
				right: 3px;
				top: calc(50% - 8px);
			}
		}
	}

	.line {
		height: 1px;
		background-color: #f0f0f0;
	}

	.setting {
		width: 100%;
		padding-left: 7px;
		height: 32px;
		font-size: 12px;
		font-weight: 400;
		color: #000000;
		line-height: 20px;
		position: relative;
		@include flexBox(flex-start);
		cursor: pointer;

		.icon {
			position: absolute;
			right: 3px;
			font-size: 16px;
			color: #c5c5c5;
			top: calc(50% - 8px);
		}
	}
}
::v-deep .el-input__inner {
	display: inline-block !important;

	max-width: 188px !important;

	overflow: hidden !important;

	text-overflow: ellipsis !important;

	white-space: nowrap !important;
}
.setting-flex {
	justify-content: space-between !important;
	align-items: center;
	.link {
		color: var(--brand-6);
		display: flex;
		align-items: center;
		&-icon {
			font-size: 16px;
		}
	}
}
.box {
	padding: 6px 54px 16px;

	&-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		&-left {
			width: 130px;
			height: 0px;
			border: 1px solid;
			border-image: linear-gradient(90deg, rgba(240, 240, 240, 0), rgba(230, 230, 230, 1)) 1 1;
		}
		&-title {
			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			font-size: 14px;
			color: #2f446b;
			line-height: 22px;
			display: flex;
			align-items: center;
			justify-content: space-between;
			.coos-iconfont {
				color: #09bb07;
				font-size: 18px;
				margin-right: 5px;
			}
		}
		&-right {
			width: 130px;
			height: 0px;
			border: 1px solid;
			border-image: linear-gradient(-90deg, rgba(240, 240, 240, 0), rgba(220, 227, 231, 1)) 1 1;
		}
	}
	&-qr {
		width: 260px;
		height: 260px;
		border-radius: 6px 6px 6px 6px;
		margin: 24px auto 0;
		border: 1px solid #bccadb;
		position: relative;
		.un-active {
			width: 260px;
			height: 260px;
			z-index: 888;
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			background: rgba(0, 0, 0, 0.3);
			cursor: pointer;
			&-img {
				width: 100px;
				height: 100px;
			}
			&-button {
				margin-top: 8px;
				color: #ffffff;
			}
		}
	}
}
::v-deep .el-dialog {
	border-radius: 16px !important;
}
</style>
