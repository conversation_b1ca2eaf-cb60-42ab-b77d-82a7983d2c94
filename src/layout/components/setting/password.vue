<template>
	<el-dialog
		:title="forceEditInitPwd == 'true' ? '首次登录请修改默认密码' : '修改密码'"
		:visible.sync="dialog"
		:close-on-click-modal="false"
		class="desk-el-custom-dialog"
		:modal-append-to-body="false"
		:class="{ 'sm-top': ['newPassword', 'sure'].includes(type) }"
		width="40%"
		top="0"
		@close="close"
	>
		<div class="dialog-body">
			<div class="dialog-body-title">{{ title }}</div>
			<div v-show="desc" class="dialog-body-desc">{{ desc }}</div>
			<!--  旧密码  -->
			<el-form v-if="type === 'oldPassword'" class="form">
				<el-form-item class="form-item">
					<el-input
						v-model="oldPassword"
						class="dialog-body-input"
						placeholder="输入密码"
						show-password
						autocomplete="new-password"
					></el-input>
				</el-form-item>
			</el-form>
			<!--  新密码  -->
			<div v-else-if="type === 'newPassword'">
				<el-form ref="ruleForm" class="form" :model="form" :rules="rules">
					<el-form-item class="form-item form-item-new" prop="newPassword">
						<el-input
							v-model="form.newPassword"
							class="dialog-body-input new-password-input"
							placeholder="请输入密码"
							show-password
						></el-input>
					</el-form-item>
					<el-form-item class="form-item form-item-new" prop="newRePassword">
						<el-input
							v-model="form.newRePassword"
							class="dialog-body-input new-password-input"
							placeholder="请再次输入密码"
							show-password
						></el-input>
					</el-form-item>
				</el-form>
			</div>
			<!--  验证码  -->
			<div v-else-if="type === 'code'" class="code-input">
				<el-input
					v-for="(item, index) of code"
					:ref="'el-input-' + index"
					:key="index"
					v-model="code[index]"
					class="code-input-item"
					:maxlength="1"
					@keydown.delete.native="deleteInput(index)"
					@paste.prevent.native="inputPaste"
					@input="changeFocus($event, index)"
				></el-input>
			</div>
			<!--  保存  -->
			<div v-else-if="type == 'sure'" class="sure-tip">
				<div class="sure-tip-title">
					<svg-icon icon-class="tip-icon" class="icon"></svg-icon>
					<span>提示</span>
				</div>
				<div class="sure-tip-con">
					{{
						`你正在修改登录“${userInfo.realname}”这个账号的密码。修改成功后，需使用新密码进行登录。`
					}}
				</div>
			</div>
			<!--  重置/重新发送  -->
			<div v-if="type === 'oldPassword' && forceEditInitPwd != 'true'" class="radio">
				<span class="radio-text">忘记密码？</span>
				<span v-loading="resetLoading" class="radio-btn" @click="sure(true)">点击重置</span>
			</div>
			<div
				v-else-if="type === 'code'"
				v-loading="resendLoading"
				class="radio-text"
				:class="timeCount === 60 ? 'radio-send' : ''"
			>
				<div @click="timeCount === 60 && reSendCode()">
					{{ timeCount === 60 ? '重新发送' : `${timeCount}s后可发送` }}
				</div>
				<div v-if="!noPhone" class="radio-text-type" @click="changeType">
					{{ sendType === 'phone' ? '使用邮箱验证' : '使用短信验证码验证' }}
				</div>
			</div>
		</div>
		<span slot="footer">
			<el-button v-if="type === 'sure'" @click="cale">取 消</el-button>
			<el-button v-loading="btnLoading" type="primary" @click="sure">
				{{ type === 'sure' ? '确定' : '下一步' }}
			</el-button>
		</span>
	</el-dialog>
</template>

<script>
import {
	checkCode,
	checkPassword,
	getEncryptKey,
	savePassword,
	sendCode
} from '@/api/modules/login';
import JSEncrypt from 'jsencrypt';
import { mapActions, mapGetters, mapMutations } from 'vuex';
import { validatePassword } from '@/utils/validate';

export default {
	name: 'Password',
	data() {
		return {
			phoneTime: null, // 手机验证码倒计时器
			phoneTimeCount: 60, // 手机验证码倒计时
			mailTime: null, // 邮箱验证码倒计时器
			mailTimeCount: 60, // 邮箱验证码倒计时
			sendType: 'phone', // 发送通道(phone:短信接收,mail:邮箱接收)
			noPhone: false, // 没有手机渠道登录
			verifyType: 1, //验证方式(1.旧密码，2.手机验证码)
			resendLoading: false, // 重新发送的状态
			forceEditInitPwd: false,
			form: {
				newPassword: '', // 新密码
				newRePassword: '' // 确认密码
			},
			rules: {
				newPassword: [{ validator: this.validatePass, trigger: 'change' }],
				newRePassword: [{ validator: this.validateSurePass, trigger: 'change' }]
			},
			dialog: false,
			oldPassword: '', // 旧密码
			code: ['', '', '', '', '', ''],
			btnLoading: false, // 异步按钮
			title: '输入密码',
			desc: '为了你的账号安全，修改密码前需要验证旧密码',
			resetLoading: false, // 重置按钮状态
			passwordCheck: '', // 旧密码校验结果
			type: 'oldPassword' // 旧密码oldPassword 新密码newPassword 验证码方式code 最后一步sure
		};
	},
	computed: {
		...mapGetters(['userInfo', 'rentInfo']),
		timeCount() {
			return this.sendType === 'phone' ? this.phoneTimeCount : this.mailTimeCount;
		}
	},
	watch: {
		type(newVal) {
			if (newVal === 'oldPassword') {
				this.title = '输入密码';
				this.desc = '为了你的账号安全，修改密码前需要验证旧密码';
			} else if (newVal === 'code') {
				this.title = this.sendType === 'phone' ? '输入手机号码验证码' : '输入邮箱验证码';
				this.desc = `请输入发送至：${
					this.sendType === 'phone' ? this.userInfo.phone : this.userInfo.email
				}的6位验证码，有效期10分钟`;
			} else if (newVal === 'newPassword') {
				this.title = '设置新密码';
				this.desc = '密码仅可由数字、字母或符号组成，且需要包含其中至少两种类型，长度不少于8个字符';
			} else if (newVal === 'sure') {
				this.title = '设置新密码';
				this.desc = '';
			}
		},
		sendType() {
			if (this.type === 'code') {
				this.title = this.sendType === 'phone' ? '输入手机号码验证码' : '输入邮箱验证码';
				this.desc = `请输入发送至：${
					this.sendType === 'phone' ? this.userInfo.phone : this.userInfo.email
				}的6位验证码，有效期10分钟`;
			}
		}
	},
	mounted() {
		// 判断是否有手机渠道的登录方式
		let extend = JSON.parse(this.rentInfo.extend || '{}');
		if (!extend.moreSetting?.sms?.channel) {
			this.noPhone = true;
			this.sendType = 'mail';
		}
	},
	methods: {
		...mapActions('user', ['LOGIN_OUT']),
		...mapMutations('user', ['REMOVE_INFO']),
		/**切换发送方式*/
		changeType() {
			this.sendType = this.sendType === 'phone' ? 'mail' : 'phone';
			this.reSendCode();
		},
		/**校验密码*/
		validatePass(rule, value, callback) {
			if (value === '') {
				callback(new Error('请输入密码'));
			} else if (validatePassword(value)) {
				callback();
			} else {
				callback(
					new Error('密码仅可由数字、字母或符号组成，且需要包含其中至少两种类型，长度不少于8个字符')
				);
			}
		},
		/**校验重复密码*/
		validateSurePass(rule, value, callback) {
			if (value === '') {
				callback(new Error('请确认密码'));
			} else if (value === this.form.newPassword) {
				callback();
			} else {
				callback(new Error('两次输入密码不一致！'));
			}
		},
		/**弹窗关闭时的回调*/
		close() {
			let forceEditInitPwd = localStorage.getItem('forceEditInitPwd');
			if (forceEditInitPwd && forceEditInitPwd == 'true') {
				this.$confirm('如本次不修改初始密码、将强制退出, 是否继续?', '提示', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					type: 'warning'
				})
					.then(() => {
						this.REMOVE_INFO();
						this.$router.push(`/login?redirect=${encodeURIComponent(this.$route.fullPath)}`);

						// this.dialog = false;
					})
					.catch(() => {
						this.open();
					});
			} else {
				this.$emit('cale');
			}
		},
		open() {
			let forceEditInitPwd = localStorage.getItem('forceEditInitPwd');
			this.forceEditInitPwd = forceEditInitPwd;
			this.dialog = true;
		},
		cale() {
			let forceEditInitPwd = localStorage.getItem('forceEditInitPwd');
			this.forceEditInitPwd = forceEditInitPwd;
			if (forceEditInitPwd && forceEditInitPwd == 'true') {
				this.$confirm('如本次不修改初始密码、将强制退出, 是否继续?', '提示', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					type: 'warning'
				})
					.then(() => {
						this.REMOVE_INFO();
						this.$router.push(`/login?redirect=${encodeURIComponent(this.$route.fullPath)}`);

						// this.dialog = false;
					})
					.catch(() => {});
			} else {
				this.dialog = false;
			}
		},
		async sure(isReset = false) {
			if (this.type === 'sure') {
				if (this.btnLoading) return;
				this.btnLoading = true;
				await this.save();
				this.btnLoading = false;
			} else if (isReset === true) {
				if (this.resetLoading) return;
				this.resetLoading = true;
				await this.resetPassword();
				this.resetLoading = false;
				this.verifyType = 2; // 点击重置的时候说明走验证码方式
			} else if (this.type === 'code') {
				if (this.btnLoading) return;
				this.btnLoading = true;
				await this.checkCode();
				this.btnLoading = false;
			} else if (this.type === 'oldPassword') {
				if (this.btnLoading) return;
				this.btnLoading = true;
				await this.checkOld();
				this.btnLoading = false;
				this.verifyType = 1; // 此时的下一步，说明走的旧密码方式
			} else if (this.type === 'newPassword') {
				this.checkNew();
			}
		},
		/**保存密码*/
		async save() {
			let encryptRes = await getEncryptKey();
			if (encryptRes.code === 200) {
				this.encryptKey = encryptRes.result;
				/**加密数据后登录*/
				let encryptor = new JSEncrypt(); // JSEncrypt对象
				encryptor.setPublicKey(this.encryptKey); // 公钥
				let pwd = encryptor.encrypt(this.form.newPassword); // 密码进行加密
				let params = {
					pwd,
					verifyType: this.verifyType,
					// verifyCode校验码(verifyType=1时为生成的PWD_CHECK_CODE校验码，verifyType=2时为手机验证码)
					verifyCode: this.verifyType === 1 ? this.passwordCheck : this.code.join('')
				};
				let res = await savePassword(params);
				if (res.code === 200) {
					localStorage.setItem('forceEditInitPwd', 'false');
					this.dialog = false;
					this.$message.success('修改成功，请重新登录！');
					let logoutRes = await this.LOGIN_OUT();
					if (logoutRes.code === 200) {
						this.$router.push(`/login?redirect=${encodeURIComponent(this.$route.fullPath)}`);
					} else {
						this.$message.error(logoutRes.message);
					}
				} else {
					this.$message.error(res.message);
				}
			} else {
				this.$message.error(encryptRes.message);
			}
		},
		/**校验验证码*/
		async checkCode() {
			let res = await checkCode({ code: this.code.join('') });
			if (res.code === 200) {
				this.type = 'newPassword';
			} else {
				this.$message.error(res.message);
			}
		},
		/**校验新密码*/
		checkNew() {
			this.$refs.ruleForm.validate(valid => {
				if (valid) {
					this.type = 'sure';
				}
			});
		},
		/**重新发送*/
		async reSendCode() {
			if (this.resendLoading) return;
			this.resendLoading = true;
			await this.resetPassword();
			this.resendLoading = false;
		},
		/**重置密码，发送验证码*/
		async resetPassword() {
			let timeKey = this.sendType + 'Time';
			let timeCountKey = this.sendType + 'TimeCount';
			if (this[timeCountKey] !== 60) {
				return;
			}
			//发送通道(phone:短信接收,mail:邮箱接收)
			let res = await sendCode(this.sendType);
			if (res.code === 200) {
				this.type = 'code';
				this[timeKey] = setInterval(() => {
					this[timeCountKey] -= 1;
					if (this[timeCountKey] <= 0) {
						this[timeCountKey] = 60;
						clearInterval(this[timeKey]);
					}
				}, 1000);
			} else {
				this.$message.error(res.message);
			}
		},
		/**校验旧密码*/
		async checkOld() {
			let res = await getEncryptKey();
			if (res.code === 200) {
				this.encryptKey = res.result;
				/**加密数据后登录*/
				let encryptor = new JSEncrypt(); // JSEncrypt对象
				encryptor.setPublicKey(this.encryptKey); // 公钥
				let pwd = encryptor.encrypt(this.oldPassword); // 密码进行加密
				let checkRes = await checkPassword({ pwd });
				if (checkRes.code === 200) {
					this.passwordCheck = checkRes.result || '';
					this.type = 'newPassword';
				} else {
					this.$message.error(checkRes.message);
				}
			} else {
				this.$message.error(res.message);
			}
		},
		/**清除输入框内容*/
		deleteInput(index) {
			if (this.code[index] === '' && index > 0) {
				let emptyIndex;
				let i = index;
				while (i > 0) {
					i--;
					if (this.code[i] !== '') {
						emptyIndex = i;
						break;
					}
				}
				this.$refs[`el-input-${emptyIndex}`][0].focus();
			}
		},
		/**输入改变看是否改变聚焦*/
		changeFocus(e, index) {
			// 如果不是数字，就取消输入的值
			if (!/\d$/.test(e)) {
				this.code[index] = '';
				return;
			}
			if (e.length > 0 && index < 5) {
				let ref = `el-input-${index + 1}`;
				this.$refs[ref][0].focus();
			}
		},
		/**监听粘贴*/
		inputPaste(e) {
			let pasteCon = e.clipboardData.getData('text');
			let string = pasteCon.substring(0, 6);
			if (parseInt(string)) {
				let arr = parseInt(string).toString().split('');
				this.code.forEach((item, index) => {
					this.code[index] = arr[index] || '';
				});
				let ref = `el-input-5`;
				this.$refs[ref][0].focus();
				this.$forceUpdate();
			}
		}
	}
};
</script>

<style scoped lang="scss">
.sm-top {
	::v-deep.el-dialog__footer {
		padding-top: 2px !important;
	}
}
.dialog-body {
	&-title {
		font-size: 16px;
		font-weight: 500;
		color: $primaryTextColor;
		line-height: 22px;
		margin-bottom: 2px;
	}
	&-desc {
		font-size: 14px;
		font-weight: 400;
		color: $subTextColor;
		line-height: 22px;
	}
	&-input {
		height: 40px;
		background: #ffffff;
		border-radius: 6px;
		margin: 12px 0 24px;
		::v-deep .el-input__inner {
			border-radius: 6px;
			height: 100%;
		}
	}
	.new-password-input {
		margin: 0 0 2px;
	}
	.form {
		&-item {
			margin-bottom: 0;
		}
		&-item-new {
			margin-top: 24px;
		}
	}
	.code-input {
		@include flexBox(flex-start);
		margin: 12px 0;
		&-item {
			height: 40px;
			border-radius: 6px;
			width: 40px;
			margin-right: 6px;
			::v-deep .el-input__inner {
				border-radius: 6px;
				height: 100%;
			}
		}
	}
	.sure-tip {
		width: 100%;
		height: 117px;
		background: linear-gradient(
			90deg,
			rgba(213, 227, 255, 0.45) 0%,
			rgba(232, 244, 255, 0.45) 100%,
			rgba(229, 243, 255, 0.45) 100%
		);
		border-radius: 9px;
		padding: 14px 17px;
		margin-top: 10px;
		&-title {
			font-size: 16px;
			font-weight: 600;
			color: $primaryTextColor;
			line-height: 24px;
			margin-bottom: 10px;
			@include flexBox(flex-start);
			.icon {
				width: 24px;
				height: 24px;
				margin-right: 7px;
			}
		}
		&-con {
			font-size: 14px;
			font-weight: 400;
			color: $textColor;
			line-height: 22px;
		}
	}
	.radio {
		&-text {
			font-size: 14px;
			font-weight: 500;
			color: $primaryTextColor;
			line-height: 22px;
			&-type {
				cursor: pointer;
				margin-top: 12px;
				color: var(--brand-6);
			}
		}
		&-send {
			cursor: pointer;
			color: var(--brand-6);
		}
		&-btn {
			cursor: pointer;
			font-size: 14px;
			font-weight: 500;
			color: var(--brand-6);
			line-height: 22px;
		}
	}
}
</style>
