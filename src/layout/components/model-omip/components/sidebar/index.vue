<template>
	<div
		class="sidebar"
		:style="menuBg ? `background-image: url(${menuBg})` : 'background: transparent'"
	>
		<div class="fixed-sidebar">
			<FileById
				:more-style="{ borderRadius: '6px', marginRight: 0 }"
				class="sidebar-logo"
				:size="44"
				:value="rentInfo.logoUrl"
				:default-font-icon="rentInfo.name.slice(0, 1)"
				@click.native="openMore"
			/>
			<FileById
				:more-style="{
					borderRadius: '9px',
					marginRight: 0,
					marginTop: '6px'
				}"
				:size="44"
				class="sidebar-user"
				:value="userInfo.avatarUrl"
				:default-font-icon="userInfo.realname.slice(-1) || '用户'"
				@click.native="changeSetting"
			/>
			<div v-show="!downLoaded" class="download-text" @click="openDownload">
				<div>下载</div>
				<i class="el-icon-bottom down-icon"></i>
			</div>
			<div class="search" @click="search">
				<i class="coos-iconfont icon-search search-icon"></i>
			</div>
			<div
				v-if="mainMenuList[0]"
				:class="{ selectBg: currentMenu === 0 }"
				class="sidebar-item"
				@click="changeMenu(0)"
			>
				<div v-if="mainMenuList[0].iconType === 'image'" class="sidebar-item-img">
					<img :src="mainMenuList[0].logoUrlPath" alt="" srcset="" />
				</div>
				<svg-icon
					v-else
					class="sidebar-item-icon"
					:class="currentMenu === 0 ? 'select-icon' : 'normal-icon'"
					:icon-class="
						currentMenu === 0 ? `${mainMenuList[0].meta.icon}-s` : mainMenuList[0].meta.icon
					"
				></svg-icon>
				<div
					class="sidebar-item-text"
					:style="{ color: currentMenu === 0 ? textResetColor : textColor }"
					:class="{ select: currentMenu === 0 }"
				>
					{{ mainMenuList[0].meta.title }}
				</div>
				<em v-if="mainMenuList[0].meta.title === '待办' && waitDoneCount" class="badge">
					{{ waitDoneCount }}
				</em>
				<em v-if="unread() > 0 && mainMenuList[0].path.match('wile-fire')" class="badge">
					{{ unread() > 99 ? '···' : unread() }}
				</em>
			</div>
			<div v-show="showLine" class="line"></div>
			<div ref="scrollContent" class="scroll-con" @scroll="scrollHandle">
				<div
					v-for="(item, index) of mainMenuList"
					v-show="index !== 0"
					:key="'list-' + index"
					:class="{ selectBg: currentMenu === index }"
					class="sidebar-item"
					:style="index === 0 ? 'top:0' : ''"
					:title="item.meta.title || item.name"
					@click="changeMenu(index)"
				>
					<div v-if="item.iconType === 'image'" class="sidebar-item-img">
						<img :src="item.logoUrlPath" alt="" srcset="" />
					</div>
					<svg-icon
						v-else
						class="sidebar-item-icon"
						:class="currentMenu === index ? 'select-icon' : 'normal-icon'"
						:icon-class="currentMenu === index ? `${item.meta.icon}-s` : item.meta.icon"
					></svg-icon>
					<div
						class="sidebar-item-text"
						:style="{ color: currentMenu === index ? textResetColor : textColor }"
						:class="{ select: currentMenu === index }"
					>
						{{ item.meta.title }}
					</div>
					<em v-if="item.meta.title == '待办' && waitDoneCount" class="badge">
						{{ waitDoneCount }}
					</em>
					<em v-if="unread() > 0 && item.path.match('wile-fire')" class="badge">
						{{ unread() > 99 ? '···' : unread() }}
					</em>
				</div>
			</div>
		</div>
		<!--所有菜单-->
		<application />

		<!--  点击头像的弹窗  -->
		<setting
			ref="setting"
			:more-style="{
				top: '70px',
				left: '70px'
			}"
		></setting>
		<!--  点击企业图标的弹窗  -->
		<rentList
			ref="rentList"
			:more-style="{
				top: '20px',
				left: '70px'
			}"
		></rentList>
		<Pop :dialog-visible.sync="dialogVisible" />
		<downloadContent ref="downloadContent" @loaded="loaded"></downloadContent>
	</div>
</template>

<script>
import { mapGetters, mapState } from 'vuex';
import LayoutMixin from '@/layout/mixin/share-variable';
import setting from '@/layout/components/setting/index.vue';
import Application from '../application/index.vue';
import rentList from '@/layout/components/rent-list/index.vue';
import { getItem } from '@/utils/localstorage';
import store from '@/wile-fire/store';
import Pop from '@/layout/components/search/pop.vue';
import downloadContent from '@/components/download-content/index.vue';
export default {
	components: {
		setting,
		rentList,
		Pop,
		downloadContent,
		Application
	},
	mixins: [LayoutMixin],
	data() {
		return {
			config: {}, // 右键菜单位置信息
			hideMenuCount: 0,
			downLoaded: true,
			dialogVisible: false,
			// currentMenu: 0,
			more: [
				{
					color: '#ED6B5F'
				},
				{
					color: '#F6C24F'
				},
				{
					color: '#63C855'
				}
			],
			showLine: false,
			sharedConversationState: store.state.conversation,
			reThem: '#0F45EA',
			hideApplicationCount: 0 // 隐藏的应用的个数
		};
	},
	computed: {
		currentMenu() {
			// 在主菜单中匹配索引
			let menuIndex = this.mainMenuList.findIndex(item => {
				// 如果是三方系统
				if (this.$route.path.match('/other-system')) {
					let otherUrl = decodeURIComponent(this.$route.query.url);
					return otherUrl.indexOf(item.mainUrl) > -1;
				}
				// 自己的路由
				else {
					return this.$route.fullPath.indexOf(item.mainUrl) > -1;
				}
			});
			// 应用菜单均保存了对应的id，直接使用id匹配
			menuIndex = menuIndex > -1 ? menuIndex : getItem('currentId');
			return menuIndex;
		},
		...mapGetters(['rentInfo', 'userInfo', 'openIm']),
		...mapState('app', ['waitDoneCount', 'imCount'])
	},
	methods: {
		// 监听滚动条显示样式
		scrollHandle() {
			this.showLine = this.$refs.scrollContent.scrollTop !== 0;
		},
		// 打开全局下载弹窗
		openDownload() {
			this.$refs.downloadContent.open();
		},
		// 搜索
		search() {
			this.dialogVisible = true;
		},
		// 下载完成
		loaded(downLoaded) {
			this.downLoaded = downLoaded;
		},
		unread() {
			let count = 0;
			if (this.openIm === 'true') {
				this.sharedConversationState.conversationInfoList.forEach(info => {
					if (info.isSilent) {
						return;
					}
					let unreadCount = info.unreadCount;
					count += unreadCount.unread;
				});
			} else {
				count = this.imCount;
			}
			// side
			return count;
		},
		/**打开设置*/
		changeSetting() {
			this.$refs.setting.open();
		},
		/**打开更多*/
		openMore() {
			this.$refs.rentList.open();
		}
	}
};
</script>
<style scoped lang="scss">
.sidebar {
	height: 100vh;
	padding: 20px 0 25px;
	width: 88px;
	position: absolute;
	left: -88px;
	top: 0;
	display: flex;
	flex-direction: column;
	align-items: center;
	background-size: 100% 100%;
	&-more {
		height: 44px;
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: 13px;
		cursor: pointer;
		&-item {
			width: 10px;
			height: 10px;
			border-radius: 50%;
			margin: 0 2.5px;
		}
	}
	//用户头像以及更多设置
	&-user {
		cursor: pointer;
	}
	//每一个菜单项
	&-item {
		cursor: pointer;
		padding: 8px 11px;
		border-radius: 9px;
		display: flex;
		align-items: center;
		justify-content: center;
		flex-direction: column;
		margin-bottom: 12px;
		&-icon {
			width: 24px;
			height: 24px;
			margin-bottom: 6px;
		}
		.select-icon {
			color: var(--brand-6);
		}
		.normal-icon {
			color: var(--brand-2);
		}
		&-text {
			font-weight: 500;
			color: $textColor;
			font-weight: 500;
			font-size: 14px;
			line-height: 18px;
		}
		.select {
			color: var(--brand-6);
		}
	}
	.selectBg {
		background: var(--brand-1);
	}
	//租户图标
	&-logo {
		cursor: pointer;
	}
}
.sidebar-item-img {
	width: 20px;
	height: 20px;
	margin: 0 auto 8px auto;
	img {
		width: 100%;
		height: 100%;
	}
}

.sidebar-item {
	min-width: 64px;
	position: sticky;
	&:hover {
		background: var(--brand-1);
	}
}
.line {
	height: 1px;
	width: 100%;
	box-shadow: 0px 2px 2px 0px rgba(96, 125, 166, 0.2);
	background: #c4d1e0;
}
.badge {
	position: absolute;
	color: white;
	font-size: 10px;
	background-color: red;
	border-radius: 8px;
	min-width: 16px;
	height: 16px;
	padding: 0 5px;
	line-height: 16px;
	font-style: normal;
	text-align: center;
	right: 12px;
	top: 4px;
	vertical-align: center;
}

.search {
	width: 40px;
	height: 40px;
	background: rgba(255, 255, 255, 0.7);
	border-radius: 31px;
	margin: 13px 0 20px;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	flex-shrink: 0;
	&-icon {
		color: #53668d;
		font-size: 20px;
	}
}
.download-text {
	flex-shrink: 0;
	margin-top: 20px;
	height: 40px;
	width: 40px;
	border-radius: 50%;
	background: #ffffff;
	font-size: 12px;
	cursor: pointer;
	line-height: 20px;
	color: var(--brand-6);
	overflow: hidden;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}
.scroll-con {
	flex: 1;
	overflow: auto;
	display: flex;
	align-items: center;
	flex-direction: column;
	width: 100%;
	&::-webkit-scrollbar {
		width: 0;
	}
}
@keyframes toBottom {
	from {
		transform: translateY(-14px);
	}
	to {
		transform: translateY(14px);
	}
}
.down-icon {
	font-size: 12px;
	color: var(--brand-6);
	animation: toBottom 1s infinite;
}
.more-menu-icon {
	width: 24px;
	height: 24px;
	margin-bottom: 6px;
	color: $textColor;
}
.fixed-sidebar {
	width: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;
	flex: 1;
	overflow: auto;
	position: relative;
	&::-webkit-scrollbar {
		width: 0;
	}
}
.sidebar-item-text {
	overflow: hidden;
	white-space: nowrap;
	width: 60px;
	text-overflow: ellipsis;
	text-align: center;
}
</style>
<!--<style>-->
<!--.customPopper {-->
<!--	min-width: 120px !important;-->
<!--	display: flex;-->
<!--	flex-direction: column;-->
<!--	height: auto !important;-->
<!--}-->
<!--</style>-->
