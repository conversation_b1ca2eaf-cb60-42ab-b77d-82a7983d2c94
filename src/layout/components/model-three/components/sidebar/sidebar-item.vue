<template>
	<div v-if="!item.hidden">
		<template
			v-if="
				hasOneShowingChild(item.children, item) &&
				(!onlyOneChild.children || onlyOneChild.noShowingChildren) &&
				!item.alwaysShow
			"
		>
			<div @click="openPath(onlyOneChild)">
				<el-tooltip :open-delay="1000" :content="onlyOneChild.meta.title" placement="right">
					<el-menu-item
						:index="onlyOneChild.id"
						:class="{ 'submenu-title-noDropdown': !isNest }"
						:style="{ paddingLeft: paddingLeft }"
					>
						<!--:icon="onlyOneChild.meta.icon || (item.meta && item.meta.icon)"-->
						<item
							:icon="level > 1 ? '' : item.meta && item.meta.icon ? item.meta.icon : ''"
							:title="onlyOneChild.meta.title"
							:end-icon="onlyOneChild.meta.endIcon || ''"
							:role="onlyOneChild.meta.role || {}"
						/>
					</el-menu-item>
				</el-tooltip>
			</div>
		</template>
		<el-submenu v-else ref="subMenu" :index="item.id" popper-append-to-body>
			<div v-if="item.meta" slot="title" :style="{ marginLeft: titlePaddingLeft }">
				<item
					:icon="level > 1 ? '' : item.meta && item.meta.icon ? item.meta.icon : ''"
					:title="item.meta.title"
					:role="item.meta.role || {}"
				/>
			</div>
			<sidebar-item
				v-for="child in item.children"
				:key="child.id"
				:is-nest="true"
				:item="child"
				:level="level + 1"
				:base-path="resolvePath(child.path)"
				class="nest-menu"
			/>
		</el-submenu>
	</div>
</template>

<script>
import path from 'path';
import { isExternal } from '@/utils/validate';
import Item from './Item.vue';
import FixiOSBug from './fixios-bug';
import { mapGetters } from 'vuex';
import { getToken } from '@/utils/auth';
import { preUrl } from '@/config';

export default {
	name: 'SidebarItem',
	components: { Item },
	mixins: [FixiOSBug],
	props: {
		// route object
		item: {
			type: Object,
			required: true
		},
		isNest: {
			type: Boolean,
			default: false
		},
		basePath: {
			type: String,
			default: ''
		},
		level: {
			type: Number,
			default: 1
		},
		isCollapse: {
			type: Boolean,
			default: false
		}
	},
	data() {
		// To fix https://github.com/PanJiaChen/vue-admin-template/issues/237
		// TODO: refactor with render function
		this.onlyOneChild = null;
		return {
			menuShowAllLevel: false
		};
	},

	computed: {
		...mapGetters(['rentInfo', 'userInfo']),

		paddingLeft() {
			// 第一级距离左边7个像素   第二级开始以34为基数，多一级向右便宜12像素
			return this.level === 1 ? 8 : 34 + (this.level - 2) * 12 + 'px';
		},
		titlePaddingLeft() {
			//menu设置的paddingleft与submenu默认paddingleft存在偏移量
			return (this.level <= 2 ? '-12' : '-20') + 'px';
		}
	},
	created() {
		let extend = JSON.parse(this.rentInfo.extend);
		this.menuShowAllLevel = extend.clientBasic.menuShowAllLevel;
	},
	methods: {
		openPath(item) {
			console.log(item, '123123');
			if (item.meta.internalOrExternal) {
				/**tdb提需求外链打开加上token-S*/
				let newUrl = item.meta.url;
				if (item.meta.url.indexOf('?') > -1) {
					newUrl += `&coos_token=${getToken()}`;
				} else {
					newUrl += `?coos_token=${getToken()}`;
				}
				/**tdb提需求外链打开加上token-E*/
				// 外链打开
				window.open(newUrl);
			} else if (item.meta.isExternal) {
				// 内链打开
				let url = item.meta.url || '';
				let mainUrl = /http/gi.test(url) ? url : (preUrl || window.location.origin) + url;
				this.$router.push(`/other-system?url=${encodeURIComponent(mainUrl || '')}`);
			} else {
				this.$router.push(item.path);
			}
		},
		hasOneShowingChild(children = [], parent) {
			const showingChildren = children.filter(item => {
				if (item.hidden) {
					return false;
				} else {
					// Temp set(will be used if only has one showing child)
					this.onlyOneChild = item;
					return true;
				}
			});
			// When there is only one child router, the child router is displayed by default
			if (showingChildren.length === 1) {
				return !this.menuShowAllLevel;
			}

			// Show parent if there are no child router to display
			if (showingChildren.length === 0) {
				this.onlyOneChild = { ...parent, noShowingChildren: true }; // path: '',
				return true;
			}

			return false;
		},
		resolvePath(routePath) {
			if (isExternal(routePath)) {
				return routePath;
			}
			if (isExternal(this.basePath)) {
				return this.basePath;
			}
			return path.resolve(this.basePath, routePath);
		}
	}
};
</script>
