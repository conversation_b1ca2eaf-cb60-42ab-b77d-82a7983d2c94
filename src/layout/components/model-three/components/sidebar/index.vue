<template>
	<div
		v-if="childrenMenuList.length"
		v-role="{ oneOfKeys: [], keys: [] }"
		class="sidebar-container"
		:style="{
			width: isCollapse ? '64px' : sidebarWidth + 'px',
			backgroundImage: `url('${urlHttp(systemInfo.main.menuBg)}')`
		}"
	>
		<el-scrollbar wrap-class="scrollbar-wrapper">
			<div class="scrollbar-wrapper-inner">
				<div class="scrollbar-wrapper-content">
					<el-menu
						:default-active="activeMenu"
						:collapse="isCollapse"
						:background-color="variables.menuBg"
						:text-color="variables.menuText"
						:unique-opened="true"
						:collapse-transition="false"
						mode="vertical"
					>
						<sidebar-item
							v-for="route in childrenMenuList"
							:key="route.id"
							:collapse="isCollapse"
							:item="route"
							:base-path="route.path"
						/>
					</el-menu>
				</div>
			</div>
		</el-scrollbar>
		<!-- 菜单有背景图时，使用collapseIsBg样式，0.5半透明展示背景图-->
		<div :class="{ button: true, collapseIsBg: systemInfo.main.menuBg ? true : false }">
			<span v-if="!isCollapse">收起导航</span>
			<i
				class="coos-iconfont button-icon icon-shouqidaohang"
				:class="{ 'rotate-icon': isCollapse }"
				@click="changeCollapse"
			></i>
		</div>
		<!-- 拖动布局宽度按钮 -->
		<div class="sidebar-drag-line" @mousedown="resize"></div>
	</div>
</template>

<script>
import { mapGetters, mapMutations } from 'vuex';
import SidebarItem from './sidebar-item.vue';
import variables from '@/styles/variables.scss';
import { urlHttp } from '@/utils';

export default {
	components: { SidebarItem },
	data() {
		return {
			// 左侧菜单宽度
			sidebarWidthDefault: 240,
			sidebarWidth: 240
			// 拖动按钮与左侧屏幕的距离
			// sidebarWidthDrift: 220
		};
	},
	computed: {
		...mapGetters(['sidebar', 'systemInfo', 'menuList', 'currentLevelMenu']),
		childrenMenuList() {
			return this.menuList[this.currentLevelMenu]?.children || [];
		},
		activeMenu() {
			let activeId = null;
			const findActive = arr => {
				for (let i = 0; i < arr.length; i++) {
					if (activeId) return;
					// iframe方式的判断
					if (this.$route.query.url && arr[i].meta.url === this.$route.query.url) {
						activeId = arr[i].id;
					}
					// 本地路由的判断方式
					else if (arr[i].path && arr[i].path === this.$route.fullPath) {
						activeId = arr[i].id;
					}
					if (arr[i].children && arr[i].children.length) {
						findActive(arr[i].children);
					}
				}
			};
			findActive(this.childrenMenuList);

			return activeId;
		},
		showLogo() {
			return this.$store.state.settings.sidebarLogo;
		},
		variables() {
			return variables;
		},
		isCollapse() {
			return !this.sidebar.opened;
		}
	},
	beforeDestroy() {
		// 组件销毁时重制菜单宽度
		this.SET_SIDEBAR_WIDTH(this.sidebarWidthDefault);
	},
	methods: {
		urlHttp,
		...mapMutations('app', ['TOGGLE_SIDEBAR', 'SET_SIDEBAR_WIDTH', 'SET_SIDEBAR_DRAG']),
		changeCollapse() {
			this.TOGGLE_SIDEBAR();
		},
		resize(e) {
			this.sidebarWidthDefault = this.sidebarWidth;
			this.startX = e.clientX;
			// document.addEventListener('mousemove', this.handleMouseMove);
			// document.addEventListener('mouseup', this.stopSelection);
		},
		handleMouseMove(e) {
			this.moveWidth = e.clientX - this.startX;
			const newWidth = this.sidebarWidthDefault + this.moveWidth;
			console.log(newWidth);
			// 更新菜单拖动状态
			this.SET_SIDEBAR_DRAG(true);
			if (newWidth >= 240 && newWidth <= 340) {
				this.sidebarWidth = newWidth;
				this.SET_SIDEBAR_WIDTH(newWidth);
			}
		},
		stopSelection() {
			document.removeEventListener('mousemove', this.handleMouseMove);
			document.removeEventListener('mouseup', this.stopSelection);
			// 修改菜单拖动状态为关闭
			this.SET_SIDEBAR_DRAG(false);
		}
	}
};
</script>
<style lang="scss" scoped>
.sidebar-box {
	position: fixed;
	left: 0;
	z-index: 10;
}
.sidebar-container {
	user-select: none;
	background-repeat: no-repeat;
	background-size: cover;
	background-position: center bottom;
	height: calc(100% - 60px) !important;
	top: 60px !important;
}
.sidebar-drag {
	z-index: 100;
	left: 220px;
	&-line {
		position: absolute;
		width: 6px;
		z-index: 100;
		top: 0;
		right: 0;
		cursor: ew-resize;
		height: calc(100vh - 44px);
	}
}
.button {
	position: absolute;
	bottom: 0;
	left: 0;
	height: 48px;
	width: 100%;
	border: 1px solid #f0f0f0;
	border-right: none;
	padding: 0 10px 0 10px;
	background: #ffffff;
	@include flexBox(space-between);
	font-size: 12px;
	font-weight: 400;
	color: #000000;
	line-height: 20px;
	span {
		margin-left: 14px;
		white-space: nowrap;
	}
	&-icon {
		width: 40px;
		height: 40px;
		text-align: center;
		font-size: 20px;
		line-height: 40px;
		cursor: pointer;
	}
	.rotate-icon {
		transform: rotateZ(180deg);
	}
}
.collapseIsBg {
	background: rgba(255, 255, 255, 0.5);
}
::v-deep .el-scrollbar__wrap {
	overflow: auto;
}

::v-deep .el-scrollbar__bar.is-vertical {
	display: none;
}
</style>
