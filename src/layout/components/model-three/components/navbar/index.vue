<template>
	<div :class="{ navbar: true, lightNavbar: systemInfo.main.mode === 'light' }">
		<div class="navbar-left" @click="openMore">
			<FileById
				:more-style="{ borderRadius: '3px', marginRight: '7px' }"
				:size="24"
				:value="rentInfo.logoUrl"
				:default-font-icon="rentInfo.name.slice(0, 1)"
			></FileById>
			<div class="navbar-title">{{ rentInfo.name }}</div>
		</div>
		<div ref="navbarCenter" class="navbar-center" :class="{ isOver: isOver }">
			<svg-icon
				v-show="isOver"
				class="handle-pre"
				icon-class="seal-left"
				@click="scrollTo('pre')"
			></svg-icon>
			<svg-icon
				v-show="isOver"
				class="handle-next"
				icon-class="next"
				@click="scrollTo('next')"
			></svg-icon>
			<div ref="scrollEl" class="navbar-center-content">
				<div
					v-for="(menu, index) of menuList"
					:key="index"
					:ref="'scrollView' + index"
					class="menu-item"
					:class="{ select: index === currentLevelMenu }"
					@click="selectMenu(index)"
				>
					<i class="coos-iconfont menu-item-icon" :class="menu.meta.icon"></i>
					<span class="menu-item-text">{{ menu.meta.title }}</span>
				</div>
			</div>
		</div>
		<div class="navbar-right">
			<div
				v-for="(item, index) of mainMenuList"
				:key="index"
				:class="{ 'nav-select': currentMenu === index }"
				class="navbar-item"
				@click="changeMenu(index)"
			>
				<el-tooltip :open-delay="1000" effect="dark" :content="item.meta.title" placement="bottom">
					<div style="position: relative">
						<div v-if="item.iconType === 'image'" class="sidebar-item-img">
							<img :src="item.logoUrlPath" alt="" srcset="" />
						</div>
						<svg-icon
							v-else
							class="sidebar-item-icon"
							:icon-class="currentMenu === index ? item.meta.icon + '-s' : item.meta.icon + 2"
						></svg-icon>
						<em v-if="item.meta.title === '消息' && unread() > 0" class="badge">
							{{ unread() > 99 ? '···' : unread() }}
						</em>
						<em v-if="item.meta.title === '待办' && waitDoneCount" class="badge">
							{{ waitDoneCount }}
						</em>
					</div>
				</el-tooltip>
			</div>
			<el-tooltip
				v-if="systemInfo.main.showGuidePage === 'true'"
				:open-delay="1000"
				effect="dark"
				content="回到引导页"
				placement="bottom"
			>
				<i class="coos-iconfont icon-home backGuide" @click="toGuide"></i>
			</el-tooltip>
			<div class="search" @click="search">
				<i class="coos-iconfont icon-search search-icon"></i>
			</div>
			<FileById
				:more-style="{ borderRadius: '6px', marginRight: 0 }"
				:size="28"
				class="navbar-avatar"
				:value="userInfo.avatarUrl"
				:default-font-icon="userInfo.realname.slice(-1) || '用户'"
				@click.native="changeSetting"
			></FileById>
			<div v-show="!downLoaded" class="download-text" @click="openDownload">
				<i class="el-icon-bottom down-icon"></i>
				下载列表
			</div>
			<!--  点击头像的弹窗  -->
			<setting ref="setting" :more-style="{ top: '40px', right: '10px' }"></setting>
			<!--  点击企业图标的弹窗  -->
			<rentList
				ref="rentList"
				:more-style="{
					top: '44px',
					left: '0'
				}"
			></rentList>
			<Pop :dialog-visible.sync="dialogVisible" />
			<downloadContent ref="downloadContent" @loaded="loaded"></downloadContent>
		</div>
	</div>
</template>

<script>
import Pop from '@/layout/components/search/pop.vue';
import { mapGetters, mapMutations, mapState } from 'vuex';
import LayoutMixin from '@/layout/mixin/share-variable';
import setting from '@/layout/components/setting/index.vue';
import rentList from '@/layout/components/rent-list/index.vue';
import downloadContent from '@/components/download-content/index.vue';
import { getToken } from '@/utils/auth';
import { isEmpty } from 'lodash';
import { debounce } from '@/utils';
import store from '@/wile-fire/store';
import { preUrl } from '@/config';
export default {
	components: {
		setting,
		rentList,
		Pop,
		downloadContent
	},
	mixins: [LayoutMixin],
	data() {
		return {
			sharedConversationState: store.state.conversation,
			downLoaded: true, // 下载完成
			keywords: '', // 搜索值
			isOver: false, // 超出滚动宽度
			dialogVisible: false // 显示弹窗
		};
	},
	computed: {
		currentMenu() {
			let index = this.mainMenuList.findIndex(item => {
				let path = item.path;
				if (path.indexOf('/') === -1) {
					path = '/' + path;
				}
				if (this.$route.path.match('/other-system')) {
					// 其他应用时根据应用名称匹配
					return item.name === this.$route.query.name;
				}
				return path === this.$route.path || path === this.$route.fullPath;
			});
			return index;
		},
		...mapGetters(['rentInfo', 'userInfo', 'systemInfo', 'menuList', 'currentLevelMenu', 'openIm']),
		...mapState('app', ['waitDoneCount', 'imCount'])
	},
	watch: {
		currentLevelMenu(newVal) {
			// 因为数据固定，所以直接执行
			if (this.$refs['scrollView' + newVal]) {
				this.$refs['scrollView' + newVal][0].scrollIntoView();
			}
		}
	},
	mounted() {
		this.$nextTick(() => {
			this.reGetIsCover();
		});
		window.addEventListener('resize', this.reGetIsCover);
	},
	beforeDestroy() {
		window.removeEventListener('resize', this.reGetIsCover);
	},
	methods: {
		...mapMutations('settings', ['SET_CURRENT_LEVEL_MENU']),
		unread() {
			let count = 0;
			if (this.openIm === 'true') {
				this.sharedConversationState.conversationInfoList.forEach(info => {
					if (info.isSilent) {
						return;
					}
					let unreadCount = info.unreadCount;
					count += unreadCount.unread;
				});
			} else {
				count = this.imCount;
			}
			// side
			return count;
		},
		toGuide() {
			this.$router.push(this.systemInfo.main.guidePageUrl);
		},
		findLevelIndex() {
			let activeId = null;
			const findActive = (arr, firstIndex) => {
				for (let i = 0; i < arr.length; i++) {
					if (activeId) return;
					// iframe方式的判断
					if (this.$route.query.url && arr[i].meta.url === this.$route.query.url) {
						activeId = firstIndex || i;
					}
					// 本地路由的判断方式
					else if (arr[i].path && arr[i].path === this.$route.fullPath) {
						activeId = firstIndex || i;
					}
					if (arr[i].children && arr[i].children.length) {
						findActive(arr[i].children, firstIndex || i);
					}
				}
			};
			findActive(this.menuList);
			return activeId;
		},
		/**重新计算是否超过*/
		reGetIsCover: debounce(
			function () {
				this.isOver =
					this.$refs.scrollEl &&
					this.$refs.scrollEl.clientWidth >= this.$refs.navbarCenter.clientWidth - 125;
			},
			500,
			false
		),
		scrollTo(type) {
			let left = type === 'next' ? 320 : -320;
			this.$refs.scrollEl.scrollBy({ left, behavior: 'smooth' });
		},
		/**选择一级菜单*/
		selectMenu(index) {
			this.SET_CURRENT_LEVEL_MENU(index);
			this.filterNode(this.menuList[index]);
		},
		/**递归取叶子节点第一条数据进行跳转*/
		filterNode(obj) {
			if (isEmpty(obj.children)) {
				this.openPath(obj);
			} else {
				this.filterNode(obj.children[0]);
			}
		},
		/**点击sidebar-item跳转的逻辑*/
		openPath(item) {
			if (item.meta.internalOrExternal) {
				/**tdb提需求外链打开加上token-S*/
				let newUrl = item.meta.url;
				if (item.meta.url.indexOf('?') > -1) {
					newUrl += `&coos_token=${getToken()}`;
				} else {
					newUrl += `?coos_token=${getToken()}`;
				}
				/**tdb提需求外链打开加上token-E*/
				// 外链打开
				window.open(newUrl);
			} else if (item.meta.isExternal) {
				// 内链打开
				let url = item.meta.url || '';
				let mainUrl = /http/gi.test(url) ? url : (preUrl || window.location.origin) + url;
				this.$router.push(`/other-system?url=${encodeURIComponent(mainUrl || '')}`);
			} else {
				this.$router.push(item.path);
			}
		},
		/**打开下载*/
		openDownload() {
			this.$refs.downloadContent.open();
		},
		/**下载完成通知*/
		loaded(downLoaded) {
			this.downLoaded = downLoaded;
		},
		/**搜索*/
		search() {
			this.dialogVisible = true;
		},
		/**切换菜单*/
		// changeMenu(i) {
		// 	this.currentMenu = i;
		// 	this.$router.push(
		// 		`/${this.mainMenuList[i].path}?${qs.stringify(this.mainMenuList[i].meta.query)}`
		// 	);
		// },
		/**打开设置*/
		changeSetting() {
			this.$refs.setting.open();
		},
		/**打开更多*/
		openMore() {
			this.$refs.rentList.open();
		}
	}
};
</script>

<style lang="scss" scoped>
.navbar {
	height: 60px;
	width: 100%;
	background: #ffffff;
	background-size: cover;
	@include flexBox(flex-start);
	border: 1px solid #f0f0f0;
	padding: 0 20px 0 16px;

	&-left {
		flex-shrink: 0;
		@include flexBox();
		cursor: pointer;
	}
	&-right {
		flex-shrink: 0;
		@include flexBox();
	}

	&-title {
		font-size: 14px;
		font-family: Source Han Sans SC, Source Han Sans SC;
		font-weight: 500;
		color: #2f446b;
		line-height: 22px;
		white-space: nowrap;
	}

	&-center {
		flex: 1;
		padding: 0 64px 0 55px;
		overflow: hidden;
		position: relative;
		.handle-pre {
			position: absolute;
			left: 44px;
			width: 16px;
			height: 16px;
			top: 8px;
			cursor: pointer;
			display: none;
		}
		.handle-next {
			position: absolute;
			right: 48px;
			width: 16px;
			height: 16px;
			top: 8px;
			cursor: pointer;
			display: none;
		}
		&-content {
			max-width: 100%;
			display: inline-flex;
			align-items: center;
			overflow: auto;
			scrollbar-width: none;
			&::-webkit-scrollbar {
				height: 0;
			}
			.menu-item {
				padding: 4px 6px;
				border-radius: 3px;
				color: #2f446b;
				margin-right: 24px;
				flex-shrink: 0;
				cursor: pointer;
				@include flexBox();
				&-icon {
					font-size: 20px;
					margin-right: 4px;
				}
				&-text {
					font-weight: 400;
					font-size: 14px;
					line-height: 22px;
				}
			}
			.select {
				background: rgba(15, 114, 234, 0.1);
				color: var(--brand-6);
			}
		}
	}

	.nav-select {
		background: #f1f3f6;
	}

	&-item {
		width: 30px;
		height: 30px;
		color: $textColor;
		border-radius: 6px;
		@include flexBox();
		margin-right: 20px;
		cursor: pointer;

		&-icon {
			width: 20px;
			height: 20px;
		}
	}

	&-avatar {
		width: 28px;
		height: 28px;
		border-radius: 6px;
		cursor: pointer;
	}
}

.lightNavbar {
	//.navbar-title {
	//	color: #2f446b;
	//}

	//.select {
	//	background-color: var(--brand-2, #cfeaff);
	//}

	//.navbar-item {
	//	color: #ffffff;
	//}
}

.download-text {
	cursor: pointer;
	line-height: 20px;
	color: #ffffff;
	overflow: hidden;
	margin-left: 12px;
}

@keyframes toBottom {
	from {
		transform: translateY(-14px);
	}

	to {
		transform: translateY(14px);
	}
}

.down-icon {
	color: #ffffff;
	animation: toBottom 1s infinite;
}

.badge {
	position: absolute;
	color: white;
	font-size: 10px;
	background-color: red;
	border-radius: 8px;
	min-width: 16px;
	height: 16px;
	padding: 0 5px;
	line-height: 16px;
	font-style: normal;
	text-align: center;
	right: -10px;
	top: -10px;
	vertical-align: center;
}

.search {
	width: 32px;
	height: 32px;
	margin-right: 8px;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	&-icon {
		color: #2f446b;
		font-size: 20px;
	}
}
.isOver {
	.handle-pre,
	.handle-next {
		display: block;
	}
}
.backGuide {
	font-size: 20px;
	margin: 0 20px 4px 0;
	cursor: pointer;
}
.sidebar-item-img {
	width: 20px;
	height: 20px;
	margin: 0 auto 8px auto;
	img {
		width: 100%;
		height: 100%;
	}
}
.sidebar-item-text {
	overflow: hidden;
	white-space: nowrap;
	width: 60px;
	text-overflow: ellipsis;
	text-align: center;
}
</style>
