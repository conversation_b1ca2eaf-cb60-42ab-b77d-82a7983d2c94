<template>
	<div
		class="main-container"
		:style="{
			marginLeft:
				isHideLayout || childrenMenuList.length === 0
					? '0px'
					: isCollapse
					? '88px'
					: sidebarWidth + 'px',
			padding: isHideLayout ? '0' : '70px 12px 12px'
		}"
	>
		<div v-if="!isHideLayout">
			<navbar :class="{ 'fixed-header': fixedHeader }"></navbar>
			<sidebar></sidebar>
		</div>
		<!-- 菜单拖动时，给内容内容区域元素不可点击，解决鼠标移入iframe时监听事件丢失或其他文字被选中问题 -->
		<app-main
			:key="pageKey"
			:class="{
				'sidebar-drag': sidebarDrag
			}"
		/>
	</div>
</template>
<script>
import AppMain from '@/layout/components/app-main';
import navbar from './components/navbar';
import sidebar from './components/sidebar';
import { mapGetters, mapState } from 'vuex';
import { getSessionItem } from '@/utils/localstorage';

export default {
	components: { AppMain, navbar, sidebar },
	props: {
		pageKey: {
			type: [String, Number],
			default: () => {
				return 0;
			}
		}
	},
	data() {
		return {};
	},
	computed: {
		...mapGetters(['sidebar', 'menuList', 'currentLevelMenu']),
		...mapState('app', ['sidebar', 'sidebarDrag']),
		...mapState('settings', ['fixedHeader']),
		isHideLayout() {
			return !!this.$route.query.hideLayout || getSessionItem('hideLayout');
		},
		childrenMenuList() {
			return this.menuList[this.currentLevelMenu]?.children || [];
		},
		isCollapse() {
			return !this.sidebar.opened;
		},
		sidebarWidth() {
			return this.$store.state.app.sidebarWidth;
		}
	}
};
</script>
<style scoped lang="scss">
.sidebar-drag {
	pointer-events: none;
}
.fixed-header {
	position: fixed;
	top: 0;
	right: 0;
	z-index: 9;
	width: 100%;
	transition: width 0.1s;
}
.el-popup-parent--hidden .fixed-header {
	padding-right: 0;
}
.hideSidebar .fixed-header {
	width: 100%;
}

.mobile .fixed-header {
	width: 100%;
}
</style>
