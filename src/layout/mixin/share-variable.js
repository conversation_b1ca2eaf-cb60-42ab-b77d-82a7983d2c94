import { mapGetters } from 'vuex';
import { deepClone } from '@/utils';
import { preUrl } from '@/config';

export default {
	computed: {
		...mapGetters(['systemInfo', 'rentThemValidate', 'mainMenu']),
		textColor() {
			return this.rentThemValidate.textColor;
		},
		textResetColor() {
			return this.rentThemValidate.textResetColor;
		},
		/**菜单背景图片*/
		menuBg() {
			let url = this.systemInfo.main.menuBg || '';
			let newUrl = /http/gi.test(url) ? url : (preUrl || window.location.origin) + url;
			return newUrl;
		},
		/**默认菜单*/
		mainMenuList() {
			let list = deepClone(this.mainMenu);
			let extend = JSON.parse(this.rentInfo.extend || '{}');
			if (extend.clientBasic?.systemAppConfig) {
				let hideArr = Object.keys(extend.clientBasic.systemAppConfig).filter(key => {
					return extend.clientBasic.systemAppConfig[key] === 'true';
				});
				list = list.filter(item => {
					return item.meta && !hideArr.includes(`hidden${item.meta.appId}`);
				});
			}
			return list;
		}
	},
	methods: {
		/**切换菜单*/
		changeMenu(i) {
			if (i === this.currentMenu) return;
			const item = this.mainMenuList[i];
			let path = item.path || item.mainUrl;
			if (path.indexOf('/') === -1 && !/^http/.test(path)) {
				path = '/' + path;
			}
			if (item.isExternal) {
				// 外链
				if (!item.openType || item.openType === 1) {
					// 内部打开
					let url = path || '';
					let mainUrl = /http/gi.test(url) ? url : (preUrl || window.location.origin) + url;
					this.$router.push(
						`/other-system?url=${encodeURIComponent(mainUrl)}&logoUrlPath=${
							item.logoUrlPath
						}&name=${item.name || item.meta.title}&appId=${item.appId || item.meta.appId}`
					);
				} else {
					window.open(path, '_blank');
				}
			} else {
				// 内链
				if (!item.openType || item.openType === 1) {
					// 内部打开
					let query = item.meta?.query;
					if (query) {
						this.$router.push({
							path,
							query
						});
					} else {
						this.$router.push({
							path
						});
					}
				} else {
					path = window.location.origin + '/coos_desk/#' + path;
					window.open(path, '_blank');
				}
			}
		}
	}
};
