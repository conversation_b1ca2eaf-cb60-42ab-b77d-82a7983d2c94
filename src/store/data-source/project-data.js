/**根据项目加载的项目数据*/
module.exports = {
	// 综管的默认数据
	zg: {
		cdn: [
			'/static_omip_eosssaas/andyui2.0/js/jquery.min.js?flag=1',
			'/static_omip_eosssaas/andyui2.0/js/andyui.js?flag=1',
			'/static_omip_eosssaas/layui/layui.js?flag=1',
			'/static_omip_eosssaas/andyui2.0/css/andyui.min.css',
			'/static_omip_eosssaas/layui/css/layui.css'
		]
	},
	// 经信局项目的默认数据
	jxj: {
		systemInfo: {
			sysMode: '5',
			logoIcon: '',
			menuShowAllLevel: 'false',
			systemAppConfig: {
				hiddenA1006: 'false',
				hiddenA1007: 'false',
				hiddenA1004: 'false',
				hiddenA1005: 'false',
				hiddenA1002: 'false',
				hiddenA1003: 'false'
			},
			name: '经新写作助手',
			logo: '',
			main: {
				mode: 'them-coos',
				menuBg: '',
				guidePageUrl: '',
				indexUrl: '/coos-ai?modeType=wdzs',
				topBg: '',
				showGuidePage: 'false'
			},
			login: {
				mainImg: '',
				modes: ['username'],
				pluginDownloadConfig: '[]',
				bg: '/coos_minio/coos/1930634567229263872.png',
				showPluginDownload: 'false',
				showAppDownload: 'false',
				appIOSQRCode: '',
				appAndroidQRCode: ''
			}
		},
		notUpdateTip: true, // 不提示更新
		hideSocket: true // 不连接socket
	}
};
