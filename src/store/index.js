import Vue from 'vue';
import Vuex from 'vuex';
import getters from './getters';
import app from './modules/app';
import settings from './modules/settings';
import user from './modules/user';
import them from './modules/them';
import rent from './modules/rent';

Vue.use(Vuex);

const store = new Vuex.Store({
	modules: {
		app,
		settings,
		user,
		them,
		rent
	},
	getters
});

export default store;
