import Cookies from 'js-cookie';
import { removeItem, setItem } from '@/utils/localstorage';
import { getTenantConfig } from '@/api/modules/login';

const getDefaultState = () => {
	return {
		sidebar: {
			opened: Cookies.get('sidebarStatus') ? !!+Cookies.get('sidebarStatus') : true,
			withoutAnimation: false
		},
		// 菜单宽度
		sidebarWidth: 240,
		// 菜单当前是否在拖动
		sidebarDrag: false,
		device: 'desktop',
		systemInfo: {},
		wujies: [], //访问过的无界主机域名集合
		iframes: [], //访问过的iframe主机域名集合
		imCount: 0, // 没有im的时候系统消息通知的im数量
		waitDoneCount: 0 // 待办数量
	};
};

const state = getDefaultState();

const mutations = {
	/**重置state*/
	RESET_STATE: state => {
		let newState = getDefaultState();
		Object.keys(newState).forEach(key => {
			state[key] = newState[key];
		});
	},
	/**设置IM系统消息数量*/
	SET_IM_COUNT: (state, value) => {
		if (value === 1) {
			state.imCount += 1;
		} else {
			state.imCount = 0;
		}
	},
	/**设置待办数量*/
	SET_DONE_COUNT: (state, value) => {
		state.waitDoneCount = value;
	},
	TOGGLE_SIDEBAR: state => {
		state.sidebar.opened = !state.sidebar.opened;
		state.sidebar.withoutAnimation = false;
		if (state.sidebar.opened) {
			Cookies.set('sidebarStatus', 1);
		} else {
			Cookies.set('sidebarStatus', 0);
		}
	},
	CLOSE_SIDEBAR: (state, withoutAnimation) => {
		Cookies.set('sidebarStatus', 0);
		state.sidebar.opened = false;
		state.sidebar.withoutAnimation = withoutAnimation;
	},
	TOGGLE_DEVICE: (state, device) => {
		state.device = device;
	},
	SET_SYSTEM_INFO: (state, value) => {
		if (value) {
			state.systemInfo = value;
			setItem('systemInfo', JSON.stringify(value));
		} else {
			state.systemInfo = {};
			removeItem('systemInfo');
		}
	},
	SET_SIDEBAR_WIDTH: (state, value) => {
		state.sidebarWidth = value;
	},
	SET_SIDEBAR_DRAG: (state, value) => {
		state.sidebarDrag = value;
	},
	/**存储iframe缓存ip*/
	SET_IFRAMES: (state, value) => {
		// state.iframes.push(value);
		state.iframes = value;
	},
	/**存储无界缓存ip*/
	SET_WUJIES: (state, value) => {
		state.wujies.push(value);
	}
};

const actions = {
	toggleSideBar({ commit }) {
		commit('TOGGLE_SIDEBAR');
	},
	closeSideBar({ commit }, { withoutAnimation }) {
		commit('CLOSE_SIDEBAR', withoutAnimation);
	},
	toggleDevice({ commit }, device) {
		commit('TOGGLE_DEVICE', device);
	},
	/**获取租户信息*/
	getTenantConfig({ commit }, { tenantId, key = 'clientBasic' }) {
		return new Promise((resolve, reject) => {
			getTenantConfig({ tenantId, key })
				.then(res => {
					const { result, message } = res;
					if (res.code == 200) {
						if (result) {
							commit('SET_SYSTEM_INFO', { ...result });
						}
						resolve(result);
					} else {
						reject(message);
					}
				})
				.catch(error => {
					reject(error);
				});
		});
	}
};

export default {
	namespaced: true,
	state,
	mutations,
	actions
};
