/**租户相关*/
import { setItem } from '@/utils/localstorage';
import { getAiEnter, getMemberList } from '@/api/modules/common';

const getDefaultState = () => {
	return {
		// ai入口列表
		aiEnterList: [],
		// coos助手的配置
		coosConfig: {}
	};
};

const state = getDefaultState();

const mutations = {
	/**重置state*/
	RESET_STATE: state => {
		let newState = getDefaultState();
		Object.keys(newState).forEach(key => {
			state[key] = newState[key];
		});
	},
	/**存储AI入口列表*/
	SET_AI_ENTER_LIST: (state, value) => {
		state.aiEnterList = value;
		setItem('aiEnterList', JSON.stringify(value));
	},
	/**存储coos应用的配置信息*/
	SET_COOS_CONFIG: (state, value) => {
		state.coosConfig = value;
		setItem('coosConfig', JSON.stringify(value));
	}
};

const actions = {
	/**获取ai入口信息*/
	GET_AI_ENTER({ commit }) {
		return new Promise((resolve, reject) => {
			getAiEnter().then(res => {
				const { result, message, code } = res;
				if (code === 200) {
					commit('SET_AI_ENTER_LIST', result);
					resolve();
				} else {
					reject(message);
				}
			});
		});
	},
	/**获取COOS应用配置*/
	GET_COOS_CONFIG({ commit }) {
		return new Promise((resolve, reject) => {
			getMemberList('A1013').then(res => {
				if (res.code === 200) {
					commit('SET_COOS_CONFIG', res.result);
					resolve();
				} else {
					reject();
				}
			});
		});
	}
};

export default {
	namespaced: true,
	state,
	mutations,
	actions
};
