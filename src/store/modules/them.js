import { THEM_MAP } from '@/utils/them';
import { setItem } from '@/utils/localstorage';
import { handleColorLoad, handleSetThemeVar } from '@/utils/color';

const state = {
	/**保存主题相关的js变量*/
	preThem: '',
	//  租户主题
	rentThem: '',
	// 租户主题变量
	rentThemValidate: {}
};

const mutations = {
	/**设置租户css变量*/
	SET_RENT_THEM_VALIDATE: (state, value) => {
		state.rentThemValidate = value;
		// 根据css变量设置uview的默认css配置
		setItem('rentThemValidate', JSON.stringify(value));
	},
	SET_GLOBAL_VAR: (state, value) => {
		// 全局颜色变量同步
		handleSetThemeVar(value);
		// 全局组件换肤色彩同步
		handleColorLoad(value, state.preThem);
		// 应用对应的色系
		document.documentElement.setAttribute('theme-color', value);
	}
};

const actions = {
	SET_THEM: ({ commit, rootGetters }) => {
		return new Promise((resolve, reject) => {
			state.preThem = state.rentThemValidate.coosThemColor || '#1890ff';
			state.rentThem = rootGetters['systemInfo']?.main?.mode || 'them-coos';
			let { globalValidate, setElementDefaultConfig } =
				THEM_MAP[state.rentThem] || THEM_MAP['them-coos'];
			// 保存主题颜色变量
			commit('SET_RENT_THEM_VALIDATE', globalValidate);
			// 设置主题色
			commit('SET_GLOBAL_VAR', globalValidate.coosThemColor);
			setElementDefaultConfig(globalValidate);
			resolve();
		});
	}
};

export default {
	namespaced: true,
	state,
	mutations,
	actions
};
