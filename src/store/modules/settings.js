import config from '@/config';
import { setItem } from '@/utils/localstorage';
import { getFileMaxSize, getMenuList } from '@/api/modules/login';
import { Message } from 'element-eoss';
import { getMenuTitleMap } from '@/utils';
import { getRoleAdmin } from '@/api/modules/common';
import { getLeftMenus } from '@/api/modules/application-market';
import { getDefaultMenuList } from '@/router';

const { showSettings, fixedHeader, sidebarLogo } = config;

const getDefaultState = () => {
	return {
		mainMenu: [], // 主菜单
		flotPosition: {}, // 拖拽AI入口的位置
		fileMax: {},
		showSettings: showSettings,
		fixedHeader: fixedHeader,
		sidebarLogo: sidebarLogo,
		menuList: [], // 基本菜单列表
		currentLevelMenu: '', // 第三种框架模式的一级菜单选中索引
		menuTitleMap: [], // 菜单和名字的映射关系
		appRoles: {}, // 应用权限
		clientSystemMode: '', // 1集成模式2单体模式
		waterConfig: {} // 水印的相关配置
	};
};

const state = getDefaultState();

const mutations = {
	/**重置state*/
	RESET_STATE: state => {
		let newState = getDefaultState();
		Object.keys(newState).forEach(key => {
			state[key] = newState[key];
		});
	},
	CHANGE_SETTING: (state, { key, value }) => {
		// eslint-disable-next-line no-prototype-builtins
		if (state.hasOwnProperty(key)) {
			state[key] = value;
		}
	},
	SET_MODE: (state, value) => {
		state.clientSystemMode = value;
		setItem('clientSystemMode', value);
	},
	/**存储菜单*/
	SET_MENU_LIST: (state, value) => {
		state.menuList = value;
		setItem('menuList', JSON.stringify(value));
		// 存储菜单和名字的映射关系
		let menuTitleMap = getMenuTitleMap(state.menuList);
		state.menuTitleMap = menuTitleMap;
		setItem('menuTitleMap', JSON.stringify(menuTitleMap));
	},
	/**存储用户权限*/
	SET_APP_ROLES: (state, value) => {
		state.appRoles = value;
		setItem('appRoles', JSON.stringify(value));
	},
	/**存储文件大小限制*/
	SET_FILE_MAX: (state, value) => {
		state.fileMax = value;
		setItem('fileMax', JSON.stringify(value));
	},
	/**存储水印配置*/
	SET_WATER_CONFIG: (state, value) => {
		state.waterConfig = { ...state.waterConfig, ...value };
		setItem('waterConfig', JSON.stringify(state.waterConfig));
	},
	/**存储第三种框架的菜单配置*/
	SET_CURRENT_LEVEL_MENU: (state, value) => {
		state.currentLevelMenu = value;
		setItem('currentLevelMenu', value);
	},
	/**设置AI快捷入口的位置*/
	SET_FLOT_POSITION: (state, value) => {
		state.flotPosition = value;
		setItem('flotPosition', JSON.stringify(value));
	},
	/**设置默认主菜单*/
	SET_MAIN_MENU: (state, value) => {
		state.mainMenu = value;
		setItem('mainMenu', JSON.stringify(value));
	}
};

const actions = {
	changeSetting({ commit }, data) {
		commit('CHANGE_SETTING', data);
	},
	/**获取动态主菜单*/
	GET_MAIN_MENU: ({ commit }) => {
		getLeftMenus()
			.then(res => {
				if (res.code === 200) {
					let arr = res?.result || [];
					arr.forEach(item => {
						if (item.icon) {
							item.icon = item.icon.replace('#icon-', '');
						}
						item.path = item.mainUrl;
						// 如果图标是图片资源  添加到链接后面，显示图标使用
						if (item.iconType === 'image') {
							if (/\?/.test(item.path)) {
								item.path = item.path + '&logoUrlPath=' + item.logoUrlPath;
							} else {
								item.path = item.path + '?logoUrlPath=' + item.logoUrlPath;
							}
						}
						item.meta = {
							icon: item.icon,
							appId: item.id,
							title: item.name
						};
					});
					commit('SET_MAIN_MENU', arr);
				} else {
					console.error('获取菜单主失败', res);
					commit('SET_MAIN_MENU', getDefaultMenuList());
				}
			})
			.catch(err => {
				console.error('获取菜单主失败.err', err);
				commit('SET_MAIN_MENU', getDefaultMenuList());
			});
	},
	/**获取菜单*/
	GET_MENU: ({ commit }, value = '') => {
		return new Promise((resolve, reject) => {
			getMenuList({ menuTheme: value })
				.then(res => {
					if (res.code === 200) {
						commit('SET_MENU_LIST', res.result);
					} else {
						Message({ message: res.message, type: 'error' });
					}
					resolve(res);
				})
				.catch(err => {
					reject(err);
				});
		});
	},
	/**获取用户的应用权限*/
	GET_APP_ROLES: ({ commit }) => {
		return new Promise((resolve, reject) => {
			getRoleAdmin()
				.then(res => {
					if (res.code === 200) {
						commit('SET_APP_ROLES', res.result);
					} else {
						Message({ message: res.message, type: 'error' });
					}
					resolve(res);
				})
				.catch(err => {
					reject(err);
				});
		});
	},
	/**获取文件大小限制*/
	GET_FILE_MAX: ({ commit }) => {
		return new Promise((resolve, reject) => {
			getFileMaxSize()
				.then(res => {
					if (res.code === 200) {
						commit('SET_FILE_MAX', JSON.parse(res.result));
					} else {
						Message({ message: res.message, type: 'error' });
					}
					resolve(res);
				})
				.catch(err => {
					reject(err);
				});
		});
	}
};

export default {
	namespaced: true,
	state,
	mutations,
	actions
};
