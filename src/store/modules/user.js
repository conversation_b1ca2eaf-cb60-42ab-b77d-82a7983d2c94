import {
	get_token,
	getToken,
	removeCoosRentApplicationRelyToken,
	removeToken,
	set_token
} from '@/utils/auth';
import { loginOut } from '@/api/modules/login';
import { Message } from 'element-eoss';
import { setItem, removeItem } from '@/utils/localstorage';
import { isEmpty } from '@/utils';
import wfc from '@/wile-fire/wfc/client/wfc';
import { CoosEventTypes } from '@/utils/bus';
// 缓存前缀
const catch_pre = 'coos_desk_';
// 缓存白名单
const whiteLocalItem = [`${catch_pre}preUser`, `${catch_pre}VERSION`, `${catch_pre}systemInfo`];
// const whiteSessionItem = [];
const getDefaultState = () => {
	return {
		preUser: {}, // 上一个用户的信息
		token: getToken(),
		openIm: null, // 是否需要im
		userInfo: {}, // 用户信息
		rentInfo: {}, // 租户信息
		rentList: {}, // 租户列表
		pageTabs: [], // 页面标签
		menuTabs: [], // 菜单标签
		permission_identification: [], // 权限标识集合
		workApp: [], //工作台应用存储
		currentId: '', //选中工作台id
		version: '', // 当前版本
		waitConfig: {}, // 待办配置
		documentConfig: {} // 文档配置
	};
};

const state = getDefaultState();

const mutations = {
	/**重置state*/
	RESET_STATE: state => {
		let newState = getDefaultState();
		Object.keys(newState).forEach(key => {
			state[key] = newState[key];
		});
	},
	/**存储用户信息*/
	SET_USERINFO(state, value) {
		state.userInfo = value;
		setItem('userInfo', JSON.stringify(value));
	},
	SET_USER_VERSION(state, value) {
		state.version = value;
		setItem('version', value);
	},
	/**更新用户信息*/
	UPDATE_USERINFO(state, value) {
		Object.keys(value).forEach(key => {
			state.userInfo[key] = value[key]; // 更新新值
		});
		setItem('userInfo', JSON.stringify(state.userInfo));
	},
	/**存储租户信息*/
	SET_RENT_INFO(state, value) {
		state.rentInfo = value;
		setItem('rentInfo', JSON.stringify(value));
		set_token('X-Coos-Client-Tenant-Id', value.id); // 租户token
	},
	/**存储租户列表*/
	SET_RENT_LIST(state, value) {
		state.rentList = value;
		setItem('rentList', JSON.stringify(value));
	},

	/**清除上一个用户的信息*/
	CLEAR_PRE_USER(state) {
		state.preUser = {};
		setItem('preUser', null);
	},

	/**清除登录信息*/
	REMOVE_INFO(state) {
		/**存储前一个用户*/
		// 防止多个401重复清除，覆盖存储
		if (isEmpty(state.preUser)) {
			let preUser = {
				userId: state.userInfo.id,
				token: getToken(),
				tenantId: get_token('X-Coos-Client-Tenant-Id')
			};
			state.preUser = preUser;
			setItem('preUser', JSON.stringify(preUser));
		}
		// 除了白名单，全部清空
		Object.keys(localStorage).forEach(key => {
			// 只清除我本框架的缓存，根据前缀进行判定
			if (key.indexOf(catch_pre) > -1 && !whiteLocalItem.includes(key)) {
				localStorage.removeItem(key);
			}
		});
		// 除了白名单，全部清空  暂时没有用到session
		// Object.keys(sessionStorage).forEach(key => {
		// 	if (!whiteSessionItem.includes(key)) {
		// 		sessionStorage.removeItem(key);
		// 	}
		// });
		// 移除token
		removeToken();
		removeCoosRentApplicationRelyToken();
		// 退出登录，防止消息通知等出问题
		wfc.disconnect();
		window.vm && window.vm._BUS.$emit(CoosEventTypes.closeSocket);
	},
	/**清空一些租户控制的数据*/
	CLEAR_CACHE() {
		state.pageTabs = [];
		state.menuTabs = [];
		state.workApp = [];
		removeItem('pageTabs');
		removeItem('menuTabs');
		removeItem('workApp');
	},
	/**存储用户权限标识*/
	SET_PERMISSION_IDENTIFICATION(state, value) {
		state.permission_identification = value;
		setItem('permission_identification', JSON.stringify(value));
	},
	/**存储用户标签页*/
	SET_PAGE_TABS: (state, value) => {
		state.pageTabs = value;
		setItem('pageTabs', JSON.stringify(value));
	},
	/**存储菜单标签页*/
	SET_MENU_TABS: (state, value) => {
		state.menuTabs = value;
		setItem('menuTabs', JSON.stringify(value));
	},
	/**存储是否需要im*/
	SET_OPEN_IM: (state, value) => {
		state.openIm = value.toString();
		setItem('openIm', value);
	},
	//工作台菜单列表
	SET_WORKAPP: (state, value) => {
		// 检查是否已存在
		const isAppExists = (appId, url) => {
			return state.workApp.some(item =>
				appId === 'OtherSystem' ? item.url === url : item.appId === appId
			);
		};
		// 检查是否已存在该应用
		if (!isAppExists(value.appId, value.url)) {
			state.workApp.push(value);
			setItem('workApp', JSON.stringify(state.workApp));
			state.currentId = value.id;
			setItem('currentId', value.id);
		} else {
			const matchedItem = state.workApp.find(item => {
				return value.appId === 'OtherSystem' ? item.url === value.url : item.appId === value.appId;
			});
			if (matchedItem) {
				state.currentId = matchedItem.id;
				setItem('currentId', matchedItem.id);
			}
		}
	},
	POP_WORKAPP: (state, value) => {
		state.workApp = state.workApp.filter(item => item.id !== value.id);
		setItem('workApp', JSON.stringify(state.workApp));
	},
	SET_CURRENTID: (state, value) => {
		state.currentId = value;
		setItem('currentId', state.currentId);
	},
	SET_WAIT_CONFIG: (state, value) => {
		state.waitConfig = value;
	},
	SET_DOCUMENT_CONFIG: (state, value) => {
		state.documentConfig = value;
	}
};

const actions = {
	/**重置全部的state属性*/
	RESET_ALL_STATE: ({ commit }) => {
		return new Promise(resolve => {
			commit('RESET_STATE');
			commit('settings/RESET_STATE', null, { root: true });
			commit('app/RESET_STATE', null, { root: true });
			commit('rent/RESET_STATE', null, { root: true });
			resolve();
		});
	},
	/**退出登录*/
	LOGIN_OUT: ({ commit }) => {
		return new Promise((resolve, reject) => {
			loginOut()
				.then(res => {
					if (res.code === 200) {
						Message({ message: '操作成功', type: 'success' });
						commit('REMOVE_INFO');
					} else {
						Message({ message: res.message, type: 'error' });
					}
					resolve(res);
				})
				.catch(err => {
					reject(err);
				});
		});
	}
};

export default {
	namespaced: true,
	state,
	mutations,
	actions
};
