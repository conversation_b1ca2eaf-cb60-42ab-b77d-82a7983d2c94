import { isEmpty } from '@/utils';
import { getItem, setItem } from '@/utils/localstorage';
import { getDictionary } from '@/utils/data-dictionary';
import logoImage from '@/assets/images/common/logo.png';
import loginBg from '@/assets/images/common/bg.png';
import coverImage from '@/assets/images/common/cover.png';
const projectData = require('@/store/data-source/project-data');
const defaultSystemInfo = {
	logo: logoImage,
	logoIcon: '',
	name: 'COOS业务操作系统办公平台',
	login: {
		bg: loginBg,
		mainImg: coverImage
	},
	main: {
		menuBg: '',
		theme: '',
		mode: '',
		topBg: ''
	}
};
const getters = {
	sidebar: state => state.app.sidebar,
	sidebarWidth: state => state.app.sidebarWidth,
	sidebarDrag: state => state.app.sidebarDrag,
	device: state => state.app.device,
	token: state => state.user.token,
	/**工作台列表 */
	workApp: state => {
		if (isEmpty(state.user.workApp)) {
			let workApp = getItem('workApp');
			workApp = workApp ? JSON.parse(workApp) : [];
			state.user.workApp = workApp;
			return state.user.workApp;
		} else {
			return state.user.workApp;
		}
	},
	/**获取用户信息*/
	userInfo: state => {
		if (isEmpty(state.user.userInfo)) {
			let userInfo = getItem('userInfo');
			userInfo = userInfo ? JSON.parse(userInfo) : { realname: '', avatarUrl: '' };
			state.user.userInfo = userInfo;
			return state.user.userInfo;
		} else {
			return state.user.userInfo;
		}
	},
	/**获取用户版本*/
	userVersion: state => {
		if (isEmpty(state.user.version)) {
			let version = getItem('version');
			state.user.version = version;
			return state.user.version;
		} else {
			return state.user.version;
		}
	},
	/**获取租户信息*/
	rentInfo: state => {
		if (isEmpty(state.user.rentInfo)) {
			let rentInfo = getItem('rentInfo');
			rentInfo = rentInfo ? JSON.parse(rentInfo) : { name: '' };
			state.user.rentInfo = rentInfo;
			return state.user.rentInfo;
		} else {
			return state.user.rentInfo;
		}
	},
	/**获取租户列表*/
	rentList: state => {
		if (isEmpty(state.user.rentList)) {
			let rentList = getItem('rentList');
			rentList = rentList ? JSON.parse(rentList) : {};
			state.user.rentList = rentList;
			return state.user.rentList;
		} else {
			return state.user.rentList;
		}
	},
	/**获取用户权限标识集合*/
	permission_identification: state => {
		if (isEmpty(state.user.permission_identification)) {
			let permission_identification = getItem('permission_identification');
			permission_identification = permission_identification
				? JSON.parse(permission_identification)
				: [];
			state.user.permission_identification = permission_identification;
			return state.user.permission_identification;
		} else {
			return state.user.permission_identification;
		}
	},
	/**页面标签*/
	pageTabs: state => {
		if (isEmpty(state.user.pageTabs)) {
			let pageTabs = getItem('pageTabs');
			pageTabs = pageTabs ? JSON.parse(pageTabs) : [];
			state.user.pageTabs = pageTabs;
			return state.user.pageTabs;
		} else {
			return state.user.pageTabs;
		}
	},
	/**菜单的标签页*/
	menuTabs: state => {
		if (isEmpty(state.user.menuTabs)) {
			let menuTabs = getItem('menuTabs');
			menuTabs = menuTabs
				? JSON.parse(menuTabs)
				: [
						{
							fullPath: '/wile-fire',
							meta: {
								appId: getDictionary('应用ID/消息'),
								icon: 'im',
								isMenu: true,
								isMenuTab: true,
								query: {},
								title: '消息'
							},
							name: 'WileFire'
						}
				  ];
			state.user.menuTabs = menuTabs;
			return state.user.menuTabs;
		} else {
			return state.user.menuTabs;
		}
	},
	/**框架模式 1集成模式2单体模式*/
	clientSystemMode: state => {
		if (!state.settings.clientSystemMode) {
			state.settings.clientSystemMode = getItem('clientSystemMode') || '';
		}
		return state.settings.clientSystemMode;
	},
	/**菜单*/
	menuList: state => {
		if (isEmpty(state.settings.menuList)) {
			let menuList = getItem('menuList');
			menuList = menuList ? JSON.parse(menuList) : [];
			state.settings.menuList = menuList;
		}
		return state.settings.menuList;
	},
	/**名字映射关系*/
	menuTitleMap: state => {
		if (isEmpty(state.settings.menuTitleMap)) {
			let menuTitleMap = getItem('menuTitleMap');
			menuTitleMap = menuTitleMap ? JSON.parse(menuTitleMap) : {};
			state.settings.menuTitleMap = menuTitleMap;
		}
		return state.settings.menuTitleMap;
	},
	systemInfo: state => {
		let systemInfo;
		// 如果项目无数据，使用缓存数据
		if (isEmpty(state.app.systemInfo)) {
			systemInfo = getItem('systemInfo');
			systemInfo = systemInfo ? JSON.parse(systemInfo) : {};
		} else {
			systemInfo = state.app.systemInfo;
		}
		// 如果配置无数据，使用默认数据
		if (isEmpty(systemInfo)) {
			let obj =
				projectData[process.env.VUE_APP_PROJECT] &&
				projectData[process.env.VUE_APP_PROJECT].systemInfo
					? projectData[process.env.VUE_APP_PROJECT].systemInfo
					: defaultSystemInfo;
			obj.isSystemInfo = true;
			state.app.systemInfo = obj;
			setItem('systemInfo', JSON.stringify(obj));
		} else {
			state.app.systemInfo = systemInfo;
		}
		return state.app.systemInfo;
	},
	/**获取是否打开im*/
	openIm: state => {
		if (!state.user.openIm) {
			state.user.openIm = getItem('openIm');
		}
		return state.user.openIm;
	},
	/**获取用户应用权限*/
	appRoles: state => {
		if (isEmpty(state.settings.appRoles)) {
			let appRoles = getItem('appRoles');
			state.settings.appRoles = appRoles ? JSON.parse(appRoles) : {};
		}
		return state.settings.appRoles;
	},
	/**获取文件大小限制*/
	fileMax: state => {
		if (isEmpty(state.settings.fileMax)) {
			let fileMax = getItem('fileMax');
			state.settings.fileMax = fileMax ? JSON.parse(fileMax) : {};
		}
		return state.settings.fileMax;
	},
	/**coos助手的配置*/
	coosConfig: state => {
		if (isEmpty(state.rent.coosConfig)) {
			let coosConfig = getItem('coosConfig');
			state.rent.coosConfig = coosConfig ? JSON.parse(coosConfig) : {};
		}
		return state.rent.coosConfig;
	},

	/**获取前一个用户的用户信息*/
	preUser: state => {
		if (isEmpty(state.user.preUser)) {
			let preUser = getItem('preUser');
			state.user.preUser = preUser ? JSON.parse(preUser) : {};
		}
		return state.user.preUser;
	},

	/**获取水印信息*/
	waterConfig: state => {
		if (isEmpty(state.settings.waterConfig)) {
			let waterConfig = getItem('waterConfig');
			state.settings.waterConfig = waterConfig ? JSON.parse(waterConfig) : {};
		}
		return state.settings.waterConfig;
	},

	/**获取第三种框架的菜单索引*/
	currentLevelMenu: state => {
		if (!state.settings.currentLevelMenu && state.settings.currentLevelMenu !== 0) {
			state.settings.currentLevelMenu = parseInt(getItem('currentLevelMenu'));
		}
		return state.settings.currentLevelMenu;
	},

	/**获取AI入口位置*/
	flotPosition: state => {
		if (isEmpty(state.settings.flotPosition)) {
			state.settings.flotPosition = getItem('flotPosition')
				? JSON.parse(getItem('flotPosition'))
				: {};
		}
		return state.settings.flotPosition;
	},

	/**租户主题*/
	rentThem: state => {
		if (!state.them.rentThem) {
			state.them.rentThem = getItem('rentThem') || 'them-coos';
		}
		return state.them.rentThem;
	},
	/**主题js变量*/
	rentThemValidate: state => {
		if (isEmpty(state.them.rentThemValidate)) {
			state.them.rentThemValidate = getItem('rentThemValidate')
				? JSON.parse(getItem('rentThemValidate'))
				: {};
		}
		return state.them.rentThemValidate;
	},
	/**主菜单*/
	mainMenu: state => {
		if (isEmpty(state.settings.mainMenu)) {
			state.settings.mainMenu = getItem('mainMenu') ? JSON.parse(getItem('mainMenu')) : [];
		}
		return state.settings.mainMenu;
	},
	/**ai入口菜单*/
	aiEnterList: state => {
		if (isEmpty(state.rent.aiEnterList)) {
			state.rent.aiEnterList = getItem('aiEnterList') ? JSON.parse(getItem('aiEnterList')) : [];
		}
		return state.rent.aiEnterList;
	}
};
export default getters;
