<svg width="49" height="48" viewBox="0 0 49 48" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Frame 3232">
<rect id="Rectangle 2900" x="0.599854" width="48" height="48" rx="9" fill="var(--brand-1)"/>
<g id="Frame">
<path id="Vector" d="M15.0833 36.0832H34.9166V37.8332H15.0833V36.0832ZM16.2499 14.4998H19.7499V16.2498H16.2499V14.4998ZM16.2499 12.1665H19.7499V13.9165H16.2499V12.1665ZM30.2499 14.4998H33.7499V16.2498H30.2499V14.4998ZM30.2499 12.1665H33.7499V13.9165H30.2499V12.1665ZM23.2499 14.4998H26.7499V16.2498H23.2499V14.4998ZM23.2499 12.1665H26.7499V13.9165H23.2499V12.1665ZM35.7478 17.4165H13.3333V34.0147C13.3333 34.2853 13.4248 34.4656 13.6086 34.6458C13.7923 34.8267 13.9761 34.9165 14.252 34.9165H35.7478C36.0238 34.9165 36.2075 34.8261 36.3913 34.6458C36.575 34.465 36.6666 34.2853 36.6666 34.0147V18.3183C36.6666 18.0477 36.575 17.8674 36.3913 17.6872C36.2162 17.5227 35.9878 17.4267 35.7478 17.4165Z" fill="var(--brand-6)"/>
</g>
<g id="Ellipse 2" filter="url(#filter0_bd_13_6125)">
<circle cx="34.6001" cy="34" r="7" fill="url(#paint0_linear_13_6125)" fill-opacity="0.3" shape-rendering="crispEdges"/>
<circle cx="34.6001" cy="34" r="7" stroke="url(#paint1_linear_13_6125)" stroke-width="0.5" shape-rendering="crispEdges"/>
</g>
<g id="Frame_2">
<path id="Vector_2" d="M37.7 33.0049H36.605C36.37 33.0049 35.705 33.1199 35.48 32.9799C35.435 32.8199 35.58 32.5299 35.62 32.3899C35.735 31.9849 35.845 31.5849 35.96 31.1749L36.45 29.4099C36.515 29.1799 36.215 29.0799 36.09 29.2549L32.13 34.6899C32.04 34.8149 32.165 34.9899 32.305 34.9899H33.91C34.02 34.9899 34.445 34.9199 34.515 35.0249C34.58 35.1099 34.455 35.3749 34.43 35.4699C34.13 36.5099 33.84 37.5499 33.55 38.5899C33.485 38.8199 33.785 38.9199 33.91 38.7449L37.865 33.3099C37.96 33.1799 37.84 33.0049 37.7 33.0049Z" fill="white"/>
</g>
</g>
<defs>
<filter id="filter0_bd_13_6125" x="23.3501" y="22.75" width="22.5" height="24.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="2"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_13_6125"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.839216 0 0 0 0 0.894118 0 0 0 0 0.992157 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_backgroundBlur_13_6125" result="effect2_dropShadow_13_6125"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_13_6125" result="shape"/>
</filter>
<linearGradient id="paint0_linear_13_6125" x1="34.6001" y1="27" x2="34.6001" y2="41" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#EDF1FA"/>
</linearGradient>
<linearGradient id="paint1_linear_13_6125" x1="34.6001" y1="27" x2="34.6001" y2="41" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
</defs>
</svg>
