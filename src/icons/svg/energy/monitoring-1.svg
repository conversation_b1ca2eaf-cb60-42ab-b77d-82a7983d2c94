<svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Frame 3228">
<rect id="Rectangle 2900" width="48" height="48" rx="9" fill="var(--brand-1)"/>
<g id="Frame" clip-path="url(#clip0_13_6078)">
<path id="Vector" d="M37.9017 25.8127L35.3763 14.6731L35.3638 14.6199C34.8961 13.0415 33.491 11.9454 31.8739 11.896H14.3812C11.7793 11.896 10.8275 14.4872 10.7887 14.5976L10.7684 14.6596C10.75 14.74 8.75718 22.8429 8.16457 25.8166C7.84405 27.2245 7.99027 28.3943 8.6129 29.289C9.55023 30.6446 11.2099 30.7996 11.2768 30.8054L34.684 30.8102L34.7237 30.8025C35.937 30.634 36.8346 30.1072 37.3982 29.2435C38.3568 27.7765 37.923 25.8873 37.9017 25.8127ZM11.3716 29.1166C11.179 29.0885 10.3559 28.9278 9.92304 28.2984C9.5909 27.821 9.53087 27.119 9.73906 26.2136C10.3036 23.3842 12.1202 15.9581 12.3225 15.1476C12.4349 14.86 13.0294 13.5838 14.3851 13.5838H31.9117C31.8933 13.5838 33.3148 13.6061 33.8125 15.1002L36.3301 26.2029C36.4124 26.5574 36.5122 27.6041 36.0619 28.2965C35.9554 28.4611 35.6329 28.952 34.5736 29.1205L11.3726 29.1166H11.3716ZM24.2193 33.8846L29.2207 34.9169V36.104H17.0228V34.9178L21.8034 33.8914V31.7224H24.2164V33.8846H24.2184H24.2193ZM13.3896 15.0275L14.6852 14.1502H21.4838L22.4715 15.0275V18.9173L21.4228 19.9214H13.3906L12.5239 18.7914L13.3906 15.0275H13.3896ZM11.8636 21.9472L13.3799 20.6274H21.3192L22.4715 21.9472V26.7791L21.2456 27.9508H11.8636L10.8517 26.6348L11.8626 21.9481L11.8636 21.9472ZM32.8209 15.0275L33.492 18.7914L32.6292 19.9214H24.595L23.5444 18.9173V15.0275L24.5311 14.1502H31.5195L32.8209 15.0275ZM34.1504 21.9472L35.1623 26.6358L34.1504 27.9508H24.7693L23.5444 26.7791V21.9491L24.6987 20.6283H32.635L34.1504 21.9491V21.9472Z" fill="var(--brand-6)"/>
</g>
<g id="Rectangle 2901" filter="url(#filter0_bd_13_6078)">
<rect x="29" y="28" width="12" height="8" rx="1" fill="url(#paint0_linear_13_6078)" fill-opacity="0.3" shape-rendering="crispEdges"/>
<rect x="29" y="28" width="12" height="8" rx="1" stroke="url(#paint1_linear_13_6078)" stroke-width="0.5" shape-rendering="crispEdges"/>
</g>
<rect id="Rectangle 2902" x="31" y="30" width="2" height="5" rx="1" fill="white"/>
<rect id="Rectangle 2903" x="34" y="31" width="2" height="4" rx="1" fill="white"/>
<rect id="Rectangle 2904" x="37" y="32" width="2" height="3" rx="1" fill="white"/>
</g>
<defs>
<filter id="filter0_bd_13_6078" x="24.75" y="23.75" width="20.5" height="18.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="2"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_13_6078"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.839216 0 0 0 0 0.894118 0 0 0 0 0.992157 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_backgroundBlur_13_6078" result="effect2_dropShadow_13_6078"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_13_6078" result="shape"/>
</filter>
<linearGradient id="paint0_linear_13_6078" x1="35" y1="28" x2="35" y2="36" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#EDF1FA"/>
</linearGradient>
<linearGradient id="paint1_linear_13_6078" x1="35" y1="28" x2="35" y2="36" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<clipPath id="clip0_13_6078">
<rect width="30" height="30" fill="white" transform="translate(8 9)"/>
</clipPath>
</defs>
</svg>
