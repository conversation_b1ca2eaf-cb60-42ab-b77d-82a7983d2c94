<svg width="49" height="48" viewBox="0 0 49 48" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Frame 3231">
<rect id="Rectangle 2900" x="0.600098" width="48" height="48" rx="9" fill="var(--brand-1)"/>
<g id="Frame">
<path id="Vector" d="M35.1 24.0222C35.1008 22.328 34.6982 20.6579 33.9255 19.1502C34.2064 18.3945 34.4483 17.625 34.6506 16.8447C35.5206 13.5822 33.5196 11.6537 30.431 11.9582C28.5801 12.1446 26.7724 12.6345 25.0805 13.4082H24.6745C22.2138 13.4205 19.8359 14.2993 17.9586 15.8903C16.0812 17.4813 14.8243 19.6827 14.4085 22.1082C15.8039 20.1566 17.6043 18.5293 19.6865 17.3377C17.649 19.4191 15.854 21.7248 14.336 24.2107L14.249 24.3412C13.0942 26.3155 12.3219 28.4898 11.9725 30.7502C11.494 33.8677 13.0455 35.5207 16.3225 35.1002C17.6277 34.7681 18.8884 34.2814 20.078 33.6502C21.5015 34.3583 23.0702 34.7257 24.66 34.7232C26.8293 34.7134 28.9415 34.0281 30.7034 32.7626C32.4652 31.4971 33.7891 29.7142 34.491 27.6617H28.865C28.3804 28.5641 27.61 29.2801 26.6746 29.6974C25.7391 30.1148 24.6916 30.2098 23.6964 29.9676C22.7011 29.7254 21.8144 29.1598 21.1753 28.3593C20.5362 27.5588 20.1809 26.5689 20.165 25.5447H35.013C35.0741 25.0638 35.1031 24.5794 35.1 24.0947V24.0222ZM16.395 34.1722C14.6405 34.3607 12.364 32.4177 14.945 27.8067C15.7829 29.9769 17.3115 31.8112 19.295 33.0267C18.4003 33.5701 17.4196 33.9575 16.395 34.1722ZM20.2375 22.2822C20.3786 21.1607 20.9241 20.1292 21.7718 19.3814C22.6195 18.6337 23.7109 18.2211 24.8413 18.2211C25.9716 18.2211 27.0631 18.6337 27.9108 19.3814C28.7585 20.1292 29.304 21.1607 29.445 22.2822H20.2375ZM28.575 14.1912C32.925 12.1032 34.5635 14.3362 34.085 16.6127C34.085 16.9317 33.7805 17.7582 33.563 18.4832C32.4113 16.5346 30.6621 15.0092 28.575 14.1332V14.1912Z" fill="var(--brand-6)"/>
</g>
<g id="Rectangle 2901" filter="url(#filter0_bd_13_6112)">
<rect x="29.8" y="28" width="12" height="8" rx="1" fill="url(#paint0_linear_13_6112)" fill-opacity="0.3" shape-rendering="crispEdges"/>
<rect x="29.8" y="28" width="12" height="8" rx="1" stroke="url(#paint1_linear_13_6112)" stroke-width="0.5" shape-rendering="crispEdges"/>
</g>
<rect id="Rectangle 2902" x="31.8" y="30" width="2" height="5" rx="1" fill="white"/>
<rect id="Rectangle 2903" x="34.8" y="31" width="2" height="4" rx="1" fill="white"/>
<rect id="Rectangle 2904" x="37.8" y="32" width="2" height="3" rx="1" fill="white"/>
</g>
<defs>
<filter id="filter0_bd_13_6112" x="25.55" y="23.75" width="20.5" height="18.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="2"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_13_6112"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.839216 0 0 0 0 0.894118 0 0 0 0 0.992157 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_backgroundBlur_13_6112" result="effect2_dropShadow_13_6112"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_13_6112" result="shape"/>
</filter>
<linearGradient id="paint0_linear_13_6112" x1="35.8" y1="28" x2="35.8" y2="36" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#EDF1FA"/>
</linearGradient>
<linearGradient id="paint1_linear_13_6112" x1="35.8" y1="28" x2="35.8" y2="36" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
</defs>
</svg>
