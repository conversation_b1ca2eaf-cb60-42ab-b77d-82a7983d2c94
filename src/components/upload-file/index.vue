<!--
 * @Description: 全局上传组件
 * @Version: 1.0
 * @Autor: FH.hao
 * @Date: 2024-06-04 17:41:37
-->
<template>
	<div class="custom-upload">
		<div v-if="!customFile && mode === 'image'" class="custom-upload-image">
			<div v-if="isPreview" class="custom-upload-image-preview">
				<div
					v-for="(item, index) of showPreviewArr"
					:key="index"
					class="custom-upload-imgPre"
					:style="{
						height: (modelHeight || modelSize) + 'px',
						width: (modelWidth || modelSize) + 'px',
						borderRadius: radius + 'px'
					}"
				>
					<el-image
						v-if="item.type === 'image'"
						class="custom-upload-imgPre-img"
						:style="{ borderRadius: radius + 'px' }"
						:preview-src-list="[item.cover]"
						:src="item.cover"
					/>
					<div
						v-else
						class="svg-con"
						:class="{ 'custom-upload-file-download': canDownload && item.process === 'success' }"
					>
						<svg-icon
							:icon-class="item.cover"
							class="custom-upload-imgPre-img"
							@click="changePreUrl(index)"
						></svg-icon>
					</div>
					<div v-if="!disabled" class="custom-upload-imgPre-del">
						<i
							class="el-icon-delete custom-upload-imgPre-del-icon"
							@click="delFile(item, index)"
						></i>
					</div>
					<div v-if="item.process !== 'success'" class="custom-upload-imgPre-process">
						<div
							class="custom-upload-imgPre-process-con"
							:class="{ error: item.status === 'fail' }"
							:style="{ width: item.process }"
						></div>
					</div>
				</div>
			</div>
			<div
				v-if="!disabled && showPreviewArr.length < limit"
				v-loading="showLoading && uploadLoading"
				:style="{
					height: (modelHeight || modelSize) + 'px',
					width: (modelWidth || modelSize) + 'px',
					borderRadius: radius + 'px'
				}"
				class="custom-upload-button"
				@click="uploadFile"
			>
				<slot v-if="customButton" name="custom-button"></slot>
				<i v-else class="coos-iconfont icon-xinzeng custom-upload-button-icon"></i>
			</div>
			<input
				ref="uploadFileInput"
				type="file"
				style="display: none"
				:multiple="multiple"
				:accept="computedAccept"
				@change="getFile"
			/>
		</div>
		<div v-else-if="!customFile && mode === 'file'" class="custom-upload-file">
			<div v-if="isPreview" class="custom-upload-file-preview">
				<div
					v-for="(item, index) of showPreviewArr"
					:key="index"
					class="custom-upload-file-pre"
					@click="changePreUrl(index)"
				>
					<el-tooltip :open-delay="1000" placement="bottom" :content="item.name">
						<div
							:class="{
								'custom-upload-file-bigImg': item.type === 'image',
								'custom-upload-file-download':
									item.type !== 'image' && canDownload && item.process === 'success'
							}"
							class="custom-upload-file-pre-box"
						>
							<svg-icon class="fileTypes" :icon-class="item.type"></svg-icon>
							<span class="custom-upload-file-pre-text">{{ item.name }}</span>
						</div>
					</el-tooltip>
					<div v-if="item.process !== 'success'" class="custom-upload-file-pre-process">
						<div
							:style="{ width: item.process }"
							class="custom-upload-file-pre-process-con"
							:class="{ error: item.status === 'fail' }"
						></div>
					</div>
					<i
						v-if="!disabled"
						class="el-icon-circle-close custom-upload-file-pre-del"
						@click.stop.prevent="delFile(item, index)"
					></i>
				</div>
			</div>
			<div
				v-if="!disabled && customButton && showPreviewArr.length < limit"
				v-loading="showLoading && uploadLoading"
				@click="uploadFile"
			>
				<slot name="custom-button"></slot>
			</div>
			<div
				v-if="!disabled && !customButton && showPreviewArr.length < limit"
				v-loading="showLoading && uploadLoading"
				class="custom-upload-file-button"
				@click="uploadFile"
			>
				上传
			</div>
			<el-image
				ref="previewDom"
				style="width: 0; height: 0"
				:src="imagePreUrl"
				:preview-src-list="[imagePreUrl]"
			/>
			<input
				ref="uploadFileInput"
				type="file"
				style="display: none"
				:multiple="multiple"
				:accept="computedAccept"
				@change="getFile"
			/>
		</div>
		<div v-else-if="customFile" class="custom-upload-image" :class="{ block: 'noFlex' }">
			<slot :upLoadFile="upLoadFile" :showPreviewArr="showPreviewArr" name="custom-file"></slot>
			<div
				v-if="alwaysShowUploadButton || (!disabled && showPreviewArr.length < limit)"
				v-loading="showLoading && uploadLoading"
				style="display: flex; align-items: center"
			>
				<!-- <slot name="custom-button"></slot> -->

				<!-- 不希望点击空白也会触发上传事件 -->
				<div class="custom-upload-file-content" @click="uploadFile">
					<slot name="custom-button"></slot>
				</div>
			</div>
			<input
				ref="uploadFileInput"
				type="file"
				style="display: none"
				:multiple="multiple"
				:accept="computedAccept"
				@change="getFile"
			/>
		</div>
	</div>
</template>

<script>
import getBackground from '@/utils/get-file-icon';
import { preUrl } from '@/config';
import { customUploadFile, getFileDetail, multipleUpload } from '@/api/modules/component';
import axios from 'axios';
const { customUploadFileHandler, customUploadFileCancel } = customUploadFile();
export default {
	name: 'UploadFile',
	props: {
		/**上传加载效果*/
		showLoading: {
			type: Boolean,
			default: () => {
				return false;
			}
		},
		/**回显的文件id集合*/
		value: {
			type: String,
			default: () => {
				return '';
			}
		},
		/**数据格式是ID还是URL*/
		dataType: {
			type: String,
			default: () => {
				return 'id'; // url模式只支持单文件  filePath
			}
		},
		/**允许选择的文件类型.png,.jpg也支持大模块image,video,audio*/
		accept: {
			type: Array,
			default: () => {
				return [];
			}
		},
		/**是否多选，默认单选*/
		radius: {
			type: Number,
			default: () => {
				return 8;
			}
		},
		/**是否多选，默认单选*/
		multiple: {
			type: Boolean,
			default: () => {
				return true;
			}
		},
		/**上传组件宽度*/
		modelWidth: {
			type: Number,
			default: () => {
				return 0;
			}
		},
		/**上传组件高度*/
		modelHeight: {
			type: Number,
			default: () => {
				return 0;
			}
		},
		/**上传组件大小*/
		modelSize: {
			type: Number,
			default: () => {
				return 120;
			}
		},
		/**组件模式image/file*/
		mode: {
			type: String,
			default: () => {
				return 'image';
			}
		},
		/**上传提示信息*/
		tip: {
			type: String,
			default: () => {
				return 'asasasas';
			}
		},
		/**上传文件的其他参数*/
		otherParams: {
			type: Object,
			default: () => {
				return {};
			}
		},
		/**是否可以下载*/
		canDownload: {
			type: Boolean,
			default: () => {
				return false;
			}
		},
		/**是否支持上传多文件*/
		limit: {
			type: Number,
			default: () => {
				return 1;
			}
		},
		/**是否自定义上传按钮*/
		customButton: {
			type: Boolean,
			default: () => {
				return false;
			}
		},
		/**是否预览*/
		isPreview: {
			type: Boolean,
			default: () => {
				return true;
			}
		},
		/**自定义预览*/
		customFile: {
			type: Boolean,
			default: () => {
				return false;
			}
		},
		/**自定义的上传样式不采用弹性*/
		noFlex: {
			type: Boolean,
			default: () => {
				return false;
			}
		},
		/**上传接口 单文件|多文件*/
		uploadType: {
			type: String,
			default: () => {
				return 'single'; // multiple多个
			}
		},
		/**是否能编辑*/
		disabled: {
			type: Boolean,
			default: () => {
				return false;
			}
		},
		/**总是显示按钮*/
		alwaysShowUploadButton: {
			type: Boolean,
			default: () => {
				return false;
			}
		}
	},
	data() {
		return {
			showPreviewArr: [], // 筛选掉上传中被关闭的数据
			cancelUpload: [], // 中断上传的数组
			uploadLoading: false,
			previewArr: [], // 上传文件的信息
			upLoadFile: [], // 上传结果
			imagePreUrl: '', // 图片预览地址
			assetsUrlPre: preUrl // 全局的变量名
		};
	},
	computed: {
		computedAccept() {
			let arr = [];
			this.accept.forEach(key => {
				if (key === 'image') {
					arr.push('image/*');
				} else if (key === 'video') {
					arr.push('video/*');
				} else if (key === 'audio') {
					arr.push('audio/*');
				} else {
					arr.push('.' + key);
				}
			});
			let accept = arr.join(',');
			return accept;
		}
	},
	watch: {
		/**数据发生变化通知外层*/
		upLoadFile: {
			deep: true,
			handler: function (newVal) {
				// console.log('upLoadFile-------------');
				//所有的文件ID或者URL
				let ids;
				if (this.dataType === 'url') {
					ids = newVal.map(res => {
						return res.fileUrl;
					});
				} else if (this.dataType === 'filePath') {
					ids = newVal.map(res => {
						return res.filePath;
					});
				} else {
					ids = newVal.map(res => {
						return res.fileId;
					});
				}
				//双向绑定v-model的数据
				this.$emit('input', ids.join(','));
				this.$emit('change', ids.join(','));
				//通知父组件上传成功的回调函数,传递所有参数
				this.$emit('success', newVal);
			}
		},
		previewArr: {
			deep: true,
			handler(newVal) {
				// console.log(newVal, '默认上传参数');
				// console.log(this.cancelUpload, '默认上传参数');
				this.showPreviewArr = newVal.filter((item, index) => {
					return !this.cancelUpload.includes(index);
				});
				// console.log(this.isPreview);
				this.$emit('onProcess', this.showPreviewArr);
			}
		},
		value(newVal) {
			// this.$emit('success', this.upLoadFile);
			// upLoadFile还没有值的时候props.value有初始值要进行回显
			if (newVal && this.upLoadFile.length === 0) {
				// 现成的图片链接
				if (this.dataType === 'url') {
					let url = /http/gi.test(this.value) ? this.value : this.assetsUrlPre + this.value;
					this.initData(url);
				} else if (this.dataType === 'filePath') {
					let fileArr = this.value.split(',');
					fileArr.forEach(item => {
						let url = /http/gi.test(item) ? item : this.assetsUrlPre + item;
						this.initData(url, item);
					});
				} else if (typeof this.value === 'string') {
					this.getInitData();
				} else if (Array.isArray(this.value)) {
					this.upLoadFile = this.value;
					this.previewArr = this.value;
				}
			}
		}
	},
	mounted() {
		if (this.value && this.upLoadFile.length === 0) {
			// 现成的图片链接
			if (this.dataType === 'url') {
				let url = /http/gi.test(this.value) ? this.value : this.assetsUrlPre + this.value;
				this.initData(url);
			} else if (this.dataType === 'filePath') {
				let fileArr = this.value.split(',');
				fileArr.forEach(item => {
					let url = /http/gi.test(item) ? item : this.assetsUrlPre + item;
					this.initData(url, item);
				});
			} else if (typeof this.value === 'string') {
				this.getInitData();
			} else if (Array.isArray(this.value)) {
				this.upLoadFile = this.value;
				this.previewArr = this.value;
			}
		}
	},
	methods: {
		/**初始化静态数据*/
		initData(url, filePath) {
			this.upLoadFile.push({
				fileType: 'image',
				fileUrl: url,
				cover: url,
				url,
				filePath
			});
			this.previewArr.push({
				type: 'image',
				cover: url,
				url,
				process: 'success',
				status: 'success',
				filePath
			});
		},
		/**有初始值的时候请求图片回显接口，把文件图片回显出来*/
		getInitData() {
			getFileDetail(this.value).then(res => {
				res.result.forEach(item => {
					let cover =
						item.fileType === 'image'
							? this.assetsUrlPre + item.filePath
							: getBackground({ name: item.originalFileName });
					this.upLoadFile.push(item);
					this.previewArr.push({
						id: item.fileId,
						type: item.fileType,
						cover,
						url: item.fileUrl,
						size: item.fileSize,
						name: item.originalFileName,
						filePath: item.filePath,
						process: 'success',
						status: 'success'
					});
				});
			});
		},
		/**点击上传*/
		uploadFile() {
			if ((this.showLoading && this.uploadLoading) || this.disabled) return;
			this.$refs.uploadFileInput.click();
		},
		/**提供给外层调用*/
		del(index) {
			let item = this.previewArr[index];
			this.delFile(item, index);
		},
		/**
		 * 删除对应的数据
		 * 如果上传成功，要删除本地数据和上传成功的数据
		 * 如果正在上传要提示上传中
		 * 如果上传失败就只用删除本地数据
		 * */
		delFile(item, i) {
			// 如果是多文件的接口，不允许中途删除
			if (this.uploadLoading && this.uploadType === 'multiple') {
				this.$message.warning('上传中，切勿删除！');
				return;
			}
			if (item.status === 'success') {
				let index = this.upLoadFile.find(item => item.fileId === this.previewArr[i].fileId);
				this.previewArr.splice(i, 1);
				this.upLoadFile.splice(index, 1);
			} else if (item.status === 'pending') {
				customUploadFileCancel('customUploadFile' + i);
				// 如果正在上传，就等上传完成再删除
				if (this.uploadLoading) {
					this.cancelUpload.push(i);
				} else {
					this.previewArr.splice(i, 1);
				}
				// this.$message.warning(`上传中！请等待`);
			} else {
				this.previewArr.splice(i, 1);
			}
		},
		/**改变预览的图片链接*/
		changePreUrl(index) {
			let localFile = this.previewArr[index];
			/**图片预览*/
			if (localFile.type === 'image') {
				this.imagePreUrl = localFile.url; // 不取线上链接，因为可能未上传完成
				this.$refs.previewDom.clickHandler();
			} else if (this.canDownload && localFile.process === 'success') {
				this.download(index);
			}
		},
		download(index) {
			let item = this.upLoadFile[index]; // 上传完成才有链接
			let href = this.assetsUrlPre + item.filePath;
			axios
				.get(href, { responseType: 'blob' })
				.then(response => {
					const blob = new Blob([response.data]);
					const link = document.createElement('a');
					link.href = URL.createObjectURL(blob);
					link.download = item.originalFileName;
					link.click();
					URL.revokeObjectURL(link.href);
				})
				.catch(console.error);
			/**如果支持下载*/
			// let a = document.createElement('a'); //创建一个a标签元素
			// a.style.display = 'none'; //设置元素不可见
			// a.download = item.originalFileName;
			// a.setAttribute('download', item.originalFileName);
			// a.href = this.assetsUrlPre + item.filePath; //设置下载地址，http地址
			// console.log('下载地址=====', a.href);
			// console.log('下载名字=====', a.download);
			// document.body.appendChild(a); //追加dom元素
			// a.click(); //触发点击,下载
			// document.body.removeChild(a); //删除dom元素
		},
		/**选择文件*/
		getFile(e) {
			this.uploadLoading = true;
			let fileList = e.target.files; // 选中的文件列表
			let fileArr = Array.from(fileList);
			let limitArr = fileArr.concat(this.previewArr);
			if (limitArr.length > this.limit) {
				this.$message.warning(`最多只能上传${this.limit}个文件`);
				this.uploadLoading = false;
				return;
			}
			this.$refs.uploadFileInput.value = ''; // 清空已经选择的东西，防止删除之后选中相同的文件无法上传
			if (this.uploadType === 'single') {
				this.singleUpload(fileArr);
			} else {
				this.multipleUpload(fileArr);
			}
		},
		/**多个文件上传*/
		multipleUpload(fileArr) {
			let currentLength = this.previewArr.length;
			fileArr.forEach((file, i) => {
				// 每一个文件上传回显处理都放入promise数组
				const fileShow = async () => {
					/**回显文件*/
					if (/image/gi.test(file.type)) {
						let fileReader = new FileReader(); // 文件读取器
						await fileReader.readAsDataURL(file);
						await new Promise(resolve => {
							fileReader.onload = () => {
								this.previewArr.push({
									type: 'image',
									cover: fileReader.result,
									url: '',
									name: file.name,
									process: '0',
									size: file.size,
									status: 'pending'
								});
								resolve(0);
							};
						});
					} else {
						this.previewArr.push({
							type: 'file',
							cover: getBackground(file),
							url: '',
							name: file.name,
							process: '0',
							size: file.size,
							status: 'pending'
						});
					}
				};
				fileShow();
			});
			multipleUpload({ files: fileArr }, process => {
				// 根据定位的文件所在索引位置，赋值进度条
				let totalProcess =
					((process.loaded / process.total) * 100).toFixed(2) > 99
						? 99
						: ((process.loaded / process.total) * 100).toFixed(2) + '%';
				this.previewArr.forEach((item, index) => {
					if (index > currentLength - 1) {
						this.previewArr[index].process = totalProcess;
					}
				});
			})
				.then(res => {
					this.uploadLoading = false;
					if (res.code === 200) {
						res.result.forEach((item, index) => {
							let i = currentLength + index;
							if (item.fileUrl) {
								this.previewArr[i].status = 'success';
								this.previewArr[i].process = 'success';
								this.previewArr[i].fileId = item.fileId;
								this.previewArr[i].url = item.fileUrl;
								this.upLoadFile.push(item);
							} else {
								this.previewArr[i].status = 'fail';
							}
						});
					} else {
						this.previewArr.forEach((item, index) => {
							if (index > currentLength - 1) {
								item.status = 'fail';
							}
						});
						this.$message.error(res.message);
					}
				})
				.catch(err => {
					this.previewArr.forEach((item, index) => {
						if (index > currentLength - 1) {
							item.status = 'fail';
						}
					});
				});
		},
		/**单文件上传接口*/
		singleUpload(fileArr) {
			let promiseArr = []; // 全部同步执行完成再执行下一步
			// 循环上传文件处理回显
			fileArr.forEach((file, i) => {
				promiseArr.push(
					// 每一个文件上传回显处理都放入promise数组
					new Promise((resolve, reject) => {
						const fileShow = async () => {
							/**回显文件*/
							if (/image/gi.test(file.type)) {
								let fileReader = new FileReader(); // 文件读取器
								await fileReader.readAsDataURL(file);
								await new Promise(resolve => {
									fileReader.onload = () => {
										this.previewArr.push({
											type: 'image',
											cover: fileReader.result,
											url: '',
											name: file.name,
											process: '0',
											size: file.size,
											status: 'pending'
										});
										resolve(0);
									};
								});
							} else {
								this.previewArr.push({
									type: 'file',
									cover: getBackground(file),
									url: '',
									name: file.name,
									process: '0',
									size: file.size,
									status: 'pending'
								});
							}
							// 因为数组在实时变化，所以定位当前文件所在索引
							let currentIndex = this.previewArr.length - 1;
							/**上传文件同步化，因为多个文件放入promiseAll会导致定位不到具体哪个文件报错*/
							customUploadFileHandler(
								{ file, ...this.otherParams },
								process => {
									// 根据定位的文件所在索引位置，赋值进度条
									const progress = ((process.loaded / process.total) * 100).toFixed(2);
									const progressValue = progress > 99 ? '99%' : progress + '%';
									this.previewArr.splice(currentIndex, 1, {
										...this.previewArr[currentIndex],
										process: progressValue
									});
								},
								'customUploadFile' + currentIndex
							)
								.then(res => {
									if (res.code === 200) {
										this.previewArr[currentIndex].status = 'success';
										this.previewArr[currentIndex].process = 'success';
										this.previewArr[currentIndex].fileId = res.result.fileId;
										this.previewArr[currentIndex].url = res.result.fileUrl;
										this.upLoadFile.push(res.result);
										resolve('success');
									} else {
										this.previewArr[currentIndex].status = 'fail';
										this.$message.error(res.message);
										resolve('fail');
									}
								})
								.catch(err => {
									/**监听到定位的文件上传失败改变状态*/
									this.previewArr[currentIndex].status = 'fail';
									this.$message.error(err);
									resolve(err);
								});
						};
						fileShow();
					})
				);
			});
			Promise.all(promiseArr)
				.then(res => {
					console.log(res);
				})
				.catch(err => {
					console.log(err);
				})
				.finally(res => {
					this.uploadLoading = false;
					this.cancelUpload.forEach(index => {
						this.previewArr.splice(index, 1);
					});
					this.cancelUpload = [];
				});
		}
	}
};
</script>

<style scoped lang="scss">
/**图片模式*/
@mixin image-block {
	display: inline-flex;
	align-items: center;
	justify-content: center;
	border: 1px solid #dce3e7;
	cursor: pointer;
	margin-right: 10px;
}

.custom-upload {
	//padding: 8px;
}

.custom-upload-image {
	display: flex;
	flex-wrap: wrap;
	align-items: center;

	&-preview {
		@include flexBox(flex-start);
	}

	.custom-upload-imgPre {
		@include image-block;
		border: none;
		position: relative;

		.svg-con {
			width: 100%;
			height: 100%;
			position: relative;
		}

		&-img {
			width: 100%;
			height: 100%;
			border: none;
			margin: 0;
		}

		&-del {
			position: absolute;
			right: 0;
			top: 0;
			width: 24px;
			height: 24px;
			display: flex;
			align-items: center;
			justify-content: center;
			background: rgba(0, 0, 0, 0.4);
			border-radius: 4px;
			z-index: 999;

			&-icon {
				font-size: 16px;
				color: #ffffff;
			}
		}

		&-process {
			position: absolute;
			left: 10%;
			bottom: 10px;
			height: 10px;
			width: 80%;
			border-radius: 50px;
			background: #c0c0c0;

			&-con {
				border-radius: 50px;
				background: #00bb00;
				height: 100%;
			}
		}
	}

	.custom-upload-button {
		@include image-block;

		&-icon {
			font-size: 28px;
			color: $borderColor;
		}
	}
}

.block {
	display: block;
}

/**file模式的样式*/
@mixin file-block {
	border-radius: 4px;
	display: inline-flex;
	align-items: center;
	justify-content: center;
	border: 1px solid #c3c3c3;
	cursor: pointer;
	margin-right: 10px;
	padding: 5px 8px;
}

.custom-upload-file {
	display: flex;
	align-items: center;
	flex-wrap: wrap;

	&-preview {
		@include flexBox(flex-start);
	}

	&-pre {
		@include file-block;
		position: relative;
		&-box {
			display: flex;
			align-items: center;
		}
		&-text {
			max-width: 120px;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
		}
		&-process {
			position: absolute;
			width: 90%;
			height: 5px;
			left: 5%;
			bottom: 2px;
			background: #c0c0c0;
			border-radius: 50px;

			&-con {
				border-radius: 50px;
				background: #00bb00;
				height: 100%;
			}
		}

		&-del {
			position: absolute;
			top: -8px;
			right: -8px;
			font-size: 12px;
			color: #c0c0c0;
			z-index: 666;
		}
	}

	&-bigImg {
		&:hover {
			&::after {
				z-index: 555;
				content: '预览';
				width: calc(100% + 2px);
				height: calc(100% + 2px);
				display: flex;
				align-items: center;
				justify-content: center;
				background: rgba(0, 0, 0, 0.4);
				color: #ffffff;
				border-radius: 4px;
				position: absolute;
				top: -1px;
				left: -1px;
			}
		}
	}

	&-button {
		@include file-block;
		background: var(--brand-6);
		padding: 5px 8px;
		border: none;
		color: #ffffff;
	}
}

.custom-upload-file-download {
	&:hover {
		&::after {
			z-index: 555;
			content: '下载';
			width: calc(100% + 2px);
			height: calc(100% + 2px);
			display: flex;
			align-items: center;
			justify-content: center;
			background: rgba(0, 0, 0, 0.4);
			color: #ffffff;
			border-radius: 4px;
			position: absolute;
			top: -1px;
			left: -1px;
		}
	}
}

.error {
	background: red !important;
}
.fileTypes {
	margin-right: 8px;
}
.custom-upload-file-content {
	//display: inline-block;
}
</style>
