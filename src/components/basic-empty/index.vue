<template>
	<div v-if="!loading && data.length === 0" class="empty">
		<el-empty
			:image="assetsUrl + '/common/img/' + name + '.png'"
			:description="description"
		></el-empty>
	</div>
</template>

<script>
import { assetsUrl } from '@/config';
export default {
	name: 'BasicEmpty',
	props: {
		loading: {
			type: Boolean,
			default: false
		},
		data: {
			type: Array,
			default: () => []
		},
		name: {
			type: String,
			default: 'no-data'
		},
		description: {
			type: String,
			default: '暂无数据'
		}
	},
	data() {
		return {
			assetsUrl
		};
	}
};
</script>

<style scoped lang="scss">
.empty {
	height: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
}
</style>
