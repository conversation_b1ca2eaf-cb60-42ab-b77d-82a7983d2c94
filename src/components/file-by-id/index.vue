<template>
	<div class="fileById">
		<div
			v-for="(item, index) of previewArr"
			:key="index"
			class="fileById-img"
			:class="{
				preview: item.type === 'image' && canPreView,
				download: item.type !== 'image' && canDownLoad
			}"
			:style="{
				width: (width || size) + 'px',
				height: (height || size) + 'px',
				...moreStyle
			}"
			@click="openBigImg(item)"
		>
			<img
				v-if="item.type === 'image'"
				:src="item.url"
				class="image"
				alt=""
				:style="{
					width: (width || size) + 'px',
					height: (height || size) + 'px',
					...moreStyle,
					margin: 0
				}"
			/>
			<svg-icon
				v-else
				class="image"
				:icon-class="item.url"
				:style="{
					width: (width || size) + 'px',
					height: (height || size) + 'px',
					...moreStyle
				}"
			></svg-icon>
		</div>
		<svg-icon
			v-if="previewArr.length === 0 && !defaultFontIcon"
			:style="{ width: (width || size) + 'px', height: (height || size) + 'px', ...moreStyle }"
			class="fileById-img"
			:icon-class="defaultIcon"
		></svg-icon>
		<div
			v-else-if="previewArr.length === 0"
			:style="{ width: (width || size) + 'px', height: (height || size) + 'px', ...moreStyle }"
			class="default-font-icon"
		>
			{{ defaultFontIcon }}
		</div>
		<el-image
			ref="previewDom"
			style="width: 0; height: 0"
			:src="imagePreUrl"
			:preview-src-list="[imagePreUrl]"
		/>
	</div>
</template>

<script>
import getBackground from '@/utils/get-file-icon';
import { preUrl } from '@/config';
import { getFileDetail } from '@/api/modules/component';
import { previewFile } from '@/utils';

export default {
	name: 'FileById',
	props: {
		// 回显的文件id集合
		value: {
			type: [String, Array],
			default: () => {
				return '';
			}
		},
		// 宽度
		width: {
			type: Number,
			default: () => {
				return 0;
			}
		},
		// 高度
		height: {
			type: Number,
			default: () => {
				return 0;
			}
		},
		// 宽高简写的尺寸
		size: {
			type: Number,
			default: () => {
				return 80;
			}
		},
		// 样式拓展
		moreStyle: {
			type: Object,
			default: () => {
				return {};
			}
		},
		// 是否支持预览
		canPreView: {
			type: Boolean,
			default: () => {
				return false;
			}
		},
		// 是否支持下载
		canDownLoad: {
			type: Boolean,
			default: () => {
				return false;
			}
		},
		// 默认图标
		defaultIcon: {
			type: String,
			default: () => {
				return 'default-icon';
			}
		},
		// 默认蓝底文字图标
		defaultFontIcon: {
			type: String,
			default: () => {
				return '';
			}
		}
	},
	data() {
		return {
			assetsUrlPre: preUrl, // 全局的变量名
			previewArr: [], // 预览对象
			imagePreUrl: '' // 当前预览的链接
		};
	},
	watch: {
		value(newVal) {
			// upLoadFile还没有值的时候props.value有初始值要进行回显
			// 现成的图片链接
			if (/\./gi.test(newVal)) {
				this.initData([newVal]);
			} else if (Array.isArray(newVal)) {
				this.initData(newVal);
			} else if (newVal) {
				this.getInitData();
			}
		}
	},
	mounted() {
		if (/\./gi.test(this.value)) {
			this.initData([this.value]);
		} else if (Array.isArray(this.value)) {
			this.initData(this.value);
		} else if (this.value) {
			this.getInitData();
		}
	},
	methods: {
		/**初始化静态数据*/
		initData(urlArr) {
			this.previewArr = [];
			urlArr.forEach(url => {
				let newUrl = /http/gi.test(url) ? url : this.assetsUrlPre + url;
				this.previewArr.push({
					type: 'image',
					url: newUrl,
					process: 'success',
					status: 'success'
				});
			});
		},
		/**有初始值的时候请求图片回显接口，把文件图片回显出来*/
		getInitData() {
			getFileDetail(this.value).then(res => {
				let arr = [];
				res.result.forEach(item => {
					let url =
						item.fileType === 'image'
							? this.assetsUrlPre + item.filePath
							: getBackground({ name: item.originalFileName });
					arr.push({
						type: item.fileType,
						url,
						name: item.originalFileName,
						fileUrl: item.fileUrl,
						source: item.filePath
					});
				});
				this.previewArr = arr;
			});
		},
		/**打开大图*/
		openBigImg(item) {
			const { type, url, name, source, fileUrl } = item;
			if (type === 'image' && this.canPreView) {
				this.imagePreUrl = url;
				this.$refs.previewDom.clickHandler();
			} else if (this.canDownLoad) {
				let a = document.createElement('a'); //创建一个a标签元素
				a.style.display = 'none'; //设置元素不可见
				a.download = name;
				a.href = this.assetsUrlPre + source; //设置下载地址，http地址
				document.body.appendChild(a); //追加dom元素
				a.click(); //触发点击,下载
				document.body.removeChild(a); //删除dom元素
			} else if (fileUrl) {
				//预览
				previewFile(fileUrl);
			}
		}
	}
};
</script>

<style scoped lang="scss">
.fileById {
	display: flex;
	&-img {
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 4px;
		margin-right: 8px;
		position: relative;
		font-size: 9px;
		.image {
			height: 100%;
			width: 100%;
		}
	}
	.default-font-icon {
		background: var(--brand-6);
		font-size: 14px;
		font-weight: 500;
		color: #ffffff;
		margin-right: 8px;
		flex-shrink: 0;
		border-radius: 4px;
		@include flexBox();
	}
	.preview {
		&:hover {
			&::after {
				cursor: pointer;
				z-index: 555;
				content: '预览';
				width: 100%;
				height: 100%;
				display: flex;
				align-items: center;
				justify-content: center;
				background: rgba(0, 0, 0, 0.4);
				color: #ffffff;
				border-radius: 4px;
				position: absolute;
				top: 0;
				left: 0;
			}
		}
	}
	.download {
		&:hover {
			&::after {
				cursor: pointer;
				z-index: 555;
				content: '下载';
				width: 100%;
				height: 100%;
				display: flex;
				align-items: center;
				justify-content: center;
				background: rgba(0, 0, 0, 0.4);
				color: #ffffff;
				border-radius: 4px;
				position: absolute;
				top: 0;
				left: 0;
			}
		}
	}
}
</style>
