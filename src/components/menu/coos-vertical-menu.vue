<template>
	<div class="menu-box">
		<el-menu
			:default-active="defaultActive"
			mode="vertical"
			active-text-color="#045DDA"
			@select="handleSelect"
		>
			<template v-for="item in items">
				<CoosMenuItem
					v-if="item.appRole"
					:key="item.index"
					v-appRole="item.appRole"
					:item="item"
					class="el-menu-vertical-box"
					:is-auth="auth"
				/>
				<CoosMenuItem
					v-else
					:key="item.index"
					:item="item"
					:is-auth="auth"
					class="el-menu-vertical-box"
				/>
			</template>
		</el-menu>
	</div>
</template>
<script>
import CoosMenuItem from './coos-menu-item.vue';
import { getItem } from '@/utils/localstorage';
export default {
	name: 'CoosVerticalMenu',
	components: { CoosMenuItem },
	props: {
		items: { type: Array, required: true },
		defaultActive: { type: String, default: '' },
		auth: { type: <PERSON><PERSON><PERSON>, required: false }
	},
	data() {
		return {};
	},
	computed: {
		hasAdmin() {
			return function (item) {
				if (!item?.rule) {
					return true;
				} else {
					return item?.rule.includes(this.auth);
				}
			};
		}
	},
	created() {
		this.currentMenu = getItem('currentId') || '';
	},
	methods: {
		handleSelect(index, indexPath) {
			this.$emit('select', index, indexPath);
		}
	}
};
</script>

<style scoped lang="scss">
::v-deep .is-active {
	background: #f5f7fa;
	border-radius: 6px;
}
.el-menu-vertical-box {
	margin-top: 6px;
}
::v-deep .el-submenu__title:hover {
	border-radius: 6px;
	background-color: var(--brand-1); /* 修改鼠标移入菜单的激活背景颜色 */
}
.menu-box {
	background-color: #fff;
	height: 100%;
	width: 200px;
	min-width: 200px;
	padding: 0px 10px;
	border-right: 1px solid #e3ebf2;
	.el-menu {
		width: 100%;
		border: none !important;
	}
}
::v-deep .el-submenu__title {
	height: 40px;
	line-height: 40px;
	margin-top: 10px;
	i {
		right: 6px !important;
	}
}
::v-deep .el-menu-item {
	height: 40px;
	line-height: 40px;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}
::v-deep .el-submenu {
	background-color: transparent !important;
}
::v-deep .el-submenu .el-menu-item {
	padding-right: 20px;
	margin-top: 10px;
	min-width: 160px;
}
::v-deep .el-menu-item:hover {
	border-radius: 6px;
	background-color: #f5f7fa !important; /* 修改鼠标移入菜单的激活背景颜色 */
}
</style>
