<template>
	<div class="menu-item">
		<el-submenu v-if="hasChildren" :index="item.index">
			<template slot="title">
				<!-- <i :class="item.icon"></i> -->
				<span class="span-title">{{ item.title }}</span>
			</template>
			<template v-for="child in item.children">
				<coos-menu-item
					v-if="child.appRole"
					:key="child.index"
					v-appRole="item.appRole"
					:item="child"
				/>
				<coos-menu-item v-else :key="child.index" :item="child" />
			</template>
			<!-- <coos-menu-item
				v-for="child in item.children"
				:key="child.index"
				v-appRole="child.appRole"
				:item="child"
			/> -->
		</el-submenu>
		<!-- <el-menu-item v-else :index="item.index"> -->
		<el-menu-item v-else-if="hasAdmin(item)" :index="item.index">
			<el-tooltip
				:open-delay="700"
				class="item"
				effect="dark"
				:content="item.title"
				placement="top"
			>
				<span class="span-title">{{ item.title }}</span>
			</el-tooltip>
		</el-menu-item>
		<!-- <el-menu-item v-else :index="item.index">
			<span slot="title">{{ item.title }}</span>
		</el-menu-item> -->
	</div>
</template>
<script>
export default {
	name: 'CoosMenuItem',
	props: {
		item: { type: Object, required: true }
	},
	computed: {
		hasAdmin() {
			return function (item) {
				if (!item?.rule) {
					return true;
				}
			};
		},
		hasChildren() {
			return this.item.children && this.item.children.length > 0;
		}
	}
};
</script>
<style scoped lang="scss">
.menu-item {
	.span-title {
		color: #000000;
	}
}

::v-deep .el-icon-arrow-down {
	right: 0;
	top: 55%;
}
</style>
