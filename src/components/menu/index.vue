<template>
	<div class="main">
		<CoosMenu
			:items="menuItems"
			:customer-menu-style="customerMenuStyle"
			:default-active="activeIndex"
			@select="handleSelect"
		/>
		<div class="main-box">
			<CoosVerticalMenu
				v-if="customerMenuStyle !== 'top'"
				:items="menuItems"
				:default-active="activeIndex"
				@select="handleSelect"
			/>
			<section class="app-main">
				<router-view />
			</section>
		</div>
	</div>
</template>
<script>
import CoosMenu from '@/components/menu/coos-menu.vue';
import CoosVerticalMenu from '@/components/menu/coos-vertical-menu.vue';

export default {
	name: 'CoosMenuIndex',
	components: { CoosVerticalMenu, CoosMenu },
	props: {
		// 应用id
		appCode: {
			type: String,
			default: ''
		},
		// 排版模式
		customerMenuStyle: {
			type: String,
			default: 'top'
		},
		// tabs数组
		menuItems: {
			type: Array,
			default: () => []
		},
		//当前激活菜单的 index
		activeIndex: {
			type: String,
			default: ''
		}
	},
	data() {
		return {};
	},
	methods: {
		handleSelect(index, indexPath) {
			this.$emit('select', index, indexPath);
		}
	}
};
</script>
<style scoped lang="scss">
.main {
	height: 100%;
	.main-box {
		height: calc(100% - 62px);
		display: flex;
		flex: 1;
	}
	.app-main {
		//height: calc(100% - 62px);
		width: 100%;
	}
}
::v-deep .el-menu-item {
	color: #000;
}
::v-deep .is-active {
	color: #0f45ea !important;
}
</style>
