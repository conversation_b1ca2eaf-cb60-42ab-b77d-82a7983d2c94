<template>
	<div class="menu-box">
		<div
			v-for="(item, index) of workApp"
			v-show="item.id == currentMenu"
			:key="index"
			class="menu-box-title"
		>
			<FileById :size="28" :more-style="{ marginRight: 0 }" :value="item.icon"></FileById>
			<div class="menu-box-title-line"></div>
			<div class="menu-box-title-text">{{ item.title }}</div>
		</div>
		<el-menu
			v-if="customerMenuStyle === 'top'"
			:default-active="defaultActive"
			class="el-menu-vertical-demo"
			mode="horizontal"
			@select="handleSelect"
		>
			<template v-for="item in items">
				<CoosMenuItem v-if="item.appRole" :key="item.index" v-appRole="item.appRole" :item="item" />
				<CoosMenuItem v-else :key="item.index" :item="item" />
			</template>
		</el-menu>
	</div>
</template>
<script>
import CoosMenuItem from './coos-menu-item.vue';
import { mapGetters } from 'vuex';
import { getItem } from '@/utils/localstorage';
export default {
	components: { CoosMenuItem },
	props: {
		items: { type: Array, required: true },
		defaultActive: { type: String, default: '' },
		customerMenuStyle: { type: String, required: true }
	},
	data() {
		return {
			currentMenu: ''
		};
	},
	computed: {
		...mapGetters(['rentInfo', 'userInfo', 'workApp', 'openIm']),
		hasAdmin() {
			return function (item) {
				if (!item?.rule) {
					return true;
				}
			};
		}
	},
	created() {
		this.currentMenu = getItem('currentId') || '';
	},
	methods: {
		handleSelect(index, indexPath) {
			this.$emit('select', index, indexPath);
		}
	}
};
</script>

<style scoped lang="scss">
.el-menu-vertical-demo {
	@include flexBox(flex-start);
}
::v-deep .is-active {
	border-bottom: 2px solid var(--brand-6);
}
.menu-box {
	background-color: #ffffff; /* 修改鼠标移入菜单的激活背景颜色 */
	display: flex;
	align-items: center;
	min-height: 57px;
	border-bottom: 1px solid #e3ebf2;
	&-title {
		display: flex;
		padding: 0 54px 0 20px;
		min-width: 200px;
		align-items: center;
		&-line {
			margin: 0 12px;
			width: 1px;
			height: 28px;
			border: 1px solid #e3ebf2;
		}
		&-text {
			font-family: PingFang SC, PingFang SC;
			font-weight: 600;
			font-size: 16px;
			color: #15224c;
			line-height: 22px;
		}
	}
}
::v-deep .el-menu {
	border: none !important;
}
</style>
