<template>
	<el-dialog
		:visible="dialogVisible"
		top="0"
		title="下载管理"
		width="80%"
		class="desk-el-custom-dialog"
		:modal-append-to-body="false"
		@close="close"
	>
		<div class="content">
			<div v-for="(item, index) of list" :key="index" class="content-item">
				<svg-icon v-if="item.fileType" class="fileTypes" :icon-class="item.fileType"></svg-icon>
				<div class="content-item-name">{{ item.name }}</div>
				<el-progress
					class="download"
					:stroke-width="6"
					:percentage="processMap[item.id]"
					:status="statusMap[item.id]"
				></el-progress>
				<div class="content-item-size">{{ tabSize(item.orgSize) }}</div>
				<div
					v-if="statusMap[item.id] === 'exception'"
					class="content-item-btn"
					@click="reload(item)"
				>
					重新下载
				</div>
				<div class="content-item-btn del" @click="delAndStop(item.id)">
					{{ statusMap[item.id] === 'exception' || processMap[item.id] === 100 ? '删除' : '取消' }}
				</div>
			</div>
			<el-empty
				v-if="list.length === 0"
				style="background: #ffffff; height: 400px"
				description="暂无下载内容"
				:image="assetsUrl + '/common/img/no-search.png'"
			></el-empty>
		</div>
	</el-dialog>
</template>

<script>
import { downloadFile } from '@/utils/down-load';
import { tabSize } from '@/utils';
import { assetsUrl } from '@/config';
import request from '@/utils/request';
import { CoosEventTypes } from '@/utils/bus';

export default {
	name: 'Index',
	data() {
		return {
			assetsUrl,
			dialogVisible: false,
			list: [],
			processMap: {},
			statusMap: {}
		};
	},
	mounted() {
		this._BUS.$on(CoosEventTypes.addLoadFile, this.addLoadFile);
	},
	destroyed() {
		this._BUS.$off(CoosEventTypes.addLoadFile, this.addLoadFile);
	},
	methods: {
		tabSize,
		getLoaded() {
			let isDownLoad = true;
			this.list.forEach(item => {
				if (this.processMap[item.id] < 100 && this.statusMap[item.id] === 'success') {
					isDownLoad = false;
				}
			});
			this.$emit('loaded', isDownLoad);
		},
		reload(item) {
			this.addLoadFile(item);
		},
		delAndStop(id) {
			// 删除
			if (this.statusMap[id] === 'exception' || this.processMap[id] === 100) {
				let i = this.list.findIndex(item => {
					return item.id === id;
				});
				this.list.splice(i, 1);
			}
			// 暂停
			else {
				request.customCancel(id); // 真实中断下载
				this.$set(this.statusMap, id, 'exception');
				this.getLoaded();
			}
		},
		addLoadFile(file) {
			let { name, url, orgSize, id } = file;
			let i = this.list.findIndex(item => {
				return item.id === id;
			});
			if (i > -1) {
				if (this.statusMap[id] === 'success' && this.processMap[id] < 100) {
					this.$message.error('正在下载，请勿重复操作');
					return;
				} else {
					this.list.splice(i, 1);
				}
			}
			this.list.push(file);
			this.$set(this.processMap, id, 0);
			this.$set(this.statusMap, id, 'success');
			this.getLoaded();
			downloadFile({
				url,
				name,
				cancelKey: id,
				onDownloadProgress: progressEvent => {
					const progress = Math.min(Math.round((progressEvent.loaded / orgSize) * 100), 100);
					this.$set(this.processMap, id, progress);
					// 在这里处理下载进度，如更新界面上的进度条
				},
				success: () => {
					this.$set(this.processMap, id, 100);
					this.$message.success(`${name}下载完成！`);
					this.getLoaded();
					// let i = this.list.findIndex(item => {
					// 	return item.id === id;
					// });
					// this.list.splice(i, 1);
				},
				fail: err => {
					console.log('下载失败------------------', err);
					this.$set(this.statusMap, id, 'exception');
					this.$message.error('下载失败！');
					this.getLoaded();
				}
			});
		},
		open() {
			this.dialogVisible = true;
		},
		close() {
			this.dialogVisible = false;
		}
	}
};
</script>

<style scoped lang="scss">
.desk-el-custom-dialog {
	::v-deep .el-dialog {
		min-height: 80%;
	}
}
.content {
	height: 100%;
	&-item {
		display: flex;
		align-items: center;
		padding: 12px 0;
		border-bottom: 1px solid #f2f2f2;
		.fileTypes {
			width: 20px;
			height: 20px;
			border-radius: 6px;
			margin-right: 8px;
		}
		&-name {
			width: 200px;
			color: $textColor;
			margin-right: 12px;
			@include aLineEllipse;
		}
		&-size {
			color: $subTextColor;
			margin-right: 12px;
		}
		&-btn {
			color: var(--brand-6);
			margin-right: 12px;
			cursor: pointer;
		}
		.del {
			color: #ff7a7b;
		}
		.download {
			flex: 1;
			margin-right: 12px;
		}
	}
}
</style>
