<template>
	<el-dialog
		:title="type === 'details' ? '详情' : id ? '编辑文件' : '上传文件'"
		:visible.sync="visible"
		class="desk-el-custom-dialog"
		top="0"
		:before-close="cancel"
		width="80%"
	>
		<!--    :before-close="beforeClose"-->
		<div v-loading="loading" class="body">
			<el-form
				ref="ruleForm"
				:model="ruleForm"
				:disabled="type === 'details'"
				:rules="rules"
				label-width="100px"
				class="demo-ruleForm"
			>
				<el-form-item label="文件标题" prop="title">
					<el-input v-model="ruleForm.title"></el-input>
				</el-form-item>
				<el-form-item label="文号">
					<el-input v-model="ruleForm.documentNumber"></el-input>
				</el-form-item>
				<el-form-item label="查看范围" prop="type">
					<el-checkbox-group v-model="ruleForm.viewAuthority">
						<el-checkbox label="title" disabled name="">标题</el-checkbox>
						<el-checkbox label="text" :disabled="chooseIndex == 2" name="">正文</el-checkbox>
						<el-checkbox label="file" :disabled="chooseIndex == 2" name="">附件</el-checkbox>
					</el-checkbox-group>
				</el-form-item>
				<el-form-item label="描述" prop="content">
					<el-input v-model="ruleForm.content" type="textarea"></el-input>
				</el-form-item>
				<el-form-item label="附件" prop="ids">
					<uploadFile
						:key="uploadKey"
						ref="uploadFile"
						v-model="ruleForm.ids"
						:show-loading="true"
						:can-download="false"
						:multiple="multiple"
						mode="file"
						:disabled="type === 'details'"
						:limit="1"
						:custom-button="true"
						@success="success"
						v-on="$listeners"
					>
						<template #custom-button>
							<div class="knowledge-title-right">
								<i class="coos-iconfont icon-jiahao add-icon"></i>
								<div>上传附件</div>
							</div>
						</template>
					</uploadFile>
				</el-form-item>
			</el-form>
		</div>
		<div class="footer">
			<div class="footer-cancel" @click="cancel">取消</div>
			<div v-if="!type" v-loading="saveLoading">
				<div class="footer-save" @click="beforeSave">确认</div>
			</div>
		</div>
	</el-dialog>
</template>

<script>
import { setFileInfo, uploadFiles } from '@/api/modules/document-content';

export default {
	name: 'UploadDialog',
	components: {},
	props: {},
	data() {
		return {
			chooseIndex: 0,
			loading: false,
			visible: false,
			saveLoading: false,
			multiple: true,
			filesObj: {
				name: ''
			},
			ruleForm: {
				title: '',
				documentNumber: '',
				viewAuthority: ['title'],
				content: '',
				ids: ''
			},
			id: '',
			type: '',
			uploadKey: 1,
			rules: {
				title: [{ required: true, message: '请输入文件名称', trigger: 'blur' }],
				ids: [{ required: true, message: '请上传附件', trigger: 'change' }]
			}
		};
	},
	watch: {},
	mounted() {},
	methods: {
		open(row, type) {
			this.uploadKey++;
			this.type = type;
			this.parentId = row.parentId || '';
			this.spaceId = row.spaceId || '';
			this.chooseIndex = row.chooseIndex || '';

			// 集中状态管理
			this.loading = true;

			if (row.infoId) {
				this.id = row.infoId;
				// 安全地处理 viewAuthority 的分割
				const viewAuthority = Array.isArray(row.viewAuthority)
					? row.viewAuthority
					: typeof row.viewAuthority === 'string' && row.viewAuthority.trim() !== ''
					? row.viewAuthority.split(',')
					: [];

				this.ruleForm = {
					title: row.title || row.name || '',
					documentNumber: row.documentNumber || '',
					viewAuthority: viewAuthority,
					content: row.content || '',
					ids: row.fileUrl || ''
				};

				this.filesObj = {
					name: row.name || '',
					fileUrl: row.fileUrl || '',
					fileExt: row.fileExt || '',
					fileSize: row.fileSize || '',
					fileType: row.fileType || '',
					parentId: row.parentId || '',
					spaceId: row.spaceId || ''
				};
				// 处理 chooseIndex 的特殊情况
				if (this.chooseIndex == 2) {
					this.ruleForm.viewAuthority = ['title', 'text', 'file'];
				}
			} else {
				this.id = '';
				this.filesObj = {
					name: ''
				};
				this.ruleForm = {
					title: '',
					documentNumber: '',
					viewAuthority: this.chooseIndex == 2 ? ['title', 'text', 'file'] : ['title'],
					content: '',
					ids: ''
				};
			}
			this.visible = true;
			this.loading = false;
		},
		// 上传文件操作
		success(val) {
			if (val.length == 0) {
				this.filesObj = {
					name: ''
				};
				return;
			}
			this.$refs.ruleForm.validateField('ids', error => {});
			// this.uploadLoading = true;
			const { fileExt, fileSize, fileType, fileId, originalFileName } = val[val.length - 1];
			let newObject = { fileExt, fileSize, fileType, fileId, originalFileName };
			const { originalFileName: name, fileId: fileUrl, ...rest } = newObject;
			const newFileObject = { name, fileUrl, ...rest };
			this.filesObj = {
				...newFileObject,
				parentId: this.parentId,
				spaceId: this.spaceId,
				spaceType: true
			};
		},
		/**取消*/
		cancel() {
			this.visible = false;
		},
		/**重置*/
		reset() {},
		/**保存前判断是否是修改操作*/
		beforeSave() {
			this.$refs.ruleForm.validate(valid => {
				if (valid) {
					this.saveLoading = true;
					let params = {
						...this.filesObj,
						fileInfo: {
							...this.ruleForm,
							viewAuthority: this.ruleForm.viewAuthority.join(',')
						}
					};
					if (this.chooseIndex == 2) {
						// 我的空间
						params.spaceType = false;
						delete params.spaceId;
					}
					if (this.id) {
						setFileInfo(this.id, params).then(res => {
							if (res.code == 200) {
								this.saveLoading = false;
								this.$emit('getList');
								this.cancel();
								this.$message.success('修改成功');
							} else {
								this.saveLoading = false;
								this.$message.error(res.message);
							}
						});
						return;
					}
					// 保存上传
					uploadFiles({
						...params
					}).then(res => {
						this.ids = '';
						if (res.code == 200) {
							this.saveLoading = false;
							this.$emit('getList');
							this.cancel();
							this.$message.success('新增成功');
						} else {
							this.saveLoading = false;
							this.$message.error(res.message);
						}
						// this.uploadLoading = false;
					});
				}
			});
		},
		/**保存数据*/
		save() {}
	}
};
</script>

<style scoped lang="scss">
.desk-el-custom-dialog {
	::v-deep .el-dialog {
		width: 960px !important;
	}
}
.body {
	.add-icon {
		margin-right: 4px;
		font-size: 16px;
	}
	.knowledge-title-right {
		width: 108px;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 6px;
		padding: 0px 10px;
		background: var(--brand-6);
		color: #fff;
		font-size: 14px;
		line-height: 32px;
		height: 32px;
		text-align: center;
		font-weight: 400;
		cursor: pointer;
	}
}

.footer {
	width: 100%;
	height: 52px;
	padding-right: 6px;
	@include flexBox(flex-end);

	&-cancel {
		width: 68px;
		height: 36px;
		background: #ffffff;
		border-radius: 6px;
		border: 1px solid $borderColor;
		margin-right: 10px;
		cursor: pointer;
		font-size: 14px;
		font-weight: 400;
		color: $primaryTextColor;
		line-height: 36px;
		text-align: center;
	}

	&-save {
		width: 96px;
		height: 36px;
		background: var(--brand-6);
		border-radius: 6px;
		font-size: 14px;
		font-weight: 400;
		color: rgba(255, 255, 255, 0.9);
		line-height: 36px;
		text-align: center;
		cursor: pointer;
	}
}

::v-deep .el-col-1 {
	width: 24px;
	margin-right: 14px;
}
::v-deep .el-checkbox {
	font-family: PingFang SC, PingFang SC;
	font-weight: 400;
	font-size: 14px;
	color: #2f446b;
	line-height: 40px !important;
}
::v-deep .el-form-item__label {
	font-family: PingFang SC, PingFang SC;
	font-weight: 400;
	font-size: 14px;
	color: #15224c;
}
::v-deep .custom-upload-file-pre {
	padding: 0 6px !important;
	&-text {
		max-width: 780px !important;
	}
}
</style>
