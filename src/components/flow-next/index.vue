<template>
	<el-dialog
		width="500px"
		top="0"
		class="desk-el-custom-dialog"
		title="请选择下一步"
		:close-on-click-modal="false"
		:wrapper-closable="false"
		:append-to-body="true"
		:visible.sync="show"
	>
		<el-form ref="forms" :model="forms" label-width="110px" :rules="rules">
			<el-form-item
				v-if="nextType === 'deptCandidateTask'"
				label="办理部门"
				prop="deptCandidate"
				class="choose-item"
			>
				<div class="flex-box">
					<el-button plain icon="el-icon-plus" @click="showDeptSelect()">
						选择{{ nodeName }}
					</el-button>
					<div class="choose-tags">
						<el-tag
							v-for="(item, index) in deptSelect"
							:key="index"
							size="small"
							closable
							@close="deptHandleClose(index)"
						>
							{{ item.title }}
						</el-tag>
					</div>
				</div>
			</el-form-item>
			<el-form-item v-else-if="nextType === 'userTask'" label="选择成员" prop="userLabel">
				<el-select
					v-model="forms.userLabel"
					plcaeholder="请选择成员"
					:clearable="true"
					@change="changeSelect"
				>
					<el-option
						v-for="(item, index) in nextHandler"
						:key="index"
						:label="item.label"
						:value="item.value"
					></el-option>
				</el-select>
			</el-form-item>
			<el-form-item
				v-else-if="nextType === 'selectNextUser'"
				label="办理人员"
				prop="assignee"
				class="choose-item"
			>
				<div class="flex-box">
					<el-button plain icon="el-icon-plus" @click="showUserSelect()">
						选择{{ nodeName }}
					</el-button>
					<div class="choose-tags">
						<el-tag
							v-for="(item, index) in nextUsers"
							:key="index"
							size="small"
							closable
							@close="nextUserDel(index)"
						>
							{{ item.title }}
						</el-tag>
					</div>
				</div>
			</el-form-item>
			<el-form-item v-else label="下一步处理人">
				<div v-for="(items, i) in nextHandler" :key="i" class="next-item">
					<div class="node-name">节点 : {{ items.nodeName }}</div>
					<template v-if="items.multiple">
						<el-checkbox-group :key="keys" v-model="vars[items.varName]" @change="assigneeChange">
							<el-checkbox
								v-for="(item, index) in items.selectValue"
								:key="index"
								:label="item.value"
							>
								{{ item.label }}
							</el-checkbox>
						</el-checkbox-group>
					</template>
					<template v-else>
						<el-radio-group :key="keys" v-model="vars[items.varName]" @change="assigneeChange">
							<el-radio v-for="(item, index) in items.selectValue" :key="index" :label="item.value">
								{{ item.label }}
							</el-radio>
						</el-radio-group>
					</template>
				</div>
			</el-form-item>
		</el-form>
		<div slot="footer" class="dialog-footer" style="text-align: right">
			<el-button type="primary" :loading="loading" @click="submit">确 定</el-button>
		</div>
		<orgPersonnelDialog
			title="选择人员"
			:visible="showNextUsers"
			:init-params="initParams"
			:init-values="nextUsers"
			:data-source="userDetaSource"
			:need-all-data="true"
			:is-radio="true"
			:disable-all="true"
			:can-select-depart="false"
			@sure="nextUserSure"
			@close="nextUserClose"
		></orgPersonnelDialog>
		<orgPersonnelDialog
			title="选择部门"
			:visible="chooseDeptVisiable"
			:init-params="initParams"
			:init-values="deptSelect"
			:data-source="detaSource"
			:need-all-data="true"
			:is-radio="true"
			:disable-all="true"
			:include-users="false"
			:can-select-depart="canSelectDepart"
			@sure="deptSure"
			@close="deptClose"
		></orgPersonnelDialog>
	</el-dialog>
</template>
<script>
import orgPersonnelDialog from '@/components/org-personnel-dialog';
import { mapGetters } from 'vuex';
import { getFlowNext } from '@/api/modules/common';
import { get_token } from '@/utils/auth';
export default {
	name: 'FlowNext',
	components: { orgPersonnelDialog },
	props: {},
	data() {
		return {
			keys: 0,
			show: false,
			rules: {
				assignee: [{ required: true, message: '请选择下一步处理人', trigger: 'change' }],
				deptCandidate: [{ required: true, message: '请选择办理部门', trigger: 'blur' }],
				userLabel: [{ required: true, message: '请选择办理人', trigger: 'change' }]
				// selectNextUser: [{ required: true, message: '请选择办理人', trigger: 'blur' }]
			},
			initParams: {
				authorityType: 2, //类型;1.可用成员范围,2.禁止使用范围
				applicationId: ''
			},
			userDetaSource: ['depart'],
			detaSource: ['depart'],
			forms: {
				assignee: '',
				deptCandidate: '',
				userLabel: '' // userTask时下拉选择值
			},
			vars: {}, // 节点选值映射
			nextHandler: [],
			nextType: '',
			deptSelect: [],
			showNextUsers: false,
			varName: '',
			nextUsers: [],
			canSelectDepart: true,
			chooseDeptVisiable: false,
			loading: false,
			nodeName: ''
		};
	},
	computed: {
		...mapGetters(['userInfo'])
	},
	methods: {
		/**下拉选择*/
		changeSelect(value) {
			this.vars[this.varName] = value;
		},
		/**检查是否打开下一步*/
		async check(id, params) {
			let data = {
				createUser: this.userInfo.id,
				id: id,
				tenantId: get_token('X-Coos-Client-Tenant-Id'),
				vars: params
			};
			let res = await getFlowNext(data);
			if (res.code === 200) {
				// 如果下一级节点为两个或者一个节点但是有多个办理人，打开弹窗显示
				if (
					res.result.length > 1 ||
					(res.result.length === 1 && Object.keys(res.result[0].selectValue).length > 1)
				) {
					this.show = true;
					this.init(res);
					return false;
				} else {
					return true;
				}
			} else {
				this.$message.error(res.message);
			}
		},
		/**初始化节点选择*/
		init(res) {
			this.$nextTick(() => {
				this.$refs.forms.resetFields();
			});
			// 如果是多个的.比如是平行任务办理人..那就需要循环去选人/部门...-_-
			if (res.result.length > 1) {
				res.result.forEach(item => {
					if (item.varName) {
						this.vars[item.varName] = null;
					}
				});
				let nextHandler = res.result;
				this.nextHandler = nextHandler.map(v => {
					let selectValue = [];
					Object.keys(v.selectValue).map(vs => {
						selectValue.push({
							label: v.selectValue[vs],
							value: vs
						});
					});
					let item = { ...v };
					item.selectValue = selectValue;
					return item;
				});
			} else {
				this.nextType = res.result[0].type;
				this.nodeName = res.result[0].nodeName;
				const selectValue = { ...res.result[0].selectValue };
				const userList = Object.keys(selectValue).map(v => {
					return {
						label: selectValue[v],
						value: v
					};
				});
				if (res.result[0].varName) {
					this.vars[res.result[0].varName] = null;
					this.varName = res.result[0].varName;
				}
				if (this.nextType === 'selectNextUser') {
					// this.userLabelList = [...userList];
				} else if (this.nextType === 'userTask') {
					this.nextHandler = [...userList];
				} else if (this.nextType === 'deptCandidateTask') {
					// this.nextHandler = [...userList];
				}
			}
		},
		/**提交*/
		submit() {
			this.$refs.forms.validate(valid => {
				if (valid) {
					let isEmptyForm = false;
					if (Object.keys(this.vars).length > 1) {
						Object.keys(this.vars).forEach(key => {
							if (!this.vars[key]) {
								isEmptyForm = true;
							}
						});
					}
					if (!isEmptyForm) {
						this.$emit('nextSelected', this.vars);
						this.show = false;
					} else {
						this.$message.error('请选择下一步处理人');
					}
				}
			});
		},
		/**打开选人组件*/
		showUserSelect() {
			this.showNextUsers = true;
		},
		/**多节点选择*/
		assigneeChange() {
			this.keys += 1;
		},
		/**确定选人组件*/
		nextUserSure(e) {
			this.nextUsers = [...e];
			if (e.length) {
				this.forms.assignee = e[0].id;
				this.vars[this.varName] = e[0].id;
				this.$refs.forms.clearValidate();
			} else {
				this.forms.assignee = '';
				this.vars[this.varName] = '';
			}
			this.showNextUsers = false;
		},
		/**关闭选人组件*/
		nextUserClose() {
			this.showNextUsers = false;
		},
		/**删除选择人*/
		nextUserDel() {
			this.forms.assignee = '';
			this.forms.vars['selectNextUser'] = '';
			this.nextUsers = [];
		},
		/**打开选择部门组件*/
		showDeptSelect() {
			this.chooseDeptVisiable = true;
		},
		/**确认选择部门组件*/
		deptSure(e) {
			this.deptSelect = [...e];
			if (e.length) {
				this.forms.deptCandidate = e[0].id;
				this.vars[this.varName] = e[0].id;
				this.$refs.forms.clearValidate();
			} else {
				this.forms.deptCandidate = null;
				this.vars[this.varName] = e[0].id;
			}
			this.chooseDeptVisiable = false;
		},
		/**关闭选择部门组件*/
		deptClose() {
			this.chooseDeptVisiable = false;
		},
		/**删除部门选择*/
		deptHandleClose(index) {
			this.deptSelect.splice(index, 1);
			let dataValue = [];
			this.deptSelect.map(v => {
				dataValue.push(v.id);
			});
			this.forms.deptCandidate = null;
		}
	}
};
</script>
<style lang="scss" scoped>
.choose-item {
	position: relative;
}
</style>
