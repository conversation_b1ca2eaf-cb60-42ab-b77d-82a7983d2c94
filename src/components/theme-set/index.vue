<!--
 * @Description: 主题设置抽屉
 * @Version: 1.0
 * @Autor: zhaodongming
 * @Date: 2022-08-17 11:45:03
 * @LastEditors: zhaodongming
 * @LastEditTime: 2023-07-06 16:56:57
-->
<template>
	<div>
		<el-drawer
			:visible.sync="drawerVal"
			:direction="direction"
			:append-to-body="true"
			:wrapper-closable="true"
			:before-close="handleClose"
			:with-header="false"
		>
			<div>
				<h3 class="setting-title">主题色</h3>
				<color-car></color-car>
				<h3 class="setting-title">风格设置</h3>
				<theme-change></theme-change>
			</div>
		</el-drawer>
	</div>
</template>

<script>
import ColorCar from '@/components/color-car';
import ThemeChange from '@/components/theme-change';

export default {
	name: 'ThemeSet',
	components: { ColorCar, ThemeChange },
	props: {
		drawer: {
			type: Boolean,
			default: false
		}
	},
	data() {
		return {
			// 是否打开设置面板
			drawerVal: false,
			// 设置面板打开方向
			direction: 'rtl'
		};
	},
	watch: {
		drawer(newVal, oldVal) {
			this.drawerVal = newVal;
		}
	},
	methods: {
		// 关闭抽屉
		handleClose(done) {
			this.drawerVal = false;
			this.$emit('change', this.drawerVal);
		}
	}
};
</script>

<style lang="scss" scoped>
::v-deep .el-drawer__body {
	padding: 24px;
}
.setting-title {
	margin: 24px 0 24px 0;
}
</style>
