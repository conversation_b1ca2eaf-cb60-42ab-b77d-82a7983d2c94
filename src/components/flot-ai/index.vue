<!--
  全局快捷入口打开coos智能对话，可以选择模式再打开，目前未开发完，保留。只是调试
  params.hideCoos 业务上强制控制隐藏 均在业务上使用 当业务不需要，即便配置项有数据，也要隐藏
  params.showCoos 业务上强制控制显示 均在业务上使用 当配置项目为空，也要展示的对话
-->
<template>
	<div
		v-if="
			(aiEnterList1.length > 0 || aiEnterList2.length > 0 || params.showCoos) && !params.hideCoos
		"
	>
		<dragg
			v-show="showAiIcon"
			:is-dragg="canDrag"
			@changeDragState="changeDragState"
			@dragging="dragging"
		>
			<template #draggable>
				<div
					v-show="!position.includes('right') || showBig"
					v-loading="loading"
					:class="
						iconStatus === 'show' ? 'show-animation' : iconStatus === 'hide' ? 'hide-animation' : ''
					"
					class="content"
					@mouseenter="enterBigIcon"
					@mouseleave="leaveBigIcon"
				>
					<div
						class="content-list"
						:class="firstLoad ? '' : 'animation-transform'"
						:style="
							imDraw
								? `height:${listHeight}px;`
								: 'height:0;padding: 0 12px;border: 0px solid #e2edf9;'
						"
					>
						<div class="ai-list" @mousedown="canDrag = false">
							<div
								v-for="(item, index) of aiEnterList1"
								:key="'always-' + index"
								class="item-content"
								@click="toRouter(item)"
							>
								<img class="ai-icon" :src="item.logoUrl" alt="" />
								<div class="custom-tip">{{ item.name }}</div>
							</div>
							<div v-show="aiEnterList2.length > 0" class="line"></div>
							<div
								v-for="(item, index) of aiEnterList2"
								:key="index"
								class="item-content"
								@click="toRouter(item)"
							>
								<img class="ai-icon" :src="item.logoUrl" alt="" />
								<div class="custom-tip">{{ item.name }}</div>
							</div>
						</div>
						<img
							class="close"
							src="../../assets/images/common/close.png"
							alt=""
							@click="imDraw = false"
						/>
					</div>
					<img
						class="aiFont"
						:src="require(`@/assets/${rentThem}/coos/coos-avatar.png`)"
						alt=""
						@mousedown="iconMousedown"
						@mouseup="handleAssistant"
						@dblclick="handleDblClickBot"
					/>
				</div>
				<img
					v-show="position.includes('right') && !showBig"
					class="ai-small"
					:class="aiSmallClass"
					:src="require(`@/assets/${rentThem}/ai/ai-small.png`)"
					alt=""
					@mouseenter="enterIcon"
					@mouseleave="leaveIcon"
				/>
				<div
					v-show="position.includes('right')"
					:style="showBig ? 'width:300px' : 'width:0px'"
					class="mask"
					@mouseleave="leaveMask"
				></div>
			</template>
		</dragg>
		<dragg
			v-if="imWindow"
			:is-dragg="draggTitle"
			:im-draw="imWindow"
			top-distance="20px"
			left-distance="50%"
			dragg-id="otherDragg"
			class="draggContent"
			@contextmenu.stop.native.prevent
			@changeDragState="changeCoosDragStatus"
		>
			<template #draggable>
				<transition name="fade">
					<imContentCoos
						v-show="imWindow"
						ref="imContentCoos"
						key="wait-list"
						:welcome-text="welcomeText"
						:is-component="true"
						:component-type="params.componentType || ''"
						:coos-type="params.coosType"
						:robot-data-content="params.robotDataContent"
						:metadata-ids="params.metadataIds"
						:chat-object-id="params.chatObjectId"
						:mode-type="params.modeType"
						:other-params="params.otherParams"
						:list-params="params.listParams"
						:list-ids="params.ids"
						class="coosContent"
						@titleDragge="titleDragge"
						@closeCoos="closeCoos"
					></imContentCoos>
				</transition>
			</template>
		</dragg>
		<div v-show="draging" class="page-mask"></div>
	</div>
</template>

<script>
import dragg from '@/components/dragge';
import imContentCoos from '@/views/coos/index.vue';
import { mapGetters } from 'vuex';
import { CoosEventTypes } from '@/utils/bus';
import { getWelcomeText } from '@/api/modules/coos';
import { isEmpty } from '@/utils';
import { getDefaultMenuList } from '@/router';
import { getDictionary } from '@/utils/data-dictionary';
import { preUrl } from '@/config';
export default {
	name: 'Index',
	components: {
		dragg,
		imContentCoos
	},
	data() {
		return {
			showTempList: [], // 临时显示的定制Ai入口
			writeRoute: ['/coos-ai', '/coosAi'],
			changeKey: false, // 是否重新赋值
			tempDbClick: false, // 临时双击权限
			params: {}, // 待办详情对话的参数
			loading: false, // 加载效果
			welcomeText: '',
			T: null, // 时间计时器
			showAiIcon: true,
			inBigIcon: false, // 是否在大图标中
			inSmallIcon: false, // 在小图标中
			dragStatus: false, // 拖拽中的状态
			realPosition: [], // 真实的位置
			firstShow: true, // 第一次隐藏小图标
			addEvent: false, // 是否增加事件
			iconStatus: 'normal', // 大图标显示状态
			showBig: false, // 是否显示大图标
			position: [], // 图标靠边的位置
			draging: false, // 拖拽中
			clickState: false, // 是点击还是拖拽
			imDraw: false, // 智能列表的拖拽窗口
			imWindow: false, // 智能对话拖拽窗口
			firstLoad: true, // 第一次加载
			draggTitle: false, // 是否拖拽到标题了
			canDrag: true, // 可以拖拽
			dbClick: false // 双击状态
		};
	},
	computed: {
		...mapGetters(['userInfo', 'aiEnterList']),
		// 前端控制临时需要显示的智能对话入口
		tempList() {
			let arr = this.showTempList;
			// 列表中没有  但是路由配置了  加入列表
			if (
				this.$route.query.aiType === getDictionary('AI编码/智问待办') &&
				!arr.includes(getDictionary('AI编码/智问待办'))
			) {
				arr.push(getDictionary('AI编码/智问待办'));
			}
			// 列表中没有  但是路由配置了  加入列表
			else if (
				this.$route.query.aiType === getDictionary('AI编码/智答私库') &&
				!arr.includes(getDictionary('AI编码/智答私库'))
			) {
				arr.push(getDictionary('AI编码/智答私库'));
			}
			return arr;
		},
		pagePath() {
			return this.$route.path;
		},
		// ai小图标的样式
		aiSmallClass() {
			let className = '';
			if (this.position.includes('right')) {
				className = 'ai-small-right';
			}
			return className;
		},
		listHeight() {
			return (this.aiEnterList1.length + this.aiEnterList2.length) * 48 + 50;
		},
		// 总是渲染的智能对话入口
		aiEnterList1() {
			// 后端权限中配置有
			return this.aiEnterList.filter(item => {
				let extend = item.extend ? JSON.parse(item.extend) : {};
				return extend.quickEntrance === 'true';
			});
		},
		// 临时渲染的智能对话入口
		aiEnterList2() {
			// 后端权限中配置有
			return this.aiEnterList.filter(item => {
				// 并且需要展示
				return this.tempList.includes(item.permsCode);
			});
		}
	},
	watch: {
		pagePath() {
			this.closeCoos();
			if (!this.changeKey) {
				this.params = {};
			}
		}
	},
	mounted() {
		this._BUS.$on(CoosEventTypes.changeTempAiEnter, this.changeTempAiEnter);
		this._BUS.$on(CoosEventTypes.aiSessionParams, this.emitAiSessionParams);
	},
	destroyed() {
		this._BUS.$off(CoosEventTypes.changeTempAiEnter, this.changeTempAiEnter);
		this._BUS.$off(CoosEventTypes.aiSessionParams, this.emitAiSessionParams);
	},
	methods: {
		isEmpty,
		/**改变定制的临时Ai入口*/
		changeTempAiEnter(list) {
			this.showTempList = list;
		},
		/**赋值ai对话的参数*/
		emitAiSessionParams(params) {
			this.params = params;
			this.changeKey = true;
			// 防止路由监听赋值覆盖
			setTimeout(() => {
				this.changeKey = false;
			}, 1000);
		},
		/**获取欢迎语*/
		async getWelcomeText() {
			let res = await getWelcomeText(this.params.applicationId, this.params.coosTypeCode);
			if (res.code === 200) {
				this.welcomeText = res.result.prologue;
			} else {
				this.$message.error(res.message);
			}
		},
		/**拖动状态*/
		dragging() {
			this.position = [];
			this.dragStatus = true;
		},
		/**进入小图标*/
		enterIcon() {
			// 记录进入小图标的状态
			this.inSmallIcon = true;
			setTimeout(() => {
				// 如果此时显示的大图标 || 刚切换大小图标 || 鼠标已经移出小图标   则不继续执行
				if (this.showBig || this.firstShow || !this.inSmallIcon) return;
				// 显示大图标
				this.showBig = true;
				// 大图标动画
				this.iconStatus = 'show';
				// 延迟执行离开大图标时候，切回小图标的事件
				setTimeout(() => {
					this.addEvent = true;
				}, 500);
			}, 500);
		},
		/**离开小图标*/
		leaveIcon() {
			// 标记离开小图标的状态
			this.inSmallIcon = false;
		},
		/**关闭大图标*/
		closeBigIcon() {
			if (this.dragStatus) return;
			this.iconStatus = 'hide';
			setTimeout(() => {
				// 隐藏大图标
				this.showBig = false;
				// 重置大图标监听事件状态
				this.addEvent = false;
			}, 500);
		},
		/**离开大图标*/
		leaveBigIcon() {
			// 记录鼠标是否在大图标之上的状态
			this.inBigIcon = false;
			this.leaveMask();
		},
		/**离开虚拟的遮罩层*/
		leaveMask() {
			setTimeout(() => {
				// 避免切换大小图标时候立即执行的矛盾，延迟执行
				if (!this.addEvent || this.inBigIcon) return;
				if (this.position.includes('right') && this.showBig) {
					this.closeBigIcon();
				}
			}, 500);
		},
		/**进入大图标*/
		enterBigIcon() {
			// 记录鼠标是否在大图标之上的状态
			this.inBigIcon = true;
		},
		/**跳转路由*/
		toRouter(item) {
			let extend = item.extend ? JSON.parse(item.extend) : {};
			if (!extend.PC) {
				this.$message.error('未配置跳转链接');
				return;
			}
			let url = extend.PC || '';
			let mainUrl = /http/gi.test(url) ? url : (preUrl || window.location.origin) + url;
			// http和项目内连接跳转
			if (extend.isExternal) {
				if (extend.openType === 2) {
					window.open(mainUrl);
				} else {
					this.$router.push({
						path: `/other-system?url=${encodeURIComponent(mainUrl)}`
					});
				}
			} else if (extend.openType === 2) {
				window.open(mainUrl);
			} else {
				let list = getDefaultMenuList();
				// 判断是不是主菜单
				let isMainMenu = list.some(item => {
					return extend.PC.match(item.path);
				});
				let query = {};
				// 判断跳转路径需要的特殊处理
				if (/\/wile-fire\/home\/index/.test(extend.PC)) {
					// query = {modeType:extend.modeType} // 当前页面是行不通的
					setTimeout(
						() => {
							this._BUS.$emit(CoosEventTypes.changeIm, extend.modeType);
						},
						this.$route.path === '/wile-fire/home/<USER>' ? 0 : 1000
					);
				} else if (!isMainMenu && !this.writeRoute.some(item => extend.PC.indexOf(item) > -1)) {
					query = {
						logoUrlPath: item.logoUrl,
						name: item.name
					};
				}
				if (
					['/wait-handle', '/document-content'].some(item => {
						return extend.PC.match(item);
					}) ||
					this.$route.query.aiType
				) {
					this.tempDbClick = true;
					this.handleDblClickBot();
				} else {
					this.$router.push({
						path: extend.PC,
						query
					});
				}
			}
		},
		/**双击*/
		async handleDblClickBot(event) {
			if (this.loading) return;
			event && event.stopPropagation();
			event && event.preventDefault();
			// 说明触发了双击状态
			this.dbClick = true;
			// tempDbClick临时双击  单机事件为双击    允许双击
			if (this.tempDbClick || this.params.clickEvent === 'dbClick' || this.params.canDbClick) {
				this.tempDbClick = false;
				this.loading = true;
				if (this.params.applicationId) {
					await this.getWelcomeText();
				} else {
					this.welcomeText = this.params.prologue || '欢迎使用智能助手~';
				}
				this.loading = false;
				this.imWindow = true;
				this.showAiIcon = false;
				this.showBig = false;
				this.imDraw = false;
				this.$nextTick(() => {
					this.$refs.imContentCoos && this.$refs.imContentCoos.toBottom();
				});
			}
		},
		/**点击ai入口事件*/
		iconMousedown() {
			if (this.loading) return;
			// 发生点击就重置双击状态
			this.dbClick = false;
			// 说明发生了点击
			this.clickState = true;
			// 可以拖拽
			this.canDrag = true;
			this.T && clearTimeout(this.T);
			this.T = null;
		},
		/**拖动标题*/
		titleDragge() {
			this.draggTitle = !this.draggTitle;
		},
		/**是否打开智能助手*/
		handleAssistant() {
			if (this.loading) return;
			// 单机事件为双击事件
			if (this.params.clickEvent === 'dbClick') {
				this.handleDblClickBot();
				return;
			}
			this.T = setTimeout(() => {
				// 不是双击
				if (!this.dbClick) {
					// 防止首次动画
					this.firstLoad = false;
					// 防止拖动影响到了click事件
					if (this.clickState) {
						// 自定义毁掉
						if (this.params.clickCallback) {
							this.params.clickCallback();
						} else if (!this.params.canDbClick || !this.imWindow) {
							this.imDraw = !this.imDraw;
						}
					}
				}
			}, 300);
		},
		/**关闭coos*/
		closeCoos() {
			this.imDraw = false;
			this.imWindow = false;
			this.showAiIcon = true;
		},
		changeCoosDragStatus(isMoved, position = [], changeDraging = true) {
			if (changeDraging) {
				this.draging = !this.draging;
			}
		},
		/**改变拖拽状态*/
		changeDragState(isMoved, position = [], changeDraging = true) {
			if (changeDraging) {
				this.draging = !this.draging;
			}
			this.iconStatus = 'normal';
			this.position = [];
			// 判断是否靠边
			if (isMoved && position.includes('right')) {
				// 此时初始状态是隐藏
				this.iconStatus = 'hide';
				setTimeout(() => {
					// 动画执行完了再切换大小图标
					this.position = position;
					setTimeout(() => {
						// 防止立即执行进入小图标的事件，进行延迟控制
						this.firstShow = false;
					}, 1000);
				}, 500);
			} else {
				// 不是隐藏的话，可以直接执行
				this.position = position;
			}
			if (isMoved) {
				// 重置第一次靠边，之前不是靠边的初始状态
				this.firstShow = true;
				this.dragStatus = false;
				// 只要发生拖动，就得把大图标切换状态隐藏
				this.showBig = false;
				// 发生了拖动，不是单纯的点击
				this.clickState = false;
				// 保存真实状态，移动的时候才会改变状态
				this.realPosition = position;
			} else {
				// 收起弹窗的列表时，判断是否需要隐藏
				this.position = this.realPosition;
			}
		}
	}
};
</script>

<style scoped lang="scss">
.aiFont {
	width: 60px;
	height: 60px;
	border-radius: 50%;
	cursor: pointer;
	box-shadow: 0px 1px 3px 0px var(--brand-1), 0px 2px 6px 0px var(--brand-1),
		0px 5px 9px 0px var(--brand-1), 0px 17px 24px 0px var(--brand-1);
}
.draggContent {
	height: calc(90% + 40px);
	display: flex;
	align-items: center;
	padding: 20px 0px;
}
.fade-enter-active,
.fade-leave-active {
	transition: all 1s;
}
.fade-enter,
.fade-leave-to {
	opacity: 0;
	transform: translateX(30px);
}
.coosContent {
	border-left: 1px solid #dce3e7;
	flex-shrink: 0;
	width: 30%;
	border-radius: 12px;
	height: 100%;
	max-width: 500px;
	min-width: 400px;
	box-shadow: 0px 1px 3px 0px rgba(40, 76, 185, 0.06), 0px 2px 6px 0px rgba(40, 76, 185, 0.09),
		0px 5px 9px 0px rgba(40, 76, 185, 0.12), 0px 17px 24px 0px rgba(40, 76, 185, 0.18);
	transition: all 0.3s;
}
.content {
	display: flex;
	flex-direction: column;
	align-items: center;
	position: relative;
	z-index: 667;
	cursor: default;
}
.content-list {
	position: absolute;
	bottom: 60px;
	padding: 16px;
	overflow: hidden;
}
.ai-list {
	background: #ffffff;
	//box-shadow: 0px 2px 12px 0px rgba(100, 101, 102, 0.12);
	box-shadow: 2px 8px 8px -2px rgba(0, 0, 0, 0.3);
	border-radius: 12px;
	font-size: 0;
	overflow: hidden;
	padding: 12px 8px 4px;
	border: 1px solid #e2edf9;
	min-width: 120px;
	.ai-icon {
		width: 40px;
		height: 40px;
		cursor: pointer;
	}
}
.animation-transform {
	transition: all 0.2s;
}
.close {
	position: absolute;
	top: 0px;
	right: 0px;
	width: 16px;
	height: 16px;
	cursor: pointer;
}
.item-content {
	display: flex;
	align-items: center;
	margin-bottom: 8px;
	overflow: hidden;
	cursor: pointer;
}
.custom-tip {
	font-weight: 400;
	font-size: 12px;
	color: #2f446b;
	line-height: 22px;
	@include aLineEllipse;
}
.ai-small {
	height: 50px;
	width: 16px;
	position: relative;
	z-index: 667;
}
.ai-small-right {
	transform: translateX(82px);
}
@keyframes show-icon {
	from {
		transform: translateX(100px);
		opacity: 0.3;
	}
	to {
		transform: translateX(0);
		opacity: 1;
	}
}
@keyframes hide-icon {
	from {
		transform: translateX(0);
		opacity: 1;
	}
	to {
		transform: translateX(100px);
		opacity: 0.3;
	}
}
.show-animation {
	animation: show-icon 0.2s forwards;
}
.hide-animation {
	animation: hide-icon 0.2s forwards;
}
.mask {
	position: absolute;
	z-index: 1;
	top: 0;
	left: 0;
	width: 300px;
	height: 60px;
}
.page-mask {
	height: 100%;
	width: 100%;
	position: absolute;
	top: 0;
	left: 0;
	background: rgba(0, 0, 0, 0);
	z-index: 9998;
}
::v-deep .message-detail-title {
	cursor: move;
}
.line {
	border: 1px dashed var(--brand-6);
	margin-bottom: 8px;
}
</style>
<style lang="scss">
.ai-flot-popper-class {
	background: transparent !important;
	border: none !important;
	padding: 0 !important;
	display: flex;
	align-items: center;
	transform: translateX(10px);
	&::after {
		content: '';
		font-size: 0;
		width: 0;
		height: 0;
		border-top: 6px solid transparent;
		border-bottom: 6px solid transparent;
		border-right: 6px solid transparent;
		border-left: 6px solid rgba(21, 34, 76, 0.6);
	}
}
</style>
