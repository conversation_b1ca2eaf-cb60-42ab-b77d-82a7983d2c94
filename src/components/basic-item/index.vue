<template>
	<div
		class="item"
		:style="customStyle"
		:class="{ 'item-active': active }"
		@mouseenter="enter"
		@mouseleave="leave"
	>
		<div v-if="item.isMark" class="finished">已完成</div>
		<header class="header">
			<div class="left">
				<div class="task" :class="{ entrust: item.workMemoType == 1 }">
					{{ item.workMemoType == 1 ? '委托' : '办理' }}
				</div>
				<div class="taskDeatil">{{ newTitle }}</div>

				<!-- <img v-if="item.targetAvatar" class="img" :src="item.targetAvatar" alt="" />
				<div v-else-if="item.targetName" class="img text">
					{{ item.targetName.slice(-1) }}
				</div>
				<div v-else class="img text">无</div>
				<div v-if="item.targetName" class="text title">{{ item.targetName }}</div> -->
			</div>
			<!-- <div class="right">
				<div v-if="!item.isMark" ref="icons" style="display: none">
					<el-tooltip
						v-for="item1 in icons"
						:key="item1.name"
						effect="dark"
						:content="item1.tips"
						placement="bottom"
					>
						<span
							:class="['coos-iconfont', item1.icon, 'icon']"
							:style="{ color: item1.color }"
							@click="clickIcon(item, item1.name)"
						></span>
					</el-tooltip>
				</div>
				<div v-else class="sucess-box">
					<div style="cursor: pointer" @click="clickIcon(item, 'recover')">
						<span class="coos-iconfont icon-refresh icon"></span>
						<span class="text">恢复到未完成</span>
					</div>
					<span class="text sucess">已完成</span>
				</div>
			</div> -->
		</header>

		<div class="detailInfo">
			<div class="detailItem">
				<div class="detailItemTitle">任务时间：</div>
				<div class="detailItemInfo">{{ item.startTime }} 至 {{ item.endTime }}</div>
			</div>
			<div v-if="collapse">
				<div class="detailItem">
					<div class="detailItemTitle">任务地点：</div>
					<div class="detailItemInfo">{{ item.address }}</div>
				</div>
				<div class="detailItem">
					<div class="detailItemTitle">{{ item.workMemoType == 1 ? '办理人：' : '委托人：' }}</div>
					<div class="detailItemInfo" style="margin-left: 14px">{{ item.targetName }}</div>
				</div>
				<div class="detailItem">
					<div class="detailItemTitle">创建时间：</div>
					<div class="detailItemInfo">{{ item.createTime }}</div>
				</div>
				<!-- <div class="detailItem">
					<div class="detailItemTitle">日程类型：</div>
					<div class="detailItemInfo">工作备忘</div>
				</div> -->
				<div class="originInfo">
					<div class="originTitle">来源：</div>
					<div class="originDetail">
						{{ item.workMemoOrigin == 2 ? '聊天消息-单聊' : '聊天消息-群聊' }}
					</div>
				</div>
			</div>
		</div>

		<div class="expand" @click="handleExpand()">
			<span>{{ collapse ? '收起详情' : '展开详情' }}</span>
			<i v-if="collapse" class="el-icon-arrow-up iconType"></i>
			<i v-else class="el-icon-arrow-down iconType"></i>
		</div>
		<div class="handleInfo">
			<div
				v-for="item1 in icons"
				v-show="!(item1.tips == '编辑' && item.isMark)"
				:key="item1.name"
				class="handleDetail"
				@click="clickIcon(item, item1.name)"
			>
				<i :class="['coos-iconfont', item1.icon]"></i>
				<span class="tipName">{{ item1.tips }}</span>
			</div>
		</div>
		<editDialog
			ref="editAdd"
			:hidden-schedule="true"
			:detail="editData"
			@update="updateEdit"
		></editDialog>
	</div>
</template>

<script>
import { parseTime } from '@/utils';
import editDialog from '@/views/calendar/components/new-add.vue';
import { getCalendarDetail } from '@/api/modules/calendar';
export default {
	components: { editDialog },
	props: {
		item: {
			type: Object,
			default: () => {}
		},
		customStyle: {
			type: Object,
			default: () => {}
		}
	},

	data() {
		return {
			dialogVisible: false, // 编辑弹窗
			active: false,
			collapse: false, //控制展开和收起
			icons: [
				{
					name: 'location',
					icon: 'icon-dangqianweizhi',
					tips: '定位到所在位置'
				},
				{
					name: this.item.isMark ? 'recover' : 'complete',
					icon: this.item.isMark ? 'icon-refresh' : 'icon-selected',
					color: '#40C274',
					tips: this.item.isMark ? '恢复到未完成' : '标记完成'
				},
				{
					name: 'delete',
					icon: 'icon-trash',
					tips: '删除'
				},
				{
					name: 'edit',
					icon: 'icon-edit',
					tips: '编辑'
				}
			],
			editContent: false,
			newTitle: this.item.title,
			timeShow: false,
			oldTime: this.item.startTimeDesc,
			newTime: [this.item.startTime, this.item.endTime],
			workMemoTypeOpitions: [
				{
					value: 1,
					label: '我的委托'
				},
				{
					value: 2,
					label: '我的任务'
				}
			],
			editData: {}
		};
	},
	mounted() {},
	methods: {
		/** 控制展开收起 */
		handleExpand() {
			this.collapse = !this.collapse;
		},
		/** 鼠标进入 */
		enter() {
			this.$nextTick(() => {
				const element = this.$refs.icons;
				if (element) {
					element.style.display = 'block';
				}
			});
			this.active = true;
			// this.$refs.icons.style.display = 'block';
		},
		/** 鼠标离开 */
		leave() {
			this.$nextTick(() => {
				const element = this.$refs.icons;
				if (element) {
					element.style.display = 'none';
				}
			});
			this.active = false;
			// this.$refs.icons.style.display = 'none';
		},
		clickEdit(type) {
			if (type === 'title') {
				this.editContent = true;
				this.$nextTick(() => {
					this.$refs.inputRef.focus();
				});
			}
		},
		/** 失焦事件处理 */
		blurHandle(type) {
			if (type === 'title') {
				this.editContent = false;
			} else {
				this.oldTime =
					parseTime(new Date(this.newTime[0]), '{y}-{m}-{d} {h}:{i}') +
					' - ' +
					parseTime(new Date(this.newTime[1]), '{y}-{m}-{d} {h}:{i}');
				this.timeShow = false;
			}
			const newDate = {
				title: this.newTitle,
				startTime: parseTime(new Date(this.newTime[0]), '{y}-{m}-{d} {h}:{i}:{s}'),
				endTime: parseTime(new Date(this.newTime[1]), '{y}-{m}-{d} {h}:{i}:{s}'),
				id: this.item.id
			};
			this.clickIcon(newDate, 'update');
		},
		/** 点击icon */
		clickIcon(item, type) {
			if (type == 'edit') {
				console.log('x');
				this.getDetail(item);
			} else {
				this.$emit('clickIcon', { item, type });
			}
		},
		/**更新列表*/
		updateEdit() {
			this.$emit('getNewList');
		},
		/**获取详情*/
		getDetail(item) {
			getCalendarDetail(item.id, { startDay: item.startDay }).then(res => {
				if (res.code === 200) {
					this.editData = res.result || {};
					this.$nextTick(() => {
						// this.$refs.editAdd.open();
						this.$refs.editAdd.open('edit', new Date(), 2);
						this.dialogVisible = true;
					});
				} else {
					this.$message.error(res.message);
				}
			});
		}
	}
};
</script>

<style scoped lang="scss">
.text {
	font-family: PingFang SC, PingFang SC;
	font-weight: 400;
	font-size: 16px;
	color: $textColor;
}

.item-active {
	box-shadow: 0px 4px 14px 0px rgba(0, 0, 0, 0.1);
}

.item {
	position: relative;
	overflow: hidden;
	margin: 8px 0;
	padding: 12px;
	width: 100%;
	background: #ffffff;
	border-radius: 9px 9px 9px 9px;
	border: 1px solid $borderColor;

	&:last-child {
		margin-bottom: 0px;
	}

	.finished {
		position: absolute;
		width: 103px;
		background: #40c274;
		font-weight: 400;
		font-size: 12px;
		color: #ffffff;
		line-height: 20px;
		text-align: center;
		transform: rotate(45deg);
		right: -30px;
	}

	.header {
		width: 100%;
		display: flex;
		justify-content: space-between;
		line-height: 24px;
		border-bottom: 1px solid #f0f0f0;
		padding-bottom: 6px;

		.left {
			display: flex;
			width: 100%;
			align-items: center;

			.task {
				flex-shrink: 0;
				width: 34px;
				height: 20px;
				background: var(--brand-1);
				border-radius: 3px 3px 3px 3px;
				font-weight: 400;
				font-size: 12px;
				color: var(--brand-6);
				line-height: 20px;
				text-align: center;
				margin-right: 5px;
			}

			.taskDeatil {
				flex: 1;
			}

			.entrust {
				background: rgba(10, 186, 155, 0.1);
				color: #09bc9b;
			}

			// .img {
			// 	width: 24px;
			// 	height: 24px;
			// 	background: #3088ff;
			// 	border-radius: 6px 6px 6px 6px;
			// }
			// .text {
			// 	text-align: center;
			// 	line-height: 24px;
			// 	color: white;
			// }
			// .title {
			// 	margin-left: 4px;
			// 	font-weight: 500;
			// 	font-size: 14px;
			// 	color: $textColor;
			// }
		}

		.right {
			display: flex;
			justify-content: space-between;

			.icon {
				font-size: 14px;
				margin-right: 10px;
				cursor: pointer;

				&:last-child {
					margin-right: 0px;
				}

				&:hover {
					color: #3088ff;
				}
			}

			.sucess-box {
				display: flex;

				.icon {
					font-size: 13px;
					margin-right: 4px;
				}

				.text {
					font-weight: 400;
					font-size: 12px;
					color: $subTextColor;
				}

				.sucess {
					margin-left: 20px;
					width: 44px;
					height: 28px;
					background: rgba(64, 194, 116, 0.2);
					border-radius: 3px 3px 3px 3px;
					font-weight: 400;
					font-size: 12px;
					color: #40c274;
					text-align: center;
				}
			}
		}
	}

	.detailInfo {
		margin-top: 4px;

		.detailItem {
			display: flex;
			align-items: center;
			margin-bottom: 8px;

			.detailItemTitle {
				flex-shrink: 0;
				font-weight: 400;
				font-size: 13px;
				color: $subTextColor;
				line-height: 22px;
				margin-right: 8px;
			}

			.detailItemInfo {
				font-weight: 400;
				font-size: 13px;
				color: $textColor;
				line-height: 22px;
			}
		}
	}

	.originInfo {
		margin-top: 2px;

		.originTitle {
			font-weight: 500;
			font-size: 12px;
			color: $textColor;
			line-height: 20px;
		}

		.originDetail {
			font-weight: 400;
			font-size: 12px;
			color: $primaryTextColor;
			line-height: 20px;
			width: 100%;
			background: #f3f4f6;
			border-radius: 6px 6px 6px 6px;
			padding: 8px;
			margin-top: 8px;
		}
	}

	.expand {
		margin-top: 9px;
		display: flex;
		align-items: center;
		font-weight: 400;
		font-size: 12px;
		color: var(--brand-6);
		line-height: 24px;
		padding-bottom: 8px;
		border-bottom: 1px solid #f0f0f0;
		cursor: pointer;

		.iconType {
			margin-left: 3px;
		}
	}

	.handleInfo {
		padding: 0px 16px;
		padding-top: 11px;
		padding-bottom: 3px;
		display: flex;
		justify-content: space-between;
		align-items: center;

		.handleDetail {
			color: $subTextColor;
			cursor: pointer;
		}

		.tipName {
			font-weight: 400;
			font-size: 14px;
			color: $primaryTextColor;
			line-height: 22px;
			margin-left: 5px;
		}
	}

	// .article {
	// 	margin-top: 12px;
	// 	display: block;
	// 	height: 60px;
	// 	background: #ffffff;
	// 	font-weight: 400;
	// 	font-size: 14px;
	// 	position: relative;

	// 	.span {
	// 		padding: 8px;
	// 		height: 100%;
	// 		display: block;
	// 		border-radius: 6px 6px 6px 6px;
	// 		border: 1px solid #f0f0f0;
	// 		line-height: 22px;
	// 		display: -webkit-box;
	// 		-webkit-box-orient: vertical;
	// 		-webkit-line-clamp: 2;
	// 		overflow: hidden;
	// 	}

	// 	.icon {
	// 		position: absolute;
	// 		bottom: 9px;
	// 		right: 9px;
	// 		cursor: pointer;
	// 		font-size: 14px;
	// 	}
	// }
	.footer {
		margin-top: 12px;
		height: 20px;
		line-height: 20px;

		.icon {
			font-size: 12px;
			color: $subTextColor;
		}

		.time {
			margin-left: 6px;
			font-size: 12px;
			color: $subTextColor;
		}

		.icon {
			margin-left: 6px;
		}
	}
}

::v-deep .el-dialog {
	border-radius: 16px;
}

.titleContent {
	display: flex;
	align-items: center;
	margin-bottom: 16px;

	.titleDetail {
		margin: 0px 24px;
	}
}
</style>
