<template>
	<div class="render">
		<div
			v-for="(item, index) of list"
			:key="index"
			:style="{
				maxHeight: '100%',
				height: item.height,
				width: item.width,
				position: 'absolute',
				top: item.top,
				left: item.left
			}"
		>
			<async-component
				v-bind="item.options"
				:umd-url="item.umdUrl ? item.umdUrl : assetsUrlPre + umdMap[item.type]"
			></async-component>
		</div>
	</div>
</template>

<script>
import asyncComponent from '@/components/async-component';
import { assetsUrl } from '@/config';

export default {
	name: 'Render',
	components: { asyncComponent },
	props: {
		list: {
			type: Array,
			default: () => {
				return [];
			}
		}
	},
	data() {
		return {
			assetsUrlPre: assetsUrl + '/common/umd',
			umdMap: {
				ApplicationManage: '/ApplicationManage.umd.js',
				ApplicationGateway: '/ApplicationGateway.umd.js',
				TenantGuide: '/TenantGuide.umd.js',
				WebDisplay: '/WebDisplay.umd.js',
				SwiperImage: '/SwiperImage.umd.js',
				MyTask: '/MyTask.umd.js',
				CustomFormFill: '/CustomFormFill.umd.js'
			}
		};
	}
};
</script>

<style scoped lang="scss">
.render {
	position: relative;
	height: 100%;
	overflow: auto;
	@include noScrollBar;
}
</style>
