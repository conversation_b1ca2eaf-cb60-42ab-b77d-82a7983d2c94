<!-- eslint-disable vue/no-v-html -->
<!--
 * @Description: 表单数据表格展示
 * @Version: 1.0
 * @Autor: zhaodongming
 * @Date: 2022-08-17 11:45:03
 * @LastEditors: zhaodongming
 * @LastEditTime: 2024-04-16 09:08:48
-->
<template>
	<div class="table-render">
		<!--		{{ eleData }}-->
		<!-- showMode=2 标签展示方式 -->
		<div v-if="eleData.showMode == 2">
			<!--            字典类-->
			<table-tage :ele-data="arrDataSplit(row, eleData.field + 'DictText')" />
		</div>
		<!-- showMode=3 图片展示方式 -->
		<FileById
			v-else-if="eleData.showMode == 3"
			:value="getImageData(row[eleData.field])"
			:size="40"
			:can-pre-view="true"
		/>
		<!-- showMode=4 文件a连接展示方式 -->
		<div v-else-if="eleData.showMode == 4" class="link">
			<el-link
				v-for="link in getLinks(row[eleData.field])"
				:key="link.fileId"
				type="primary"
				@click="fileView(link)"
			>
				<i class="coos-iconfont icon-lianjie"></i>
				{{ link.originalFileName }}
			</el-link>
		</div>
		<!-- showMode=5 富文本展示方式 -->
		<div v-else-if="eleData.showMode == 5" class="editor" @click="editorInfo(row[eleData.field])">
			<el-link>{{ getEditorText(row[eleData.field], 50) }}</el-link>
		</div>
		<!--          时间日期范围-->
		<div v-else-if="eleData.showMode == 6" class="text">
			{{ Array.isArray(row[eleData.field]) ? row[eleData.field].join('至') : '' }}
		</div>
		<div v-else-if="eleData.showMode == 7">
			<!-- <el-tag v-for="(item, index) in scope.row[t.field]"  :key="index">{{ item }}</el-tag> -->
			<!--          开关-->
			<div v-if="typeof row[eleData.field] == 'boolean'" class="text">
				<el-switch v-model="row[eleData.field]" disabled></el-switch>
			</div>
		</div>
		<!-- 默认展示且不是按钮 -->
		<div v-else-if="!eleData.btns" class="text">
			{{ row[eleData.field + 'DictText'] || row[eleData.field] || '-' }}
		</div>
		<slot></slot>
		<!-- 富文本预览 -->
		<el-dialog title="" :visible.sync="dialogVisible" width="80%">
			<div class="editor-view" v-html="row[eleData.field]"></div>
		</el-dialog>
	</div>
</template>

<script>
import { previewFile } from '@/utils';
import TableTage from './coos-table-tag.vue';
export default {
	name: 'FormTableRender',
	components: { TableTage },
	props: {
		row: {
			type: Object,
			default: () => {}
		},
		eleData: {
			type: Object,
			default: () => {}
		}
	},
	data() {
		return {
			dialogVisible: false,
			editorHtml: null
		};
	},
	watch: {},
	created() {},
	methods: {
		// 数组字符串切割为数组
		arrDataSplit(data, field) {
			const arrString = data[field] ? String(data[field]) : '';
			if (arrString) {
				return arrString.split(',');
			}
			return [];
		},
		// 图片文件id获取
		getImageData(data) {
			try {
				const idsArr = data.map(list => list?.fileUrl);
				return idsArr.join(',');
			} catch (e) {
				return data || '';
			}
		},
		// 获取链接
		getLinks(links) {
			if (links && links.length > 0) {
				return links;
			}
			return [];
		},
		// 获取富文本缩略文本
		getEditorText(data, length) {
			if (data && data.length > 0) {
				const richText = data.replace(/<[^<>]+>/g, '').replace(/&nbsp;/gi, '');
				// 有文本内容
				return richText ? richText.substring(0, length) : '...';
			}
			return '';
		},
		// 富文本详情查看
		editorInfo(html) {
			this.editorHtml = html;
			this.dialogVisible = true;
		},
		fileView(link) {
			const url = link.fileUrl;
			var fileExtension = url.substring(url.lastIndexOf('.') + 1); // 从最后一个点开始提取字符串作为文件格式
			//文件预览
			if (['doc', 'pdf', 'text', 'excel', 'pp', 'video', 'unknown'].includes(fileExtension)) {
				previewFile(url);
			} else {
				// 不支持预览的文件就下载
				let aDom = document.createElement('a');
				aDom.style.display = 'none';
				aDom.href = url;
				aDom.setAttribute('download', link.originalFileName);
				document.body.appendChild(aDom);
				aDom.click();
				document.body.removeChild(aDom);
			}
		}
	}
};
</script>

<style lang="scss" scoped>
.table-render {
	width: 100%;
}

.tags {
	text-wrap: wrap;

	.table-tag {
		margin-right: 6px;
		max-width: 100%;
		overflow: hidden;
		text-wrap: nowrap;
		text-overflow: ellipsis;

		&:last-child {
			margin-right: 0;
		}
	}
}

.text {
	text-wrap: nowrap;
	text-overflow: ellipsis;
	overflow: hidden;
}

.link {
	text-wrap: wrap;
}

.editor {
	max-height: 60px;
	cursor: pointer;
	white-space: nowrap;
	text-overflow: ellipsis;
	overflow: hidden;

	::v-deep .el-link {
		display: inline;
	}
}

.editor-view {
	::v-deep img {
		max-height: 100%;
		max-width: 100%;
	}

	::v-deep .ql-video,
	::v-deep video {
		max-width: 100%;
		max-height: 100%;
	}
}
</style>
