<!-- eslint-disable vue/no-v-html -->
<!--
 * @Description: 表格标签
 * @Version: 1.0
 * @Autor: zhaodongming
 * @Date: 2022-08-17 11:45:03
 * @LastEditors: zhaodongming
 * @LastEditTime: 2024-04-16 10:00:28
-->
<template>
	<div class="table-tag-box">
		<div ref="tableTag" class="table-tag">
			{{ eleData.join('，') }}
		</div>
		<span
			v-if="eleData.length > 1 && getRenderWidth(eleData.join('，')) > tagWidth"
			class="table-tag-length"
			:style="{ minWidth: `${getRenderWidth(`+${eleData.length}`)}px` }"
		>
			{{ `+${eleData.length}` }}
		</span>
	</div>
</template>

<script>
export default {
	name: 'CoosTableTag',
	props: {
		eleData: {
			type: Array,
			default: () => []
		}
	},
	data() {
		return {
			tagWidth: 0
		};
	},
	watch: {},
	created() {},
	mounted() {
		this.getTagWidth();
	},
	methods: {
		getTagWidth() {
			this.$nextTick(() => {
				this.tagWidth = this.$refs.tableTag.offsetWidth;
			});
		},
		getRenderWidth(content) {
			// 新建一个 span
			let span = document.createElement('span');
			// 设置表头名称
			span.innerText = content;
			// 临时插入 document
			document.body.appendChild(span);
			const minWidth = span.getBoundingClientRect().width;
			// 移除 document 中临时的 span
			document.body.removeChild(span);
			return minWidth + 8;
		}
	}
};
</script>

<style lang="scss" scoped>
.table-tag-box {
	// position: relative;
	display: flex;
}

.table-tag {
	text-wrap: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.table-tag-length {
	color: #ffac07;
	// position: absolute;
	// right: 0;
	// top: 0;
}
</style>
