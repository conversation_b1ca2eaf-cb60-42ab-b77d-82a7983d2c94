<!--
 * @Description: 全局办理详情弹窗
 * @Version: 1.0
 * @Autor: FH.hao
 * @Date: 2024-06-04 17:41:37
-->
<template>
	<el-dialog
		title=""
		class="desk-el-dialog"
		:visible.sync="drawer"
		:fullscreen="true"
		append-to-body
		:modal="false"
	>
		<div v-loading="dialogLoading" class="content">
			<div class="content-title">
				<span class="content-title-text">
					{{ needReload ? `待办处理` : '查看详情' }}
					<span
						v-if="
							waitConfig &&
							waitConfig.customerTaskMenu &&
							waitConfig.customerTaskMenu.handleBtnPlace === 'mix'
						"
						style="font-size: 16px; font-weight: 400"
					>
						【{{ todoTitle }}】
					</span>
				</span>
				<div>
					<el-tooltip class="item" effect="dark" content="新窗口打开" placement="left">
						<i
							v-if="!isPage"
							class="coos-iconfont icon-send content-title-icon"
							@click="goHandle"
						></i>
					</el-tooltip>
					<i
						v-if="!isPage"
						class="coos-iconfont icon-guanbi1 content-title-icon"
						@click="doCloseHandler"
					></i>
				</div>
			</div>
			<div class="content-body">
				<!--    三方集成的待办，完全为第三方提供的页面Start    -->
				<webviewBox
					v-if="isWebview"
					style="flex: 1"
					:view-url="detailUrl"
					:need-socket="false"
				></webviewBox>
				<!--    三方集成的待办，完全为第三方提供的页面End    -->
				<!--    基础框架的待办表单+流程Start    -->
				<process-flow
					v-else-if="!isWebview && drawer"
					:flow-handle-params="flowParams"
					:need-reload="needReload"
					@close="doCloseHandler"
					@handleTask="handleTask"
				></process-flow>
				<!--    基础框架的待办表单+流程End    -->
			</div>
		</div>
	</el-dialog>
</template>
<script>
import { mapState } from 'vuex';
import webviewBox from '@/components/webview-box/index.vue';
import processFlow from '@/views/process/start-process/index.vue';
import { handleTask } from '@/api/modules/common';
import { updateCount } from '@/utils/update-help';
import { handleList } from '@/api/modules/wait-handle';
import { CoosEventTypes } from '@/utils/bus';
import { getDictionary } from '@/utils/data-dictionary';
import { GetQueryString } from '@/utils';

export default {
	components: { processFlow, webviewBox },
	data() {
		return {
			isPage: false,
			flowParams: {}, // 流程参数
			showCoos: true, // 是否显示coos对话
			isWebview: true, // 是否webview
			needReload: true, // 是否需要重新加载列表
			detailUrl: '', // webview的地址
			currentDetailId: '',
			row: {},
			currentDetailUserId: '',
			drawer: false, // 显隐
			dialogLoading: false, // 详情加载效果
			todoTitle: ''
		};
	},
	computed: {
		...mapState('user', ['waitConfig'])
	},
	mounted() {
		if (this.$route.query.blank_id) {
			this.isPage = true;
			this.openDetail(this.$route.query.blank_id);
		}
		this._BUS.$on(CoosEventTypes.closePopup, this.doClose);
	},
	beforeDestroy() {
		this._BUS.$off(CoosEventTypes.closePopup, this.doClose);
	},
	methods: {
		getQueryParam(url, paramName) {
			const parsedUrl = new URL(url);
			const queryParams = parsedUrl.searchParams;
			const params = {};
			for (const [key, value] of queryParams) {
				params[key] = value;
			}
			return params;
		},
		/**三方集成的待办页面通知关闭弹窗*/
		doClose(handleType) {
			if (this.isPage) {
				window.close();
			}
			// 有数据id并且弹窗显示才调用，避免SDK攻击
			if (this.currentDetailId && this.drawer) {
				// 办理还是关闭
				if (handleType === 'close') {
					this.doCloseHandler();
				} else {
					this.handleTask(); // 处理提交
				}
			}
		},
		/**如果处理了待办，要及时更新数据*/
		handleTask() {
			this.dialogLoading = true;
			handleTask(this.currentDetailId, this.currentDetailUserId).then(res => {
				this.dialogLoading = false;
				this.currentDetailId = '';
				this.currentDetailUserId = '';
				if (res.code === 200) {
					this.$message.success('操作成功');
					// TODO 调用后台数据刷新
					this.doCloseHandler();
				} else {
					this.$message.error(res.message);
				}
			});
		},
		/** 管理处理/详情对话框 */
		doCloseHandler() {
			updateCount(); // 更新待办数据
			if (this.$route.path === '/wait-handle' && Object.keys(this.$route.query).length) {
				this.$router.replace({ path: this.$route.path });
			}
			this.detailUrl = ''; // 防止iframe不刷新，导致页面生命周期不重新执行
			this._BUS.$emit(CoosEventTypes.aiSessionParams, {
				coosType: 'waitList',
				coosTypeCode: getDictionary('AI编码/智问待办'),
				applicationId: getDictionary('应用ID/待办'),
				modeType: 'zdbk',
				canDbClick: true
			});
			if (!this.needReload) {
				this.drawer = false;
				return;
			}
			this.drawer = false;
			this.$emit('updateList');
		},
		// 获取详情
		openDetail(id) {
			handleList({ id, pageNo: 1, pageSize: 30 }).then(res => {
				let arr = res.result.records || [];
				if (arr.length !== 0) {
					let row = arr[0];
					this.open(row);
				}
			});
		},
		goHandle() {
			const route = this.$router.resolve({ name: 'WaitDetail' });
			window.open(`${route.href}?blank_id=${this.row.id}`, '_blank');
			this.doCloseHandler();
		},
		/**打开详情弹窗*/
		open(row) {
			this.row = row;
			this.todoTitle = row.title;
			let url;
			let config = JSON.parse(row.slotAccessJson);
			try {
				this.showCoos = config.coosRobot === 'true';
			} catch (e) {
				this.showCoos = false;
			}
			// 详情中是否有coos机器人
			if (this.showCoos) {
				let robotData = JSON.parse(row.robotData);
				this._BUS.$emit(CoosEventTypes.aiSessionParams, {
					robotDataContent: robotData.content,
					metadataIds: row.transactionId,
					chatObjectId: row.id,
					componentType: 'wait',
					prologue: '你好，我是COOS助手，可以为你检索事务细节，助你快速决策。',
					clickEvent: 'dbClick',
					showCoos: true
				});
			} else {
				this._BUS.$emit(CoosEventTypes.aiSessionParams, {
					hideCoos: true
				});
			}
			if (config && config.clientTypes && config.clientTypes.length === 0) {
				url = row.detailUrl;
			} else {
				let urlMap = JSON.parse(row.detailUrl); // 解析出url映射
				url = urlMap.PC;
			}
			if (url.indexOf('?')) {
				url += `&coosStatus=${row.status}&dealOptType=${row.dealOptType}`;
			} else {
				url += `?coosStatus=${row.status}&dealOptType=${row.dealOptType}`;
			}
			if (url.indexOf('http') < 0) {
				// 如果没有匹配到http,那么就是当前项目的,
				this.flowParams = this.getQueryParam(`http://${url}`);
				this.isWebview = false;
			} else {
				this.detailUrl = url;
				this.isWebview = true;
			}
			let openDetailType = this.waitConfig.customerTaskMenu.detailOpenType || '1'; // 打开待办的方式
			// 川投待办新窗口打开的url参数判断
			if (GetQueryString(url, 'newPage') && openDetailType !== '2') {
				window.open(url, '_blank');
				return;
			}
			this.needReload = row.status === 0;
			this.currentDetailId = row.id;
			this.currentDetailUserId = row.userId;
			this.drawer = true;
		}
	}
};
</script>
<style scoped lang="scss">
.content {
	display: flex;
	flex-direction: column;
	height: 100%;
	width: 100%;

	&-title {
		height: 60px;
		padding: 0 20px;
		@include flexBox(space-between);
		border-bottom: 1px solid #dce3e7;

		&-text {
			font-weight: 800;
			font-size: 18px;
			color: #303133;
			line-height: 24px;
			text-align: left;
			font-style: normal;
			text-transform: none;
		}

		&-icon {
			margin-right: 12px;
			font-size: 22px;
			cursor: pointer;
		}
	}

	&-body {
		display: flex;
		align-items: center;
		flex: 1;
		overflow: hidden;
	}
}

// 提取组件复制过来，不知道哪里会用
.desk-el-form {
	::v-deep .el-input__inner {
		height: 32px !important;
	}
}
.desk-el-dialog {
	display: flex;
	align-items: center;
	justify-content: center;

	::v-deep .el-dialog {
		border-radius: 0;
		min-width: 530px;
		margin: 0;

		.el-dialog__header {
			display: none;
		}

		.el-dialog__body {
			height: 100%;
			width: 100%;
			padding: 0;
			border-radius: 0;
		}

		.el-dialog__headerbtn {
			top: 31px;
			right: 30px;
		}
	}
}

.coos-iconfont {
	&:hover {
		color: var(--brand-6);
	}
}
</style>
