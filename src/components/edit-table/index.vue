<template>
	<div class="edit_table">
		<el-table
			class="desk-el-table"
			:data="tableData"
			v-bind="$attrs"
			:header-row-style="headerRowStyle"
			:header-cell-style="headerCellStyle"
			:row-style="getRowStyle"
			:row-class-name="rowClassName"
			v-on="$listeners"
		>
			<el-table-column
				v-if="showCheckbox"
				type="selection"
				width="55"
				align="center"
			></el-table-column>
			<el-table-column
				v-if="sort"
				label="序号"
				type="index"
				width="80"
				align="center"
			></el-table-column>
			<el-table-column
				v-for="(item, index) in tableColumn"
				:key="index"
				v-bind="item"
				:render-header="item.required ? renderHeader : null"
			>
				<template slot-scope="scope">
					<section v-if="canEdit">
						<span
							v-if="
								item.isReadOnly ||
								item.type === 'view' ||
								(scope.row.readOnlyKey &&
									scope.row.readOnlyKey.indexOf(item.field || item.prop) > -1)
							"
							@click="
								() => {
									handleClick(item.prop, scope.row);
								}
							"
						>
							{{ getValue(item, scope.row[item.prop] || '') }}
							<span
								v-if="scope.row.showAddBtnKeys && scope.row.showAddBtnKeys.indexOf(item.prop) > -1"
								class="btn_cell_add"
								@click="handleCellAdd(item.prop, scope.row, scope.$index)"
							>
								<i class="el-icon-circle-plus-outline" title="添加"></i>
							</span>
							<!-- 显示 -->
							<span
								v-if="scope.row.showExpenKeys && scope.row.showExpenKeys.indexOf(item.prop) > -1"
								class="btn_cell_open"
								:class="scope.row.isOpenList ? 'visible' : 'defalt'"
								@click="handleClickShowBtn(item.prop, scope.row, scope.$index)"
							>
								<i class="el-icon-arrow-right" :title="scope.row.isOpenList ? '收起' : '展开'"></i>
							</span>
						</span>
						<el-date-picker
							v-else-if="
								item.type === 'date' ||
								item.type === 'time' ||
								item.type === 'datetime' ||
								item.type === 'daterange'
							"
							v-model="scope.row[item.prop]"
							v-bind="item.formInputConfig || {}"
							:type="item.type"
							@change="
								val => {
									handleTableChange(item.prop, val, scope.row, scope.$index);
								}
							"
							@click="
								() => {
									handleClick(item.prop, scope.row);
								}
							"
							@focus="
								() => {
									handleFocus(item.prop, scope.row, scope.$index);
								}
							"
						></el-date-picker>
						<el-select
							v-else-if="item.type === 'select'"
							v-model="scope.row[item.prop]"
							v-bind="item.formInputConfig || {}"
							@change="
								val => {
									handleTableChange(item.prop, val, scope.row, scope.$index);
								}
							"
							@click="
								() => {
									handleClick(item.prop, scope.row);
								}
							"
							@focus="
								() => {
									handleFocus(item.prop, scope.row, scope.$index);
								}
							"
						>
							<el-option
								v-for="(item2, index2) in options[item.prop]"
								:key="index2"
								:label="item2.label"
								:value="item2.value"
							></el-option>
						</el-select>
						<el-button v-else-if="item.type === 'file'" @click="uploadFile(scope.$index)">
							上传
						</el-button>
						<span v-else-if="item.type === 'staff'">
							<div
								class="inp-box"
								:class="[scope.row[item.prop] ? '' : 'act']"
								@click="selectPersonnel(scope.$index)"
							>
								<span>{{ scope.row[item.prop] || '请选择成员' }}</span>
							</div>
							<!-- {{ scope.row[item.prop] }}
							<el-button >选择人员</el-button> -->
						</span>
						<span v-else-if="item.type === 'string'">{{ scope.row[item.prop] }}</span>
						<el-input-number
							v-else-if="item.type === 'number'"
							v-model="scope.row[item.prop]"
							v-bind="item"
							controls-position="right"
							:precision="item.precision != 0 && !item.precision ? 2 : 0"
							placeholder="请输入"
							style="width: 140px"
							@change="
								val => {
									handleTableChange(item.prop, val, scope.row, scope.$index);
								}
							"
							@click="
								() => {
									handleClick(item.prop, scope.row);
								}
							"
							@focus="
								() => {
									handleFocus(item.prop, scope.row, scope.$index);
								}
							"
						></el-input-number>
						<el-input
							v-else
							v-model="scope.row[item.prop]"
							v-bind="item.formInputConfig || {}"
							placeholder="请输入"
							@change="
								val => {
									handleTableChange(item.prop, val, scope.row, scope.$index);
								}
							"
							@click="
								() => {
									handleClick(item.prop, scope.row);
								}
							"
							@focus="
								() => {
									handleFocus(item.prop, scope.row, scope.$index);
								}
							"
						></el-input>
					</section>
					<!-- <span
						v-else
						@click="
							() => {
								handleClick(item.prop, scope.row);
							}
						"
					>
						{{ getValue(item, scope.row[item.prop] || '') }}
					</span> -->
					<el-button v-else-if="item.type === 'file'" @click="uploadFile(scope.$index)">
						查看附件
					</el-button>
					<div v-else>
						{{ scope.row.showAddBtn }} {{ getValue(item, scope.row[item.prop]) }}
						<span v-if="scope.row.showAddBtn">+</span>
						<span
							v-if="scope.row.showExpenKeys && scope.row.showExpenKeys.indexOf(item.prop) > -1"
							class="btn_cell_open"
							:class="scope.row.isOpenList ? 'visible' : 'defalt'"
							@click="handleClickShowBtn(item.prop, scope.row, scope.$index)"
						>
							<i class="el-icon-arrow-right" :title="scope.row.isOpenList ? '收起' : '展开'"></i>
						</span>
					</div>
				</template>
			</el-table-column>
			<el-table-column v-if="canEdit && (showAdd || showDelete)" label="操作" width="150">
				<template slot-scope="scope">
					<div class="btn_grounp">
						<div
							v-if="showAdd && scope.row.showAdd != false"
							class="btn_cell_add"
							@click="handleItemAdd(scope.$index)"
						>
							新增
						</div>
						<div
							v-if="showDelete && scope.row.showDelete != false"
							class="btn_cell_dlt"
							@click="handleItemDlt(scope.$index)"
						>
							删除
						</div>
					</div>
				</template>
			</el-table-column>
		</el-table>
	</div>
</template>

<script>
export default {
	props: {
		showDelete: {
			type: Boolean,
			default: false
		},
		showAdd: {
			type: Boolean,
			default: false
		},
		sort: {
			type: Boolean,
			default: false
		},
		tableColumn: {
			type: Array,
			default() {
				return [];
			}
		},
		tableData: {
			type: Array,
			default() {
				return [];
			}
		},
		// 下拉项配置
		options: {
			type: Object,
			default() {
				return {};
			}
		},
		canEdit: {
			type: Boolean,
			default: true
		},
		showCheckbox: {
			type: Boolean,
			default: false
		}
	},
	data() {
		return {
			/**每一行的样式*/
			headerRowStyle: {
				height: '48px',
				background: '#EDF2F6',
				borderRadius: '6px'
			},
			/**每一格样式*/
			headerCellStyle: {
				background: 'transparent',
				fontSize: '16px',
				fontFamily: 'PingFang SC, PingFang SC',
				fontWeight: 500,
				color: '#2f446b',
				lineHeight: '24px'
			}
		};
	},
	computed: {
		// dataList() {}
	},
	methods: {
		/**获取表格奇偶列的样式*/
		getRowStyle({ row, rowIndex }) {
			return {
				height: '54px',
				borderRadius: '6px',
				background: rowIndex % 2 === 0 ? '#ffffff' : ' #f5f7fa'
			};
		},
		handleClick(key, row) {
			this.$emit('click', key, row);
		},
		handleFocus(key, row, index) {
			this.$emit('focus', key, row, index);
		},
		handleTableChange(key, val, row, index) {
			this.$emit('change', key, val, row, index);
		},
		renderHeader(h, { column }) {
			return h('span', [
				column.label,
				h('span', { style: 'color: red;', class: 'optional' }, ' *')
			]);
		},
		// 点击上传按钮
		uploadFile(index) {
			this.$emit('uploadFile', index);
		},
		// 点击选择人员
		selectPersonnel(index) {
			this.$emit('selectPersonnel', index);
		},
		getValue(item, v) {
			if (v != 0 && !v) {
				return '';
			}
			if (!this.options[item.prop]) {
				return v;
			}

			let vals =
				this.options[item.prop].filter(item => {
					return item.value == v;
				}) || [];
			let val = vals
				.map(e => {
					return e.label;
				})
				.join(',');
			return val || v;
		},
		handleCellAdd(key, row, index) {
			// 绑定在某个字段上的添加按钮
			this.$emit('handleCellAdd', key, row, index);
		},
		handleClickShowBtn(key, row, index) {
			// 绑定在某个字段上的显示和隐藏按钮
			this.$emit('handleClickShow', key, row, index);
		},
		handleItemAdd(index) {
			// 操作栏的添加按钮
			this.$emit('handleItemAdd', index);
		},
		handleItemDlt(index) {
			// 操作栏的删除按钮
			this.$emit('handleItemDlt', index);
		},
		// 行的样式控制方法，通过这个回调方法控制隐藏显示
		rowClassName: function ({ row }) {
			if (row.hideRow) {
				return 'hidden-row';
			}
			return '';
		}
	}
};
</script>

<style>
.btn_cell_add {
	cursor: pointer;
	font-size: 14px;
	color: var(--brand-6);
	margin-right: 8px;
}
.btn_grounp {
	display: flex;
}
.btn_cell_dlt {
	cursor: pointer;
	font-size: 14px;
	color: #e34d59;
}
.btn_cell_open {
	transition: all 0.3s;
	cursor: pointer;
	display: inline-block;
	padding: 0 4px;
}
.btn_cell_open.visible {
	transform: rotate(90deg); /* 顺时针旋转90度 */
}
.el-table .hidden-row {
	display: none;
}

.inp-box {
	border-radius: 3px;
	border: 1px solid #d9d9d9;
	font-size: inherit;
	height: 32px;
	line-height: 32px;
	outline: 0;
	padding: 0 15px;
	margin: 4px 0;
	&.act {
		color: #d1d1d1;
	}
	&.disabled {
		cursor: not-allowed;
		background: rgba(0, 0, 0, 0.8);
		background-color: rgba(0, 0, 0, 0.04);
		border-color: rgba(0, 0, 0, 0.15);
		color: #999;
	}
}
</style>
