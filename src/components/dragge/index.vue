<template>
	<div
		:id="draggId"
		class="draggable"
		:style="styleObject"
		@mousedown="dragStart"
		@touchstart="dragStart"
	>
		<slot name="draggable"></slot>
	</div>
</template>

<script>
import { mapGetters, mapMutations } from 'vuex';
import { isEmpty, debounce } from '@/utils';

export default {
	props: {
		draggId: {
			type: String,
			default: () => {
				return 'dragg';
			}
		},
		topDistance: {
			type: String,
			default: '75%'
		},
		leftDistance: {
			type: String,
			default: 'calc(100% - 100px)'
		},
		isDragg: {
			type: Boolean,
			default: true
		},
		imDraw: {
			type: Boolean,
			default: false
		}
	},

	data() {
		return {
			moved: false, // 是否移动过
			dragging: false,
			mouseX: 0,
			mouseY: 0,
			styleObject: {
				position: 'fixed',
				top: this.topDistance,
				left: this.leftDistance
			},
			position: []
		};
	},
	computed: {
		...mapGetters(['flotPosition'])
	},
	watch: {
		/**对话窗口的初始位置特殊处理*/
		imDraw: {
			handler(newVal) {
				if (newVal) {
					this.$nextTick(() => {
						// let width = document.getElementById(this.draggId).clientWidth;
						// this.styleObject.left = `calc(100% - ${width + 20}px )`;
						this.styleObject.left = this.leftDistance;
						this.styleObject.top = this.topDistance;
					});
				}
			}
		},
		flotPosition: {
			deep: true,
			handler: function (newVal) {
				if (!this.imDraw) {
					this.updateCache(false);
				}
			}
		}
	},
	mounted() {
		window.addEventListener('resize', this.onResize);
		if (!isEmpty(this.flotPosition)) {
			this.updateCache();
		}
	},
	destroyed() {
		window.removeEventListener('resize', this.onResize);
	},
	methods: {
		...mapMutations('settings', ['SET_FLOT_POSITION']),
		onResize: debounce(
			function () {
				this.checkPosition();
			},
			500,
			false
		),
		/**检测位置合理性*/
		checkPosition(isSave = true) {
			if (this.imDraw) return;
			if (this.position.includes('right')) {
				this.styleObject.left = window.innerWidth + 'px';
			}
			if (this.position.includes('bottom')) {
				this.styleObject.top = window.innerHeight + 'px';
			}
			this.moved = true;
			this.dragEnd(false, isSave);
		},
		/**更新缓存*/
		updateCache(isSave = true) {
			if (this.imDraw) return;
			this.styleObject.top = this.flotPosition.top || this.topDistance;
			this.styleObject.left = this.flotPosition.left || this.leftDistance;
			this.position = this.flotPosition.position || [];
			this.checkPosition(isSave);
		},
		dragStart(e) {
			if (this.isDragg) {
				e.preventDefault();
				this.$emit('changeDragState');
				this.moved = false;
				this.dragging = true;
				const rect = e.target.getBoundingClientRect();
				if (this.imDraw) {
					this.mouseY = e.clientY - rect.top + 20;
					this.mouseX = e.clientX - rect.left + 20;
				} else {
					this.mouseY = e.clientY - rect.top;
					this.mouseX = e.clientX - rect.left;
				}
				document.addEventListener('mousemove', this.drag);
				document.addEventListener('touchmove', this.drag);
				document.addEventListener('mouseup', this.dragEnd);
				document.addEventListener('touchend', this.dragEnd);
			} else {
				return false;
			}
		},
		drag(e) {
			if (this.dragging) {
				this.$emit('dragging');
				this.moved = true;
				const x = e.clientX - this.mouseX;
				const y = e.clientY - this.mouseY;
				this.styleObject.left = `${x}px`;
				this.styleObject.top = `${y}px`;
			}
		},
		dragEnd(changeDraging = true, isSave = true) {
			if (this.moved) {
				let left = this.styleObject.left.replace('px', '');
				let top = this.styleObject.top.replace('px', '');
				this.position = [];
				if (left < 0) {
					this.styleObject.left = '20px';
					this.position.push('left');
				}
				if (left > window.innerWidth - 100) {
					this.styleObject.left = window.innerWidth - 100 + 'px';
					this.position.push('right');
				}
				if (top < 0) {
					this.styleObject.top = '20px';
					this.position.push('top');
				}
				if (top > window.innerHeight - 100) {
					this.styleObject.top = window.innerHeight - 100 + 'px';
					this.position.push('bottom');
				}
				/**除了im对话窗口才缓存位置*/
				if (!this.imDraw && isSave) {
					this.SET_FLOT_POSITION({ ...this.styleObject, position: this.position });
				}
				this.dragging = false;
			}
			// 是否通知外层
			this.$emit('changeDragState', this.moved, this.position, changeDraging);
			document.removeEventListener('mousemove', this.drag);
			document.removeEventListener('touchmove', this.drag);
			document.removeEventListener('mouseup', this.dragEnd);
			document.removeEventListener('touchend', this.dragEnd);
		}
	}
};
</script>

<style>
.draggable {
	width: auto;
	z-index: 9999;
	cursor: pointer;
	user-select: none;
}
</style>
