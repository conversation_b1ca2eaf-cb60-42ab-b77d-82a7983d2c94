<template>
	<div ref="bar" :key="echartsKey" class="bar" :style="{ height: height, width: width }"></div>
</template>

<script>
import * as echarts from 'echarts';
import { debounce } from '@/utils';
export default {
	name: 'EchartsIndex',
	props: {
		height: {
			type: String,
			default: '300px'
		},
		width: {
			type: String,
			default: '100%'
		}
	},
	data() {
		return {
			myChart: null,
			options: null,
			echartsKey: 0
		};
	},
	mounted() {
		window.addEventListener('resize', this.onResize);
		let chartDom = this.$refs.bar;
		this.myChart = echarts.init(chartDom);
	},
	destroyed() {
		window.removeEventListener('resize', this.onResize);
	},
	methods: {
		onResize: debounce(
			function () {
				this.echartsKey += 1;
				this.$nextTick(() => {
					let chartDom = this.$refs.bar;
					this.myChart = echarts.init(chartDom);
					this.getEcharts(this.options);
				});
			},
			500,
			false
		),
		saveImage() {
			if (this.myChart) {
				const src = this.myChart.getDataURL({
					pixelRatio: 2,
					backgroundColor: '#fff'
				});
				const a = document.createElement('a');
				a.href = src;
				a.download = 'chart-img';
				a.click();
			}
		},
		getEcharts(option) {
			this.options = option;
			this.myChart.clear();
			option && this.myChart.setOption(option);
		}
	}
};
</script>
