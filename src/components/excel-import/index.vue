<template>
	<div class="excel-import">
		<el-upload
			ref="upload"
			class="excel-upload"
			:action="action"
			:auto-upload="false"
			:accept="accept"
			:show-file-list="showFileList"
			:on-change="handleChange"
			:before-upload="beforeUpload"
			:limit="1"
			:on-exceed="handleExceed"
			:file-list="fileList"
			:disabled="loading"
		>
			<div class="upload-area" @click.stop>
				<div class="upload-button-box">
					<el-button :loading="loading" :type="uploadButtonType" @click.stop="handleButtonClick">
						<i v-if="!loading" class="el-icon-upload"></i>
						{{ loading ? '导入中...' : buttonText }}
					</el-button>
					<div v-if="tip" class="tips">{{ tip }}</div>
				</div>
				<div v-if="showTemplateLink" class="tips">
					<el-link type="primary" @click="handleTemplateDownload">{{ downloadTip }}</el-link>
				</div>
			</div>
		</el-upload>

		<el-dialog :visible.sync="dialogVisible" title="导入结果" width="600px" append-to-body>
			<div class="import-result">
				<div v-if="importSuccess" class="success-info">
					<i class="el-icon-success"></i>
					<span>成功导入 {{ successCount }} 条数据</span>
				</div>
				<div v-else class="error-info">
					<i class="el-icon-error"></i>
					<span>导入失败</span>
					<div class="error-reason">{{ errorMessage }}</div>
				</div>
			</div>
			<span slot="footer" class="dialog-footer">
				<el-button @click="dialogVisible = false">关闭</el-button>
				<el-button v-if="!importSuccess" type="primary" @click="dialogVisible = false">
					确定
				</el-button>
			</span>
		</el-dialog>
	</div>
</template>

<script>
import * as XLSX from 'xlsx';

export default {
	name: 'ExcelImport',
	props: {
		// 自定义上传地址，一般不使用，我们使用手动处理上传
		action: {
			type: String,
			default: '#'
		},
		// 接受的文件类型
		accept: {
			type: String,
			default: '.xlsx, .xls'
		},
		// 是否显示已上传文件列表
		showFileList: {
			type: Boolean,
			default: false
		},
		// 按钮文字
		buttonText: {
			type: String,
			default: '导入Excel'
		},
		// 提示文字
		tip: {
			type: String,
			default: '请上传Excel文件'
		},
		// 下载提示文字
		downloadTip: {
			type: String,
			default: '下载模板'
		},
		// 上传按钮类型
		uploadButtonType: {
			type: String,
			default: 'primary'
		},
		// 是否显示模板下载链接
		showTemplateLink: {
			type: Boolean,
			default: false
		},
		// 模板下载链接
		templateUrl: {
			type: String,
			default: ''
		},
		// 数据校验函数，返回 { valid: boolean, message: string }
		validator: {
			type: Function,
			default: () => ({ valid: true, message: '' })
		},
		// 有无前置操作 无前置操作，直接打开  有前置操作调用before-import事件
		beforehand: {
			type: Boolean,
			default: false
		}
	},
	data() {
		return {
			loading: false,
			fileList: [],
			dialogVisible: false,
			importSuccess: false,
			successCount: 0,
			errorMessage: '',
			excelData: []
		};
	},
	methods: {
		// 处理按钮点击
		handleButtonClick() {
			//无前置操作 直接打开
			if (!this.beforehand) {
				this.openFileDialog();
			} else {
				this.fileList = [];

				// 有前置操作，手动调用打开文件选择对话框的方法
				this.$emit('before-import', this.openFileDialog);
			}
		},

		// 手动打开文件选择对话框
		openFileDialog() {
			// 通过引用获取el-upload组件并找到其内部的input元素，然后点击它
			const uploadComponent = this.$refs.upload;
			if (uploadComponent && uploadComponent.$el) {
				const inputElement = uploadComponent.$el.querySelector('input[type="file"]');
				if (inputElement) {
					inputElement.click();
				}
			}
		},

		// 处理文件变化
		async handleChange(file) {
			this.fileList = [file];
			if (file.raw) {
				// 直接处理上传，不需要再次调用beforeImport
				// 因为在打开文件选择对话框之前已经调用过了
				this.handleUpload(file);
			}
		},

		// 上传前的验证
		beforeUpload(file) {
			const isExcel = /\.(xlsx|xls|csv)$/.test(file.name.toLowerCase());
			if (!isExcel) {
				this.$message.error('仅支持上传Excel文件!');
				return false;
			}

			const isLt5M = file.size / 1024 / 1024 < 5;
			if (!isLt5M) {
				this.$message.error('文件大小不能超过5MB!');
				return false;
			}

			return false; // 阻止自动上传，我们手动处理
		},

		// 处理超出上传限制
		handleExceed() {
			this.$message.warning('只能上传一个文件!');
		},

		// 处理文件上传
		async handleUpload(file) {
			this.loading = true;
			try {
				const data = await this.readExcel(file.raw);
				const validationResult = this.validator(data);

				if (validationResult.valid) {
					this.importSuccess = true;
					this.successCount = data.length;
					this.$emit('on-success', data);
				} else {
					this.importSuccess = false;
					this.errorMessage = validationResult.message || '数据格式不正确';
					this.$emit('on-error', validationResult.message);
					this.fileList = [];
				}

				// this.dialogVisible = true;
			} catch (error) {
				this.importSuccess = false;
				this.errorMessage = error.message || '解析文件时出错';
				this.dialogVisible = true;
				this.$emit('on-error', this.errorMessage);
			} finally {
				this.loading = false;
			}
		},

		// 读取Excel文件
		readExcel(file) {
			return new Promise((resolve, reject) => {
				const reader = new FileReader();

				reader.onload = e => {
					try {
						const data = e.target.result;
						const workbook = XLSX.read(data, { type: 'array' });

						// 获取第一个工作表
						const firstSheetName = workbook.SheetNames[0];
						const worksheet = workbook.Sheets[firstSheetName];

						// 将工作表转换为JSON
						const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

						// 处理数据 - 第一行通常是表头
						const headers = jsonData[0];
						const result = [];

						// 从第二行开始处理数据
						for (let i = 1; i < jsonData.length; i++) {
							const row = jsonData[i];

							// 跳过空行
							if (row.length === 0 || row.every(cell => !cell)) continue;

							const rowData = {};
							for (let j = 0; j < headers.length; j++) {
								rowData[headers[j]] = row[j] !== undefined ? row[j] : '';
							}

							result.push(rowData);
						}

						resolve(result);
					} catch (error) {
						reject(new Error('解析Excel文件失败'));
					}
				};

				reader.onerror = () => {
					reject(new Error('读取文件失败'));
				};

				reader.readAsArrayBuffer(file);
			});
		},

		// 下载模板
		handleTemplateDownload() {
			if (this.templateUrl) {
				window.open(this.templateUrl);
			} else {
				this.$emit('download-template');
			}
		}
	}
};
</script>

<style scoped lang="scss">
::v-deep .el-upload {
	width: 100%;
}
.excel-import {
	.excel-upload {
		.upload-area {
			width: 100%;
			@include flexBox(space-between, center);
			.upload-button-box {
				@include flexBox(flex-start, center);
			}
		}
		.tips {
			margin-top: 8px;
			margin-left: 6px;
			font-size: 12px;
			color: #909399;
		}
	}

	.import-result {
		padding: 20px 0;
		text-align: center;

		.success-info {
			color: #67c23a;
			font-size: 16px;
			.el-icon-success {
				font-size: 24px;
				margin-right: 8px;
				vertical-align: middle;
			}
		}

		.error-info {
			color: #f56c6c;
			font-size: 16px;
			.el-icon-error {
				font-size: 24px;
				margin-right: 8px;
				vertical-align: middle;
			}

			.error-reason {
				margin-top: 16px;
				padding: 10px;
				background-color: #fff3f3;
				border-radius: 4px;
				text-align: left;
				font-size: 14px;
				color: #666;
			}
		}
	}
}
</style>
