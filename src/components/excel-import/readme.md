# Excel 导入组件

一个便捷的 Excel 文件导入组件，支持文件验证、数据校验和结果反馈。

## 功能特点

- 支持 .xlsx、.xls 格式文件上传
- 自动解析 Excel 文件为结构化数据
- 提供数据校验接口
- 导入结果反馈
- 支持模板下载

## 安装依赖

确保项目中安装了 xlsx 库：

```bash
npm install xlsx --save
```

## 基本用法

```vue
<template>
	<div>
		<excel-import
			:validator="validateExcelData"
			:show-template-link="true"
			@on-success="handleImportSuccess"
			@on-error="handleImportError"
			@download-template="downloadTemplate"
		/>
	</div>
</template>

<script>
import ExcelImport from '@/components/excel-import';
import { exportTemplate } from '@/utils/excel';

export default {
	components: {
		ExcelImport
	},
	methods: {
		// 数据校验函数
		validateExcelData(data) {
			if (!data || data.length === 0) {
				return { valid: false, message: '导入的数据为空' };
			}

			// 检查必填字段
			for (let i = 0; i < data.length; i++) {
				const row = data[i];
				if (!row['名称'] || !row['数量']) {
					return {
						valid: false,
						message: `第${i + 2}行: "名称"和"数量"为必填字段`
					};
				}
			}

			return { valid: true };
		},

		// 导入成功处理
		handleImportSuccess(data) {
			console.log('导入数据', data);
			this.$message.success(`成功导入${data.length}条数据`);
		},

		// 导入失败处理
		handleImportError(message) {
			this.$message.error(message);
		},

		// 下载模板
		downloadTemplate() {
			const headers = ['名称', '数量', '单价', '备注'];
			exportTemplate([headers], 'Sheet1', '导入模板.xlsx');
		}
	}
};
</script>
```

## 属性

| 属性名           | 类型     | 默认值                               | 说明                                             |
| ---------------- | -------- | ------------------------------------ | ------------------------------------------------ |
| action           | String   | '#'                                  | 上 Boolea 传地址，一般不使用（组件内部处理上传） |
| beforehand       | Boolean  | false                                | 有无前置操作                                     |
| accept           | String   | '.xlsx, .xls'                        | 接受的文件类型                                   |
| showFileList     | Boolean  | false                                | 是否显示已上传文件列表                           |
| buttonText       | String   | '导入 Excel'                         | 上传按钮文字                                     |
| tip              | String   | '请上传 Excel 文件'                  | 提示文字                                         |
| uploadButtonType | String   | 'primary'                            | 按钮类型                                         |
| showTemplateLink | Boolean  | false                                | 是否显示模板下载链接                             |
| templateUrl      | String   | ''                                   | 模板下载链接，为空时触发 download-template 事件  |
| validator        | Function | () => ({ valid: true, message: '' }) | 数据校验函数                                     |

## 事件

| 事件名            | 参数    | 说明                                                                                                   |
| ----------------- | ------- | ------------------------------------------------------------------------------------------------------ |
| on-success        | data    | 导入成功时触发，参数为解析后的数据                                                                     |
| before-import     | -       | 前置操作事件，在打开文件选择器弹窗前的函数，openFileDialog 回调函数打开弹窗，结合 beforehand=true 使用 |
| on-error          | message | 导入失败时触发，参数为错误信息                                                                         |
| download-template | -       | 点击下载模板链接且未设置 templateUrl 时触发                                                            |

## 数据校验

验证函数接收解析后的数据，返回一个对象：

- valid: Boolean - 是否验证通过
- message: String - 验证失败时的错误信息

```javascript
function validator(data) {
  // 验证逻辑
  if (/* 验证失败条件 */) {
    return { valid: false, message: '错误信息' };
  }
  return { valid: true };
}
```

## 数据格式

Excel 文件的第一行会被解析为表头，解析后的数据格式为：

```javascript
[
	{ 姓名: '张三', 年龄: 25, 性别: '男' },
	{ 姓名: '李四', 年龄: 30, 性别: '女' }
	// ...
];
```

## 辅助工具

使用 `/src/utils/excel.js` 中提供的工具函数可以方便地创建导出模板和处理数据导出：

```javascript
import { exportTemplate, exportData } from '@/utils/excel';

// 导出模板 - 正确格式是一维数组
const headers = ['姓名', '年龄', '性别', '手机号'];
exportTemplate(headers, 'Sheet1', '模板.xlsx');

// 如果需要多行表头，可以使用二维数组
const complexHeaders = [
	['姓名', '年龄', '联系方式', '联系方式'],
	['', '', '电话', '邮箱']
];
exportTemplate(complexHeaders, 'Sheet1', '复杂表头模板.xlsx');

// 导出数据
const data = [
	{ 姓名: '张三', 年龄: 25, 性别: '男' },
	{ 姓名: '李四', 年龄: 30, 性别: '女' }
];
exportData(data, headers, 'Sheet1', '导出数据.xlsx');
```

## 注意事项

1. Excel 文件大小限制为 5MB
2. 支持.xlsx、.xls 格式文件
3. 空行会被自动跳过
4. 使用前需安装 xlsx 库
5. 导入大文件可能会导致性能问题，请合理控制数据量
