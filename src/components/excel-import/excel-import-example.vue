<template>
	<div class="excel-import-demo">
		<excel-import
			:validator="validateExcelData"
			:show-template-link="true"
			@on-success="handleImportSuccess"
			@on-error="handleImportError"
			@download-template="downloadTemplate"
		/>
	</div>
</template>

<script>
import ExcelImport from '@/components/excel-import/index.vue';
import { exportTemplate } from '@/utils/excel.js';

export default {
	components: {
		ExcelImport
	},
	methods: {
		// 验证导入的数据
		validateExcelData(data) {
			if (!data || data.length === 0) {
				return { valid: false, message: '导入的数据为空' };
			}

			// 检查必填字段
			for (let i = 0; i < data.length; i++) {
				const row = data[i];

				// 假设"名称"和"数量"是必填字段
				if (!row['名称'] || !row['数量']) {
					return {
						valid: false,
						message: `第${i + 2}行: "名称"和"数量"为必填字段`
					};
				}

				// 验证数量是否为数字
				if (isNaN(Number(row['数量']))) {
					return {
						valid: false,
						message: `第${i + 2}行: "数量"必须为数字`
					};
				}
			}

			return { valid: true };
		},

		// 处理导入成功
		handleImportSuccess(data) {
			console.log('导入成功', data);
			this.$message.success(`成功导入${data.length}条数据`);
			// 通常这里会调用API将数据保存到后端
		},

		// 处理导入失败
		handleImportError(message) {
			console.error('导入失败', message);
			this.$message.error(message);
		},

		// 下载模板 - 修复格式问题
		downloadTemplate() {
			// 这里直接传递一维数组，不需要嵌套数组
			const headers = ['名称', '数量', '单价', '备注'];
			exportTemplate(headers, 'Sheet1', '导入模板.xlsx');
		}
	}
};
</script>
