<!--因为tabs组件不好调整样式，自定义了tabs组件-->
<template>
	<div id="custom-tabs" class="tabs">
		<div
			v-for="(item, index) of tabs"
			:id="'tab-' + index"
			:key="index"
			class="tabs-tab"
			:class="{ select: value === item.value }"
			:style="{ marginLeft: index === 0 ? '0' : '24px' }"
			@click="handleClick(index, item.value)"
		>
			{{ item.label }}
		</div>
		<div
			:style="{ width: lineWidth + 'px', transform: `translateX(${lineLeft}px)` }"
			class="bottom-line"
		></div>
		<svg-icon icon-class="ellips" class="more-icon"></svg-icon>
	</div>
</template>

<script>
export default {
	name: 'Index',
	props: {
		tabs: {
			type: Array,
			default: () => {
				return [];
			}
		},
		active: {
			type: String,
			default: () => {
				return '';
			}
		}
	},
	data() {
		return {
			value: '',
			// 自定义tabs下划线
			lineWidth: 0,
			lineLeft: 0
		};
	},
	watch: {
		tabs: {
			deep: true,
			handler: function (newVal) {
				this.handleClick(0, this.active);
			}
		}
	},
	mounted() {
		this.handleClick(0, this.active);
	},
	methods: {
		handleClick(index, val) {
			let el = document.getElementById(`tab-${index}`);
			this.lineWidth = el.clientWidth;
			this.lineLeft = el.offsetLeft;
			this.value = val;
			this.$emit('handleClick', val);
		}
	}
};
</script>

<style scoped lang="scss">
.tabs {
	padding-bottom: 12px;
	@include flexBox(flex-start);
	position: relative;
	border-bottom: 1px solid #f0f0f0;
	&-tab {
		font-size: 14px;
		font-weight: 400;
		color: $textColor;
		line-height: 22px;
		cursor: pointer;
	}
	.select {
		color: var(--brand-6);
	}
	.more-icon {
		width: 16px;
		height: 16px;
		margin-left: 21px;
		cursor: pointer;
	}
	.bottom-line {
		width: 20px;
		height: 3px;
		background: var(--brand-6);
		border-radius: 9px;
		position: absolute;
		bottom: 0;
		left: 0;
		transition: all 0.3s;
	}
}
</style>
