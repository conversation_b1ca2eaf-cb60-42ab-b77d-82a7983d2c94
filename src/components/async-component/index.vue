<!--
 * @Description: 异步加载网络UMD组件
 * @Version: 1.0
 * @Autor: zhaodongming
 * @Date: 2022-12-01 16:32:36
 * @LastEditors: zhaodongming
 * @LastEditTime: 2022-12-02 19:26:00
-->
<template>
	<component :is="comp" v-bind="$attrs" v-on="$listeners"></component>
</template>

<script>
import { importScript } from 'runtime-import';
import { serveUrl } from '@/config';
export default {
	name: 'AsyncComponent',
	inheritAttrs: true,
	props: {
		umdUrl: {
			type: String,
			default: ''
		}
	},
	data() {
		return {
			comp: 'div'
		};
	},
	watch: {
		umdUrl: {
			immediate: true,
			handler: function (newVal, oldVal) {
				this.handleImport(newVal);
			}
		}
	},
	methods: {
		async handleImport(url) {
			try {
				url = url.startsWith('http') ? url : serveUrl + url;
				// 获取缓存中的组件
				const catchComponent = window[url];
				if (catchComponent) {
					// 渲染缓存中的组件
					this.comp = catchComponent;
				} else {
					// 加载远端umd格式组件内容
					const comp = await importScript(url);
					// 渲染远端组件并缓存到磁盘
					this.comp = comp;
					window[url] = comp;
				}
			} catch (err) {
				console.error(err);
			}
		}
	}
};
</script>

<style lang="scss" scoped></style>
