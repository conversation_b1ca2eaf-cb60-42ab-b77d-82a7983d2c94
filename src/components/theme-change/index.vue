<!--
 * @Description: 主题切换
 * @Version: 1.0
 * @Autor: zhaodongming
 * @Date: 2022-08-17 11:45:03
 * @LastEditors: zhaodongming
 * @LastEditTime: 2023-07-06 16:52:54
-->
<template>
	<div class="theme">
		<div
			v-for="(val, index) in model"
			:key="index"
			:class="{
				'theme-list': true,
				light: val === 'light',
				dark: val === 'dark',
				'theme-action': themeAction === val
			}"
			@click="handlerThemeChange(val, index)"
		>
			<div class="theme-left"></div>
			<div class="theme-top"></div>
			<div class="theme-section el-icon-check"></div>
		</div>
	</div>
</template>

<script>
// 获取element版本号,便于拉去对应版本的主题文件
// 默认主题颜色
import { changeMode, handleGetDefaultColor } from '@/utils/color.js';

export default {
	name: 'ThemeChange',
	props: {},
	data() {
		return {
			model: ['light', 'dark'],
			themeAction: 'light',
			defaultColorName: []
		};
	},
	mounted() {
		this.defaultColorName = handleGetDefaultColor().map(color => {
			return color.key;
		});
	},
	methods: {
		handlerThemeChange(mode, index) {
			this.themeAction = mode;
			// console.log(document.documentElement.getAttribute('theme-color'));
			// const isIndexColor = document.documentElement.getAttribute('theme-color');
			// if (this.defaultColorName.includes(isIndexColor)) {
			//属于内置色系，使用内置theme变量即可
			// 切换主题模式light、dark
			// 	document.documentElement.setAttribute('theme-mode', mode);
			// } else {
			// 自定义颜色
			// 调用主题切换方法
			changeMode(mode);
			// }
		}
	}
};
</script>

<style lang="scss" scoped>
.theme {
	display: flex;
	&-list {
		width: 48px;
		height: 36px;
		position: relative;
		box-shadow: 0 1px 2.5px 0 rgb(0 0 0 / 18%);
		cursor: pointer;
		border-radius: 4px;
		overflow: hidden;
		margin-right: 10px;
	}
	&-left {
		width: 33%;
		height: 100%;
		position: absolute;
		top: 0;
		left: 0;
		background-color: #fff;
	}
	&-top {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 25%;
		background-color: #fff;
	}
	.light {
		background-color: #f0f2f5;
		.theme-section {
			color: #000;
		}
	}
	.dark {
		background-color: rgba(0, 21, 41, 0.85);
		.theme-left {
			background-color: rgba(0, 21, 41, 0.65);
		}
		.theme-top {
			background-color: rgba(0, 21, 41, 0.85);
		}
		.theme-section {
			color: #fff;
		}
	}
	&-section {
		font-size: 24px;
		position: absolute;
		left: 50%;
		top: 50%;
		margin-top: -10px;
		margin-left: -4px;
		display: none;
	}
	&-action {
		.theme-section {
			display: block;
		}
	}
}
</style>
