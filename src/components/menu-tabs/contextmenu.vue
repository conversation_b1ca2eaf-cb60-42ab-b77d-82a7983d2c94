<template>
	<div
		class="context"
		:style="{ top: (config.pageY || 0) + 'px', left: (config.pageX || 0) + 'px' }"
	>
		<div
			v-for="(item, index) of list"
			:key="index"
			class="context-item"
			@click="handleButton(item.value)"
		>
			{{ item.name }}
		</div>
	</div>
</template>

<script>
export default {
	name: 'Contextmenu',
	props: {
		config: {
			type: Object,
			default: () => {
				return {};
			}
		}
	},
	data() {
		return {
			list: [
				{ name: '关闭当前标签', value: 'current' },
				{ name: '关闭其他标签', value: 'other' },
				{ name: '关闭所有标签', value: 'all' },
				{ name: '关闭左侧标签', value: 'left' },
				{ name: '关闭右侧标签', value: 'right' },
				{ name: '刷新当前页', value: 'update' }
			]
		};
	},
	methods: {
		handleButton(type) {
			this.$emit('close', type);
		}
	}
};
</script>

<style scoped lang="scss">
.context {
	position: fixed;
	z-index: 888;
	background: #ffffff;
	border-radius: 6px;
	padding: 0 8px;
	width: 110px;
	box-shadow: 0px 3px 4px 0px rgba(0, 0, 0, 0.1);
	&-item {
		margin: 10px 0;
		font-size: 12px;
		color: $textColor;
		cursor: pointer;
		text-align: center;
		&:hover {
			color: var(--brand-6);
		}
	}
}
</style>
