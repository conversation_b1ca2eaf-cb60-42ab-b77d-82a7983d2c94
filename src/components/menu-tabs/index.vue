<template>
	<div
		v-if="show && computedTabs.length > 0"
		ref="pageMenus"
		class="page-tabs"
		:class="{ isOver: isOver }"
	>
		<svg-icon
			v-show="isOver"
			class="handle-pre"
			icon-class="seal-left"
			@click="scrollTo('pre')"
		></svg-icon>
		<svg-icon
			v-show="isOver"
			class="handle-next"
			icon-class="next"
			@click="scrollTo('next')"
		></svg-icon>
		<div ref="tabsScroll" class="tabs">
			<div
				v-for="(item, index) in computedTabs"
				:ref="item.meta.title + index"
				:key="item.fullPath"
				class="tabs-item"
				:class="item.meta.title + index === currentTab ? 'select' : 'normal'"
				@click="selectTab(item.meta.title + index)"
				@mouseenter="hoverKey = item.meta.title + index"
				@mouseleave="hoverKey = ''"
				@contextmenu.stop.prevent="contextmenu($event, item.meta.title + index)"
			>
				<div>{{ item.meta.title }}</div>
				<i
					v-show="[currentTab, hoverKey].includes(item.meta.title + index)"
					class="coos-iconfont icon icon-guanbi1"
					@click.stop="del(item)"
				></i>
			</div>
		</div>
		<contextmenu v-show="showMenu" :config="config" @close="close"></contextmenu>
	</div>
</template>

<script>
import { mapMutations, mapGetters } from 'vuex';
import { deepClone } from '@/utils';
import contextmenu from '@/components/menu-tabs/contextmenu';
import { debounce } from 'lodash';
import { CoosEventTypes } from '@/utils/bus';
import { getDictionary } from '@/utils/data-dictionary';
export default {
	name: 'Index',
	components: {
		contextmenu
	},
	data() {
		return {
			hoverKey: '',
			currentTab: '',
			showMenu: false,
			isOver: false,
			config: {} // 右键菜单位置信息
		};
	},
	computed: {
		...mapGetters(['menuTabs', 'sidebarWidth', 'currentLevelMenu', 'clientSystemMode']),
		show() {
			return this.menuTabs.length !== 1 || this.menuTabs[0].fullPath !== '/wile-fire';
		},
		computedTabs: {
			get: function () {
				return this.menuTabs;
			},
			set: function (val) {
				this.SET_MENU_TABS(val);
			}
		}
	},
	watch: {
		computedTabs() {
			this.getIndex(); // 外部系统打开，路由没有发生变化，但是iframe地址变了
			this.reGetIsCover();
		},
		// 路由变化就要重新匹配索引项
		$route(newVal) {
			this.getIndex();
		},
		currentTab(newVal) {
			// 因为第三种框架，左边不一定有菜单，所以等布局动画完成
			this.$nextTick(() => {
				this.$refs[newVal][0].scrollIntoView({ behavior: 'smooth' });
			}, 500);
		}
	},
	mounted() {
		this.reGetIsCover();
		this.getIndex();
		document.addEventListener('click', this.closeMenu);
		window.addEventListener('resize', this.reGetIsCover);
	},
	destroyed() {
		window.removeEventListener('resize', this.reGetIsCover);
		document.removeEventListener('click', this.closeMenu);
	},
	methods: {
		...mapMutations('user', ['SET_MENU_TABS']),
		...mapMutations('settings', ['SET_CURRENT_LEVEL_MENU']),
		/**重新计算是否超过*/
		reGetIsCover: debounce(
			function () {
				this.isOver =
					this.$refs.tabsScroll &&
					this.$refs.tabsScroll.clientWidth >= this.$refs.pageMenus.clientWidth - 50;
			},
			500,
			false
		),
		/**关闭菜单项*/
		closeMenu() {
			this.showMenu = false;
		},
		/**右键操作项*/
		close(type) {
			let current, currentIndex, other, path;
			switch (type) {
				case 'current':
					other = this.menuTabs.filter((item, index) => {
						let title = item.meta.title;
						return this.currentTab !== title + index;
					});
					if (other.length === 0) {
						this.resetMenuTabs();
					} else {
						this.computedTabs = other;
					}
					path = this.computedTabs[this.computedTabs.length - 1].fullPath;
					this.$router.replace(path);
					break;
				case 'other':
					current = this.menuTabs.filter((item, index) => {
						let title = item.meta.title;
						return this.currentTab === title + index;
					});
					this.computedTabs = current;
					break;
				case 'left':
					currentIndex = this.menuTabs.findIndex((item, index) => {
						let title = item.meta.title;
						return this.currentTab === title + index;
					});
					current = this.menuTabs.filter((item, index) => {
						return index >= currentIndex;
					});
					this.computedTabs = current;
					break;
				case 'right':
					currentIndex = this.menuTabs.findIndex((item, index) => {
						let title = item.meta.title;
						return this.currentTab === title + index;
					});
					current = this.menuTabs.filter((item, index) => {
						return index <= currentIndex;
					});
					this.computedTabs = current;
					break;
				case 'all':
					this.resetMenuTabs();
					if (this.$route.fullPath !== '/wile-fire') {
						this.$router.replace('/wile-fire');
					}
					break;
				case 'update':
					this._BUS.$emit(CoosEventTypes.updateCurrentPage);
					break;
			}
		},
		/**重置面包屑*/
		resetMenuTabs() {
			this.computedTabs = [
				{
					fullPath: '/wile-fire',
					meta: {
						appId: getDictionary('应用ID/消息'),
						icon: 'im',
						isMenu: true,
						isMenuTab: true,
						query: {},
						title: '消息'
					}
				}
			];
		},
		/**右键打开操作栏的位置*/
		contextmenu(e, id) {
			if (id === this.currentTab) {
				let innerWidth = window.innerWidth;
				let { pageX, pageY } = e;
				// 防止右键操作视图溢出屏幕
				this.config = { pageX: pageX > innerWidth / 2 ? pageX - 100 : pageX, pageY: pageY };
				this.showMenu = true;
			}
		},
		/**获取当前面包屑所在的索引*/
		getIndex() {
			this.menuTabs.forEach((item, index) => {
				if (item.fullPath === this.$route.fullPath) {
					this.currentTab = item.meta.title + index;
					// 第三种框架模式，根据变动的面包屑所在一级菜单索引，动态改变一级菜单的索引
					if (
						(item.levelIndex || item.levelIndex === 0) &&
						item.levelIndex !== this.currentLevelMenu &&
						this.clientSystemMode === '3'
					) {
						this.SET_CURRENT_LEVEL_MENU(item.levelIndex);
					}
				}
			});
		},
		/**点击标签*/
		selectTab(key) {
			this.currentTab = key;
			let obj = this.menuTabs.find((tab, index) => {
				let title = tab.meta.title;
				return key === title + index;
			});
			this.$router.replace(obj.fullPath);
		},
		/**删除标签页*/
		del(item) {
			let newArr = deepClone(this.menuTabs).filter(route => {
				return route.fullPath !== item.fullPath;
			});
			if (newArr.length === 0) {
				this.resetMenuTabs();
			} else {
				this.computedTabs = newArr;
			}
			// 如果是当前路由被删除了，就跳转到最新的路由
			if (item.fullPath === this.$route.fullPath) {
				let path = this.computedTabs[this.computedTabs.length - 1].fullPath;
				this.$router.replace(path);
			}
			// 滚动到最后
			// this.$refs.tabsScroll.scrollTo({ left: this.$refs.tabsScroll.clientWidth });
		},
		scrollTo(type) {
			let left = type === 'next' ? 320 : -320;
			this.$refs.tabsScroll.scrollBy({ left, behavior: 'smooth' });
		}
	}
};
</script>

<style scoped lang="scss">
.page-tabs {
	width: 100%;
	padding: 0;
	margin-bottom: 10px;
	position: relative;
}
.page-tabs.isOver {
	padding: 0 24px;
}

.tabs {
	display: inline-flex;
	align-items: center;
	overflow-x: auto;
	max-width: 100%;
	scrollbar-width: none;
	&::-webkit-scrollbar {
		height: 0;
	}
	&-item {
		flex-shrink: 0;
		border-radius: 6px;
		padding: 4px 22px;
		font-weight: 400;
		font-size: 14px;
		line-height: 22px;
		margin-right: 8px;
		cursor: pointer;
		display: flex;
		align-items: center;
		position: relative;
		.icon {
			position: absolute;
			right: 7px;
			top: 5px;
			font-size: 8px;
			margin-left: 12px;
			color: #ffffff;
		}
	}
	.normal {
		border: 1px solid #ffffff;
		background: #ffffff;
		color: $primaryTextColor;
		&:hover {
			background: var(--brand-6);
			color: #ffffff;
		}
	}
	.select {
		background: #ffffff;
		color: var(--brand-6);
		border: 1px solid var(--brand-6);
		.icon {
			color: var(--brand-6);
		}
	}
}
.handle-pre {
	position: absolute;
	left: 0;
	width: 30px;
	height: 30px;
	top: 6px;
	cursor: pointer;
	display: none;
}
.handle-next {
	position: absolute;
	right: 0;
	width: 30px;
	height: 30px;
	top: 6px;
	cursor: pointer;
	display: none;
}
.isOver {
	.handle-pre,
	.handle-next {
		display: block;
	}
}
</style>
