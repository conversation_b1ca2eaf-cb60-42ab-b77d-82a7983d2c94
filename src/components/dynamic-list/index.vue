<template>
	<div :key="tableHead.length" class="dynamic-list">
		<el-table
			ref="dynamicTable"
			v-loading="loading"
			class="desk-el-table"
			:row-style="getRowStyle"
			:cell-style="{ paddingLeft: '10px' }"
			:header-row-style="headerRowStyle"
			:header-cell-style="headerCellStyle"
			:data="tableData"
			style="width: 100%"
			height="100%"
		>
			<el-table-column
				v-for="(t, i) in tableHead"
				:key="i"
				align="left"
				:label="t.desc"
				:width="t.width"
				:fixed="t.btns && t.btns.length > 0 ? 'right' : ''"
				show-overflow-tooltip
			>
				<template slot-scope="{ row }">
					<slot v-if="t.isSlot" name="row" :row="row"></slot>
					<FormTableRender v-else :row="row" :ele-data="t">
						<div v-if="t.btns && t.btns.length > 0" class="buttons">
							<div
								v-for="(item, index) of t.btns"
								:key="index"
								class="buttons-item"
								:class="{ delete: item.type === 'delete' }"
								@click="openPopup(item.type, row.id)"
							>
								{{ item.name }}
							</div>
						</div>
					</FormTableRender>
				</template>
			</el-table-column>
			<el-table-column
				v-if="tableHead.length && tableHeadStatus"
				label="状态"
				align="center"
				prop="statusName"
				width="100px"
				fixed="right"
			></el-table-column>
			<el-table-column
				v-if="tableHead.length && tableHeadBtns"
				fixed="right"
				label="操作"
				width="120px"
				align="center"
			>
				<template slot-scope="scope">
					<div class="handler">
						<div
							v-if="canEdit.includes(scope.row.status)"
							class="edit"
							@click="openPopup('edit', scope.row)"
						>
							编辑
						</div>
						<div class="detail" @click="openPopup('detail', scope.row)">详情</div>
						<el-dropdown
							v-if="canRevoke.includes(scope.row.status) || canDelect.includes(scope.row.status)"
							@command="openPopup($event, scope.row.id)"
						>
							<div class="dynamic-editor">
								<i class="el-icon-more dynamic-editor-icon"></i>
							</div>
							<el-dropdown-menu slot="dropdown" class="dynamic-editor-list">
								<el-dropdown-item
									v-if="canRevoke.includes(scope.row.status)"
									class="dynamic-editor-list-btn"
									command="revoke"
								>
									撤回
								</el-dropdown-item>
								<el-dropdown-item
									v-if="canDelect.includes(scope.row.status)"
									class="dynamic-editor-list-btn"
									command="delete"
								>
									删除
								</el-dropdown-item>
							</el-dropdown-menu>
						</el-dropdown>
					</div>
				</template>
			</el-table-column>
			<div slot="empty">
				<el-empty description="暂无数据"></el-empty>
			</div>
		</el-table>
	</div>
</template>

<script>
import { assetsUrl } from '@/config';
import FormTableRender from '@/components/form-table-render';

export default {
	name: 'DynamicList',
	components: {
		FormTableRender
	},
	props: {
		tableData: {
			type: Array,
			default: () => {
				return [];
			}
		},
		tableHead: {
			type: Array,
			default: () => {
				return [];
			}
		},
		loading: {
			type: Boolean,
			default: () => {
				return false;
			}
		},
		tableHeadBtns: {
			type: Boolean,
			default: () => {
				return true;
			}
		},
		tableHeadStatus: {
			type: Boolean,
			default: () => {
				return true;
			}
		}
	},
	data() {
		return {
			emptyImg: assetsUrl + '/desk-app/empty.png',
			/**每一行的样式*/
			headerRowStyle: {
				height: '48px',
				background: '#EDF2F6',
				borderRadius: '6px'
			},
			/**每一格样式*/
			headerCellStyle: {
				paddingLeft: '10px',
				background: 'transparent',
				fontSize: '14px',
				fontWeight: 500,
				color: '#2f446b',
				lineHeight: '22px'
			},
			canEdit: [0, 3, 5],
			canDelect: [0, 5, 6],
			// canDelect: [],
			canRevoke: [1, 4]
		};
	},
	created() {},
	methods: {
		layout() {
			this.$refs.dynamicTable.doLayout();
		},
		arrDataSplit(data, field) {
			const arrString = data[field] ? String(data[field]) : '';
			if (arrString) {
				return arrString.split(',');
			}
			return [];
		},
		/**获取详情*/
		openPopup(type, row) {
			this.$emit('openPopup', type, row);
		},
		/**获取表格奇偶列的样式*/
		getRowStyle({ row, rowIndex }) {
			return {
				height: '56px',
				borderRadius: '6px',
				background: rowIndex % 2 === 0 ? '#ffffff' : ' #f5f7fa'
			};
		}
	}
};
</script>

<style lang="scss" scoped>
.dynamic-list {
	width: 100%;
	height: 100%;
}
.dynamic-editor {
	display: flex;
	align-items: center;
	font-family: PingFang SC, PingFang SC;
	font-weight: 400;
	color: $primaryTextColor;
	cursor: pointer;
	&-icon {
		transform: rotate(90deg);
		width: 15px;
		height: 15px;
		font-size: 12px;
		text-align: center;
	}
	&-list {
		padding: 12px 11px;
		&-btn {
			border-radius: 4px;
		}
	}
}
.handler {
	display: flex;
	align-items: center;
	font-size: 14px;
	font-weight: 400;
	color: var(--brand-6);
	justify-content: center;
	.edit {
		margin-right: 12px;
		cursor: pointer;
	}
	.detail {
		cursor: pointer;
	}
}
.buttons {
	display: flex;
	align-items: center;
	&-item {
		cursor: pointer;
		font-weight: 400;
		font-size: 14px;
		color: var(--brand-6);
		line-height: 22px;
		margin-right: 20px;
	}
	.delete {
		color: #ff4d4f;
	}
}
</style>
