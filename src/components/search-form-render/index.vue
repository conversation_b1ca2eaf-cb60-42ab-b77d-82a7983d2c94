<!--
 * @Description: 动态表单搜索渲染器
 * @Version: 1.0
 * @Autor: FH.hao
 * @Date: 2024-06-04 17:41:37
-->
<template>
	<div
		v-if="newFormJson.widgetList && newFormJson.widgetList.length > 0"
		v-handleSearch="handleSearchParams"
		:class="{ formInfo: isSearchViews, isEditeView: !isSearchViews }"
	>
		<v-form-render
			ref="searchVFormRef"
			class="formRender el-form--inline"
			:form-json="newFormJson"
			:base-url="config.baseUrl"
		></v-form-render>
		<!-- 搜索组件时展示 -->
		<div
			v-if="isSearchViews"
			class="form_button"
			:style="{ flexDirection: searchStatus ? 'column' : 'row' }"
		>
			<el-button
				class="butSearch"
				:style="
					searchStatus ? 'margin-bottom:8px;margin-right:0' : 'margin-bottom:0;margin-right:8px'
				"
				type="default"
				@click="onSearch"
			>
				查询
			</el-button>
			<el-button class="butReset" type="default" @click="onReset">重置</el-button>
		</div>
	</div>
</template>

<script>
import config from '@/config';

export default {
	name: 'SearchFormRender',
	components: {},
	props: {
		// 渲染数据
		formJson: {
			type: Object,
			default: () => ({})
		},
		// 如果需要当作搜索组件使用需要需要设为为true 如果是true 就有搜索和重置按钮
		isSearchViews: {
			type: Boolean,
			default: false
		},
		minHeight: {
			type: [Number, String],
			default: 66
		},
		maxHeight: {
			type: [Number, String],
			default: 144
		}
	},
	data() {
		return {
			searchStatus: false,
			newFormJson: {},
			isShowOpen: false,
			config
		};
	},
	computed: {
		handleSearchParams() {
			return {
				maxHeight: this.maxHeight,
				minHeight: this.minHeight,
				changeStatus: this.changeSearch,
				caleText: '收起',
				openText: '展开',
				initStatus: this.searchStatus,
				// 是否能够收起展开
				isSearchViews: this.isShowOpen
			};
		}
	},
	watch: {
		// formJson: {
		// 	deep: true,
		// 	handler: function (newVal) {
		// 		if (newVal.widgetList) {
		// 			this.searchFiledInit(newVal);
		// 		} else {
		// 			this.searchFiledInit({ widgetList: [] });
		// 		}
		// 	}
		// }
	},
	created() {},
	methods: {
		changeSearch(status) {
			this.searchStatus = status;
		},
		// 重置搜索条件
		onReset() {
			// 通知列表
			this.$refs.searchVFormRef.resetForm();
			// 情况表单
			this.$emit('resetClick');
		},
		onSearch() {
			this.$refs.searchVFormRef.getFormData().then(res => {
				const resultData = [];
				if (Object.keys(res) && Object.keys(res).length > 0) {
					Object.keys(res).forEach(list => {
						this.formJson.widgetList.forEach(item => {
							const options = item.options || item.field.options;
							if (list == options.name) {
								const listData = {
									field: list,
									value: res[list],
									source: options.source
								};
								// 判断queryType
								// if (
								// 	item.field.type == 'number' ||
								// 	item.field.type == 'radio' ||
								// 	item.field.type == 'time' ||
								// 	item.field.type == 'date' ||
								// 	item.field.type == 'switch' ||
								// 	item.field.type == 'rate' ||
								// 	item.field.type == 'color' ||
								// 	item.field.type == 'slider'
								// ) {
								// 	listData.queryType = '1';
								// } else if (item.field.type == 'input' || item.field.type == 'textarea') {
								// 	listData.queryType = '2';
								// } else if (item.field.type == 'checkbox' || item.field.type == 'select') {
								// 	listData.queryType = '3';
								// } else {
								// 	// 暂未开放
								// 	listData.queryType = '4';
								// }
								listData.queryType = this.queryType(item.oldType);
								resultData.push(listData);
							}
						});
					});
				}
				this.$emit('searchClick', JSON.stringify(resultData));
			});
		},
		queryType(type) {
			const eleType = {
				input: 2,
				textarea: 2,
				number: 1,
				radio: 3,
				checkbox: 3,
				select: 3,
				time: 4,
				'time-range': 5,
				date: 4,
				'date-range': 5,
				slider: 3,
				rate: 1,
				switch: 1
			};
			return eleType[type] ? eleType[type] : 2;
		},
		searchFiledInit(newVal) {
			const newValue = { ...newVal };
			const initWidgetList = [];
			newValue.widgetList.forEach(list => {
				// 多行输入->input
				// 单选项->select
				// 多选选->select
				// 开关->select
				// 评分->number
				// console.log('list:', list);
				list.oldType = list.type;
				if (list.options.label.indexOf(':') === -1) {
					list.options.label = list.options.label + ':';
				}
				if (list.field?.options) {
					list.field.options.required = false;
					list.field.options.validation = '';
				}
				if (list.options) {
					list.options.required = false;
					list.options.validation = '';
				}
				if (
					list.type == 'textarea' ||
					list.type == 'color' ||
					list.type == 'picture-upload' ||
					list.type == 'file-upload' ||
					list.type == 'rich-editor' ||
					list.type == 'cascader'
				) {
					list.type = 'input';
				} else if (list.type == 'radio' || list.type == 'checkbox') {
					list.type = 'select';
					if (list.type == 'checkbox') {
						list.field.options.multiple = true;
						console.log('checkbox:', list);
					}
				} else if (list.type == 'rate' || list.type == 'slider') {
					list.type = 'number';
				} else if (list.type == 'switch') {
					if (list.options) {
						list.options.optionItems = [
							{
								label: list.options.activeText ? list.options.activeText : '是',
								value: true
							},
							{
								label: list.options.inactiveText ? list.options.inactiveText : '否',
								value: false
							}
						];
					} else if (list.field.options) {
						list.options = { optionItems: [] };
						const options = list.field.options;
						list.options.optionItems = [
							{
								label: options.activeText ? options.activeText : '是',
								value: true
							},
							{
								label: options.inactiveText ? options.inactiveText : '否',
								value: false
							}
						];
					}

					list.type = 'select';
				} else if (list.type == 'date') {
					// 日期为搜索条件时,应该修改为日期范围
					if (list.field) {
						list.field.type = 'date-range';
						list.field.options.type = 'daterange';
					} else {
						list.type = 'date-range';
						list.options.type = 'daterange';
					}
				} else if (list.type == 'time') {
					// 时间为搜索条件时,应该修改为时间范围
					if (list.field) {
						list.field.type = 'time-range';
						list.field.options.type = 'timerange';
					} else {
						list.type = 'time-range';
						list.options.type = 'timerange';
					}
				} else if (list.type == 'orgPersonnel') {
					if (list.field) {
						list.field.type = 'input';
						list.field.options.type = 'input';
					} else {
						list.type = 'input';
						list.options.type = 'input';
					}
				} else if (list.type == 'number') {
					if (list.field) {
						list.field.options.defaultValue = undefined;
					}
				}
				// 给select多选情况下加collapse-tags属性，防止选项多了，会挤压组件换行
				if (list.type == 'select' && list?.field?.options?.multiple) {
					list.field.options.collapseTags = true;
				}
				// 兼容普通表单组件和高级组件的数据格式兼容
				if (list.field) {
					initWidgetList.push(list.field);
				} else {
					initWidgetList.push(list);
				}
			});
			if (initWidgetList.length > 4) {
				this.isShowOpen = true;
			}
			const newFormJson = {
				formConfig: newValue.formConfig ? newValue.formConfig : {},
				widgetList: initWidgetList
			};
			if (this.$refs.searchVFormRef) {
				this.$refs.searchVFormRef.setFormJson({ ...newFormJson });
			} else {
				this.newFormJson = { ...newFormJson };
			}
		},
		setFormJson(data) {
			this.searchFiledInit(data);
		},
		// 获取表单设计的字段数据
		getFieldWidgets() {
			if (this.$refs.searchVFormRef) {
				return this.$refs.searchVFormRef.getFieldWidgets();
			}
		},
		// 获取表单填写的数据
		getFormData() {
			if (this.$refs.searchVFormRef) {
				return this.$refs.searchVFormRef.getFormData();
			}
		}
	}
};
</script>

<style lang="scss" scoped>
/* my-select 绑定在 el-select 上*/
.formRender {
	// overflow: hidden;
	flex: 1;
	display: grid;
	grid-template-columns: 1fr 1fr 1fr 1fr;
	::v-deep {
		.el-form-item {
			display: flex;
			width: 100%;
			margin-bottom: 0 !important;
		}
		.el-form-item__label {
			font-size: 14px;
			font-weight: 400;
			color: rgba(0, 0, 0, 0.6);
			padding: 0px 8px 0px 0px;
			width: 90px;
			text-align: right;
		}
		.el-form-item__content {
			width: 100%;
			margin-left: 0 !important;
			padding-right: 10px;
		}
	}
}
@media screen and (max-width: 1700px) {
	.formRender {
		grid-template-columns: 1fr 1fr 1fr;
	}
}
@media screen and (max-width: 1400px) {
	.formRender {
		grid-template-columns: 1fr 1fr;
	}
}

.isEditeView {
	::v-deep {
		// .field-wrapper {
		// 	width: 33.33%;
		// 	display: flex;
		// }
		// .el-form-item__label {
		// 	text-align: right !important;
		// 	max-width: 100px;
		// 	overflow: hidden;
		// 	flex: 0 0 100px;
		// }
		// .el-form-item__content {
		// 	width: calc(100% - 100px);
		// 	margin-left: 0 !important;
		// }
	}
}
.formInfo {
	padding: 0 20px;
	display: flex;
	justify-content: space-between;
	// border-bottom: 1px solid #f0f0f0;
	flex-shrink: 0;
	::v-deep {
		// 	.el-input__inner {
		// 		width: 180px;
		// 		height: 32px;
		// 		border-radius: $borderRadius;
		// 	}
		// 	.el-form-item {
		// 		margin-bottom: 8px;
		// 	}

		// 	.el-form--inline .el-form-item {
		// 		margin-right: 24px;
		// 	}
		.select-user {
			height: 32px;
			line-height: 32px;
		}
	}

	.form_button {
		display: flex;
		flex-direction: column;
		align-items: flex-start;
		font-size: 14px;
		padding-top: 2px;
		.butSearch {
			width: 68px;
			height: 32px;
			border-radius: $borderRadius;
			color: var(--brand-6);
			line-height: 22px;
			margin-bottom: 8px;
			border: 1px solid var(--brand-6);
		}
		.butReset {
			width: 68px;
			height: 32px;
			border-radius: $borderRadius;
			border: 1px solid $borderColor;
			color: $primaryTextColor;
			margin-left: 0px;
		}
	}
}
@media screen and (max-width: 1000px) {
	.isEditeView ::v-deep .field-wrapper {
		width: 50%;
	}
}
::v-deep .field-wrapper {
	// height: 48px !important;
	margin-bottom: 8px;
}
::v-deep .el-input--small .el-input__inner {
	height: 32px !important;
	line-height: 32px !important;
	border: 1px solid $borderColor !important;
	border-radius: $borderRadius !important;
}
::v-deep .el-input--medium .el-input__inner {
	height: 32px !important;
	line-height: 32px !important;
	border: 1px solid $borderColor !important;
	border-radius: $borderRadius !important;
}
</style>
