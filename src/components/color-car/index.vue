<!--
 * @Description: 色卡
 * @Version: 1.0
 * @Autor: zhaodongming
 * @Date: 2022-08-17 11:45:03
 * @LastEditors: zhaodongming
 * @LastEditTime: 2023-07-06 16:52:43
-->
<template>
	<div class="color">
		<div
			v-for="(colorItem, index) in defaultColorList"
			:key="index"
			class="color-spot"
			:style="`background-color:${colorItem.color}`"
			@click="handleColorSelect(colorItem)"
		></div>
		<div class="color-more">
			<el-color-picker v-model="theme" size="mini" @change="handlerPickerChange"></el-color-picker>
		</div>
	</div>
</template>

<script>
import { handleSetThemeVar, handleColorLoad, handleGetDefaultColor } from '@/utils/color.js';
import { getItem, setItem } from '@/utils/localstorage';
// 获取element版本号,便于拉去对应版本的主题文件
// 默认主题颜色
const defaultColor = '#409EFF';

export default {
	name: 'ColorCar',
	props: {
		drawer: {
			type: Boolean,
			default: false
		}
	},
	data() {
		return {
			// 默认颜色列表
			defaultColorList: [],
			// 默认颜色
			theme: defaultColor,
			// 样式字符串
			chalk: ''
		};
	},
	watch: {
		drawer(newVal, oldVal) {
			this.drawerVal = newVal;
		},
		// 1、监听默认主题变化,主题切换要同步给theme对象
		async theme(newVal, oldVal) {
			// 获取当前风格模式
			handleSetThemeVar(newVal, model ? model : 'light');
			const model = document.documentElement.getAttribute('theme-mode');
			handleColorLoad(newVal, oldVal, model ? model : 'light');
		}
	},
	created() {
		// 获取默认内置色系
		this.defaultColorList = handleGetDefaultColor();
		// 回显主题设置
		this.handlerReShow();
	},
	methods: {
		// 设置过主题或者是需要从后端接口获取主题时回显
		handlerReShow() {
			const reTheme = getItem('admin-theme');
			// 获取到主题配置时,设置回显
			if (reTheme) {
				this.theme = reTheme;
				// 获取当前风格模式
				const model = document.documentElement.getAttribute('theme-mode');
				handleSetThemeVar(reTheme, model ? model : 'light');
				document.documentElement.setAttribute('theme-color', reTheme);
			}
		},
		// 切换品牌色
		handleColorSelect(colorItem) {
			this.theme = colorItem.color;
			setItem('admin-theme', colorItem.color);
			// 内置颜色可以直接使用theme-color = 色系名称进行切换
			document.documentElement.setAttribute('theme-color', colorItem.key);
		},
		// 色盘变更
		handlerPickerChange(color) {
			console.log(color);
			// 切换主题
			document.documentElement.setAttribute('theme-color', color);
		}
	}
};
</script>

<style lang="scss" scoped>
.color {
	display: flex;
	align-items: center;
	flex-wrap: wrap;
	&-spot {
		width: 20px;
		height: 20px;
		margin-right: 8px;
		margin-bottom: 10px;
		font-weight: 700;
		color: #fff;
		text-align: center;
		cursor: pointer;
		border-radius: 2px;
	}
}
</style>
