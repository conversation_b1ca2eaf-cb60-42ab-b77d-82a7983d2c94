<template>
	<el-dialog
		top="0"
		width="960px"
		:append-to-body="true"
		class="desk-el-custom-dialog"
		:title="title"
		:show-close="true"
		:visible.sync="showPopup"
	>
		<div class="content">
			<div class="content-search">
				<el-input
					v-model="keywords"
					placeholder="输入搜索关键词"
					class="content-search-input"
					@input="search"
				>
					<template slot="suffix">
						<svg-icon icon-class="search" class="search-input-icon"></svg-icon>
					</template>
				</el-input>
				<div class="content-search-button" @click="reset">重置</div>
			</div>
			<div v-loading="loading" class="content-table">
				<el-table
					ref="table"
					class="desk-el-table border-table"
					:row-style="getRowStyle"
					:header-row-style="headerRowStyle"
					:header-cell-style="headerCellStyle"
					:highlight-current-row="true"
					:data="computedTableData"
					style="width: 100%; margin-bottom: 12px"
					:border="true"
					height="100%"
					v-bind="$attrs"
					@row-dblclick="dblclick"
					@row-click="rowClick"
					@selection-change="selectionChange"
					v-on="$listeners"
				>
					<el-table-column
						v-if="showCheckbox"
						type="selection"
						width="55"
						:selectable="selectableStatusFun"
						align="center"
					></el-table-column>
					<el-table-column type="index" label="序号" width="80" align="center"></el-table-column>
					<el-table-column
						v-for="(header, i) of tableHeader"
						:key="'head-' + i"
						v-bind="header"
					></el-table-column>
				</el-table>
			</div>
			<div class="paging">
				<el-pagination
					background
					:current-page="pageNo"
					:page-sizes="[10, 20, 50, 100]"
					:page-size="pageSize"
					layout="sizes, prev, pager, next"
					:total="total"
					@size-change="handleSizeChange"
					@current-change="handleCurrentChange"
				></el-pagination>
			</div>
		</div>
		<span slot="footer" class="dialog-footer">
			<el-button @click="cale">取 消</el-button>
			<el-button type="primary" @click="saveEditAvatar">确 定</el-button>
		</span>
	</el-dialog>
</template>

<script>
import { debounce } from '@/utils';
import { isEmpty } from '@/utils/index';

export default {
	name: 'ProjectList',
	props: {
		// 弹窗标题
		title: {
			type: String,
			default: () => {
				return '选择数据';
			}
		},
		//  禁止选择数据函数
		selectableStatusFun: {
			type: [Function, Object],
			default: null
		},
		// 请求函数
		requestFun: {
			type: [Function, Object],
			default: null
		},
		// 请求参数，除了关键词和分页数据
		requestParams: {
			type: Object,
			default: () => {
				return {};
			}
		},
		// 表头
		tableHeader: {
			type: Array,
			default: () => {
				return [];
			}
		},
		// 表格数据
		tableData: {
			type: Array,
			default: () => {
				return [];
			}
		},
		// 是否多选
		showCheckbox: {
			type: Boolean,
			default: () => {
				return true;
			}
		}
	},
	data() {
		return {
			selection: [], // 多选的集合
			requestTableData: [], // 请求回来的表格数据
			currentRow: null,
			pageSize: 10,
			pageNo: 1,
			keywords: '',
			total: 0,
			loading: false,
			showPopup: false,
			/**每一行的样式*/
			headerRowStyle: {
				height: '46px',
				background: '#f5f7fa'
			},
			/**每一格样式*/
			headerCellStyle: {
				background: 'transparent',
				fontSize: '16px',
				fontFamily: 'PingFang SC, PingFang SC',
				fontWeight: 500,
				color: '#2f446b',
				lineHeight: '24px'
			}
		};
	},
	computed: {
		computedTableData() {
			return isEmpty(this.requestTableData) ? this.tableData : this.requestTableData;
		}
	},
	methods: {
		/**重置*/
		reset() {
			this.keywords = '';
			if (this.requestFun) {
				this.pageNo = 1;
				this.currentRow = null;
				this.getTableData();
			}
			this.$emit('reset');
		},
		/**搜索*/
		search: debounce(
			function () {
				if (this.requestFun) {
					this.getTableData();
				}
				this.$emit('search', this.keywords);
			},
			500,
			false
		),
		/**改变页码*/
		handleCurrentChange(i) {
			this.pageNo = i;
			if (this.requestFun) {
				this.getTableData();
			}
			this.$emit('handleCurrentChange', this.pageNo, this.pageSize);
		},
		/**改变分页大小*/
		handleSizeChange(i) {
			if (this.requestFun) {
				this.pageNo = 1;
				this.pageSize = i;
				this.getTableData();
			}
			this.$emit('handleSizeChange', this.pageNo, this.pageSize);
		},
		/**获取表格奇偶列的样式*/
		getRowStyle({ row, rowIndex }) {
			return {
				height: '54px',
				background: '#ffffff'
			};
		},
		/**关闭弹窗*/
		cale() {
			this.currentRow = null;
			this.showPopup = false;
		},
		/**确认保存*/
		saveEditAvatar() {
			let data = this.showCheckbox ? this.selection : this.currentRow;
			this.$emit('confirm', data);
			this.currentRow = null;
			this.showPopup = false;
		},
		/**点击某一行*/
		rowClick(row) {
			this.currentRow = row;
			this.$emit('row-click', row);
		},
		/**双击*/
		dblclick(row) {
			this.currentRow = row;
			if (!this.showCheckbox) {
				this.saveEditAvatar();
			}
			this.$emit('row-dblclick', row);
		},
		/**选项发生变化*/
		selectionChange(selection) {
			this.selection = selection;
			this.$emit('selection-change', selection);
		},
		/**
		 * 打开弹窗
		 * @param {String } sourceType 资源类型(project:项目选择、contract:合同选择)
		 * @param {String } code 项目类型编码-项目信息获取
		 * @param {String } accessApplicationId 项目编码
		 * */
		open(sourceType, code, accessApplicationId) {
			if (this.requestFun) {
				this.pageNo = 1;
				this.pageSize = 10;
				this.keywords = '';
				this.requestTableData = [];
				this.getTableData();
			}
			this.showPopup = true;
		},
		/**获取表格数据*/
		getTableData() {
			this.loading = true;
			this.requestFun({
				...this.requestParams,
				pageNo: this.pageNo,
				pageSize: this.pageSize,
				keywords: this.keywords
			}).then(res => {
				this.loading = false;
				if (res.code === 200) {
					this.requestTableData = res.result.records || [];
					this.total = res.result.total || 0;
				} else {
					this.$message.error(res.message);
				}
			});
		}
	}
};
</script>

<style scoped lang="scss">
.content {
	height: 100%;
	display: flex;
	flex-direction: column;
	&-search {
		margin: 12px 0;
		display: flex;
		align-items: center;
		justify-content: flex-end;
		&-input {
			width: 200px;
			height: 32px;
			border-radius: $borderRadius;
			::v-deep .el-input__inner {
				height: 32px;
				background: #ffffff;
				width: 100%;
				color: $textColor !important;
				border-radius: $borderRadius;
			}
			::v-deep .el-input__suffix {
				display: flex;
				align-items: center;
			}
			&-icon {
				height: 12px;
				width: 12px;
			}
		}
		&-button {
			background: #ffffff;
			border-radius: 6px;
			border: 1px solid var(--brand-6);
			font-weight: 400;
			font-size: 14px;
			color: var(--brand-6);
			line-height: 22px;
			padding: 4px 20px;
			margin-left: 8px;
			cursor: pointer;
		}
	}
	&-table {
		flex: 1;
		overflow: hidden;
	}
	.paging {
		display: flex;
		justify-content: center;
		padding: 16px 0;
	}
}
::v-deep .el-table__body tr.current-row > td {
	background: var(--brand-6);
}
.desk-el-custom-dialog {
	::v-deep .el-dialog {
		display: flex;
		flex-direction: column;
		overflow: hidden;
		height: 80%;
		.el-dialog__body {
			flex: 1;
			overflow: hidden;
		}
	}
}
::v-deep .desk-el-table .el-table__body-wrapper .el-table__body tr.el-table__row > td:first-child,
::v-deep .desk-el-table .el-table__body-wrapper .el-table__body tr.el-table__row > td:last-child {
	border-radius: 0 !important;
}
::v-deep .el-table__body tr.current-row > td {
	background: #eef4fd !important;
}
</style>
