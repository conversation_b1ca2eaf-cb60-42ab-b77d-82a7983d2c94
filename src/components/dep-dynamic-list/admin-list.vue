<template>
	<!-- 通讯录【部门】 -->
	<div class="load-dynamic-list">
		<div class="table">
			<el-table
				ref="table"
				:key="tableKey"
				v-loading="loading"
				class="custom-el-table"
				:data="tableData"
				style="width: 100%"
				row-key="id"
				lazy
				:load="load"
				:tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
			>
				<el-table-column
					v-for="(t, i) in tableHead"
					:key="getRandom(i)"
					:label="t.desc"
					:fixed="t.btns && t.btns.length > 0 ? 'right' : null"
					show-overflow-tooltip
					:render-header="renderHeader"
				>
					<template slot-scope="scope">
						<div v-if="t.field === 'directors'">
							<div
								v-if="scope.row.directors && scope.row.directors.length > 0"
								class="directors-body-tableUser"
							>
								<!-- <div v-for="(item, index) in scope.row.directors" :key="index">
									<div class="directors-body-tableUser-type">
										<div :style="{ width: item.realname.length * 14 + 'px' }">
											{{ item.realname }}
										</div>
									</div>
								</div> -->
								<el-tooltip
									class="item"
									effect="dark"
									:content="buildData(scope.row.directors)"
									placement="top"
								>
									<span>{{ buildData(scope.row.directors, 1) }}</span>
								</el-tooltip>
							</div>
							<div v-else>
								<div>暂无</div>
							</div>
						</div>
						<div v-else-if="t.field == 'status'">
							<el-switch
								v-model="scope.row.status"
								@change="e => statusChange(e, scope.row, 1)"
							></el-switch>
						</div>
						<div v-else-if="t.field == 'isDefault'">
							<el-switch
								v-model="scope.row.isDefault"
								@change="e => statusChange(e, scope.row, 2)"
							></el-switch>
						</div>

						<div v-else>
							<form-table-render :row="scope.row" :ele-data="t">
								<el-dropdown
									v-if="t.btns && t.btns.length > 0"
									@command="
										type => {
											return handleCommand(type, scope.row, scope.$index);
										}
									"
								>
									<div class="dynamic-editor">
										<span class="dynamic-editor-text">更多</span>
										<i class="el-icon-arrow-down el-icon--right"></i>
									</div>
									<el-dropdown-menu slot="dropdown" class="dynamic-editor-list">
										<el-dropdown-item
											v-for="(btn, k) in t.btns"
											:key="k"
											class="dynamic-editor-list-btn"
											:command="btn.type"
										>
											{{ btn.name }}
										</el-dropdown-item>
									</el-dropdown-menu>
								</el-dropdown>
							</form-table-render>
						</div>
					</template>
				</el-table-column>
				<template slot="empty">
					<BasicEmpty />
				</template>
			</el-table>
		</div>
		<div class="page">
			<el-pagination
				background
				:current-page="pageNo"
				:page-sizes="[10, 20, 50, 100]"
				:page-size="pageSize"
				layout="total, sizes, ->, prev, pager, next, jumper"
				:total="total"
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
			></el-pagination>
		</div>
		<!-- 新增和编辑部门 -->
		<el-dialog
			class="dia custom-el-dialog-footer"
			:title="type == 'add' ? '新增岗位' : type == 'view' ? '岗位详情' : '编辑岗位'"
			:visible.sync="drawer"
			:before-close="handleClose"
		>
			<div class="btn-title">
				<!-- v-if="btnPower.includes('system:sys_depart:formDesign')" -->
				<div class="btn" @click="openDia(1)">
					<img src="../../assets/images/common/setting.png" alt="" />
					<span>设计表单</span>
				</div>
			</div>
			<div v-loading="userInfoLoading" class="drawer">
				<v-form-render
					v-if="drawerJson.widgetList"
					:key="formKey"
					ref="vFormRef"
					:form-json="drawerJson"
					:form-data="formData"
					:option-data="optionData"
					:base-url="config.baseUrl"
					:disabled="type == 'view'"
					@formChange="formChange"
				></v-form-render>
			</div>
			<div slot="footer" class="drawer_foot">
				<el-button @click="handleClose">关 闭</el-button>
				<el-button v-if="type != 'view'" type="primary" @click="handleAdd">确 定</el-button>
			</div>
		</el-dialog>
		<!-- 表单设计 -->
		<el-dialog
			v-loading="diaLoading"
			class="flow-design"
			:visible.sync="dialogVisible"
			fullscreen
			:show-close="false"
			destroy-on-close
		>
			<div class="dia">
				<div class="head">
					<div class="lf">
						<el-button class="btn" ghost icon="el-icon-arrow-left" @click="close">返回</el-button>
						<!-- <div class="inp">
							<i class="coos-iconfont icon-zhanghuxiugai"></i>
							<el-input v-model="search" class="search" placeholder="未命名的表单"></el-input>
						</div> -->
					</div>
					<div class="rf">
						<!-- <div class="btn">帮助</div>
						<div class="btn">预览</div> -->
						<el-button class="el-btn" type="primary" @click="handleSave">保存</el-button>
						<!-- <el-button class="el-btn" type="primary">发布</el-button> -->
					</div>
				</div>
				<v-form-designer v-show="act == 1" ref="vform" :base-url="config.baseUrl"></v-form-designer>
				<addList
					v-show="act == 3"
					ref="addList"
					:add-sys-code="addSysCode"
					:form-json="drawerJson"
					is-app-apend-model
					:app-query="appQuery"
					@listDataReloading="listDataReloading"
				></addList>
			</div>
		</el-dialog>
		<orgPersonnelDialog
			title="选择"
			:visible="chooseVisiable"
			:init-params="initParams"
			:init-values="deptManagers"
			:data-source="detaSource"
			:need-all-data="true"
			:can-select-depart="false"
			:disable-all="true"
			@sure="sure"
			@close="selectClose"
		></orgPersonnelDialog>
	</div>
</template>

<script>
import addList from '@/views/address-book/members-departments/components/add-list.vue';
import orgPersonnelDialog from '@/components/org-personnel-dialog';

import {
	getPostPage,
	getDesigns,
	getDepartsChildren,
	addPost,
	sysPost,
	putPost,
	editDesign,
	delPost,
	addDirectors,
	editStatus,
	setDefaultPost
} from '@/api/modules/dynamic';
import { getShowMode } from '@/utils/form-design';
import formTableRender from '@/components/form-table-render';
import { deepClone, processList, renderHeader } from '@/utils';
import config from '@/config';
import { TableMixin } from '@/mixins/table-fix';
// import DynamicList from '@/components/dynamic-list/index.vue';
import { getItem } from '@/utils/localstorage';
import { getDictionary } from '@/utils/data-dictionary';

// import AfTableColumn from 'af-table-column';

export default {
	name: 'DepDynamicList',
	components: {
		addList,
		formTableRender,
		// DynamicList,
		orgPersonnelDialog
		// AfTableColumn
	},
	mixins: [TableMixin],
	data() {
		return {
			chooseVisiable: false,
			initParams: {
				authorityType: 1, //类型;1.可用成员范围,2.禁止使用范围
				applicationId: ''
			},
			deptManagers: [],
			detaSource: ['depart'],
			appQuery: {
				applicationId: getDictionary('应用ID/通讯录'),
				moduleCode: 'module:depart',
				slotCode: 'slot:departListSlotColumns'
			},
			config,
			tableKey: 0,
			newPid: null, //新的pid，判断之后防止死循环
			formKey: 0, // 动态表单的key,用于刷新
			drawerJsonSource: {}, // 初始的表单JSON数据
			designerJson: {},
			search: '',
			act: 1,
			diaLoading: false,
			dialogVisible: false,
			optionData: {},
			formData: {},
			userInfoLoading: true,
			tableHead: [],
			tableData: [{}],
			pageNo: 1,
			pageSize: 10,
			total: 0,
			drawer: false,
			drawerJson: {},
			type: 'add',
			rowId: '',
			loading: false,
			orgId: undefined,
			selectedTableHead: [],
			addSysCode: {
				sysCode: 'sys_post',
				designCode: 'formSys:sysPostList',
				designType: '2',
				// isH5: 0,
				moduleId: '1737019234036236292',
				applicationId: getDictionary('应用ID/通讯录'),
				isDefault: '0'
			},
			param: null,
			popoverShow: false,
			thingTableHead: [
				{ desc: '应用(族)名称', field: 'appName' },
				{ desc: '订阅目的', field: 'purpose' }
			],
			renderHeader,
			btnPower: getItem('permission_identification')
				? JSON.parse(getItem('permission_identification'))
				: [],
			selectRow: {},
			selectRowIndex: '',
			postType: ''
		};
	},
	created() {
		this.getDepartsRootSearch();
		this.getDesigns(1);
		this.getDesigns(2);
	},
	methods: {
		// 切换状态
		statusChange(e, data, type) {
			console.log(e, data, type);
			if (type == 1) {
				editStatus(data.id, { status: e ? 1 : 0 });
			} else {
				setDefaultPost(data.id).then(res => {
					if (res.code == 200) {
						this.getDepartsRootSearch();
					}
				});
			}
		},
		async sure(e) {
			const deptSelects = [];
			const deptManagers = [];
			e.map(v => {
				deptSelects.push(v.id);
				deptManagers.push({
					id: v.id,
					title: v.title,
					realname: v.title
				});
			});
			this.deptManagers = [...deptManagers];
			const addRes = await addDirectors(this.selectRow.id, { directorIds: deptSelects });
			if (addRes.code == 200) {
				this.$message({
					message: '成功设置部门负责人',
					type: 'success'
				});
				this.tableData[this.selectRowIndex].directors = [...deptManagers];

				this.chooseVisiable = false;
				this.selectRow = {};
				this.selectRowIndex = '';
			} else {
				this.$message({
					message: '设置部门负责人失败',
					type: 'error'
				});
			}
		},
		selectClose() {
			this.chooseVisiable = false;
		},
		formChange(fieldName, newValue, oldValue, formModel) {
			if (fieldName == 'postType') {
				if (newValue == 2) {
					this.drawerJson.widgetList[1].options.required = true;
				} else {
					this.drawerJson.widgetList[1].options.required = false;
				}
			}
			console.log(this.drawerJson.widgetList[1].options);
			// 如果上级部门发生变化并且有值，就要隐藏部门的输入框，后端会根据部门自动填充组织
			if (fieldName === 'pid') {
				if (newValue === this.newPid) return;
				if (newValue) {
					this.drawerJson.widgetList = this.drawerJsonSource.widgetList.filter(item => {
						return item.options.name !== 'orgId';
					});
				} else {
					this.drawerJson.widgetList = this.drawerJsonSource.widgetList;
				}
				let data = this.$refs.vFormRef.getFormData(false);
				data.orgId = data.orgId ? data.orgId : '';
				this.newPid = data.pid;
				this.formKey += 1;
				this.$nextTick(() => {
					this.$refs.vFormRef.setFormData(data);
				});
			}
		},
		handleCommand(type, row, index) {
			this.selectRow = { ...row };
			this.selectRowIndex = index;
			const id = row.id;
			if (type == 'add') {
				this.openDrawer(id);
			} else if (type == 'edite') {
				this.openEdit(id, 'edit');
			} else if (type == 'view') {
				this.openEdit(id, 'view');
			} else {
				this.delDrawer(id);
			}
		},
		close() {
			this.act = 1;
			this.dialogVisible = false;
		},
		handleSave() {
			let listJson = {
				optionalColumns: this.$refs.addList.list,
				selectedColumns: this.$refs.addList.tableHead,
				optionalQueryColumns: this.$refs.addList.getOptionalQueryColumns(),
				selectedQueryColumns: this.$refs.addList.getFieldWidgets()
			};
			let fromJson = this.$refs.vform.getFormJson();
			this.editDesign(fromJson, listJson, 0);
		},
		async editDesign(fromJson, listJson, isH5) {
			this.diaLoading = true;
			let res;
			if (this.act == 1) {
				let valList = this.$refs.vform.getFieldWidgets();
				// 更新列表设计字段
				const tableKey = valList.map((newCloumns, i) => {
					// 获取最新更新后的selectedColumns【右侧列表】
					return {
						act: false,
						fields: newCloumns.field,
						desc: newCloumns.field.options.label,
						field: newCloumns.name,
						// 数据来源类型
						dataSourceType: newCloumns.field.options.dataSourceType,
						// 数据选项
						optionItems:
							newCloumns.field.options.dataSourceType == 1
								? newCloumns.field.options.optionItems
								: [],
						// 数据字典code
						dictCode: newCloumns.field.options.dictCode,
						// 数据外部API
						url: newCloumns.field.options.url,
						source: newCloumns.field.options.source,
						search: !(
							newCloumns.field.options.search === false || newCloumns.field.options.source !== 1
						),
						// dictData,
						showMode: getShowMode(newCloumns.type),
						slotWidgetAccessId: newCloumns.field.options.slotWidgetAccessId,
						type: newCloumns.type,
						name: newCloumns.name,
						options: newCloumns.field.options,
						pcChecked: false,
						appChecked: false,
						pcQueryChecked: false,
						appQuerySort: i + 1,
						pcQuerySort: i + 1,
						appSort: i + 1,
						pcSort: i + 1,
						appQueryChecked: false
					};
				});
				let newtableKey = [];
				if (Array.isArray(this.optionalColumns)) {
					const optionalColumnsMap = this.optionalColumns.reduce((acc, column) => {
						acc[column.field] = { ...column };
						return acc;
					}, {});
					tableKey.forEach(newColumn => {
						const updatedColumn = optionalColumnsMap[newColumn.field];
						if (updatedColumn && updatedColumn.appQuerySort && updatedColumn.fields) {
							newtableKey.push({ ...updatedColumn });
						} else {
							newtableKey.push({ ...newColumn });
						}
					});
				}
				console.log(newtableKey, 'newtableKey');
				// end
				let tableList = {
					optionalColumns: newtableKey
				};
				res = await editDesign({
					sysCode: 'sys_post',
					designCode: 'formSys:sysPostFormPc',
					designType: '1',
					// isH5,
					configJson: JSON.stringify(fromJson),
					moduleId: '1737019234036236292',
					applicationId: getDictionary('应用ID/通讯录'),
					isDefault: '0'
				});
				const res1 = await editDesign({
					sysCode: 'sys_post',
					designCode: 'formSys:sysPostList',
					designType: '2',
					// isH5,
					configJson: JSON.stringify(tableList),
					moduleId: '1737019234036236292',
					applicationId: getDictionary('应用ID/通讯录'),
					isDefault: '0'
				});
				const list = await Promise.all([res, res1]);
				if (list[0].code == 200 && list[1].code == 200) {
					this.$message({
						message: '修改成功',
						type: 'success'
					});
					this.act = 1;
					this.getDesigns(2);
					// 更新新增表单
					this.getDesigns(1);
					this.drawer = false;
					this.dialogVisible = false;
				} else {
					this.$message.error('操作失败，请稍后再试！');
				}
			} else if (this.act == 3) {
				res = await editDesign({
					sysCode: 'sys_post',
					designCode: 'formSys:sysPostList',
					designType: '2',
					// isH5,
					configJson: JSON.stringify(listJson),
					moduleId: '1737019234036236292',
					applicationId: getDictionary('应用ID/通讯录'),
					isDefault: '0'
				});
				if (res.code == 200) {
					this.$message({
						message: '修改成功',
						type: 'success'
					});
					this.act = 1;
					this.getDesigns(2);
					this.dialogVisible = false;
				} else {
					this.$message.error('操作失败，请稍后再试！');
				}
			}

			this.diaLoading = false;
		},
		// 设计步骤
		async handleAct(i, init) {
			this.diaLoading = true;
			if (!init) {
				this.act = i;
			}
			if (i == 3) {
				const res = await getDesigns({
					sysCode: 'sys_post',
					designType: 2
				});
				const data = res.result[0]?.configJson
					? JSON.parse(res.result[0]?.configJson)
					: {
							selectedColumns: [],
							optionalColumns: []
					  };
				let list = data.optionalColumns || [];
				console.log(data, '设置table的header头部字段');
				// 设置搜索字段
				// this.$refs.addList.searchFieldInit(data.selectedQueryColumns, data.optionalQueryColumns);
				//   初始化时，存储列表设计信息
				this.selectedTableHead = data.selectedColumns;
				//   end
				// 设置table的data展示数据
				this.$refs.addList.list = list.map((t, i) => {
					console.log(t, '123123');
					let dictData;
					if (t.dictData) {
						dictData = t.dictData;
					}
					// let dataSourceType;
					// if (t.source == 2) {
					// 	dataSourceType = 1;
					// }
					return {
						act: t.act,
						desc: t.desc,
						fields: t.fields,
						field: t.field,
						// 数据来源类型
						dataSourceType: t.dataSourceType,
						// 数据选项
						optionItems: t.dataSourceType == 1 ? t.optionItems : [],
						// 数据字典code
						dictCode: t.dictCode,
						// 数据外部API
						url: t.url,
						source: t.source,
						search: !(t.search === false || t.source !== 1),
						sourceDesc: t.sourceDesc,
						// dataSourceType,
						dictData,
						showMode: t.showMode,
						slotWidgetAccessId: t.slotWidgetAccessId,
						type: t.type,
						name: t.name,
						pcChecked: t.pcChecked || false,
						appChecked: t.appChecked || false,
						pcQueryChecked: t.pcQueryChecked || false,
						appQuerySort: t.appQuerySort || i + 1,
						pcQuerySort: t.appQuerySort || i + 1,
						appSort: t.appQuerySort || i + 1,
						pcSort: t.appQuerySort || i + 1,
						appQueryChecked: t.appQueryChecked || false
					};
				});
				this.$refs.addList.newList = this.$refs.addList.list;
				// 关闭loading
				this.diaLoading = false;
			}
		},
		openDia(i) {
			this.dialogVisible = true;
			if (i === 3) {
				this.act = 3;
				this.handleAct(i);
			} else {
				this.act = 1;
				// 获取列表设计数据
				this.handleAct(3, true);
				this.$nextTick(() => {
					this.designerJson = JSON.parse(JSON.stringify(this.drawerJson));
					if (this.$refs.vform) {
						this.$refs.vform.setForm(this.designerJson);
					}
				});
			}
		},
		handleClose() {
			this.drawerJson = {};
			this.formData = {};
			this.drawer = false;
		},
		openEdit(id, type) {
			sysPost(id).then(async res => {
				if (res.code == 200) {
					res.result.status = res.result.status ? '1' : '0';
					res.result.isDefault = res.result.isDefault ? '1' : '0';
					console.log(res.result);
					this.formData = res.result;
					await this.getDesigns(1);
					if (this.formData.pid) {
						this.drawerJson.widgetList = this.drawerJson.widgetList.filter(item => {
							return item.options.name !== 'orgId';
						});
						this.formKey += 1;
					}
				}
			});
			this.type = type;
			this.rowId = id;
			this.drawer = true;
		},
		// 列表设计数据更新
		listDataReloading() {
			this.openDia(3);
			// 更新列表数据
			this.getDesigns(2);
		},
		handleAdd() {
			this.$refs.vFormRef.getFormData().then(formData => {
				// 去除表单值前后空格
				Object.keys(formData).forEach(key => {
					if (typeof formData[key] == 'string') {
						formData[key] = formData[key].trim();
					}
				});
				// 其他处理逻辑
				this.userInfoLoading = true;
				let params = [];
				let inpList = this.$refs.vFormRef.getFieldWidgets();
				inpList = inpList.reduce((accumulator, currentValue) => {
					accumulator[currentValue.name] = currentValue;
					return accumulator;
				}, {});
				for (let key in inpList) {
					let source = inpList[key]?.field?.options?.source;
					if (!source) {
						source = this.getDataSourceType(formData[key]?.type) ? 2 : '';
					}
					let item = {
						field: key,
						value: formData[key],
						source: source
					};
					params.push(item);
				}
				if (this.type == 'add') {
					addPost(params).then(res => {
						if (res.code == 200) {
							this.$message({
								message: '新增成功',
								type: 'success'
							});
							this.drawer = false;
							// 刷新列表
							this.getDepartsRootSearch();
							this.userInfoLoading = false;
						} else {
							this.$message.error(res.message);
							this.userInfoLoading = false;
						}
					});
				} else {
					putPost({ id: this.rowId, data: params }).then(res => {
						if (res.code == 200) {
							this.$message({
								message: '修改成功',
								type: 'success'
							});
							this.drawer = false;
							// 刷新列表
							this.getDepartsRootSearch();
							this.userInfoLoading = false;
						} else {
							this.$message.error(res.message);
							this.userInfoLoading = false;
						}
					});
				}
			});
		},
		openDrawer(id) {
			if (id) {
				this.formData.pid = id;
			}
			this.type = 'add';
			this.drawer = true;
			this.formData = {};
			this.drawerJson = {};
			this.getDesigns(1);
		},
		/**删除部门*/
		delDrawer(ids) {
			this.$confirm('此操作将永久删除该岗位, 是否继续?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {
				delPost(ids).then(res => {
					if (res.code === 200) {
						this.$message.success('操作成功');
						this.getDepartsRootSearch();
					} else {
						this.$message.error(res.message);
					}
				});
			});
		},
		load(tree, treeNode, resolve) {
			getDepartsChildren({ pid: tree.id }).then(res => {
				if (res.code == 200) {
					resolve(res.result || []);
				} else {
					resolve([]);
				}
			});
		},
		handleCurrentChange(i) {
			this.pageNo = i;
			this.getDepartsRootSearch();
		},
		handleSizeChange(i) {
			this.pageNo = 1;
			this.pageSize = i;
			this.getDepartsRootSearch();
		},
		// 查询根节点
		getDepartsRootSearch() {
			this.loading = true;
			this.tableData = [];
			console.log(this.param);
			getPostPage({
				postType: this.postType,
				pageNo: this.pageNo,
				...this.param,
				pageSize: this.pageSize,
				// 组织ID
				orgId: this.orgId
			}).then(res => {
				if (res.code == 200) {
					this.tableData = res.result.records;
					this.total = res.result.total;
					this.tableKey += 1;
				}
			});
			this.loading = false;
		},
		/**获取设计的JSON*/
		async getDesigns(type) {
			this.userInfoLoading = true;
			let res = await getDesigns({
				sysCode: 'sys_post',
				designType: type
			});
			if (res.code == 200) {
				// 编辑PC端展示配置
				const result = res.result;
				let pcrRsult = [];
				result.forEach(element => {
					// if (element.isH5 == 0) {
					pcrRsult.push(element);
					// }
				});
				if (type == 2) {
					// 解析JSON字符串
					const configJson = JSON.parse(pcrRsult[0].configJson);
					let optionalColumns = JSON.parse(JSON.stringify(configJson.optionalColumns));
					this.$nextTick(() => {
						if (this.$refs.addList) {
							this.$refs.addList.list = JSON.parse(JSON.stringify(configJson.optionalColumns));
							this.$refs.addList.newList = JSON.parse(JSON.stringify(configJson.optionalColumns));
						}
					});

					this.optionalColumns = optionalColumns;
					let resColums = [];
					resColums = processList(optionalColumns, 'pcChecked', 'pcSort');
					if (resColums && resColums.length === 0) {
						resColums = optionalColumns;
					}
					resColums.push({
						desc: '操作',
						btns: [
							{
								name: '查看',
								type: 'view'
							},
							{
								name: '编辑',
								type: 'edite'
							},
							{
								name: '删除',
								type: 'delete'
							}
						]
					});
					resColums[1] = {
						desc: '状态',
						field: 'status'
					};
					this.tableHead = resColums;
					let widgetList = processList(optionalColumns, 'pcQueryChecked', 'pcQuerySort');
					console.log(widgetList, 'widgetList');
					widgetList = widgetList.map(item => {
						// 当搜索时关闭所有表单验证
						item.fields.options.required = false;
						// 将隐藏的字段展示到搜索配置上
						item.fields.options.hidden = false;
						return {
							...item.fields,
							formItemFlag: true
						};
					});
					this.$emit('selectedQueryColumns', widgetList);
				} else {
					this.drawerJson = pcrRsult[0]?.configJson ? JSON.parse(pcrRsult[0]?.configJson) : {};
					this.drawerJsonSource = deepClone(this.drawerJson); // 保存最开始的状态，防止对象内存污染
					this.$nextTick(() => {
						if (this.$refs.vFormRef) {
							this.$refs.vFormRef.setFormData(this.formData);
						}
					});
				}
			}
			this.userInfoLoading = false;
		},
		buildData(arr, bool) {
			let name = arr.map(v => {
				return v.realname;
			});
			if (bool) {
				let names = '';
				if (name.length == 1) {
					names = name[0] + ' 共1人';
				}
				if (name.length == 2) {
					names = name[0] + '、' + name[1] + ' 共2人';
				}
				if (name.length > 2) {
					names = name[0] + '、' + name[1] + ' 等' + name.length + '人';
				}
				return names;
			}
			return name.join('、');
		}
	}
};
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__header {
	padding: 0;
}
.load-dynamic-list {
	height: 100%;
	.table {
		height: calc(100% - 50px);
		overflow: auto;
	}
	.page {
		margin-top: 15px;
		display: flex;
		justify-content: center;
		::v-deep.el-pager li.active {
			color: #fff;
		}
	}
	::v-deep.el-table__header-wrapper .has-gutter th {
		background: #edf2f6;
		.cell {
			font-size: 14px;
			font-weight: 500;
			color: $primaryTextColor;
		}
	}
	::v-deep.el-table__header th {
		background: #edf2f6;
		.cell {
			font-size: 14px;
			font-weight: 500;
			color: $primaryTextColor;
		}
	}
	::v-deep.el-table .cell {
		display: flex;
	}
	.btn {
		// display: flex;
		.btns {
			margin-right: 20px;
		}
	}
}
.flow-design {
	::v-deep .el-dialog__body {
		padding-top: 10px;
	}
}
.dia {
	::v-deep.el-dialog__body {
		padding: 12px 44px 0px 42px;
		.dia-head {
			display: flex;
			align-items: center;
			.title {
				font-weight: 800;
				font-size: 18px;
				color: #303133;
				line-height: 24px;
			}
			.icon {
				margin-left: auto;
			}
		}
		.btn-title {
			margin: 29px 0 24px 0;
			display: flex;
			width: 100%;
			.tit {
				height: 22px;
				font-size: 16px;
				font-weight: 800;
				color: $primaryTextColor;
				line-height: 22px;
				border-left: 4px solid var(--brand-6);
				padding: 0 19px 0 3px;
			}
			.btn {
				cursor: pointer;
				margin-left: auto;
				display: flex;
				align-items: center;
				img {
					width: 16px;
					height: 16px;
					margin-right: 3px;
				}
				span {
					font-size: 14px;
					font-weight: 400;
					color: $textColor;
					line-height: 22px;
				}
			}
		}
		.tips {
			margin-top: 33px;
			.tit {
				display: flex;
				align-items: center;
				margin-bottom: 11px;
				font-size: 14px;
				font-weight: 800;
				color: $textColor;
				line-height: 22px;
				img {
					width: 16px;
					height: 16px;
					margin-left: 5px;
				}
			}
			.list {
				margin-top: 13px;
				display: flex;
				align-items: center;
				img {
					display: block;
					width: 10px;
					height: 9px;
				}
				.name {
					margin-left: 7px;
					font-size: 14px;
					font-weight: 400;
					color: $primaryTextColor;
					line-height: 22px;
					b {
						margin-left: 11px;
						font-size: 14px;
						font-weight: 400;
						color: var(--brand-6);
						line-height: 22px;
					}
					i {
						font-size: 14px;
						color: var(--brand-6);
						margin-left: 4px;
					}
				}
			}
			.open {
				cursor: pointer;
			}
		}
	}
}
.dynamic-editor {
	display: flex;
	align-items: center;
	font-family: PingFang SC, PingFang SC;
	font-weight: 400;
	color: $primaryTextColor;
	cursor: pointer;
	&-list {
		padding: 12px 11px;
		&-btn {
			border-radius: 4px;
		}
	}
}
.popover {
	width: 500px;
	.title {
		font-weight: 500;
		font-style: normal;
		font-size: 16px;
		color: #000000;
		.close {
			float: right;
			font-size: 14px;
			cursor: pointer;
			color: $holderTextColor;
			font-weight: 400;
		}
	}
	.tit {
		margin-top: 10px;
		display: flex;
		align-items: center;
		i {
			display: block;
			width: 10px;
			height: 10px;
			background: var(--brand-6, '#0f45ea');
			border-radius: 50%;
			margin-right: 5px;
		}
		b {
			margin-left: 5px;
			color: var(--brand-5, '#0f45ea');
		}
	}
	.table {
		margin-top: 10px;
	}
}
.directors-body {
	display: flex;
	flex-direction: column;
	flex-shrink: 0;
	padding: 15px 17px;
	height: 100%;
	width: calc(100% - 290px);
	// width: 100%;
	//min-width: 1350px;
	background: #ffffff;
	border-radius: 0px 0px 12px 0px;
	border-top: 1px solid #f0f0f0;
	font-size: 14px;
	font-weight: 400;
	color: $primaryTextColor;
	line-height: 12px;
	opacity: 1;

	&-tableUser {
		display: flex;
		align-items: center;

		&-type {
			display: inline-block;
			align-items: center;
			margin-right: 4px;
			padding: 3px 4px;
			border-radius: 3px;
			background-color: #f2f3f5;
		}
		&-Type {
			display: inline-block;
			flex-shrink: 0;
			padding: 2px 4px;
			margin-left: 1px;
			border-radius: 3px;
			text-align: center;
			background-color: #f2f3f5;
			color: #1890ff;
		}
	}
	&-tableperms {
		&-type {
			color: var(--brand-6);
		}
	}
	&-tableHandle {
		&-detailBtn {
			cursor: pointer;
			width: 56px;
			text-align: left;
			padding: 0;
			color: var(--brand-6);
			border: none;
			background-color: transparent;
		}
		&-deleteBtn {
			cursor: pointer;
			border: none;
			width: 56px;
			padding: 0;
			text-align: left;
			color: #ff7a7b;
			background-color: transparent;
		}
	}
	& ::v-deep.el-table td,
	.el-table th.is-leaf {
		border: none;
	}
}
::v-deep .el-range-editor.is-disabled,
.el-range-editor.is-disabled input {
	background-color: #fff !important;
}
::v-deep .el-range-editor.is-disabled input {
	background-color: #fff !important;
	color: rgba(0, 0, 0, 0.75) !important;
}
</style>
