<template>
	<div class="tree">
		<el-tree
			ref="tree"
			:key="data.length"
			:data="data"
			node-key="id"
			:default-expanded-keys="data && data.length ? [data[0].id] : []"
			:current-node-key="data && data.length ? data[0].id : ''"
			highlight-current
			:props="defaultProps"
			@node-click="handleNode"
		>
			<!-- eslint-disable-next-line vue/no-template-shadow -->
			<template #default="{ data }">
				<div class="tree-box company">
					<img v-if="data.logo" :src="data.logo" alt="" class="logo" />
					<div v-else class="logo-text">
						{{ data[label][0] }}
					</div>
					<el-tooltip
						:disabled="showTitle"
						effect="dark"
						:content="data[label]"
						placement="right"
						:open-delay="500"
					>
						<span @mouseover="onShowNameTips">{{ data[label] }}</span>
					</el-tooltip>
					<!-- <span>{{ data[label] }}</span> -->
					<!-- <img class="icon" src="@/assets/images/dynamic/more.png" /> -->
				</div>
			</template>
		</el-tree>
	</div>
</template>

<script>
import { mapGetters } from 'vuex';
export default {
	name: 'DepartmentTree',
	props: {
		data: {
			type: Array,
			default: () => {
				return [];
			}
		},
		label: {
			type: String,
			default: 'title'
		},
		showHead: {
			type: Boolean,
			default: true
		}
	},
	data() {
		return {
			isBg: true,
			defaultProps: {
				children: 'children',
				label: 'title'
			},
			tooltipTitle: null,
			showTitle: false
		};
	},
	computed: {
		...mapGetters(['rentInfo'])
	},
	methods: {
		handleCom() {
			if (this.isBg) return;
			this.isBg = true;
			this.$refs.tree.setCurrentKey(null);
			this.$emit('handleNode', '1');
		},
		handleNode(i) {
			this.isBg = false;
			this.$emit('handleNode', i);
		},
		getIcon(i) {
			if (i == 'org') {
				return 'icon-zuzhijigou';
			} else if (i == 'depart') {
				return 'icon-zuzhixiaxia';
			}
			return 'icon-renyuan';
		},
		// 判断内容长度是否启用tooltip展示
		onShowNameTips(e) {
			var target = e.target;
			// 文字长度
			let textLength = target.clientWidth;
			// 容器宽度
			let containerLength = target.scrollWidth;
			// 文字长度小于容器宽度
			if (textLength < containerLength) {
				this.showTitle = false;
			} else {
				this.showTitle = true;
			}
		}
	}
};
</script>

<style lang="scss" scoped>
.tree {
	width: 100%;
	height: 100%;
	padding: 8px 10px;
	box-sizing: border-box;
	.bg {
		background-color: #edf6ff;
	}
	.company {
		cursor: pointer;
		width: 100%;
		display: flex;
		align-items: center;
		height: 42px;
		padding: 0 10px;
		box-sizing: border-box;
		.logo {
			width: 24px;
			border-radius: 4px 4px 4px 4px;
		}
		.logo-text {
			width: 24px;
			height: 24px;
			border-radius: 4px;
			background-color: #3667fc;
			text-align: center;
			line-height: 24px;
			color: #fff;
			font-size: 14px;
		}
		.title {
			margin-left: 10px;
			font-size: 14px;
			font-weight: 400;
			color: $primaryTextColor;
			line-height: 22px;
			width: calc(100% - 55px);
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
		}
		.icon {
			width: 16px;
			height: 16px;
		}
	}
	::v-deep.el-tree-node__content {
		height: 42px;
		.el-tree-node__expand-icon {
			font-size: 18px;
		}
	}
	.tree-box {
		width: 100%;
		padding: 0;
		box-sizing: border-box;
		height: 42px;
		display: flex;
		align-items: center;
		.iconfont {
			font-size: 16px;
			color: $primaryTextColor;
		}
		span {
			// display: flex;
			font-size: 14px;
			font-weight: 400;
			color: $primaryTextColor;
			line-height: 22px;
			margin-left: 10px;
			width: calc(100% - 55px);
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
		}
		.icon {
			width: 16px;
			margin-left: auto;
		}
	}
}
</style>
