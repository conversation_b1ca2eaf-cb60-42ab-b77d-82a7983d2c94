<!--
 * @Description: 全局统一的iframe容器
 * @Version: 1.0
 * @Autor: FH.hao
 * @Date: 2024-06-04 17:41:37
-->
<template>
	<div v-loading="loading" class="iframe">
		<iframe
			v-if="show && viewUrl"
			ref="iframe"
			:key="key"
			class="iframe"
			frameborder="0"
			:src="newViewUrl"
			@load="iframeLoaded"
		></iframe>
	</div>
</template>

<script>
import WebviewSocket from '@/utils/socket';
import { getUUID, updateFormData } from '@/api/modules/coos';
import { mapGetters } from 'vuex';
import { checkLogin } from '@/api/modules/common';
import { get_token, getToken } from '@/utils/auth';
export default {
	name: 'Index',
	props: {
		/**不需要socket*/
		needSocket: {
			type: Boolean,
			default: () => {
				return true;
			}
		},
		/**打开的链接*/
		viewUrl: {
			type: String,
			default: () => {
				return '';
			}
		},
		/**额外的配置参数*/
		webviewConfig: {
			type: Object,
			default: () => {
				return {};
			}
		}
	},
	data() {
		return {
			key: 0,
			load: 0,
			one_uuid: '',
			socketVM: null,
			show: false,
			loading: true,
			eventMap: [
				{
					code: 0,
					name: '传递信息',
					change: () => {}
				},
				{
					code: 1,
					name: '设置标题',
					change: () => {}
				},
				{
					code: 2,
					name: '返回上一页',
					change: this.backPage
				},
				{
					code: 3,
					name: '发送Token',
					change: this.getToken
				},
				{
					code: 4,
					name: '发送位置',
					change: () => {}
				},
				{
					code: 5,
					name: '登录失效',
					change: this.goLogin
				},
				{
					code: 6,
					name: '获取用户Id',
					change: () => {}
				},
				{
					code: 7,
					name: '更换webview地址',
					change: () => {}
				},
				{
					code: 8,
					name: '加载完成',
					change: () => {}
				},
				{
					code: 9,
					name: '跳转页面',
					change: () => {}
				},
				{
					code: 10,
					name: '存储业务表单数据',
					change: this.setFormData
				}
			]
		};
	},
	computed: {
		...mapGetters(['rentInfo']),
		// 拼接后的真实页面地址
		newViewUrl() {
			return this.viewUrl.includes('?')
				? this.viewUrl + `&COOS_UUID=${this.one_uuid}&TerminalType=PC`
				: this.viewUrl + `?COOS_UUID=${this.one_uuid}&TerminalType=PC`;
		}
	},
	watch: {
		viewUrl(newVal) {
			if (newVal) {
				this.loading = true;
				this.key += 1;
			}
		}
	},

	async mounted() {
		window.addEventListener('message', this.handleMessage);
		if (this.needSocket) {
			let uuidRes = await getUUID();
			this.one_uuid = uuidRes.result[0];
			this.socketVM = new WebviewSocket(this.one_uuid, this.webviewConfig);
		}
		this.show = true;
	},
	beforeDestroy() {
		this.socketVM && this.socketVM.close();
		window.removeEventListener('message', this.handleMessage);
	},
	methods: {
		resetSession() {
			if (this.$refs.iframe && this.$refs.iframe.contentWindow) {
				this.$refs.iframe.contentWindow.postMessage(
					{
						code: 'createNewSession'
					},
					'*'
				);
			}
		},
		/**获取token*/
		getToken() {
			if (this.$refs.iframe && this.$refs.iframe.contentWindow) {
				this.$refs.iframe.contentWindow.postMessage(
					{
						code: 3,
						accessToken: getToken(), // token
						tenantId: get_token('X-Coos-Client-Tenant-Id') // 租户id
					},
					'*'
				);
			}
		},
		goLogin() {
			checkLogin().then(res => {
				if (res.code === 200) {
					this.$message.error('主应用登录未过期！');
				}
			});
		},
		/**存储业务表单数据*/
		setFormData(data) {
			updateFormData(this.webviewConfig.dataInstanceId, {
				businessData: JSON.stringify(data.message)
			}).then(res => {
				if (res.code === 200) {
					console.log('res----', res);
				} else {
					this.$message.error(res.message);
				}
			});
		},
		/**
		 * @method 处理主子应用通信事件
		 * */
		handleMessage(message) {
			let data = message.data.data && message.data.data.arg ? message.data.data.arg : message.data;
			// 因为postMessage都挂载在window监听，所以通过code判断是否本实例进行的通知
			// && data.arg.message.webKey === this.socketVM.code
			let eventObj = this.eventMap.find(item => {
				return item.code === data.code;
			});
			if (eventObj) {
				eventObj.change(data);
			}
		},
		/**返回上一页*/
		backPage() {
			this.$emit('goBack');
		},
		/**主动更新webview*/
		updateWebview() {
			this.key += 1;
		},
		/**iframe 内容加载后*/
		iframeLoaded() {
			this.loading = false;
			this.$emit('load');
		},
		/**通知表单渲染区提交数据(外部调用)*/
		sendMessage(message) {
			if (this.$refs.iframe && this.$refs.iframe.contentWindow) {
				this.$refs.iframe.contentWindow.postMessage(message, '*');
			}
		}
	}
};
</script>

<style scoped>
.iframe {
	height: 100%;
	width: 100%;
}
</style>
