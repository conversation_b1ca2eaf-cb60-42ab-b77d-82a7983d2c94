<template>
	<el-dialog
		width="500px"
		top="0"
		class="desk-el-custom-dialog"
		title="表单设置"
		:close-on-click-modal="false"
		:wrapper-closable="false"
		:append-to-body="true"
		:visible.sync="show"
		@close="close"
	>
		<el-form ref="forms" :key="show" :model="forms" label-width="110px" :rules="rules">
			<el-form-item label="紧急程度" prop="urgency" class="choose-item">
				<el-select v-model="forms.urgency" plcaeholder="紧急程度" :clearable="true">
					<el-option
						v-for="(item, index) in flowUrgency"
						:key="index"
						:label="item.itemText"
						:value="item.itemValue"
					></el-option>
				</el-select>
			</el-form-item>
			<!--  v-if="isSkipDone && (status == 3 || status == 5)"     isSkipDone;-->
			<el-form-item v-if="status == 3" label="是否跳过已办" prop="isSkipDone" class="choose-item">
				<el-switch v-model="forms.isSkipDone" @change="switchChange"></el-switch>
			</el-form-item>
			<!--			<el-form-item label="紧急程度" prop="urgency" class="choose-item">-->
			<!--				<el-select v-model="forms.urgency" plcaeholder="紧急程度" :clearable="true">-->
			<!--					<el-option-->
			<!--						v-for="(item, index) in flowUrgency"-->
			<!--						:key="index"-->
			<!--						:label="item.itemText"-->
			<!--						:value="item.itemValue"-->
			<!--					></el-option>-->
			<!--				</el-select>-->
			<!--			</el-form-item>-->
			<template v-if="showNextBlock">
				<el-form-item
					v-if="nextType === 'selectNextDept'"
					label="办理部门"
					prop="selectNextDept"
					class="choose-item"
				>
					<div class="flex-box">
						<el-button plain icon="el-icon-plus" @click="showDeptSelect()">
							选择{{ nodeName }}
						</el-button>
						<div class="choose-tags">
							<el-tag
								v-for="(item, index) in deptSelect"
								:key="index"
								size="small"
								closable
								@close="deptHandleClose(index)"
							>
								{{ item.title }}
							</el-tag>
						</div>
					</div>
				</el-form-item>
				<!--				<el-form-item-->
				<!--					v-else-if="nextType === 'userTask' && nextHandler && nextHandler.length === 1"-->
				<!--					label="选择成员"-->
				<!--					prop="userLabel"-->
				<!--				>-->
				<!--					<el-select-->
				<!--						v-model="forms.userLabel"-->
				<!--						plcaeholder="请选择成员"-->
				<!--						:clearable="true"-->
				<!--						@change="changeSelect"-->
				<!--					>-->
				<!--						<el-option-->
				<!--							v-for="(item, index) in nextHandler[0].selectValue"-->
				<!--							:key="index"-->
				<!--							:label="item.label"-->
				<!--							:value="item.value"-->
				<!--						></el-option>-->
				<!--					</el-select>-->
				<!--				</el-form-item>-->
				<el-form-item
					v-else-if="nextType === 'selectNextUser'"
					label="办理人员"
					prop="assignee"
					class="choose-item"
				>
					<div class="flex-box">
						<el-button plain icon="el-icon-plus" @click="showUserSelect()">
							选择{{ nodeName }}
						</el-button>
						<div class="choose-tags">
							<el-tag
								v-for="(item, index) in nextUsers"
								:key="index"
								size="small"
								closable
								@close="nextUserDel(index)"
							>
								{{ item.title }}
							</el-tag>
						</div>
					</div>
				</el-form-item>
				<template v-else>
					<!--          多节点-->
					<el-form-item
						v-if="
							nextHandler &&
							nextHandler.length &&
							nextHandler[0].selectValue &&
							nextHandler[0].selectValue.length != 0
						"
						label="下一步处理人"
						prop="vars"
					>
						<div v-for="(items, i) in nextHandler" :key="i" class="next-item">
							<div class="node-name">节点 : {{ items.nodeName }}</div>
							<template v-if="items.multiple">
								<el-checkbox-group :key="keys" v-model="vars[items.varName]">
									<div style="display: flex; align-items: center">
										<div
											v-for="(item, index) in items.selectValue"
											:key="index"
											style="margin-right: 10px"
											:disabled="setDisabled(items)"
											@click.prevent="assigneeChange(item, items)"
										>
											<el-checkbox :label="item.value" :disabled="setDisabled(items)">
												{{ item.label }}
											</el-checkbox>
										</div>
									</div>
								</el-checkbox-group>
							</template>
							<template v-else>
								<el-radio-group :key="keys" v-model="vars[items.varName]">
									<div style="display: flex; align-items: center">
										<div
											v-for="(item, index) in items.selectValue"
											:key="index"
											style="margin-right: 10px"
											@click.prevent="assigneeChange(item, items)"
										>
											<el-radio :label="item.value">
												{{ item.label }}
											</el-radio>
										</div>
									</div>
								</el-radio-group>
							</template>
						</div>
					</el-form-item>
					<!--					<el-form-item v-else label="下一步处理人">-->
					<!--						<div v-for="(items, i) in nextHandler" :key="i" class="next-item">-->
					<!--							<div class="node-name">节点 : {{ items.nodeName }}</div>-->
					<!--							<template v-if="items.multiple">-->
					<!--								<el-checkbox-group :key="keys" v-model="assignee" @change="assigneeChange">-->
					<!--									<el-checkbox-->
					<!--										v-for="(item, index) in items.selectValue"-->
					<!--										:key="index"-->
					<!--										:label="item.value"-->
					<!--									>-->
					<!--										{{ item.label }}-->
					<!--									</el-checkbox>-->
					<!--								</el-checkbox-group>-->
					<!--							</template>-->
					<!--							<template v-else>-->
					<!--								<el-radio-group :key="keys" v-model="assignee" @change="assigneeChange">-->
					<!--									<el-radio-->
					<!--										v-for="(item, index) in items.selectValue"-->
					<!--										:key="index"-->
					<!--										:label="item.value"-->
					<!--									>-->
					<!--										{{ item.label }}-->
					<!--									</el-radio>-->
					<!--								</el-radio-group>-->
					<!--							</template>-->
					<!--						</div>-->
					<!--					</el-form-item>-->
				</template>
			</template>
		</el-form>
		<div slot="footer" class="dialog-footer" style="text-align: right">
			<el-button type="primary" :loading="loading" @click="submit">确 定</el-button>
		</div>
		<orgPersonnelDialog
			title="选择人员"
			:visible="showNextUsers"
			:init-params="initParams"
			:init-values="nextUsers"
			:data-source="userDetaSource"
			:need-all-data="true"
			:is-radio="!multiple"
			:disable-all="true"
			:can-select-depart="false"
			@sure="nextUserSure"
			@close="nextUserClose"
		></orgPersonnelDialog>
		<orgPersonnelDialog
			title="选择部门"
			:visible="chooseDeptVisiable"
			:init-params="initParams"
			:init-values="deptSelect"
			:data-source="detaSource"
			:need-all-data="true"
			:is-radio="!multiple"
			:disable-all="true"
			:include-users="false"
			:can-select-depart="canSelectDepart"
			@sure="deptSure"
			@close="deptClose"
		></orgPersonnelDialog>
	</el-dialog>
</template>
<script>
import orgPersonnelDialog from '@/components/org-personnel-dialog';
import { mapGetters } from 'vuex';
import { getFlowNext } from '@/api/modules/common';
import { get_token } from '@/utils/auth';
import { getDicts } from '@/api/modules/wait-handle';
export default {
	name: 'FormSetting',
	components: { orgPersonnelDialog },
	props: {},
	data() {
		var validateVars = (rule, value, callback) => {
			// 检查对象的每个属性是否有值
			for (let key in value) {
				if (
					value[key] === null ||
					value[key] === undefined ||
					value[key] === '' ||
					value[key].length === 0
				) {
					console.log('对象的所有属性都有值1');
					return callback(new Error('请选择'));
				}
			}
			console.log('对象的所有属性都有值');
			callback();
		};
		return {
			keys: 0,
			show: false,
			rules: {
				urgency: [{ required: true, message: '请选择紧急程度', trigger: 'change' }],
				assignee: [{ required: true, message: '请选择下一步处理人', trigger: 'change' }],
				selectNextDept: [{ required: true, message: '请选择办理部门', trigger: 'blur' }],
				vars: [{ required: true, validator: validateVars, trigger: 'change' }],
				userLabel: [{ required: true, message: '请选择办理人', trigger: 'change' }]
				// selectNextUser: [{ required: true, message: '请选择办理人', trigger: 'blur' }]
			},
			initParams: {
				authorityType: 2, //类型;1.可用成员范围,2.禁止使用范围
				applicationId: ''
			},
			userDetaSource: ['depart'],
			detaSource: ['depart'],
			forms: {
				urgency: '',
				assignee: '',
				selectNextDept: '',
				vars: {},
				userLabel: '' // userTask时下拉选择值
			},
			vars: {}, // 节点选值映射
			nextHandler: [],
			assignee: [],
			nextType: '',
			deptSelect: [],
			showNextUsers: false,
			varName: '',
			nextUsers: [],
			canSelectDepart: true,
			chooseDeptVisiable: false,
			loading: false,
			nodeName: '',
			flowUrgency: [],
			flowObj: {},
			openParams: {},
			showNextBlock: false,
			status: '',
			isSkipDone: false,
			multiple: false
		};
	},
	computed: {
		...mapGetters(['userInfo'])
	},
	mounted() {
		getDicts('flow_urgency').then(res => {
			this.flowUrgency = res.result.flow_urgency.reverse();
		});
	},
	methods: {
		/**下拉选择*/
		changeSelect(value) {
			this.vars[this.varName] = value;
		},
		setDisabled(items) {
			return !items.nodeConfig.action.isMultipleOptional;
		},
		// 重新获取下一步
		switchChange() {
			let { id, params, businessId, processInstanceId } = this.openParams;
			this.flowObj = {
				businessId,
				processInstanceId,
				isSkipDone: this.forms.isSkipDone
			};
			this.check(id, params, businessId, processInstanceId, true);
		},
		/**检查是否打开下一步*/
		async check(id, params, businessId, processInstanceId, isSkipDoneType) {
			let data = {
				createUser: this.userInfo.id,
				id: id,
				businessId,
				processInstanceId,
				isSkipDone: this.forms.isSkipDone || false,
				tenantId: get_token('X-Coos-Client-Tenant-Id'),
				vars: params
			};
			let res = await getFlowNext(data);
			if (res.code === 200) {
				// 如果下一级节点为两个或者一个节点但是有多个办理人，打开弹窗显示
				if (res.result.length > 0) {
					this.showNextBlock = true;
					this.init(res, isSkipDoneType);
				}
			} else {
				this.$message.error(res.message);
			}
		},
		openData(res, flowObj) {
			this.flowObj = flowObj; // 业务id 流程实例id  是否跳过已办
			this.show = true;
			this.showNextBlock = true;
			this.init(res);
		},
		/**初始化节点选择*/
		init(res, isSkipDoneType) {
			this.$nextTick(() => {
				this.forms.urgency = this.flowUrgency[0].itemValue;
			});
			// 如果是多个的.比如是平行任务办理人..那就需要循环去选人/部门...-_-
			res.result.forEach(item => {
				if (item.varName) {
					this.vars[item.varName] = null;
				}
			});
			let nextHandler = res.result;
			this.status = nextHandler[0].flowStatus; // 数据状态
			let userList = nextHandler.map(v => {
				let selectValue = [];
				try {
					Object.keys(v.selectValue).map(vs => {
						selectValue.push({
							label: v.selectValue[vs],
							value: vs
						});
					});
				} catch (e) {
					selectValue = [];
				}
				let item = { ...v };
				item.selectValue = selectValue;
				if (v.varName) {
					if (v.multiple) {
						if (v.nodeConfig.action.isMultDefaultSelected) {
							this.vars[v.varName] = selectValue.map(event => {
								return event.value;
							});
						} else {
							this.vars[v.varName] = [];
						}
					} else {
						this.vars[v.varName] = selectValue.length ? selectValue[0].value : '';
					}
				}
				return item;
			});
			if (res.result.length > 1) {
				this.nextHandler = userList;
				// res.result.forEach(item => {
				// 	if (item.varName) {
				// 		this.vars[item.varName] = null;
				// 	}
				// });
				// let nextHandler = res.result;
				// this.nextHandler = nextHandler.map(v => {
				// 	let selectValue = [];
				// 	Object.keys(v.selectValue).map(vs => {
				// 		selectValue.push({
				// 			label: v.selectValue[vs],
				// 			value: vs
				// 		});
				// 	});
				// 	let item = { ...v };
				// 	item.selectValue = selectValue;
				// 	if (v.varName) {
				// 		this.vars[v.varName] = selectValue[0].value;
				// 	}
				// 	return item;
				// });
			} else {
				let v = res.result[0];

				// 一个节点
				this.nextType = res.result[0].type;
				this.multiple = res.result[0].multiple ? true : false;
				this.nodeName = res.result[0].nodeName;
				// const selectValue = { ...res.result[0].selectValue };
				// let userList = Object.keys(selectValue).map(v => {
				// 	return {
				// 		label: selectValue[v],
				// 		value: v
				// 	};
				// });
				// let arr = [];
				// Object.keys(selectValue).map(vs => {
				// 	arr.push({
				// 		label: selectValue[vs],
				// 		value: vs
				// 	});
				// });
				// userList = userList.map(item => {
				// 	return {
				// 		...item,
				// 		nodeName: this.nodeName,
				// 		multiple: this.multiple,
				// 		selectValue: arr
				// 	};
				// });
				// console.log('userList:', userList);
				// else if (this.nextType === 'userTask') {
				//     this.nextHandler = [...userList];
				//     if (userList.selectValue.length == 1) {
				//       this.showNextBlock = false;
				//     }
				//   }
				if (this.nextType !== 'multipleTask' && res.result[0].varName) {
					this.vars[res.result[0].varName] = null;
					this.varName = res.result[0].varName;
				}
				if (this.nextType === 'selectNextUser') {
					// this.userLabelList = [...userList];
				} else if (this.nextType === 'selectNextDept') {
					// this.nextHandler = [...userList];
				} else if (this.nextType === 'multipleTask') {
					// 一个节点 多实例选人
					this.nextHandler = [...userList];
					// 多实例选择人员赋值
					this.assignee = userList[0].selectValue.map(item => {
						return item.value;
					});
					this.varName = res.result[0].varName;
					this.forms.assignee = this.assignee.join(',');
					if (v.nodeConfig.action.isMultDefaultSelected) {
						this.vars[this.varName] = this.assignee;
					} else {
						this.vars[this.varName] = [];
					}
				} else {
					// 一个节点 选人
					this.nextHandler = [...userList];
					this.assignee = userList[0].selectValue.map(item => {
						return item.value;
					});
					this.forms.assignee = this.assignee.join(',');
					this.varName = res.result[0].varName;
					if (!this.multiple) {
						// 单选
						const firstSelectValue = userList[0]?.selectValue?.[0];
						this.assignee = firstSelectValue ? firstSelectValue.value : '';
					}
					if (this.varName) {
						// 参数
						if (v.nodeConfig.action.isMultDefaultSelected) {
							this.vars[this.varName] = this.assignee;
						} else {
							this.vars[this.varName] = [];
						}
					}
				}
				// ai节点 并且没有选人时间
				try {
					let obj = this.nextHandler[0] || {};
					if (obj.selectValue.length == 0 && obj.userTaskProperties.nodeType == 2) {
						this.vars = {};
					}
				} catch (error) {
					this.vars = {};
				}
			}
			if (this.status == 3 && !isSkipDoneType) {
				this.forms.isSkipDone = this.nextHandler.every(
					item => item.nodeConfig.action.isSkipDone === true
				);
				this.switchChange();
			}
		},
		/**提交*/
		submit() {
			this.$refs.forms.validate(valid => {
				if (valid) {
					let isEmptyForm = false;
					if (Object.keys(this.vars).length > 0) {
						Object.keys(this.vars).forEach(key => {
							if (!this.vars[key]) {
								isEmptyForm = true;
							}
						});
					}
					const parmas = { urgency: this.forms.urgency, vars: this.vars, ...this.flowObj };
					if (this.nextType === 'selectNextDept') {
						delete parmas.vars;
						parmas.deptCandidate = this.forms.selectNextDept;
					}
					// if (this.nextType === 'selectNextUser') {
					// 	parmas.claimUserId = id;
					// }
					console.log(isEmptyForm, this.showNextBlock);
					if (!isEmptyForm || !this.showNextBlock) {
						this.$emit('nextSelected', parmas);
						this.show = false;
					} else {
						this.$message.error('请选择下一步处理人');
					}
				}
			});
		},
		/**打开选人组件*/
		showUserSelect() {
			this.showNextUsers = true;
		},
		toggleElementInArray(array, element) {
			const index = array.indexOf(element);
			if (index > -1) {
				// 如果元素已存在，则删除
				array.splice(index, 1);
			} else {
				// 如果元素不存在，则添加
				array.push(element);
			}
		},

		/**多节点选择*/
		assigneeChange(row, items) {
			let e = row.value || '';
			if (items.multiple) {
				if (this.setDisabled(items)) {
					return;
				}
				let arr = [...(this.vars[items.varName] || [])];
				this.toggleElementInArray(arr, e);
				// if (items.type === 'multipleTask') {
				this.forms.assignee = arr.join(',');
				this.vars[items.varName] = arr;
				this.forms.vars[items.varName] = arr;
				// } else {
				// 	this.forms.assignee = arr.join(',');
				// 	this.vars[items.varName] = arr.join(',');
				// }
			} else {
				//单选
				// vars[e] = this.nextHandler[e];
				this.vars[items.varName] = e;
				this.forms.vars[items.varName] = e;
				this.forms.assignee = e;
			}
			console.log(this.forms, 'vvvv');
			this.keys += 1;
			this.$refs.forms.validateField('vars', error => {
				console.log(error, 'error');
			});
		},
		/**确定选人组件*/
		nextUserSure(e) {
			this.nextUsers = [...e];
			if (e.length) {
				if (this.multiple) {
					const id = e.map(v => {
						return v.id;
					});
					this.forms.assignee = id.join();
					this.vars[this.varName] = id;
				} else {
					this.forms.assignee = e[0].id;
					this.vars[this.varName] = e[0].id;
				}

				this.$refs.forms.clearValidate();
			} else {
				this.forms.assignee = '';
				this.vars[this.varName] = '';
			}
			this.showNextUsers = false;
		},
		/**关闭选人组件*/
		nextUserClose() {
			this.showNextUsers = false;
		},
		/**删除选择人*/
		nextUserDel() {
			this.forms.assignee = '';
			this.vars[this.varName] = '';
			this.nextUsers = [];
		},
		/**打开选择部门组件*/
		showDeptSelect() {
			this.chooseDeptVisiable = true;
		},
		/**确认选择部门组件*/
		deptSure(e) {
			this.deptSelect = [...e];
			if (e.length) {
				if (this.multiple) {
					const id = e.map(v => {
						return v.id;
					});
					this.forms.selectNextDept = id.join();
					this.vars[this.varName] = id.join();
				} else {
					this.forms.selectNextDept = e[0].id;
					this.vars[this.varName] = e[0].id;
				}

				this.$refs.forms.clearValidate();
			} else {
				this.forms.selectNextDept = null;
				this.vars[this.varName] = e[0].id;
			}
			this.chooseDeptVisiable = false;
		},
		/**关闭选择部门组件*/
		deptClose() {
			this.chooseDeptVisiable = false;
		},
		/**删除部门选择*/
		deptHandleClose(index) {
			this.deptSelect.splice(index, 1);
			let dataValue = [];
			this.deptSelect.map(v => {
				dataValue.push(v.id);
			});
			this.forms.selectNextDept = null;
		},
		open(id, params, businessId, processInstanceId, status) {
			this.flowObj = {
				// 流程参数提交是需要
				businessId,
				processInstanceId,
				isSkipDone: false
			};

			this.openParams = { id, params, businessId, processInstanceId }; // 保存参数、 切换是否跳过已办需要重新调接口
			this.check(id, params, businessId, processInstanceId); // 获取下一步办理 数据
			this.show = true;
		},
		close() {
			this.$refs.forms.resetFields();
			this.vars = {};
			this.nextType = '';
			this.nextUsers = [];
			this.deptSelect = [];
		}
	}
};
</script>
<style lang="scss" scoped>
.choose-item {
	position: relative;
}
.choose-tags {
	display: flex;
	grid-gap: 4px;
}
</style>
