import { PolygonNode, PolygonNodeModel, h } from '@logicflow/core';
const statusColor = { current: '#1890ff', selected: '#2A9D8F' };

class InclusivekView extends PolygonNode {
	getShape() {
		const { model } = this.props;
		const { x, y, width, height, points, properties } = model;
		const style = model.getNodeStyle();
		if (properties.status) {
			style.stroke = statusColor[properties.status];
		}
		return h(
			'g',
			{
				transform: `matrix(1 0 0 1 ${x - width / 2} ${y - height / 2})`
			},
			h('polygon', {
				...style,
				x,
				y,
				points
			}),
			h('circle', {
				cx: 25,
				cy: 25,
				r: 13,
				style: `stroke-linecap: round; stroke-linejoin: round; stroke: ${style.stroke}; stroke-width: 2.5px; fill: white;`
			})
		);
	}
	getNodeStyle() {
		const style = super.getNodeStyle();
		// style.strokeDasharray = '3 3';
		const { properties } = this;
		if (properties.status) {
			style.stroke = statusColor[properties.status];
		}
		return style;
	}
}

class InclusivekModel extends PolygonNodeModel {
	constructor(data, graphModel) {
		data.text = {
			value: '',
			x: data.x,
			y: data.y + 40
		};
		super(data, graphModel);
		this.points = [
			[25, 0],
			[50, 25],
			[25, 50],
			[0, 25]
		];
	}
}

export default {
	type: 'inclusiveGateway',
	view: InclusivekView,
	model: InclusivekModel
};
