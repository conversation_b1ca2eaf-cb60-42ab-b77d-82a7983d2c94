// theme.js
const theme = {
	// 节点的基础样式
	baseNode: {
		fill: '#F1FAEE', // 柔和的浅橙色背景
		stroke: '#2A9D8F', // 柔和的绿色边框
		strokeDasharray: 'none' // 实线边框
	},

	// 矩形节点的样式
	rect: {
		fill: '#F1FAEE', // 柔和的白色背景
		strokeDasharray: 'none', // 实线边框
		radius: 8 // 边框圆角半径
	},

	// 圆形节点的样式
	circle: {
		r: 20, // 圆的半径
		fill: '#F1FAEE', // 浅蓝色背景
		stroke: '#2A9D8F', // 边框颜色
		strokeWidth: 2, // 边框宽度
		strokeDasharray: 'none', // 实线边框
		shadow: {
			// 阴影效果
			color: '#888888', // 阴影颜色
			blur: 4, // 模糊半径
			offsetX: 2, // 水平偏移
			offsetY: 2 // 垂直偏移
		}
	},

	// 菱形节点的样式
	diamond: {
		fill: '#457B9D', // 深蓝色背景
		width: 100, // 菱形的宽度
		height: 100 // 菱形的高度
	},

	// 文字样式
	text: {
		fontSize: 14, // 文字的字体大小
		color: '#1D3557' // 深蓝色文字
	},

	// 边的样式
	edge: {
		strokeWidth: 2, // 边的宽度
		stroke: '#2A9D8F', // 绿色边框
		color: '#2A9D8F' // 连接线的颜色（如果需要单独指定，可以添加此行）
	},

	// 箭头样式
	arrow: {
		offset: 8, // 箭头的长度
		fill: 'none', // 箭头的填充颜色
		stroke: '#2A9D8F' // 绿色边框
	},

	// 折线连接线样式
	polyline: {
		stroke: '#2A9D8F', // 连接线颜色
		strokeWidth: 2, // 连接线宽度
		strokeDasharray: 'none' // 实线连接线
	},

	// 贝塞尔曲线连接线样式
	bezier: {
		stroke: '#2A9D8F', // 连接线颜色
		strokeWidth: 2, // 连接线宽度
		strokeDasharray: 'none' // 实线连接线
	}
};

// 导出主题配置
export default theme;
