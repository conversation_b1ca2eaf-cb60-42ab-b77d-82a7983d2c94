<template>
	<div v-loading="flowLoading" class="flow-box">
		<div v-print="printOrderObject" class="print" @click="print">
			<i class="el-icon-printer"></i>
			打印
		</div>
		<el-timeline style="width: 60%; margin: 0 auto">
			<!--
			<el-timeline-item
				timestamp="流程开始"
				placement="top"
				:color="nodeColor[1]"
				icon="el-icon-check"
				class="node-type1"
			></el-timeline-item>
		-->
			<template v-for="(item, index) in flowRecordList">
				<el-timeline-item
					:key="index"
					:timestamp="item.name || item.flowInfo.name"
					placement="top"
					:color="nodeColor[item.nodeType]"
					:icon="!item.isAi ? 'el-icon-check' : 'icon-coos-avatar'"
					:class="`node-type${item.nodeType}`"
				>
					<el-card>
						<div class="flow-card">
							<div class="flow-item">
								<div class="flow-label">
									<i class="el-icon-user"></i>
									<span>{{ item['flowable:nodeType'] == 3 ? '抄送至' : '办理人' }} :</span>
								</div>
								<div class="flow-value">{{ item.assignee || item.flowInfo.assignee || '-' }}</div>
							</div>
							<div v-if="item.owner" class="flow-item">
								<div class="flow-label">
									<i class="el-icon-user"></i>
									<span>任务所属 :</span>
								</div>
								<div class="flow-value">{{ item.owner || '-' }}</div>
							</div>
							<div
								v-if="setShow(item)"
								class="flow-item"
								:style="{ flexDirection: item.isAi ? 'column' : '' }"
							>
								<div class="flow-label">
									<i class="el-icon-s-check"></i>
									<span>{{ item['flowable:nodeType'] == 3 ? '批注内容' : '审批意见' }} :</span>
								</div>
								<div style="width: 75%">
									<div v-for="(event, eventIndex) of item.comment" :key="'event' + eventIndex">
										<template v-if="event.type == 2">
											<div v-if="event.isTable" style="width: 100%">
												<div style="flex: 1">
													<table class="flow-table">
														<tr>
															<th>序号</th>
															<th>问题</th>
															<th>是否通过</th>
														</tr>
														<tr v-for="(que, i) in event.aiQuestion" :key="i" class="flow-value">
															<td width="48px">
																{{ Number(i) + 1 }}
															</td>
															<td
																:style="{
																	color: '#3A546C',
																	textAlign: 'left',
																	padding: '10px 20px'
																}"
															>
																{{ que.question }}
															</td>
															<td
																:style="{
																	color: que.answer == 'true' ? '#2ca65e' : '#d60000',
																	textAlign: 'center'
																}"
															>
																<i
																	:class="[
																		que.answer == 'true' ? 'el-icon-success' : 'el-icon-error'
																	]"
																></i>
															</td>
														</tr>
													</table>
												</div>
											</div>
											<div v-else class="flow-text" style="width: 100%">
												人工审核：{{ event.comment }}
											</div>
										</template>
										<div v-else class="flow-value">{{ event.comment || '-' }}</div>
									</div>
								</div>
							</div>
							<div v-if="firstNodeId == item.taskDefinitionKey" class="flow-item">
								<div class="flow-label">
									<i class="el-icon-time"></i>
									发起时间 :
								</div>
								<div class="flow-value">
									{{ item.startTime || item.createTime || item.flowInfo.createTime }}
								</div>
							</div>
							<div
								v-if="item.nodeType == 1 && firstNodeId != item.taskDefinitionKey && item.comment"
								class="flow-item"
							>
								<div class="flow-label">
									<i class="el-icon-time"></i>
									<span>{{ item['flowable:nodeType'] == 3 ? '批注时间' : '办理时间' }} :</span>
								</div>
								<div class="flow-value">{{ item.endTime }}</div>
							</div>
							<div v-for="(node, indexs) in item.duplicateList" :key="indexs" class="duplica-list">
								<div class="flow-item">
									<div class="flow-label">
										<i class="el-icon-user"></i>
										抄送至:
									</div>
									<div class="flow-value">{{ node.assigneeName || '-' }}</div>
								</div>
								<div v-if="node.comment" class="flow-item">
									<div class="flow-label">
										<i class="el-icon-s-check"></i>
										批注内容:
									</div>
									<div class="flow-value">{{ node.comment || '-' }}</div>
								</div>
								<div v-if="node.comment" class="flow-item">
									<div class="flow-label">
										<i class="el-icon-time"></i>
										批注时间:
									</div>
									<div class="flow-value">{{ node.comment ? node.updateTime : '-' }}</div>
								</div>
							</div>
						</div>
					</el-card>
				</el-timeline-item>
			</template>
		</el-timeline>
		<!-- <flow-print
			v-if="flowRecordList.length > 0 && !printConfig.formConfig"
			ref="flowPrint"
			:flow-record="flowRecordList"
			:first-node-id="firstNodeId"
		/> -->
		<!--新打印需自己组装表单数据传进来-->
		<flow-print-new
			id="printArea"
			:print-config="printConfig"
			:first-node-id="firstNodeId"
			:circulation-list="circulationList"
		/>
	</div>
</template>
<script>
import {
	selectListHis,
	selectListActivity,
	selectList,
	getFlow,
	findByProcessInstance,
	getNodeConfig
} from '@/api/modules/flow-design';
// import flowPrint from './flow-print.vue';
import flowPrintNew from './flow-print-new.vue';
import { get_token } from '@/utils/auth';
import { CoosEventTypes } from '@/utils/bus';
import Print from 'vue-print-nb';

export default {
	name: 'FlowRecords',
	components: { flowPrintNew },
	directives: {
		Print
	},
	props: {
		flowRecord: {
			type: Array,
			default: () => {
				return [];
			}
		},
		processInstanceId: {
			type: String,
			default: ''
		},
		formId: {
			type: String,
			default: ''
		}
		// 打印的表单配置
		// printConfig: {
		// 	type: Object,
		// 	default: null
		// }
	},
	data() {
		return {
			printConfig: {}, // 打印的表单配置
			flowRecordList: [],
			isLoading: false,
			flowLoading: false,
			flowId: '',
			nodeDatas: {},
			nodeColor: ['var(--brand-6)', '#2ca65e'],
			firstNodeId: null,
			printOrderObject: {
				id: 'printArea', // 打印区域的id
				importStyle: true, // 引入样式
				manualTrigger: true // 手动触发打印
			},
			circulationList: [] // 流转记录
		};
	},
	watch: {
		processInstanceId: function (val) {
			if (val) {
				this.findByFormIdRecord(val);
			}
		}
	},
	mounted() {
		this._BUS.$on(CoosEventTypes.responseChildFormData, this.printData);
		if (this.processInstanceId) {
			this.findByFormIdRecord(this.processInstanceId);
		}
	},
	destroyed() {
		this._BUS.$off(CoosEventTypes.responseChildFormData, this.printData);
	},
	methods: {
		setShow(item) {
			try {
				return item.comment && item.comment.length && item.comment[0].comment;
			} catch (error) {
				return false;
			}
		},
		async findByFormIdRecord(val) {
			this.flowLoading = true;
			const params = {
				desc: false,
				pageNo: 1,
				pageSize: 1000,
				processInstanceId: val
			};
			const hisRes = await selectListHis({
				...params
			});
			const flowRecordList = hisRes.result?.records;

			if (!this.flowId && flowRecordList.length > 0) {
				this.flowId = flowRecordList[0].workbenchVO.flowId;
			}

			const flowRecord = {};
			flowRecordList.map(v => {
				flowRecord[v.taskDefinitionKey] = {
					...v
				};
			});

			// 已完成的所有节点
			const hisAllRes = await selectListActivity({
				...params
			});
			const allRecords = hisAllRes.result?.records;

			// console.log('已完成的所有节点:', allRecords);
			if (allRecords.length) {
				this.firstNodeId = allRecords[2].activityId;
			}

			const re = flowRecordList.reduce((acc, curr) => {
				curr.hide = true;
				curr.nodeType = 1;
				acc.push({
					...curr
				});
				return acc;
			}, []);

			// 当前待办任务节点
			const currRes = await selectList(params);
			// let currResRe = [];
			if (currRes.code == 200 && currRes.result && currRes.result.records) {
				const records = currRes.result.records;

				if (records.length > 0) {
					// 两种情况
					// 返回都是多个待办任务节点
					// 如果是同一个任务,就需要把assignee拼成一个节点

					const keyMap = {};
					// 遍历数组
					records.forEach(item => {
						const taskDefinitionKey = item.flowInfo.taskDefinitionKey;
						item.hide = true;
						item.nodeType = 0;
						if (keyMap[taskDefinitionKey]) {
							const assignee = item.flowInfo.assignee;
							keyMap[taskDefinitionKey].flowInfo.assignee += `, ${assignee}`;
						} else {
							keyMap[taskDefinitionKey] = item;
						}
					});

					// 将结果转换回数组
					const rebuild = Object.keys(keyMap).map(key => {
						return keyMap[key];
					});
					// console.log('rebuild:', rebuild);

					// // 调用函数并输出结果

					// currResRe = records.reduce((acc, curr) => {
					// 	curr.hide = true;
					// 	curr.nodeType = 0;
					// 	const found = acc.find(
					// 		item => item.flowInfo.taskDefinitionKey === curr.flowInfo.taskDefinitionKey
					// 	);

					// 	if (found) {
					// 		found.assignee += `,${curr.flowInfo.assignee}`;
					// 		found.createTime = curr.flowInfo.createTime;
					// 	} else {
					// 		acc.push({
					// 			assignee: curr.flowInfo.assignee,
					// 			createTime: curr.flowInfo.createTime,
					// 			...curr
					// 		});
					// 	}
					// 	return acc;
					// }, []);
					// console.log('currResRe:', currResRe[0]);
					re.push(...rebuild);
				}
			}

			this.circulationList = re;
			getFlow({ flowId: this.flowId, processInstanceId: this.processInstanceId }).then(resp => {
				const jsonContent = JSON.parse(resp.result.config.jsonContent);
				const { nodes = [] } = jsonContent || {};
				let aiContent = [];
				nodes.map(v => {
					if (v.properties['flowable:nodeType'] == 3) {
						re.map(vv => {
							if (v.id == vv.taskDefinitionKey) {
								vv['flowable:nodeType'] = 3;
							}
							return vv;
						});
					}
				});
				nodes.map(v => {
					if (v.properties['flowable:nodeType'] == 2) {
						re.map(vv => {
							if (vv.comment && vv.comment.length) {
								vv.comment = vv.comment.map(item => {
									// if (v.id == vv.taskDefinitionKey && item.comment) {
									item.isAi = true;
									const comment = item.comment;
									let AIComment = '';
									if (comment.split('：').length > 1) {
										AIComment = comment.split('：')[1]?.split(',');
									} else {
										AIComment = comment.split(',');
									}
									item.isTable = comment.indexOf('|') == -1 ? false : true;
									const aiComments = AIComment.map(v => {
										return {
											question: v.split('|')[0],
											answer: v.split('|')[1]
										};
									});
									item.aiQuestion = { ...aiComments };
									// 这里相当于显示最后一个ai审核
									aiContent = aiComments;
									// }
									return item;
								});
							}
							return vv;
						});
					}
				});

				this.flowLoading = false;
				this.flowRecordList = [...re];
				this.$emit('showFlowRecordList', flowRecordList);
				// console.log('this.flowRecordList:', this.flowRecordList);
				// 有待办节点以及 有ai审核
				if (currRes.result.records.length > 0 && aiContent.length) {
					if (this.flowRecordList.length >= 2) {
						const { flowRecordList } = this;
						//会不会存在多实例的问题?
						// 如果最后一个节点是待办,判断上一个节点是否为AI节点,
						// ==> 是AI节点,才展示AI审核内容,
						// ==> 不是AI节点,就不需要展示审核内容
						// ==> 防止驳回
						flowRecordList.forEach((v, index) => {
							if (v.comment && v.comment.length) {
								v.comment.forEach((event, index) => {
									if (event.isAi) {
										this.$emit('showAIcontent', aiContent);
									}
								});
							}
						});
					}
				}

				this.findByProcessInstance();
			});

			// const records = re.map(v => {
			// 	if (keyMap[key].comment && keyMap[key].name.indexOf('AI')) {
			// 		//大概格式 驳回；原因：申请日期是2024年12月吗|false,申请日期是2024年月吗|false
			// 		const comment = keyMap[key].comment;
			// 		const AIComment = comment.split('：')[1]?.split(',');
			// 		console.log(AIComment);
			// 	}

			// 	return v;
			// });
		},
		findByProcessInstance() {
			const tenantId = get_token('X-Coos-Client-Tenant-Id');
			findByProcessInstance(this.processInstanceId, tenantId).then(res => {
				if (res.code == 200) {
					const result = res.result || [];
					if (result.length > 0) {
						console.log('抄送的节点:', result);
						const { flowRecordList } = this;
						result.map(v => {
							flowRecordList.map(async f => {
								if (v.taskId == f.id) {
									if (f['flowable:nodeType'] == 3) {
										const configRe = await getNodeConfig({
											nodeId: f.taskDefinitionKey,
											processDefinitionId: f.processDefinitionId
										});
										console.log('configRe:', configRe.result.node.properties.nodeBaseForm);
										const isMulti = configRe.result.node.properties.nodeBaseForm.isMulti;
										// 如果是多实例
										if (isMulti) {
											//
											if (!f.isMulti) {
												f.isMulti = isMulti;
												f.comment = v.comment;
												f.endTime = v.updateTime;
											} else {
												if (f.duplicateList && f.duplicateList.length) {
													f.duplicateList.push(v);
												} else {
													f.duplicateList = [];
													f.duplicateList.push(v);
												}
											}
										} else {
											f.comment = v.comment;
										}
										this.$forceUpdate();
										// getNodeConfig(f.taskDefinitionKey).then(re => {
										// 	// 获取指定节点的属性
										// 	if (re.code == 200 && re.result) {
										// 		console.log('re.result.node.properties:', re.result.node.properties);
										// 		const otherProperties = { ...re.result.node.properties.otherProperties };
										// 	}
										// });
									} else {
										// 手动抄送的,就需要把抄送信息附加上去
										if (f.duplicateList && f.duplicateList.length) {
											f.duplicateList.push(v);
										} else {
											f.duplicateList = [];
											f.duplicateList.push(v);
										}
									}
								}
							});
						});
						console.log('flowRecordList:', flowRecordList);

						this.flowRecordList = [...flowRecordList];
					}
				}
			});
		},
		printData(data) {
			this.printConfig = data;
		},
		print() {
			this._BUS.$emit(CoosEventTypes.getChildFormData); // 打印前获取打印数据
		}
	}
};
</script>
<style lang="scss" scoped>
.node-type1 {
	::v-deep {
		.el-timeline-item__timestamp {
			color: #2ca65e;
		}
	}
}
.node-type0 {
	::v-deep {
		.el-timeline-item__timestamp {
			color: var(--brand-6);
		}
	}
}
.flow-box {
	padding: 0 10px;
	margin: 0 auto;
	min-height: 200px;
	position: relative;
}
.flow-card {
	display: flex;
	grid-gap: 10px;
	flex-direction: column;
	.flow-item {
		display: flex;
		grid-gap: 10px;
		.flow-label {
			width: 85px;
			display: flex;
			grid-gap: 5px;
			align-items: center;
		}
		.flow-value {
			flex: 1;
		}
	}
}
.duplica-list {
	display: flex;
	grid-gap: 10px;
	flex-direction: column;
}
.print {
	position: absolute;
	right: 20px;
	top: 0;
	cursor: pointer;
	background: var(--brand-6, '#0f45ea');
	color: #fff;
	padding: 6px 16px;
	border-radius: 6px;
}
.flow-table {
	width: 100%;
	border-spacing: 0;
	th {
		background: #edf2f6;
		padding: 10px 0;
		text-align: center;
		border: 1px solid #cfe1ef;
		font-weight: bold;
	}
	td {
		padding: 10px 8px;
		border: 1px solid #cfe1ef;
	}
}
::v-deep {
	.icon-coos-avatar {
		background: url('~@/assets/them-coos/coos/coos-avatar.png') no-repeat center;
		background-size: 14px 14px;
		height: 14px;
		width: 14px;
		border-radius: 50%;
	}
}
.flow-text {
	margin-top: 18px;
}
</style>
