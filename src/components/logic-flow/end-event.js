import { CircleNode, CircleNodeModel } from '@logicflow/core';
const statusColor = { current: '#1890ff', selected: '#2A9D8F' };
class EndModel extends CircleNodeModel {
	constructor(data, graphModel) {
		data.text = {
			value: data.text || '结束',
			x: data.x,
			y: data.y + 40
		};
		super(data, graphModel);

		this.r = 20;
	}
	getNodeStyle() {
		const style = super.getNodeStyle();
		style.strokeWidth = 4;
		const { properties } = this;
		if (properties.status) {
			style.stroke = statusColor[properties.status];
		}
		return style;
	}
	getConnectedSourceRules() {
		const rules = super.getConnectedSourceRules();
		const notAsTarget = {
			message: '终止节点不能作为连线的起点',
			validate: () => false
		};
		rules.push(notAsTarget);
		return rules;
	}
}

export default {
	type: 'endEvent',
	view: CircleNode,
	model: EndModel
};
