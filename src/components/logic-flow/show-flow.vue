<template>
	<div v-loading="loading" style="position: relative">
		<div id="flowContainer" ref="flowContainer" class="flowContainer"></div>
		<div v-show="showInfo" class="node-info" :style="{ top: `${clientY}px`, left: `${clientX}px` }">
			<div class="title">节点信息</div>
			<div class="node-item">
				<div class="node-label">节点名称:</div>
				<div class="node-ctn">{{ nodeData.nodeName }}</div>
			</div>

			<template v-if="nodeData.activityId">
				<template v-if="nodeData.nodeType == 3 && nodeData.hanldeNodes">
					<template v-for="(item, index) in nodeData.duplicateList">
						<div v-if="true" :key="index" class="duplicate-box">
							<div class="node-item">
								<div class="node-label">抄送至:</div>
								<div class="node-ctn">{{ item.assigneeName || '-' }}</div>
							</div>
							<div class="node-item">
								<div class="node-label">批注内容:</div>
								<div class="node-ctn">{{ item.comment || '-' }}</div>
							</div>
							<div v-if="item.comment" class="node-item">
								<div class="node-label">批注时间:</div>
								<div class="node-ctn">{{ item.updateTime || '-' }}</div>
							</div>
						</div>
					</template>
				</template>
				<template v-else-if="nodeData.hanldeNodes">
					<div v-for="(node, index) in nodeData.hanldeNodes" :key="index" class="muti-box">
						<template v-if="node.isHanlde">
							<div class="node-item current">
								<div class="node-label">待办人:</div>
								<div class="node-ctn">{{ calcName(node.assignee) || '-' }}</div>
							</div>
						</template>
						<template v-else>
							<div class="node-item">
								<div class="node-label">办理人:</div>
								<div class="node-ctn">{{ node.assignee || '-' }}</div>
							</div>

							<div class="node-item">
								<div class="node-label">审核意见:</div>
								<div class="node-ctn">{{ setComment(node.comment) || '-' }}</div>
							</div>
							<div class="node-item">
								<div class="node-label">办理时间:</div>
								<div class="node-ctn">{{ node.endTime || '-' }}</div>
							</div>
						</template>

						<template v-for="(item, i) in node.duplicateList">
							<div v-if="true" :key="i" class="duplicate-box">
								<div class="node-item">
									<div class="node-label">抄送至:</div>
									<div class="node-ctn">{{ item.assigneeName || '-' }}</div>
								</div>
								<div class="node-item">
									<div class="node-label">批注内容:</div>
									<div class="node-ctn">{{ item.comment || '-' }}</div>
								</div>
								<div v-if="item.comment" class="node-item">
									<div class="node-label">批注时间:</div>
									<div class="node-ctn">{{ item.updateTime || '-' }}</div>
								</div>
							</div>
						</template>
					</div>
				</template>
				<template v-else>
					<div class="node-item">
						<div class="node-label">{{ nodeData.nodeType == 3 ? '抄送至' : '办理人' }}:</div>
						<div class="node-ctn">{{ nodeData.assignee || '-' }}</div>
					</div>
					<div class="node-item" :style="{ flexDirection: nodeData.isAi ? 'column' : '' }">
						<div class="node-label">{{ nodeData.nodeType == 3 ? '批注内容' : '审核意见' }}:</div>
						<template v-if="nodeData.isAi">
							<div style="flex: 1">
								<table class="flow-table">
									<tr>
										<th width="48px">序号</th>
										<th>问题</th>
										<th width="70px">是否通过</th>
									</tr>
									<tr v-for="(que, i) in nodeData.aiQuestion" :key="i" class="flow-value">
										<td>
											{{ Number(i) + 1 }}
										</td>
										<td
											:style="{
												color: que.answer == 'true' ? '#2ca65e' : '#d60000',
												textAlign: 'left',
												padding: '2px 5px'
											}"
										>
											{{ que.question }}
										</td>
										<td :style="{ color: que.answer == 'true' ? '#2ca65e' : '#d60000' }">
											<i :class="[que.answer == 'true' ? 'el-icon-success' : 'el-icon-error']"></i>
										</td>
									</tr>
								</table>
							</div>
						</template>
						<div v-else class="node-ctn">{{ setComment(nodeData.comment) || '-' }}</div>
					</div>
					<div v-if="nodeData.isFirst" class="node-item">
						<div class="node-label">办理时间:</div>
						<div class="node-ctn">{{ nodeData.endTime || nodeData.startTime || '-' }}</div>
					</div>

					<div v-else class="node-item">
						<div class="node-label">{{ nodeData.nodeType == 3 ? '批注时间' : '办理时间' }}:</div>
						<div class="node-ctn">{{ nodeData.endTime || '-' }}</div>
					</div>
					<template v-for="(item, index) in nodeData.duplicateList">
						<div v-if="true" :key="index" class="duplicate-box">
							<div class="node-item">
								<div class="node-label">抄送至:</div>
								<div class="node-ctn">{{ item.assigneeName || '-' }}</div>
							</div>
							<div class="node-item">
								<div class="node-label">批注内容:</div>
								<div class="node-ctn">{{ item.comment || '-' }}</div>
							</div>
							<div v-if="item.comment" class="node-item">
								<div class="node-label">批注时间:</div>
								<div class="node-ctn">{{ item.updateTime || '-' }}</div>
							</div>
						</div>
					</template>
				</template>
			</template>
			<template v-else>
				<div class="node-item">
					<div class="node-label">待办人:</div>
					<div class="node-ctn">{{ calcName(nodeData.assignee) }}</div>
				</div>
				<!-- <div class="node-item">
					<div class="node-label">创建时间:</div>
					<div class="node-ctn">{{ nodeData.createTime || '-' }}</div>
				</div> -->
			</template>
		</div>
	</div>
</template>
<script>
import LogicFlow from '@logicflow/core';
import '@logicflow/core/dist/style/index.css';
import '@logicflow/extension/lib/style/index.css';

import startEvent from '@/components/logic-flow/start-event';
import endEvent from '@/components/logic-flow/end-event';
import userTask from '@/components/logic-flow/user-task';
import callActivity from '@/components/logic-flow/sub-process';
import exclusiveGateway from '@/components/logic-flow/exclusive-gateway';
import inclusiveGateway from '@/components/logic-flow/inclusive-gateway';
import parallelGateway from '@/components/logic-flow/paralle-gateway';
import sequenceFlow from '@/components/logic-flow/sequence-flow';
import {
	findByFormId,
	getProgressStatus,
	selectList,
	selectListActivity,
	selectListHis,
	getUserTaskList,
	getNextAssignee,
	findByProcessInstance,
	getAllNodeUserByInstanceId
} from '@/api/modules/flow-design';
import { get_token } from '@/utils/auth';

let winWidth = document.querySelector('body').clientWidth;
// winHeight = document.querySelector('body').clientHeight;

window.onload = window.onresize = () => {
	winWidth = document.querySelector('body').clientWidth;
	// winHeight = document.querySelector('body').clientHeight;
};

export default {
	name: 'ShowFlow',
	props: {
		formId: {
			type: String,
			default: ''
		},
		proInstanceId: {
			type: String,
			default: ''
		}
	},
	data() {
		return {
			lf: null,
			graphData: {},
			flowNodes: {},
			showInfo: false,
			clientX: -100,
			clientY: -100,
			nodeDatas: {},
			nodeData: {},
			flowParams: {
				desc: false,
				pageNo: 1,
				pageSize: 100,
				processInstanceId: ''
			},
			processDefinitionId: '',
			handleNodeId: '',
			flowRecord: [],
			loading: false,
			multiNodes: [],
			nodeList: [],
			duplicateList: []
		};
	},
	computed: {
		fixX: function () {
			return this.$refs.flowContainer.getBoundingClientRect().x;
		},
		fixY: function () {
			return this.$refs.flowContainer.getBoundingClientRect().y;
		}
	},
	watch: {
		proInstanceId: function (val) {
			this.flowParams.processInstanceId = val;
			if (!val) {
				this.lf = null;
			}
		}
	},
	mounted() {},
	methods: {
		deduplicateById(arr, key) {
			const map = new Map();
			arr.forEach(item => {
				const compositeKey = `${item[key]}_${item.assignee || ''}`;
				map.set(compositeKey, item);
			});
			return Array.from(map.values());
		},
		async init() {
			this.nodeDatas = {};
			this.flowParams.processInstanceId = this.proInstanceId;
			if (!this.lf) {
				this.initLogicFlow();
			}
			// 1.加载流程基础组件
			this.initLogicFlow();
			// 2.通过表单的id 获取流程图的json数据
			const flowsRes = await findByFormId(this.formId, {
				processInstanceId: this.flowParams.processInstanceId
			});
			if (flowsRes.code != 200) return;
			// 3.加载表单的所有流程图的节点
			this.initNodes(flowsRes);
			// 4 高亮 通过 驳回(暂时没法判断) 当前
			//5 节点信息
			// 如果有流程实例的id 那么就有流程记录
			if (this.proInstanceId) {
				// 5.获取节点的信息

				// 已完成的节点信息
				const hisRes = await selectListHis({ ...this.flowParams });
				this.flowRecord = hisRes.result?.records;
				if (!this.flowId && this.flowRecord.length > 0) {
					this.flowId = this.flowRecord[0].workbenchVO.flowId;
					this.processDefinitionId = this.flowRecord[0].workbenchVO.processDefinitionId;
				}
				const flowRecord = {};
				this.flowRecord.map(v => {
					flowRecord[v.taskDefinitionKey] = { ...v };
				});
				const tenantId = get_token('X-Coos-Client-Tenant-Id');
				const nodeRes = await getAllNodeUserByInstanceId({
					id: this.flowId,
					processInstanceId: this.flowParams.processInstanceId,
					processDefinitionId: this.processDefinitionId,
					tenantId
				});
				this.nodeList = nodeRes?.result || [];
				// 已完成的所有节点
				const hisAllRes = await selectListActivity({ ...this.flowParams });
				let allRecords = hisAllRes.result?.records;
				allRecords = this.deduplicateById(allRecords, 'activityId');
				console.log('已完成的所有节点:', allRecords);
				if (allRecords.length) {
					// 因为流程发起了,必然有发起节点
					allRecords[2].isFirst = true;
					let lastIndex = 2;
					const startNodeId = allRecords[2].activityId;
					for (let i = 2; i < allRecords.length; i++) {
						const v = allRecords[i];
						if (v.activityId == startNodeId) {
							lastIndex = i;
						}
					}
					allRecords.map((v, i) => {
						if (i < 2) {
							this.lf.setProperties(v.activityId, { status: 'selected' });
						} else if (i >= lastIndex) {
							if (v.activityId.indexOf('polyline_') > -1) {
								const edge = this.lf.getEdgeModelById(v.activityId);
								if (edge) {
									edge.setZIndex('top');
								}
							}
							if (v.activityId.indexOf('userTask_') > -1) {
								const node = this.lf.getNodeModelById(v.activityId);
								if (node) {
									node.setZIndex('top');
								}
							}
							this.lf.setProperties(v.activityId, { status: 'selected' });
							if (this.multiNodes.includes(v.activityId)) {
								let hanldeNodes = [];
								if (this.nodeDatas[v.activityId]?.hanldeNodes) {
									hanldeNodes = this.nodeDatas[v.activityId].hanldeNodes;
								} else {
									this.nodeDatas[v.activityId] = { activityId: v.activityId, hanldeNodes: [] };
								}

								// allRecords 包含了节点的办理开始时间和结束时间 不包含办理的人员和意见
								// 所以对比办理的节点信息 就能排除 之前办理的节点
								// 但是会不会存在驳回到上一个节点的问题
								this.flowRecord.map(vf => {
									if (vf.taskDefinitionKey == v.activityId && vf.endTime == v.endTime) {
										const nodeData = { ...v };
										nodeData.comment = vf.comment;
										nodeData.assignee = vf.assignee;
										nodeData.endTime = vf.endTime;
										nodeData.id = vf.id;
										hanldeNodes.push(nodeData);
									}
								});
								console.log('hanldeNodes:', hanldeNodes);
								this.nodeDatas[v.activityId].hanldeNodes = hanldeNodes;
							} else {
								// 如果不是多实例 就直接取
								this.nodeDatas[v.activityId] = { ...v };
								this.nodeDatas[v.activityId].id = flowRecord[v.activityId]?.id;
								this.nodeDatas[v.activityId].comment = flowRecord[v.activityId]?.comment;
								this.nodeDatas[v.activityId].assignee = flowRecord[v.activityId]?.assignee;
							}
						}
					});
				}

				// 当前待办任务节点
				const currRes = await selectList(this.flowParams);
				const keyMap = {};
				if (currRes.code == 200 && currRes.result && currRes.result.records) {
					const records = currRes.result.records;
					console.log('待办任务:', records);
					if (records.length > 0) {
						const taskDefinitionKey = records[0].flowInfo.taskDefinitionKey;
						// 组装待办人
						records.forEach(item => {
							let newTaskDefinitionKey = item.flowInfo.taskDefinitionKey;
							if (keyMap[newTaskDefinitionKey]) {
								const assignee = item.flowInfo.assignee;
								keyMap[newTaskDefinitionKey].assignee += `, ${assignee}`;
							} else {
								// 否则，初始化key对应的名字
								keyMap[newTaskDefinitionKey] = { ...item.flowInfo };
								keyMap[newTaskDefinitionKey].isHanlde = true;
							}
						});
						// 判断节点是否是多实例
						if (this.multiNodes.includes(taskDefinitionKey)) {
							// 如果是多实例
							// =>

							let hanldeNodes = [];
							// 判断是否已经有已办节点
							if (this.nodeDatas[taskDefinitionKey]?.hanldeNodes?.length > 0) {
								// 如果有 那就将待办和已办放在一起
								hanldeNodes = this.nodeDatas[taskDefinitionKey].hanldeNodes;
								const hanlde = keyMap[taskDefinitionKey];

								// 设置是否待办
								hanlde.isHanlde = true;
								hanldeNodes = [hanlde].concat(hanldeNodes);
								this.nodeDatas[taskDefinitionKey].hanldeNodes = [...hanldeNodes];
								this.nodeDatas[taskDefinitionKey].isHanlde = true;
							} else {
								this.nodeDatas[taskDefinitionKey] = {
									activityId: taskDefinitionKey,
									hanldeNodes: [keyMap[taskDefinitionKey]]
								};
							}
						} else {
							this.nodeDatas = { ...this.nodeDatas, ...keyMap };
						}
						Object.keys(keyMap).map(v => {
							const item = keyMap[v];
							const assignee = item.assignee;
							this.lf.setProperties(v, { status: 'current' });
							if (!assignee) return;
							// if (assignee.split(',') > 1) {
							// 	// 一个任务,如果有多个待办人,那么下一步节点的信息所有人都是一样的,只需要取第一个
							// 	this.getUserTaskList(assignee[0], item);
							// } else {
							// 	// 如果是多个任务 将就需要分别去查询下一步节点信息
							// 	this.getUserTaskList(assignee, item);
							// }
						});
					}
				}
				// 4.设置节点高亮 ? 如何判断驳回节点
				// this.getProgressStatus(keyMap);
				// 获取自动办理的节点信息
				this.findByProcessInstance();
			} else {
				this.nodeDatas = {};
			}
		},
		setComment(comment) {
			try {
				return comment
					.map(v => {
						return v.comment;
					})
					.join(',');
			} catch (error) {
				return comment || '-';
			}
		},
		findByProcessInstance() {
			const tenantId = get_token('X-Coos-Client-Tenant-Id');
			findByProcessInstance(this.flowParams.processInstanceId, tenantId).then(res => {
				if (res.code == 200) {
					this.duplicateList = res.result || [];
				}
			});
		},
		// 初始化画布
		initLogicFlow() {
			if (this.lf) {
				this.lf.translateCenter();
				return;
			}
			this.lf = new LogicFlow({
				grid: true,
				isSilentMode: true,
				stopScrollGraph: true,
				nodeSelectedOutline: false,
				edgeSelectedOutline: false,
				container: this.$refs.flowContainer
				// style: theme
			});
			this.lf.register(startEvent);
			this.lf.register(userTask);
			this.lf.register(exclusiveGateway);
			this.lf.register(inclusiveGateway);
			this.lf.register(parallelGateway);
			this.lf.register(endEvent);
			this.lf.register(callActivity);
			this.lf.register(sequenceFlow);
			this.lf.render();
			this.lf.on('node:mouseenter', ({ data, e }) => {
				const { id, text, type } = data;
				if (type != 'userTask') return;
				if (this.nodeDatas[id]?.show === false) return;
				const nodeName = text.value ? text.value : text;
				const nodeType = data.properties['flowable:nodeType'];
				if (this.nodeDatas[id]) {
					this.nodeData = { nodeName, ...this.nodeDatas[id] };
				} else {
					let arr = [];
					this.nodeList.forEach(item => {
						if (item.nodeId === id) {
							arr = Object.values(item.selectValue);
						}
					});
					this.nodeData = { nodeName, assignee: arr.join(',') };
				}
				if (this.nodeData.comment && data.properties['flowable:nodeType'] == 2) {
					this.nodeData.isAi = true;
					const comment = this.nodeData.comment;
					let AIComment = '';
					if (comment.split('：').length > 1) {
						AIComment = comment.split('：')[1]?.split(',');
					} else {
						AIComment = comment.split(',');
					}

					const aiComments = AIComment.map(v => {
						return {
							question: v.split('|')[0],
							answer: v.split('|')[1]
						};
					});
					this.nodeData.aiQuestion = [...aiComments];
				}
				if (this.duplicateList.length) {
					for (let i = 0; i < this.duplicateList.length; i++) {
						// 自动抄送节点,并且是多实例 // 多实例的
						if (nodeType == 3 && this.nodeData.hanldeNodes) {
							this.nodeData.nodeType = 3;

							this.nodeData.hanldeNodes.map(v => {
								if (this.duplicateList[i].taskId == v.id) {
									if (this.nodeData.duplicateList && this.nodeData.duplicateList.length) {
										this.nodeData.duplicateList.push(this.duplicateList[i]);
									} else {
										this.nodeData.duplicateList = [];
										this.nodeData.duplicateList.push(this.duplicateList[i]);
									}
								}
							});
						} else if (this.nodeData.hanldeNodes) {
							this.nodeData.hanldeNodes.map(v => {
								if (this.duplicateList[i].taskId == v.id) {
									if (v.duplicateList && v.duplicateList.length) {
										v.duplicateList.push(this.duplicateList[i]);
									} else {
										v.duplicateList = [];
										v.duplicateList.push(this.duplicateList[i]);
									}
									return v;
								}
							});
						}

						if (this.duplicateList[i].taskId == this.nodeData.id) {
							// 等于3 就是自动抄送节点 就不需要拼接 抄送信息
							if (nodeType == 3) {
								this.nodeData.nodeType = 3;
								this.nodeData.comment = this.duplicateList[i].comment;
							} else if (nodeType == 1) {
								// 手动抄送的,就需要把抄送信息附加上去
								if (this.nodeData.duplicateList && this.nodeData.duplicateList.length) {
									this.nodeData.duplicateList.push(this.duplicateList[i]);
								} else {
									this.nodeData.duplicateList = [];
									this.nodeData.duplicateList.push(this.duplicateList[i]);
								}
							}
						}
					}
				}
				if (this.nodeData?.hanldeNodes?.length) {
					const hanldeNodes = this.deduplicateLastByKey(this.nodeData.hanldeNodes, 'id');
					this.nodeData.hanldeNodes = [...hanldeNodes];
				}
				const target = e.target;
				const { x, y } = target.getBoundingClientRect();
				this.clientX = x + 125 - this.fixX;
				this.clientY = y - this.fixY;
				if (x + 420 > winWidth) {
					this.clientX = x - 305 - this.fixX;
				}
				this.showInfo = true;
				this.$nextTick(() => {
					const showInfo = document.querySelector('.node-info');
					const flowContainer = document.querySelector('#flowContainer');
					const clientHeight = showInfo.clientHeight;
					const fclientHeight = flowContainer.clientHeight;
					if (this.clientY + clientHeight > fclientHeight) {
						if (clientHeight < fclientHeight) {
							this.clientY = fclientHeight - clientHeight;
						} else {
							this.clientY = 0;
						}
					}
				});
			});
			// 鼠标移出
			this.lf.on('node:mouseleave', ({ data, e }) => {
				this.showInfo = false;
				const { id } = data;
				if (this.nodeDatas[id]?.hanldeNodes) {
					const hanldeNodes = this.nodeDatas[id].hanldeNodes;
					const hanldeNode = hanldeNodes.map(v => {
						const node = { ...v };
						if (v.duplicateList) {
							node.duplicateList = [];
						}
						return node;
					});
					this.nodeDatas[id].hanldeNodes = [...hanldeNode];
				}

				this.nodeData = {};
				this.clientX = -9999;
				this.clientY = -9999;
			});
		},
		deduplicateLastByKey(arr, key) {
			const map = new Map();
			arr.forEach(item => map.set(item[key], item));
			return Array.from(map.values());
		},
		// 初始化节点
		initNodes(res) {
			const jsonContent = JSON.parse(res.result.config.jsonContent);
			const config = [...res.result.config.config];
			const flowNodes = {};
			this.flowId = config.id;
			config.map(v => {
				// const nodeConfig = { ...JSON.parse(v.configData) };
				// console.log(nodeConfig);
				flowNodes[v.nodeId] = { nodeConfig: { ...JSON.parse(v.configData) } };
			});
			this.flowNodes = { ...flowNodes };
			const { nodes = [], edges = [] } = jsonContent || {};
			const rebuildNodes = nodes.map(v => {
				const { id, properties, type, x, y } = v;
				if (properties.multiInstanceLoopCharacteristics) {
					this.multiNodes.push(id);
				}
				return {
					id,
					properties,
					type,
					x,
					y,
					text: v.text?.value
				};
			});
			const rebuildEdges = edges.map(v => {
				const {
					id,
					endPoint,
					properties,
					pointsList,
					sourceNodeId,
					startPoint,
					targetNodeId,
					text
				} = v;

				return {
					id,
					endPoint,
					properties,
					type: 'sequenceFlow',
					pointsList,
					sourceNodeId,
					startPoint,
					targetNodeId,
					text: text ? text : ''
				};
			});
			this.graphData.nodes = [...rebuildNodes];
			this.graphData.edges = [...rebuildEdges];
			// console.log(this.graphData);
			this.lf.renderRawData(this.graphData);
			this.lf.translateCenter();
		},
		// 初始化节点高亮状态 方法已经废弃
		getProgressStatus(hanldeNode) {
			this.loading = true;
			getProgressStatus(this.flowParams.processInstanceId)
				.then(res => {
					// 获取流程状态
					this.loading = false;
					if (res.result == null) return;
					const { highLightedActivities, highLightedFlows } = res.result;
					const len = Object.keys(highLightedActivities).length;
					highLightedActivities.map((v, index) => {
						if (index + 1 == len) {
							// 最后一个节点
							// 如果是结束节点,那么流程应该就是结束了
							if (v.indexOf('endEvent_') > -1) {
								this.lf.setProperties(v, { status: 'selected' });
							}
							if (hanldeNode[v]) {
								// 如果是待办节点
								this.lf.setProperties(v, { status: 'current' });
								// this.nodeDatas[v].show = false;
							}
						} else {
							if (hanldeNode[v]) {
								// 如果是待办节点
								this.lf.setProperties(v, { status: 'current' });
								// this.nodeDatas[v].show = false;
							} else {
								this.lf.setProperties(v, { status: 'selected' });
							}
						}
					});
					highLightedFlows.map((v, index) => {
						this.lf.setProperties(v, { status: 'selected' });
					});
				})
				.catch(() => {
					this.loading = false;
				});
		},

		getUserTaskList(assignee, workInfo) {
			// 获取全部节点
			const { processDefinitionId, processInstanceId } = workInfo;
			getUserTaskList({ assignee, processDefinitionId, processInstanceId }).then(res => {
				// console.log('getUserTaskList', res);
				const taskNodeList = [...res.result];
				taskNodeList.map(v => {
					// 获取到所有的用户节点
					v.map(vv => {
						// vv.activityId 就是当前待办的节点 获取待办节点后面的节点信息
						// console.log('vv:', vv);
						// console.log('this.nodeDatas:', this.nodeDatas[vv.nodeId]);
						// if (this.nodeDatas[vv.nodeId] != undefined && !vv.activityId) {
						// 	console.log('vv:', vv);
						// 	console.log('vv:', this.nodeDatas[vv.nodeId]);
						// const nodeId = vv.nodeId;
						// this.getNextAssignee(nodeId, processDefinitionId, processInstanceId);
						// }
					});
				});
				//
			});
		},
		getNextAssignee(nodeId, processDefinitionId, processInstanceId) {
			getNextAssignee({ nodeId, processDefinitionId, processInstanceId }).then(resp => {
				// 获取到的是待办节点的信息,但是 然后再获取再
				// 并没有获取到
				if (resp.result && resp.result.length == 1) {
					const result = [...resp.result];
					// 只有一个
					if (result[0].selectValue) {
						// console.log(vv.nodeId);
						const selectValue = result[0].selectValue;
						const assignee = Object.keys(selectValue).map(v => {
							return selectValue[v];
						});
						const nextNode = this.lf.getNodeOutgoingNode(nodeId);
						if (nextNode.length > 0) {
							// 如果 数据为空 那么应该就是下一个节点 如果数据不为空,那么就需要继续请求下一个节点
							if (this.nodeDatas[nextNode[0].id] == undefined) {
								// this.getNextAssignee(nextNode[0].id, processDefinitionId, processInstanceId);
								this.nodeDatas[nextNode[0].id] = { assignee: assignee.join() };
							}
						}
					}
				}
			});
		},
		calcName(name) {
			if (name) {
				const names = name.split(',');
				if (names.length > 20) {
					let na = [...names];
					na.length = 20;
					return na.join(',') + `等${names.length}人`;
				} else {
					return name;
				}
			} else {
				return '-';
			}
		}
	}
};
</script>
<style lang="scss" scoped>
.flowContainer {
	height: calc(100vh - 300px);
}
.node-info {
	position: absolute;
	left: -200px;
	width: 300px;
	box-shadow: 0 0 5px #ccc;
	border-radius: 3px;
	border: 1px solid #ccc;
	background: #fff;
	z-index: 2;
	.title {
		border-top-left-radius: 3px;
		border-top-right-radius: 3px;
		line-height: 40px;
		background-color: #f5f5f5;
		padding-left: 15px;
	}
	.muti-box {
		margin: 10px 15px 0 15px;
		&:not(:last-child) {
			border-bottom: 1px solid #ccc;
		}

		.node-item {
			margin: 0;
			&:not(:last-child) {
				margin-top: 10px;
				border-bottom: 1px solid #ccc;
			}
		}
	}
	.node-item {
		margin: 0 15px;
		padding-bottom: 10px;
		display: flex;
		grid-gap: 5px;
		&.current {
			color: rgb(24, 144, 255);
		}
		.node-label {
			width: 70px;
			text-align: right;
		}
		.node-ctn {
			flex: 1;
		}
		&:not(:first-child) {
			margin-top: 10px;
			border-bottom: 1px solid #ccc;
		}
	}
}
.flow-table {
	width: 100%;
	border-spacing: 0;
	th {
		background: #f2f2f2;
	}
	td,
	th {
		text-align: center;
		border: 1px solid #ccc;
		padding: 3px 0;
	}
}
.duplicate-box {
	.node-item {
		margin-top: 5px;
		border-bottom: 1px solid #ccc;
		padding-bottom: 5px;
	}
}
</style>
