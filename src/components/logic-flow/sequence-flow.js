import { PolylineEdge, PolylineEdgeModel } from '@logicflow/core';
const statusColor = { current: '#1890ff', selected: '#2A9D8F' };
class SequenceModel extends PolylineEdgeModel {
	getEdgeStyle() {
		const style = super.getEdgeStyle();
		const { properties } = this;
		if (properties.status) {
			style.stroke = statusColor[properties.status];
		}
		return style;
	}
}
export default {
	type: 'sequenceFlow',
	view: PolylineEdge,
	model: SequenceModel
};
