import { PolygonNode, PolygonNodeModel, h } from '@logicflow/core';

class ParallelkView extends PolygonNode {
	getShape() {
		const { model } = this.props;
		const { x, y, width, height, points } = model;
		const style = model.getNodeStyle();
		return h(
			'g',
			{
				transform: `matrix(1 0 0 1 ${x - width / 2} ${y - height / 2})`
			},
			h('polygon', {
				...style,
				x,
				y,
				points
			}),
			h('path', {
				fill: style.stroke,
				d: 'm 23,10 0,12.5 -12.5,0 0,5 12.5,0 0,12.5 5,0 0,-12.5 12.5,0 0,-5 -12.5,0 0,-12.5 -5,0 z'
			})
		);
	}
}

class ParallelkModel extends PolygonNodeModel {
	constructor(data, graphModel) {
		data.text = {
			value: '',
			x: data.x,
			y: data.y + 40
		};
		super(data, graphModel);
		this.points = [
			[25, 0],
			[50, 25],
			[25, 50],
			[0, 25]
		];
	}
}

export default {
	type: 'parallelGateway',
	view: <PERSON>llelkView,
	model: ParallelkModel
};
