<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-04-07 10:04:46
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2025-04-25 16:38:26
 * @FilePath: /coos-desktop-app/src/components/logic-flow/flow-print-new.vue
 * @Description:
 * demo :print-config="{
						title: '科研打印',
						formConfig: [
							{ label: '标题', printValue: '我是标题' },
							{ label: '申请编号', printValue: '我是申请编号' },
							{ label: '打印时间', printValue: '2023-04-03 17:00:00' },
							{ label: '打印人', printValue: '张三' },
							{ label: '打印份数', printValue: '2' },
							{ label: '印章名称', printValue: '我是印章名称', type: '' },
							{
								title: 'xxxx', // 可选
								label: '项目名称', // 当title存在时，可选
								printValueType: 'table', // 可选值：list、table、data
								printValue: [
									{
										code: '项目编码1',
										name: '项目名称1'
									},
									{
										code: '项目编码2',
										name: '项目名称2'
									}
								],
								tableShowIndex: true, // 可选，为true时，显示序号列
								tableHeader: [
									{
										value: 'code',
										label: '项目编码'
									},
									{
										value: 'name',
										label: '项目名称'
									}
								]
							},
							{
								label: '是否涉及法律等重要事项',
								printValue:
									'是否涉及法律等重要事项是否涉及法律等重要事项是否涉及法律等重要事项是否涉及法律等重要事项是否涉及法律等重要事项是否涉及法律等重要事项'
							},
              {
                label:'打印文件',
                printValueType: 'file',
                printValue: [
                  {
                    id: '1',
                    originalFileName: '文件1.pdf',
                    name: '文件1.pdf'
                  },
                  {
                    id: '2',
                    originalFileName: '文件2.pdf',
                    name: '文件2.pdf'
                  }
                ]
              }
						] // 配置项传入label-printValue
					}"
-->
<template>
	<div class="print-container">
		<div class="print-container-title">
			{{ title }}
		</div>
		<table class="print-container-table">
			<template v-for="(item, index) in formConfig">
				<template v-if="!item.printHide">
					<tr v-if="item.title" :key="`${item.label || item.title}_${index}_title`">
						<td colspan="2" class="tc">{{ item.title }}</td>
					</tr>
					<tr
						v-if="item.hasOwnProperty('printValue')"
						:key="`${item.label || item.title}_${index}_label`"
					>
						<td v-if="item.label" style="width: 160px">{{ item.label }}</td>
						<td :colspan="item.label ? 1 : 2">
							<template v-if="item.printValueType === 'table'">
								<table class="print-container-table" style="width: 100%">
									<th v-if="item.tableShowIndex" width="60">序号</th>
									<th v-for="col in item.tableHeader" :key="col.printValue">
										{{ col.label }}
									</th>
									<template v-if="Array.isArray(item.printValue) && item.printValue.length">
										<tr v-for="(row, idx) in item.printValue" :key="row.id">
											<td v-if="item.tableShowIndex">{{ idx + 1 }}</td>
											<td v-for="col in item.tableHeader" :key="col.value">
												<template v-if="col.printValueType === 'image'">
													<img :src="row[col.value]" />
												</template>
												<template v-else>
													{{ row[col.value] }}
												</template>
											</td>
										</tr>
									</template>
									<template v-else>
										<tr>
											<td :colspan="item.tableHeader.length" class="tc">暂无数据</td>
										</tr>
									</template>
								</table>
							</template>
							<template v-else-if="item.printValueType === 'richText'">
								<div class="print-container-rich" v-html="item.printValue"></div>
							</template>
							<template v-else-if="item.printValueType === 'image'">
								<img
									:src="item.printValue"
									:style="`width:${item.imageWidth || ''}`"
									v-bind="item"
								/>
							</template>
							<template v-else-if="item.printValueType === 'file'">
								<template v-if="item.printValue.length > 0">
									<div
										v-for="file in item.printValue"
										:key="file.id"
										class="print-container-file-item"
									>
										{{ file.originalFileName || file.name }}
									</div>
								</template>
								<div v-else class="print-container-file-item">暂无文件</div>
							</template>
							<template v-else>
								{{ item.printValue }}
							</template>
						</td>
					</tr>
				</template>
			</template>
		</table>
		<div class="print-container-sub-title">流转意见</div>
		<table class="print-container-table">
			<tr>
				<th class="tc" width="60">序号</th>
				<th class="tc">当前步骤</th>
				<th class="tc">审核意见</th>
				<th class="tc" width="100">人员签字</th>
				<th class="tc" width="180">发起/办理时间</th>
			</tr>
			<tr v-for="(flow, index) in circulationList" :key="flow.id">
				<td class="tc">{{ index + 1 }}</td>
				<td class="tc">{{ flow.name || flow.flowInfo.name }}</td>
				<td>
					{{
						flow.comment
							? flow.comment
									.map(item => {
										return item.comment;
									})
									.join(',')
							: '-'
					}}
				</td>
				<td class="tc">{{ flow.assignee }}</td>
				<td class="tc">
					<template v-if="flow.nodeType == 1 && firstNodeId != flow.taskDefinitionKey">
						{{ flow.endTime }}
					</template>
					<template v-if="firstNodeId == flow.taskDefinitionKey">
						{{ flow.startTime || flow.createTime }}
					</template>
				</td>
			</tr>
		</table>
	</div>
</template>

<script>
export default {
	props: {
		printConfig: {
			type: Object,
			default: () => ({
				// title: '打印demo',
				// formConfig: [
				// 	{ title: '中间标题' },
				// 	{ label: '标题', printValue: '我是标题' },
				// 	{ label: '申请编号', printValue: '我是申请编号' },
				// 	{ label: '打印时间', printValue: '2023-04-03 17:00:00' },
				// 	{ title: 'title和label同时存在', label: '打印时间', printValue: '2023-04-03 17:00:00' },
				// 	{ label: '标题', printValue: '我是标题' },
				// 	{ label: '申请编号', printValue: '我是申请编号' },
				// 	{ label: '打印时间', printValue: '2023-04-03 17:00:00' },
				// 	{ label: '打印人', printValue: '张三' },
				// 	{ label: '打印份数', printValue: '2' },
				// 	{ label: '印章名称', printValue: '我是印章名称', type: '' },
				// 	{
				// 		title: '标题名称', // 可选
				// 		label: '项目名称', // 当title存在时，可选
				// 		printValueType: 'table', // 可选值：list、table、data
				// 		printValue: [
				// 			{
				// 				code: '项目编码1',
				// 				name: '项目名称1'
				// 			},
				// 			{
				// 				code: '项目编码2',
				// 				name1: '项目名称2',
				// 				name: 'http://117.174.100.38:818/coos_desk/static/img/attendance1.f07e5e07.png'
				// 			}
				// 		],
				// 		tableShowIndex: true, // 可选，为true时，显示序号列
				// 		tableHeader: [
				// 			{
				// 				value: 'code',
				// 				label: '项目编码'
				// 			},
				// 			{
				// 				value: 'name',
				// 				label: '项目名称',
				// 				printValueType: 'image',
				// 				imageWidth: '120px'
				// 			}
				// 		]
				// 	},
				// 	{
				// 		label: '是否涉及法律等重要事项',
				// 		printValue:
				// 			'是否涉及法律等重要事项是否涉及法律等重要事项是否涉及法律等重要事项是否涉及法律等重要事项是否涉及法律等重要事项是否涉及法律等重要事项'
				// 	},
				// 	{
				// 		label: '打印图片',
				// 		printValueType: 'image',
				// 		printValue: 'http://117.174.100.38:818/coos_desk/static/img/attendance1.f07e5e07.png'
				// 	},
				// 	{
				// 		label: '打印文件',
				// 		printValueType: 'file',
				// 		printValue: [
				// 			{
				// 				id: '1',
				// 				originalFileName: '文件1.pdf',
				// 				name: '文件1.pdf'
				// 			},
				// 			{
				// 				id: '2',
				// 				originalFileName: '文件2.pdf',
				// 				name: '文件2.pdf'
				// 			}
				// 		]
				// 	}
				// ]
			})
		},
		// 流转记录
		circulationList: {
			type: Array,
			default: () => []
		},
		firstNodeId: {
			type: String,
			default: ''
		}
	},
	data() {
		return {};
	},
	computed: {
		title() {
			return this.printConfig.title;
		},
		formConfig() {
			return this.printConfig.formConfig;
		}
	}
};
</script>

<style lang="scss" scoped>
.print-container {
	width: 1000px;
	padding: 20px 80px;
	background-color: #ffffff;
	display: none;
	&-title {
		text-align: center;
		margin-bottom: 40px;
		color: #15224c;
	}
	&-file-item:not(:first-child) {
		padding-top: 12px;
	}
	&-table {
		width: 100%;
		border-collapse: collapse;
		margin: 0;
		// table-layout: fixed;
		font-size: 14px;
		margin-top: -1px;
		thead {
			th {
				background-color: #f5f7fa;
				text-align: center;
			}
		}
		th,
		td {
			border: 1px solid #e3ebf2;
			padding: 16px 12px !important;
			text-align: left;
			word-wrap: break-word;
		}
		th {
			color: #15224c;
		}
		td {
			color: #2f446b;
		}
		.tc {
			text-align: center;
		}
	}
	&-sub-title {
		text-align: center;
		margin: 20px 0;
		color: #2f446b;
	}
	&-rich {
		display: flex;
		flex-direction: column;
		white-space: pre-wrap;
		word-break: break-word;
		line-height: 1.5;
		::v-deep {
			h1,
			h2,
			h3,
			h4,
			h5,
			h6 {
				margin: 20px 0;
			}
		}
	}
}
@media print {
	@page {
		size: A4;
		background-color: #fff;
		margin: 8mm 0mm;
	}
	body,
	html {
		height: auto;
		margin: 0px;
		background-color: #fff;
	}
	html,
	body,
	#app {
		height: 100%;
	}
	.print-container {
		display: block;
		min-height: 100%;
	}
}
</style>
