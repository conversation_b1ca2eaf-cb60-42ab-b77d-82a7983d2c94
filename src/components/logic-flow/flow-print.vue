<template>
	<div style="width: 1060px; overflow: hidden; display: none">
		<div id="print-area" style="padding-right: 1px; margin-left: -5px; width: 1060px">
			<table class="table-head">
				<tr>
					<th class="head" width="50%" align="left">{{ flowRecord[0].workbenchVO.flowName }}</th>
					<th class="print-time" width="50%" align="right">打印时间:{{ date }}</th>
				</tr>
			</table>
			<table class="print-table" border="1" boderColor="#e3ebf2" cellspacing="0">
				<tbody class="tbody">
					<tr class="thead">
						<td align="left" width="50px">序号</td>
						<td align="left" width="22%">节点名称</td>
						<td align="left" width="22%">办理人</td>
						<td align="left" width="">审核结果</td>
						<td align="left" width="22%">发起/办理时间</td>
					</tr>
					<tr v-for="(item, index) in flowRecord" :key="index">
						<td align="left">{{ index + 1 }}</td>
						<td align="left">{{ item.name || item.flowInfo.name }}</td>
						<td align="left">{{ item.assignee }}</td>
						<td align="left">{{ getComment(item.comment) }}</td>
						<td align="left">
							<template v-if="item.nodeType == 1 && firstNodeId != item.taskDefinitionKey">
								{{ item.endTime }}
							</template>
							<template v-if="firstNodeId == item.taskDefinitionKey">
								{{ item.startTime || item.createTime }}
							</template>
						</td>
					</tr>
				</tbody>
			</table>
		</div>
	</div>
</template>
<script>
import printJS from 'print-js';
import moment from 'moment';
export default {
	name: 'FlowPrint',
	props: {
		flowRecord: {
			type: Array,
			default: () => {
				return [];
			}
		},
		firstNodeId: {
			type: String,
			default: ''
		},
		processInstanceId: {
			type: String,
			default: ''
		}
	},
	data() {
		return {
			flowName: '',
			date: moment().format('yyyy-MM-DD HH:mm:ss')
		};
	},
	watch: {},
	mounted() {},
	methods: {
		getComment(comment) {
			try {
				return comment
					.map(item => {
						return item.comment;
					})
					.join(',');
			} catch (e) {
				return comment || '';
			}
		},
		print() {
			printJS({
				// 需要打印区域设置的Id
				printable: 'print-area',
				// 打印类型
				type: 'html',
				// 默认值为800，我们把把设置为100%
				maxWidth: '100%',
				// *代表应用所有样式，默认值为null，如果不设置，打印窗口则会忽略所有样式
				targetStyles: ['*']
			});
		}
	}
};
</script>
<style lang="scss" scoped>
@media print {
	.print-table {
		width: 100%;
	}
}
.table-head {
	color: #737a94;
	line-height: 40px;
	width: 100%;
	.head {
		color: $primaryTextColor;
		font-weight: bold;
		font-size: 16px;
	}
}
.print-table {
	color: #737a94;
	font-family: PingFang SC, PingFang SC;
	font-weight: 400;
	table-layout: auto !important;
	width: 100%;

	th,
	td {
		padding: 5px;
		line-height: 40px;
	}
	.head {
		font-size: 16px;
		font-weight: bold;
		color: #2f446b;
	}
	.tbody {
		tr {
			&:not(.thead) {
				td {
					color: $primaryTextColor;
				}
			}
		}
	}
}
</style>
