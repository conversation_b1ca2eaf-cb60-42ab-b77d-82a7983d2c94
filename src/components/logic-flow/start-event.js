import { CircleNode, CircleNodeModel } from '@logicflow/core';
const statusColor = { current: '#1890ff', selected: '#2A9D8F' };

class CustomCircleModel extends CircleNodeModel {
	constructor(data, graphModel) {
		data.text = {
			value: data.text || '开始',
			x: data.x,
			y: data.y + 40
		};
		super(data, graphModel);

		this.r = 20;
	}
	getTextStyle() {
		const style = super.getTextStyle();
		// style.hover.color = 'blue';
		return style;
	}
	getNodeStyle() {
		const style = super.getNodeStyle();
		// style.strokeDasharray = '3 3';
		const { properties } = this;
		if (properties.status) {
			style.stroke = statusColor[properties.status];
		}
		return style;
	}
	getEdgeStyle() {
		const style = super.getEdgeStyle();
		style.stroke = 'blue';
		style.strokeDasharray = '3 3';
		return style;
	}
	getOutlineStyle() {
		const style = super.getOutlineStyle();
		const { properties } = this;
		if (properties.status) {
			style.hover.stroke = '#2A9D8F';
		}

		return style;
	}
	getConnectedTargetRules() {
		const rules = super.getConnectedTargetRules();
		const notAsTarget = {
			message: '起始节点不能作为连线的终点',
			validate: () => false
		};
		rules.push(notAsTarget);
		return rules;
	}
}
class CustomCirclenNode extends CircleNode {}
export default {
	type: 'startEvent',
	view: CustomCirclenNode,
	model: CustomCircleModel
};
