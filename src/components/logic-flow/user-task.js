import { RectNode, RectNodeModel, h } from '@logicflow/core';
const statusColor = { current: '#1890ff', selected: '#2A9D8F', rejects: '#d2b211' };
class UserTaskView extends RectNode {
	getLabelShape() {
		const { model } = this.props;
		const { x, y, width, height, properties } = model;
		const style = model.getNodeStyle();
		const { status } = properties;
		let hs = [];
		if (properties['flowable:nodeType'] == '2') {
			hs = [
				h('path', {
					fill: status ? statusColor[status] : style.stroke,
					d: 'M139.4688 423.2192l6.9632 0.256a88.064 88.064 0 0 1 32.3072 166.656l-3.7888 1.6384 0.1024 110.0288c0 3.2256 1.1776 6.2976 2.816 8.448l1.792 1.792 2.304 1.536 323.3792 182.0672c3.584 2.048 7.8848 2.304 10.4448 1.28l2.8672-1.3312 321.792-181.9136a13.312 13.312 0 0 0 6.656-9.1648l0.256-2.6624v-38.0928c0-18.0224 12.8-33.4336 32.4096-36.9664l4.8128-0.3584c20.5824 0 37.2736 16.6912 37.2736 37.2736v38.144a88.064 88.064 0 0 1-44.6976 76.6976l-321.8432 181.9136a88.064 88.064 0 0 1-86.528 0.1024L145.408 778.5472a88.064 88.064 0 0 1-44.9024-76.7488v-111.5136l-5.0176-2.56a88.1152 88.1152 0 0 1-44.0832-81.408l0.7168-7.2704a88.064 88.064 0 0 1 87.3984-75.8272z m415.8464-360.6016l321.8432 181.9136c27.648 15.616 44.7488 44.9024 44.7488 76.6464l-0.0512 110.2336 4.864 2.4064c29.184 15.8208 47.4112 47.104 45.9776 81.0496l-0.5632 7.3216a88.064 88.064 0 1 1-129.7408-88.1664l4.864-2.5088 0.1024-110.2848a14.2848 14.2848 0 0 0-2.7648-8.448l-1.792-1.792-2.304-1.536L518.656 127.488a13.3632 13.3632 0 0 0-10.5984-1.28l-2.7136 1.2288-323.3792 182.0672a13.312 13.312 0 0 0-6.7072 8.96l-0.2048 2.816v37.2736c0.256 19.456-14.4896 35.84-33.8944 37.632-19.4048 1.7408-36.864-11.7248-40.2944-32.512l-0.3584-4.864v-37.5296c0-31.7952 17.152-61.1328 44.9024-76.7488l323.3792-182.016a88.064 88.064 0 0 1 86.528 0.1024zM139.4176 477.3888a33.8944 33.8944 0 1 0 0 67.7888 33.8944 33.8944 0 0 0 0-67.7888z m745.2672 0a33.8944 33.8944 0 1 0 0 67.7888 33.8944 33.8944 0 0 0 0-67.7888z'
				}),
				h('path', {
					fill: status ? statusColor[status] : style.stroke,
					d: 'M510.976 579.9424H391.168l-22.4768 66.816H297.6768L413.696 321.6896h76.7488l115.1488 325.12h-73.728l-20.8896-66.8672z m-18.944-56.0128l-40.6016-127.8976-41.9328 127.8976H492.032zM641.536 321.6896h67.4816v325.12H641.536z'
				})
			];
		} else if (properties['flowable:nodeType'] == '3') {
			hs = [
				h('path', {
					fill: status ? statusColor[status] : style.stroke,
					d: 'M663.552 707.072c-8.192 0-14.848-2.56-20.48-7.168-5.632-5.12-9.728-11.264-11.776-19.456l-36.352-91.136h-174.08L394.24 657.408c-2.56 5.632-4.608 11.264-6.656 16.896-2.56 5.632-5.12 10.752-8.192 15.872s-7.168 9.216-11.776 12.288c-4.608 3.072-10.24 5.12-16.384 5.12-10.752 0-18.944-3.072-25.088-9.216-6.656-6.656-7.168-17.92-1.024-33.792l143.36-377.856c2.56-7.68 7.168-13.824 13.824-17.92 6.656-4.608 14.848-6.656 25.088-6.656 10.752 0 19.456 2.048 26.112 6.656 6.656 4.608 11.776 10.752 14.336 18.432l143.872 378.368c4.096 11.264 4.096 20.992 0.512 29.184-4.096 8.704-13.312 13.312-27.136 13.312 0-1.024-1.024-1.024-1.536-1.024z m-84.48-171.008L508.416 344.064l-70.656 192h141.312z'
				}),
				h('path', {
					fill: status ? statusColor[status] : style.stroke,
					d: 'M507.904 266.24c9.728 0 17.408 2.048 23.04 5.632 6.144 4.096 10.24 9.216 12.288 15.872l143.872 378.88c3.584 9.728 4.096 18.432 0.512 25.088-3.072 6.656-10.752 10.24-22.528 10.24h-2.048c-6.656 0-12.288-2.048-16.896-6.144s-8.192-9.728-10.752-16.896l-37.376-94.72H417.792L389.632 655.36c-2.048 5.632-4.608 11.264-6.656 16.896-2.048 5.12-5.12 10.24-7.68 14.848-3.072 4.608-6.144 8.192-10.24 10.752s-8.192 4.096-13.312 4.096c-9.216 0-16.384-2.56-20.992-7.68-5.12-5.12-5.12-14.848 0-28.672l143.36-377.344c2.048-6.656 6.144-11.776 11.776-15.872 5.12-4.096 12.8-6.144 22.016-6.144m-77.312 274.944h155.648L508.416 329.216l-77.824 211.968M507.904 256c-11.264 0-20.48 2.56-28.16 7.68s-12.8 12.288-15.872 20.48l-143.36 377.344c-2.56 6.656-9.728 27.136 2.048 39.424 7.168 7.168 16.384 10.752 28.672 10.752 7.168 0 13.312-2.048 18.944-5.632 5.12-3.584 9.216-8.192 12.8-13.824 3.072-5.12 6.144-10.752 8.704-16.384 2.048-5.12 4.608-10.752 7.168-16.896l25.6-64.512h166.912l34.816 87.552c3.072 8.704 7.168 15.872 13.312 20.992 6.144 5.632 14.336 8.704 23.552 8.704h2.048c19.968 0 28.16-8.704 31.744-15.872 4.608-9.216 4.096-20.48-0.512-32.768l-143.872-378.88c-3.072-8.704-8.704-15.872-16.384-20.992-7.168-4.608-16.896-7.168-28.16-7.168zM445.44 530.944l62.976-172.032 62.976 172.032H445.44z'
				}),
				h('path', {
					fill: status ? statusColor[status] : style.stroke,
					d: 'M512 964.608c-137.216 0-265.216-61.44-351.744-167.936-1.536-1.536-2.56-3.584-4.096-5.12C93.184 711.168 59.904 614.4 59.904 512v-6.656l5.12-0.512h-5.12c1.024-68.096 17.92-135.168 50.176-199.68 3.584-9.728 12.288-16.384 23.04-16.384 13.824 0 25.088 11.264 25.088 25.088 0 2.56-0.512 4.608-1.536 6.656 0 1.024 0 1.536-0.512 2.048-30.208 56.32-45.568 119.296-45.568 186.88 0 64 14.336 124.928 43.52 181.248 68.096 138.24 205.824 224.256 359.424 224.256 71.68 0 141.824-19.456 203.776-56.32l-24.064-41.472c-1.024-1.536-1.024-4.096 0.512-5.632 1.024-1.536 2.56-2.048 4.096-2.048h1.536l98.304 26.112c1.536 0.512 2.56 1.024 3.072 2.56s1.024 2.56 0.512 4.096l-26.112 98.304c-0.512 2.048-2.048 3.584-4.096 3.584h-0.512c-2.048 0-3.584-1.024-4.608-2.56l-23.552-40.96c-67.584 39.936-143.36 61.952-219.648 63.488h-1.024c-3.584 0.512-6.656 0.512-9.728 0.512z m378.88-228.864c-13.824 0-25.088-11.264-25.088-25.088 0-2.56 0.512-4.608 1.536-6.656 0-1.024 0-1.536 0.512-2.048 30.208-56.32 45.568-119.296 45.568-186.88 0-64-14.336-124.928-43.52-181.248-68.096-138.24-205.824-224.256-359.424-224.256-71.68 0-141.824 19.456-203.776 56.32l24.064 41.472c1.024 2.048 1.024 4.096-0.512 5.632-1.024 1.536-2.56 2.048-4.096 2.048h-1.536l-98.304-26.112c-1.536-0.512-2.56-1.024-3.072-2.56s-1.024-2.56-0.512-4.096l26.112-98.304c0.512-2.048 2.048-3.584 4.096-3.584h0.512c2.048 0 3.584 1.024 4.608 2.56l23.552 40.96c67.584-39.936 143.36-61.952 219.648-63.488h10.24c137.216 0 265.216 61.44 351.744 167.936 1.536 1.536 2.56 3.584 4.096 5.12 62.976 80.384 96.256 176.64 96.256 279.04v6.656l-5.12 0.512h5.12c-1.024 68.096-17.92 135.168-50.176 199.68-2.56 9.728-11.776 16.384-22.528 16.384z'
				}),
				h('path', {
					fill: status ? statusColor[status] : style.stroke,
					d: 'M512 65.024c140.288 0 265.728 64.512 347.648 165.888 1.536 1.536 2.56 3.072 4.096 5.12 59.904 75.776 95.744 172.032 95.744 276.48V519.68c-1.024 71.168-19.968 138.24-50.176 198.144-2.56 7.68-9.728 13.312-18.432 13.312-10.752 0-19.968-8.704-19.968-19.968 0-2.048 0.512-4.096 1.536-6.144v-0.512c30.208-56.32 46.08-120.832 46.08-189.44 0-66.048-15.872-128.512-44.032-183.296-66.048-134.144-204.288-226.816-364.032-226.816-76.8 0-148.992 22.016-210.432 59.392l26.624 45.568-98.304-26.112 26.112-98.304 26.112 45.568C346.112 90.624 421.376 66.56 502.272 65.024h9.728M133.12 293.888c10.752 0 19.968 8.704 19.968 19.968 0 2.048-0.512 4.096-1.536 6.144v0.512c-30.208 56.32-46.08 120.832-46.08 189.44 0 66.048 15.872 128.512 44.032 183.296 66.048 134.144 204.288 226.816 364.032 226.816 76.8 0 148.992-22.016 210.432-59.392l-26.624-45.568 98.304 26.112-26.112 98.304-26.112-45.568c-65.024 39.424-140.288 63.488-221.184 65.536h-9.728c-140.288 0-265.728-64.512-347.648-165.888-1.536-1.536-2.56-3.072-4.096-5.12-59.904-75.776-95.744-172.032-95.744-276.48V504.832c1.024-71.168 19.968-138.24 50.176-198.144 2.048-7.68 9.728-12.8 17.92-12.8m378.88-239.104h-10.24c-75.264 1.536-150.528 23.04-217.6 61.44l-20.992-36.352c-2.048-3.072-5.12-5.12-8.704-5.12h-1.536c-4.096 0.512-7.68 3.584-8.704 7.68l-26.112 98.304c-1.536 5.632 2.048 11.264 7.168 12.288l98.304 26.112c1.024 0 1.536 0.512 2.56 0.512 3.072 0 6.144-1.536 8.192-4.096 2.56-3.072 2.56-7.68 0.512-11.264l-21.504-36.864c59.904-34.816 127.488-52.736 196.608-52.736 75.264 0 148.48 21.504 211.456 61.44 61.44 38.912 111.104 94.208 143.36 159.744 28.672 55.808 43.008 116.224 43.008 179.2 0 67.072-14.848 129.024-45.056 184.832-0.512 1.024-1.024 2.56-1.024 3.584-0.512 2.048-1.536 4.608-1.536 7.68 0 16.384 13.312 30.208 30.208 30.208 12.288 0 23.552-7.68 27.648-19.456 32.256-65.024 49.664-133.12 50.688-201.728v-0.512-6.656c0-103.424-33.792-201.216-97.28-282.112-1.536-1.536-2.56-3.584-4.096-5.12-87.04-109.056-216.576-171.008-355.328-171.008zM133.12 283.648c-12.288 0-23.552 7.68-27.648 19.456-32.256 65.024-49.664 133.12-50.688 201.728V512c0 103.424 33.792 201.216 97.28 282.112 1.536 1.536 2.56 3.584 4.096 5.12 87.552 108.032 217.088 169.984 355.84 169.984h10.24c75.264-1.536 150.528-23.04 217.6-61.44l20.992 36.352c2.048 3.072 5.12 5.12 8.704 5.12h1.536c4.096-0.512 7.68-3.584 8.704-7.68l26.112-98.304c1.536-5.632-2.048-11.264-7.168-12.288l-98.304-26.112c-1.024 0-1.536-0.512-2.56-0.512-3.072 0-6.144 1.536-8.192 4.096-2.56 3.072-2.56 7.68-0.512 11.264l21.504 36.864c-59.904 34.816-127.488 52.736-196.608 52.736-75.264 0-148.48-21.504-211.456-61.44-61.44-38.912-111.104-94.208-143.36-159.744-28.672-55.808-43.008-116.224-43.008-179.2 0-67.072 14.848-129.024 45.056-184.832 0.512-1.024 1.024-2.56 1.024-3.584 0.512-2.048 1.536-4.608 1.536-7.68-0.512-15.872-13.824-29.184-30.72-29.184z'
				})
			];
		} else {
			hs = [
				h('path', {
					fill: status ? statusColor[status] : style.stroke,
					d: 'M690.366075 350.568358c0-98.876614-79.937349-179.048571-178.558027-179.048571-98.59935 0-178.515371 80.150629-178.515371 179.048571 0 98.833958 79.916021 178.963259 178.515371 178.963259C610.428726 529.531617 690.366075 449.380988 690.366075 350.568358M376.140632 350.568358c0-75.159877 60.72082-136.072649 135.667416-136.072649 74.989253 0 135.667416 60.912772 135.667416 136.072649 0 75.117221-60.678164 136.029993-135.667416 136.029993C436.861451 486.577022 376.140632 425.664251 376.140632 350.568358M197.284012 762.923936 197.284012 778.472049l15.526785 0 291.255186 0.127968L819.784387 778.472049l15.569441 0 0-15.548113c0-139.783721-136.413897-285.581938-311.026243-273.275681-10.002833 0.703824-24.740482 9.128385-34.658002 9.938849-8.573857 0.74648 13.692577 8.232609 14.396401 16.827793 9.021745-0.789136 6.313088 13.095393 15.505457 13.095393 150.597017 0 263.14488 103.07823 263.14488 224.62651l15.441473-15.590769-285.816546-0.042656-278.991585 1.81288 15.526785 15.612097c0-82.752645 75.095893-152.70849 136.861785-191.824044 7.25152-4.58552 8.659169-17.659585 4.862784-22.906273-6.846288-9.426977-19.877697-8.701825-28.046322-6.014496C285.262018 560.521203 197.284012 667.758394 197.284012 762.923936'
				}),
				h('path', {
					fill: status ? statusColor[status] : style.stroke,
					d: 'M512.31992 1.535616c-282.766642 0-512.021328 228.89211-512.021328 511.210864 0 282.46805 229.254686 511.25352 512.021328 511.25352 117.431975 0 228.828126-39.606098 318.810964-111.204199 10.791969-8.488545 12.540865-24.22861 3.988336-34.99925-8.616513-10.770641-24.356578-12.540865-35.127218-3.94568-81.174373 64.538532-181.586603 100.241606-287.650754 100.241606-255.210864 0-462.028493-206.561693-462.028493-461.367325 0-254.762976 206.817629-461.303341 462.028493-461.303341 255.210864 0 462.092477 206.561693 462.092477 461.303341 0 87.380821-24.33525 171.093227-69.614596 243.651087-7.272848 11.645089-3.668416 27.086562 8.040657 34.35941 11.709073 7.272848 27.10789 3.62576 34.402066-7.976672 50.184787-80.406565 77.143381-173.247355 77.143381-270.055153C1024.383904 230.427726 795.10789 1.535616 512.31992 1.535616z'
				})
			];
		}
		return h(
			'svg',
			{
				x: x - width / 2 + 5,
				y: y - height / 2 + 5,
				width: 25,
				height: 25,
				viewBox: '0 0 1274 1024'
			},
			[...hs]
		);
	}
	getMenuShape(bool) {
		const { model } = this.props;
		const { x, y, height, properties } = model;
		const { status } = properties;
		const style = model.getNodeStyle();
		let path =
			'M128 768 896 768 896 682.666666 128 682.666666 128 768 128 768ZM128 554.666666 896 554.666666 896 469.333334 128 469.333334 128 554.666666 128 554.666666ZM128 256 128 341.333334 896 341.333334 896 256 128 256 128 256Z';
		if (bool) {
			path =
				'M234.901333 95.658667c27.2 0 49.258667 18.474667 49.25866699 41.237333L284.16 883.498667c0 22.76266699-22.058667 41.258667-49.258667 41.258667l0 0c-27.2 0-49.258667-18.474667-49.258667-41.258667l0-746.602667C185.642667 114.154667 207.701333 95.658667 234.901333 95.658667L234.901333 95.658667z M520.618667 95.658667c27.2 0 49.258667 18.474667 49.258667 41.237333L569.877333 883.498667c0 22.76266699-22.037333 41.258667-49.258667 41.258667l0 0c-27.2 0-49.258667-18.474667-49.258667-41.258667l1e-8-746.602667C471.36 114.154667 493.418667 95.658667 520.618667 95.658667L520.618667 95.658667z M806.336 95.658667c27.2 0 49.258667 18.474667 49.25866701 41.237333L855.594667 883.498667c0 22.76266699-22.037333 41.258667-49.258667 41.258667l0 0c-27.2 0-49.258667-18.474667-49.258667-41.258667l0-746.602667C757.077333 114.154667 779.114667 95.658667 806.336 95.658667L806.336 95.658667z';
		}
		return h(
			'svg',
			{
				x: x - 8,
				y: bool ? y + height / 2 - 18 : y + height / 2 - 16,
				width: 16,
				height: 16,
				viewBox: '0 0 1024 1024'
			},
			[
				h('path', {
					fill: status ? statusColor[status] : style.stroke,
					d: path
				})
			]
		);
	}
	getShape() {
		const { model } = this.props;
		const { x, y, width, height, properties } = model;
		const style = model.getNodeStyle();
		const multi = properties.multiInstanceLoopCharacteristics || {};
		const { status } = properties;
		return h('g', {}, [
			h('rect', {
				...style,
				x: x - width / 2,
				y: y - height / 2,
				rx: 6,
				ry: 6,
				width,
				height,
				stroke: status ? statusColor[status] : style.stroke
			}),
			this.getLabelShape(),
			multi.isSequential === true ? this.getMenuShape() : '',
			multi.isSequential === false ? this.getMenuShape(true) : ''
		]);
	}
}

class UserTaskModel extends RectNodeModel {
	constructor(data, graphModel) {
		data.text = {
			value: data.text || '用户任务',
			x: data.x,
			y: data.y
		};
		super(data, graphModel);
	}
	setAttributes() {
		this.width = 120;
		this.height = 80;
	}
	getTextStyle() {
		const style = super.getTextStyle();
		style.fontSize = 12;
		// const properties = this.properties;
		// style.color = 'black';
		return style;
	}
	getNodeStyle() {
		const style = super.getNodeStyle();
		// const properties = this.properties;

		// style.stroke = 'black';
		return style;
	}
	getAnchorStyle() {
		const style = super.getAnchorStyle();
		// style.stroke = 'black';
		style.r = 3;
		style.hover.r = 8;
		// style.hover.fill = 'black';
		// style.hover.stroke = 'black';
		return style;
	}
	getAnchorLineStyle() {
		const style = super.getAnchorLineStyle();
		// style.stroke = 'black';
		return style;
	}
	getOutlineStyle() {
		const style = super.getOutlineStyle();
		return style;
	}
}

export default {
	type: 'userTask',
	view: UserTaskView,
	model: UserTaskModel
};
