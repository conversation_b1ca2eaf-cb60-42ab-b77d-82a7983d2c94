/*
 * @Description:
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-06-20 17:43:10
 * @LastEditTime: 2024-08-12 15:01:58
 */
import { PolygonNode, PolygonNodeModel, h } from '@logicflow/core';
const statusColor = { current: '#1890ff', selected: '#2A9D8F' };

class ExclusivekView extends PolygonNode {
	getShape() {
		const { model } = this.props;
		const { x, y, width, height, points, properties } = model;
		const style = model.getNodeStyle();
		if (properties.status) {
			style.stroke = statusColor[properties.status];
		}
		return h(
			'g',
			{
				transform: `matrix(1 0 0 1 ${x - width / 2} ${y - height / 2})`
			},
			h('polygon', {
				...style,
				x,
				y,
				points
			}),
			h('path', {
				fill: style.stroke,
				d: 'm 16,15 7.42857142857143,9.714285714285715 -7.42857142857143,9.714285714285715 3.428571428571429,0 5.714285714285715,-7.464228571428572 5.714285714285715,7.464228571428572 3.428571428571429,0 -7.42857142857143,-9.714285714285715 7.42857142857143,-9.714285714285715 -3.428571428571429,0 -5.714285714285715,7.464228571428572 -5.714285714285715,-7.464228571428572 -3.428571428571429,0 z'
			})
		);
	}
	getNodeStyle() {
		const style = super.getNodeStyle();
		// style.strokeDasharray = '3 3';
		const { properties } = this;
		if (properties.status) {
			style.stroke = statusColor[properties.status];
		}
		return style;
	}
}

class ExclusivekModel extends PolygonNodeModel {
	constructor(data, graphModel) {
		data.text = {
			value: '',
			x: data.x,
			y: data.y + 40
		};
		super(data, graphModel);
		this.points = [
			[25, 0],
			[50, 25],
			[25, 50],
			[0, 25]
		];
	}
}

export default {
	type: 'exclusiveGateway',
	view: ExclusivekView,
	model: ExclusivekModel
};
