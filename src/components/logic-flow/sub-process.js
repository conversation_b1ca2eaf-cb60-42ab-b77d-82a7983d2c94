import { RectNode, RectNodeModel, h } from '@logicflow/core';
class BaseNodeView extends RectNode {
	getLabelShape() {
		const { model } = this.props;
		const { x, y, width, height } = model;
		const style = model.getNodeStyle();
		return h(
			'svg',
			{
				x: x - width / 2 + 5,
				y: y - height / 2 + 5,
				width: 25,
				height: 25,
				viewBox: '0 0 1024 1024'
			},
			[
				h('path', {
					fill: style.stroke,
					d: 'M508.292 518.042h423.36a30.966 30.966 0 0 0 30.897-30.897v-226.58a30.966 30.966 0 0 0-30.897-30.896h-423.36a30.966 30.966 0 0 0-30.897 30.897v78.685h-138.9V126.815h287.55a32.408 32.408 0 1 0 0-64.815H94.682a32.408 32.408 0 1 0 0 64.815H273.2v687.222a32.27 32.27 0 0 0 32.408 32.408h171.65v84.383a30.966 30.966 0 0 0 30.898 30.897h423.36a30.966 30.966 0 0 0 30.897-30.897V704.25a30.966 30.966 0 0 0-30.897-30.897H508.292a30.966 30.966 0 0 0-30.897 30.897v77.174h-138.9V403.791h138.9v82.805a30.623 30.623 0 0 0 30.897 30.897z m30.898 240.723a20.598 20.598 0 0 1 21.078-20.048h315.357a20.598 20.598 0 0 1 21.079 20.048v117.822a20.598 20.598 0 0 1-21.079 20.048H560.268a20.598 20.598 0 0 1-21.078-20.048z m0-444.438a20.598 20.598 0 0 1 21.078-20.049h315.357a20.598 20.598 0 0 1 21.079 20.049v117.821a20.598 20.598 0 0 1-21.079 20.049H560.268a20.598 20.598 0 0 1-21.078-20.049z'
				})
			]
		);
	}
	getMenuShape(bool) {
		const { model } = this.props;
		const { x, y, height } = model;
		const style = model.getNodeStyle();
		let path =
			'M128 768 896 768 896 682.666666 128 682.666666 128 768 128 768ZM128 554.666666 896 554.666666 896 469.333334 128 469.333334 128 554.666666 128 554.666666ZM128 256 128 341.333334 896 341.333334 896 256 128 256 128 256Z';
		if (bool) {
			path =
				'M234.901333 95.658667c27.2 0 49.258667 18.474667 49.25866699 41.237333L284.16 883.498667c0 22.76266699-22.058667 41.258667-49.258667 41.258667l0 0c-27.2 0-49.258667-18.474667-49.258667-41.258667l0-746.602667C185.642667 114.154667 207.701333 95.658667 234.901333 95.658667L234.901333 95.658667z M520.618667 95.658667c27.2 0 49.258667 18.474667 49.258667 41.237333L569.877333 883.498667c0 22.76266699-22.037333 41.258667-49.258667 41.258667l0 0c-27.2 0-49.258667-18.474667-49.258667-41.258667l1e-8-746.602667C471.36 114.154667 493.418667 95.658667 520.618667 95.658667L520.618667 95.658667z M806.336 95.658667c27.2 0 49.258667 18.474667 49.25866701 41.237333L855.594667 883.498667c0 22.76266699-22.037333 41.258667-49.258667 41.258667l0 0c-27.2 0-49.258667-18.474667-49.258667-41.258667l0-746.602667C757.077333 114.154667 779.114667 95.658667 806.336 95.658667L806.336 95.658667z';
		}
		return h(
			'svg',
			{
				x: x - 8,
				y: bool ? y + height / 2 - 18 : y + height / 2 - 16,
				width: 16,
				height: 16,
				viewBox: '0 0 1024 1024'
			},
			[
				h('path', {
					fill: style.stroke,
					d: path
				})
			]
		);
	}
	getShape() {
		const { model } = this.props;
		const { x, y, width, height, properties } = model;
		const style = model.getNodeStyle();
		const multi = properties.multiInstanceLoopCharacteristics || {};
		return h('g', {}, [
			h('rect', {
				...style,
				x: x - width / 2,
				y: y - height / 2,
				rx: 6,
				ry: 6,
				width,
				height
			}),
			this.getLabelShape(),
			multi.isSequential === true ? this.getMenuShape() : '',
			multi.isSequential === false ? this.getMenuShape(true) : ''
		]);
	}
}

class BaseNodeModel extends RectNodeModel {
	constructor(data, graphModel) {
		data.text = {
			value: data.text || '子流程',
			x: data.x,
			y: data.y
		};
		super(data, graphModel);
	}
	setAttributes() {
		this.width = 120;
		this.height = 80;
	}
	getTextStyle() {
		const style = super.getTextStyle();
		style.fontSize = 12;
		// const properties = this.properties;
		// style.color = 'black';
		return style;
	}
	getNodeStyle() {
		const style = super.getNodeStyle();
		// const properties = this.properties;

		// style.stroke = 'black';
		return style;
	}
	getAnchorStyle() {
		const style = super.getAnchorStyle();
		// style.stroke = 'black';
		style.r = 3;
		style.hover.r = 8;
		// style.hover.fill = 'black';
		// style.hover.stroke = 'black';
		return style;
	}
	getAnchorLineStyle() {
		const style = super.getAnchorLineStyle();
		// style.stroke = 'black';
		return style;
	}
	getOutlineStyle() {
		const style = super.getOutlineStyle();
		return style;
	}
}

export default {
	type: 'callActivity',
	view: BaseNodeView,
	model: BaseNodeModel
};
