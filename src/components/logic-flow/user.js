import { HtmlNode, HtmlNodeModel } from '@logicflow/core';
import userTaskNode from './userTaskNode.vue';
import Vue from 'vue';
class BaseNodeView extends HtmlNode {
	constructor(props) {
		super(props);
		this.root = document.createElement('div');
		this.vueComponent = userTaskNode;
	}
	setHtml(rootEl) {
		rootEl.appendChild(this.root);
		if (this.vm) {
			this.vm.$mount(this.root);
		} else {
			this.vm = new Vue({
				render: h =>
					h(this.vueComponent, {
						props: {
							model: this.props.model,
							graphModel: this.props.graphModel,
							disabled: this.props.graphModel.editConfigModel.isSilentMode,
							isSelected: this.props.model.isSelected,
							isHovered: this.props.model.isHovered,
							properties: this.props.model.getProperties()
						}
					})
			});
			this.vm.$mount(this.root);
		}
	}
}
class BaseNodeModel extends HtmlNodeModel {
	setAttributes() {
		this.width = 100;
		this.height = 80;
	}
	setHeight(val) {
		this.height = val;
	}
	getOutlineStyle() {
		const style = super.getOutlineStyle();
		style.stroke = 'none';
		style.hover.stroke = 'none';
		return style;
	}
}

export default {
	type: 'user',
	model: BaseNodeModel,
	view: BaseNodeView
};
