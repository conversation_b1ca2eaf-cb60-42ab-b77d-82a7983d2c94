<template>
	<div v-if="computedTabs.length > 0" ref="pageTabs" class="page-tabs" :class="{ isOver: isOver }">
		<svg-icon class="handle-pre" icon-class="seal-left" @click="scrollTo('pre')"></svg-icon>
		<svg-icon class="handle-next" icon-class="next" @click="scrollTo('next')"></svg-icon>
		<draggable
			id="scroll-content"
			v-model="computedTabs"
			class="scroll"
			animation="300"
			drag-class="drag"
			:options="options"
			@change="handleDrag"
		>
			<div
				v-for="(item, index) of computedTabs"
				:ref="'pageTabs' + index"
				:key="index"
				class="tab"
				@click="toRoute(item)"
			>
				<div v-if="currentIndex === index && index !== 0" class="left">
					<div class="left-con"></div>
				</div>
				<div class="item" :class="{ select: currentIndex === index }">
					<div class="con">
						<svg-icon
							v-if="item.meta.icon"
							:icon-class="item.meta.icon + '-s'"
							class="con-icon"
							style="color: var(--brand-6)"
						></svg-icon>
						<file-by-id
							v-else
							:value="item.meta.query.logoUrlPath == 'null' ? '' : item.meta.query.logoUrlPath"
							class="con-icon"
							:more-style="{
								marginRight: 0
							}"
							:size="20"
						></file-by-id>
						<!--						<img :src="item.meta.query.logo || ''" class="con-icon" />-->
						<div v-if="item.meta.query.title" class="text">{{ item.meta.query.title }}</div>
						<div v-else class="text">{{ item.meta.title }}</div>
					</div>
					<svg-icon
						v-if="item.name !== 'WorkSetting'"
						class="icon"
						icon-class="close"
						@click.stop="del(item)"
					></svg-icon>
				</div>
				<div v-if="currentIndex === index" class="right">
					<div class="right-con"></div>
				</div>
			</div>
		</draggable>
	</div>
</template>

<script>
import { mapMutations, mapGetters } from 'vuex';
import { deepClone, debounce } from '@/utils';
import draggable from 'vuedraggable';

export default {
	name: 'Index',
	components: {
		draggable
	},
	data() {
		return {
			options: {
				sort: true
			},
			scrollEl: null, // 滚动元素
			isOver: false // 是否溢出开始滚动
		};
	},
	computed: {
		...mapGetters(['pageTabs']),
		currentIndex() {
			return this.pageTabs.findIndex(item => {
				return item.fullPath === this.$route.fullPath;
			});
		},
		computedTabs: {
			get: function () {
				return this.pageTabs;
			},
			set: function (val) {
				this.SET_PAGE_TABS(val);
			}
		}
	},
	watch: {
		pageTabs(newVal) {
			this.reGetWidth();
		},
		currentIndex(newVal) {
			this.$nextTick(() => {
				if (this.$refs['pageTabs' + newVal]) {
					this.$refs['pageTabs' + newVal][0].scrollIntoView({ behavior: 'smooth' });
				}
			});
		}
	},
	mounted() {
		this.scrollEl = document.getElementById('scroll-content');
		this.reGetWidth();
		window.addEventListener('resize', this.reGetWidth);
	},
	destroyed() {
		window.removeEventListener('resize', this.reGetWidth);
	},
	methods: {
		...mapMutations('user', ['SET_PAGE_TABS']),
		/**重新计算宽度*/
		reGetWidth: debounce(
			function () {
				this.isOver =
					this.scrollEl &&
					this.scrollEl.clientWidth &&
					this.scrollEl.clientWidth >= this.$refs.pageTabs.clientWidth - 50;
			},
			500,
			false
		),
		handleDrag() {},
		scrollTo(type) {
			let left = type === 'next' ? 320 : -320;
			this.scrollEl.scrollBy({ left, behavior: 'smooth' });
		},
		// 删除标签页
		del(item) {
			let newArr = deepClone(this.pageTabs).filter(route => {
				return route.fullPath !== item.fullPath;
			});
			this.SET_PAGE_TABS(newArr);
			// 如果是当前路由被删除了，就跳转到最新的路由
			if (item.path === this.$route.path) {
				let path = newArr.length === 0 ? '/' : newArr[newArr.length - 1].fullPath;
				this.$router.replace(path);
				// 滚动到最后
				// this.scrollEl.scrollTo({ left: this.scrollEl.clientWidth });
			}
		},
		// 跳转路由
		toRoute(item) {
			this.$router.push(item.fullPath);
		}
	}
};
</script>

<style scoped lang="scss">
.page-tabs.isOver {
	padding: 0 24px;
}
.page-tabs {
	width: 100%;
	height: 40px;
	background: var(--brand-1);
	position: relative;
	border-radius: 12px 12px 0 0;
	.handle-pre {
		position: absolute;
		left: 0;
		width: 30px;
		height: 30px;
		top: calc(50% - 15px);
		cursor: pointer;
		display: none;
	}
	.handle-next {
		position: absolute;
		right: 0;
		width: 30px;
		height: 30px;
		top: calc(50% - 15px);
		cursor: pointer;
		display: none;
	}

	.scroll {
		display: inline-flex;
		align-items: center;
		justify-content: flex-start;
		max-width: 100%;
		height: 100%;
		overflow: auto;
		&::-webkit-scrollbar {
			width: 0;
			height: 0;
		}
	}
	.tab {
		height: 100%;
		cursor: pointer;
		@include flexBox();
		.left {
			width: 10px;
			height: 100%;
			background: #f8f9fa;
			&-con {
				width: 100%;
				height: 100%;
				background: var(--brand-1);
				border-radius: 0 0 12px 0;
			}
		}
		.right {
			width: 10px;
			height: 100%;
			background: #f8f9fa;
			&-con {
				width: 100%;
				height: 100%;
				background: var(--brand-1);
				border-radius: 0 0 0 12px;
			}
		}
		.item {
			width: 180px;
			height: 100%;
			background: transparent;
			padding: 0 10px;
			border-radius: 12px 12px 0 0;
			overflow: hidden;
			@include flexBox(space-between);
			.con {
				flex: 1;
				overflow: hidden;
				@include flexBox();
				font-size: 16px;
				font-weight: 400;
				color: $primaryTextColor;
				line-height: 24px;
				&-icon {
					width: 20px;
					height: 20px;
					border-radius: 6px;
					margin-right: 12px;
				}
				.text {
					flex: 1;
					overflow: hidden;
					font-weight: 600;
					font-size: 14px;
					color: rgba(21, 34, 76, 0.9);
					line-height: 24px;
					@include aLineEllipse;
				}
			}
			.icon {
				width: 16px;
				height: 16px;
			}
		}
		.select {
			margin: 0;
			background: #f8f9fa;
			box-shadow: inset 1px 1px 1px 0px rgba(255, 255, 255, 0.92);
		}
	}
}
.isOver {
	.handle-pre,
	.handle-next {
		display: block;
	}
}
</style>
