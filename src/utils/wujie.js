import WujieVue from 'wujie-vue2';
import { otherSystem } from '@/config';
const { setupApp, preloadApp } = WujieVue;

export default {
	install: Vue => {
		Vue.use(WujieVue);
		otherSystem.forEach(item => {
			setupApp({
				alive: true,
				name: item.name, // 这里这个名称要和展示子应用页面设置的名称一样哦！！！！
				url: item.url // 子应用的域名链接
			});
			preloadApp({
				alive: true,
				name: item.name, // 这里这个名称要和展示子应用页面设置的名称一样哦！！！！
				url: item.url // 子应用的域名链接
			});
		});
	}
};
