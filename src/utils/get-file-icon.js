const typeMap = {
	xls: 'excel',
	xlsx: 'excel',
	pdf: 'pdf',
	txt: 'txt',
	docx: 'word',
	doc: 'word'
};
function isImage(item) {
	const type = item.type || '';
	if (type.indexOf('image') >= 0) {
		return true;
	}
	return false;
}

function getBackground(item) {
	if (isImage(item)) {
		return 'none';
	} else {
		const name = item.name;
		if (!name) {
			return 'none';
		}
		const suffix = name.substring(name.lastIndexOf('.') + 1);
		console.log('suffix', suffix);
		let bg = typeMap[suffix];
		if (!bg) {
			bg = 'other';
		}
		return bg;
	}
}

export default getBackground;
