import store from '@/store';
/**
 * @Method 校验是否有权限
 * @Params el dom树
 * @Params binding 绑定的值
 * @Params binding.value  oneOfKeys[] 满足其中一个就可以显示
 * @Params binding.value  keys[] 全部满足才可以显示
 * */
function role(el, binding) {
	let { oneOfKeys = [], keys = [] } = binding.value;
	let permission_identification = store.getters.permission_identification;
	// 参数keys中必须每一项都满足
	let hasEveryRole = keys.every(item => {
		return permission_identification.includes(item);
	});
	// 参数oneOfKeys中任意一项满足即可
	// 当oneOfKeys为空的时候some返回的为false,所以要判断数组是否为空
	let hasSomeRole = true;
	if (oneOfKeys.length) {
		hasSomeRole = oneOfKeys.some(item => {
			return permission_identification.includes(item);
		});
	}
	if (!(hasEveryRole && hasSomeRole)) {
		el.style.display = 'none';
	}
}

/**
 * @Method 校验用户是否拥有应用权限
 * @Params el dom树
 * @Params binding 绑定的值
 * @Params binding.appCode 应用编码
 * @Params binding.roleCode 权限编码
 * */
function appRole(el, binding) {
	try {
		let { appCode, roleCode } = binding.value;
		let appRoleObject = store.getters.appRoles;
		let roleArr = appRoleObject[appCode];
		let index = roleArr.findIndex(item => {
			return item.perms === roleCode;
		});
		if (index > -1) {
			// console.log('应用' + appCode + '的' + roleCode + '有权限');
		} else {
			throw new Error('无权限');
		}
	} catch (err) {
		el.style.display = 'none';
	}
}

/**
 * @Method 校验用户是否拥有应用权限
 * @Desc 用于element组件v-if的判断
 * @Params binding 绑定的值
 * @Params binding.appCode 应用编码
 * @Params binding.roleCode 权限编码
 * */

export function appRoleShow(binding) {
	try {
		let { appCode, roleCode } = binding;
		let appRoleObject = store.getters.appRoles;
		let roleArr = appRoleObject[appCode];
		let index = roleArr.findIndex(item => {
			return item.perms === roleCode;
		});
		if (index > -1) {
			// console.log('应用' + appCode + '的' + roleCode + '有权限');
			return true;
		} else {
			throw new Error('无权限');
		}
	} catch (err) {
		console.log(err);
		return false;
	}
}

/**
 * 表格上方搜索框收齐展开
 * @param {Boolean} initStatus 初始状态
 * @param {Number} minHeight 最小高度
 * @param {Number} maxHeight 最高高度
 * @param {Function} changeStatus 切换状态通知方法
 * @param {String} openText 展开文字
 * @param {String} caleText 收起文字
 * @param {String} textLineHeight 展开收起文字行高
 * @param {Boolean} isSearchViews 是否显示搜索模块吧？
 * */
function handleSearch(el, binding) {
	let {
		initStatus, // 初始状态
		minHeight, // 最小高度
		maxHeight, // 最高高度
		changeStatus, // 切换状态通知方法
		openText = '展开', // 展开文字
		caleText = '收起', // 收起文字
		textLineHeight = '36px', // 展开收起文字行高
		isSearchViews = true
	} = binding.value;
	if (!isSearchViews) return;
	let _maxHeight = maxHeight ? maxHeight + 'px' : 'auto';
	//父元素
	el.style.paddingRight = '100px';
	el.style.position = 'relative';
	el.style.transition = 'all 0.3s';
	el.style.overflow = 'hidden';
	el.style.height = initStatus ? _maxHeight : minHeight + 'px';
	// 弹性布局
	let element = document.createElement('div');
	let style = `display: flex;align-items: center;justify-content: center;width: 100px;height:${textLineHeight};cursor:pointer;position:absolute;right:0;color:#0F45EA;`;
	element.setAttribute('style', style);
	// 文字
	let textDom = document.createElement('span');
	textDom.innerHTML = initStatus ? caleText : openText;
	textDom.style.lineHeight = textLineHeight;
	// 图标
	let iconDom = document.createElement('i');
	iconDom.className = 'icon-nav-bottom coos-iconfont';
	iconDom.style.transform = initStatus ? 'rotate(180deg)' : 'rotate(0)';
	iconDom.style.transition = 'all 0.3s';
	// 追加元素
	element.appendChild(textDom);
	element.appendChild(iconDom);
	// 按钮事件
	element.onclick = function () {
		if (textDom.innerHTML === openText) {
			textDom.innerHTML = caleText;
			iconDom.style.transform = 'rotate(180deg)';
			el.style.height = _maxHeight;
		} else {
			textDom.innerHTML = openText;
			iconDom.style.transform = 'rotate(0)';
			el.style.height = minHeight + 'px';
		}
		if (changeStatus) changeStatus(textDom.innerHTML !== openText);
	};
	el.append(element);
}

const directiveMap = {
	role: role,
	appRole: appRole,
	handleSearch: handleSearch
};
const install = Vue => {
	Object.keys(directiveMap).forEach(key => {
		Vue.directive(key, {
			bind: directiveMap[key]
		});
	});
};
export default { install };
