import Cookies from 'js-cookie';

const userAuthToken = 'X-Coos-Client-Access-Token';
const appRelyToken = 'coosRentApplicationRelyToken';

/**普通的cookie存储管理*/
export function set_token(key, value) {
	const expirationTime = new Date();
	expirationTime.setFullYear(expirationTime.getFullYear() + 2); //设置永不过期
	return Cookies.set(key, value, { expires: expirationTime });
}
export function get_token(key) {
	return Cookies.get(key);
}
export function remove_token(key) {
	return Cookies.remove(key);
}

//业务cookie存储管理(用户token管理)
export function setToken(token) {
	return set_token(userAuthToken, token);
}
export function getToken() {
	return get_token(userAuthToken);
}
export function removeToken() {
	return remove_token(userAuthToken);
}

//业务cookie存储管理(应用依赖接口调用的token管理)
export function setCoosRentApplicationRelyToken(token) {
	return set_token(appRelyToken, token);
}
export function getCoosRentApplicationRelyToken() {
	return get_token(appRelyToken);
}
export function removeCoosRentApplicationRelyToken() {
	return remove_token(appRelyToken);
}
