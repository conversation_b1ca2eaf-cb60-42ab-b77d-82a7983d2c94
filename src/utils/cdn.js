// 挂载cdn
import { getItem } from '@/utils/localstorage';

/**
 * 根据配置动态加载第三方项目的cdn资源
 * 需要优化的是：动态加入的时候设置一个id，每次切换用户、租户的时候更新cnd资源可以根据id进行删减，防止冗余
 * */
export function cdnLoad() {
	try {
		let coos_desk_rentInfo = JSON.parse(getItem('rentInfo'));
		let extend =
			coos_desk_rentInfo && coos_desk_rentInfo.extend ? JSON.parse(coos_desk_rentInfo.extend) : {};
		if (extend.scriptConfig && extend.scriptConfig.cdnConfig) {
			let cdnConfig = JSON.parse(extend.scriptConfig.cdnConfig);
			cdnConfig.forEach(element => {
				let id = element.split('/')[element.split('/').length - 1].replace(/\./gi, '-');
				const script = document.getElementById(id);
				// 没有挂载过才挂载
				if (!script) {
					let type = element.endsWith('.css');
					const tag = document.createElement(type ? 'link' : 'script');
					tag.id = id;
					if (type) {
						tag.rel = 'stylesheet';
						tag.href = element;
					} else {
						tag.src = element;
						// document.body.appendChild(tag);
					}
					document.head.appendChild(tag);
				}
			});
		}
	} catch (error) {
		console.log('--------------cdnConfigError', error);
	}
}
