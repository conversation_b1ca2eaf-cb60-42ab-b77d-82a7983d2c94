/**
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2024-01-16 14:05:34
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-03-05 10:47:43
 * @FilePath: /coos-desktop-app/src/utils/crypto.js
 * @Description: aes 对称加解密
 */
import CryptoJS from 'crypto-js';

/**
 * AES加密
 * @param {string} key - 加密密钥
 * @param {string} value - 需要加密的值
 * @returns {string} - 返回加密后的字符串
 */
export const encrypt = (key, value) => {
	const encrypted = CryptoJS.AES.encrypt(value, key);
	return encrypted.toString();
};

/**
 * AES解密
 * @param {string} key - 解密密钥
 * @param {string} encrypted - 需要解密的值
 * @returns {string} - 返回解密后的字符串
 */
export const decrypt = (key, encrypted) => {
	const decrypted = CryptoJS.AES.decrypt(encrypted, key);
	return decrypted.toString(CryptoJS.enc.Utf8);
};
