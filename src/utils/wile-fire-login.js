import appServer<PERSON>pi from '@/wile-fire/api/appServerApi';
import wfc from '@/wile-fire/wfc/client/wfc';
import { setItem, getItem } from '@/wile-fire/ui/util/storageHelper';
import { Message } from 'element-eoss';
import Conversation from '@/wile-fire/wfc/model/conversation';
export const loginWileFire = async () => {
	try {
		let res = await appServerApi.loginMecp({
			clientId: wfc.getClientId(),
			platform: 5
		});
		const { userId, token, portrait, clientCode, tenantId, clientUser } = res;
		wfc.connect(userId, token);
		setItem('clientCode', clientCode); // im项目分离的代码
		setItem('userId', userId);
		setItem('tenantId', tenantId);
		setItem('token', token);
		setItem('userPortrait', portrait);
		setItem('clientUser', clientUser);
	} catch (err) {
		console.log(err);
		Message({ type: 'error', message: err.message });
	}
};

export function createConversation(SingleType, userId, line) {
	let clientCode = getItem('clientCode');
	let tenantId = getItem('tenantId');
	let clientUser = getItem('clientUser');
	let targetId = userId.replace(clientCode, '').replace(tenantId, '');
	let conversation = new Conversation(
		SingleType,
		targetId + clientCode + (clientUser.indexOf(targetId) != -1 ? '' : tenantId),
		line
	);
	return conversation;
}
