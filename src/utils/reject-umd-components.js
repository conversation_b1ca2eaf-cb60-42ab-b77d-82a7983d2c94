/**es引入目录下面的所有模块，目前只是一个函数*/
// const umdArr = require.context('@/components/umd-component', true, /\.js$/); // 正式
const umdArr = require.context('@/umd-component-dev', true, /index\.js$/); // 调试
/**根据目录下面所有的键名回调函数拿到每一个模块*/
let activeComponent = umdArr
	.keys()
	.map(umdArr)
	.map(item => {
		return item.default;
	});
/**循环动态注册UMD组件*/
const install = VUE => {
	activeComponent.forEach(item => {
		VUE.use(item);
	});
};
export default {
	install
};
