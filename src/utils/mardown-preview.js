import VMdPreview from '@kangc/v-md-editor/lib/preview';
import '@kangc/v-md-editor/lib/style/preview.css';
import githubTheme from '@kangc/v-md-editor/lib/theme/github.js';
import '@kangc/v-md-editor/lib/theme/style/github.css';
import hljs from 'highlight.js/lib/core'; // highlightjs 核心代码
import xml from 'highlight.js/lib/languages/xml'; // 按需引入语言包

export default {
	install: Vue => {
		hljs.registerLanguage('xml', xml);
		VMdPreview.use(githubTheme, {
			Hljs: hljs,
			codeHighlightExtensionMap: {
				vue: 'xml'
			}
		});
		Vue.use(VMdPreview);
	}
};
