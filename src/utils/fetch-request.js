import { get_token, getToken } from '@/utils/auth';

/**统一封装的fetch请求*/
function fetchRequest(url, config) {
	return new Promise((resolve, reject) => {
		fetch(url, {
			method: config.method || 'post',
			headers: {
				'Content-Type': 'application/json',
				'X-Coos-Client-Access-Token': getToken(),
				'X-Coos-Client-Tenant-Id': get_token('X-Coos-Client-Tenant-Id')
			},
			...config
		})
			.then(async res => {
				// 1. 如果不是 JSON，则当作流处理
				if (!res.ok) {
					throw new Error(`网关${res.status}！`);
				}
				// 2. 首先检查 Content-Type 判断是 JSON 错误还是流式响应
				const contentType = res.headers.get('content-type');
				// 3. 如果是 JSON 响应，先解析检查错误码
				if (contentType && contentType.includes('application/json')) {
					const resData = await res.json();
					if (resData.code !== 200) {
						throw new Error(resData.message);
					}
				}
				resolve(res);
			})
			.catch(reason => {
				if (reason.name === 'AbortError') {
					console.log('Request aborted');
					resolve();
				} else {
					reject(reason);
				}
			});
	});
}
export default fetchRequest;
