import axios from 'axios';

const requestMap = new Map();

export function initCancelTokenInterceptors(request) {
	request.interceptors.response.use(response => {
		if (response.config && response.config.cancelKey) {
			// 请求完成后 移除 requestMap 中缓存的需要请求函数
			requestMap.delete(response.config.cancelKey);
		}

		return response;
	});

	request.interceptors.request.use(req => {
		// 检查是否需要取消重复请求操作
		if (req.cancelKey) {
			// 检查当前请求是否还存在
			if (requestMap.has(req.cancelKey)) {
				const cancel = requestMap.get(req.cancelKey);
				cancel();
			}

			req.cancelToken = new axios.CancelToken(cancel => {
				requestMap.set(req.cancelKey, cancel);
			});
		}

		return req;
	});

	request.customCancel = cancelKey => {
		if (requestMap.has(cancelKey)) {
			const cancel = requestMap.get(cancelKey);
			cancel();
		}
	};
}
