import { appsLogout } from '@/api/modules/login';
import axios from 'axios';
import { baseUrl, serveUrl } from '@/config';
const service = axios.create({
	baseURL: baseUrl,
	timeout: 30 * 1000
});
/**应用系统使用提供的静态资源地址进行退出登录处理*/
function doReqOutUrl(url) {
	let src = /http/.test(url) ? url : (serveUrl || window.location.origin) + url;
	let handleIframe = () => {
		const iframe = document.createElement('iframe');
		iframe.setAttribute('src', src);
		iframe.setAttribute('style', 'display: none;');
		document.body.appendChild(iframe);
	};
	service({
		url: src,
		method: 'get'
	})
		.then(res => {
			handleIframe();
		})
		.catch(err => {
			if (err.response && err.response.status !== 404) {
				handleIframe();
			}
		});
}

/**退出第三方的应用*/
export function logoutApps(params = {}) {
	appsLogout('authInvalid', params).then(res => {
		if (res.code !== 200) {
			return;
		}
		// 每次挂载之前先清除所有的iframe
		clearIframe();
		let urls = res.result?.url;
		if (urls) {
			let unionFlag = Math.random();
			for (let url of urls) {
				let tUrl = url.indexOf('?') == -1 ? url + '?flag=' + unionFlag : url + '&flag=' + unionFlag;
				doReqOutUrl(tUrl);
			}
		}
	});
}
/**处理退出清除iframe*/
function clearIframe() {
	let iframes = document.getElementsByTagName('iframe');
	// 不是数组，不能用forEach
	for (let i = iframes.length - 1; i >= 0; i--) {
		iframes[i].parentNode.removeChild(iframes[i]);
	}
}
