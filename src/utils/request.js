import axios from 'axios';
import { Message } from 'element-eoss';
import {
	getToken,
	get_token,
	getCoosRentApplicationRelyToken,
	setCoosRentApplicationRelyToken,
	remove_token
} from '@/utils/auth';
import { baseUrl } from '@/config';
import { initCancelTokenInterceptors } from '@/utils/cancel-token';
import { relyToken } from '@/api/modules/login';
import store from '@/store';
import router from '@/router';
import { getItem, setItem } from '@/utils/localstorage';
import { getFingerprint } from './fingerprint';
import axiosRetry from 'axios-retry';
import { checkVersion } from '@/utils/index';
import { debounce } from '@/utils';
import projectData from '@/store/data-source/project-data';
// 给请求头设置设备唯一标识
const addFingerprintToHeaders = async () => {
	let cachedFingerprint = getItem('coos-fingerprint');
	if (!cachedFingerprint) {
		cachedFingerprint = await getFingerprint(); //获取唯一标识
		if (cachedFingerprint) {
			setItem('coos-fingerprint', cachedFingerprint);
		}
	}
	return cachedFingerprint;
};

const service = axios.create({
	baseURL: baseUrl,
	timeout: 30 * 1000
});
// 配置axios的retries和retryDelay;
axiosRetry(service, {
	retries: 1, // 最大重试次数
	retryDelay: retryCount => {
		// 重试延迟，这里是固定延迟1秒，也可以根据重试次数来设定动态延迟
		return retryCount * 500;
	},
	shouldResetTimeout: true, //  重置超时时间
	retryCondition: error => {
		//true为打开自动发送请求，false为关闭自动发送请求
		return !!error.message.includes('timeout');
	},
	// 自定义 axiosRetry 的重试逻辑，确保在重试时不会重复添加 baseURL。
	onRetry: (retryCount, error, requestConfig) => {
		// 手动移除 baseURL
		if (requestConfig.url.startsWith(baseUrl)) {
			requestConfig.url = requestConfig.url.replace(baseUrl, '');
		}
	}
});
initCancelTokenInterceptors(service); // 初始化cancelToken实例，可以根据参数取消重复请求
// 请求对象
service.interceptors.request.use(
	async config => {
		// 设置token
		if (getToken()) {
			config.headers['X-Coos-Client-Access-Token'] = getToken();
			config.headers['X-Coos-Client-Tenant-Id'] = get_token('X-Coos-Client-Tenant-Id');
		}
		config.headers['X-Coos-Terminal-Type'] = '1'; //终端类型
		config.headers['X-Coos-Terminal-OS'] = 'Windows'; //终端系统类型
		config.headers['X-Coos-Terminal-Code'] = await addFingerprintToHeaders(); // 添加指纹

		// 如果是post请求的表单格式并且含有data数据
		if (config.method === 'post' && config.isFormData && config.data) {
			config.headers['Content-type'] = 'multipart/form-data;charset=UTF-8';
			let formData = new FormData();
			Object.keys(config.data).forEach(key => {
				const value = config.data[key];
				if (Array.isArray(value)) {
					value.forEach((item, index) => {
						formData.append(`${key}`, item);
					});
					return;
				}
				formData.append(key, config.data[key]);
			});

			config.data = formData;
		}
		if (config.params) {
			let params = {};
			Object.keys(config.params).forEach(key => {
				if (config.params[key] || config.params[key] === 0 || config.params[key] === false) {
					params[key] = config.params[key];
				}
			});
			config.params = params;
		}
		return config;
	},
	error => {
		return Promise.reject(error);
	}
);

function handle401(res, tip = true) {
	// 新能源免密登录不做提示
	if (localStorage.getItem('SecretFree') === 'true') return;
	store.commit('user/REMOVE_INFO');
	Message.closeAll();
	// 没有提示信息就不提示
	if (res.message && tip) {
		Message({
			message: res.message,
			type: 'error',
			duration: 1.5 * 1000
		});
	}
	setTimeout(() => {
		router.push(
			`/login?code=authInvalid&redirect=${encodeURIComponent(router.currentRoute.fullPath)}`
		);
	}, 1500);
}
const check = debounce(
	res => {
		checkVersion(tip => handle401(res, tip));
	},
	3000,
	true
);

// 响应对象
service.interceptors.response.use(
	async response => {
		let isBlob = response.data instanceof Blob; // 文件流类型
		const res = isBlob ? response : response.data;
		if (res.code === 401) {
			// 判断是否需要更新提示功能
			if (projectData[process.env.VUE_APP_PROJECT]?.notUpdateTip) {
				handle401(res);
			} else {
				check(res);
			}
			return Promise.reject(res.message);
		} else if (res.code === 510) {
			const rentInfoId = JSON.parse(getItem('rentInfo')).id;
			// 重新获取令牌刷新token
			const res1 = await relyToken(rentInfoId);
			setCoosRentApplicationRelyToken(res1.result);
			response.config.headers['X-Application-Rely-Token'] = getCoosRentApplicationRelyToken();
			// 再次以当前参数重新请求接口
			return axios.request(response.config);
		} else if (res.code === 302) {
			remove_token('X-Coos-Third-Origin');
			remove_token('X-Coos-Third-Origin-Url');
		} else {
			return res;
		}
	},
	error => {
		if (!error.__CANCEL__) {
			let message;
			if (error.response) {
				message = error.response.data?.message || '未知异常，请联系管理员！';
				// nginx上传限制413文件过大
				if (error.response.status === 413) {
					message = '上传文件过大!';
				} else if (error.response.status === 401) {
					store.commit('user/REMOVE_INFO');
					setTimeout(() => {
						router.push(`/login?redirect=${encodeURIComponent(router.currentRoute.fullPath)}`);
					}, 1500);
				} else if (error.response.status === 510) {
					const rentInfoId = JSON.parse(getItem('rentInfo')).id;
					relyToken(rentInfoId).then(async res => {
						setCoosRentApplicationRelyToken(res.result);
						const res2 = await axios.request(error.config);
						console.log(res2);
					});
				} else if (error.response.status === 502) {
					message = '服务不可用!';
				} else if (error.response.status === 404) {
					message = '服务不存在!';
				}
			} else if (error.message.includes('timeout')) {
				message = '请求超时!';
			} else if (error.message === 'Network Error') {
				message = '网络开小差了!';
			} else {
				message = error.message;
			}
			Message.closeAll();
			// 没有提示信息就不提示
			if (message) {
				Message({
					message,
					type: 'error',
					duration: 3 * 1000
				});
			}
		}
		return Promise.reject(error);
	}
);

export default service;
