/**缓存隔离，区分租户端、用户端、平台端的缓存前缀*/
const catch_pre = 'coos_desk_';
// localStorage.setItem('RENT_END_PRE', catch_pre);
export const setItem = (key, value) => {
	localStorage.setItem(catch_pre + key, value);
};
export const getItem = key => {
	return localStorage.getItem(catch_pre + key);
};
export const removeItem = key => {
	localStorage.removeItem(catch_pre + key);
};
// 会话级别的缓存
export const setSessionItem = (key, value) => {
	sessionStorage.setItem(catch_pre + key, value);
};
export const getSessionItem = key => {
	return sessionStorage.getItem(catch_pre + key);
};
export const removeSessionItem = key => {
	sessionStorage.removeItem(catch_pre + key);
};
const customLocalstorage = {
	setItem,
	getItem,
	removeItem,
	setSessionItem,
	getSessionItem,
	removeSessionItem
};
const install = Vue => {
	Vue.prototype.$localStorage = customLocalstorage;
};
export default { install };
