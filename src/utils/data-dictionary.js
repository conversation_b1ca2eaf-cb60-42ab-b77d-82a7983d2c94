/**所有枚举的键值对*/
const enumeration = {
	模块编号: {
		用户端工作台: 'module:clientWorkSpace',
		租户端企业概况: 'module:tenantWorkSpace'
	},
	日程: {
		会议: 'slotAccess:schedule:meeting'
	},
	智能助手: {
		创意智绘: '1',
		智答宝库: '2',
		智控领航: '3',
		数据智析: '4',
		ppt: '101',
		智能问数: '102'
	},
	AI编码: {
		创意智绘: 'AI-cyzh',
		智答宝库: 'AI-zdbk',
		智控领航: 'AI-zklh',
		数据智析: 'AI-sjzx',
		ppt: 'AI-ppt',
		智能问数: 'AI-znws',
		智问待办: 'AI-zwdb',
		问答助手: 'AI-wdzs',
		智答私库: 'AI-zdsk',
		写作助手: 'AI-xzzs',
		写作助手3: 'AI-xzzs3',
		文稿纠错: 'AI-wgjc',
		我要提问: 'AI-zdbk2',
		知识库: 'AI-zsk',
		智能问数H5: 'AI-znwsH5',
		智能问政H5: 'AI-znwzH5'
	},
	应用ID: {
		待办: 'A1006',
		文档: 'A1007',
		通讯录: 'A1002',
		日历: 'A1005',
		工作台: 'A1003',
		消息: 'A1004',
		系统表单: 'A1008'
	}
};
/**
 * 获取字典对应的值
 * @param {String} key 'key1/key2' key1是枚举的模块，key2是模块中具体的枚举
 * */
export function getDictionary(key) {
	let keyArr = key.split('/');
	let enumerationType = keyArr[0];
	let dataType = keyArr[1];
	return enumeration[enumerationType]?.[dataType];
}

/**
 * 通过值获取中文
 * @param {String} value 根据value获取对应的枚举中文key
 * */
export function getKeyByValue(value) {
	let map = Object.values(enumeration).reduce((source, item) => {
		source = Object.assign(source, item);
		return source;
	}, {});
	return Object.keys(map).find(key => {
		return map[key] === value;
	});
}
