/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-12-23 14:57:23
 * @LastEditTime: 2024-12-26 11:30:17
 * @LastEditors: zhangrongqiu
 * @Description: 浏览器指纹
 */

import FingerprintJS from '@fingerprintjs/fingerprintjs';

export const getFingerprint = async () => {
	try {
		const fp = await FingerprintJS.load();
		const result = await fp.get();
		let terminalFlag = result?.components.webGlBasics.value.rendererUnmasked || result?.visitorId; //使用 FingerprintJS 库提供的 visitorId
		return btoa(terminalFlag);
	} catch (error) {
		console.error('Error getting fingerprint:', error);
		return '';
	}
};
