import FileById from '@/components/file-by-id';
import UploadFile from '@/components/upload-file';
import BasicEmpty from '@/components/basic-empty/index';
import BasicItem from '@/components/basic-item/index';
/**自动引入三方集成的下面的路由文件*/
const componentsFiles = require.context('../third-party', true, /components.js/);
const components = componentsFiles.keys().reduce((modules, modulePath) => {
	const moduleRouter = componentsFiles(modulePath);
	modules = Object.assign(modules, moduleRouter.default);
	return modules;
}, {});
const componentsMap = {
	FileById: FileById,
	UploadFile: UploadFile,
	BasicEmpty: BasicEmpty,
	BasicItem: BasicItem,
	...components
};
const install = Vue => {
	Object.keys(componentsMap).forEach(key => {
		Vue.component(key, componentsMap[key]);
	});
};
export default { install };
