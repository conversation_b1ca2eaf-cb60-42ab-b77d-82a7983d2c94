import { download, download2 } from '@/api/modules/document-content';

/**
 * @description 通过链接下载流式数据，重命名文件
 * @param {Object} config -配置
 * @param {String} config.url -下载地址
 * @param {String} config.name -下载文件名称
 * @param {Object} config.params -下载参数
 * @param {Function} config.onDownloadProgress -下载进度通知
 * @param {Function} config.success -下载成功的通知
 * @param {Function} config.fail -下载失败的通知
 * @param {String} config.cancelKey -关闭请求的key
 * @return null
 * */
export function downloadFile(config) {
	let { url, name, params = {}, onDownloadProgress, success, fail, cancelKey, reqFn } = config;
	let requestMethod = reqFn ? download2 : download;
	// 本地开发环境，全路径前缀替代为代理
	if (process.env.NODE_ENV === 'development') {
		url = url.replace(/http.+\/coos_api/, '');
	}
	requestMethod(
		url,
		params,
		e => {
			if (onDownloadProgress) {
				onDownloadProgress(e);
			}
		},
		cancelKey
	)
		.then(async res => {
			if (success) {
				success(res);
			}
			let fileName = name || '';
			// 返回的文件流有这个头部，要解析名字
			if (!name && res.headers['content-disposition']) {
				let defaultFileName = res.headers['content-disposition']
					.split(';')[1]
					.split('filename=')[1]; //从响应头中获取文件名
				fileName = decodeURI(defaultFileName); //解码
			}
			const file = new Blob([res.data]);
			// 把二进制数据生成对应的url地址（缓存地址）
			const objectUrl = URL.createObjectURL(file);
			const a = document.createElement('a');
			document.body.appendChild(a);
			a.style.display = 'none';
			a.href = objectUrl;
			a.download = fileName;
			console.log(fileName, 'downLoad-fileName');
			a.click();
			window.URL.revokeObjectURL(objectUrl);
			document.body.removeChild(a);
		})
		.catch(err => {
			if (!err.__CANCEL__) {
				if (fail) {
					fail(err);
				}
			}
		});
}
