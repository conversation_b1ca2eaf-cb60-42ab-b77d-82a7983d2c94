import Bus, { CoosEventTypes } from '@/utils/bus';
const _BUS = new Bus();
import router from '@/router';
import Vue from 'vue';
import { preUrl } from '@/config';
/**供coos子项目使用的全局sdk*/
const COOS_SDK = {
	/**子项目在弹窗中打开，调用此事件关闭弹窗*/
	closePopup: handleType => {
		_BUS.$emit(CoosEventTypes.closePopup, handleType);
	},
	/**打开三方项目需要打开的webview*/
	openOther: ({ title = '', url = '' }) => {
		let mainUrl = /http/gi.test(url) ? url : (preUrl || window.location.origin) + url;
		router.push(`/other-system?url=${encodeURIComponent(mainUrl)}&name=${title}`);
	},
	/**判断内部组件是否存在*/
	isExitComponents: name => {
		return typeof Vue.component(name) !== 'undefined';
	}
};

export default {
	install: Vue => {
		/**公共bus组件传参*/
		Vue.prototype._BUS = _BUS;
		window.COOS_SDK = COOS_SDK;
	}
};
