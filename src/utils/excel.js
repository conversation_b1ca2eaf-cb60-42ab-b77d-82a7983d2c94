import * as XLSX from 'xlsx';

/**
 * 导出Excel模板
 * @param {Array} headers 表头 ['姓名', '年龄', '性别', '手机号']
 * @param {String} sheetName 表单名称
 * @param {String} fileName 文件名称
 */
export function exportTemplate(headers, sheetName = 'Sheet1', fileName = 'template.xlsx') {
	// 创建一个工作簿
	const wb = XLSX.utils.book_new();

	// 将表头处理为正确格式 - 确保是二维数组
	const headerRow = Array.isArray(headers[0]) ? headers : [headers];

	// 创建一个工作表
	const ws = XLSX.utils.aoa_to_sheet(headerRow);

	// 设置列宽 (可选)
	const colWidth = headerRow[0].map(h => ({ wch: Math.max(10, h.length * 2) }));
	ws['!cols'] = colWidth;

	// 将工作表添加到工作簿
	XLSX.utils.book_append_sheet(wb, ws, sheetName);
	1;

	// 导出Excel文件
	XLSX.writeFile(wb, fileName);
}

/**
 * 解析Excel数据
 * @param {File} file 上传的Excel文件
 * @param {Function} callback 回调函数
 */
export function parseExcel(file, callback) {
	const reader = new FileReader();

	reader.onload = e => {
		try {
			const data = e.target.result;
			const workbook = XLSX.read(data, { type: 'array' });

			// 获取第一个工作表
			const firstSheetName = workbook.SheetNames[0];
			const worksheet = workbook.Sheets[firstSheetName];

			// 将工作表转换为JSON
			const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

			callback(null, jsonData);
		} catch (error) {
			callback(error);
		}
	};

	reader.onerror = error => {
		callback(error);
	};

	reader.readAsArrayBuffer(file);
}

/**
 * 导出Excel数据
 * @param {Array} data 导出的数据
 * @param {Array} headers 表头
 * @param {String} sheetName 表单名称
 * @param {String} fileName 文件名称
 */
export function exportData(data, headers, sheetName = 'Sheet1', fileName = 'export.xlsx') {
	// 创建一个工作簿
	const wb = XLSX.utils.book_new();

	// 处理数据
	const exportData = [headers];

	// 添加数据行
	data.forEach(item => {
		const row = headers.map(header => item[header] || '');
		exportData.push(row);
	});

	// 创建一个工作表
	const ws = XLSX.utils.aoa_to_sheet(exportData);

	// 将工作表添加到工作簿
	XLSX.utils.book_append_sheet(wb, ws, sheetName);

	// 导出Excel文件
	XLSX.writeFile(wb, fileName);
}
