import { getWatermark } from '@/api/modules/component';
import store from '@/store';
import { getDictionary } from '@/utils/data-dictionary';
/**更新全部的水印*/
export function updateWater() {
	getImWater();
	getDocWater();
}
/**im的水印配置*/
export function getImWater() {
	getWatermark(getDictionary('应用ID/消息')).then(res => {
		let isWaterMarker = res.result?.open || false;
		let waterMarker = res.result.content || '';
		store.commit('settings/SET_WATER_CONFIG', { im: { isWaterMarker, waterMarker } });
	});
}
/**文档的水印配置*/
export function getDocWater() {
	getWatermark(getDictionary('应用ID/文档')).then(res => {
		let isWaterMarker = res.result?.open || false;
		let waterMarker = res.result.content || '';
		store.commit('settings/SET_WATER_CONFIG', { doc: { isWaterMarker, waterMarker } });
	});
}
