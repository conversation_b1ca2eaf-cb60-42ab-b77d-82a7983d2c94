/**
 * Created by PanJiaChen on 16/11/18.
 */

import { previewUrl, serveUrl } from '@/config';
import { Message } from 'element-eoss';
import axios from 'axios';
import { getItem, setItem } from '@/utils/localstorage';

/**
 * Parse the time to string
 * @param {(Object|string|number)} time
 * @param {string} cFormat
 * @returns {string | null}
 */
export function parseTime(time, cFormat) {
	if (arguments.length === 0 || !time) {
		return null;
	}
	const format = cFormat || '{y}-{m}-{d} {h}:{i}:{s}';
	let date;
	if (typeof time === 'object') {
		date = time;
	} else {
		if (typeof time === 'string') {
			if (/^[0-9]+$/.test(time)) {
				// support "1548221490638"
				time = parseInt(time);
			} else {
				// support safari
				// https://stackoverflow.com/questions/4310953/invalid-date-in-safari
				time = time.replace(new RegExp(/-/gm), '/');
			}
		}

		if (typeof time === 'number' && time.toString().length === 10) {
			time = time * 1000;
		}
		date = new Date(time);
	}
	const formatObj = {
		y: date.getFullYear(),
		m: date.getMonth() + 1,
		d: date.getDate(),
		h: date.getHours(),
		i: date.getMinutes(),
		s: date.getSeconds(),
		a: date.getDay()
	};
	const time_str = format.replace(/{([ymdhisa])+}/g, (result, key) => {
		const value = formatObj[key];
		// Note: getDay() returns 0 on Sunday
		if (key === 'a') {
			return ['日', '一', '二', '三', '四', '五', '六'][value];
		}
		return value.toString().padStart(2, '0');
	});
	return time_str;
}

/**通过日期获取周几*/
export function getCharByDay(day) {
	return ['周日', '周一', '周二', '周三', '周四', '周五', '周六'][day];
}

/**
 * @param {number} time
 * @param {string} option
 * @returns {string}
 */
export function formatTime(time, option) {
	if (('' + time).length === 10) {
		time = parseInt(time) * 1000;
	} else {
		time = +time;
	}
	const d = new Date(time);
	const now = Date.now();

	const diff = (now - d) / 1000;

	if (diff < 30) {
		return '刚刚';
	} else if (diff < 3600) {
		// less 1 hour
		return Math.ceil(diff / 60) + '分钟前';
	} else if (diff < 3600 * 24) {
		return Math.ceil(diff / 3600) + '小时前';
	} else if (diff < 3600 * 24 * 2) {
		return '1天前';
	}
	if (option) {
		return parseTime(time, option);
	} else {
		return (
			d.getMonth() + 1 + '月' + d.getDate() + '日' + d.getHours() + '时' + d.getMinutes() + '分'
		);
	}
}

/**
 * @param {string} url
 * @returns {Object}
 */
export function getQueryObject(url) {
	url = url == null ? window.location.href : url;
	const search = url.substring(url.lastIndexOf('?') + 1);
	const obj = {};
	const reg = /([^?&=]+)=([^?&=]*)/g;
	search.replace(reg, (rs, $1, $2) => {
		const name = decodeURIComponent($1);
		let val = decodeURIComponent($2);
		val = String(val);
		obj[name] = val;
		return rs;
	});
	return obj;
}

/**
 * @param {string} input value
 * @returns {number} output value
 */
export function byteLength(str) {
	// returns the byte length of an utf8 string
	let s = str.length;
	for (var i = str.length - 1; i >= 0; i--) {
		const code = str.charCodeAt(i);
		if (code > 0x7f && code <= 0x7ff) s++;
		else if (code > 0x7ff && code <= 0xffff) s += 2;
		if (code >= 0xdc00 && code <= 0xdfff) i--;
	}
	return s;
}

/**
 * @param {Array} actual
 * @returns {Array}
 */
export function cleanArray(actual) {
	const newArray = [];
	for (let i = 0; i < actual.length; i++) {
		if (actual[i]) {
			newArray.push(actual[i]);
		}
	}
	return newArray;
}

/**
 * @param {Object} json
 * @returns {Array}
 */
export function param(json) {
	if (!json) return '';
	return cleanArray(
		Object.keys(json).map(key => {
			if (json[key] === undefined) return '';
			return encodeURIComponent(key) + '=' + encodeURIComponent(json[key]);
		})
	).join('&');
}

/**
 * @param {string} url
 * @returns {Object}
 */
export function param2Obj(url) {
	const search = decodeURIComponent(url.split('?')[1]).replace(/\+/g, ' ');
	if (!search) {
		return {};
	}
	const obj = {};
	const searchArr = search.split('&');
	searchArr.forEach(v => {
		const index = v.indexOf('=');
		if (index !== -1) {
			const name = v.substring(0, index);
			const val = v.substring(index + 1, v.length);
			obj[name] = val;
		}
	});
	return obj;
}

/**
 * @param {string} val
 * @returns {string}
 */
export function html2Text(val) {
	const div = document.createElement('div');
	div.innerHTML = val;
	return div.textContent || div.innerText;
}

/**
 * Merges two objects, giving the last one precedence
 * @param {Object} target
 * @param {(Object|Array)} source
 * @returns {Object}
 */
export function objectMerge(target, source) {
	if (typeof target !== 'object') {
		target = {};
	}
	if (Array.isArray(source)) {
		return source.slice();
	}
	Object.keys(source).forEach(property => {
		const sourceProperty = source[property];
		if (typeof sourceProperty === 'object') {
			target[property] = objectMerge(target[property], sourceProperty);
		} else {
			target[property] = sourceProperty;
		}
	});
	return target;
}

/**
 * @param {HTMLElement} element
 * @param {string} className
 */
export function toggleClass(element, className) {
	if (!element || !className) {
		return;
	}
	let classString = element.className;
	const nameIndex = classString.indexOf(className);
	if (nameIndex === -1) {
		classString += '' + className;
	} else {
		classString =
			classString.substr(0, nameIndex) + classString.substr(nameIndex + className.length);
	}
	element.className = classString;
}

/**
 * @param {string} type
 * @returns {Date}
 */
export function getTime(type) {
	if (type === 'start') {
		return new Date().getTime() - 3600 * 1000 * 24 * 90;
	} else {
		return new Date(new Date().toDateString());
	}
}

/**
 * @param {Function} func
 * @param {number} wait
 * @param {boolean} immediate
 * @return {*}
 */
export function debounce(func, wait, immediate) {
	let timeout, globalArgs, context, timestamp, result;

	const later = function () {
		// 据上一次触发时间间隔
		const last = +new Date() - timestamp;

		// 上次被包装函数被调用时间间隔 last 小于设定时间间隔 wait
		if (last < wait && last > 0) {
			timeout = setTimeout(later, wait - last);
		} else {
			timeout = null;
			// 如果设定为immediate===true，因为开始边界已经调用过了此处无需调用
			if (!immediate) {
				result = func.apply(context, globalArgs);
				if (!timeout) context = globalArgs = null;
			}
		}
	};

	return function (...args) {
		// args属于代码块里面的局部变量，上面的later函数中无法使用
		globalArgs = args;
		context = this;
		timestamp = +new Date();
		const callNow = immediate && !timeout;
		// 如果延时不存在，重新设定延时
		if (!timeout) timeout = setTimeout(later, wait);
		if (callNow) {
			result = func.apply(context, args);
			context = args = null;
		}

		return result;
	};
}

/**
 * This is just a simple version of deep copy
 * Has a lot of edge cases bug
 * If you want to use a perfect deep copy, use lodash's _.cloneDeep
 * @param {Object} source
 * @returns {Object}
 */
export function deepClone(source) {
	if (!source && typeof source !== 'object') {
		throw new Error('error arguments', 'deepClone');
	}
	const targetObj = source.constructor === Array ? [] : {};
	Object.keys(source).forEach(keys => {
		if (source[keys] && typeof source[keys] === 'object') {
			targetObj[keys] = deepClone(source[keys]);
		} else {
			targetObj[keys] = source[keys];
		}
	});
	return targetObj;
}

/**
 * @param {Array} arr
 * @returns {Array}
 */
export function uniqueArr(arr) {
	return Array.from(new Set(arr));
}

/**
 * @returns {string}
 */
export function createUniqueString() {
	const timestamp = +new Date() + '';
	const randomNum = parseInt((1 + Math.random()) * 65536) + '';
	return (+(randomNum + timestamp)).toString(32);
}

/**
 * Check if an element has a class
 * @param {HTMLElement} elm
 * @param {string} cls
 * @returns {boolean}
 */
export function hasClass(ele, cls) {
	return !!ele.className.match(new RegExp('(\\s|^)' + cls + '(\\s|$)'));
}

/**
 * Add class to element
 * @param {HTMLElement} elm
 * @param {string} cls
 */
export function addClass(ele, cls) {
	if (!hasClass(ele, cls)) ele.className += ' ' + cls;
}

/**
 * Remove class from element
 * @param {HTMLElement} elm
 * @param {string} cls
 */
export function removeClass(ele, cls) {
	if (hasClass(ele, cls)) {
		const reg = new RegExp('(\\s|^)' + cls + '(\\s|$)');
		ele.className = ele.className.replace(reg, ' ');
	}
}

/**判断是否为空*/
export function isEmpty(obj) {
	if (Array.isArray(obj)) {
		return obj.length === 0;
	} else if (Object.prototype.toString.call(obj) === '[object Object]') {
		return Object.keys(obj).length === 0;
	} else {
		return !obj;
	}
}

/**
 * 预览文档
 * @param {String} fileUrl 预览文档的地址
 * */
export function previewFile(fileUrl) {
	fileUrl = /^http/.test(fileUrl) ? fileUrl : (serveUrl || window.location.origin) + fileUrl;
	let url = previewUrl + '?url=' + encodeURIComponent(btoa(encodeURIComponent(fileUrl)));
	window.open(url);
}

/**循环菜单，遍历出url和title的映射关系*/
export function getMenuTitleMap(menus) {
	let menuTitleMap = {};
	const createMap = arr => {
		arr.forEach(item => {
			if (item.children && item.children.length) {
				createMap(item.children);
			}
			if (item.meta && (item.meta.url || item.path)) {
				menuTitleMap[item.meta.url || item.path] = item.meta.title || '无标题';
			}
		});
	};
	createMap(menus);
	return menuTitleMap;
}

/**
 * 文件大小转换
 * @param {Number,String} size 文件字节大小
 * */
export function tabSize(size) {
	size = parseInt(size);
	let text = '';
	if (size < 1024 * 1024) {
		text = (size / 1024).toFixed(2) + 'KB';
	} else if (1024 * 1024 < size < 1024 * 1024 * 1024) {
		text = (size / (1024 * 1024)).toFixed(2) + 'MB';
	} else {
		text = (size / (1024 * 1024 * 1024)).toFixed(2) + 'GB';
	}
	return text;
}
/**
 * 数据字典 Value 类型转换 String ====> Number
 * @param {arr} arr 数组
 * */
export function dictValuesToNumbers(items) {
	return items.map(item => ({
		...item,
		itemValue: Number(item.itemValue)
	}));
}

function sendNotification(title, options) {
	new Notification(title, options);
}
/**桌面通知*/
export function showNotification(title, options) {
	// 检查浏览器是否支持Notification API
	if ('Notification' in window) {
		// 检查用户是否已授权显示通知
		if (Notification.permission === 'granted') {
			// 如果已授权，直接显示通知
			sendNotification(title, options);
		} else {
			// 请求授权显示通知
			Notification.requestPermission().then(permission => {
				if (permission === 'granted') {
					sendNotification(title, options);
				}
			});
		}
	} else {
		Message.error('您的浏览器不支持通知功能');
	}
}
export function processList(arr, checkedKey, sortKey) {
	let newArr = [];
	arr.forEach(list => {
		list.act = list[checkedKey];
		if (list.act) {
			newArr.push(list);
		}
	});
	newArr.sort((a, b) => a[sortKey] - b[sortKey]);
	return [...newArr];
}
// 表头部重新渲染
export function renderHeader(h, { column, $index }) {
	// 新建一个 span
	let span = document.createElement('span');
	// 设置表头名称
	span.innerText = column.label;
	// 临时插入 document
	document.body.appendChild(span);
	column.minWidth = span.getBoundingClientRect().width + 100;
	// 移除 document 中临时的 span
	document.body.removeChild(span);
	return h('span', column.label);
}
/**
 * 数组转换树
 * 示例：将数组的数据转换为树选择器所需的树状数据
 * @param {*} param
 * @returns 树状数组结构数据
 */
export const arrayToTree = ({ data, id = 'id', pid = 'pid' }) => {
	// 将数据存储为 以 id 为 KEY 的 map 索引数据列
	const map = {};
	data.forEach(item => {
		map[item[id]] = item;
	});

	const arr = [];
	data.forEach(item => {
		// 以当前遍历项的pid,去map对象中找到对应对象
		const parent = map[item[pid]];
		// 如果找到索引，那么说明此项不在顶级当中,那么需要把此项添加到，他对应的父级中
		if (parent) {
			(parent.children || (parent.children = [])).push(item);
		} else {
			// 如果没有在map中找到对应的索引ID,那么直接把 当前的item添加到 arr结果集中，作为顶级
			arr.push(item);
		}
	});
	return arr;
};
/**
 * 根据数组对象中的key及value获取多维数组中满足条件的对象
 * @param {*} array 数组数据
 * @param {*} objKeyName 对象的key
 * @param {*} objKeyValue 对象的值
 * @param {boolean} isReturnAll 是否返回全部
 * @returns 满足条件的对象数组
 */
export const findObjectByKeyValue = (array, objKeyName, objKeyValue, isReturnAll = false) => {
	const result = [];

	const recursiveSearch = arr => {
		for (const item of arr) {
			if (Array.isArray(item)) {
				recursiveSearch(item); // 递归遍历嵌套数组
			} else if (typeof item === 'object' && item !== null) {
				if (item[objKeyName] === objKeyValue) {
					result.push(item); // 将满足条件的对象添加到结果数组中
					if (!isReturnAll) {
						return; // 如果只需要返回第一个满足条件的对象，则直接返回结果
					}
				}
				Object.values(item).forEach(value => {
					if (Array.isArray(value)) {
						recursiveSearch(value); // 递归遍历嵌套对象的值
					}
				});
			}
		}
	};

	recursiveSearch(array);
	// 根据入参条件返回对应的数组或对象
	if (isReturnAll) {
		return result;
	}
	return result.length > 0 ? result[0] : {};
};
export function urlHttp(url) {
	// 正则表达式用于检测 Base64 编码
	const base64Regex = /^([A-Za-z0-9+/]{4})*([A-Za-z0-9+/]{3}=|[A-Za-z0-9+/]{2}==)?$/;
	// 辅助函数：判断是否为 Base64 编码
	function isBase64(str) {
		return base64Regex.test(str);
	}
	// 辅助函数：判断是否为 HTTP URL
	function isHttpUrl(str) {
		return /^(http|https):\/\/.*/i.test(str);
	}
	// 辅助函数：判断是否为 Data URI
	function isDataUri(str) {
		return str && str.indexOf('data:') === 0;
	}
	// 辅助函数：判断是否为相对路径
	function isRelativePath(str) {
		return str && (str.startsWith('../') || str.startsWith('./'));
	}
	if (isRelativePath(url)) {
		return url; // 已经是 相对路径，直接返回
	}
	if (isDataUri(url)) {
		return url; // 已经是 Data URI，直接返回
	}
	if (isBase64(url)) {
		// 假设 Base64 数据默认为 PNG 图片，可以根据实际情况调整 MIME 类型
		return `data:image/png;base64,${url}`;
	}
	if (isHttpUrl(url)) {
		return url; // 已经是 HTTP URL，直接返回
	}
	// 如果都不是，则拼接 serveUrl
	return `${serveUrl}${url}`;
}

/**检测更新*/
export function checkVersion(callback) {
	axios.get(`${window.location.origin}${window.location.pathname}version.json`, {}).then(res => {
		if (res.data.version != getItem('VERSION')) {
			window.vm
				.$confirm('检测到有新版本，是否立即更新?', '更新提示', {
					confirmButtonText: '立即更新',
					showCancelButton: false,
					type: 'warning'
				})
				.then(() => {
					setItem('VERSION', res.data.version);
					location.reload();
					callback(false);
				});
		} else {
			callback();
		}
	});
}
/*
 * 判断url中是否有参数
 * name为参数名
 * */
export function GetQueryString(url, name) {
	//name为参数名
	let reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)');
	let r = url.match(reg);
	if (r != null) return unescape(r[2]);
	return null;
}
