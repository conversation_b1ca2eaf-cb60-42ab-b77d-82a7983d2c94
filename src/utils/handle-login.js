import { setToken, setCoosRentApplicationRelyToken } from '@/utils/auth';
import { Message } from 'element-eoss';
import store from '@/store';
import router from '@/router';
import { relyToken } from '@/api/modules/login';
import { CoosEventTypes } from '@/utils/bus';
/**
 * @Descrict 处理登录结果
 * @Param {Object} {result} 登录结果
 * @Param {String} redirectUrl 重定向路由
 * @Param {Boolean} isPrecedenceRedirect 是否优先重定向路由
 * */
export async function handleLoginRes({ result }, redirectUrl = '/', isPrecedenceRedirect = false) {
	try {
		const {
			token,
			userInfo,
			tenantList,
			clientSystemMode,
			openIm,
			forceEditInitPwd,
			version,
			tenantExtend
		} = result;
		setToken(token);
		store.commit('user/SET_USERINFO', userInfo); // 用户信息
		store.commit('user/SET_RENT_LIST', tenantList); // 租户列表
		// 筛选出租户信息
		let currentRentInfo = tenantList.find(item => {
			return item.id === userInfo.loginTenantId;
		});
		store.commit('user/SET_RENT_INFO', currentRentInfo); // 存储租户信息
		store.commit('user/SET_USER_VERSION', version); // 存储用户版本
		store.commit('settings/SET_MODE', clientSystemMode); // 存储框架模式
		store.commit('app/SET_SYSTEM_INFO', JSON.parse(tenantExtend).clientBasic); // 存储系统信息
		await store.dispatch('settings/GET_APP_ROLES'); // 获取权限
		await store.dispatch('settings/GET_FILE_MAX'); // 获取文件大小限制
		store.commit('user/SET_OPEN_IM', openIm); // 存储是否打开im
		await getRToken(userInfo.loginTenantId);
		// 因为在reloadProject事件中会重新登陆IM，所以不重复处理
		// if (openIm) {
		// 	await loginWileFire(); // 同步野火登录
		// }
		window.vm._BUS.$emit(CoosEventTypes.reloadProject); // 优化重载带来的等待交互
		if (forceEditInitPwd) {
			// 是否要强制修改密码
			localStorage.setItem('forceEditInitPwd', 'true');
		} else {
			localStorage.setItem('forceEditInitPwd', 'false');
		}
		// 动态挂在cdn
		// cdnLoad();
		// 通过reload清楚上一个项目的cdn,根据配置加载此次的cdn
		// localStorage.setItem('isReLoad', 'true');
		// if (forceEditInitPwd && showGuidePage !== 'true') {
		// 	router.replace('/');
		// 	return;
		// }
		// 配置的首页地址
		let home =
			!isPrecedenceRedirect && store.getters.systemInfo.main.indexUrl
				? store.getters.systemInfo.main.indexUrl
				: redirectUrl;
		let encodeHome = encodeURIComponent(home);
		// 如果有引导页就跳转引导页
		if (store.getters.systemInfo?.main?.showGuidePage === 'true') {
			let url = store.getters.systemInfo?.main?.guidePageUrl;
			url = url ? url + `?redirect=${encodeHome}` : home;
			router.replace(url);
		} else {
			await store.dispatch('settings/GET_MENU'); // 没有引导页的时候正常获取菜单
			if (home.indexOf('#') > -1) {
				window.location.href = window.location.origin + home;
			} else {
				router.replace(home);
			}
		}
		// Message({
		// 	showClose: true,
		// 	message: '登录成功！',
		// 	type: 'success'
		// });
	} catch (err) {
		console.log('报错信息--------------------------', err);
		// 有一个接口失败，就意味着登录失败，把登录信息清除
		store.commit('user/REMOVE_INFO'); // 用户信息
		throw new Error(err);
	}
}
// 获取token
async function getRToken(tenantId) {
	let res = await relyToken(tenantId);
	if (res.code === 200) {
		setCoosRentApplicationRelyToken(res.result);
	} else {
		Message({
			message: res.message,
			type: 'error'
		});
	}
}
