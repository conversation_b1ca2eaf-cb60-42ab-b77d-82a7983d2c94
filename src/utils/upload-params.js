/**
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-03-25 16:05:40
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-03-25 16:06:24
 * @FilePath: /coos-desktop-app/src/utils/upload-params.js
 * @Description:
 */
import { getToken, get_token } from './auth';
import { getItem, setItem } from './localstorage';
import { getFingerprint } from './fingerprint';

// 给请求头设置设备唯一标识
const fingerprint = () => {
	let cachedFingerprint = getItem('coos-fingerprint');
	if (!cachedFingerprint) {
		cachedFingerprint = getFingerprint(); // 同步获取指纹
		if (cachedFingerprint) {
			setItem('coos-fingerprint', cachedFingerprint);
		}
	}
	return cachedFingerprint;
};

export const uploadParams = {
	'X-Coos-Client-Access-Token': getToken(),
	'X-Coos-Client-Tenant-Id': get_token('X-Coos-Client-Tenant-Id'),
	'X-Coos-Client-Tenant-Type': '1',
	'X-Coos-Client-Tenant-OS': 'windows',
	'X-Coos-Client-Tenant-Code': fingerprint() // 添加指纹
};
