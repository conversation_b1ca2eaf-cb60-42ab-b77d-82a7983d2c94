import { webviewSocketUrl } from '@/config';
import { get_token, getToken } from '@/utils/auth';
import { getFormData } from '@/api/modules/coos';
import { isEmpty } from '@/utils';
import { debounce } from '@/utils';
import { updateCount } from '@/utils/update-help';
import { CoosEventTypes } from '@/utils/bus';
import projectData from '@/store/data-source/project-data';

let newWebviewSocketUrl = /(ws|wss)/.test(webviewSocketUrl)
	? webviewSocketUrl
	: `${/https/.test(window.location.origin) ? 'wss' : 'ws'}:${
			window.location.host
	  }${webviewSocketUrl}`;
/**
 * @method app中嵌套webview，主子应用之间的通信工具类
 * @param ${String} socket连接地址
 *
 * @api close 关闭socket连接
 * */
class WebviewSocket {
	code = ''; // socket连接唯一标识
	status = 0; // 连接状态 0连接中 1成功 -1主动断开 2报错 3关闭
	connectCount = 0; // 重连次数
	socketVM = null; //socket实例
	T = null; // 重连报错倒计时
	// 额外的参数配置
	config = {
		dataInstanceId: '', //业务表单的id
		longConnect: false, //是否长连接，默认否
		onMessage: null // 自定义的socket通知处理
	};
	// 延迟五秒更新数量和列表
	updateWait = debounce(
		function () {
			updateCount();
			console.log('收到更新通知-----');
			window.vm._BUS.$emit(CoosEventTypes.updateWaitCount);
			window.vm._BUS.$emit(CoosEventTypes.updateWaitList); // 单独通知更新列表，和APP不一样的是，PC切换租户时直接更新router-view的key
		},
		5 * 1000,
		false
	);
	constructor(code, config) {
		this.code = code;
		this.config = config;
		if (
			projectData[process.env.VUE_APP_PROJECT] &&
			projectData[process.env.VUE_APP_PROJECT].hideSocket
		) {
			console.log('该环境不建立socket通信');
			return;
		}
		console.log('创建socket的参数code/config', code, config);
		this.socketVM = new WebSocket(newWebviewSocketUrl + code);
		this.socketOnEvent();
	}
	/**检测是否断开连接*/
	checkConnect() {
		return new Promise((resolve, reject) => {
			console.log(
				'观察socket原生状态（勿删）=========',
				this.socketVM.readyState,
				'------和自定义状态-----',
				this.status
			);
			if ([0, 1].includes(this.status)) {
				resolve(true);
			} else {
				this.status = 3; // 如果需要重连，重置为断开状态
				this.connectCount = 0;
				this.reConnect(resolve, reject);
			}
		});
	}
	/**socket相关的监听事件*/
	socketOnEvent(resolve) {
		this.socketVM.onclose = () => {
			if (this.status !== -1) {
				this.status = 3;
				console.log('socket连接关闭');
				this.reConnect();
			}
		};
		this.socketVM.onerror = () => {
			console.log('socket连接报错');
			this.status = 2;
			this.reConnect();
		};
		this.socketVM.onmessage = e => {
			if (e.data === 'openSuccess') {
				console.log('socket后端连接打开');
				this.status = 1;
				resolve && resolve(true);
			} else {
				this.handleRes(e.data);
			}
		};
		this.socketVM.onopen = () => {
			// console.log('socket连接打开');
		};
	}
	/**重连*/
	reConnect(resolve, reject) {
		// 如果重连超过五次就不用重连了
		if (this.status !== -1 && this.connectCount < 5) {
			// 先关闭再重连
			this.close();
			this.socketVM = null;
			console.log('准备重连的实例id=', this.code);
			// this.status = 0;
			this.socketVM = new WebSocket(newWebviewSocketUrl + this.code);
			this.connectCount += 1;
			// 如果5秒没有断开就说明非报错导致
			if (this.T) {
				clearTimeout(this.T);
				this.T = null;
			}
			this.T = setTimeout(() => {
				this.connectCount = 0;
				clearTimeout(this.T);
				this.T = null;
			}, 5 * 1000);
			this.socketOnEvent(resolve);
			// resolve && resolve(true);
			// console.log('WebView的socket重连成功');
		} else {
			reject && reject(this.status === -1 ? '主动关闭，不再重连' : '重连次数过多');
		}
	}

	/**关闭socket连接*/
	close() {
		if (
			projectData[process.env.VUE_APP_PROJECT] &&
			projectData[process.env.VUE_APP_PROJECT].hideSocket
		) {
			return;
		}
		this.socketVM.close();
		this.status = -1;
		console.log('主动关闭socket');
	}

	/**处理收到的消息*/
	handleRes(data) {
		let { dataKey, dataFlag, dataVal } = JSON.parse(data);
		// if (dataKey === 'KeepConnect') return;
		console.log('socket接收到消息----------------', dataKey);
		switch (dataKey) {
			/**获取token && 获取租户ID*/
			case 'getToken': {
				let accessToken = getToken();
				let tenantId = get_token('X-Coos-Client-Tenant-Id');
				let res = {
					accessToken,
					tenantId
				};
				// 如果是高级版，需要将用户的身份id传给集成方
				if (
					!isEmpty(window.vm.$store.getters.userInfo) &&
					window.vm.$store.getters.userVersion === 'v2'
				) {
					res.identityId = window.vm.$store.getters.userInfo.currentIdentityId || ''; // 身份id
				}
				let data = {
					dataKey, // 数据项key
					dataFlag: dataFlag || '', // 数据标识(有才传递)
					dataVal: JSON.stringify(res)
				};
				this.socketVM.send(JSON.stringify(data));
				break;
			}

			/**获取定位*/
			// case 'getLocation': {
			// 	uni.getLocation({
			// 		type: 'wgs84 ', //'gcj02',
			// 		geocode: true, //该参数是为了获取经纬度城市信息
			// 		success: res => {
			// 			let data = {
			// 				dataKey,
			// 				dataFlag: dataFlag || '', // 数据标识(有才传递)
			// 				dataVal: JSON.stringify(res)
			// 			};
			// 			this.socketVM.send(JSON.stringify(data)),
			// 		fail: function () {
			// 			console.log('定位失败');
			// 		}
			// 	});
			// 	break;
			// }

			/**获取表单数据*/
			case 'getFormData': {
				if (this.config.dataInstanceId) {
					getFormData(this.config.dataInstanceId).then(res => {
						let obj = { ...res, webKey: this.code };
						let data = {
							dataKey, // 数据项key
							dataFlag: dataFlag || '', // 数据标识(有才传递)
							dataVal: JSON.stringify(obj)
						};
						this.socketVM.send(JSON.stringify(data));
					});
				} else {
					this.$message.error('当前页面不支持该通信！');
				}
				break;
			}
			/**通知没有im的租户用户更新消息数量（系统消息）*/
			case 'upDateImCount': {
				window.vm._BUS.$emit(CoosEventTypes.updateSystemCount, dataVal);
				break;
			}
			/**通知待办更新消息数量（系统消息）*/
			case 'sysTaskAdd': {
				this.updateWait();
				break;
			}

			case 'sysTaskFinish': {
				// dataVal：{id:待办编号、title：待办标题}
				this.updateWait();
				break;
			}
			case 'riDocsDealCallback': {
				window.vm._BUS.$emit(CoosEventTypes.onSocketMessage, JSON.parse(data));
				break;
			}
			default:
				if (this.config.onMessage) {
					this.config.onMessage(JSON.parse(data));
				} else {
					window.vm._BUS.$emit(CoosEventTypes.onSocketMessage, JSON.parse(data));
				}
		}
	}
}

export default WebviewSocket;
