import { Message } from 'element-eoss';

export const CoosEventTypes = {
	closePopup: 'closePopup', // 子项目关闭弹窗
	updatePage: 'updatePage', // 更新当前包含框架在内的页面
	changeIm: 'changeIm', // 改变智能对话的模型
	changeMask: 'changeMask', // 改变iframe上的遮罩层状态
	updateCurrentPage: 'updateCurrentPage', // 更新当前router-view页面
	reloadProject: 'reloadProject', // 模仿window.reload，重载框架级生命周期
	clickEndIcon: 'clickEndIcon', // 第三种框架，slide-bar菜单后缀图标自定义事件
	windowResize: 'windowResize', // 页面尺寸发生变化
	closeSocket: 'closeSocket', // 关闭全局的socket
	updateWaitCount: 'updateWaitCount', // 更新待办数量
	updateWaitList: 'updateWaitList', // 更新待办列表
	updateSystemCount: 'updateSystemCount', // 更新系统消息数量
	addLoadFile: 'addLoadFile', // 添加下载文件，全局下载列表
	aiSessionParams: 'aiSessionParams', // 智能会话的参数
	changeTempAiEnter: 'changeTempAiEnter', // 改变是否显示定制的会话入口
	getChildFormData: 'getChildFormData', // 获取子表单的数据
	responseChildFormData: 'responseChildFormData', // 返回子表单数据
	onSocketMessage: 'onSocketMessage' // 全局socket消息通知
};

/**
 * author fanghonghao
 * desc 全局组件事件通知bus类
 * */
export default class Bus {
	constructor() {
		this.callbacks = {}; // 事件订阅集合
	}
	$on(name, fn) {
		this.callbacks[name] = this.callbacks[name] || [];
		let index = this.callbacks[name].indexOf(fn);
		//如果事件相同，只替换不继续订阅
		if (index > -1) {
			this.callbacks[name].splice(index, 1, fn);
		} else {
			this.callbacks[name].push(fn);
		}
	}
	$emit(name, args) {
		if (!CoosEventTypes[name]) {
			Message.error('为了代码规范，请到utils/bus文件注册您的派发事件！');
			return;
		}
		if (this.callbacks[name]) {
			this.callbacks[name].forEach(cb => cb(args)); //通知订阅事件的函数
		} else {
			setTimeout(() => {
				this.callbacks[name]?.forEach(cb => cb(args)); //通知订阅事件的函数
			}, 1000);
		}
	}
	$off(name, fn) {
		let eventArr = this.callbacks[name] || [];
		let index = eventArr.indexOf(fn);
		if (index > -1) {
			this.callbacks[name].splice(index, 1);
		}
	}
}
