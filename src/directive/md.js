/*
 * @Description: md指令
 * @Version: 1.0
 * @Autor: zhaodongming
 * @Date: 2022-09-08 14:40:53
 * @LastEditors: zhaodongming
 * @LastEditTime: 2022-09-13 16:09:40
 */

// 埋点参数转base64
function serilizeParams(params) {
	return window.btoa(encodeURIComponent(JSON.stringify(params)));
}
function sendBeacon(url, params) {
	const formData = new FormData();
	formData.append('params', serilizeParams(params));
	navigator.sendBeacon(url, formData);
}
function sendImage(url, params) {
	// 创建一个隐藏的img标记
	const img = new Image();
	img.style.display = 'none';
	// 清除上一次提交的img
	const removeImage = function () {
		img.parentNode.removeChild(img);
	};
	//加载完成后或图片报错
	img.onload = removeImage;
	img.onerror = removeImage;
	// 将数据拼接到url
	img.src = `${url}?params=${serilizeParams(params)}`;
	// 插入dom，发送数据
	document.body.appendChild(img);
}
// 兼容sendBeacon的方案
function sendLog(url, params) {
	if (navigator.sendBeacon) {
		sendBeacon(url, params);
	} else {
		sendImage(url, params);
	}
}
// 自定义埋点指令
const install = function (Vue, options = {}) {
	Vue.directive('md', {
		inserted(el, binding) {
			el.addEventListener('click', function () {
				// 获取所有自定义属性data-
				const getDataset = this.dataset;
				const setData = {};
				for (const key in getDataset) {
					// 埋点信息过滤掉v-指令
					if (getDataset[key] !== undefined && key.indexOf('v-') === -1) {
						setData[key] = getDataset[key];
					}
				}
				setData.event_name = binding.arg;
				// console.log('埋点数据', setData);
				sendLog('https://cdn.shupian.cn/sp/cms/9ki8yw0aplw0000.png', setData);
			});
		}
	});
};

/* 判断直接在script标签引入使用 */
if (typeof window !== 'undefined' && window.Vue) {
	install(window.Vue);
}
export default { install };
