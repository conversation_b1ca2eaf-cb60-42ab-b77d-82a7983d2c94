<template>
	<div class="md">
		<div class="md-menu">
			<div
				v-for="(anchor, index) of titles"
				:key="index"
				class="md-menu-item"
				:style="{
					paddingLeft: 5 + anchor.indent * 20 + 'px',
					fontSize: 14 - anchor.indent + 'px',
					fontWeight: current === index ? '600' : '400'
				}"
				@click="handleAnchorClick(anchor, index)"
			>
				{{ anchor.title }}
			</div>
		</div>
		<div class="md-con">
			<v-md-preview ref="preview" :text="text"></v-md-preview>
		</div>
	</div>
</template>

<script>
import axios from 'axios';
export default {
	name: 'MarkIndex',
	data() {
		return {
			text: '',
			titles: [],
			current: 0
		};
	},
	mounted() {
		axios
			.get('/howToUse.md', {})
			.then(res => {
				this.text = res.data;
				this.$nextTick(() => {
					this.init();
				});
			})
			.catch(err => {
				return Promise.reject(err);
			});
	},
	methods: {
		init() {
			const anchors = this.$refs.preview.$el.querySelectorAll('h1,h2,h3,h4,h5,h6');
			const titles = Array.from(anchors).filter(title => !!title.innerText.trim());
			if (!titles.length) {
				this.titles = [];
				return;
			}

			const hTags = Array.from(new Set(titles.map(title => title.tagName))).sort();

			this.titles = titles.map(el => ({
				title: el.innerText,
				lineIndex: el.getAttribute('data-v-md-line'),
				indent: hTags.indexOf(el.tagName)
			}));
			titles.forEach(el => {
				if (el.innerText === '使用示例') {
					el.style.position = 'relative';
					let copy = document.createElement('span');
					copy.setAttribute(
						'style',
						'font-weight: 400;transform:scale(0.8);color:#c3c3c3;position:absolute;right: 0;cursor: pointer'
					);
					copy.innerHTML = '复制';
					copy.addEventListener('click', () => {
						let copyContent = el.nextElementSibling.getElementsByTagName('code');
						copyContent = copyContent.length ? copyContent[0].innerText : '';
						navigator.clipboard.writeText(copyContent).then(() => {
							this.$message.success('复制成功！');
						});
					});
					el.appendChild(copy);
				}
			});
		},
		handleAnchorClick(anchor, index) {
			this.current = index;
			const { preview } = this.$refs;
			const { lineIndex } = anchor;
			const heading = preview.$el.querySelector(`[data-v-md-line="${lineIndex}"]`);

			if (heading) {
				heading.scrollIntoView({ behavior: 'smooth' });
			}
		}
	}
};
</script>
<style lang="scss" scoped>
.md {
	background: #ffffff;
	border-radius: 6px;
	margin: 0 32px;
	padding: 20px 0;
	height: 100%;
	@include flexBox(flex-start);
	&-menu {
		height: 100%;
		width: 200px;
		padding: 0 12px;
		overflow: auto;
		@include noScrollBar;
		&-item {
			cursor: pointer;
			width: 100%;
			font-size: 12px;
			@include aLineEllipse;
			padding: 10px 5px;
			&:hover {
				background: #f0f0f0;
			}
		}
	}
	&-con {
		height: 100%;
		overflow-y: auto;
		@include scrollBar;
	}
}
</style>
