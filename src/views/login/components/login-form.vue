<template>
	<div class="login-form" :class="{ 'small-height': isUsername && !isNeedCode && !switchState }">
		<!-- 扫码和输入相互切换 -->
		<div v-if="newQrCodeLogin && (qrcode || isUsername)" class="switch-login" @click="swichBtn">
			<div class="box">
				<img
					v-if="switchState"
					:src="require(`@/assets/${rentThem}/login/switchQR2.png`)"
					alt=""
					@mouseenter="hoverImg = true"
					@mouseleave="hoverImg = false"
				/>
				<img
					v-else
					:src="require(`@/assets/${rentThem}/login/switchQR.png`)"
					alt=""
					@mouseenter="hoverImg = true"
					@mouseleave="hoverImg = false"
				/>
				<div v-show="hoverImg" class="left">
					<div class="text">{{ switchText }}</div>
					<div class="icon"></div>
				</div>
			</div>
		</div>
		<div class="title">欢迎登录</div>
		<div v-if="switchState" class="QRinput">
			<div class="QRinput-head">
				<div class="left"></div>
				<div class="center">扫描二维码登录</div>
				<div class="right"></div>
			</div>
			<div class="QRinput-title">请使用手机版COOS扫码登录</div>
			<div class="QRBox">
				<div v-loading="getCodeLoading" class="QR">
					<img ref="loginQrCode" alt="" :src="qrCodeBase64" />
					<!-- <vue-qr text="http://baidu.com" color-dark="black" :callback="qrCodeCallback" width="230"
						height="230"></vue-qr> -->
					<div v-if="unActive" class="un-active" @click="getLoginQRCode">
						<img class="un-active-img" src="../../../assets/images/common/update.png" alt="" />
						<span class="un-active-button">点击刷新</span>
					</div>
				</div>
			</div>
			<div class="bottom" @click="appDownload">
				<span class="left">首次使用</span>
				<span class="right">下载COOS APP</span>
			</div>
		</div>
		<div v-else>
			<el-tabs v-model="activeName" @tab-click="clickTab">
				<el-tab-pane v-if="isUsername" label="账号登录" name="account">
					<div class="form">
						<div class="form-item">
							<i class="icon coos-iconfont icon-me"></i>
							<el-input
								v-model="form.username"
								class="input"
								placeholder="请输入登录账号"
								type="text"
								@keydown.enter.native="login"
							/>
						</div>
						<div class="form-item">
							<i class="icon coos-iconfont icon-lock"></i>
							<el-input
								v-model="form.password"
								class="input"
								placeholder="请输入登录密码"
								type="text"
								:show-password="true"
								@keydown.enter.native="login"
							/>
						</div>
						<div v-if="isNeedCode" class="form-item">
							<i class="icon coos-iconfont icon-yinsixieyi"></i>
							<el-input
								v-model="form.randomCode"
								class="input"
								placeholder="请输入验证码"
								type="text"
								@keydown.enter.native="login"
							/>
							<img :src="codeUrl" class="login-code-img" @click="getRandomCode" />
						</div>
					</div>
					<div class="sure-box">
						<div class="sure-psd" @click="remember">
							<div class="sure-psd-icon" :class="rememberPassword ? 'select' : 'no-select'">
								<img src="../../../assets/images/common/sure.png" class="sure" alt="" />
							</div>
							<div class="sure-psd-text">记住密码</div>
						</div>
						<div class="forgot" @click="forgot">忘记密码</div>
					</div>
				</el-tab-pane>
				<el-tab-pane v-if="qrcode" label="手机登录" name="phone">
					<div class="form">
						<div class="form-item">
							<i class="icon coos-iconfont icon-shouji1"></i>
							<el-input
								v-model="form.phone"
								class="input"
								placeholder="请输入手机号"
								type="text"
								maxlength="11"
							/>
						</div>
						<div class="form-item">
							<i class="icon coos-iconfont icon-key"></i>
							<el-input
								v-model="form.code"
								maxlength="6"
								class="input"
								placeholder="请输入验证码"
								type="text"
							/>
							<div v-loading="codeLoading" class="get-code" @click="sendCode">
								{{ sendCodeText }}
							</div>
						</div>
					</div>
				</el-tab-pane>
			</el-tabs>
			<div v-loading="loading" class="submit" @click="login">登录</div>
			<div class="download-btn">
				<!-- 二维码下载App -->
				<div
					v-if="
						clientBasicObj.showAppDownload === 'true' || clientBasicObj.showAppDownload === true
					"
					class="QRcode"
					@click="qrCodeUpload"
				>
					<svg
						t="1716271066182"
						class="icon"
						viewBox="0 0 1024 1024"
						version="1.1"
						xmlns="http://www.w3.org/2000/svg"
						p-id="5461"
						width="15"
						height="15"
					>
						<path
							d="M709.98223 0H313.653832A181.968961 181.968961 0 0 0 131.684871 181.968961v659.69814a182.575524 182.575524 0 0 0 181.968961 181.968961h396.328398a182.575524 182.575524 0 0 0 181.968961-181.968961V181.968961a181.968961 181.968961 0 0 0-181.968961-181.968961z m100.204241 753.472811H213.813529V181.968961A100.446866 100.446866 0 0 1 313.653832 82.128658h396.328398A100.325554 100.325554 0 0 1 810.186471 181.968961zM213.813529 835.601469h596.372942v6.065632a100.204241 100.204241 0 0 1-100.204241 100.082929H313.653832a100.325554 100.325554 0 0 1-100.204241-100.082929z"
							fill="currentColor"
							p-id="5462"
						></path>
						<path
							d="M469.540576 909.116929h84.918848a24.262528 24.262528 0 1 0 0-49.374244h-84.918848a24.262528 24.262528 0 0 0 0 49.374244z"
							fill="currentColor"
							p-id="5463"
						></path>
					</svg>
					App下载
				</div>
				<el-divider
					v-if="
						(clientBasicObj.showAppDownload === 'true' ||
							clientBasicObj.showAppDownload === true) &&
						pluginDownloadConfig.length !== 0 &&
						(clientBasicObj.showPluginDownload === 'true' ||
							clientBasicObj.showPluginDownload === true)
					"
					direction="vertical"
				></el-divider>
				<div
					v-if="
						pluginDownloadConfig.length !== 0 &&
						(clientBasicObj.showPluginDownload === 'true' ||
							clientBasicObj.showPluginDownload === true)
					"
					class="QRcode"
					@click="downloadPlugin"
				>
					<i class="coos-iconfont icon-chajiangongneng icon"></i>
					插件下载
				</div>
			</div>
		</div>

		<div class="LoginFormTaking">
			<el-dialog :visible.sync="dialogVisible" width="530px">
				<div slot="title" class="QRprops-header">请使用手机扫描二维码下载APP使用</div>
				<div class="QRprops">
					<div class="QRprops-content">
						<div class="qr">
							<span>Android</span>
							<img
								v-if="clientBasicObj.appAndroidQRCode"
								class="qrImg"
								:src="clientBasicObj.appAndroidQRCode"
							/>
							<div v-else class="qrImg" style="border: 1px solid #dadada; opacity: 0.5">
								<img src="../../../assets/images/common/failUpload.png" width="65" height="60" />
								未上传
							</div>
						</div>
						<div style="border-left: 1px solid $borderColor; height: 100%"></div>
						<div class="qr">
							<span>IOS</span>
							<img
								v-if="clientBasicObj.appIOSQRCode"
								class="qrImg"
								:src="clientBasicObj.appIOSQRCode"
							/>
							<div v-else class="qrImg" style="border: 1px solid #dadada; opacity: 0.5">
								<img src="../../../assets/images/common/failUpload.png" width="65" height="60" />
								未上传
							</div>
						</div>
					</div>
					<span class="QRprops-footer">
						备注：苹果手机安装后请在设置-通用-设备管理中进行激活操作。
					</span>
					<span class="QRprops-footer QRprops-footer-bottom">
						为避免潜在的安全风险，请勿将二维码截图分享给他人。
					</span>
				</div>
				<!-- <span slot="footer" class="dialog-footer">
				<el-button @click="dialogVisible = false">取 消</el-button>
				<el-button type="primary" @click="dialogVisible = false">确 定</el-button>
			</span> -->
			</el-dialog>
		</div>
		<downLoad-plugin
			v-if="pluginVisible"
			:plugin-download-config="pluginDownloadConfig"
			:dialog-visible.sync="pluginVisible"
		></downLoad-plugin>
	</div>
</template>

<script>
import { debounce } from 'lodash';
import JSEncrypt from 'jsencrypt';
import {
	getEncryptKey,
	login,
	loginCode,
	codeToLogin,
	qrCodeLogin,
	isQrCodeLogin,
	getUserCode,
	isNeedUserActionVerifyCode
} from '@/api/modules/login';
import { mapGetters } from 'vuex';
import { get_token } from '@/utils/auth';
import { handleLoginRes } from '@/utils/handle-login';
import { logoutApps } from '@/utils/app-logout';
import DownLoadPlugin from './download-plugin.vue';
import { encrypt, decrypt } from '@/utils/crypto';

const LOCAL_STORAGE_ACCOUNT_NAME = 'coos_user_account_name';

export default {
	name: 'LoginForm',
	components: { DownLoadPlugin },
	data() {
		return {
			hoverImg: false,
			unActive: true,
			getCodeLoading: false,
			encryptKey: '', // 密码加密秘钥
			codeLoading: false, // 发送验证码的加载效果
			sendCodeText: '获取验证码', // 发送验证码文字
			codeTime: null, // 验证码计时器
			activeName: 'account',
			rememberPassword: true, // 记住密码的状态
			/**表单数据*/
			form: {
				password: '',
				username: '',
				phone: '',
				code: '',
				randomCode: ''
			},
			loading: false, //登录状态
			qrcode: false, // 是否支持验证码登录
			isUsername: true, // 是否可以账号密码登录
			//
			dialogVisible: false, //下载二维码弹出
			clientBasicObj: {}, // 租户二维码信息配置
			pluginDownloadConfig: [], //下载插件列表
			pluginVisible: false, //下载插件弹窗
			switchText: '二维码登录',
			switchState: false,
			newQrCodeLogin: false, // 是否支持扫码登录
			qrCodeBase64: '',
			scanTimer: null,
			isNeedCode: false, //是否需要验证码
			code: '',
			qrIndex: 1,
			checkKey: '', //验证码key
			codeUrl: '' //验证码图片
		};
	},
	computed: {
		...mapGetters(['preUser', 'systemInfo']),
		tenantId() {
			const queryTenantId = this.$route.query.tenantId;
			const ClientTenantId = get_token('X-Coos-Client-Tenant-Id');
			return queryTenantId ? queryTenantId : ClientTenantId ? ClientTenantId : undefined;
		}
	},
	watch: {
		systemInfo() {
			this.reloadLogin();
		}
	},
	mounted() {
		//租户配置显示二维码下载
		// getTenantConfig({ tenantId: this.tenantId, key: 'clientBasic' }).then(({ result, code }) => {
		// 	if (code === 200) {
		// 		console.log('配置信息：', result);
		// 		this.clientBasicObj = result ? result.login : {};
		// 		this.pluginDownloadConfig = JSON.parse(this.clientBasicObj?.pluginDownloadConfig || '[]');
		// 	}
		// });
		// 同步退出登录应用系统
		logoutApps(this.preUser);
		let tenantId = this.$route.query.tenantId || '';
		if (!tenantId) {
			this.reloadLogin();
		}
	},
	destroyed() {
		this.scanTimer && clearInterval(this.scanTimer);
		this.scanTimer = null;
	},
	methods: {
		// 重新加载登录
		reloadLogin() {
			this.clientBasicObj = this.systemInfo.login || {};
			this.pluginDownloadConfig = JSON.parse(this.clientBasicObj?.pluginDownloadConfig || '[]');
			//根据配置判断登录方式

			if (this.clientBasicObj.modes && this.clientBasicObj.modes.length > 0) {
				this.qrcode = this.clientBasicObj.modes.includes('phoneVerificationCode');
				this.newQrCodeLogin = this.clientBasicObj.modes.includes('qrcode');
				this.isUsername = this.clientBasicObj.modes.includes('username');
				this.activeName = this.isUsername ? 'account' : 'phone';
				// 只有二维码登录的时候

				if (this.clientBasicObj.modes.length === 1) {
					if (this.newQrCodeLogin) {
						this.switchState = true;
						if (!this.qrCodeBase64 && !this.getCodeLoading) {
							this.getLoginQRCode();
						}
					} else {
						this.qrCodeBase64 = '';
						this.switchState = false;
					}
				}
			} else {
				this.isUsername = true;
				this.activeName = 'account';
			}
			// 账号密码登录时获取
			if (this.isUsername) {
				// 是否需要验证码
				this.getIsNeedCode();
				const localStorageAccount = localStorage.getItem(LOCAL_STORAGE_ACCOUNT_NAME);
				// 如果有记住账号
				if (localStorageAccount) {
					const localStorageAccountObjStr = decrypt(
						LOCAL_STORAGE_ACCOUNT_NAME,
						localStorageAccount
					);
					const localStorageAccountObj = JSON.parse(localStorageAccountObjStr);
					this.form = {
						...localStorageAccountObj
					};
					this.rememberPassword = localStorageAccountObj.checked;
				}
			}
		},
		// 是否需要验证码
		getIsNeedCode() {
			isNeedUserActionVerifyCode().then(res => {
				this.isNeedCode = res.result;
				if (this.isNeedCode) {
					this.getRandomCode();
				}
			});
		},
		/**点击标签*/
		clickTab() {
			console.log(this.activeName);
		},
		/**
		 * 二维码下载手机端
		 */
		qrCodeUpload() {
			this.dialogVisible = true;
		},
		/**下载插件*/
		downloadPlugin() {
			this.pluginVisible = true;
		},
		/**记住密码*/
		remember() {
			this.rememberPassword = !this.rememberPassword;
		},
		/** 忘记密码 */
		forgot() {
			this.$emit('forgot', this.form.username);
		},
		/**发送验证码*/
		sendCode: debounce(function () {
			if (this.codeLoading) return;
			if (this.codeTime) {
				// this.$message.error('验证码发送中，请勿重复操作！');
				return;
			}
			this.codeLoading = true;
			let data = {
				channel: 'phone',
				businessType: 1,
				receiver: this.form.phone
			};
			// 模拟请求
			// setTimeout(() => {
			if (!this.form.phone) {
				this.codeLoading = false;
				return this.$message.error('手机号不能为空');
			}
			loginCode(data, this.tenantId).then(res => {
				if (res.code === 200) {
					let code = 59;
					this.codeLoading = false;
					this.sendCodeText = 59 + '秒后重新发送';
					this.codeTime = setInterval(() => {
						if (code === 1) {
							this.sendCodeText = '重新发送';
							clearTimeout(this.codeTime);
							this.codeTime = null;
						} else {
							code = code - 1;
							this.sendCodeText = code + '秒后重新发送';
						}
					}, 1000);
				} else {
					this.$message.error(res.message);
					this.codeLoading = false;
				}
			});

			// }, 500);
		}, 100),
		/**登录*/
		login: debounce(function () {
			if (this.loading) return;
			if (this.activeName === 'account') {
				if (!this.form.username) {
					return this.$message.error('用户名不能为空！');
				}
				if (!this.form.password) {
					return this.$message.error('密码不能为空！');
				}
				if (!this.form.randomCode && this.isNeedCode) {
					return this.$message.error('验证码不能为空！');
				}
				this.loading = true;
				// 记住账号
				if (this.rememberPassword) {
					const { username, password } = this.form;
					localStorage.setItem(
						LOCAL_STORAGE_ACCOUNT_NAME,
						encrypt(
							LOCAL_STORAGE_ACCOUNT_NAME,
							JSON.stringify({
								username,
								password,
								checked: this.rememberPassword
							})
						)
					);
				} else {
					localStorage.setItem(
						LOCAL_STORAGE_ACCOUNT_NAME,
						encrypt(
							LOCAL_STORAGE_ACCOUNT_NAME,
							JSON.stringify({
								checked: this.rememberPassword
							})
						)
					);
				}
				/**获取秘钥对密码加密*/
				getEncryptKey()
					.then(res => {
						if (res.code === 200) {
							this.encryptKey = res.result;
							/**加密数据后登录*/
							let encryptor = new JSEncrypt(); // JSEncrypt对象
							encryptor.setPublicKey(this.encryptKey); // 公钥
							let rsaPassWord = encryptor.encrypt(this.form.password); // 密码进行加密
							let data = {
								password: rsaPassWord,
								username: this.form.username,
								checkKey: this.checkKey,
								captcha: this.form.randomCode,
								// 先获取路由上的tenantId，不存在就获取cookie中的tenantId
								tenantId: this.tenantId
							};
							login(data)
								.then(async res => {
									if (res.code === 200) {
										await handleLoginRes(res);
									} else if (res.code === 40101) {
										this.isNeedCode = true;
									} else {
										this.$message({
											showClose: true,
											type: 'error',
											message: res.message || '登录失败！'
										});
										this.getRandomCode();
									}
									this.loading = false;
								})
								.catch(err => {
									this.loading = false;
								});
						} else if (res.code === 40101) {
							this.isNeedCode = true;
						} else {
							this.loading = false;
							this.$message.error(res.message);
						}
					})
					.catch(err => {
						this.loading = false;
					});
			} else if (this.activeName === 'phone') {
				let pattern = /^1\d{10}$/;

				if (!this.form.phone) {
					return this.$message.error('手机号不能为空！');
				} else {
					if (!pattern.test(this.form.phone)) return this.$message.error('请填写正确的手机号');
				}
				if (!this.form.code) {
					return this.$message.error('验证码不能为空！');
				}
				let data = {
					username: this.form.phone,
					captcha: this.form.code,
					channel: 'phone',
					// 先获取路由上的tenantId，不存在就获取cookie中的tenantId
					tenantId: this.tenantId
				};
				this.loading = true;
				codeToLogin(data).then(async res => {
					if (res.code === 200) {
						await handleLoginRes(res);
					} else {
						this.$message({
							showClose: true,
							type: 'error',
							message: res.message || '登录失败！'
						});
					}
					this.loading = false;
				});
			}
		}, 500),
		//切换登录方式
		swichBtn() {
			this.switchState = !this.switchState;
			if (this.switchState) {
				this.switchText = '账号密码登录';
				if (!this.qrCodeBase64) {
					this.getLoginQRCode();
				}
			} else {
				this.switchText = '二维码登录';
			}
		},
		//下载app
		appDownload() {
			this.dialogVisible = true;
		},
		//获取扫码登录的二维码
		getLoginQRCode() {
			this.scanTimer && clearInterval(this.scanTimer);
			this.scanTimer = null;
			this.qrCodeBase64 = '';
			this.unActive = false;
			this.getCodeLoading = true;
			qrCodeLogin()
				.then(res => {
					this.getCodeLoading = false;
					if (res.code !== 200) {
						throw new Error(res.message);
					}
					this.qrCodeBase64 = res.result.qrCode;
					this.code = res.result.code;
					this.scanTimer = setInterval(this.checkQrCodeScan, 1000); // 每隔1秒检查一次
				})
				.catch(err => {
					this.$message.error(err.message || '获取验证码失败');
				});
		},
		//检查二维码是否扫码
		checkQrCodeScan() {
			isQrCodeLogin(this.code)
				.then(res => {
					if (res.code !== 200) {
						throw new Error(res.message);
					}
					//确认状态(-1.已过期/无效，0.待扫描，1.确认登录中，2.登录完成)
					if (res.result.status === -1) {
						clearInterval(this.scanTimer);
						this.unActive = true;
					}

					if (res.result.status === 2) {
						clearInterval(this.scanTimer);
						let obj = {};
						obj.result = res.result.loginInfo;
						handleLoginRes(obj);
					}
				})
				.catch(err => {
					this.$message({
						showClose: true,
						type: 'error',
						message: err.message || '二维码扫码失败'
					});
				});
		},
		//获取用户行为验证码
		getRandomCode() {
			// this.codeUrl=""
			getUserCode()
				.then(res => {
					if (res.code !== 200) {
						throw new Error(res.message);
					} else {
						this.checkKey = res.result.checkKey;
						this.codeUrl = res.result.img;
					}
				})
				.catch(err => {
					this.$message({
						showClose: true,
						type: 'error',
						message: err.message || '获取验证码失败失败'
					});
				});
		}
	}
};
</script>

<style scoped lang="scss">
::v-deep .el-loading-spinner .circular {
	width: 20px;
}

.login-form {
	position: relative;
	width: 428px;
	height: 496px;
	background: #ffffff;
	box-shadow: 0px 1px 3px 0px rgba(40, 76, 185, 0), 0px 2px 6px 0px rgba(40, 76, 185, 0),
		0px 5px 9px 0px rgba(40, 76, 185, 0.05), 0px 17px 24px 0px rgba(40, 76, 185, 0.08);
	border-radius: 24px;
	border: 1px solid #f0f0f0;
	padding: 26px 21px;
	position: relative;

	.switch-login {
		cursor: pointer;
		position: absolute;
		top: 17px;
		right: 15px;

		.box {
			position: relative;

			.left {
				background: #fefefe;
				top: 8px;
				right: 38px;
				position: absolute;
				width: 100px;
				height: 32px;
				box-shadow: 0px 5px 5px -3px rgba(0, 0, 0, 0.1), 0px 8px 10px 1px rgba(0, 0, 0, 0.06),
					0px 3px 14px 2px rgba(0, 0, 0, 0.05);
				border-radius: 0px 0px 0px 0px;

				.text {
					background: var(--brand-6);
					border-radius: 6px 6px 6px 6px;
					width: 100px;
					height: 32px;
					font-family: PingFang SC, PingFang SC;
					font-weight: 400;
					font-size: 14px;
					color: #ffffff;
					line-height: 32px;
					text-align: center;
					font-style: normal;
					text-transform: none;
				}

				.icon {
					position: absolute;
					top: 12px;
					right: -12px;
					width: 0;
					height: 0;
					border-left: 8px solid transparent;
					border-right: 8px solid transparent;
					border-bottom: 10px solid var(--brand-6);
					transform: rotate(90deg);
					/* 86.6px 是三角形的高度，可以根据需要进行调整 */
				}
			}
		}
	}

	.QRinput {
		.QRinput-head {
			display: flex;
			justify-content: center;
			align-items: center;

			.left {
				width: 130px;
				height: 1px;
				border: 1px solid;
				border-image: linear-gradient(90deg, rgba(240, 240, 240, 0), rgba(230, 230, 230, 1)) 1 1;
			}

			.center {
				font-family: PingFang SC, PingFang SC;
				font-weight: 500;
				font-size: 14px;
				color: $textColor;
				line-height: 22px;
				text-align: center;
				font-style: normal;
				text-transform: none;
				padding: 0 10px;
			}

			.right {
				width: 130px;
				height: 1px;
				border: 1px solid;
				border-image: linear-gradient(90deg, rgba(240, 240, 240, 0), rgba(220, 227, 231, 1)) 1 1;
			}
		}

		.QRinput-title {
			font-family: PingFang SC, PingFang SC;
			font-weight: 400;
			font-size: 16px;
			color: $textColor;
			line-height: 24px;
			text-align: center;
			font-style: normal;
			text-transform: none;
			margin: 10px 0;
		}

		.QRBox {
			width: 100%;
			height: 260px;
			display: flex;
			justify-content: center;
			align-items: center;

			.QR {
				width: 260px;
				height: 260px;
				border-radius: 6px 6px 6px 6px;
				border: 1px solid $borderColor;
				display: flex;
				justify-content: center;
				align-items: center;
				position: relative;
				.un-active {
					z-index: 667;
					position: absolute;
					top: 0;
					left: 0;
					right: 0;
					bottom: 0;
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: center;
					background: rgba(0, 0, 0, 0.3);
					cursor: pointer;
					&-img {
						width: 100px;
						height: 100px;
					}
					&-button {
						margin-top: 8px;
						color: #ffffff;
					}
				}
				img {
					width: 100%;
				}
			}
		}

		.bottom {
			cursor: pointer;
			margin-top: 50px;
			display: flex;
			justify-content: center;
			align-items: center;
			font-family: PingFang SC, PingFang SC;
			font-weight: 400;
			font-size: 14px;

			line-height: 22px;
			font-style: normal;
			text-transform: none;

			.left {
				color: $textColor;
				margin-right: 10px;
			}

			.right {
				color: var(--brand-6);
			}
		}
	}

	position: relative;

	.title {
		font-size: 24px;
		font-weight: 800;
		color: #2f3b66;
		line-height: 44px;
		margin-bottom: 4px;
	}

	::v-deep .el-tabs__header {
		margin: 0 !important;

		& > .el-tabs__nav-wrap::after {
			display: none !important;
		}

		& .el-tabs__active-bar {
			height: 3px;
		}
	}

	.form {
		margin: 24px 0;

		&-item {
			width: 380px;
			height: 56px;
			background: #ffffff;
			box-shadow: 0px 8px 10px -5px rgba(148, 188, 225, 0.08),
				0px 16px 24px 2px rgba(148, 188, 225, 0.04), 0px 6px 30px 5px rgba(148, 188, 225, 0.05);
			border-radius: 9px;
			border: 1px solid #dce3e7;
			padding: 0 18px 0 17px;
			display: flex;
			align-items: center;
			margin-bottom: 12px;

			.icon {
				flex-shrink: 0;
				width: 20px;
				height: 20px;
				font-size: 20px;
				margin-right: 5px;
				color: #8990a8;
			}

			.input {
				flex: 1;
				height: 22px;

				::v-deep .el-input__clear {
					margin-top: -4px;
				}

				::v-deep .el-input__inner {
					width: 100%;
					height: 100%;
					border: none;
					outline: none;
					font-size: 14px;
					font-weight: 400;
					line-height: 22px;
					padding: 0 30px 0 0 !important;

					&:focus {
						box-shadow: none;
					}

					&:-webkit-autofill,
					&:-webkit-autofill:hover,
					&:-webkit-autofill:focus,
					&:-webkit-autofill:active {
						-webkit-transition-delay: 9999s !important;
						-webkit-transition: background-color 9999s ease-out !important;
						background-color: #ffffff !important;
					}

					&::placeholder {
						font-size: 13px;
						font-weight: 400;
						color: $holderTextColor;
						line-height: 20px;
					}
				}
			}

			.get-code {
				font-size: 13px;
				font-weight: 400;
				color: var(--brand-6);
				line-height: 20px;
				cursor: pointer;
			}
		}
	}

	.sure-box {
		display: flex;
		justify-content: space-between;

		.forgot {
			font-size: 14px;
			cursor: pointer;
			color: var(--brand-6);
		}
	}

	.sure-psd {
		margin-left: 3px;
		display: inline-flex;
		align-items: center;
		cursor: pointer;

		&-icon {
			width: 16px;
			height: 16px;
			border-radius: 10px;
			margin-right: 10px;
			display: flex;
			align-items: center;
			justify-content: center;
			background: #ffffff;

			.sure {
				width: 10px;
				height: 10px;
				border-radius: 50%;
			}
		}

		.select {
			background: var(--brand-6);
		}

		.no-select {
			border: 1px solid var(--brand-6);
		}

		&-text {
			font-size: 14px;
			font-weight: 400;
			color: $textColor;
			line-height: 14px;
		}
	}

	.submit {
		position: absolute;
		bottom: 56px;
		left: 24px;
		font-weight: 400;
		color: #ffffff;
		cursor: pointer;
		display: flex;
		align-items: center;
		justify-content: center;
		width: 380px;
		height: 56px;
		background: linear-gradient(176deg, var(--brand-4) 0%, var(--brand-6) 100%);
		box-shadow: 0px 6px 5px 0px var(--brand-1), 0px 19px 8px 0px rgba(186, 214, 255, 0.25);
		border-radius: 34px;
		font-size: 20px;
		line-height: 22px;

		::v-deep .el-loading-mask {
			border-radius: 34px;
		}
	}

	.download-btn {
		position: absolute;
		bottom: 28px;
		width: 380px;
		display: flex;
		justify-content: center;
		color: var(--brand-6);
		font: normal 400 14px 'PingFang SC, PingFang SC';
		align-items: center;

		.el-divider--vertical {
			margin: 0 15px;
		}

		.QRcode {
			cursor: pointer;
			display: flex;
			align-items: center;
			gap: 8px;
		}
	}

	.QRprops {
		width: 100%;
		display: flex;
		flex-direction: column;
		align-items: center;

		&-header {
			color: $primaryTextColor;
			font: normal 800 18px 'PingFang SC-Heavy';
		}

		&-content {
			width: 470px;
			height: 256px;
			display: flex;
			justify-content: space-between;

			.qr {
				display: flex;
				color: $textColor;
				font: normal 800 18px 'PingFang SC-Heavy';
				flex-direction: column;
				align-items: center;
				gap: 16px;

				.qrImg {
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: center;
					width: 200px;
					height: 200px;
				}
			}
		}

		&-footer {
			text-align: center;
			width: 470px;
			color: #ff4d4f;
			font: normal 400 14px 'PingFang SC-Regular';
			&.QRprops-footer-bottom {
				text-indent: 32px;
			}
		}
	}
}

// 没有验证码输入框的高度
.small-height {
	height: 436px;
}

.login-code-img {
	height: 28px;
	cursor: pointer;
}
</style>
<style lang="scss">
.LoginFormTaking .el-dialog {
	border-radius: 16px !important;

	::v-deep .el-loading-mask {
		border-radius: 34px;
	}
}
</style>
