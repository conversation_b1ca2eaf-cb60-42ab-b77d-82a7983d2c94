<template>
	<el-dialog
		title="忘记密码"
		:visible.sync="dialogVisible"
		width="600px"
		:before-close="handleClose"
	>
		<div>
			<el-form
				ref="regForm"
				:model="regForm"
				:rules="rules"
				label-position="right"
				label-width="75px"
				:hide-required-asterisk="true"
			>
				<el-form-item prop="username" label="账号">
					<el-input v-model="regForm.username" placeholder="请输入账号"></el-input>
				</el-form-item>
				<!--				<div v-if="message" class="tips">{{ message }}</div>-->
				<el-form-item prop="captcha" label="验证码" :class="showSendChannel ? 'validItem' : ''">
					<div class="index-box">
						<el-input
							v-model="regForm.captcha"
							style="width: 330px"
							placeholder="请输入您收到的验证码"
						></el-input>
						<el-button class="get-code" :loading="btnLoading" @click="getCode('regForm')">
							{{ second === totalSecond ? '获取验证码' : second + `秒后重新发送` }}
						</el-button>
					</div>
				</el-form-item>
				<el-form-item v-if="showSendChannel" class="validItem">
					<p style="color: #848484b5; font-size: 13px; line-height: 1.5">
						已向绑定的{{ channelName }}
						<span style="color: #4ca9ff">{{ channelAccount }}</span>
						，发送验证码，请注意查收!
					</p>
				</el-form-item>
				<el-form-item prop="password" label="新密码">
					<el-input
						v-model="regForm.password"
						autocomplete="new-password"
						class="input"
						placeholder="8-20位，需包含数字及字母"
						type="text"
						:show-password="true"
					/>
				</el-form-item>
				<el-form-item prop="repassword" label="确认密码">
					<el-input
						v-model="regForm.repassword"
						class="input"
						placeholder="8-20位，需包含数字及字母"
						type="text"
						:show-password="true"
					/>
				</el-form-item>
			</el-form>
		</div>
		<span slot="footer" class="dialog-footer">
			<el-button @click="handleClose">取消</el-button>
			<el-button type="primary" @click="forgetPasw('regForm')">重置密码</el-button>
		</span>
	</el-dialog>
</template>

<script>
import { verifyCodeSend, forgetPwd, getEncryptKey } from '@/api/modules/login';
import JSEncrypt from 'jsencrypt';
export default {
	props: {
		visible: {
			type: Boolean,
			default: false
		},
		userName: {
			type: String,
			default: ''
		}
	},
	data() {
		// 密码验证
		var checkPwd = (rule, value, callback) => {
			if (value === '') {
				callback(new Error('请再次输入密码'));
			} else if (value !== this.regForm.password) {
				callback(new Error('两次输入密码不一致!'));
			} else {
				callback();
			}
		};
		return {
			btnLoading: false,
			dialogVisible: this.visible,
			totalSecond: 60, // 总秒数
			second: 60, // 倒计时的秒数
			timer: null, // 定时器 id
			regForm: {
				username: this.userName,
				captcha: '',
				password: '',
				repassword: ''
			},
			showSendChannel: false, //是否展示验证码发送的提示
			channelName: '',
			channelAccount: '',
			message: '',
			rules: {
				username: [{ required: true, message: '请输入账号', trigger: 'blur' }],
				password: [
					{ required: true, message: '请输入密码', trigger: 'blur' },
					// {min:8,max:20,message: '密码长度是8-20字符之间',trigger: 'blur'}
					// pattern: 指定正则来匹配
					{ pattern: /^\S{8,20}$/, message: '密码必须是8-20位的非空字符', trigger: 'blur' }
				],
				repassword: [
					{ required: true, message: '请输入确认密码', trigger: 'blur' },
					{ validator: checkPwd, trigger: 'blur' } //验证-两次密码一直
				],
				captcha: [{ required: true, message: '请输入验证码', trigger: 'blur' }]
			}
		};
	},
	methods: {
		handleClose() {
			this.$emit('handleClose');
		},
		// 重置密码
		async forgetPasw(formName) {
			let encryptRes = await getEncryptKey();
			this.$refs[formName].validate(valid => {
				if (valid) {
					this.encryptKey = encryptRes.result;
					/**加密数据后登录*/
					let encryptor = new JSEncrypt(); // JSEncrypt对象
					encryptor.setPublicKey(this.encryptKey); // 公钥
					let password = encryptor.encrypt(this.regForm.password); // 密码进行加密
					let params = {
						username: this.regForm.username,
						captcha: this.regForm.captcha,
						password: password
					};
					forgetPwd(params).then(res => {
						if (res.code == 200) {
							this.$message({
								message: '重置成功！',
								type: 'success'
							});
							this.handleClose();
						} else {
							this.$message.error(res.message);
						}
					});
				} else {
					console.log('error submit!!');
					return false;
				}
			});
		},
		// 获取验证码
		async getCode(formName) {
			this.showSendChannel = false;
			this.$refs[formName].validateField('username', valid => {
				if (valid) {
					return;
				}
				if (!this.timer && this.second === this.totalSecond) {
					this.btnLoading = true;
					let params = {
						username: this.regForm.username
					};
					verifyCodeSend(params).then(res => {
						this.message = res.message;
						this.btnLoading = false;
						if (res.code == 200) {
							this.channelAccount = res.result.channelAccount;
							this.channelName = res.result.channelName;
							this.showSendChannel = true;
							// 开启倒计时
							this.timer = setInterval(() => {
								this.second--;
								if (this.second <= 0) {
									clearInterval(this.timer);
									this.timer = null; // 重置定时器 id
									this.second = this.totalSecond; // 归位
								}
							}, 1000);
						} else {
							this.$message.error(res.message);
						}
					});
				}
			});
		}
	}
};
</script>

<style lang="scss" scoped>
.index-box {
	display: flex;
	justify-content: space-between;
}
.get-code {
	width: 120px;
	height: 40px;
	background: var(--brand-1);
	border-radius: 6px 6px 6px 6px;
	font-size: 14px;
	color: var(--brand-6);
	border: none;
	cursor: pointer;
}
.tips {
	margin-bottom: 15px;
	padding-left: 15px;
	color: red;
}
.validItem {
	margin-bottom: 12px !important;
}
::v-deep .el-input__inner {
	height: 40px;
}
::v-deep .el-dialog__body {
	padding: 30px;
}
::v-deep .el-dialog__title {
	font-weight: 600;
	font-size: 18px;
	color: #303133;
	font-family: PingFang SC, PingFang SC;
}
::v-deep .el-form-item__label {
	font-family: PingFang SC, PingFang SC;
	font-size: 14px;
	color: rgba(0, 0, 0, 0.9);
}
</style>
