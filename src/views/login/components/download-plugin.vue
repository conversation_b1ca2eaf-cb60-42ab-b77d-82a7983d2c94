<template>
	<div>
		<el-dialog
			:visible="dialogVisible"
			title="插件下载"
			width="530px"
			top="4vh"
			class="desk-el-custom-dialog"
			:modal-append-to-body="false"
			@close="close"
		>
			<div>
				<div v-for="(item, index) in pluginDownloadConfig" :key="index">
					<component :is="item.url ? 'a' : 'div'" :href="item.url" class="plugin-download-item">
						<div class="flex1">
							<div class="title">{{ item.name }}</div>
							<div class="desc">{{ item.desc }}</div>
						</div>
						<div class="download">
							<i class="coos-iconfont icon-downland icon"></i>
							下载插件
						</div>
					</component>
				</div>
			</div>
		</el-dialog>
	</div>
</template>

<script>
export default {
	props: {
		dialogVisible: {
			type: Boolean,
			default: false
		},
		pluginDownloadConfig: {
			type: Array,
			default: () => []
		}
	},
	methods: {
		close() {
			this.$emit('update:dialogVisible', false);
		}
	}
};
</script>

<style lang="scss" scoped>
::v-deep {
	.desk-el-custom-dialog .el-dialog {
		display: flex;
		flex-direction: column;
		.el-dialog__header {
			padding: 27px 20px 22px 20px;
			height: unset;
			color: #303133;
			.el-dialog__headerbtn {
				top: 30px;
			}
		}
		.el-dialog__body {
			padding: 0 20px 12px 20px;
			flex: 1;
			overflow-y: auto;
			scrollbar-width: none;
		}
	}
}
.plugin-download-item {
	background: #fafcff;
	border-radius: 12px;
	border: 1px solid #e2edf9;
	padding: 12px;
	cursor: pointer;
	display: flex;
	justify-content: space-between;
	align-items: center;

	&:hover {
		background: #f1f6ff;
		border: 1px solid var(--brand-6);

		.download {
			display: inline-block;
		}
	}

	&:not(:last-child) {
		margin-bottom: 8px;
	}

	&:last-child {
		margin-bottom: 24px;
	}

	.flex1 {
		flex: 1;
		margin-right: 12px;
	}

	.title {
		color: $primaryTextColor;
		font-weight: 500;
		line-height: 22px;
		margin-bottom: 2px;
	}

	.desc {
		color: $subTextColor;
		font-size: 12px;
		line-height: 22px;
	}

	.download {
		display: none;
		color: var(--brand-6);
	}
}
</style>
