<template>
	<div
		class="coos-login"
		:style="{
			backgroundImage: `url('${urlHttp(systemInfo.login.bg)}')`
		}"
	>
		<img class="coos-login-logo" :src="systemInfo.logo" alt="" />
		<div
			class="coos-login-cover"
			:style="{ backgroundImage: `url('${urlHttp(systemInfo.login.mainImg)}')` }"
		></div>
		<!--  登录表单  -->
		<loginForm v-if="!loginSuccess" @forgot="forgot"></loginForm>
		<!-- 忘记密码 -->
		<forgotPassword
			v-if="visible"
			:visible="visible"
			:user-name="userName"
			@handleClose="visible = false"
		></forgotPassword>
	</div>
</template>

<script>
import loginForm from '@/views/login/components/login-form';
import forgotPassword from './components/forgot-password.vue';
import { getToken } from '@/utils/auth';
import { mapActions, mapGetters, mapMutations } from 'vuex';
import { urlHttp } from '@/utils';

export default {
	name: 'Login',
	components: {
		loginForm,
		forgotPassword
	},
	data() {
		return {
			loginSuccess: false,
			visible: false,
			userName: ''
		};
	},
	computed: {
		...mapGetters(['systemInfo'])
	},
	mounted() {
		if (window.top !== window.self) {
			window.parent.postMessage('toLogin');
		}
		if (!getToken()) {
			this.SET_IFRAMES([]);
			// 每次进入登录页就初始化state状态，所有用到getters的，会去localstorage获取进行赋值
			this.RESET_ALL_STATE();
		}
	},
	methods: {
		urlHttp,
		...mapMutations('app', ['SET_IFRAMES']),
		...mapActions('user', ['RESET_ALL_STATE']),
		forgot(data) {
			this.visible = true;
			this.userName = data;
		}
	}
};
</script>

<style lang="scss" scoped>
.coos-login {
	display: flex;
	min-width: 960px;
	height: 100vh;
	background-repeat: no-repeat;
	background-size: cover;
	position: relative;
	align-items: center;
	justify-content: center;
	&-logo {
		position: absolute;
		top: 20px;
		left: 25px;
		display: block;
		height: 55px;
		// width: 218px;
	}
	&-cover {
		display: block;
		height: 496px;
		width: 536px;
		background-size: auto 496px;
		background-position: center left;
		background-repeat: no-repeat;
	}
}
</style>
