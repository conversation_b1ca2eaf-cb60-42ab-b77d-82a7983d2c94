<template>
	<el-dialog
		:title="
			conversationInfo && conversationInfo.conversation.type == 1
				? '群消息聊天记录'
				: '消息聊天记录'
		"
		:visible.sync="show"
		class="desk-el-rent-dialog"
		:before-close="handleClose"
	>
		<!--				@keydown.enter.native="search"-->
		<div style="height: 100%">
			<el-input
				v-model.trim="query"
				placeholder="查找消息、文档等"
				prefix-icon="el-icon-search"
				@input="search"
			>
				<template v-if="messagelable" slot="prepend">
					<el-tag type="info" closable @close="closableMessage">
						{{ messagelable }}
					</el-tag>
				</template>
			</el-input>
			<div v-loading="loading" class="content">
				<div class="tabs-box">
					<div
						v-for="(item, index) of freshValue"
						:key="index"
						class="tabs-box-item hide_input"
						:class="messagelable == item.label ? 'tabs-box-current' : ''"
					>
						<div v-if="item.name != 'sender'" @click="changeTab(item)">
							{{ item.label }}
						</div>
						<el-date-picker
							v-if="item.name == 'queryDate'"
							v-model="date"
							type="date"
							placeholder="选择日期"
							value-format="yyyy-MM-dd"
							:popup-append-to-body="false"
							@change="setMessageParams"
						></el-date-picker>
						<el-popover
							v-if="
								item.name == 'sender' && conversationInfo && conversationInfo.conversation.type == 1
							"
							ref="popRef"
							placement="bottom-start"
							width="400"
							trigger="click"
						>
							<div>
								<div class="search-user">
									<el-select
										v-model="value"
										placeholder="请选择"
										filterable
										@change="setMessageUser"
									>
										<el-option
											v-for="userItem in options"
											:key="userItem.uid"
											:label="userItem.displayName"
											:value="userItem.uid"
										></el-option>
									</el-select>
								</div>
								<div class="user-list">
									<div
										v-for="(event, i) of options"
										:key="i"
										class="listItem"
										@click="setMessageUser(event)"
									>
										<div class="nameInfo">
											<el-image
												v-if="event.avatarUrl"
												style="width: 36px; height: 36px; border-radius: 6px"
												:src="event.avatarUrl"
											></el-image>
											<div v-else class="image">
												{{
													event.displayName
														? event.displayName.charAt(event.displayName.length - 1)
														: ''
												}}
											</div>
											<div class="name">{{ event.displayName }}</div>
											<div
												v-if="conversationInfo.conversation._target.owner === item.uid"
												class="admin"
											>
												群主
											</div>
										</div>
									</div>
								</div>
							</div>
							<div slot="reference">{{ item.label }}</div>
						</el-popover>
					</div>
				</div>
				<el-empty
					v-if="!loading && messages.length === 0"
					style="background: #ffffff; height: 400px"
					:image="assetsUrl + '/common/img/no-search.png'"
				></el-empty>
				<!--        全部类型-->
				<div v-if="messageType == 'all'" class="list-content">
					<div v-if="messages.length" class="flex-bt">
						<div class="all" :style="isScreen ? 'width:70%' : 'width:100%'">
							<div v-for="(ltem, index) in messages" :key="index" class="all-list">
								<p class="date">{{ ltem.day }}</p>
								<div v-for="(item, i) in ltem.dataList" :key="i">
									<div v-if="item.type == 'text'" class="flex all-item" @click="messageClick(item)">
										<img v-if="item.senderAvatar" class="head" :src="item.senderAvatar" />
										<div v-else class="head">{{ item.senderName.substr(-1, 1) }}</div>
										<div class="message">
											<div class="all-name">
												<span>{{ item.senderName }}</span>
												<span>{{ item.time }}</span>
											</div>
											<div class="text">{{ item.content }}</div>
										</div>
									</div>
									<div v-if="item.type == 'img'" class="flex all-item">
										<img v-if="item.senderAvatar" class="head" :src="item.senderAvatar" />
										<div v-else class="head">{{ item.senderName.substr(-1, 1) }}</div>
										<div class="message">
											<div class="all-name">
												<span>{{ item.senderName }}</span>
												<span>{{ item.time }}</span>
											</div>
											<el-image
												style="width: 100px; height: 100px"
												class="img-box"
												:src="item.linkUrl"
												:preview-src-list="[item.linkUrl]"
											></el-image>
										</div>
									</div>
									<div v-if="item.type == 'video' && isVideo" class="flex all-item">
										<img v-if="item.senderAvatar" class="head" :src="item.senderAvatar" />
										<div v-else class="head">{{ item.senderName.substr(-1, 1) }}</div>
										<div class="message">
											<div class="all-name">
												<span>{{ item.senderName }}</span>
												<span>{{ item.time }}</span>
											</div>
											<messageVideo
												class="text"
												:width="300"
												:height="200"
												:src="item.linkUrl"
												:second="3"
											/>
										</div>
									</div>
									<div v-if="item.type == 'file'" class="flex all-item">
										<img v-if="item.senderAvatar" class="head" :src="item.senderAvatar" />
										<div v-else class="head">{{ item.senderName.substr(-1, 1) }}</div>
										<div class="message">
											<div class="all-name">
												<span>{{ item.senderName }}</span>
												<span>{{ item.time }}</span>
											</div>
											<div class="all-item-flex-box">
												<svg-icon
													v-if="iconType.includes(item.fileType)"
													class="fileTypes"
													:icon-class="item.fileType"
												></svg-icon>
												<div>
													<div class="file-text" @click.stop="downFolder(item)">
														{{ item.content }}
													</div>
													<div class="size">{{ item.size }}</div>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<!--        文件类型-->
				<div v-if="messageType == 'file'" class="list-content">
					<div v-if="messages.length" class="flex-bt">
						<div class="all">
							<div v-for="(ltem, index) in messages" :key="index" class="all-list">
								<p class="date">{{ ltem.day }}</p>
								<div class="flex all-item">
									<div>
										<!--@click="messageClick(item)"-->
										<div
											v-for="(item, i) in ltem.dataList"
											:key="i"
											class="all-item-flex file-box"
											@click.stop="downFolder(item)"
										>
											<svg-icon
												v-if="iconType.includes(item.fileType)"
												class="fileTypes"
												:icon-class="item.fileType"
											></svg-icon>
											<div>
												<div class="file-text">
													{{ item.content }}
												</div>
												<div class="size">{{ item.size }}</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<!--        视频图片类型-->
				<div v-if="messageType == 'media'" class="list-content">
					<div v-if="messages.length" class="flex-bt">
						<div class="all">
							<div v-for="(ltem, index) in messages" :key="index" class="all-list">
								<p class="date">{{ ltem.day }}</p>
								<div class="flex all-item">
									<div class="all-item-flex">
										<div v-for="(item, i) in ltem.dataList" :key="i" class="all-item-flex-box">
											<messageVideo
												v-if="item.type == 'video' && isVideo"
												:width="94"
												:height="94"
												class="text"
												:src="item.linkUrl"
												:second="3"
											/>
											<el-image
												v-if="item.type == 'img'"
												style="width: 100px; height: 100px"
												:src="item.linkUrl"
												:preview-src-list="[item.linkUrl]"
											></el-image>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</el-dialog>
</template>

<script>
import store from '@/wile-fire/store';
// import ConversationInfo from '@/wile-fire/wfc/model/conversationInfo';
// import wfc from '@/wile-fire/wfc/client/wfc';
// import { getNewSystemMessage, getSystemMessageList } from '@/api/modules/im';
import { searchHistory } from '@/api/modules/historyMessage';
import Conversation from '@/wile-fire/wfc/model/conversation';
import MessageContentType from '@/wile-fire/wfc/messages/messageContentType';
import messageVideo from '@/components/message-video/index.vue';
import { debounce, previewFile } from '@/utils';
import { assetsUrl } from '@/config';

export default {
	components: {
		messageVideo
	},
	props: {
		params: {
			type: Object,
			default: null
		},
		users: {
			type: Array,
			default: () => []
		}
	},
	data() {
		return {
			assetsUrl,
			show: true,
			category: 'all',
			loading: false,
			searchMessage: [], //搜索出的历史消息
			searchCategory: 'all',
			date: '',
			value: '',
			preMonth: '',
			freshValue: [
				{ label: '图片/视频', name: 'media', type: 'media' },
				{ label: '文件', name: 'file', type: 'file' },
				{ label: '日期', type: 'all', name: 'queryDate' },
				{ label: '成员', type: 'all', name: 'sender' }
			],
			options: [],
			messages: [],
			chatHistoryList: [],
			isScreen: false,
			searchParams: {
				keyword: '',
				queryDate: ''
			},
			isVideo: true,
			query: '',
			messageType: 'all',
			conversationInfo: null,
			messagelable: '',
			iconType: [
				'folder',
				'doc',
				'image',
				'pp',
				'excel',
				'pdf',
				'text',
				'word',
				'unknown',
				'zip',
				'video'
			]
		};
	},
	watch: {
		category() {
			this.infiniteHandler();
		}
	},
	mounted() {
		this.conversationInfo = store.getConversationInfo(
			new Conversation(this.params.type, this.params.target, this.params.line)
		);
		this.options = this.users;
		console.log(this.users, 'users');
		this.infiniteHandler();
	},
	methods: {
		//搜索改变
		search: debounce(
			function () {
				this.searchParams = {
					sender: this.value,
					queryDate: this.date,
					keyword: this.query
				};
				this.infiniteHandler();
			},
			500,
			false
		),
		messageClick(item) {
			let message = {
				messageId: item.msgId,
				...item
			};
			this.$emit('positionHistory', message);
			// this.positionHistory(message);
		},
		//
		categoryContentTypes() {
			let contentTypes = [];
			switch (this.category) {
				case 'file':
					contentTypes = [MessageContentType.File];
					break;
				case 'media':
					contentTypes = [MessageContentType.Video, MessageContentType.Image];
					break;
				default:
					break;
			}
			return contentTypes;
		},
		// 选择筛选条件
		changeTab(item) {
			this.messages = [];

			this.messageType = item.type;
			this.messagelable = item.label;
			this.date = '';
			this.value = '';
			this.search();
		},
		//时间
		setMessageParams() {
			this.messages = [];
			this.messageType = 'all';
			this.messagelable = '日期';
			this.value = '';
			this.search();
		},
		//成员
		setMessageUser(item) {
			this.messages = [];
			this.date = '';
			this.messageType = 'all';
			this.messagelable = '成员';
			this.value = item.uid;
			console.log(this.$refs.popRef, 'this.$refs.popRef');
			this.$refs.popRef[0].doClose();
			this.search();
		},
		closableMessage() {
			this.messages = [];

			this.messageType = 'all';
			this.messagelable = '';
			this.value = '';
			this.date = '';
			this.search();
		},
		// 获取消息数据
		infiniteHandler() {
			this.loading = true;

			let params = {
				convType: this.conversationInfo.conversation.type,
				target: this.conversationInfo.conversation.target,

				type: this.messageType,
				...this.searchParams
			};
			searchHistory(params)
				.then(res => {
					console.log('res', res);
					if (res.code === 200 && res.success) {
						this.messages = res?.result?.list || [];
						this.preMonth = res?.result?.preMonth || '';
						this.loading = false;
					}
					//数据处理（按时间分类）
				})
				.catch(err => {
					this.$messages.error(err.message || '数据查询异常');
				});
			/* let tmp = store.searchMessageInTypes(this.conversationInfo.conversation, [], '', 20);
      //let p = store.searchMessageInTypes(this.conversationInfo.conversation, []);
      //let a = store.searchMessage(this.conversationInfo.conversation, '');
      //let p = wfc.loadRemoteLineMessages(0,[0],a[5].messageId,99)
      console.log('1111122222', tmp); */
		},

		handleClose(done) {
			this.isVideo = false;
			this.$emit('close');
			done();
		},
		downFolder(item) {
			console.log(item);
			let { linkUrl } = item;
			previewFile(linkUrl);
		},
		handleClick(tab, event) {
			this.category = tab.name;
		}
	}
};
</script>

<style lang="scss" scoped>
.flex {
	display: flex;
}

.flex-bt {
	display: flex;
	height: 100%;
	overflow-y: scroll;
	justify-content: space-between;
	@include noScrollBar;
}

.contentMessage {
	display: flex;
	flex-direction: column;
	padding: 8px 0 8px 0;

	.fileContent {
		display: flex;
		gap: 8px;
		width: 324px;
		height: 50px;

		&-image {
			width: 40px;
			height: 40px;
		}

		&-detail {
			height: 50px;
			display: flex;
			flex-direction: column;
			gap: 8px;

			.name {
				font: normal 500 14px 'PingFang SC-Medium';
				color: $textColor;
			}

			.size {
				font: normal 400 114;
			}
		}
	}
}

.content {
	position: relative;
	height: calc(100% - 55px);

	::v-deep .el-tabs {
		height: 100%;

		.el-tabs__content {
			height: calc(100% - 55px);

			.el-tab-pane {
				height: 100%;
			}
		}
	}

	.screen {
		cursor: pointer;
		position: absolute;
		top: 10px;
		right: 0px;
		padding: 3px;
	}

	.isScreen {
		color: var(--brand-6);
		background: #eceef4;
	}

	.all {
		width: 100%;

		.all-list {
			margin-top: 24px;

			.date {
				font-size: 13px;
				color: $subTextColor;
			}

			.all-item {
				margin-top: 16px;
				padding-bottom: 16px;
				border-bottom: 1px solid #f0f0f0;

				img {
					width: 94px;
					height: 94px;
					border-radius: 3px;
					margin-right: 12px;
				}

				.message {
					margin-left: 10px;
					width: calc(100% - 48px);

					.all-name {
						display: flex;
						justify-content: space-between;
						font-size: 12px;
						color: $subTextColor;
					}

					.text {
						margin-top: 6px;
						font-size: 14px;
						color: $textColor;
					}
				}

				.head {
					width: 36px;
					height: 36px;
					background: var(--brand-6);
					border-radius: 6px;
					line-height: 36px;
					text-align: center;
					color: #fff;
					font-size: 14px;
					font-family: PingFang SC, PingFang SC;
				}
			}
		}
	}
}

.all {
	min-height: 405px;
}

.screen-box {
	width: 200px;
	background: #f3f4f6;
	//height: 400px;
	bottom: 0;
	position: absolute;
	top: 40px;
	right: 0;
	z-index: 999;
	padding: 8px 16px;

	.header {
		display: flex;
		justify-content: space-between;
		align-items: center;

		span {
			font-size: 14px;
			font-weight: 600;
			color: $textColor;
			font-style: normal;
		}

		i {
			cursor: pointer;
		}
	}

	.one {
		margin-top: 34px;
	}

	.options {
		margin-bottom: 12px;

		p {
			margin-bottom: 5px;
			font-size: 14px;
			color: $primaryTextColor;
		}
	}
}

::v-deep .el-dialog__title {
	font-weight: 800;
	font-size: 18px;
	color: #303133;
}

::v-deep .el-input__inner {
	height: 40px;
	border-radius: 6px;
	border: 1px solid #dce3e7 !important;
}

::v-deep .el-input__icon {
	line-height: 40px;
	color: var(--brand-6);
}

::v-deep .el-tabs--top .el-tabs__item.is-top:nth-child(2) {
	padding-left: 20px;
}

::v-deep .el-tabs__item {
	font-size: 16px;
}

.all-item-flex {
	display: flex;
	align-items: center;
}

.all-item-flex-box {
	margin-right: 11px;
	display: flex;
	margin-top: 8px;
}

.fileTypes {
	flex-shrink: 0;
	width: 40px !important;
	height: 40px !important;
}

.size {
	font-family: PingFang SC, PingFang SC;
	font-weight: 400;
	font-size: 12px;
	color: $subTextColor;
	line-height: 20px;
	margin-left: 8px;
}

.file-text {
	font-family: PingFang SC, PingFang SC;
	margin-left: 8px;
	font-weight: 500;
	font-size: 14px;
	color: $textColor;
	line-height: 22px;
}

.file-box {
	height: 50px;
	margin-bottom: 8px;
}

.img-box {
	margin-top: 8px;
}

.tabs-box {
	margin-top: 10px;
	border-bottom: 1px solid #dce3e7;
	padding: 0 15px;
	.tabs-box-item {
		font-size: 16px;
		cursor: pointer;
		margin-right: 48px;
		padding: 12px 0;
		// height: 40px;
		-webkit-box-sizing: border-box;
		box-sizing: border-box;
		// line-height: 40px;
		display: inline-block;
		list-style: none;
		font-weight: 500;
		// color: #1890ff;
		color: #2f446b;
		font-size: 16px;
	}
	.tabs-box-current {
		color: var(--brand-6);
		border-bottom: 3px solid var(--brand-6);
	}
}

.hide_input {
	position: relative !important;
}

//修改控件自带的css
.hide_input .el-date-editor {
	width: 10px;
	position: absolute; //绝对定位
	top: 0;
	left: 0;
	opacity: 0; //设置完全透明
	::v-deep .el-input__inner {
		padding: 0 !important;
	}
}

.search-user {
	margin: 0 28px;

	.el-select {
		width: 100%;
	}
}

.user-list {
	height: 400px;
	overflow-y: auto;
}

.listItem {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 16px 8px;
	margin: 0px 20px;
	border-bottom: 1px solid #f0f0f0;
	font-size: 14px;
	line-height: 22px;
	font-weight: 400;
	color: $primaryTextColor;

	.nameInfo {
		display: flex;
		align-items: center;
		width: 160px;
		cursor: pointer;

		.name {
			margin-left: 8px;
			font-weight: 500;
		}

		.image {
			width: 36px;
			border-radius: 6px;
			background: #3088ff;
			height: 36px;
			line-height: 36px;
			text-align: center;
			font-size: 14px;
			color: #ffffff;
		}
	}

	.depart {
		//width: 153px;
		flex: 1;
		display: flex;
		// justify-content: center;
	}

	.response {
		margin-right: 40px;
		width: 260px;
	}
}
.admin {
	font-size: 12px;
	color: #4e66e2;
	background: #edf3ff;
	border-radius: 3px;
	padding: 5px;
	margin-left: 6px;
}
.list-content {
	overflow-y: auto;
	height: calc(100% - 77px);
}

::v-deep .el-dialog__body {
	overflow: hidden;
}

::v-deep .el-tag.el-tag--info {
	border: none;
	background: #e8e9ed;
	color: #2f446b;
}
</style>
