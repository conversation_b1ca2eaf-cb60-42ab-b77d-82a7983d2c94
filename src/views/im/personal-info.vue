<template>
	<div class="history">
		<div v-if="!memberShow">
			<div class="info">
				<img
					v-if="conversationInfo && conversationInfo.conversation"
					:src="conversationInfo.conversation._target.portrait"
					alt=""
				/>
				<img v-else src="../../wile-fire/assets/images/Group-avatar.png" alt="" />
				<div>{{ conversationInfo.conversation._target._displayName }}</div>
			</div>
			<div class="config">
				<div>
					<div>查找聊天记录</div>
					<span @click="openHistoty">
						图片、视频、文件等
						<i class="el-icon-arrow-right"></i>
					</span>
				</div>
				<div>
					<div>消息免打扰</div>
					<el-switch v-model="notDisturb" @change="onDisturb"></el-switch>
				</div>
				<div style="border: none">
					<div>消息置顶</div>
					<el-switch v-model="messageTop" @change="onTop"></el-switch>
				</div>
			</div>
			<div class="delete" @click="clearMessage">删除聊天记录</div>
		</div>

		<groupMembersVue
			v-else
			:conversation-info="conversationInfo"
			@onBack="memberShow = false"
		></groupMembersVue>
		<chatHistory
			v-if="dialogVisible"
			:params="params"
			:users="users"
			@close="dialogVisible = false"
			@positionHistory="positionHistory"
		></chatHistory>
	</div>
</template>

<script>
import groupMembersVue from './group-members.vue';
import store from '@/wile-fire/store';
import ConversationInfo from '@/wile-fire/wfc/model/conversationInfo';
import wfc from '@/wile-fire/wfc/client/wfc';
import { msgReminder } from '@/api/modules/address-book';
import { getItem } from '@/wile-fire/ui/util/storageHelper';
import chatHistory from './chat-history.vue';

export default {
	components: {
		groupMembersVue,
		chatHistory
	},
	props: {
		conversationInfo: {
			type: ConversationInfo,
			required: false,
			default: {}
		}
	},
	data() {
		return {
			value: false,
			memberShow: false,
			dialogVisible: false,
			notDisturb: false,
			messageTop: false,
			params: null,
			visible: false,
			disabledIds: [],
			users: [],
			filterQuery: '',
			groupMemberUserInfos: store.getConversationMemberUsrInfos(this.conversationInfo.conversation)
		};
	},
	computed: {
		// 是否是自己创建的群组
		isSelfGroup() {
			return (
				this.conversationInfo &&
				this.conversationInfo.conversation &&
				this.conversationInfo.conversation._target.owner === wfc.getUserId()
			);
		}
	},
	mounted() {
		this.init();
	},
	methods: {
		init() {
			this.notDisturb = this.conversationInfo.isSilent;
			this.messageTop = this.conversationInfo.top === 1;
		},
		openPersonnel(type) {
			this.memberShow = true;
		},
		/**确定人员选择*/
		surePerson(data) {
			let ids = data.map(u => u.id + getItem('clientCode') + getItem('tenantId'));
			wfc.addGroupMembers(this.conversationInfo.conversation.target, ids, null, [0]);
			this.visible = false;
		},
		positionHistory(message) {
			this.dialogVisible = false;
			this.$emit('positionHistory', message);
		},
		/**关闭人员选择*/
		closePerson() {
			this.visible = false;
		},
		// 置顶聊天
		onTop(e) {
			store.setConversationTop(this.conversationInfo.conversation, e ? 1 : 0);
		},
		// 消息免打扰
		onDisturb(e) {
			store.setConversationSilent(this.conversationInfo.conversation, e);
			this.msgReminderRecord(this.conversationInfo.conversation, e);
		},
		// 清空聊天记录
		clearMessage() {
			this.$confirm('请确认是否删除聊天记录?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			})
				.then(() => {
					wfc.clearMessages(this.conversationInfo.conversation);
					this.$message({
						type: 'success',
						message: '删除成功!'
					});
				})
				.catch(() => {
					this.$message({
						type: 'info',
						message: '已取消删除'
					});
				});
		},
		//打开聊天记录
		openHistoty() {
			let conversation = this.conversationInfo.conversation;
			this.params = {
				type: conversation.type,
				target: conversation.target,
				line: conversation.line
			};

			this.dialogVisible = true;
		},
		//邀请
		showCreateConversationModal() {
			this.visible = true;
			// let successCB = users => {
			// 	let ids = users.map(u => u.uid);
			// 	wfc.addGroupMembers(this.conversationInfo.conversation.target, ids, null, [0]);
			// };
			// let groupMemberUserInfos = store.getGroupMemberUserInfos(
			// 	this.conversationInfo.conversation.target,
			// 	false
			// );

			// this.$pickContact({
			// 	successCB,
			// 	initialCheckedUsers: groupMemberUserInfos,
			// 	uncheckableUsers: groupMemberUserInfos,
			// 	confirmTitle: this.$t('common.add')
			// });
		},
		// 移除
		showRemoveGroupMemberModal() {
			this.memberShow = true;
		},
		/**
		 * 同步记录用户设置会话免打扰情况
		 * @param target 会话对象(单聊IM用户编号/群聊编号)
		 * @param notNotice 消息免打扰设置值
		 * 操作：不处理响应
		 */
		msgReminderRecord(conversation, notNotice) {
			msgReminder({
				convType: conversation.type,
				target: conversation.target,
				messageNoNotice: notNotice
			});
		}
	}
};
</script>

<style scoped lang="scss">
.history {
	// flex-shrink: 0;
	border-left: 1px solid #dce3e7;
	width: 320px;
	background: #f3f4f6;
	height: 100%;
	// @include flexBox(flex-start);
	// flex-direction: column;
	box-shadow: -3px 0px 7px 0px rgba(226, 231, 235, 0.81);
	padding: 16px;

	> div {
		> div {
			background: #ffffff;
			border-radius: 5px;
			padding: 10px;
			width: 100%;
		}
	}
}
.title {
	font-size: 14px;
	color: $subTextColor;
	margin-top: 16px;
	font-family: PingFang SC, PingFang SC;
	padding-left: 5px;
	width: 100%;
}

.info {
	display: flex;
	align-items: center;
	cursor: pointer;
	div {
		margin-left: 8px;
		font-size: 15px;
		color: $primaryTextColor;
	}
	img {
		width: 48px;
		height: 48px;
		border-radius: 6px;
	}
}

.personnel {
	margin-top: 12px;
	font-size: 12px;
	.num {
		display: flex;
		justify-content: space-between;
		.renNum {
			font-size: 12px;
			color: $subTextColor;
			cursor: pointer;
		}
	}
	.people-config {
		padding: 12px 10px 2px 10px;
		display: flex;
		// justify-content: space-between;
		> div {
			div {
				width: 30px;
				height: 30px;
				line-height: 28px;
				text-align: center;
				border-radius: 50%;
				margin-bottom: 5px;
			}
			span {
				font-size: 10px;
				color: $textColor;
				display: inline-block;
				min-width: 28px;
				text-align: center;
			}
		}
		.user {
			text-align: center;
			width: 40px;
			margin-right: 17px;
			div {
				background: var(--brand-6);
				color: #fff;
				font-size: 12px;
				font-family: PingFang SC, PingFang SC;
			}
			.people_img {
				width: 30px;
				height: 30px;
				border-radius: 50%;
			}
		}
		.add {
			cursor: pointer;
			div {
				border: 1px solid $borderColor;
				color: $borderColor;
				font-size: 20px;
			}
		}
	}
}
.names {
	margin-top: 6px;
	padding: 5px !important;
}

.config {
	margin-top: 12px;
	padding: 8px 10px;
	> div {
		display: flex;
		justify-content: space-between;
		font-size: 14px;
		color: $textColor;
		font-family: PingFang SC, PingFang SC;
		padding: 8px 0;
		border-bottom: 1px solid #f0f0f0;
		span {
			font-size: 12px;
			color: $subTextColor;
			cursor: pointer;
		}
	}
}
.delete {
	margin-top: 12px;
	cursor: pointer;
	font-size: 14px;
	color: $textColor;
	padding: 8px 10px;
}
.quit {
	text-align: center;
	color: #ff4d4f;
}

::v-deep .el-input__inner {
	border: 0;
}
::v-deep .el-input.is-disabled .el-input__inner {
	background-color: rgba(0, 0, 0, 0);
	color: $textColor;
	cursor: auto;
}
</style>
