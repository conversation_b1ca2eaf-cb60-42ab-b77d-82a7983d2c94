<template>
	<div class="group-members">
		<div class="header">
			<img src="../../wile-fire/assets/images/left.png" alt="" @click="back" />
			群成员({{ users.length }})
		</div>
		<el-input
			v-model="filterQuery"
			placeholder="请输入内容"
			prefix-icon="el-icon-search"
		></el-input>
		<el-checkbox-group v-if="false" v-model="checkList" @change="onChange">
			<div v-for="(item, index) in userList" :key="index">
				<el-checkbox :label="'复选框' + item">
					<span>{{ item }}</span>
				</el-checkbox>
			</div>
		</el-checkbox-group>
		<div v-else class="list-content">
			<div
				v-for="(item, index) in users"
				:key="index"
				style="margin-top: 12px"
				@mouseenter="item.isConfig = true"
				@mouseleave="item.isConfig = false"
			>
				<div class="list" :class="item.isConfig ? 'current' : ''">
					<img v-if="item.portrait" :src="item.portrait" class="people_img" />
					<div v-else class="user">{{ item.displayName.substr(-1, 1) }}</div>
					<div class="name">{{ item.displayName }}</div>
					<div v-if="conversationInfo.conversation._target.owner === item.uid" class="admin">
						群主
					</div>
					<div
						v-show="item.isConfig && isSelfGroup && index !== 0"
						class="btns"
						@click="remove(item)"
					>
						<i class="el-icon-delete"></i>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import store from '@/wile-fire/store';
import wfc from '@/wile-fire/wfc/client/wfc';
import ConversationInfo from '@/wile-fire/wfc/model/conversationInfo';
export default {
	props: {
		conversationInfo: {
			type: ConversationInfo,
			required: false,
			default: {}
		}
	},
	data() {
		return {
			checkList: [],
			userList: [],

			filterQuery: ''
		};
	},

	computed: {
		// 是否是自己创建的群组
		isSelfGroup() {
			return (
				this.conversationInfo &&
				this.conversationInfo.conversation &&
				this.conversationInfo.conversation._target.owner === wfc.getUserId()
			);
		},
		users() {
			if (this.filterQuery) {
				let arr = store.filterUsers(this.userList, this.filterQuery);
				arr.forEach((item, index) => {
					if (item.uid === this.conversationInfo.conversation._target.owner) {
						arr.splice(index, 1); // 从原位置删除元素
						arr.unshift(item); // 将元素插入数组首位
					}
				});
				return arr;
			} else {
				let arr = this.userList;
				arr.forEach((item, index) => {
					if (item.uid === this.conversationInfo.conversation._target.owner) {
						arr.splice(index, 1); // 从原位置删除元素
						arr.unshift(item); // 将元素插入数组首位
					}
				});
				console.log(arr);
				return arr;
			}
		}
	},
	created() {
		this.init();
	},
	methods: {
		init() {
			let arr = store.getConversationMemberUsrInfos(this.conversationInfo.conversation);
			this.userList = arr.map(item => {
				return {
					...item,
					isConfig: false
				};
			});
		},
		onChange(e) {
			console.log(e, this.checkList);
		},
		back() {
			this.$emit('onBack');
		},
		remove(data) {
			this.$emit('removeMembers', data);
		}
		//
	}
};
</script>

<style lang="scss" scoped>
.header {
	font-weight: 800;
	font-size: 16px;
	height: 30px;
	color: $primaryTextColor;
	margin-bottom: 12px;
	display: flex;
	align-items: center;
	img {
		height: 20px;
		margin-right: 8px;
		cursor: pointer;
	}
}
.list {
	width: 100%;
	padding: 16px 8px;
	position: relative;
	cursor: pointer;
	display: flex;
	align-items: center;
	// div {
	// 	display: inline-block;
	// }
	.people_img {
		width: 36px;
		height: 36px;
		border-radius: 6px;
		margin-right: 8px;
	}
	.user {
		width: 36px;
		height: 36px;
		text-align: center;
		line-height: 36px;
		background: var(--brand-6);
		color: #fff;
		font-size: 14px;
		font-family: PingFang SC, PingFang SC;
		border-radius: 6px;
		margin-right: 8px;
	}
	.admin {
		font-size: 12px;
		color: var(--brand-6);
		background: var(--brand-1);
		border-radius: 3px;
		padding: 5px;
		margin-left: 6px;
	}
	.btns {
		color: #ff4d4f;
		position: absolute;
		right: 10px;
		top: 50%;
		transform: translateY(-50%);
	}
}
.current {
	border-radius: 5px;
	background: #fff;
}
.group-members {
	height: 100%;
	display: flex;
	flex-direction: column;
	overflow: hidden;
}
.list-content {
	overflow: auto;
	flex: 1;
	@include noScrollBar;
}
::v-deep .el-input__inner {
	width: 100%;
	background-color: #e6e8ec;
}
</style>
