<template>
	<div class="history">
		<div v-if="!memberShow">
			<div class="info">
				<img
					v-if="conversationInfo && conversationInfo.conversation"
					:src="conversationInfo.conversation._target.portrait"
					alt=""
					@click="pickFile"
				/>
				<img v-else src="../../wile-fire/assets/images/Group-avatar.png" alt="" @click="pickFile" />
				<input
					v-if="isSelfGroup"
					ref="fileInput"
					class="yehuo-ion-android-attach"
					type="file"
					accept="image/png, image/jpeg"
					style="display: none"
					@change="onPickFile($event)"
				/>
				<div>{{ conversationInfo.conversation._target._displayName }}</div>
			</div>
			<div class="personnel">
				<div class="num">
					<span>群成员</span>
					<span class="renNum" @click="openPersonnel('arr')">
						{{ users.length }}人
						<i class="el-icon-arrow-right"></i>
					</span>
				</div>
				<div class="people-config">
					<div v-for="(item, index) in users.slice(0, 3)" :key="index" class="user">
						<img v-if="item.portrait" :src="item.portrait" class="people_img" />
						<div v-else>{{ item.displayName.substr(-1, 1) }}</div>
						<span>{{ item.displayName }}</span>
					</div>
					<div class="add" style="margin-right: 16px" @click="showCreateConversationModal">
						<div>+</div>
						<span>邀请</span>
					</div>
					<div
						v-if="isSelfGroup"
						class="add"
						style="margin-right: 0"
						@click="showRemoveGroupMemberModal"
					>
						<div>-</div>
						<span>移除</span>
					</div>
				</div>
			</div>
			<p class="title">群聊名称</p>
			<div class="names">
				<el-input
					v-model="groupName"
					placeholder="请输入内容"
					:disabled="!isSelfGroup"
					@blur="setGroupName"
				></el-input>
			</div>
			<p class="title">我的昵称</p>
			<div class="names">
				<el-input v-model="newGroupAlias" :placeholder="groupAlias" @blur="setNickname"></el-input>
			</div>
			<div class="config">
				<div>
					<div>查找聊天记录</div>
					<span @click="openHistoty">
						图片、视频、文件等
						<i class="el-icon-arrow-right"></i>
					</span>
				</div>
				<div>
					<div>消息免打扰</div>
					<el-switch v-model="notDisturb" @change="onDisturb"></el-switch>
				</div>
				<div style="border: none">
					<div>群消息置顶</div>
					<el-switch v-model="messageTop" @change="onTop"></el-switch>
				</div>
			</div>
			<!-- <div class="delete" @click="clearMessage">删除聊天记录</div> -->
			<div class="delete quit" @click="quitGroup">退出群聊</div>
			<div v-if="isSelfGroup" class="delete quit" style="color: #1890ff" @click="dismissGroup">
				解散群聊
			</div>
		</div>

		<groupMembersVue
			v-else
			ref="groupMembersVue"
			:conversation-info="conversationInfo"
			@onBack="memberShow = false"
			@removeMembers="removeMembers"
		></groupMembersVue>

		<chatHistory
			v-if="dialogVisible"
			:params="params"
			:users="users"
			@close="dialogVisible = false"
			@positionHistory="positionHistory"
		></chatHistory>

		<!-- 新增弹窗 -->
		<orgPersonnelDialog
			title="请选择群成员"
			:init-values="memberArr"
			:visible="visible"
			need-all-data
			disable-all
			:data-source="['user']"
			:disabled-ids="disabledIds"
			@sure="surePerson"
			@close="closePerson"
		></orgPersonnelDialog>
	</div>
</template>

<script>
import chatHistory from './chat-history.vue';
import groupMembersVue from './group-members.vue';
import store from '@/wile-fire/store';
import ConversationInfo from '@/wile-fire/wfc/model/conversationInfo';
import wfc from '@/wile-fire/wfc/client/wfc';
import ModifyGroupInfoType from '@/wile-fire/wfc/model/modifyGroupInfoType';
import orgPersonnelDialog from '@/components/org-personnel-dialog/index.vue';
import MessageContentMediaType from '@/wile-fire/wfc/messages/messageContentMediaType';
import { msgReminder } from '@/api/modules/address-book';
import { getItem } from '@/wile-fire/ui/util/storageHelper';
export default {
	components: {
		groupMembersVue,
		orgPersonnelDialog,
		chatHistory
	},
	props: {
		conversationInfo: {
			type: ConversationInfo,
			required: false,
			default: {}
		}
	},
	data() {
		return {
			groupName: '',
			groupAlias: '',
			newGroupAlias: '',
			value: false,
			memberShow: false,
			dialogVisible: false,
			notDisturb: false,
			messageTop: false,
			params: null,
			visible: false,
			memberArr: [],
			disabledIds: [],

			filterQuery: '',
			groupMemberUserInfos: store.getConversationMemberUsrInfos(this.conversationInfo.conversation)
		};
	},
	computed: {
		// 是否是自己创建的群组
		isSelfGroup() {
			return (
				this.conversationInfo &&
				this.conversationInfo.conversation &&
				this.conversationInfo.conversation._target.owner === wfc.getUserId()
			);
		},
		users() {
			if (this.filterQuery) {
				let arr = store.filterUsers(this.groupMemberUserInfos, this.filterQuery);
				arr.forEach((item, index) => {
					if (item.uid === this.conversationInfo.conversation._target.owner) {
						arr.splice(index, 1); // 从原位置删除元素
						arr.unshift(item); // 将元素插入数组首位
					}
				});
				return arr;
			} else {
				let arr = this.groupMemberUserInfos;
				arr.forEach((item, index) => {
					if (item.uid === this.conversationInfo.conversation._target.owner) {
						arr.splice(index, 1); // 从原位置删除元素
						arr.unshift(item); // 将元素插入数组首位
					}
				});
				return arr;
			}
		}
	},
	mounted() {
		this.init();
		this.getList();
	},
	methods: {
		getList() {
			let clientCode = getItem('clientCode');
			let tenantId = getItem('tenantId');
			this.disabledIds = this.groupMemberUserInfos.map(item => {
				return item.uid.replace(clientCode, '').replace(tenantId, '');
			});
			// getPersonTree({pageSize:999,pageNo:1,realName:''}).then(res=>{
			// 	console.log(this.memberArr)
			// })
		},
		positionHistory(message) {
			this.dialogVisible = false;
			this.$emit('positionHistory', message);
		},
		//
		removeMembers(data) {
			let that = this;
			console.log(data);
			this.$IMalert({
				title: '移除成员',
				content: '是否确认将该成员群组？',
				confirmText: '确定',
				cancelText: '取消',
				cancelCallback: () => {},
				confirmCallback: () => {
					console.log(this.conversationInfo.conversation.target);
					wfc.kickoffGroupMembers(
						this.conversationInfo.conversation.target,
						[data.uid],
						[0],
						null,
						() => {
							that.groupMemberUserInfos = store.getConversationMemberUsrInfos(
								that.conversationInfo.conversation
							);
							this.$refs.groupMembersVue.init();
						}
					);
				}
			});
		},
		// 设置头像
		pickFile() {
			if (!this.isSelfGroup) {
				this.$notify({
					text: '群主或管理员，才能更新头像',
					type: 'warn'
				});
				return;
			}
			this.$refs['fileInput'].click();
		},
		onPickFile(event) {
			let file = event.target.files[0];
			wfc.uploadMedia(
				file.name,
				file,
				MessageContentMediaType.Portrait,
				url => {
					wfc.modifyGroupInfo(
						this.conversationInfo.conversation.target,
						ModifyGroupInfoType.Modify_Group_Portrait,
						url,
						[0],
						null,
						() => {
							console.log('modify group portrait success', url);
						},
						err => {
							console.log('err', err);
						}
					);
				},
				err => {
					console.log('update media error', err);
				},
				(p, t) => {}
			);
		},
		init() {
			// 群名称
			this.groupName = this.conversationInfo.conversation._target._displayName;
			let userInfo = wfc.getUserInfo(
				wfc.getUserId(),
				false,
				this.conversationInfo.conversation.target
			);
			this.groupAlias = userInfo.groupAlias ? userInfo.groupAlias : userInfo.displayName;
			this.newGroupAlias = userInfo.groupAlias;
			this.notDisturb = this.conversationInfo.isSilent;
			this.messageTop = this.conversationInfo.top === 1;
		},
		openPersonnel(type) {
			this.memberShow = true;
		},
		/**确定人员选择*/
		surePerson(data) {
			let that = this;
			let ids = data.map(u => u.id + getItem('clientCode') + getItem('tenantId'));
			console.log(ids);
			wfc.addGroupMembers(this.conversationInfo.conversation.target, ids, '', [0], null, () => {
				that.groupMemberUserInfos = store.getConversationMemberUsrInfos(
					that.conversationInfo.conversation
				);
			});
			this.visible = false;
		},
		/**关闭人员选择*/
		closePerson() {
			this.visible = false;
		},
		// 置顶聊天
		onTop(e) {
			store.setConversationTop(this.conversationInfo.conversation, e ? 1 : 0);
		},
		// 消息免打扰
		onDisturb(e) {
			store.setConversationSilent(this.conversationInfo.conversation, e);
			this.msgReminderRecord(this.conversationInfo.conversation, e);
		},
		// 修改群名称
		setGroupName() {
			let groupId = this.conversationInfo.conversation.target;
			if (
				!this.groupName ||
				this.groupName === this.conversationInfo.conversation._target._displayName
			) {
				this.groupName = this.conversationInfo.conversation._target._displayName;
			} else {
				wfc.modifyGroupInfo(
					groupId,
					ModifyGroupInfoType.Modify_Group_Name,
					this.groupName,
					[0],
					null,
					() => {
						this.conversationInfo.conversation._target._displayName = this.groupName;
						this.$message({
							message: '修改成功',
							type: 'success'
						});
					},

					err => {
						// do nothing
						console.log('err', err);
					}
				);
			}
		},
		// 修改个人群昵称
		setNickname() {
			let groupId = this.conversationInfo.conversation.target;
			if (this.newGroupAlias && this.newGroupAlias !== this.groupAlias) {
				wfc.modifyGroupAlias(
					groupId,
					this.newGroupAlias,
					[0],
					null,
					() => {
						this.groupAlias = this.newGroupAlias;
					},
					null
				);
			}
		},
		// 清空聊天记录
		clearMessage() {
			this.$confirm('请确认是否删除聊天记录?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			})
				.then(() => {
					wfc.clearMessages(this.conversationInfo.conversation);
					this.$message({
						type: 'success',
						message: '删除成功!'
					});
				})
				.catch(() => {});
		},
		// 退出群聊
		quitGroup() {
			this.$confirm('确定退出群组？?', '退出群组', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			})
				.then(() => {
					store.quitGroup(this.conversationInfo.conversation.target);
					this.$emit('close');
					this.$message({
						type: 'success',
						message: '操作成功!'
					});
				})
				.catch(() => {});
		},
		// 解散群聊
		dismissGroup() {
			let groupId = this.conversationInfo.conversation.target;
			this.$confirm('确定解散群组？', '解散群组', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			})
				.then(() => {
					wfc.dismissGroup(
						groupId,
						[0],
						null,
						res => {
							this.$emit('close');
							this.$message({
								type: 'success',
								message: '操作成功!'
							});
						},
						err => {}
					);
				})
				.catch(() => {});
		},
		//打开聊天记录
		openHistoty() {
			let conversation = this.conversationInfo.conversation;
			this.params = {
				type: conversation.type,
				target: conversation.target,
				line: conversation.line
			};

			this.dialogVisible = true;
		},
		//邀请
		showCreateConversationModal() {
			this.visible = true;
			// let successCB = users => {
			// 	let ids = users.map(u => u.uid);
			// 	wfc.addGroupMembers(this.conversationInfo.conversation.target, ids, null, [0]);
			// };
			// let groupMemberUserInfos = store.getGroupMemberUserInfos(
			// 	this.conversationInfo.conversation.target,
			// 	false
			// );

			// this.$pickContact({
			// 	successCB,
			// 	initialCheckedUsers: groupMemberUserInfos,
			// 	uncheckableUsers: groupMemberUserInfos,
			// 	confirmTitle: this.$t('common.add')
			// });
		},
		// 移除
		showRemoveGroupMemberModal() {
			this.memberShow = true;
		},
		/**
		 * 同步记录用户设置会话免打扰情况
		 * @param target 会话对象(单聊IM用户编号/群聊编号)
		 * @param notNotice 消息免打扰设置值
		 * 操作：不处理响应
		 */
		msgReminderRecord(conversation, notNotice) {
			msgReminder({
				convType: conversation.type,
				target: conversation.target,
				messageNoNotice: notNotice
			});
		}
	}
};
</script>

<style scoped lang="scss">
.history {
	// flex-shrink: 0;
	border-left: 1px solid #dce3e7;
	width: 320px;
	background: #f3f4f6;
	height: 100%;
	// @include flexBox(flex-start);
	// flex-direction: column;
	box-shadow: -3px 0px 7px 0px rgba(226, 231, 235, 0.81);
	padding: 16px;

	> div {
		> div {
			background: #ffffff;
			border-radius: 5px;
			padding: 10px;
			width: 100%;
		}
	}
}
.title {
	font-size: 14px;
	color: $subTextColor;
	margin-top: 16px;
	font-family: PingFang SC, PingFang SC;
	padding-left: 5px;
	width: 100%;
}

.info {
	display: flex;
	align-items: center;
	cursor: pointer;
	div {
		margin-left: 8px;
		font-size: 15px;
		color: $primaryTextColor;
	}
	img {
		width: 48px;
		height: 48px;
		border-radius: 6px;
	}
}

.personnel {
	margin-top: 12px;
	font-size: 12px;
	.num {
		display: flex;
		justify-content: space-between;
		.renNum {
			font-size: 12px;
			color: $subTextColor;
			cursor: pointer;
		}
	}
	.people-config {
		padding: 12px 10px 2px 10px;
		display: flex;
		// justify-content: space-between;
		> div {
			div {
				width: 30px;
				height: 30px;
				line-height: 28px;
				text-align: center;
				border-radius: 50%;
				margin-bottom: 5px;
			}
			span {
				font-size: 10px;
				color: $textColor;
				display: inline-block;
				min-width: 28px;
				text-align: center;
			}
		}
		.user {
			text-align: center;
			width: 40px;
			margin-right: 17px;
			div {
				background: var(--brand-6);
				color: #fff;
				font-size: 12px;
				font-family: PingFang SC, PingFang SC;
			}
			.people_img {
				width: 30px;
				height: 30px;
				border-radius: 50%;
			}
			span {
				width: 40px;
				margin-top: 5px;
				display: inline-block;
				overflow: hidden; /* 确保超出容器的内容被裁剪 */
				white-space: nowrap; /* 确保文本在一行内显示 */
				text-overflow: ellipsis; /* 超出部分显示省略号 */
			}
		}
		.add {
			cursor: pointer;
			div {
				border: 1px solid $borderColor;
				color: $borderColor;
				font-size: 20px;
			}
		}
	}
}
.names {
	margin-top: 6px;
	padding: 5px !important;
}

.config {
	margin-top: 12px;
	padding: 8px 10px;
	> div {
		display: flex;
		justify-content: space-between;
		font-size: 14px;
		color: $textColor;
		font-family: PingFang SC, PingFang SC;
		padding: 8px 0;
		border-bottom: 1px solid #f0f0f0;
		span {
			font-size: 12px;
			color: $subTextColor;
			cursor: pointer;
		}
	}
}
.delete {
	margin-top: 12px;
	cursor: pointer;
	font-size: 14px;
	color: $textColor;
	padding: 8px 10px;
}
.quit {
	text-align: center;
	color: #ff4d4f;
}

::v-deep .el-input__inner {
	border: 0;
}
</style>
