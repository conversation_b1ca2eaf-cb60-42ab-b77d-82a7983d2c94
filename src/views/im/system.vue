<template>
	<div class="message">
		<div class="message-detail">
			<div class="message-detail-title">系统消息</div>
			<!--  消息模块  -->
			<div
				id="system-detail-con"
				ref="contentEl"
				v-infinite-scroll="scrollBottom"
				class="message-detail-con"
				@scroll="scroll"
			>
				<div v-show="loading" class="load-more">
					<span
						v-for="(text, index) of '加载中...'"
						:key="index"
						:style="{ animationDelay: index * 0.5 + 's' }"
						class="load-more-text"
					>
						{{ text }}
					</span>
				</div>
				<!--  普通聊天消息展示  -->
				<!--@contextmenu="contextmenu"-->
				<div v-for="item of systemMessageList" :key="item.id" class="message-item">
					<FileById
						:more-style="{
							borderRadius: '6px',
							margin: '0'
						}"
						:size="40"
						:value="rentInfo.logoUrl"
					></FileById>
					<TextMessageContentView
						style="flex: 1"
						:is-system="true"
						:extra="item.extra"
						:message="item.content"
					></TextMessageContentView>
				</div>
				<!--  保持消息展示最新的块级元素  -->
				<div id="message-bottom"></div>
			</div>
		</div>
		<!--  消息右键菜单  -->
		<div v-show="menuShow" class="menu" :style="{ top: menuTop + 'px', left: menuLeft + 'px' }">
			<div
				v-for="(item, index) of menuList"
				:key="index"
				class="menu-item"
				@click="handleContext(item.type)"
			>
				{{ item.name }}
			</div>
		</div>
	</div>
</template>

<script>
import TextMessageContentView from '@/wile-fire/ui/main/conversation/message/content/TextMessageContentView.vue';
import { mapGetters } from 'vuex';
import { debounce } from '@/utils';
import { getSystemMessageList } from '@/api/modules/coos';

export default {
	name: 'ImContentSystem',
	components: {
		TextMessageContentView
	},
	props: {
		messageType: {
			type: String,
			default: () => {
				return '';
			}
		}
	},
	data() {
		return {
			systemMessageList: [], // 当前对话框数据
			totalPage: 0, // 总页数
			params: {
				pageSize: 10,
				pageNo: 1,
				keywords: ''
			},
			loading: false,
			/**右键菜单选项*/
			menuLeft: 0,
			menuTop: 0,
			menuShow: false,
			menuIndex: 0, // 右键菜单所在消息索引
			menuList: [
				// { name: '复制', type: 'copy' },
				// { name: '转发', type: 'forward' },
				// { name: '引用', type: 'quote' },
				// { name: '收藏', type: 'collect' },
				// { name: '撤回', type: 'withdraw' },
				{ name: '删除', type: 'delete' }
			],
			initHeight: 100, // 聊天框初始高度
			height: 100, // 变化的高度
			sendCon: '', // 发送内容
			message: [],
			el: null,
			inBottom: false, // 在底部
			needLoadNew: false, // 需要加载新数据
			lock: false //还有没有更多消息，请求锁
		};
	},
	computed: {
		...mapGetters(['rentInfo']),
		contextMenu() {
			return [];
		},
		// 普通消息图片预览，获取全部图片
		srcList() {
			let arr = [];
			this.message.forEach(item => {
				item.list.forEach(detail => {
					if (detail.type === 'image') {
						arr.push(detail.url);
					}
				});
			});
			return arr;
		}
	},
	watch: {
		messageType(newVal) {
			if (newVal === 'system') {
				this.getMessageList(true);
			}
		}
	},
	mounted() {
		window.addEventListener('click', this.caleMenu);
		this.el = document.getElementById('message-bottom');
	},
	beforeDestroy() {
		window.removeEventListener('click', this.caleMenu);
	},
	methods: {
		/**更新列表*/
		update() {
			if (
				this.$route.path !== '/wile-fire/home/<USER>' ||
				this.messageType !== 'system' ||
				this.inBottom
			) {
				this.params.pageNo = 1;
				this.getMessageList();
			}
			// 在当前的会话并且不在底部，说明正在浏览，等触底在加载新数据
			else {
				this.needLoadNew = true;
			}
		},
		/**滚动到底部*/
		scrollBottom: debounce(
			function () {
				if (this.needLoadNew) {
					this.params.pageNo = 1;
					this.getMessageList();
					this.needLoadNew = false;
				}
			},
			500,
			true
		),
		/**滚动加载*/
		scrollLoad() {
			if (this.params.pageNo < this.totalPage) {
				this.params.pageNo += 1;
				this.getMessageList();
			}
		},
		/**获取系统消息列表*/
		getMessageList(toBottom = false) {
			this.loading = true;
			getSystemMessageList(this.params).then(res => {
				this.loading = false;
				if (res.code === 200) {
					this.systemMessageList =
						this.params.pageNo === 1
							? res.result.records
							: res.result.records.concat(this.systemMessageList);
					this.totalPage = res.result.pages;
					if (toBottom) {
						this.toBottom();
					}
				} else {
					this.$message.error(res.message);
				}
			});
		},
		/**监听滚动*/
		scroll: debounce(
			function (e) {
				const container = this.$refs.contentEl; // 获取包含内容的元素
				this.inBottom = container.scrollHeight - 500 < container.clientHeight + container.scrollTop; // 判断元素距离底部多少，以便新消息通知的时候是否立即更新
				// 触顶
				if (container.scrollTop < 10) {
					this.scrollLoad();
				}
			},
			500,
			false
		),
		/**操作右键*/
		handleContext(type) {
			switch (type) {
				case 'delete': {
					console.log('删除');
					break;
				}
				default:
					this.$message.warning('开发中，敬请期待！');
			}
		},
		/**关闭右键菜单*/
		caleMenu() {
			this.menuShow = false;
		},
		/**右键菜单*/
		contextmenu(event, index) {
			this.menuIndex = index;
			// 根据需要进行位置调整等操作
			this.menuLeft = event.clientX;
			this.menuTop = event.clientY;
			this.menuShow = true;
		},
		/**阻止回车默认行为*/
		preventEnter(e) {
			if (!e.shiftKey && e.key === 'Enter') {
				e.preventDefault();
			}
		},
		/**滚动到最底部*/
		toBottom() {
			this.$nextTick(() => {
				this.el.scrollIntoView();
			});
		},
		resize(e) {
			this.initHeight = this.height;
			this.startY = e.clientY;
			document.addEventListener('mousemove', this.handleMouseMove);
			document.addEventListener('mouseup', this.stopSelection);
		},
		handleMouseMove(e) {
			this.moveHeight = this.startY - e.clientY;
			this.height = this.initHeight + this.moveHeight;
		},
		stopSelection() {
			document.removeEventListener('mousemove', this.handleMouseMove);
			document.removeEventListener('mouseup', this.stopSelection);
		}
	}
};
</script>

<style scoped lang="scss">
.message {
	flex: 1;
	background: #f3f5f6;
	height: 100%;
	display: flex;
	flex-direction: column;
	position: relative;
	overflow: hidden;
	&-detail {
		flex: 1;
		background: #f3f5f6;
		display: flex;
		flex-direction: column;
		position: relative;
		overflow: hidden;
		&-title {
			height: 54px;
			padding: 0 23px 0 18px;
			@include flexBox(space-between);
			font-size: 18px;
			font-weight: 800;
			color: $primaryTextColor;
			line-height: 24px;
			border-top: 1px solid #f0f0f0;
			border-bottom: 1px solid #dce3e7;
		}
		&-con {
			padding: 20px 20px 0;
			flex: 1;
			overflow-y: auto;
			@include flexBox(flex-start, flex-start);
			flex-direction: column;
			@include noScrollBar;
			.load-more {
				@include flexBox();
				font-size: 12px;
				width: 100%;
				height: 40px;
				color: #c3c3c3;
				flex-shrink: 0;
				&-text {
					animation: text-loading 1s infinite;
					margin: 0 1px;
				}
			}
		}
	}
	.message-item {
		display: flex;
		align-items: flex-start;
		margin-bottom: 24px;
		width: calc(100% - 60px);
	}
	&-resize {
		height: 4px;
		cursor: ns-resize;
	}
	.menu {
		position: fixed;
		background: #ffffff;
		border-radius: 3px;
		padding: 5px 0;
		width: 70px;
		text-align: center;
		box-shadow: 0px 3px 4px 0px rgba(0, 0, 0, 0.3);
		&-item {
			padding: 8px 0;
			font-size: 12px;
			cursor: default;
			&:hover {
				background: #f0f0f0;
			}
		}
	}
}
</style>
