<template>
	<div class="main_content">
		<div class="menu">
			<div class="menu_title">
				<div>通讯录</div>
				<div>
					<el-tooltip class="item" effect="dark" content="组织管理" placement="right">
						<i
							v-appRole="{
								appCode: getDictionary('应用ID/通讯录'),
								roleCode: 'user:manageOrgAndUser'
							}"
							class="coos-iconfont icon-shezhi1 icon-class"
							@click="goPath"
						></i>
					</el-tooltip>
				</div>
			</div>
			<div v-loading="menuLoading" class="menu_tree">
				<el-menu
					ref="elMenu"
					background-color="#f3f4f6"
					:default-active="defaultActive"
					:default-openeds="defaultOpen"
					:unique-opened="false"
					@select="selectChange"
					@open="handleOpen"
					@close="handleColse"
				>
					<el-menu-item
						v-if="openIm === 'true'"
						index="1"
						class="peopleTitle"
						style="height: 46px; line-height: 46px"
					>
						<svg-icon
							v-if="chooseId === 1"
							icon-class="people-select"
							class="peopleGroup"
						></svg-icon>
						<svg-icon
							v-if="chooseId !== 1"
							icon-class="pepole"
							class="chover1 peopleGroup"
						></svg-icon>
						<svg-icon
							v-if="chooseId !== 1"
							icon-class="people-select"
							class="chover2 peopleGroup"
							style="display: none"
						></svg-icon>
						<span slot="title" style="margin-left: 8px">我的群聊</span>
					</el-menu-item>
					<menuTree :tree-list="treeList" :default-active="defaultActive"></menuTree>
				</el-menu>
			</div>
		</div>
		<div class="content">
			<mygroup v-if="index == 1"></mygroup>
			<designGroup
				v-else
				ref="group"
				:isload="isload"
				:title-info="titleInfo"
				:user-id.sync="userId"
			></designGroup>
		</div>
	</div>
</template>

<script>
import mygroup from './content-info/mygroup.vue';
import designGroup from './content-info/design-group.vue';
import menuTree from './content-info/menuTree.vue';
import { getTreeList, getMemberList } from '@/api/modules/address-book';
import { mapGetters } from 'vuex';
import { getDictionary } from '@/utils/data-dictionary';
export default {
	name: 'Index',
	components: { mygroup, designGroup, menuTree },
	data() {
		return {
			index: '1',
			menuLoading: true,
			treeList: [],
			titleInfo: '',
			isload: false,
			chooseId: '',
			defaultOpen: [],
			defaultActive: '1',
			userId: ''
		};
	},
	computed: {
		...mapGetters(['openIm'])
	},
	watch: {
		$route(newVal) {
			this.getSearch(newVal);
		}
	},
	mounted() {
		this.getTreeInfo();
	},
	methods: {
		getDictionary,
		goPath() {
			this.$router.push('/departments-members?pathType=1');
		},
		// 寻找父级id
		findParentIds(arr, targetId, parentIds = []) {
			for (let i = 0; i < arr.length; i++) {
				const obj = arr[i];
				if (obj.children && obj.children.length > 0) {
					const foundInChildren = this.findParentIds(
						obj.children,
						targetId,
						parentIds.concat(obj.id)
					);
					if (foundInChildren) return foundInChildren;
				}
				if (obj.id === targetId) {
					parentIds.push(obj.id);
					return parentIds;
				}
			}
			return false;
		},
		//获取跳转
		getSearch(newVal) {
			if (newVal.path == '/address-book' && Object.keys(newVal.query).length) {
				const { orgId, userId, departId } = newVal.query;
				this.userId = userId;
				this.handleOpen(orgId);
				if (departId) {
					this.defaultActive = departId;
					const parentIds = this.findParentIds(this.treeList, departId);
					this.selectChange(departId, parentIds);
				}
				this.$router.replace({ path: '/address-book', query: {} });
			}
		},
		getTreeInfo() {
			getTreeList().then(res => {
				if (res.code == 200) {
					this.treeList = res.result;
					this.menuLoading = false;
					this.getSearch(this.$route);

					if (this.openIm === 'false') {
						this.handleOpen(res.result[0].id);
					}
				}
			});
		},
		getOrgTitle(key, val) {
			val.forEach(item => {
				if (item.id == key) {
					this.titleInfo = item.title;
				} else if (Array.isArray(item.children)) {
					item.children.forEach(item1 => {
						if (item1.id == key) {
							this.titleInfo = item.title + '/' + item1.title;
						}
					});
				}
			});
		},

		handleColse(key) {
			this.handleOpen(key);
			let isExist = this.defaultOpen.indexOf(key);
			if (isExist !== -1) {
				this.defaultOpen.splice(isExist, 1);
			}
			// this.chooseId = '';
			// this.$refs.elMenu.activeIndex = null;
		},
		// 遍历title
		forTitle(arrInfo, len, i, val, val1) {
			let count = i;
			arrInfo.forEach(item => {
				if (item.id == val1[count]) {
					this.titleInfo = this.titleInfo + item.title;
					count++;
					if (count <= len) {
						this.titleInfo += '/';
						this.forTitle(item.children, len, count, val, val1);
					}
				}
			});
		},
		findNodeById(node, targetId) {
			// 如果当前节点的 id 与目标 id 相等，则返回该节点
			if (node.id === targetId) {
				return node;
			}

			// 遍历当前节点的所有子节点
			for (let child of node.children || []) {
				// 对每个子节点递归调用 findNodeById
				const found = this.findNodeById(child, targetId);
				if (found) {
					// 如果在子节点中找到了匹配的目标 id，则返回找到的节点
					return found;
				}
			}

			// 如果没有找到匹配的目标 id，则返回 null
			return null;
		},
		handleOpen(key, val1) {
			let obj = {
				id: '',
				children: this.treeList
			};
			let newObj = this.findNodeById(obj, key);
			if (newObj.dataType == 'depart') {
				//判断是否是部门
				this.defaultActive = key;
				this.chooseId = key;
				this.isload = true;
				this.index = key;
				this.titleInfo = '';
				this.getOrgTitle(key, this.treeList);
				getMemberList({ depId: key, pageSize: -1 })
					.then(res => {
						if (res.code == 200) {
							this.$refs.group.depId = key;
							this.$refs.group.listInfo = res.result.records;
							this.$refs.group.total = res.result.records.length; // res.result.total;
							this.isload = false;
						}
					})
					.catch(error => {
						console.log(error);
					});
				return;
			} else {
				this.defaultActive = key;
				this.chooseId = key;
				this.$nextTick(() => {
					this.$refs.group.orgId = key;
				});
				let isExist = this.defaultOpen.indexOf(key);
				if (isExist == -1) {
					this.defaultOpen.push(key);
				}
				this.index = key;
				this.isload = true;
				// this.treeList.forEach(item => {
				// 	if (item.id == key) {
				// 		this.titleInfo = item.title;
				// 	}
				// });
				this.getOrgTitle(key, this.treeList);
				getMemberList({ orgId: key, pageSize: -1 }).then(res => {
					this.$refs.group.listInfo = res.result.records;
					this.$nextTick(() => {
						this.$refs.group.scrollTo();
					});
					this.$refs.group.total = res.result.records.length; // res.result.total;
					this.isload = false;
				});
			}
		},
		selectChange(val, val1) {
			this.defaultActive = val;
			this.chooseId = val;
			this.isload = true;
			this.index = val;
			this.titleInfo = '';
			let len = val1.length - 1;
			let i = 0;
			this.forTitle(this.treeList, len, i, val, val1);
			getMemberList({ depId: val, pageSize: -1 })
				.then(res => {
					if (res.code == 200) {
						this.$refs.group.depId = val;
						this.$refs.group.listInfo = res.result.records;
						this.$refs.group.total = res.result.records.length; // res.result.total;
						this.isload = false;
					}
				})
				.catch(error => {
					console.log(error);
				});
		}
	}
};
</script>

<style scoped lang="scss">
.main_content {
	display: flex;
	height: 100%;
	.menu {
		width: 283px;
		background: #f3f4f6;
		overflow-y: auto;
		@include noScrollBar;
		.menu_title {
			display: flex;
			width: 100%;
			justify-content: space-between;
			font-size: 18px;
			font-weight: 800;
			color: $primaryTextColor;
			line-height: 24px;
			padding: 16px 13px 0 13px;
		}
		.menu_tree {
			margin-top: 8px;
			padding: 0px 10px;
			.peopleTitle {
				.peopleGroup {
					width: 14px;
					height: 14px;
					margin-left: 2px;
					margin-left: 4px;
				}
			}
			.el-menu {
				border-right: none;
			}
			//主菜单悬浮样式
			::v-deep .el-submenu__title:hover {
				color: var(--brand-6) !important;
				background: var(--brand-1) !important;
				border-radius: 6px;
			}

			//子菜单悬浮样式
			::v-deep .el-menu-item:hover {
				color: var(--brand-6) !important;
				background: var(--brand-1) !important;
				border-radius: 6px;
			}
			.el-menu-item:hover .chover1 {
				display: none;
			}
			.el-menu-item:hover .chover2 {
				display: inline-block !important;
			}
			::v-deep .el-submenu__title {
				color: $primaryTextColor;
			}
			.el-menu-item.is-active {
				background-color: var(--brand-1) !important; //选中背景色
				color: var(--brand-6); //选中颜色
				border-radius: 6px;
			}

			::v-deep .el-submenu__title {
				// padding-left: 11px !important;
				height: 45px;
				line-height: 45px;
			}
			.el-submenu .el-menu-item {
				height: 46px;
				line-height: 46px;
			}

			.firstName {
				display: inline-block;
				background: #3088ff;
				border-radius: 12px;
				width: 25px;
				height: 25px;
				text-align: center;
				line-height: 25px;
				color: #fff;
			}
		}
	}
	.content {
		width: calc(100% - 283px);
		background: #fff;
		border: 1px solid #f0f0f0;
	}
}
.icon-class {
	cursor: pointer;
}
</style>
