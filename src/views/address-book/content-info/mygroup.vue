<template>
	<div class="main_content">
		<div class="choose_info">
			<div class="chooseItems">
				<div
					class="choose_item"
					:class="{ clicked: current === 0 }"
					style="margin-right: 24px"
					@click="changeChoose(0)"
				>
					我创建的群组
				</div>
				<div class="choose_item" :class="{ clicked: current === 1 }" @click="changeChoose(1)">
					我加入的群组
				</div>
			</div>
			<div class="creat_group" @click="creatGroup">
				<svg-icon icon-class="user-groupw" style="width: 16px; height: 16px"></svg-icon>
				<span style="margin-left: 4px">创建群聊</span>
			</div>
		</div>
		<div
			v-loading="loading"
			v-infinite-scroll="scrollBottom"
			class="tableInfo"
			:infinite-scroll-disabled="disabled"
		>
			<el-row v-for="(item, index) in list" :key="index" :gutter="24" class="tableItem">
				<el-col :span="12" class="item_left">
					<img v-if="item.portrait" class="itemImag" :src="item.portrait" @click="chat(item.gid)" />
					<div v-else class="itemImag item-block" @click="chat(item.gid)">
						{{ item.name.slice(0, 2) }}
					</div>
					<div class="itemInfo">
						<div class="infoTitle">{{ item.name }}</div>
						<div class="peopleNum">
							<!-- <i class="el-icon-user" style="color: #5e586e;"></i> -->
							<svg-icon icon-class="user-group" style="width: 16px; height: 12px"></svg-icon>
							<span class="numInfo">{{ item.memberCount }}人</span>
						</div>
					</div>
				</el-col>
				<el-col :span="12" class="item_right">
					<span class="createTitle">创建人：</span>
					<span class="createName">{{ item.ownerName }}</span>
				</el-col>
			</el-row>
			<!-- <el-empty :image="assetsUrl + '/common/img/no-data.png'"></el-empty> -->
			<BasicEmpty :loading="loading" :data="list" name="no-data" class="empty" />
		</div>
		<el-dialog
			title="创建群组"
			:visible.sync="newAdd"
			class="desk-el-custom-dialog"
			:modal-append-to-body="false"
			top="0"
			width="40%"
		>
			<el-form ref="ruleForm" class="form" :model="form" :rules="rules">
				<el-form-item class="form-item" prop="title">
					<div class="form-item-title">
						群名称
						<span class="require">*</span>
					</div>
					<el-input v-model="form.title" class="title-input" placeholder="请输入群名称"></el-input>
				</el-form-item>
				<el-form-item class="form-item" prop="avatarUrl">
					<div class="form-item-title">
						群头像
						<span class="require">*</span>
					</div>
					<div class="UploadFile">
						<UploadFile
							v-model="form.avatarUrl"
							:accept="['image']"
							:multiple="false"
							data-type="url"
						></UploadFile>
					</div>
				</el-form-item>
				<div class="form-item">
					<div class="form-item-title">管理员</div>
					<div class="tabs">
						<div class="tab">
							<div class="tab-icon">{{ userInfo.realname.slice(-1) }}</div>
							<div class="tab-name">{{ userInfo.realname }}</div>
						</div>
					</div>
					<!--						<div v-if="leaderArr.length === 0" class="placeholder">设置管理员</div>-->
					<!--						<div v-else class="tabs">-->
					<!--							<div v-for="(item, index) of leaderArr" :key="index" class="tab">-->
					<!--								<div class="tab-icon">{{ item.title.slice(0, 1) }}</div>-->
					<!--								<div class="tab-name">{{ item.title }}</div>-->
					<!--								<i class="coos-iconfont icon-guanbi1 tab-close" @click.stop="delLeader(index)"></i>-->
					<!--							</div>-->
					<!--						</div>-->
					<!--						<i class="coos-iconfont icon-nav-bottom select-icon"></i>-->
				</div>
				<div class="form-item">
					<div class="form-item-title">群成员</div>
					<div class="select-person" @click="openSelectPerson('member')">
						<div v-if="memberArr.length === 0" class="placeholder">设置群成员</div>
						<div v-else class="tabs">
							<div v-for="(item, index) of memberArr" :key="index" class="tab">
								<div class="tab-icon">{{ item.title.slice(0, 1) }}</div>
								<div class="tab-name">{{ item.title }}</div>
								<i class="coos-iconfont icon-guanbi1 tab-close" @click.stop="delMember(index)"></i>
							</div>
						</div>
						<i class="coos-iconfont icon-nav-bottom select-icon"></i>
					</div>
				</div>
			</el-form>
			<span slot="footer" class="dialog-footer">
				<el-button @click="cale">取 消</el-button>
				<el-button v-loading="sureLoading" type="primary" @click="saveEditAvatar">确 定</el-button>
			</span>
			<orgPersonnelDialog
				:title="type === 'leader' ? '请选择管理员' : '请选择群成员'"
				:init-values="type === 'leader' ? leaderArr : memberArr"
				:visible="visible"
				need-all-data
				disable-all
				:data-source="dataSource"
				@sure="surePerson"
				@close="closePerson"
			></orgPersonnelDialog>
		</el-dialog>
	</div>
</template>

<script>
import { createMyGroup, getMyGroup } from '@/api/modules/address-book';
import orgPersonnelDialog from '@/components/org-personnel-dialog';
import { assetsUrl } from '@/config';
import Conversation from '@/wile-fire/wfc/model/conversation';
import ConversationType from '@/wile-fire/wfc/model/conversationType';
import store from '@/wile-fire/store';
import { mapGetters } from 'vuex';
import { debounce } from '@/utils';
export default {
	components: {
		orgPersonnelDialog
	},
	data() {
		return {
			assetsUrl,
			loading: false,
			dataSource: ['user'],
			visible: false,
			type: '', //leader|member
			form: {
				title: '',
				avatarUrl: assetsUrl + '/common/img/default-group.png' // 头像
			},
			leaderArr: [],
			memberArr: [],
			current: 0,
			// 请求参数
			params: {
				pageNo: 1,
				pageSize: 7,
				optType: 'my' // join
			},
			allPage: 0,
			list: [], // 数据
			newAdd: false, // 弹窗显隐
			sureLoading: false, // 异步确定
			rules: {
				title: [{ required: true, message: '请填写群聊名称', trigger: ['change', 'blur'] }],
				avatarUrl: [{ required: true, message: '请选择群聊头像', trigger: ['change', 'blur'] }]
			}
		};
	},
	computed: {
		...mapGetters(['userInfo']),
		disabled() {
			return this.loading || this.allPage <= this.params.pageNo;
		}
	},
	watch: {
		'form.avatarUrl'(val) {
			this.$refs.ruleForm.validate();
		}
	},
	mounted() {
		this.getData();
	},
	methods: {
		scrollBottom: debounce(
			function () {
				this.params.pageNo += 1;
				this.getData();
			},
			500,
			true
		),
		/**进入群聊*/
		chat(gid) {
			let conversation = new Conversation(ConversationType.Group, gid, 0);
			// 跳转过去没有刷新会话消息数据，没有查到原因，时间紧迫，延时处理
			setTimeout(() => {
				store.setCurrentConversation(conversation);
			}, 1000);
			this.$router.replace('/wile-fire/home/<USER>');
		},
		/**打开人员选择*/
		openSelectPerson(type) {
			this.type = type;
			this.visible = true;
		},
		/**确定人员选择*/
		surePerson(arr) {
			if (this.type === 'leader') {
				this.leaderArr = arr;
			} else {
				this.memberArr = arr;
			}
			this.visible = false;
		},
		/**关闭人员选择*/
		closePerson() {
			this.visible = false;
		},
		/**关闭弹窗*/
		cale() {
			this.newAdd = false;
		},
		/**新增*/
		saveEditAvatar() {
			if (this.sureLoading) return;
			this.$refs.ruleForm.validate(valid => {
				if (valid) {
					if (this.memberArr.length < 2) {
						this.$message.error('至少选择两名群成员！');
						return;
					}
					let params = {
						name: this.form.title,
						portrait: this.form.avatarUrl,
						members: this.memberArr.map(item => item.id)
						// managers: this.leaderArr.map(item => item.id)
					};
					this.sureLoading = true;
					createMyGroup(params).then(res => {
						this.sureLoading = false;
						if (res.code === 200) {
							this.newAdd = false;
							this.list = [];
							this.params.pageNo = 1;
							this.getData();
							this.$message.success('创建成功');
						} else {
							this.$message.error(res.message);
						}
					});
				}
			});
		},
		/**创建群聊*/
		creatGroup() {
			this.newAdd = true;
		},
		/**获取数据*/
		getData() {
			this.loading = true;
			getMyGroup(this.params).then(res => {
				this.loading = false;
				if (res.code === 200) {
					this.list = this.list.concat(res.result.records);
					this.allPage = res.result.pages || 0;
				} else {
					this.$message.error(res.message);
				}
			});
		},
		/**切换选择*/
		changeChoose(type) {
			this.current = type;
			this.params.optType = type === 0 ? 'my' : 'join';
			this.list = [];
			this.params.pageNo = 1;
			this.getData();
		},
		/**删除管理员*/
		delLeader(index) {
			this.leaderArr.splice(index, 1);
		},
		/**删除群成员*/
		delMember(index) {
			this.memberArr.splice(index, 1);
		}
	}
};
</script>

<style lang="scss" scoped>
.main_content {
	background: #ffffff;
	height: 100%;
	display: flex;
	flex-direction: column;
	.choose_info {
		width: 100%;
		height: 54px;
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20px 19px 0px 20px;
		border: 1px solid #f0f0f0;
		.chooseItems {
			display: flex;
			.choose_item {
				height: 34px;
				font-size: 14px;
				font-weight: 400;
				line-height: 22px;
				color: $textColor;
				cursor: pointer;
			}
			.choose_item.clicked {
				font-weight: 600;
				border-bottom: 3px solid var(--brand-6);
				color: var(--brand-6);
			}
		}
		.creat_group {
			cursor: pointer;
			margin-bottom: 11px;
			width: 100px;
			height: 32px;
			background: var(--brand-6);
			border-radius: 6px 6px 6px 6px;
			display: flex;
			justify-content: center;
			align-items: center;
			color: #fff;
			font-size: 14px;
		}
	}
	.tableInfo {
		margin-top: 11px;
		padding: 0px 20px;
		overflow-y: auto;
		height: 100%;
		display: flex;
		flex-direction: column;
		.empty {
			flex: 1;
			display: flex;
			align-items: center;
			justify-content: center;
		}
		.tableItem {
			padding: 16px 0px;
			border-bottom: 1px solid #f0f0f0;
			display: flex;
			align-items: center;
			.item_left {
				display: flex;
				.itemImag {
					width: 48px;
					height: 48px;
					border-radius: 9px;
					margin-right: 8px;
				}
				.item-block {
					background: #6c58e7;
					line-height: 48px;
					color: #ffffff;
					font-size: 14px;
					text-align: center;
				}
				.itemInfo {
					flex: 1;
					overflow: hidden;
					.infoTitle {
						line-height: 22px;
						color: $primaryTextColor;
						font-weight: 800;
						font-size: 14px;
						margin-bottom: 8px;
						@include aLineEllipse;
					}
					.peopleNum {
						display: flex;
						align-items: center;
						.numInfo {
							line-height: 20px;
							color: var(--brand-6);
							font-weight: 400;
							font-size: 12px;
							margin-left: 5px;
						}
					}
				}
			}
			.item_right {
				font-weight: 400;
				line-height: 22px;
				font-size: 14px;
				.createTitle {
					color: $subTextColor;
				}
				.createName {
					color: $primaryTextColor;
				}
			}
		}
	}
}
.form {
	padding: 0 11px;
	&-item {
		margin-bottom: 20px;
		&-title {
			font-weight: 400;
			font-size: 14px;
			color: $textColor;
			line-height: 22px;
			margin-bottom: 8px;
			.require {
				color: #ff4d4f;
				margin-left: 5px;
			}
		}
		.title-input {
			height: 40px;
			background: #ffffff;
			border-radius: 6px;
			::v-deep .el-input__inner {
				border-radius: 6px;
				height: 100%;
			}
		}
		.UploadFile {
			margin: -8px;
		}
		.select-person {
			width: 100%;
			height: 40px;
			background: #ffffff;
			border-radius: 6px;
			border: 1px solid $borderColor;
			padding: 0 19px;
			position: relative;
			display: flex;
			align-items: center;
			cursor: pointer;
			.select-icon {
				font-size: 16px;
				color: $holderTextColor;
				position: absolute;
				right: 7px;
				top: 12px;
			}
			.placeholder {
				font-size: 14px;
				font-weight: 400;
				color: $holderTextColor;
				line-height: 22px;
			}
		}
		.tabs {
			margin-right: 20px;
			display: flex;
			align-items: center;
			overflow: hidden;
			.tab {
				padding: 4px 10px;
				background: #f3f4f6;
				border-radius: 3px;
				margin-right: 8px;
				flex-shrink: 0;
				@include flexBox(flex-start);
				&-icon {
					width: 16px;
					height: 16px;
					background: var(--brand-6);
					border-radius: 3px;
					@include flexBox();
					font-size: 10px;
					font-weight: 400;
					color: #ffffff;
					line-height: 10px;
				}
				&-name {
					font-size: 12px;
					font-weight: 400;
					color: $textColor;
					line-height: 20px;
					margin: 0 4px;
				}
				&-close {
					font-size: 12px;
					line-height: 12px;
					cursor: pointer;
				}
			}
		}
	}
}
</style>
