<template>
	<div>
		<template v-for="menu in treeList">
			<!-- 如果当前有子菜单，则显示 el-submenu ，在el-subment 里调用 递归组件 -->
			<el-submenu
				v-if="menu.children && menu.children.length > 0"
				:key="menu.id"
				:index="menu.id"
				:class="[`submenu-children${childrenIndex}`, { isDianji: defaultActive == menu.id }]"
			>
				<template slot="title">
					<i v-if="menu.dataType === 'depart'" class="coos-iconfont icon-cengji"></i>
					<i v-if="menu.dataType === 'org'" class="coos-iconfont icon-shuzhuangtu1 icon"></i>
					<el-tooltip
						:open-delay="1000"
						class="item"
						effect="dark"
						:content="menu.title"
						placement="top"
					>
						<span class="spanTitle" style="margin-left: 8px">{{ menu.title }}</span>
					</el-tooltip>
				</template>
				<!-- 调用自身  此处是重点-->
				<MenuTree :tree-list="menu.children" :children-index="childrenIndex + 1"></MenuTree>
			</el-submenu>
			<!-- 如果没有子菜单，则显示当前内容 -->
			<el-menu-item v-else :key="menu.id" :index="menu.id">
				<i v-if="menu.dataType === 'depart'" class="coos-iconfont icon-cengji"></i>
				<i v-if="menu.dataType === 'org'" class="coos-iconfont icon-shuzhuangtu1 icon"></i>
				<el-tooltip
					:open-delay="1000"
					class="item"
					effect="dark"
					:content="menu.title"
					placement="top"
				>
					<span class="spanTitle" style="margin-left: 3px; margin-right: 8px">
						{{ menu.title }}
					</span>
				</el-tooltip>
			</el-menu-item>
		</template>
	</div>
</template>

<script>
export default {
	name: 'MenuTree',
	props: {
		treeList: {
			type: Array,
			default: () => []
		},
		childrenIndex: {
			type: Number,
			default: 0
		},
		defaultActive: {
			type: String,
			default: ''
		}
	},
	watch: {
		defaultActive(newVal) {
			console.log(newVal, 'new');
		}
	}
};
</script>

<style lang="scss" scoped>
::v-deep .isDianji > .el-submenu__title {
	color: var(--brand-6) !important;
	background: var(--brand-1) !important;
	border-radius: 6px;
}
//主菜单悬浮样式
::v-deep .el-submenu__title:hover {
	color: var(--brand-6) !important;
	background: var(--brand-1) !important;
	border-radius: 6px;
}
.el-submenu__title:hover .thover1 {
	display: none;
}
.el-submenu__title:hover .thover2 {
	display: inline-block !important;
}

//子菜单悬浮样式
::v-deep .el-menu-item:hover {
	color: var(--brand-6) !important;
	background: var(--brand-1) !important;
	border-radius: 6px;
}
.el-menu-item:hover .chover1 {
	display: none;
}
.el-menu-item:hover .chover2 {
	display: inline-block !important;
}
::v-deep .el-submenu__title {
	color: $primaryTextColor;
}
.el-menu-item.is-active {
	background-color: var(--brand-1) !important; //选中背景色
	color: var(--brand-6); //选中颜色
	border-radius: 6px;
}

::v-deep .el-submenu__title {
	// padding-left: 11px !important;
	height: 45px;

	line-height: 45px;
	padding-right: 30px;
	@include aLineEllipse;
}
::v-deep .submenu-children1 {
	.el-submenu__title {
		padding-right: 40px;
	}
}
::v-deep .submenu-children2 {
	.el-submenu__title {
		padding-right: 40px;
	}
}
::v-deep .submenu-children3 {
	.el-submenu__title {
		padding-right: 40px;
	}
}
::v-deep .submenu-children4 {
	.el-submenu__title {
		padding-right: 40px;
	}
}
::v-deep .submenu-children5 {
	.el-submenu__title {
		padding-right: 40px;
	}
}
.el-submenu .el-menu-item {
	height: 46px;
	line-height: 46px;
	@include aLineEllipse;
}
.el-submenu {
}
</style>
