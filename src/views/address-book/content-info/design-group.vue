<template>
	<div v-loading="isload" class="main_content" @click="cancleUserId">
		<div class="contentTitle">
			<div class="titleText">{{ titleInfo }}({{ total }})</div>
			<div class="titleSearch">
				<el-input
					v-model="searchInfo"
					placeholder="输入搜索"
					prefix-icon="el-icon-search"
					@keydown.enter.native="handleSearch"
				></el-input>
				<el-button type="primary" class="searchBut" @click="handleSearch">搜索</el-button>
			</div>
		</div>
		<div v-if="listInfo.length > 0" ref="scrollContainer" class="contentList">
			<div>
				<div
					v-for="item in listInfo"
					:key="item.id"
					class="listItem"
					:class="{ whichOne: userId == item.userId }"
					:data-id="item.userId"
				>
					<div class="nameInfo" @click="detailInfo(item)">
						<el-image
							v-if="item.avatarUrl"
							style="width: 36px; height: 36px; border-radius: 6px"
							:src="item.avatarUrl"
						></el-image>
						<div v-else class="image">
							{{ item.realname ? item.realname.charAt(item.realname.length - 1) : '' }}
						</div>

						<div class="name">{{ item.realname }}</div>
					</div>
					<el-popover
						v-if="item.identityList && item.identityList.length"
						placement="bottom-start"
						popper-class="depart-popper"
						trigger="hover"
					>
						<div>
							<div v-for="(event, index) in item.identityList" :key="index" class="depart-item">
								{{ setDecText(event) }}
							</div>
						</div>
						<div slot="reference" class="depart">
							<span>{{ setTableText(item) }}</span>
						</div>
					</el-popover>
					<div v-else class="depart">
						<span>{{ setTableText(item) }}</span>
					</div>
					<div class="phone">{{ item.phone }}</div>
					<div class="response">{{ item.email }}</div>
					<div class="send-message">
						<!--						<el-tooltip class="item" effect="dark" content="发送消息" placement="left">-->
						<i
							v-if="openIm === 'true' && item.userId !== userInfo.id"
							class="coos-iconfont icon-xiaoxi send-icon"
							@click="toImSendMessage(item.userId)"
						></i>
						<!--						</el-tooltip>-->
					</div>
				</div>
			</div>
		</div>
		<div v-else class="isEmpty">
			<!-- <el-empty class="desk-el-empty" description="暂无数据"></el-empty> -->
			<BasicEmpty :loading="isload" :data="listInfo" name="no-data" />
		</div>
		<el-dialog :visible.sync="dialogVisible" width="330px" top="25vh">
			<div v-loading="loadDialog">
				<img
					:src="require(`@/assets/${rentThem}/address_book/dialogImg.png`)"
					class="dialog-image"
					alt=""
				/>
				<div class="topInfo">
					<el-image
						v-if="deInfo.avatarUrl"
						style="width: 64px; height: 64px; border-radius: 12px"
						:src="deInfo.avatarUrl"
					></el-image>
					<div v-else class="image">
						{{ deInfo.realname ? deInfo.realname.charAt(deInfo.realname.length - 1) : '' }}
					</div>
					<div class="nameInfo">
						<div class="name">
							<div style="margin-right: 2px">{{ deInfo.realname }}</div>
							<svg-icon
								v-if="deInfo.sex == 2"
								icon-class="women"
								style="width: 14px; height: 13px"
							></svg-icon>
							<svg-icon
								v-else-if="deInfo.sex == 1"
								icon-class="man"
								style="width: 14px; height: 13px"
							></svg-icon>
						</div>
						<!--						<div class="position">{{ deInfo.labelTitles }}</div>-->
						<!-- <div class="statusInfo">
						<div class="status">
							<svg-icon icon-class="outpeople" style="width: 14px; height: 13px"></svg-icon>
							<div>差旅中</div>
						</div>
						<div class="stopTime">截至2023年9月1日</div>
					</div> -->
					</div>
				</div>
				<!-- <div class="notesInfo" style="margin: ">
				<el-input placeholder="设置备注和描述"></el-input>
			</div> -->
				<div class="contactInfo">
					<div class="contactItem">
						<i
							class="coos-iconfont icon-29youxiangfill"
							style="width: 16px; height: 16px; color: #aab3c6; line-height: 24px"
						></i>
						<span>{{ deInfo.email }}</span>
					</div>
					<div class="contactItem">
						<!--						<svg-icon icon-class="phone" style="width: 16px; height: 16px"></svg-icon>-->
						<i
							class="coos-iconfont icon-24gf-telephone"
							style="width: 16px; height: 16px; color: #aab3c6; line-height: 20px"
						></i>
						<span>{{ deInfo.phone }}</span>
					</div>
					<div class="contactItem">
						<i
							class="coos-iconfont icon-bumenguanli"
							style="width: 16px; height: 16px; color: #aab3c6; line-height: 20px"
						></i>
						<div class="dept-box">
							<div v-for="(event, index) in deInfo.identityList" :key="index" class="dept-box-item">
								{{ setDecText(event) }}
								<div class="dept-box-item-icon">
									<div v-if="event.isMain" class="dept-box-item-icon-box">
										<i class="coos-iconfont icon-shoucang2 dept-box-item-icon-class"></i>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div
					v-if="openIm === 'true' && deInfo.userId !== userInfo.id"
					v-loading="sendLoading"
					class="sendInfo"
					@click="toImSendMessage(deInfo.userId)"
				>
					发送消息
				</div>
			</div>
		</el-dialog>
	</div>
</template>

<script>
import { getMemberList } from '@/api/modules/address-book';
import ConversationType from '@/wile-fire/wfc/model/conversationType';
import store from '@/wile-fire/store';
import { mapGetters } from 'vuex';
import { createConversation } from '@/utils/wile-fire-login';
export default {
	props: {
		titleInfo: {
			type: String,
			default: ''
		},
		isload: {
			type: Boolean,
			default: false
		},
		userId: {
			type: String,
			default: ''
		}
	},

	data() {
		return {
			sendLoading: false, // 发送消息异步状态
			dialogVisible: false,
			listInfo: [],
			searchInfo: '',
			total: 0,
			depId: '',
			orgId: '',
			deInfo: {},
			loadDialog: false
		};
	},
	computed: {
		...mapGetters(['openIm', 'userInfo'])
	},
	watch: {
		// userId() {
		// 	this.scrollTo();
		// }
	},
	mounted() {
		// this.getMemberInfo();
		// setTimeout(() => {
		// 	this.scrollTo();
		// }, 1000);
	},
	methods: {
		/**去IM发送消息*/
		toImSendMessage(userId) {
			this.dialogVisible = false;
			let conversation = createConversation(ConversationType.Single, userId, 0);
			// 跳转过去没有刷新会话消息数据，没有查到原因，时间紧迫，延时处理
			setTimeout(() => {
				store.setCurrentConversation(conversation);
			}, 1000);
			if (this.$router.currentRoute.path !== '/wile-fire/home/<USER>') {
				this.$router.replace('/wile-fire/home/<USER>');
			}
		},
		scrollTo() {
			const targetElement = this.$refs.scrollContainer
				? this.$refs.scrollContainer.querySelector(`[data-id='${this.userId}']`)
				: null;
			if (targetElement) {
				const container = this.$refs.scrollContainer;
				const offsetTop = targetElement.offsetTop;
				container.scrollTop = offsetTop - container.offsetTop;
			}
		},
		cancleUserId() {
			if (this.isload) return;
			this.$emit('update:userId', '');
		},
		setDecText(event) {
			if (!event) return '';
			const getPrefixedValue = (value, prefix = '-') => (value ? `${prefix}${value}` : '');
			const org = event.orgShort || event.orgName || '';
			const depart = getPrefixedValue(event.departShort || event.departName);
			const postName = getPrefixedValue(event.postName);
			const jobTitle =
				getPrefixedValue(event.jobTitle, '(').replace(/^-/, '') + (event.jobTitle ? ')' : '');
			return `${org}${depart}${postName}${jobTitle}`;
		},
		setTableText(item) {
			let str = '';
			if (item && item.identityList && item.identityList.length) {
				const mainEvent = item.identityList.find(event => event.isMain);
				if (mainEvent) {
					str = mainEvent.jobTitle || mainEvent.departShort || mainEvent.departName || '';
				}
			}
			return str;
		},
		detailInfo(val) {
			if (this.loadDialog) return;
			this.loadDialog = true;
			// getDetailInfo(id).then(res => {
			// 	if (res.code == 200) {
			// 		this.deInfo = res.result;
			// 		this.loadDialog = false;
			// 	}
			// });
			this.deInfo = val;
			this.loadDialog = false;
			this.dialogVisible = true;
		},
		handleSearch() {
			getMemberList({
				keywords: this.searchInfo,
				depId: this.depId,
				orgId: this.orgId,
				pageSize: -1
			}).then(res => {
				this.listInfo = res.result.records;
				this.total = res.result.records.length; // res.result.total;
			});
		}
	}
};
</script>

<style lang="scss" scoped>
.main_content {
	display: flex;
	flex-direction: column;
	.contentTitle {
		height: 54px;
		padding: 10px 20px 12px 21px;
		display: flex;
		justify-content: space-between;
		align-items: center;
		border-bottom: 1px solid #f0f0f0;
		width: 100%;
		.titleText {
			line-height: 22px;
			color: $primaryTextColor;
			font-weight: 800;
			font-size: 16px;
		}
		.titleSearch {
			display: flex;
			::v-deep .el-input__inner {
				width: 196px;
				border: 1px solid $borderColor;
				border-radius: 6px 6px 6px 6px;
				height: 32px;
			}
			.searchBut {
				margin-left: 9px;
			}
		}
	}
	.contentList::-webkit-scrollbar {
		display: none;
	}
	.isEmpty {
		height: calc(100% - 120px);
		display: flex;
		align-items: center;
		margin: 0 auto;
	}
	.contentList {
		overflow-y: auto;
		margin-top: 10px;

		.listItem {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 16px 8px;
			margin: 0px 20px;
			border-bottom: 1px solid #f0f0f0;
			font-size: 14px;
			line-height: 22px;
			font-weight: 400;
			color: $primaryTextColor;
			&:hover .send-icon {
				display: block; // 悬停时显示
			}
			.nameInfo {
				display: flex;
				align-items: center;
				width: 160px;
				min-width: 160px;
				cursor: pointer;
				.name {
					margin-left: 8px;
					font-weight: 500;
				}
				.image {
					width: 36px;
					border-radius: 6px;
					background: var(--brand-6);
					height: 36px;
					line-height: 36px;
					text-align: center;
					font-size: 14px;
					color: #ffffff;
				}
			}

			.depart {
				//width: 153px;
				flex: 1;
				cursor: pointer;
				display: flex;
				min-width: 400px;
				width: 400px;
				max-width: 400px;
				height: 22px;
				display: -webkit-box;
				-webkit-box-orient: vertical;
				-webkit-line-clamp: 1;
				overflow: hidden;
				text-overflow: ellipsis;
				// justify-content: center;
			}
			.phone {
				//width: 153px;
				flex: 1;
				padding-left: 26px;
				display: flex;
				min-width: 180px;
				// justify-content: center;
			}
			.response {
				margin-right: 40px;
				min-width: 200px;
				width: 260px;
			}
		}
		.whichOne {
			background: #f5f7fa;
			border-radius: 6px;
		}
	}
	::v-deep .el-dialog__body {
		padding: 0px 17px 35px 12px;
		margin-top: 7px;
		.el-input__inner {
			width: 294px;
			height: 32px;
			border-radius: 6px;
			background: #f2f7fc;
		}
	}
	::v-deep .el-dialog {
		border-radius: 16px;
		background-size: 330px 370px;
		.el-dialog__headerbtn {
			position: absolute;
			z-index: 999;
		}
	}
	.topInfo {
		position: relative;
		z-index: 666;
		width: 257px;
		padding: 9px 6px 6px 7px;
		display: flex;
		.image {
			width: 64px;
			height: 64px;
			background: var(--brand-6);
			border-radius: 12px;
			text-align: center;
			line-height: 64px;
			font-size: 32px;
			color: #ffffff;
			font-weight: 800;
		}
		.nameInfo {
			display: flex;
			flex-direction: column;
			justify-content: center;
			margin-left: 9px;
			line-height: 22px;
			.name {
				font-weight: 800;
				font-size: 16px;
				color: $primaryTextColor;
				display: flex;
				align-items: center;
			}
			.position {
				font-size: 12px;
				font-weight: 500;
				color: $textColor;
			}
			.statusInfo {
				display: flex;
				align-items: center;
				.status {
					line-height: 20px;
					color: #26c88e;
					font-size: 10px;
					display: flex;
					align-items: center;
					background: linear-gradient(180deg, #d7f5e3 0%, rgba(206, 255, 226, 0) 100%);
					border-radius: 23px 23px 23px 23px;
					padding: 0px 5px;
				}
				.stopTime {
					margin-left: 11px;
					font-size: 12px;
					color: $subTextColor;
					line-height: 20px;
				}
			}
		}
	}
	.notesInfo {
		margin: 5px 0px 12px 6px;
	}
	.contactInfo {
		position: relative;
		z-index: 666;
		padding-left: 6px;
		.contactItem {
			display: flex;
			margin-top: 12px;
			color: $primaryTextColor;
			line-height: 22px;
			font-size: 14px;
			span {
				margin-left: 7px;
			}
		}
	}
	.sendInfo {
		position: relative;
		z-index: 666;
		cursor: pointer;
		margin: 18px auto 0;
		text-align: center;
		line-height: 32px;
		font-size: 14px;
		color: #ffffff;
		width: 243px;
		height: 32px;
		background: var(--brand-6);
		border-radius: 6px 6px 6px 6px;
	}
}

.depart-item {
	height: 20px;
	font-family: PingFang SC, PingFang SC;
	font-weight: 400;
	font-size: 12px;
	color: #2f446b;
	line-height: 20px;
	margin-top: 12px;
}
.send-message {
	min-width: 80px;
	width: 80px;
	min-height: 30px;
}
.send-icon {
	display: none;
	color: #b9bdc9;
	cursor: pointer;
}
.dept-box {
	padding-left: 7px;
	.dept-box-item {
		margin-bottom: 8px;
		font-family: PingFang SC, PingFang SC;
		font-weight: 400;
		font-size: 12px;
		color: $primaryTextColor;
		line-height: 20px;
		display: flex;
		&-icon {
			width: 18px;
			height: 18px;
			&-box {
				width: 18px;
				height: 18px;
				background: #fff6df;
				border-radius: 3px 3px 3px 3px;
				display: flex;
				justify-content: center;
				align-items: center;
			}
			&-class {
				color: #faad14;
				font-size: 8px;
			}
		}
	}
}
.dialog-image {
	position: absolute;
	top: 0;
	left: 0;
	width: 330px;
	height: 303px;
	bottom: 0;
	z-index: 1;
}
</style>
<style>
.depart-popper {
	padding-top: 0 !important;
	border-radius: 12px;
}
</style>
