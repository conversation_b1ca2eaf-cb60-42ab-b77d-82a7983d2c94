<template>
	<div id="boxView" class="diagram">
		<iframe
			v-if="iframeUrl"
			id="diagramFrame"
			name="diagramFrame"
			frameborder="0"
			width="100%"
			scrolling="no"
			:src="iframeUrl"
		></iframe>
	</div>
</template>

<script>
import { getSmartBiToken } from '@/api/modules/smartbi.js';
export default {
	data() {
		return {
			iframeUrl: ''
		};
	},
	created() {
		this.getInfo();
	},
	methods: {
		getInfo() {
			// this.iframeUrl = 'http://www.baidu.com';
			getSmartBiToken().then(res => {
				console.log('res', res);
				if (res.code === 200) {
					const query = this.$route.query;
					// openType=1内部打开 openType=2外部打开
					if (query.openType && query.openType == 1) {
						this.iframeUrl = `${res.result}&targetPath=${query.targetPath}?resid=${query.resid}`;
					} else {
						window.open(`${res.result}&targetPath=${query.targetPath}?resid=${query.resid}`);
					}
				}
			});
		}
	}
};
</script>

<style lang="scss" scoped>
.diagram {
	width: 100%;
	#diagramFrame {
		height: calc(100vh - 105px);
		//@include scroll();
	}
}
</style>
