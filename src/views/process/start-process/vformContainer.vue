<template>
	<div v-loading="floading" style="min-height: 100px">
		<div v-if="aiContent.length > 0" class="aiContent">
			<el-popover placement="bottom" width="500" trigger="hover">
				<table class="flow-table">
					<tr>
						<th>序号</th>
						<th>问题</th>
						<th>是否通过</th>
					</tr>
					<tr v-for="(que, i) in aiContent" :key="i" class="flow-value">
						<td width="48px">
							{{ Number(i) + 1 }}
						</td>
						<td
							:style="{
								color: '#3A546C',
								textAlign: 'left',
								padding: '10px 20px'
							}"
						>
							{{ que.question }}
						</td>
						<td
							:style="{
								color: que.answer === 'true' ? '#2ca65e' : '#d60000',
								textAlign: 'center',
								width: '148px'
							}"
						>
							<i :class="[que.answer === 'true' ? 'el-icon-success' : 'el-icon-error']"></i>
						</td>
					</tr>
				</table>
				<div slot="reference" class="aitip" :class="[isPass ? '' : 'no-pass']">
					<span
						class="coos-avatar"
						:style="{
							background: `url(${require(`@/assets/${rentThem}/coos/coos-avatar.png`)}) `
						}"
					></span>
					<span
						:style="{
							color: isPass ? '#00A870' : '#E34D59',
							marginRight: '8px'
						}"
					>
						当前流程前置AI自动审批{{ isPass ? '已' : '未' }}通过
					</span>
					<span style="cursor: pointer">查看详情 ></span>
				</div>
			</el-popover>
		</div>
		<!--    业务表单Start    -->
		<template v-if="isSlot">
			<energyComponents
				v-if="isComponents"
				:components-name="componentsType"
				:business-key="businessKey"
			></energyComponents>
			<!--    内部动态组件渲染S    -->
			<components
				:is="innerComponentsType"
				v-else-if="innerComponentsType"
				ref="innerComponents"
				:route-query="routeQuery"
			></components>
			<!--    内部动态组件渲染E    -->
			<!--    外部表单渲染E    -->
			<webviewBox
				v-else
				ref="iframePage"
				:need-socket="false"
				:view-url="pcUrl"
				width="100%"
				style="height: calc(100vh - 195px); border: 0; position: relative; z-index: 2"
			></webviewBox>
			<!--    外部表单渲染E    -->
		</template>
		<!--    业务表单End    -->
		<!--    动态表单Start    -->
		<template v-else>
			<v-form-render
				v-if="formJson.widgetList"
				:key="formKey"
				ref="vFormRef"
				class="desk-el-active-form"
				:option-data="optionData"
				:form-json="formJson"
				:form-data="formData"
				:base-url="config.baseUrl"
				:disabled="isView"
				v-bind="$attrs"
				@viewUploadFile="viewUploadFile"
			></v-form-render>
		</template>
		<!--    动态表单End    -->
	</div>
</template>
<script>
import { getNodeConfig } from '@/api/modules/flow-design';
import { getDesignById, getFormData, getFormDetail } from '@/api/modules/form-page';
import { getQueryObject, previewFile } from '@/utils';
import config from '@/config';
import { mapGetters } from 'vuex';
import webviewBox from '@/components/webview-box/index.vue';
export default {
	name: 'VformContainer',
	components: {
		webviewBox,
		// 集成的新能源业务板块的待办表单
		// energyComponents: () => {
		// 	if (process.env.VUE_APP_ENERGY === 'true') {
		// 		return import('@/third-party/energy/components/energy-components/index.vue');
		// 	} else {
		// 		return 'div';
		// 	}
		// },
		// 集成的omip供应商业务板块的待办表单
		qualifiedComponents: () => {
			if (process.env.VUE_APP_OMIP === 'true') {
				return import(
					'@/third-party/omip/views/supplier-management/qualified-suppliers/content.vue'
				);
			} else {
				return 'div';
			}
		},
		// 集成的omip专家管理业务板块的待办表单
		expertComponents: () => {
			if (process.env.VUE_APP_OMIP === 'true') {
				return import('@/third-party/omip/views/expert/info/wait-index');
			} else {
				return 'div';
			}
		},
		// 集成的omip采购管理板块的待办表单
		purchaseComponents: () => {
			if (process.env.VUE_APP_OMIP === 'true') {
				return import('@/third-party/omip/views/procurement/wait-index');
			} else {
				return 'div';
			}
		},
		// 集成的招投标管理板块的待办表单
		bidComponents: () => {
			if (process.env.VUE_APP_OMIP === 'true') {
				return import('@/third-party/omip/views/bid/wait-index');
			} else {
				return 'div';
			}
		},
		// 集成的印章业务板块的待办表单
		seal: () => {
			if (process.env.VUE_APP_COOS === 'true') {
				return import('@/third-party/coos/views/seal/apply-popup/content.vue');
			} else {
				return 'div';
			}
		},
		// 集成的用车业务板块的待办表单
		car: () => {
			if (process.env.VUE_APP_COOS === 'true') {
				return import('@/third-party/coos/views/vehicle-management/apply-popup/content.vue');
			} else {
				return 'div';
			}
		},
		// 集成的驾驶员备案业务板块的待办表单
		driver: () => {
			if (process.env.VUE_APP_COOS === 'true') {
				return import(
					'@/third-party/coos/views/vehicle-management/driver-registration/components/content.vue'
				);
			} else {
				return 'div';
			}
		},
		// 集成的科研管理模块待办表单
		researchComponents: () => {
			if (process.env.VUE_APP_RESEARCH === 'true') {
				return import('@/third-party/research/waitIndex.vue');
			} else {
				return 'div';
			}
		},
		// 绩效管理待办表单
		performanceComponents: () => {
			if (process.env.VUE_APP_PERFORMANCE === 'true') {
				return import('@/third-party/performance/wait-index');
			} else {
				return 'div';
			}
		}
	},
	props: {
		nodeId: {
			type: String,
			default: ''
		},
		processDefinitionId: {
			type: String,
			default: ''
		},
		businessKey: {
			type: String,
			default: ''
		},
		formVars: {
			type: Object,
			default: () => {
				return {};
			}
		},
		isView: {
			type: Boolean,
			default: false
		},
		needReload: {
			type: Boolean,
			default: false
		},
		aiContent: {
			type: Array,
			default: () => {
				return [];
			}
		}
	},
	data() {
		return {
			config,
			innerComponentsType: '', // 内部渲染组件的名称
			routeQuery: {}, // 内部组件渲染的参数
			floading: true,
			optionData: {},
			formJson: {},
			formData: {},
			formState: false,
			isSlot: false,
			pcUrl: '',
			formType: 1,
			formDataId: '',
			formId: '',
			formDataIdKey: '',
			isComponents: false,
			componentsType: null,
			isPass: true
		};
	},
	computed: {
		...mapGetters(['userInfo']),
		formKey: function () {
			return Math.random();
		}
	},
	watch: {
		processDefinitionId: function (val) {
			if (val) {
				this.getNodeConfig();
			}
		},
		aiContent: function (val) {
			if (val.length > 0) {
				val.map(v => {
					if (v.answer === 'false') {
						this.isPass = false;
					}
				});
			}
		}
	},
	mounted() {},
	methods: {
		/**通知表单渲染区提交数据*/
		sendMessage(actionsType) {
			let message = {
				content: 'submitForm',
				type: 'submit',
				auditType: actionsType,
				handleUser: this.userInfo
			};
			if (this.$refs.iframePage) {
				this.$refs.iframePage.sendMessage(message);
			}
			if (this.$refs.innerComponents && this.$refs.innerComponents.handleMessage) {
				this.$refs.innerComponents.handleMessage(message);
			}
		},
		getVFormData() {
			return new Promise((resolve, reject) => {
				this.$refs.vFormRef
					.getFormData()
					.then(formData => {
						resolve(formData);
						// reject();
					})
					.catch(() => {
						reject('error');
					});
			});
		},
		async getNodeConfig() {
			const nodeConfig = await getNodeConfig({
				nodeId: this.nodeId,
				processDefinitionId: this.processDefinitionId
			});
			this.floading = false;
			// 获取指定节点的属性
			if (nodeConfig.result === null) {
				this.$message({
					message: '获取表单属性失败!',
					type: 'error'
				});
				return;
			}
			if (nodeConfig.code === 200) {
				const formProperties = { ...nodeConfig.result.node.properties.formProperties };
				const designById = await getDesignById(formProperties.templateId);
				if (designById.code === 500) {
					this.$message({
						message: designById.message,
						type: 'error'
					});
					return;
				}
				this.formId = designById.result.formId;
				this.$emit('setFormId', this.formId);
				this.floading = false;

				//表单的详情
				const formDetail = await getFormDetail(designById.result.formId);
				this.formType = formDetail?.result?.type || 1;
				if (designById.code === 200) {
					const { result } = designById;
					this.isSlot = result.isSlot;
					if (result.isSlot) {
						const { slotAccessInfo } = result;
						const url = slotAccessInfo.PC;
						const random = Math.random();
						const str = `businessKey=${this.businessKey}&random=${random}`;
						let urls = '';
						if (url.indexOf('?') > -1) {
							urls = url + '&' + str;
						} else {
							urls = url + '?' + str;
						}
						let urlParams = getQueryObject(urls);
						this.isComponents = false; // 重置渲染方式
						this.componentsType = null;
						this.innerComponentsType = ''; // 重置渲染方式
						// 规定好这个前缀的，为内部组件渲染
						if (/^innerComponents/.test(url)) {
							this.routeQuery = urlParams; // 组件参数
							if (!this.needReload) {
								delete this.routeQuery.flowFlag;
							}
							this.innerComponentsType = urlParams.componentsType; // 内部组件名称
						} else if (urlParams.components) {
							this.isComponents = true;
							this.componentsType = urlParams.components;
						}
						// if (process.env.NODE_ENV === 'development') {
						// 	this.pcUrl = '/coos_desk/#/' + urls.split('/#/')[1];
						// } else {
						// 	this.pcUrl = urls;
						// }
						this.pcUrl = urls;
					} else {
						const formJson = JSON.parse(result.configJson);
						this.formJson = { ...formJson };
						if (this.formType === 1) {
							if (this.businessKey) {
								this.getFormData(formProperties.privileges, formJson);
							}
						} else {
							// 通过视图的id formProperties.templateId
							// {"customForm_视图id_dataId","数据id"}

							let keys = `customForm_${formProperties.templateId}_dataId`;
							let dataId = this.formVars[keys];
							this.formDataIdKey = keys;
							this.formDataId = dataId;
							if (dataId) {
								// 去查询数据
								this.getFormData(formProperties.privileges, formJson, dataId);
							}
						}
					}
				}
			}
		},
		getFormData(privileges, formJson, dataId) {
			// 表单的数据 是通过 当前待办的businessKey来查询
			getFormData(dataId ? dataId : this.businessKey).then(res => {
				if (res.code === 200) {
					this.formData = { ...res.result.data };
					const disableWidgets = [],
						enableWidgets = [],
						hideWidgets = [],
						showWidgets = [];
					privileges.map(v => {
						if (v.displayable) {
							//显示 默认false 默认显示
							hideWidgets.push(v.id);
						} else {
							// 显示
							showWidgets.push(v.id);
						}
						if (v.readable) {
							// 只读
							disableWidgets.push(v.id);
						}
						if (v.writable) {
							// 可编辑
							enableWidgets.push(v.id);
						}
					});
					// console.log('disableWidgets:', disableWidgets);
					// console.log('enableWidgets:', enableWidgets);
					this.formJson = { ...formJson };

					this.$nextTick(() => {
						setTimeout(() => {
							if (this.$refs.vFormRef) {
								this.$refs.vFormRef.setFormData(this.formData);
								this.$refs.vFormRef.disableWidgets(disableWidgets);
								this.$refs.vFormRef.enableWidgets(enableWidgets);
								this.$refs.vFormRef.hideWidgets(hideWidgets);
								this.$refs.vFormRef.showWidgets(showWidgets);
							}
						}, 0);
					});
					this.floading = false;
				}
			});
		},
		viewUploadFile(link) {
			if (link?.fileUrl) {
				previewFile(link.fileUrl);
			} else {
				this.$message.info('文件有误,无法预览');
			}
		}
	}
};
</script>
<style lang="scss" scoped>
.flow-table {
	width: 100%;
	border-spacing: 0;
	th {
		background: #edf2f6;
		padding: 10px 0;
		text-align: center;
		border: 1px solid #cfe1ef;
		font-weight: bold;
	}
	td {
		padding: 10px 8px;
		border: 1px solid #cfe1ef;
	}
}
.aiContent {
	display: flex;
	justify-content: flex-end;
	margin-bottom: 15px;
}
.aitip {
	height: 48px;
	line-height: 48px;
	background: rgba(0, 168, 112, 0.1);
	border-radius: 6px 6px 6px 6px;
	padding: 0 16px;
	display: flex;
	align-items: center;
	grid-gap: 8px;
}
.coos-avatar {
	background-size: 20px 20px !important;
	height: 20px;
	width: 20px;
	border-radius: 4px;
}
</style>
