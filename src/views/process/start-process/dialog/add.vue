<template>
	<div>
		<el-dialog
			top="10vh"
			:title="title"
			:visible.sync="addVisible"
			:before-close="handleClose"
			class="custom-el-dialog-footer"
		>
			<div class="labelInfo">
				<div class="labelPic"></div>
				<div class="labelTitle">我的流程</div>
			</div>
			<el-form
				ref="formRef"
				class="formStyle"
				:rules="rules"
				:model="form"
				label-position="right"
				label-width="60"
			>
				<el-form-item label="发起人" prop="createUser">
					<el-input v-model="form.createUser" placeholder="请输入发起人"></el-input>
				</el-form-item>
				<el-form-item label="流程名称" prop="flowName">
					<el-input v-model="form.flowName" placeholder="请输入流程名称"></el-input>
				</el-form-item>
			</el-form>
			<div slot="footer" class="dialog-footer">
				<el-button style="border-radius: 6px" @click="cancleForm()">取 消</el-button>
				<el-button style="border-radius: 6px" type="primary" @click="handleSure">确 定</el-button>
			</div>
		</el-dialog>
	</div>
</template>

<script>
import { addStartProcess, updateStartProcess } from '@/api/modules/process';

export default {
	props: {
		isShow: {
			type: Number,
			default: 1
		},
		title: {
			type: String,
			default: '添加用户菜单'
		}
	},
	data() {
		return {
			addVisible: false,
			form: {
				createUser: '',
				groupId: '',
				tenantId: ''
			},
			rules: {
				createUser: [{ required: true, message: '请输入发起人', trigger: 'blur' }],
				flowName: [{ required: true, message: '请输入流程名称', trigger: 'blur' }]
			},
			isPrompt: false //关闭前是否提示
		};
	},
	computed: {},
	methods: {
		handleClose() {
			if (this.isPrompt) {
				this.$confirm('你还未保存信息,是否关闭?', '提示', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					type: 'warning'
				})
					.then(() => {
						this.$refs.formRef.resetFields();
						this.addVisible = false;
					})
					.catch(() => {});
			} else {
				this.$refs.formRef.resetFields();
				this.addVisible = false;
				this.isPrompt = false;
			}
		},
		cancleForm() {
			this.handleClose();
		},
		handleSure() {
			this.$refs.formRef.validate(valid => {
				if (valid) {
					if (this.isShow == 3) {
						addStartProcess({ ...this.form }).then(res => {
							if (res.code == 200) {
								this.$message({
									message: '添加成功',
									type: 'success'
								});
								this.$emit('getTableData');
								// 重制表单
								this.$refs.formRef.resetFields();
								this.addVisible = false;
							} else if (res.code == 500) {
								this.$message({
									message: `${res.message}`,
									type: 'error'
								});
							}
						});
					} else if (this.isShow == 2) {
						updateStartProcess({ ...this.form }).then(res => {
							if (res.code == 200) {
								this.$message({
									message: '编辑成功',
									type: 'success'
								});
								this.$emit('getTableData');
								this.$refs.formRef.resetFields();
								this.addVisible = false;
							}
						});
					}
				} else {
					console.log('error submit!!');
					return false;
				}
			});
		}
	}
};
</script>

<style lang="scss" scoped>
// 核心思路：通过css隐藏小圆圈，并扩大小圆圈的显示范围,然后设置visibility为hidden
//::v-deep .custom-el-dialog-footer .el-dialog__body {
//	padding: 0px 32px 16px 32px;
//}

.labelInfo {
	font-size: 16px;
	font-weight: 800;
	line-height: 22px;
	color: $primaryTextColor;
	margin-bottom: 16px;
	display: flex;
	align-items: center;
	.labelPic {
		width: 4px;
		height: 18px;
		background: var(--brand-6);
		border-radius: 20px 20px 20px 20px;
		margin-right: 3px;
	}
}
::v-deep .el-form-item__label {
	line-height: 22px;
	color: $textColor;
	font-size: 14px;
	font-weight: 400;
	margin-bottom: 8px;
}
.formStyle {
	height: 100%;
	@include scrollBar;
	.formItemSwitch {
		display: flex;
		margin-top: 2px;
	}
}
.tableStyle {
	.elTable {
		border-radius: 6px;
	}
}
::v-deep .el-input__inner {
	border-radius: $borderRadius;
	height: 40px;
	border: 1px solid $borderColor;
}
::v-deep .el-textarea__inner {
	border-radius: $borderRadius;
}

.addbut {
	display: flex;
	justify-content: flex-end;
	margin-bottom: 9px;
	.butItem {
		border-radius: $borderRadius;
		border: 1px solid var(--brand-6);
		color: var(--brand-6);
	}
}
.nameArea {
	display: flex;
	align-items: center;
	padding: 0px 4px;
	height: 24px;
	background: #f2f3f5;
	border-radius: 3px;
	.Image {
		width: 18px;
		height: 18px;
		border-radius: 3px;
	}
	.nameLast {
		width: 18px;
		height: 18px;
		background: var(--brand-6);
		border-radius: 3px;
		line-height: 10px;
		color: #ffffff;
		font-size: 10px;
		display: flex;
		justify-content: center;
		align-items: center;
	}
	.nameAll {
		margin-left: 2px;
		font-size: 14px;
		line-height: 20px;
		color: $primaryTextColor;
	}
}
</style>
<style>
.el-cascader-panel .el-radio {
	position: absolute;
	width: 100%;
	height: 100%;
	/* // border: 1px solid #f00; */
	right: -10px;
}

.el-cascader-panel .el-radio .el-radio__input {
	visibility: hidden;
}
</style>
