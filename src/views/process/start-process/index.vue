<template>
	<div v-loading="pageloading" class="main_content">
		<div class="task-box">
			<div
				class="left"
				:style="{
					width: `${
						isView || !pageloading || !ccTask || activeName == 'flowinfo' || activeName == 'records'
							? 100
							: 66.6667
					}%`,
					overflowX: 'hidden'
				}"
			>
				<div class="tab-box">
					<div v-if="taskRecored.length != 0 && isView && canRevoke" class="revoke-btn">
						<el-button type="primary" class="submit-btn" :loading="loading" @click="revokeFlow">
							撤回
						</el-button>
					</div>
					<el-tabs v-model="activeName" type="card" @tab-click="tabClick">
						<el-tab-pane label="表单信息" name="forminfo">
							<vform-container
								ref="vfContainer"
								:node-id="nodeId"
								:need-reload="needReload"
								:business-key="businessKey"
								:is-view="isView"
								:process-definition-id="processDefinitionId"
								:form-vars="formVars"
								:ai-content="aiContent"
								@setFormId="setFormId"
							></vform-container>
						</el-tab-pane>
						<el-tab-pane label="流转记录" name="records">
							<flow-records
								v-if="formId"
								:process-instance-id="flowParams.processInstanceId"
								:form-id="formId"
								@showAIcontent="showAIcontent"
								@showFlowRecordList="showFlowRecordList"
							></flow-records>
						</el-tab-pane>
						<el-tab-pane label="流程图" name="flowinfo">
							<!-- <div id="flowContainer" ref="flowContainer" class="flowContainer"></div> -->
							<flow-container
								v-if="formId"
								ref="flowContainer"
								:flow-handle-params="flowHandleParams"
								:form-id="formId"
							></flow-container>
						</el-tab-pane>
					</el-tabs>
				</div>
			</div>
			<div v-if="ccTask" class="right" style="width: 33.3333%">
				<el-form ref="ccTaskForm" label-width="80px" :model="ccTaskForm" :rules="ccTaskRules">
					<el-form-item label="批注" prop="ccTaskComment">
						<el-input
							v-model="ccTaskForm.ccTaskComment"
							type="textarea"
							maxlength="500"
							:show-word-limit="true"
							:autosize="{ minRows: 3, maxRows: 5 }"
						></el-input>
					</el-form-item>
				</el-form>
			</div>
			<div
				class="right"
				:style="{
					width: `${
						isView || activeName === 'flowinfo' || activeName === 'records' ? 0 : 33.3333
					}%`,
					display: `${
						isView || ccTask || activeName === 'flowinfo' || activeName === 'records' ? 'none' : ''
					}`
				}"
			>
				<el-form ref="forms" :model="forms" label-width="110px" :rules="rules">
					<el-form-item label="执行操作">
						<el-radio-group v-model="actionsType" @change="actionChange">
							<el-radio label="execute">通过</el-radio>
							<el-radio v-if="actionNames['isRefusalStart']" label="isRefusalStart">驳回</el-radio>
							<el-radio v-if="actionNames['isCounterSign']" label="isCounterSign">加签</el-radio>
							<el-radio v-if="actionNames['isDelegate']" label="isDelegate">委派</el-radio>
							<el-radio v-if="actionNames['isReassign']" label="isReassign">转办</el-radio>
							<!-- <el-radio v-if="actionNames['isSkipNode']" label="isSkipNode">
								驳回至指定节点
							</el-radio> -->
							<el-radio v-if="actionNames['isInterrupt']" label="isInterrupt">终止</el-radio>
							<!-- <el-radio v-for="(item, index) in radiosSetting" :key="index" :label="item.value">
								{{ item.label }}
							</el-radio> -->
						</el-radio-group>
					</el-form-item>
					<div v-if="actionsType !== 'isRefusalStart'">
						<el-form-item :label="actionsType !== 'execute' ? '原因' : '审核意见'" prop="comment">
							<el-input
								v-model="forms.comment"
								type="textarea"
								maxlength="500"
								:show-word-limit="true"
								:autosize="{ minRows: 3, maxRows: 5 }"
								@input="commentChange"
							></el-input>
						</el-form-item>
						<el-form-item v-if="parallelHandler.length > 1" label="上一步处理人">
							<el-radio-group>
								<el-radio v-for="(item, index) in parallelHandler" :key="index" :label="item.value">
									{{ item.label }}
								</el-radio>
							</el-radio-group>
						</el-form-item>
						<div v-if="actionsType === 'execute'">
							<div v-for="(next, i) in nextHandler" :key="i">
								<div v-if="setShow(next)">
									<el-form-item :label="next.nodeName" :prop="'vars.' + next.varName">
										<div v-if="next.multiple && next.selectValue && next.selectValue.length">
											<el-checkbox-group :key="keys" v-model="forms.vars[next.varName]">
												<div style="display: flex; align-items: center">
													<div
														v-for="(item, index) in next.selectValue"
														:key="index"
														style="margin-right: 10px"
														:disabled="setDisabled(next)"
														@click.prevent="assigneeChange(item, next)"
													>
														<el-checkbox :label="item.value" :disabled="setDisabled(next)">
															{{ item.label }}
														</el-checkbox>
													</div>
												</div>
											</el-checkbox-group>
										</div>
										<div v-else-if="next.selectValue && next.selectValue.length">
											<el-radio-group v-model="forms.vars[next.varName]">
												<div style="display: flex; align-items: center">
													<div
														v-for="(item, index) in next.selectValue"
														:key="index"
														style="margin-right: 10px"
														@click.prevent="assigneeChange(item, next)"
													>
														<el-radio :label="item.value">
															{{ item.label }}
														</el-radio>
													</div>
												</div>
											</el-radio-group>
										</div>
										<div v-if="next.formsError" class="el-form-item__error">
											{{ next.nodeName }}不能为空
										</div>
									</el-form-item>
								</div>
								<!-- 这一段代码重复了 -->
								<!-- <template v-for="(next, i) in nextHandler">
									<el-form-item
										v-if="next.selectValue && next.selectValue.length > 1"
										:key="i"
										:label="next.nodeName"
										prop="assignee"
									>
										<template v-if="multiple">
											<el-checkbox-group v-model="assignee" @change="assigneeChange">
												<el-checkbox
													v-for="(item, index) in next.selectValue"
													:key="index"
													:label="item.value"
												>
													{{ item.label }}
												</el-checkbox>
											</el-checkbox-group>
										</template>
										<template v-else>
											<el-radio-group v-model="assignee" @change="assigneeChange">
												<el-radio
													v-for="(item, index) in next.selectValue"
													:key="index"
													:label="item.value"
												>
													{{ item.label }}
												</el-radio>
											</el-radio-group>
										</template>
									</el-form-item>
								</template> -->
							</div>
						</div>
						<el-form-item v-if="actionNames['isManualCondition']" label="请选择节点" prop="nodeId">
							<el-select
								v-model="forms.nodeId"
								plcaeholder="请选择处理节点"
								:clearable="true"
								@change="nextNodesChange"
							>
								<el-option
									v-for="(item, index) in nextNodes"
									:key="index"
									:label="item.nodeName"
									:value="item.nodeId"
								></el-option>
							</el-select>
						</el-form-item>
					</div>
					<div v-else>
						<el-form-item label="驳回原因" prop="reason">
							<el-input
								v-model="forms.reason"
								type="textarea"
								maxlength="500"
								:show-word-limit="true"
								:autosize="{ minRows: 3, maxRows: 5 }"
								@input="isRefusalStartCommentChange"
							></el-input>
						</el-form-item>
					</div>

					<el-form-item
						v-if="nextType === 'selectNextDept' && actionsType === 'execute'"
						label="办理部门"
						prop="deptCandidate"
						class="choose-item"
					>
						<div class="flex-box">
							<el-button plain icon="el-icon-plus" @click="showDeptSelect()">
								选择办理部门
							</el-button>
							<div class="choose-tags">
								<el-tag
									v-for="(item, index) in deptSelect"
									:key="index"
									size="small"
									closable
									@close="deptHandleClose(index)"
								>
									{{ item.title }}
								</el-tag>
							</div>
						</div>
					</el-form-item>
					<el-form-item
						v-if="nextType === 'selectNextUser' && actionsType === 'execute'"
						label="办理人员"
						prop="assignee"
						class="choose-item"
					>
						<div class="flex-box">
							<el-button plain icon="el-icon-plus" @click="showUserSelect()">
								选择办理人员
							</el-button>
							<div class="choose-tags">
								<el-tag
									v-for="(item, index) in nextUsers"
									:key="index"
									size="small"
									closable
									@close="nextUserDel(index)"
								>
									{{ item.title }}
								</el-tag>
							</div>
						</div>
					</el-form-item>
					<el-form-item
						v-if="isUserLabel && actionsType === 'execute' && userLabelList.length > 0"
						label="选择成员"
						prop="userLabel"
					>
						<el-select
							v-model="forms.userLabel"
							plcaeholder="请选择成员"
							:clearable="true"
							@change="userLabelChange"
						>
							<el-option
								v-for="(item, index) in userLabelList"
								:key="index"
								:label="item.label"
								:value="item.value"
							></el-option>
						</el-select>
					</el-form-item>

					<el-form-item
						v-if="actionNames['isCc'] && actionsType === 'execute'"
						label="抄送至"
						class="choose-item"
					>
						<div class="flex-box">
							<el-button plain icon="el-icon-plus" @click="showSelect()">请选择抄送人</el-button>
							<div class="choose-tags">
								<el-tag
									v-for="(item, index) in ccSelect"
									:key="index"
									size="small"
									closable
									@close="handleClose(index)"
								>
									{{ item.title }}
								</el-tag>
							</div>
						</div>
					</el-form-item>
					<div
						v-if="
							actionsType === 'isReassign' ||
							actionsType === 'isCounterSign' ||
							actionsType === 'isDelegate'
						"
					>
						<el-form-item label="选择人员" prop="userId" class="choose-item">
							<!-- <el-input
							placeholder="请选择"
							:readonly="true"
							suffix-icon="el-icon-user"
							@click.native="showZbSelect"
						></el-input> -->
							<el-button plain icon="el-icon-plus" @click="showZbSelect()">请选择人员</el-button>
							<div class="choose-tags">
								<el-tag
									v-if="zbUserSelect.length > 0"
									size="small"
									closable
									@close="handleZbClose()"
								>
									{{ zbUserSelect[0].title }}
								</el-tag>
							</div>
						</el-form-item>
					</div>
					<el-form-item
						v-if="actionsType === 'isRefusalStart'"
						label="驳回至"
						prop="newActivityId"
						class="choose-item"
					>
						<el-radio-group v-model="rejectType" @change="actionChange">
							<el-radio label="default">发起节点</el-radio>
							<el-radio label="targetNode" :disabled="!actionNames['isSkipNode']">
								指定节点
							</el-radio>
						</el-radio-group>
					</el-form-item>
					<el-form-item
						v-if="
							actionNames['isSkipNode'] &&
							actionsType === 'isRefusalStart' &&
							rejectType === 'targetNode'
						"
						label="选择节点"
						prop="newActivityId"
						class="choose-item"
					>
						<el-select v-model="forms.newActivityId" plcaeholder="请选择节点" :clearable="true">
							<el-option
								v-for="(item, index) in beforeNodes"
								:key="index"
								:label="item.nodeName"
								:value="item.nodeId"
							></el-option>
						</el-select>
					</el-form-item>

					<!-- <el-form-item v-if="actionsType == 'execute'" label="下一节点">
						{{ nextNodeName }}
					</el-form-item> -->
					<el-form-item v-if="actionsType === 'execute' && assignees" label="下一待办人">
						<div style="color: rgb(24, 144, 255)">{{ assignees }}</div>
					</el-form-item>

					<el-form-item
						v-if="actionsType === 'execute' && assigneeList.length > 0"
						label="节点名称"
					>
						<div v-for="(agn, i) in assigneeList" :key="i" class="assignees-box">
							<div class="node-name">
								<span>
									{{ agn.nodeName }}-{{ agn.nodeType == 3 ? '抄送人：' : '待办人：' }}
									<span style="color: var(--brand-6)">{{ agn.assignees || '-' }}</span>
								</span>
							</div>
						</div>
					</el-form-item>
				</el-form>
				<div
					v-if="ccTask || (activeName === 'forminfo' && handleBtnPlace === 'mix')"
					class="task-actions task-actions-mix"
				>
					<el-button
						v-if="!isView && !ccTask"
						type="primary"
						:loading="loading"
						class="submit-btn"
						@click="save"
					>
						提交
					</el-button>
					<el-button v-if="ccTask" type="primary" :loading="loading" @click="isRead">
						已阅
					</el-button>
					<el-button @click="cancel">取消</el-button>
				</div>
			</div>
		</div>
		<div
			v-if="ccTask || (activeName === 'forminfo' && handleBtnPlace === 'bottom')"
			class="task-actions"
		>
			<el-button @click="cancel">取消</el-button>
			<el-button v-if="ccTask" type="primary" :loading="loading" @click="isRead">已阅</el-button>
			<el-button v-if="!isView && !ccTask" type="primary" :loading="loading" @click="save">
				提交
			</el-button>
		</div>
		<orgPersonnelDialog
			title="选择"
			:visible="chooseVisiable"
			:init-params="initParams"
			:init-values="ccSelect"
			:data-source="userDetaSource"
			:need-all-data="true"
			:can-select-depart="false"
			:disable-all="true"
			:limit-org="false"
			@sure="sure"
			@close="close"
		/>
		<orgPersonnelDialog
			title="选择人员"
			:visible="chooseZb"
			:init-params="initParams"
			:init-values="zbUserSelect"
			:data-source="userDetaSource"
			:need-all-data="true"
			:is-radio="true"
			:disable-all="true"
			:can-select-depart="false"
			:limit-org="false"
			@sure="zbSure"
			@close="zbClose"
		/>
		<orgPersonnelDialog
			title="选择部门"
			:visible="chooseDeptVisiable"
			:init-params="initParams"
			:init-values="deptSelect"
			:data-source="detaSource"
			:need-all-data="true"
			:is-radio="true"
			:disable-all="true"
			:can-select-depart="canSelectDepart"
			:limit-org="false"
			:include-users="false"
			@sure="deptSure"
			@close="deptClose"
		/>
		<orgPersonnelDialog
			title="选择人员"
			:visible="showNextUsers"
			:init-params="initParams"
			:init-values="nextUsers"
			:data-source="userDetaSource"
			:need-all-data="true"
			:is-radio="!multiple"
			:disable-all="true"
			:can-select-depart="false"
			:limit-org="false"
			@sure="nextUserSure"
			@close="nextUserClose"
		/>
		<el-dialog top="5vh" title="查看子流程" :visible.sync="showChildFlow" width="90%">
			<child-flow v-if="showChildFlow"></child-flow>
		</el-dialog>
	</div>
</template>

<script>
import { mapGetters, mapState } from 'vuex';
import {
	backStart,
	counterSign,
	delegateTask,
	execute,
	getBeforeNode,
	getNextHandler,
	getNextNode,
	getNodeConfig,
	getNodeSettings,
	interrupt,
	noticeExecute,
	reassign,
	resolveTask,
	selectByTaskInfo,
	selectList,
	selectListHis,
	setHaveRead,
	skipNode,
	undoneTask
} from '@/api/modules/flow-design';
import {
	getHisVariables,
	getVariables,
	saveFormData,
	updateFormData
} from '@/api/modules/form-page';
import orgPersonnelDialog from '@/components/org-personnel-dialog';
import childFlow from './child-flow.vue';
import VformContainer from './vformContainer';
import FlowContainer from './flowContainer';
import FlowRecords from '@/components/logic-flow/flow-records';

export default {
	components: { orgPersonnelDialog, childFlow, FlowRecords, VformContainer, FlowContainer },
	props: {
		flowHandleParams: {
			type: Object,
			default: () => {
				return {};
			}
		},
		needReload: {
			type: Boolean,
			default: false
		},
		customerTaskMenu: {
			type: Object,
			default: () => {
				return {};
			}
		}
	},
	data() {
		return {
			rejectType: 'default',
			userLabelValue: null,
			showChildFlow: false,
			chooseZb: false,
			zbUserSelect: [],
			userDetaSource: ['depart'],
			chooseVisiable: false,
			detaSource: ['depart'],
			chooseDeptVisiable: false,
			deptSelect: [],
			nextUsers: [],
			processDefinitionId: '',
			showNextUsers: false,
			canSelectDepart: true,
			initParams: {
				authorityType: 2, //类型;1.可用成员范围,2.禁止使用范围
				applicationId: ''
			},
			ccTitle: '',
			ccSelect: [],
			nodeData: {},
			nodeDatas: {},
			clientX: 400,
			clientY: 100,
			rules: {
				comment: [{ required: true, message: '请输入审核意见', trigger: 'blur' }],
				assignee: [{ required: true, message: '请选择下一步处理人', trigger: 'change' }],
				reason: [{ required: true, message: '请输入驳回原因', trigger: 'blur' }],
				userId: [{ required: true, message: '请选择人员', trigger: 'blur' }],
				deptCandidate: [{ required: true, message: '请选择办理部门', trigger: 'blur' }],
				userLabel: [{ required: true, message: '请选择办理人', trigger: 'change' }],
				selectNextUser: [{ required: true, message: '请选择办理人', trigger: 'blur' }],
				nodeId: [{ required: true, message: '请选择办理节点', trigger: 'change' }]
			},
			ccTaskRules: {
				ccTaskComment: [{ required: true, message: '请输入批注内容', trigger: 'blur' }]
			},
			ccTaskForm: {
				ccTaskComment: '已知晓。'
			},
			processVisible: false,
			activeName: 'forminfo',
			parallelHandler: [],
			radios: [
				{ label: '单选1', value: '1' },
				{ label: '单选2', value: '2' }
			],
			nextHandler: [],
			formInfo: {},
			assignee: [],
			commentIschange: false,
			isRefusalStartIschange: false,
			executeTempComment: '',
			isRefusalStartTempComment: '',
			forms: {
				comment: '同意。', // 审批意见
				assignee: '', // 流程处理人 由用户选择一个，(数据从下一步处理人来)1个时默认值参至完成接口（assignee）。
				ccUsers: [], //抄送用户
				formVars: {}, // 表单数据
				taskId: '', // 流程执行任务ID
				vars: {
					// leaveDays: 4
				}, // 流程变量 选择的流程处理人 {}
				reason: '', // 驳回原因
				userId: '',
				deptCandidate: null,
				claimUserId: '',
				userLabel: '',
				selectNextUser: null,
				newActivityId: null
			},
			formId: '',
			flowRecord: [
				{
					name: 'xx审批',
					user: '张三',
					dept: '部门',
					userCandidate: '李四',
					receiveTime: '2024-6-5 16:19:08'
				}
			],
			lf: null,
			graphData: {},
			multiple: false,
			flowParams: {
				desc: false,
				pageNo: 1,
				pageSize: 1000,
				processInstanceId: ''
			},
			taskRecored: [],
			// 支持的操作
			actions: [],
			actionNames: {
				// isRefusalStart: '驳回', //支持驳回至开始节点
				// isCounterSign: '加签', //支持加签
				// isDelegate: '委派', //支持委派
				// isReassign: '转办', //支持转办
				// isCc: '抄送', //支持抄送
				// isSkipNode: '跳转', //支持跳转
				// isInterrupt: '终止', //支持终止
				// isRefusalCurrent: '驳回至当前节点', //支持驳回至当前节点
				// isRevoke: '撤回' //支持撤回
			},
			actionsType: 'execute',
			loading: false,
			floading: false,
			myWindow: null,
			isView: false,
			flowId: null,
			formJson: {},
			optionData: {},
			formData: {},
			formState: false,
			nodeId: '',
			businessKey: '',
			needResponse: false,
			nextType: null,
			isUserLabel: false,
			userLabelList: [],
			radiosSetting: [],
			varName: null,
			formDataId: '',
			formVars: {},
			ccTask: false,
			ccTaskComment: '',
			initFlowData: {},
			nodeName: '',
			pageloading: false,
			aiContent: [],
			beforeNodes: [],
			nextNodes: [],
			formsError: false,
			nextNodeName: '',
			assignees: '',
			userTaskProperties: {},
			assigneeList: [],
			keys: 1,
			canRevoke: false
		};
	},
	computed: {
		...mapState('user', ['waitConfig']),
		formKey: function () {
			return Math.random();
		},
		...mapGetters(['userInfo']),
		// 操作按钮位置 默认底部
		handleBtnPlace() {
			return this.waitConfig?.customerTaskMenu?.handleBtnPlace || 'bottom';
		}
	},
	beforeDestroy() {
		window.removeEventListener('message', this.handleMessage);
		this.$refs.forms.resetFields();
	},
	mounted() {
		/**
		 * taskId 用来获取 获取平行任务待办人 和 下一步代办人
		 * processInstanceId 用来获取待办任务列表(就是当前的节点,设置流程图的当前节点)
		 * =>通过待办任务的taskDefinitionKey 获取 可执行的操作功能
		 * isCandidateTask=1,那么 claimUserId就等于当前用户的id
		 */
		// let bools = { true: true, false: false, undefined: false };
		let initFlowData = {};
		if (this.$route.query.processInstanceId) {
			initFlowData = this.$route.query;
		} else {
			initFlowData = this.flowHandleParams;
		}
		this.initFlowData = { ...initFlowData };
		let { taskId, processInstanceId, coosStatus, businessKey, isCandidateTask, ccTask } =
			initFlowData;
		this.isView = coosStatus == 1 ? true : false;
		// 是否是抄送 => 抄送 抄送的话 需要已阅功能,并且点击需要调用接口

		this.taskId = taskId;
		this.forms.taskId = taskId;
		this.businessKey = businessKey;
		this.nodeId = initFlowData.nodeId;
		// isCandidateTask == 1
		if (isCandidateTask == 1) {
			this.forms.claimUserId = this.userInfo.id;
		}

		// processInstanceId = processInstanceId || '1800341473282932738';
		const params = {
			pageNo: 1,
			pageSize: 100,
			processInstanceId
		};

		this.flowParams.processInstanceId = processInstanceId;
		// 获取所有节点的属性以及其他节点的属性

		// 获取待办任务列表
		selectList(params).then(res => {
			this.taskRecored = res.result?.records;
			const recored = res.result?.records || [];
			if (recored.length > 0) {
				// 组装待办人
				const nodeId = recored[0].flowInfo.taskDefinitionKey;
				const processDefinitionId = recored[0].flowInfo.processDefinitionId;
				this.processDefinitionId = processDefinitionId;
				getNodeSettings({ nodeId, processDefinitionId }).then(res => {
					if (res.code == 200) {
						const result = { ...res.result };
						this.actionNames = { ...result };
						const nodeSetting = {
							isRefusalStart: '驳回', //支持驳回至开始节点
							isCounterSign: '加签', //支持加签
							isDelegate: '委派', //支持委派
							isReassign: '转办', //支持转办
							isSkipNode: '跳转', //支持跳转
							isInterrupt: '终止' //支持终止
						};
						// 驳回 加签 委派 转办 跳转 终止
						const radiosSetting = [{ label: '通过', value: 'execute' }];
						Object.keys(nodeSetting).map(v => {
							if (nodeSetting[v] && result[v]) {
								radiosSetting.push({
									label: nodeSetting[v],
									value: v
								});
							}
						});
						this.radiosSetting = [...radiosSetting];
					}
				});

				getNodeConfig({ nodeId: this.nodeId, processDefinitionId }).then(re => {
					// 获取指定节点的属性
					if (re.code == 200 && re.result) {
						const otherProperties = { ...re.result.node.properties.otherProperties };
						this.needResponse = !!otherProperties.needResponse;
					}
				});

				getNextNode(this.taskId).then(resp => {
					if (resp.code == 200 && resp.result) {
						this.nextNodes = resp.result;
						if (resp.result.length) {
							this.nextNodeName = resp.result[0].nodeName;
						}
					}
				});

				/**
				 * 使用nexthalder了 这个接口没有维护了
				const processDefinitionId = recored[0].workInfo.processDefinitionId;
				// 获取当前待办的下一个节点信息
				getNextAssignee({ nodeId, processDefinitionId, processInstanceId }).then(resp => {
					if (resp.code != 200 || !resp.result) return;
					const result = [...resp.result];
					// console.log('result:', result);
					// if (result && result.length == 1) {
					// 	// 只有一个
					// 	if (result[0].selectValue) {
					// 		// console.log(vv.nodeId);

					// 		const selectValue = result[0].selectValue;
					// 		const assignee = Object.keys(selectValue).map(v => {
					// 			return selectValue[v];
					// 		});
					// 		this.assignees = assignee.join();
					// 	}
					// }
					if (result && result.length > 0) {
						const assigneeList = [];
						result.map(v => {
							const selectValue = v.selectValue;
							const assignee = Object.keys(selectValue).map(v => {
								return selectValue[v];
							});
							assigneeList.push({
								nodeName: v.nodeName,
								assignees: assignee.join()
							});
						});
						this.assigneeList = assigneeList;
					}
				});
				 */
			} else {
				// this.processDefinitionId = processDefinitionId;
				let data = {
					desc: false,
					pageNo: 1,
					pageSize: 2,
					processInstanceId
				};
				selectListHis(data).then(res => {
					const newRes = res.result?.records || [];
					if (newRes.length > 0) {
						// 组装待办人
						this.processDefinitionId = newRes[0].processDefinitionId;
					}
				});
			}
		});
		// 监听页面发送来的消息
		window.addEventListener('message', this.handleMessage);
		// 通过流程变量 去拿表单的数据
		if (this.isView) {
			//如果是查看
			getHisVariables({ processInstanceId }).then(res => {
				this.formVars = { ...res.result };
			});
		} else {
			// 如果不是查看 就通过taskid去拿
			getVariables({ taskId }).then(res => {
				this.formVars = { ...res.result };
			});
		}

		getNextHandler({ taskId: this.taskId, beforeUserId: '' }).then(res => {
			if (res.code == 200 && res.result) {
				const result = [...res.result];
				if (result && result.length > 0) {
					const assigneeList = [];
					result.map(v => {
						const selectValue = v.selectValue;
						if (selectValue) {
							const assignee = Object.keys(selectValue).map(v => {
								return selectValue[v];
							});
							assigneeList.push({
								nodeName: v.nodeName,
								nodeType: v.userTaskProperties.nodeType || '',
								varName: v.varName,
								assignees: v.multiple ? assignee.join() : assignee[0],
								selectValue: v.selectValue
							});
						}
					});
					this.assigneeList = assigneeList;
					console.log(this.assigneeList, 'this.assigneeList');
				}
				res.result.forEach(item => {
					if (item.varName) {
						this.forms.vars[item.varName] = null;
					}
				});
				let nextHandler = res.result;
				let userList = nextHandler.map(v => {
					let selectValue = [];
					Object.keys(v.selectValue).map(vs => {
						selectValue.push({
							label: v.selectValue[vs],
							value: vs
						});
					});
					let item = { ...v };
					item.selectValue = selectValue;
					if (v.varName) {
						if (v.multiple) {
							if (v.nodeConfig.action.isMultDefaultSelected) {
								this.forms.vars[v.varName] = selectValue.map(event => {
									return event.value;
								});
							} else {
								this.forms.vars[v.varName] = [];
							}
						} else {
							this.forms.vars[v.varName] = selectValue[0]?.value || '';
						}
					}
					return item;
				});
				// 如果是多个的.比如是平行任务办理人..那就需要循环去选人/部门...-_-
				if (res.result.length > 1) {
					this.nextHandler = userList;
				} else {
					let v = res.result[0];
					this.nodeName = res.result[0].nodeName;
					this.nextType = res.result[0].type;
					this.multiple = res.result[0].isMultiple;
					if (res.result[0].varName && this.formVars[res.result[0].varName]) {
						if (v.nodeConfig.action.isMultDefaultSelected) {
							this.forms.vars[res.result[0].varName] = this.formVars[res.result[0].varName];
						} else {
							this.forms.vars[res.result[0].varName] = null;
						}
						this.varName = res.result[0].varName;
					}
					if (this.nextType === 'selectNextUser') {
						// this.userLabelList = [...userList];
					} else if (this.nextType === 'userTask') {
						this.isUserLabel = true;
						this.nextHandler = [...userList];
					} else if (this.nextType === 'selectNextDept') {
						this.nextHandler = [...userList];
					} else if (this.nextType === 'multipleTask') {
						// 一个节点 多实例选人 复选
						this.nextHandler = [...userList];
						// 多实例选择人员赋值
						this.assignee = userList[0].selectValue.map(item => {
							return item.value;
						});
						this.forms.assignee = this.assignee.join(',');
					}
					this.varName = res.result[0].varName;
					console.log(this.forms, 'this.formsthis.forms');
					console.log(this.nextHandler, 'this.nextType this.nextType ');
				}
			}
		});
		// cctask为1的时候 就是抄送过来的 但是无法判断是否已阅,如果已阅就不需要再显示已阅按钮了
		if (ccTask == 1) {
			// this.ccTask = ccTask == 1 ? true : false;
			this.pageloading = true;
			selectByTaskInfo({ taskId, processInstanceId })
				.then(res => {
					this.pageloading = false;

					if (res.code == 200) {
						if (this.needReload) {
							this.ccTask = res?.result?.status == 1 ? false : true;
						} else {
							// res.result.status 等于1就是已阅,就不显示按钮了
							this.ccTask = false;
						}
						console.log(this.ccTask, 'this.ccTask ');
					}
				})
				.catch(() => {
					this.pageloading = false;
				});
		}
	},
	methods: {
		setFormId(formId) {
			this.formId = formId;
		},
		setDisabled(items) {
			return !items.nodeConfig.action.isMultipleOptional;
		},

		setShow(next) {
			// 默认逻辑
			return Array.isArray(next.selectValue) && next.selectValue.length > 1;
		},
		// 撤回流程
		revokeFlow() {
			this.$prompt('请输入撤回原因', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				inputPattern: /./,
				inputErrorMessage: '请输入撤回原因'
			}).then(({ value }) => {
				this.doRevokeFlow(value);
			});
		},
		doRevokeFlow(reason) {
			this.loading = true;
			undoneTask({ isCheck: false, taskId: this.taskId, reason })
				.then(res => {
					this.loading = false;
					if (res.code === 200) {
						this.$message.success('流程已撤回!');
						if (this.$route.query.blank_id) {
							window.close();
							return;
						}
						this.$emit('handleTask');
					} else {
						this.$message.error(res.message);
					}
				})
				.catch(e => {
					this.$message.error(e.message ? e.message : '流程撤回失败!');
				});
		},
		// 监听页面发送来的消息
		handleMessage(message) {
			// 收到iframe的消息
			const { type, content, params } = message.data;
			if (!type) return;
			// 也有其他的webpack的消息,暂时就不过滤了
			if (type.indexOf('webpack') > -1) return;
			if (type == 'msg' && content == 'success') {
				this.doSave(params);
			} else {
				this.loading = false;
			}
		},
		sendMessage() {
			// 向iframe发送提交的消息
			this.$refs.vfContainer.sendMessage(this.actionsType);
		},
		doSave(params) {
			// 外部表单保存成功后调用通过
			if (params) {
				this.forms.formVars = { ...params };
				this.forms.vars = { ...params };
			}
			this.actionChange(this.actionsType, true);
		},
		save() {
			this.$nextTick(() => {
				console.log(vfContainer, 'vfContainer');
				const vfContainer = this.$refs.vfContainer;
				const isSlot = vfContainer.isSlot;
				const formType = vfContainer.formType;
				const formDataId = vfContainer.formDataId;
				const formId = vfContainer.formId;
				const formDataIdKey = vfContainer.formDataIdKey;
				if (!vfContainer) {
					return;
				}
				console.log(vfContainer, 'vfContaine111r');
				// formType 纯内部表单
				if (formType === 1) {
					// 如果是内部表单 需要提交表单的数据,有可能表单中有需要填写的数据
					this.$refs.vfContainer
						.getVFormData()
						.then(res => {
							// 验证通过
							const formData = { ...res };
							// 需要提交表单填写的数据
							updateFormData(this.businessKey, formData).then(resp => {
								if (resp.code == 200) {
									// 需要保存表单的数据成功之后才走流程
									this.forms.formVars = { ...formData };
									this.actionChange(this.actionsType, true);
								} else {
									this.$message.error(resp.message);
								}
							});
						})
						.catch(e => {
							// 验证失败
							console.log(e);
						});
				} else {
					// 外部/应用表单 和 内部表单混合
					if (isSlot) {
						// 如果是纯外部表单(比如 新能源) 需要提交外部表单成功后再调用执行通过
						if (this.needResponse) {
							this.sendMessage();
						} else {
							this.doSave();
						}
					} else {
						// 应用表单+内部表单
						if (formDataId) {
							// 如果应用表单的内部表单的数据Id存在
							// 执行更新
							vfContainer
								.getVFormData()
								.then(res => {
									// 验证通过
									const formData = { ...res };
									// 需要提交表单填写的数据
									updateFormData(formDataId, formData).then(resp => {
										if (resp.code == 200) {
											// 需要保存表单的数据成功之后才走流程
											this.forms.formVars = { ...formData };
											this.actionChange(this.actionsType, true);
										} else {
											this.$message.error(resp.message);
										}
									});
								})
								.catch(e => {
									// 验证失败
									console.log(e);
								});
						} else {
							//执行新增
							vfContainer.getVFormData().then(res => {
								// 验证通过
								const formData = { ...res };
								saveFormData(formId, formData).then(response => {
									if (response.code == 200) {
										this.formDataId = response.result.id;
										this.forms.formVars = { ...formData };
										this.forms.vars[formDataIdKey] = this.formDataId;
										this.actionChange(this.actionsType, true);
									}
								});
							});
						}
					}
				}
			});
			// this.$refs.vFormRef.getFormData().then(formData => {
			// 	console.log('formData:', formData);
			// 	console.log('保存的数据:', this.actionsType, this.forms);
			// 	this.actionChange(this.actionsType, true);
			// });
		},

		// 选择操作
		actionChange(e, bool) {
			if (e == 'execute') {
				if (!this.commentIschange) {
					this.forms.comment = '同意。';
				} else {
					this.forms.comment = this.executeTempComment;
				}
			}
			if (e == 'isRefusalStart') {
				if (!this.isRefusalStartIschange) {
					this.forms.reason = '不同意。';
				} else {
					this.forms.reason = this.isRefusalStartTempComment;
				}
			}
			// 节点跳转
			if (this.actionNames['isSkipNode']) {
				if (this.beforeNodes.length == 0) {
					this.getBeforeNode();
				}
			}
			if (bool) {
				const ispass = this.validForm();
				if (!ispass) return;
				this.loading = true;
			} else {
				if (e != 'execute' && e != 'isRefusalStart') {
					this.forms.reason = '';
					this.forms.comment = '';
				}
				// 切换时候清空选择 重置验证
				// this.$refs.forms.resetFields();
				this.$refs.forms.clearValidate();

				this.ccTitle = [];
				this.ccSelect = [];
				// this.assignee = [];
			}
			/*
				isCc: '抄送', //支持抄送
				isReassign: '转办', //支持转办
				isDelegate: '委派', //支持委派
				isCounterSign: '加签', //支持加签
				isInterrupt: '终止', //支持终止
				isRevoke: '撤回', //支持撤回
				isRefusalStart: '驳回', //支持驳回至开始节点
				isRefusalCurrent: '驳回至当前节点', //支持驳回至当前节点
				isSkipNode: '跳转' //支持跳转
			 */

			switch (e) {
				// 通过
				case 'execute':
					if (bool) {
						const ispass = this.validForm();
						if (!ispass) return;
						this.execute();
					}
					break;
				//抄送
				case 'isCc':
					if (bool) {
						const ispass = this.validForm();
						if (!ispass) return;
						this.cc();
					}
					break;
				// 转办
				case 'isReassign':
					if (bool) {
						const ispass = this.validForm();
						if (!ispass) return;
						this.reassign();
					}
					break;
				// 委办
				case 'isDelegate':
					if (bool) {
						const ispass = this.validForm();
						if (!ispass) return;
						this.delegateTask();
					}
					break;
				// 加签
				case 'isCounterSign':
					if (bool) {
						const ispass = this.validForm();
						if (!ispass) return;
						this.counterSign();
					}
					break;
				// 终止
				case 'isInterrupt':
					if (bool) {
						const ispass = this.validForm();
						if (!ispass) return;
						this.interrupt();
					}
					break;
				// 撤回
				case 'isRevoke':
					this.isRevoke();
					break;
				// 驳回
				case 'isRefusalStart':
					if (bool) {
						const ispass = this.validForm();
						if (!ispass) return;
						this.backStart();
					}
					break;
				// 跳转
				case 'isSkipNode':
					if (bool) {
						const ispass = this.validForm();
						if (!ispass) return;
						this.cc();
					}
					break;
			}
		},
		// 获取当前节点可跳转节点
		getBeforeNode() {
			getBeforeNode(this.taskId).then(res => {
				if (res.code == 200) {
					this.beforeNodes = res.result;
				} else {
					this.$message.error(res.message);
				}
			});
		},
		commentChange(e) {
			this.commentIschange = true;
			this.executeTempComment = e;
		},
		isRefusalStartCommentChange(e) {
			this.isRefusalStartIschange = true;
			this.isRefusalStartTempComment = e;
		},
		// 通过
		execute() {
			// 执行通过
			let { taskType, dealOptType } = this.initFlowData;
			let obj = {
				...this.forms
			};
			if (dealOptType == 1) {
				obj.secretaryUserId = this.userInfo.id;
			}
			if (taskType == 3) {
				const { userId, taskId, comment } = this.forms;
				resolveTask({ userId, taskId, comment }).then(res => {
					this.handleResult(res);
				});
			} else {
				execute(obj).then(res => {
					this.handleResult(res);
				});
			}
		},
		validForm() {
			let pass = true;
			if (this.formsError) {
				return false;
			}
			this.$refs.forms.validate(valid => {
				if (valid) {
					let value = this.$refs.forms.vars;
					for (let key in value) {
						if (
							value[key] === null ||
							value[key] === undefined ||
							value[key] === '' ||
							value[key].length === 0
						) {
							pass = false;
							this.$message.error(`请检查选项`);
							return;
						}
					}
					pass = true;
				} else {
					pass = false;
				}
			});
			return pass;
		},
		// 抄送
		cc() {
			execute(this.forms).then(res => {
				this.handleResult(res);
			});
		},
		// 驳回
		backStart() {
			const { reason, taskId, newActivityId } = this.forms;
			if (this.actionNames['isSkipNode'] && this.beforeNodes.length > 0 && newActivityId) {
				skipNode({ reason, id: taskId, newActivityId }).then(res => {
					this.handleResult(res);
				});
			} else {
				backStart({ reason, taskId }).then(res => {
					this.handleResult(res);
				});
			}
		},
		// 终止
		interrupt() {
			interrupt(this.forms).then(res => {
				this.handleResult(res);
			});
		},
		// 转办
		reassign() {
			const { userId, taskId, comment } = this.forms;
			reassign({ userId, taskId, comment }).then(res => {
				this.handleResult(res);
			});
		},
		// 加签
		counterSign() {
			const { userId, taskId, comment } = this.forms;
			counterSign({ userId, taskId, comment }).then(res => {
				this.handleResult(res);
			});
		},
		// 委办
		delegateTask() {
			const { userId, taskId, comment } = this.forms;
			delegateTask({ userId, taskId, comment }).then(res => {
				this.handleResult(res);
			});
		},
		handleResult(res) {
			this.loading = false;
			if (res.code === 200) {
				if (this.$route.query.blank_id) {
					window.close();
					return;
				}
				this.$emit('handleTask');
			} else if (res.code == 51111) {
				this.$message.error(res.message);
				// 代表流程已经办理,不需要再处理了
				const { taskId, processInstanceId } = this.flowHandleParams;
				noticeExecute({ taskId, processInstanceId })
					.then(resp => {
						// 关闭弹窗
						if (this.$route.query.blank_id) {
							window.close();
							return;
						}
						this.$emit('handleTask');
					})
					.catch(e => {
						this.$message.error(e.message);
					});
			} else {
				this.$message.error(res.message || '未知错误!');
			}
		},
		// 撤回
		isRevoke() {
			// revoke(){}
		},
		toggleElementInArray(array, element) {
			const index = array.indexOf(element);
			if (index > -1) {
				// 如果元素已存在，则删除
				array.splice(index, 1);
			} else {
				// 如果元素不存在，则添加
				array.push(element);
			}
		},
		// 下一步处理人选择了
		assigneeChange(row, items) {
			let e = row.value || '';
			if (items.multiple) {
				if (this.setDisabled(items)) {
					return;
				}
				let arr = [...(this.forms.vars[items.varName] || [])];
				this.toggleElementInArray(arr, e);
				// if (items.type === 'multipleTask') {
				this.forms.assignee = arr.join(',');
				this.forms.vars[items.varName] = arr;
				if (arr && arr.length) {
					items.formsError = false;
					this.formsError = false;
				} else {
					items.formsError = true;
					this.formsError = true;
				}

				// } else {
				// 	this.forms.assignee = arr.join(',');
				// 	this.vars[items.varName] = arr.join(',');
				// }
			} else {
				//单选
				// vars[e] = this.nextHandler[e];
				this.forms.vars[items.varName] = e;
				this.forms.assignee = e;
			}
			this.keys += 1;
			console.log(this.forms.vars[items.varName], 'this.vars[items.varName]');
			this.$nextTick(() => {
				let str = 'vars.' + items.varName;
				console.log(str, '11132');
				this.$refs.forms.validateField(str);
			});
			this.getUserName();
		},
		// 显示联动
		getUserName() {
			this.assigneeList.forEach(event => {
				let arr = [];
				// 检查 forms.vars[event.varName] 是否存在
				const value = this.forms.vars[event.varName];
				if (!value || !event.selectValue) {
					event.assign = ''; // 如果值不存在或 selectValue 为空，直接赋空字符串
					return;
				}
				try {
					const getLabels = env => {
						return Object.keys(event.selectValue)
							.filter(element => element === env)
							.map(element => event.selectValue[element]);
					};
					if (Array.isArray(value)) {
						value.forEach(env => {
							arr.push(...getLabels(env));
						});
					} else if (typeof value === 'string') {
						arr.push(...getLabels(value));
					} else {
						console.warn(`Unsupported type for value: ${typeof value}`);
					}
					event.assignees = arr.join(',');
				} catch (error) {
					console.error('Error processing assignee list:', error);
					event.assignees = ''; // 发生错误时设置为空字符串
				}
			});
		},
		// 获取下一步待办人
		processClose() {
			this.lf = null;
			this.processVisible = false;
		},
		tabClick() {
			if (this.activeName == 'flowinfo') {
				this.$refs.flowContainer.init();
			}
		},

		cancel() {
			if (this.$route.query.blank_id) {
				window.close();
				return;
			}
			this.$emit('close');
		},
		showZbSelect() {
			this.chooseZb = true;
		},
		showSelect() {
			this.chooseVisiable = true;
		},
		showDeptSelect() {
			this.chooseDeptVisiable = true;
		},
		close() {
			this.chooseVisiable = false;
		},
		zbClose() {
			this.chooseZb = false;
		},
		zbSure(e) {
			this.zbUserSelect = [...e];
			this.forms.userId = e[0].id;
			this.$refs.forms.clearValidate();
			this.chooseZb = false;
		},
		showUserSelect() {
			this.showNextUsers = true;
		},
		nextUserSure(e) {
			this.nextUsers = [...e];
			if (e.length) {
				if (this.multiple) {
					const ids = [];
					e.map(v => {
						ids.push(v.id);
					});
					this.forms.assignee = ids.join;
					this.forms.vars[this.varName] = ids;
				} else {
					this.forms.assignee = e[0].id;
					this.forms.vars[this.varName] = e[0].id;
				}
			} else {
				this.forms.assignee = '';
				this.forms.vars[this.varName] = '';
			}
			this.showNextUsers = false;
		},
		nextUserClose() {
			this.showNextUsers = false;
		},
		nextUserDel() {
			this.forms.assignee = '';
			this.forms.vars['selectNextUser'] = '';
			this.nextUsers = [];
		},
		sure(e) {
			// this.initValues = [...e];
			this.ccSelect = [...e];
			let dataValue = [];
			e.map(v => {
				dataValue.push(v.id);
			});
			this.buildTitle();
			this.forms.ccUsers = [...dataValue];
			this.$refs.forms.clearValidate();
			this.chooseVisiable = false;
		},
		deptSure(e) {
			this.deptSelect = [...e];
			if (e.length) {
				this.forms.deptCandidate = e[0].id;
			} else {
				this.forms.deptCandidate = null;
			}
			this.chooseDeptVisiable = false;
		},
		deptClose() {
			this.chooseDeptVisiable = false;
		},
		// 点击tage删除
		handleClose(index) {
			this.ccSelect.splice(index, 1);
			let dataValue = [];
			this.ccSelect.map(v => {
				dataValue.push(v.id);
			});
			this.forms.ccUsers = [...dataValue];
			this.buildTitle();
		},
		deptHandleClose(index) {
			this.deptSelect.splice(index, 1);
			let dataValue = [];
			this.deptSelect.map(v => {
				dataValue.push(v.id);
			});
			this.forms.deptCandidate = null;
		},
		handleZbClose() {
			this.zbUserSelect = [];
			this.forms.userId = '';
		},
		buildTitle() {
			let title = [];
			this.ccSelect.map(v => {
				title.push(v.title);
			});
			this.ccTitle = title.join(',');
		},
		userLabelChange(e) {
			if (e) {
				this.forms.vars = {
					userLabel: e
				};
			} else {
				this.forms.vars = {};
			}
		},
		//已阅
		isRead() {
			this.$refs.ccTaskForm.validate(valid => {
				if (valid) {
					const params = {
						comment: this.ccTaskForm.ccTaskComment,
						taskId: this.taskId,
						processInstanceId: this.flowHandleParams.processInstanceId
					};
					this.loading = true;
					setHaveRead(params)
						.then(res => {
							this.loading = false;
							if (res.code === 200) {
								if (this.$route.query.blank_id) {
									window.close();
									return;
								}
								this.$emit('handleTask');
							} else {
								this.$message.error(res.message);
							}
						})
						.catch(() => {
							this.loading = false;
						});
				}
			});
		},
		// AI节点信息
		showAIcontent(e) {
			this.aiContent = e;
		},
		showFlowRecordList(e = []) {
			const userTask = e.filter(v => v.nodeType == 1);
			if (this.taskId == userTask[userTask.length - 1].id) {
				this.canRevoke = true;
			}
		},
		nextNodesChange(e) {
			this.forms.vars.nodeId = e;
		}
	}
};
</script>

<style lang="scss" scoped>
.choose-item {
	position: relative;
}
.node-info {
	position: absolute;
	left: -200px;
	width: 300px;
	box-shadow: 0 0 5px #ccc;
	border-radius: 3px;
	border: 1px solid #ccc;
	background: #fff;
	z-index: 2;
	.title {
		border-top-left-radius: 3px;
		border-top-right-radius: 3px;
		line-height: 40px;
		background-color: #f5f5f5;
		padding-left: 15px;
	}
	.node-item {
		margin: 0 15px;
		padding-bottom: 10px;
		display: flex;
		grid-gap: 5px;
		.node-label {
			width: 70px;
			text-align: right;
		}
		.node-ctn {
			flex: 1;
		}
		&:not(:first-child) {
			margin-top: 10px;
			border-bottom: 1px solid #ccc;
		}
	}
}
::v-deep {
	.lf-node,
	.lf-element-text {
		cursor: pointer;
	}
	.el-tag + .el-tag {
		margin-left: 5px;
	}
	.el-select {
		width: 100%;
	}
}
.main_content {
	height: 100%;
	width: 100%;
	display: flex;
	flex-direction: column;
	background-color: #fff;
	::v-deep .el-tabs {
		height: 100%;
		display: flex;
		flex-direction: column;
		overflow: hidden;
	}
	::v-deep .el-tabs__content {
		flex: 1;
		overflow: auto;
		@include noScrollBar;
	}
}
.task-box {
	flex-grow: 1;
	padding: 0 15px;
	overflow: auto;
	display: flex;
	grid-gap: 20px;
	.left {
		display: flex;
		flex-direction: column;
	}
	> div {
		height: 100%;
		padding-top: 15px;
	}
	.right {
		border-left: 1px solid #dce3e7;
	}
}
.task-actions {
	border-top: 1px solid #dce3e7;
	flex-shrink: 0;
	text-align: right;
	padding: 8px 15px;
	&-mix {
		display: flex;
		justify-content: space-between;
		border-top: none;
		padding: 0px 0 0px 15px;
		.el-button {
			width: 100px;
			padding: 8px 20px;
			&.submit-btn {
				flex-grow: 1;
			}
		}
	}
}
label {
	font-weight: normal;
}
.right {
	::v-deep {
		.el-form-item__label {
			font-weight: normal;
			display: flex;
			justify-content: flex-end;
			align-items: center;
			min-height: 40px;
		}
	}
}
::v-deep {
	.el-tabs--card > .el-tabs__header .el-tabs__item.is-active {
		background: #fff;
	}
	.el-checkbox {
		line-height: inherit;
	}
	.el-timeline-item__timestamp {
		color: #000;
		font-size: 16px;
		font-weight: bold;
	}
}
.tab-box {
	position: relative;
	.revoke-btn {
		position: absolute;
		right: 0;
		top: 4px;
		z-index: 2;
	}
}
.tab-box {
	.el-button {
		padding: 4px 16px;
	}
}
.assignees-box {
	line-height: 20px;
	margin-top: 10px;
}
</style>
