<template>
	<div class="main_content">
		<div class="task-box">
			<div
				class="left"
				:style="{
					width: `100%`
				}"
			>
				<el-tabs v-model="activeName" type="card" @tab-click="tabClick">
					<el-tab-pane label="流转记录" name="records">
						<flow-records
							ref="flowRecords"
							:process-instance-id="flowParams.processInstanceId"
						></flow-records>
					</el-tab-pane>
					<el-tab-pane label="流程图" name="flowinfo">
						<flow-container
							ref="flowContainer"
							:flow-handle-params="flowHandleParams"
						></flow-container>
					</el-tab-pane>
				</el-tabs>
			</div>
		</div>
	</div>
</template>

<script>
import FlowContainer from '../start-process/flowContainer.vue';
import FlowRecords from '@/components/logic-flow/flow-records';
import { isEmpty } from '@/utils';
export default {
	components: { FlowRecords, FlowContainer },
	props: {
		flowQuery: {
			type: Object,
			default: () => {
				return {};
			}
		}
	},
	data() {
		return {
			activeName: 'records',
			flowHandleParams: {},
			flowParams: {}
		};
	},
	mounted() {
		// processInstanceId : 流程实例id
		// taskId : 任务id
		// height : 流程图高度 500px 50% calc(100vh - 100px)
		this.handleFlowData();
	},
	methods: {
		handleFlowData() {
			if (this.flowQuery.processInstanceId || this.$route.query.processInstanceId) {
				let initFlowData = !isEmpty(this.flowQuery) ? this.flowQuery : this.$route.query;
				this.flowHandleParams = { ...initFlowData };
				this.flowParams = { ...initFlowData };
				this.$nextTick(() => {
					this.$refs.flowRecords.findByFormIdRecord(
						this.flowQuery.processInstanceId || this.$route.query.processInstanceId
					);
				});
			}
		},
		// 获取下一步待办人
		tabClick() {
			if (this.activeName == 'flowinfo') {
				this.$refs.flowContainer.init();
			}
		},

		cancel() {
			window.COOS_SDK.closePopup();
		},

		close() {
			this.chooseVisiable = false;
		}
	}
};
</script>

<style lang="scss" scoped>
.choose-item {
	position: relative;
}
.node-info {
	position: absolute;
	left: -200px;
	width: 300px;
	box-shadow: 0 0 5px #ccc;
	border-radius: 3px;
	border: 1px solid #ccc;
	background: #fff;
	z-index: 2;
	.title {
		border-top-left-radius: 3px;
		border-top-right-radius: 3px;
		line-height: 40px;
		background-color: #f5f5f5;
		padding-left: 15px;
	}
	.node-item {
		margin: 0 15px;
		padding-bottom: 10px;
		display: flex;
		grid-gap: 5px;
		.node-label {
			width: 70px;
			text-align: right;
		}
		.node-ctn {
			flex: 1;
		}
		&:not(:first-child) {
			margin-top: 10px;
			border-bottom: 1px solid #ccc;
		}
	}
}
::v-deep {
	.lf-node,
	.lf-element-text {
		cursor: pointer;
	}
	.el-tag + .el-tag {
		margin-left: 5px;
	}
	.el-select {
		width: 100%;
	}
}
.main_content {
	height: 100%;
	width: 100%;
	background: #fff;
}
.task-box {
	padding: 0 15px;
	height: 100%;
	overflow: auto;
	display: flex;
	grid-gap: 20px;
	border-bottom: 1px solid #dce3e7;
	> div {
		height: 100%;
		padding-top: 15px;
	}
	.right {
		border-left: 1px solid #dce3e7;
	}
}
.task-actions {
	text-align: right;
	padding-right: 15px;
	padding-top: 8px;
}
label {
	font-weight: normal;
}

::v-deep {
	.el-form-item__label {
		font-weight: normal;
	}
	.el-tabs--card > .el-tabs__header .el-tabs__item.is-active {
		background: #fff;
	}
	.el-checkbox {
		line-height: inherit;
	}
	.el-timeline-item__timestamp {
		color: #000;
		font-size: 16px;
		font-weight: bold;
	}
}
</style>
