<template>
	<div v-loading="loading" class="guide">
		<div class="guide-title-content">
			<div class="guide-title">四川川投新能源基建与运维管理平台</div>
		</div>
		<div class="content">
			<div
				v-for="(item, index) of list"
				:key="index"
				class="content-item"
				@click="selectTheme(item.value)"
			>
				<img :src="imgs[index]" class="content-item-img" alt="" />
				<div class="content-item-text">{{ item.title }}</div>
			</div>
		</div>
		<password ref="password" />
	</div>
</template>
<script>
import { geMatrixtDicts } from '@/api/modules/common';
import { setItem } from '@/utils/localstorage';
import { mapActions } from 'vuex';
import Password from '@/layout/components/setting/password';
import { CoosEventTypes } from '@/utils/bus';

export default {
	name: 'GuidePage',
	components: {
		Password
	},
	data() {
		return {
			loading: false,
			list: []
		};
	},
	computed: {
		imgs() {
			return [
				require(`@/assets/images/common/guide-1.png`),
				require(`@/assets/images/common/guide-2.png`),
				require(`@/assets/images/common/guide-3.png`)
			];
		}
	},
	created() {
		//  强制修改密码
		let forceEditInitPwd = localStorage.getItem('forceEditInitPwd');
		if (forceEditInitPwd && forceEditInitPwd == 'true') {
			this.$nextTick(() => {
				// this.$refs.password.open();
			});
		}
	},
	mounted() {
		this.getList();
	},
	methods: {
		...mapActions('settings', ['GET_MENU']),
		async selectTheme(value) {
			setItem('currentTheme', value);
			this.loading = true;
			await this.GET_MENU(value); // 获取菜单
			this.loading = false;
			console.log('======================', this.$route.query.redirect);
			let reUrl = this.$route.query.redirect || '/'; //? decodeURIComponent(this.$route.query.redirect) : '/';
			let noLogin = this.$route.query.noLogin;
			this.$router.replace(reUrl);
			// 非登录页过来就重新加载
			if (noLogin) {
				this._BUS.$emit(CoosEventTypes.reloadProject, true); // 优化重载带来的等待交互
			}
		},
		getList() {
			this.loading = true;
			geMatrixtDicts('menu_theme').then(res => {
				this.loading = false;
				if (res.code === 200) {
					this.list = res.result.menu_theme;
				} else {
					this.$message.error(res.message);
				}
			});
		}
	}
};
</script>
<style scoped lang="scss">
.guide {
	width: 100%;
	height: 100%;
	background-image: url('../../assets/images/common/guide-bg.png');
	background-size: 100% 100%;
	display: flex;
	flex-direction: column;
	justify-content: center;
	&-title-content {
		background-image: url('../../assets/images/common/guide-title-bg.png');
		background-size: 100% 100%;
		width: 1200px;
		height: 120px;
		display: flex;
		align-items: center;
		justify-content: center;
		margin: 0 auto;
	}
	&-title {
		font-family: Alimama ShuHeiTi, Alimama ShuHeiTi;
		font-weight: bold;
		font-size: 48px;
		line-height: 56px;
		background: linear-gradient(270deg, #304762 0%, #5b9ff0 100%);
		-webkit-background-clip: text;
		background-clip: text;
		color: transparent;
	}
	.content {
		flex: 1;
		display: flex;
		align-items: center;
		justify-content: center;
		&-item {
			width: 410px;
			height: 520px;
			background-image: url('../../assets/images/common/card-bg.png');
			background-size: 100% 100%;
			overflow: hidden;
			border-radius: 32px;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			margin: 0 12px;
			cursor: pointer;
			transition: all 0.5s;
			padding: 0 24px;
			&:hover {
				transform: translateY(-20px);
			}
			&-img {
				width: 242px;
				height: 242px;
			}
			&-text {
				margin-top: 45px;
				font-weight: 500;
				font-size: 28px;
				color: #2b70c1;
				line-height: 38px;
			}
		}
	}
}
</style>
