<template>
	<div v-loading="loading" class="ai-uses-statistics">
		<div class="go-back" @click="goBack">
			<el-button icon="el-icon-arrow-left">返回</el-button>
		</div>
		<div class="table">
			<div class="search">
				<div class="search-box">
					<el-form :inline="true" :model="formInline" class="demo-form-inline">
						<el-form-item label="时间日期">
							<el-date-picker
								v-model="formInline.value1"
								type="daterange"
								format="yyyy 年 MM 月 dd 日"
								value-format="yyyy-MM-dd"
								range-separator="至"
								start-placeholder="开始日期"
								end-placeholder="结束日期"
							></el-date-picker>
						</el-form-item>
						<el-form-item label="用户">
							<el-select v-model="formInline.userId" placeholder="请选择用户">
								<el-option
									v-for="item in options"
									:key="item.userId"
									:label="item.realname"
									:value="item.userId"
								></el-option>
							</el-select>
						</el-form-item>
						<el-form-item>
							<el-button type="primary" @click="onSubmit">查询</el-button>
							<el-button @click="onReset">重置</el-button>
						</el-form-item>
					</el-form>
				</div>
			</div>
			<div class="table-box">
				<el-table
					ref="table"
					class="table-box"
					:data="tableData"
					style="width: 100%"
					height="99%"
					show-summary
					:summary-method="customSummaryMethod"
				>
					<el-table-column label="功能/使用情况">
						<el-table-column prop="realname" label="用户" width="120"></el-table-column>
					</el-table-column>
					<el-table-column label="我要提问" header-align="center" width="180" prop="createTime">
						<el-table-column
							prop="questionClickVolume"
							label="点击量"
							width="120"
						></el-table-column>
						<el-table-column
							prop="questionCompletionTokens"
							label="输出token数"
							width="120"
						></el-table-column>
						<el-table-column
							prop="questionPromptTokens"
							label="输入token数"
							width="120"
						></el-table-column>
						<el-table-column
							prop="questionTotalTokens"
							label="总token数"
							width="120"
						></el-table-column>
					</el-table-column>
					<el-table-column label="我要写作" header-align="center" width="180" prop="createTime">
						<el-table-column prop="writingClickVolume" label="点击量" width="120"></el-table-column>
						<el-table-column
							prop="writingCompletionTokens"
							label="输出token数"
							width="120"
						></el-table-column>
						<el-table-column
							prop="writingPromptTokens"
							label="输入token数"
							width="120"
						></el-table-column>
						<el-table-column
							prop="writingTotalTokens"
							label="总token数"
							width="120"
						></el-table-column>
					</el-table-column>
					<el-table-column label="知识库" width="180" header-align="center" prop="createTime">
						<el-table-column prop="kbClickVolume" label="点击量" width="120"></el-table-column>
						<el-table-column
							prop="kbCompletionTokens"
							label="输出token数"
							width="120"
						></el-table-column>
						<el-table-column
							prop="kbPromptTokens"
							label="输入token数"
							width="120"
						></el-table-column>
						<el-table-column prop="kbTotalTokens" label="总token数" width="120"></el-table-column>
					</el-table-column>
					<el-table-column label="数智员工" header-align="center" width="180" prop="createTime">
						<el-table-column prop="dieClickVolume" label="点击量" width="120"></el-table-column>
						<el-table-column
							prop="dieCompletionTokens"
							label="输出token数"
							width="120"
						></el-table-column>
						<el-table-column
							prop="diePromptTokens"
							label="输入token数"
							width="120"
						></el-table-column>
						<el-table-column prop="dieTotalTokens" label="总token数" width="120"></el-table-column>
					</el-table-column>
				</el-table>
			</div>
			<div class="page">
				<el-pagination
					class="desk-el-pagination"
					background
					:current-page="pageNo"
					:page-sizes="[10, 20, 50, 100]"
					:page-size="pageSize"
					layout="total, sizes, ->, prev, pager, next, jumper"
					:total="total"
					@size-change="handleSizeChange"
					@current-change="handleCurrentChange"
				></el-pagination>
			</div>
		</div>
	</div>
</template>
<script>
import { userCount, userPage } from '@/api/modules/ai-uses-statistics';
import { getMemberList } from '@/api/modules/address-book';

export default {
	name: 'AiUsesStatistics',
	data() {
		return {
			pageNo: 1,
			pageSize: 10,
			total: 0,
			tableData: [],
			visible: false,
			options: [],
			initValue: [], // 初始数据
			dataSource: ['depart'], // 选人的数据来源
			loading: false,
			countRow: {},
			formInline: {
				userId: '',
				value1: []
			}
		};
	},
	created() {
		let depId = this.$route.query.depId;
		getMemberList({ depId: depId, pageNo: 1, pageSize: 9999 }).then(res => {
			this.options = res.result.records || [];
		});
		this.getCount();
		this.getList();
	},
	methods: {
		getCount(params) {
			userCount({
				...params,
				deptId: this.$route.query.depId
			}).then(res => {
				this.countRow = res.result || {};
				console.log(res);
			});
		},
		getList(params) {
			this.loading = true;
			let data = {
				deptId: this.$route.query.depId,
				pageNo: this.pageNo,
				pageSize: this.pageSize,
				...params
			};
			userPage(data).then(res => {
				this.loading = false;
				if (res.code === 200) {
					this.tableData = res.result.records || [];
				} else {
					this.$message.error(res.message);
				}
			});
		},
		goBack() {
			this.$router.go(-1);
		},
		onSubmit() {
			let params = {
				userId: this.formInline.userId
			};
			if (this.formInline.value1) {
				params.updateDate = {
					start: this.formInline.value1[0],
					end: this.formInline.value1[1]
				};
			}
			this.getCount(params);
			this.getList(params);
		},
		customSummaryMethod({ columns, data }) {
			const sums = [];
			columns.forEach((column, index) => {
				if (index === 0) {
					sums[index] = '总计';
					return;
				}
				console.log(column.property, 'columncolumn', this.countRow);
				// 获取当前列的 prop 名称
				const property = column.property;
				if (property) {
					sums[index] = this.countRow[property] || 0;
				}
			});
			console.log(sums);
			return sums;
		},
		onReset() {
			this.initValue = [];
			this.formInline.input = '';
			this.formInline.id = '';
			this.formInline.value1 = [];
		},
		clearInput() {
			this.initValue = [];
			this.formInline.input = '';
			this.formInline.id = '';
		},
		handleClick(row) {},
		surePerson(item) {
			if (item && item.length) {
				this.initValue = item;
				this.formInline.input = item[0].title;
				this.formInline.id = item[0].value;
			}
			this.visible = false;
		},
		closePerson() {
			this.visible = false;
		},
		/**改变页码*/
		handleCurrentChange(i) {
			this.pageNo = i;
			this.getList();
		},
		/**改变分页大小*/
		handleSizeChange(i) {
			this.pageNo = 1;
			this.pageSize = i;
			this.getList();
		}
	}
};
</script>

<style scoped lang="scss">
.ai-uses-statistics {
	background: #fcfcfc;
	display: flex;
	flex-direction: column;
	flex: 1;
	height: 100%;
	padding: 11px 24px;
	overflow: hidden;
	.go-back {
		padding-bottom: 16px;
	}
	.table {
		height: 100%;
		flex: 1;
		background: #ffffff;
		box-shadow: 0px 7px 20px 0px rgba(232, 234, 237, 0.4);
		border-radius: 9px 9px 9px 9px;
		padding: 20px;
		display: flex;
		flex-direction: column;
		.table-box {
			flex: 1;
		}
		.search {
		}
		.page {
			padding-top: 20px;
		}
	}
	.dept {
	}
}
.custom-clear-input .el-input__suffix {
	display: none;
}

.custom-clear-input:hover .el-input__suffix {
	display: flex;
	align-items: center;
	justify-content: center;
}
</style>
