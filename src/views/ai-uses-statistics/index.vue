<template>
	<div v-loading="loading" class="ai-uses-statistics">
		<div class="table">
			<div class="search">
				<div class="search-box">
					<el-form :inline="true" :model="formInline" class="demo-form-inline">
						<el-form-item label="时间日期">
							<el-date-picker
								v-model="formInline.value1"
								type="daterange"
								format="yyyy 年 MM 月 dd 日"
								value-format="yyyy-MM-dd"
								range-separator="至"
								start-placeholder="开始日期"
								end-placeholder="结束日期"
							></el-date-picker>
						</el-form-item>
						<el-form-item label="部门">
							<el-input
								v-model="formInline.input"
								readonly
								placeholder="请选择部门"
								class="custom-clear-input"
								@click.native="handleDeptClick"
							>
								<!-- 自定义清除按钮 -->
								<i
									v-if="formInline.input"
									slot="suffix"
									class="el-icon-close"
									@click.stop="clearInput"
								></i>
							</el-input>
						</el-form-item>
						<el-form-item>
							<el-button type="primary" @click="onSubmit">查询</el-button>
							<el-button @click="onReset">重置</el-button>
						</el-form-item>
					</el-form>
				</div>
			</div>
			<div class="table-box">
				<el-table
					ref="table"
					class="table-box"
					:data="tableData"
					style="width: 100%"
					height="99%"
					show-summary
					:summary-method="customSummaryMethod"
				>
					<el-table-column label="功能/使用情况">
						<el-table-column prop="departName" label="部门" width="120"></el-table-column>
					</el-table-column>
					<el-table-column label="我要提问" header-align="center" width="180" prop="createTime">
						<el-table-column
							prop="questionClickVolume"
							label="点击量"
							width="120"
						></el-table-column>
						<el-table-column
							prop="questionCompletionTokens"
							label="输出token数"
							width="120"
						></el-table-column>
						<el-table-column
							prop="questionPromptTokens"
							label="输入token数"
							width="120"
						></el-table-column>
						<el-table-column
							prop="questionTotalTokens"
							label="总token数"
							width="120"
						></el-table-column>
					</el-table-column>
					<el-table-column label="我要写作" header-align="center" width="180" prop="createTime">
						<el-table-column prop="writingClickVolume" label="点击量" width="120"></el-table-column>
						<el-table-column
							prop="writingCompletionTokens"
							label="输出token数"
							width="120"
						></el-table-column>
						<el-table-column
							prop="writingPromptTokens"
							label="输入token数"
							width="120"
						></el-table-column>
						<el-table-column
							prop="writingTotalTokens"
							label="总token数"
							width="120"
						></el-table-column>
					</el-table-column>
					<el-table-column label="知识库" width="180" header-align="center" prop="createTime">
						<el-table-column prop="kbClickVolume" label="点击量" width="120"></el-table-column>
						<el-table-column
							prop="kbCompletionTokens"
							label="输出token数"
							width="120"
						></el-table-column>
						<el-table-column
							prop="kbPromptTokens"
							label="输入token数"
							width="120"
						></el-table-column>
						<el-table-column prop="kbTotalTokens" label="总token数" width="120"></el-table-column>
					</el-table-column>
					<el-table-column label="数智员工" header-align="center" width="180" prop="createTime">
						<el-table-column prop="dieClickVolume" label="点击量" width="120"></el-table-column>
						<el-table-column
							prop="dieCompletionTokens"
							label="输出token数"
							width="120"
						></el-table-column>
						<el-table-column
							prop="diePromptTokens"
							label="输入token数"
							width="120"
						></el-table-column>
						<el-table-column prop="dieTotalTokens" label="总token数" width="120"></el-table-column>
					</el-table-column>
					<el-table-column fixed="right" label="操作" width="60">
						<template slot-scope="scope">
							<div class="btn-icon">
								<el-button
									type="text"
									:disabled="!scope.row.depId"
									@click.stop="handleClick(scope.row)"
								>
									详情
								</el-button>
							</div>
						</template>
					</el-table-column>
				</el-table>
			</div>
			<div class="page">
				<el-pagination
					class="desk-el-pagination"
					background
					:current-page="pageNo"
					:page-sizes="[10, 20, 50, 100]"
					:page-size="pageSize"
					layout="total, sizes, ->, prev, pager, next, jumper"
					:total="total"
					@size-change="handleSizeChange"
					@current-change="handleCurrentChange"
				></el-pagination>
			</div>
		</div>
		<orgPersonnelDialog
			title="选择部门"
			:need-all-data="true"
			:init-values="initValue"
			:visible="visible"
			:is-radio="true"
			:include-users="false"
			disable-all
			:data-source="dataSource"
			@sure="surePerson($event)"
			@close="closePerson"
		></orgPersonnelDialog>
	</div>
</template>
<script>
import { deptCount, deptPage } from '@/api/modules/ai-uses-statistics';
import orgPersonnelDialog from '@/components/org-personnel-dialog/index.vue';
import { getMemberList } from '@/api/modules/address-book';

export default {
	name: 'AiUsesStatistics',
	components: { orgPersonnelDialog },
	data() {
		return {
			pageNo: 1,
			pageSize: 10,
			total: 0,
			tableData: [],
			visible: false,
			initValue: [], // 初始数据
			dataSource: ['depart'], // 选人的数据来源
			loading: false,
			countRow: {},
			formInline: {
				input: '',
				value1: [],
				id: ''
			}
		};
	},
	created() {
		getMemberList().then(res => {});
		this.getCount();
		this.getList();
	},
	methods: {
		getCount() {
			deptCount({}).then(res => {
				this.countRow = res.result || {};
				console.log(res);
			});
		},
		getList(params) {
			this.loading = true;
			let data = {
				pageNo: this.pageNo,
				pageSize: this.pageSize,
				...params
			};
			deptPage(data).then(res => {
				this.loading = false;
				if (res.code === 200) {
					this.tableData = res.result.records || [];
				} else {
					this.$message.error(res.message);
				}
			});
		},
		handleDeptClick() {
			this.visible = true;
		},
		onSubmit() {
			let params = {
				deptId: this.formInline.id
			};
			if (this.formInline.value1) {
				params.updateDate = {
					start: this.formInline.value1[0],
					end: this.formInline.value1[1]
				};
			}
			this.getList(params);
		},
		customSummaryMethod({ columns, data }) {
			const sums = [];
			columns.forEach((column, index) => {
				if (index === 0) {
					sums[index] = '总计';
					return;
				}
				console.log(column.property, 'columncolumn', this.countRow);
				// 获取当前列的 prop 名称
				const property = column.property;
				if (property) {
					sums[index] = this.countRow[property] || 0;
				}
			});
			console.log(sums);
			return sums;
		},
		onReset() {
			this.initValue = [];
			this.formInline.input = '';
			this.formInline.id = '';
			this.formInline.value1 = [];
		},
		clearInput() {
			this.initValue = [];
			this.formInline.input = '';
			this.formInline.id = '';
		},
		handleClick(row) {
			this.$router.push({
				path: '/ai-uses-statistics/details',
				query: {
					depId: row.depId
				}
			});
		},
		surePerson(item) {
			if (item && item.length) {
				this.initValue = item;
				this.formInline.input = item[0].title;
				this.formInline.id = item[0].value;
			}
			this.visible = false;
		},
		closePerson() {
			this.visible = false;
		},
		/**改变页码*/
		handleCurrentChange(i) {
			this.pageNo = i;
			this.getList();
		},
		/**改变分页大小*/
		handleSizeChange(i) {
			this.pageNo = 1;
			this.pageSize = i;
			this.getList();
		}
	}
};
</script>

<style scoped lang="scss">
.ai-uses-statistics {
	background: #fcfcfc;
	display: flex;
	flex-direction: column;
	flex: 1;
	height: 100%;
	padding: 11px 24px;
	overflow: hidden;
	.table {
		height: 100%;
		flex: 1;
		background: #ffffff;
		box-shadow: 0px 7px 20px 0px rgba(232, 234, 237, 0.4);
		border-radius: 9px 9px 9px 9px;
		padding: 20px;
		display: flex;
		flex-direction: column;
		.table-box {
			flex: 1;
		}
		.search {
		}
		.page {
			padding-top: 20px;
		}
	}
	.dept {
	}
}
.custom-clear-input .el-input__suffix {
	display: none;
}

.custom-clear-input:hover .el-input__suffix {
	display: flex;
	align-items: center;
	justify-content: center;
}
</style>
