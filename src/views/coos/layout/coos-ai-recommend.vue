<template>
	<div class="welcome">
		<div
			v-show="['cyzh', 'zdbk'].includes(currentMode.type) && questionsList.length"
			v-loading="questionLoading"
			class="questions"
		>
			<div class="questions-title">
				<label>你可以试着问我：</label>
				<div v-if="questionsNum > 4" class="questions-title-other" @click="otherQuestions">
					<i class="coos-iconfont icon-refresh plugin-icon" />
					<span>换一批</span>
				</div>
			</div>
			<div class="questions-container">
				<div
					v-for="(item, index) of questionsList"
					:key="index"
					class="questions-item"
					@click="questionClick(item.title)"
				>
					{{ item.title }}
				</div>
			</div>
		</div>
		<div v-show="['zklh', 'sjzx'].includes(currentMode.type)" class="some-plugin">
			<div class="some-plugin-title">
				<div class="some-plugin-title-left">
					{{ currentMode.type === 'zklh' ? '你可以试试这些功能' : '你可以试试搜索这些业务数据' }}
				</div>
				<div class="some-plugin-title-right" @click="lookMore">
					<span>查看全部</span>
					<i class="coos-iconfont icon-nav-right plugin-icon"></i>
				</div>
			</div>
			<div class="some-plugin-content">
				<div
					v-for="(item, index) of pluginOrDataDrilling"
					:key="index"
					class="some-plugin-content-item"
					:class="{
						'select-plugin':
							(currentPlugin && currentPlugin.name && currentPlugin.name === item.name) ||
							(currentDataDrilling &&
								currentDataDrilling.moduleName &&
								currentDataDrilling.moduleName === item.moduleName)
					}"
					@click="clickSome(item)"
				>
					<FileById
						:more-style="{ borderRadius: '6px' }"
						:size="32"
						:value="item.logoUrl || item.icons"
					></FileById>
					<div class="plugin-text">
						<div class="plugin-text-title">
							<span>{{ item.name || item.moduleName }}</span>
							<div
								v-if="currentMode.type === 'zklh'"
								class="plugin-logo"
								:class="'plugin-logo-' + item.type"
							>
								{{ item.type === 1 ? '智能填表' : item.type === 2 ? '智能查询' : '功能直连' }}
							</div>
						</div>
						<div class="plugin-text-desc">{{ item.digest || item.remark }}</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
export default {
	props: {
		/**当前问题数量*/
		questionsNum: {
			type: Number,
			default: () => {
				return 0;
			}
		},
		/**当前插件*/
		currentPlugin: {
			type: [Object, null],
			default: () => {
				return null;
			}
		},
		/**当前数据智析插件*/
		currentDataDrilling: {
			type: [Object, null],
			default: () => {
				return null;
			}
		},
		/**请求等待*/
		questionLoading: {
			type: Boolean,
			default: () => {
				return false;
			}
		},
		/**问题集合*/
		questionsList: {
			type: Array,
			default: () => {
				return [];
			}
		},
		/**插件集合*/
		pluginOrDataDrilling: {
			type: Array,
			default: () => {
				return [];
			}
		},
		/**配置的标题*/
		coosTitle: {
			type: String,
			default: () => {
				return '';
			}
		},
		/**当前模式*/
		currentMode: {
			type: Object,
			default: () => {
				return {};
			}
		},
		/**模式集合*/
		modeList: {
			type: Array,
			default: () => {
				return [];
			}
		},
		/**是否组件*/
		isComponent: {
			type: Boolean,
			default: () => {
				return false;
			}
		}
	},
	data() {
		return {};
	},
	methods: {
		changeMode(item) {
			this.$emit('changeMode', item);
		},
		otherQuestions() {
			this.$emit('otherQuestions');
		},
		questionClick(item) {
			this.$emit('questionClick', '', item);
		},
		lookMore() {
			this.$emit('lookMore');
		},
		clickSome(item) {
			this.$emit('clickSome', item);
		}
	}
};
</script>
<style scoped lang="scss">
.welcome {
	width: 100%;
	margin: 44px 0 58px;
	& .questions {
		width: 100%;
		max-width: 632px;
		margin-top: 15px;

		&-container {
			width: 100%;
			display: flex;
			flex-wrap: wrap;
			gap: 8px;
		}

		&-item {
			cursor: pointer;
			border-radius: 6px 6px 6px 6px;
			border: 1px solid #e4eaf5;
			border-radius: 6px;
			background-color: #ffffff;
			font-family: PingFang SC, PingFang SC;
			font-weight: 400;
			font-size: 14px;
			color: $primaryTextColor;
			line-height: 24px;
			padding: 10px 12px;
		}

		&-title {
			width: 100%;
			display: flex;
			margin-bottom: 8px;
			justify-content: space-between;

			& label {
				font-family: PingFang SC, PingFang SC;
				height: 24px;
				font-weight: 400;
				font-size: 14px;
				color: $subTextColor;
				line-height: 24px;
			}

			&-other {
				cursor: pointer;
				height: 24px;
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 12px;
				color: var(--brand-6);
				line-height: 24px;
				display: flex;
				align-items: center;

				& i {
					margin-right: 10px;
				}
			}
		}
	}

	&-top {
		@include flexBox(flex-start);
		margin-bottom: 29px;

		&-left {
			width: 48px;
			height: 48px;
			border-radius: 12px;
		}

		&-right {
			margin-left: 15px;

			&-title {
				font-weight: 500;
				font-size: 20px;
				color: $primaryTextColor;
				line-height: 28px;
				text-align: left;
				font-style: normal;
				text-transform: none;
			}

			&-desc {
				font-weight: 400;
				font-size: 14px;
				color: var(--brand-6);
				line-height: 22px;
				text-align: left;
				font-style: normal;
				text-transform: none;
			}
		}
	}

	&-bottom {
		width: 100%;
		@include flexBox();
		flex-wrap: wrap;

		&-item {
			padding: 0 0 0 4px;
			@include flexBox(flex-start);
			width: 200px;
			height: 80px;
			background: #ffffff;
			border-radius: 9px;
			border: 1px solid #dce3e7;
			margin: 0 8px 8px 0;
			cursor: pointer;

			&-img {
				width: 60px;
				height: 60px;
				margin-right: 6px;
			}

			&-text {
				margin-bottom: 5px;
			}

			&-title {
				font-weight: 500;
				font-size: 16px;
				color: $primaryTextColor;
				line-height: 24px;
				text-align: left;
				font-style: normal;
				text-transform: none;
			}

			&-subTitle {
				font-weight: 400;
				font-size: 12px;
				color: $subTextColor;
				line-height: 20px;
				text-align: left;
				font-style: normal;
				text-transform: none;
			}
		}

		.mode-select {
			width: 200px;
			height: 78px;
			padding-left: 3px;
			overflow: hidden;
			border-radius: 9px;
			border: 2px solid var(--brand-4);
		}
	}

	.some-plugin {
		margin-top: 16px;
		padding: 12px 16px;
		border-radius: 9px;
		min-width: 630px;
		background: #ffffff;

		&-title {
			@include flexBox(space-between);
			font-weight: 400;
			font-size: 14px;
			text-align: left;
			font-style: normal;
			text-transform: none;
			margin-bottom: 12px;

			&-left {
				color: $subTextColor;
			}

			&-right {
				cursor: pointer;
				color: var(--brand-6);

				.plugin-icon {
					font-size: 16px;
				}
			}
		}

		&-content {
			display: flex;
			gap: 8px;
			flex-wrap: wrap;

			&-item {
				width: 294px;
				height: 66px;
				background: #f6f9fd;
				border-radius: 6px;
				padding: 10px 12px;
				display: flex;
				cursor: pointer;

				.plugin-text {
					&-title {
						font-weight: 500;
						font-size: 14px;
						color: $primaryTextColor;
						line-height: 24px;
						text-align: left;
						font-style: normal;
						text-transform: none;
						margin-bottom: 2px;
						@include flexBox(flex-start);
					}

					&-desc {
						font-weight: 400;
						font-size: 12px;
						color: $subTextColor;
						line-height: 20px;
						text-align: left;
						font-style: normal;
						text-transform: none;
					}
				}
			}

			.select-plugin {
				padding: 9px 11px;
				border: 1px solid var(--brand-6);
			}
		}
	}
}
</style>
