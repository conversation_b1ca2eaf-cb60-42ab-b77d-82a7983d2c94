<template>
	<div>
		<!--  事件用于待办中拖动  -->
		<div
			class="message-detail-title"
			@mousedown="$emit('titleDragge')"
			@mouseup="$emit('titleDragge')"
		>
			<div class="message-detail-title-item" style="flex: 1; display: flex; justify-content: left">
				<img class="avatar" :src="require(`@/assets/${rentThem}/coos/coos-avatar.png`)" alt="" />
				<div>{{ coosTitle }}</div>
			</div>
			<div v-if="!isComponent" class="message-detail-title-btn" @click="createNewChat">
				创建新对话
			</div>
			<div v-else class="close-coos" @click="closeCoos">关闭</div>
		</div>
		<div v-if="currentMode.type === 'cyzh' && fileQuery" class="file-tip">
			<div class="file-tip-left">
				<svg-icon class="file-tip-image" :icon-class="getBackground(fileQuery)"></svg-icon>
				<span class="file-tip-text">正在基于文件“</span>
				<span class="file-tip-name">{{ fileQuery.name }}</span>
				<span class="file-tip-text">”为你作答</span>
			</div>
			<span class="file-tip-btn" @click="clearFile">清空</span>
			<el-progress
				v-if="fileQuery.process !== 100"
				class="file-tip-process"
				:stroke-width="2"
				:percentage="50"
				:show-text="false"
				:color="processStatus"
			></el-progress>
		</div>
	</div>
</template>
<script>
import getBackground from '@/utils/get-file-icon';

export default {
	name: 'CoosHeader',
	props: {
		/**组件模式*/
		isComponent: {
			type: Boolean,
			default: () => {
				return false;
			}
		},
		/**配置的标题*/
		coosTitle: {
			type: String,
			default: () => {
				return '';
			}
		},
		/**进度条颜色*/
		processStatus: {
			type: String,
			default: () => {
				return '';
			}
		},
		/**文件对象*/
		fileQuery: {
			type: [Object, null],
			default: () => {
				return null;
			}
		},
		/**当前对话模式*/
		currentMode: {
			type: Object,
			default: () => {
				return {};
			}
		}
	},
	data() {
		return {};
	},
	methods: {
		/**获取文件背景图*/
		getBackground,
		/**组件模式，关闭coos*/
		closeCoos() {
			this.$emit('closeCoos');
		},
		createNewChat() {
			this.$emit('createNewChat');
		},
		/**清空文件上下文*/
		clearFile() {
			this.$emit('clearFile');
		}
	}
};
</script>
<style scoped lang="scss">
.message-detail {
	&-title {
		height: 54px;
		padding: 0 20px 0 18px;
		@include flexBox(space-between);
		border-top: 1px solid #f0f0f0;
		border-bottom: 1px solid #dce3e7;

		&-item {
			font-size: 18px;
			font-weight: 800;
			color: $primaryTextColor;
			line-height: 24px;
			@include flexBox();

			.avatar {
				width: 36px;
				height: 36px;
				border-radius: 6px;
				margin-right: 7px;
			}
		}

		.close-coos {
			font-weight: 400;
			font-size: 14px;
			color: $subTextColor;
			line-height: 22px;
			text-align: center;
			font-style: normal;
			text-transform: none;
			cursor: pointer;
		}

		&-btn {
			width: 94px;
			height: 32px;
			border-radius: 6px;
			border: 1px solid var(--brand-6);
			@include flexBox();
			font-size: 14px;
			font-weight: 400;
			color: var(--brand-6);
			line-height: 22px;
			cursor: pointer;
		}
	}
	.file-tip {
		//position: absolute;
		//top: 0;
		//left: 0;
		width: 100%;
		background: rgba(172, 200, 255, 0.24);
		padding: 8px 20px;
		@include flexBox(space-between);

		.file-tip-left {
			flex: 1;
			overflow: hidden;
			@include flexBox(flex-start);

			.file-tip-image {
				width: 24px;
				height: 24px;
				flex-shrink: 0;
			}

			.file-tip-text {
				font-size: 14px;
				flex-shrink: 0;
			}

			.file-tip-name {
				max-width: 400px;
				font-size: 14px;
				@include aLineEllipse;
				color: var(--brand-6);
			}
		}

		.file-tip-btn {
			cursor: pointer;
			color: var(--brand-6);
		}

		.file-tip-process {
			position: absolute;
			bottom: 0;
			width: 100%;
		}
	}
}
</style>
