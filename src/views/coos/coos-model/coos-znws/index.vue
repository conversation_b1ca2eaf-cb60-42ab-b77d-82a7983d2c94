<template>
	<!--  style 对象形式 渐变背景出不来-->
	<div
		class="znws"
		:style="`background: ${rightBackground}; overflow-y: ${
			messageList.length === 0 ? 'auto' : 'hidden'
		};`"
	>
		<div v-if="!messageList.length" class="header">
			<div>
				<img class="header-img" :src="extend.bgUrl" alt="Animated PNG" />
			</div>
			<div class="header-title">{{ extend.title }}</div>
			<div class="header-dec">{{ extend.rentDigest }}</div>
		</div>
		<div class="content" :style="messageList.length ? 'overflow: hidden;' : ''">
			<!--			2025/07/01经信需求去掉头部提示，保留底部-->
			<!--			<div v-if="messageList.length" class="content-box">-->
			<!--				<img class="content-box-img" src="@/assets/them-coos/coos/dialog-icon.png" alt="" />-->
			<!--				<div class="content-box-dec">内容由人工智能大模型生成，信息仅供参考</div>-->
			<!--			</div>-->
			<!--      渲染对话-->
			<div
				v-if="messageList.length"
				id="coos-detail-con"
				ref="messageContent"
				class="message-list"
				@scroll="scroll"
			>
				<div
					v-for="(item, index) of messageList"
					:key="index"
					style="width: 100%"
					:style="{ marginTop: index === 0 ? FirstAnswerMargin : '' }"
					@contextmenu="contextmenu($event, item, index)"
				>
					<!--  提问的展示、选择知识库的展示  -->
					<CoosQuery
						v-if="item.type === 'query'"
						:ref="'message-item-' + index"
						:hide-time="true"
						:znws-style="true"
						coos-version="coosAi"
						:coos-avatar="false"
						:is-component="isComponent"
						:item="item"
						:index="index"
					></CoosQuery>
					<!--  选择知识库后的回答  -->
					<div v-else-if="item.type === 'answer'">
						<messageStep
							v-if="item.hasStep"
							:key="item.currentStep"
							:current-step="item.currentStep"
						></messageStep>
						<CoosAnswer
							:ref="'message-item-' + index"
							:is-component="isComponent"
							:is-title="false"
							:item="item"
							:znws-style="true"
							:coos-avatar="false"
							:is-pause="index === messageList.length - 1 ? isPause : false"
							:index="index"
							:session-id="sessionId"
							@refresh="refresh"
							@addMoreApplicationAnswer="addMoreApplicationAnswer"
							@continueQuery="continueQuery"
							@agentTypeChange="handleChangeAgent"
							@modelIdTypeChange="modelIdTypeChange"
							@handleModelChange="handleModelChange"
							@addMessage="addMessage"
							@handleMoreRecommend="send"
						></CoosAnswer>
					</div>
				</div>
			</div>
			<div v-if="fileQuery" class="file-tip">
				<div class="file-tip-left">
					<svg-icon class="file-tip-image" :icon-class="getBackground(fileQuery)"></svg-icon>
					<span class="file-tip-text">正在基于文件“</span>
					<span class="file-tip-name">{{ fileQuery.name }}</span>
					<span class="file-tip-text">”为你作答</span>
				</div>
				<span class="file-tip-btn" @click="clearFile">清空</span>
			</div>
			<!--  输入内容  -->
			<div class="message-send">
				<div class="message-send-box" :style="{ height: height + 'px' }">
					<el-input
						v-model="sendCon"
						class="message-send-input"
						resize="none"
						type="textarea"
						autosize
						:placeholder="currentMode.prologue || extend.rentPrologue || '请输入问题'"
						@keydown.native="preventEnter"
						@keyup.native="sendMessage"
					></el-input>
				</div>
				<!--          工具栏-->
				<div class="message-utils">
					<!--          类型-->
					<div v-if="!fileQuery">
						<el-dropdown trigger="click" :popper-append-to-body="false" @command="activeType">
							<div
								:class="
									agentObj.id
										? 'dropdown-active  IsInternetSearch IsInternetSearch-box'
										: 'dropdown-active'
								"
							>
								<i class="coos-iconfont icon-xuanxiang icon dropdown-icon"></i>
								<span>{{ agentObj.agentName }}</span>
							</div>
							<el-dropdown-menu slot="dropdown" class="znws-type-dropdown-bg">
								<el-dropdown-item v-for="item in typeList" :key="item.id" :command="item">
									<div
										:class="
											agentObj.id === item.id
												? 'dropdown-item dropdown-item-active'
												: 'dropdown-item'
										"
									>
										<img class="dropdown-item-img" :src="item.logoUrlPath" alt="" />
										<div class="dropdown-item-text">{{ item.agentName }}</div>
									</div>
								</el-dropdown-item>
							</el-dropdown-menu>
						</el-dropdown>
					</div>
					<!--            深度思考-->
					<el-tooltip
						class="item"
						effect="dark"
						content="开启推理模式，进行深度思考"
						placement="top"
					>
						<div
							v-if="currentModeButtons.includes('deepThink')"
							:class="
								deepThink
									? 'message-utils-select IsInternetSearch IsInternetSearch-box'
									: 'message-utils-select'
							"
							@click.stop
						>
							<i class="coos-iconfont icon-shendusousuo icon deepThink"></i>
							<span
								:class="deepThink ? 'text IsInternetSearch' : 'text'"
								@click="internetSearch('deepThink')"
							>
								深度思考
							</span>
						</div>
					</el-tooltip>
					<!--            联网搜索-->
					<div
						v-if="currentModeButtons.includes('onlineSearch') && !fileQuery"
						:class="
							searchType === 'IsInternetSearch'
								? 'message-utils-select IsInternetSearch IsInternetSearch-box'
								: 'message-utils-select'
						"
						@click.stop
					>
						<i class="coos-iconfont icon-lianwangsousuo icon"></i>
						<span
							:class="searchType === 'IsInternetSearch' ? 'text IsInternetSearch' : 'text'"
							@click="internetSearch('IsInternetSearch')"
						>
							联网搜索
						</span>
					</div>
					<el-divider v-if="searchType === ''" direction="vertical"></el-divider>
					<!--					<i-->
					<!--						v-if="currentModeButtons.includes('imageUpload') && searchType === ''"-->
					<!--						class="coos-iconfont icon-tu more-icon"-->
					<!--						@click="pickFile('img')"-->
					<!--					></i>-->
					<div v-if="currentModeButtons.includes('imageUpload') && searchType === ''">
						<i
							class="coos-iconfont icon-folder folder-file-icon more-icon"
							title="上传文件"
							@click="pickFile('file')"
						/>
						<input
							ref="fileInput"
							type="file"
							:accept="accept"
							style="display: none"
							@change="onPickFile($event)"
						/>
					</div>
					<!--           发送暂停  -->
					<div class="send-pause">
						<div v-if="IsSend" class="send-pause-pause-icon" @click="pause">
							<div class="pause-icon"></div>
						</div>
						<div
							v-else
							:class="
								sendCon.length ? 'active-send-icon send-pause-send-icon' : 'send-pause-send-icon'
							"
							@click="send"
						>
							<i class="coos-iconfont icon-fasong1"></i>
						</div>
					</div>
				</div>
			</div>
			<!--     推荐 -->
			<div v-if="!messageList.length" class="recommend">
				<div class="recommend-title">
					<div class="recommend-title-text">热门问题</div>
					<div class="recommend-title-dec" @click="changeQuestion">换一换</div>
				</div>
				<div class="recommend-box">
					<div
						v-for="(item, index) of groupedQuestionList[groupedQuestionListIndex]"
						:key="index"
						class="recommend-box-item"
						@click="setInput(item.title)"
					>
						<img
							class="recommend-box-item-img"
							src="../../../../assets/images/coos/hot.png"
							alt=""
						/>
						<div class="recommend-box-item-text">{{ item.title }}</div>
					</div>
				</div>
			</div>
		</div>
		<div class="bottom">本服务内容由人工智能整理生成，所提供的信息仅供参考。</div>
	</div>
</template>
<script>
import { getAgents, getCoosHistory, uploadCoosFile } from '@/api/modules/coos';
import { mapGetters } from 'vuex';
import queryMixins from '@/views/coos/coos-model/coos-znws/index';
import CoosQuery from '@/views/coos/message/coos-query.vue';
import CoosAnswer from '@/views/coos/message/coos-answer.vue';
import { debounce } from '@/utils';
import MessageStep from '@/views/coos/components/message-step.vue';
import { questionsSearch } from '@/api/modules/coos-znwz';
import getBackground from '@/utils/get-file-icon';
const { customUploadFileHandler } = uploadCoosFile();
export default {
	name: 'CoosZnws',
	components: { MessageStep, CoosAnswer, CoosQuery },
	mixins: [queryMixins],
	props: {
		currentMode: {
			type: Object,
			default: () => {
				return {};
			}
		},
		currentModeButtons: {
			type: Array,
			default: () => {
				return [];
			}
		},
		extend: {
			type: Object,
			default: () => {
				return {};
			}
		}
	},
	data() {
		return {
			initHeight: 100, // 聊天框初始高度
			height: 100, // 变化的高度
			sendCon: '',
			deepThink: false,
			typeList: [],
			isComponent: false,
			IsSend: false,
			lastMessageId: '', // 最后一条messageId
			params: {
				pageSize: 3,
				pageNo: 1
			},
			sendType: 1,
			contentEl: null, // 内容区域
			lock: false, //还有没有更多消息，请求锁
			loading: false, // 数据加载loading
			isAdmin: true,
			lockAutoScroll: false, // 锁住自动滚动
			messageId: 0,
			isPause: false,
			processStatus: '#19be6b', // 进度条颜色
			fileQuery: null, // 当前上传的文件
			accept: '*',
			checkList: [], // 选中的知识库
			knowledge: [], // 知识库
			agentObj: {},
			questionList: [],
			groupedQuestionList: [],
			controller: null,
			sessionId: '', // 当前对话的ID
			messageList: [],
			groupedQuestionListIndex: 0,
			searchType: ''
		};
	},
	computed: {
		...mapGetters(['rentInfo', 'aiEnterList', 'userInfo']),
		// 第一条消息是答案的时候加上间距
		FirstAnswerMargin() {
			return this.messageList.length > 0 && this.messageList[0].type === 'answer' ? '24px' : 0;
		},
		rightBackground() {
			return this.extend?.background || '';
		}
	},
	watch: {},
	created() {
		this.getQuestionsSearch();
		this.getAgents();
	},
	methods: {
		getQuestionsSearch() {
			questionsSearch({ isSceneSuggest: true }).then(res => {
				this.questionList = res.result.records || [];
				this.groupedQuestionList = [];
				for (let i = 0; i < this.questionList.length; i += 4) {
					this.groupedQuestionList.push(this.questionList.slice(i, i + 4));
				}
			});
		},
		changeQuestion() {
			if (this.groupedQuestionListIndex === this.groupedQuestionList.length - 1) {
				this.groupedQuestionListIndex = 0;
				return;
			}
			this.groupedQuestionListIndex += 1;
		},
		getAgents() {
			getAgents({
				pageNo: 1,
				pageSize: 10
			}).then(res => {
				if (res.code === 200) {
					let data = res.result.records || [];
					this.typeList = data.map(_ => {
						return {
							..._,
							name: _.agentName
						};
					});
				} else {
					this.$message.error(res.message);
				}
			});
		},
		activeType(item) {
			if (this.agentObj.id === item.id) {
				this.agentObj = {};
				return;
			}
			if (this.searchType === 'IsInternetSearch') {
				this.searchType = '';
			}
			this.agentObj = item;
		},
		/**右键菜单*/
		contextmenu(event, item, index) {
			this.clickRightMessage = item;
			this.menuIndex = index;
			// 根据需要进行位置调整等操作
			this.menuLeft = event.clientX;
			this.menuTop = event.clientY;
			this.menuShow = true;
		},
		refresh(text) {
			console.log(text);

			this.inputMessage = text;
			// this.messageList.splice(-2, 2);
			this.send('', this.inputMessage, false);
		},
		/**获取文件背景图*/
		getBackground,
		/**清空文件上下文*/
		clearFile() {
			this.fileQuery = null;
		},
		/**选择文件*/
		pickFile(type) {
			this.accept = '.pdf,.doc,.docx,.txt,.xlsx,.png,.jpg,.jpeg,.gif,.png,.jpg,.jpeg,.gif';
			this.$nextTick(() => {
				this.$refs['fileInput'].click();
			});
		},
		/**选择文件完成*/
		onPickFile(event) {
			let file = event.target.files[0];
			let { name } = file;
			let typeArr = name.split('.');
			let type = typeArr[typeArr.length - 1];
			event.target.value = '';
			this.processStatus = '#19be6b';
			this.fileQuery = {
				name,
				type,
				process: 0
			};
			customUploadFileHandler({ file }, process => {
				// 根据定位的文件所在索引位置，赋值进度条
				this.fileQuery.process =
					((process.loaded / process.total) * 100).toFixed(2) > 99
						? 99
						: ((process.loaded / process.total) * 100).toFixed(2);
			}).then(res => {
				if (res.code === 200) {
					this.fileQuery.id = res.result.filenames;
					// 选择文件  清空联网搜索 智能体选择
					this.agentObj = {};
					if (this.searchType === 'IsInternetSearch') {
						this.searchType = '';
					}
					this.fileQuery.process = 100;
				} else {
					this.processStatus = '#ff0000';
					this.$message.error(res.message);
				}
			});
		},
		/**更多应用点击之后的生成答案框*/
		addMoreApplicationAnswer(message, functionId) {
			let { answerId } = this.addTemporaryMessage('');
			this.getPluginAiAnswer(answerId, message, functionId);
		},
		// 没有智能体 切换模式
		handleModelChange(record, agent) {
			this.messageList.forEach((item, index) => {
				if (item.id === record.id) {
					item.modelId = agent.modelId;
					this.setJxjAnswer(item, index);
				}
			});
		},
		modelIdTypeChange(modelId) {
			this.modelId = modelId;
		},
		// 切换当前回答的智能体回答
		handleChangeAgent(record, agent) {
			let query = record.query;
			let messageId = record.id;
			this.tempAgentObj = agent;
			this.messageList = this.messageList.map(item => {
				if (item.id === messageId) {
					return {
						...item,
						recommendAgentId: agent.id,
						answerDone: false,
						currentStep: 1,
						answerFrom: [],
						recommendedQuestions: [],
						answerMulti: null,
						answer: ''
					};
				}
				return item;
			});
			this.lastMessageId = messageId;
			if (this.IsSend) {
				return;
			}
			this.isPause = false;
			this.IsSend = true;
			this.getZnwsAiAnswer(messageId, query);
		},

		/**监听滚动*/
		scroll(e) {
			let event = e.srcElement;
			this.contentEl = event;
			this.scrollLoad();
			if (event.scrollTop + event.clientHeight >= event.scrollHeight - 10) {
				this.lockAutoScroll = false;
			} else {
				this.lockAutoScroll = true;
			}
		},
		getChatRecords() {
			this.$emit('getChatRecords');
		},
		/**继续在全部知识库中查找*/
		continueQuery(message) {
			let { answerId } = this.addTemporaryMessage('在全部知识库中查询'); // 添加消息队列
			// 区分智控模式和普通对话模式
			this.getQuery(answerId, message, true); // 异步获取消息回复
		},
		/**
		 * 添加消息
		 * @param {Object} answer 图表配置答案
		 * @param {Array} haveChats 历史对话的图标名称
		 * */
		addMessage(answer, haveChats = []) {
			let { answerId } = this.addTemporaryMessage('');
			let options;
			let name;
			if (Array.isArray(answer)) {
				options = answer[0].data;
				name = answer[0].name;
			} else {
				options = answer.data || answer;
				name = answer.name || '图表';
			}
			let newHaveChats = [...haveChats, name];
			this.messageList.forEach(item => {
				if (item.id === answerId) {
					item.answer = options;
					item.title = name;
					item.haveChats = newHaveChats;
					item.answerType = 'chart';
					item.isSpecialAnswer = true; // 答案是特殊渲染
					item.answerDone = true;
				}
			});
		},
		/**阻止回车默认行为*/
		preventEnter(e) {
			if (!e.shiftKey && e.key === 'Enter') {
				e.preventDefault(); // 只有普通回车才阻止默认行为
			}
		},
		/**发送消息*/
		sendMessage(e) {
			if (this.sendType === 1 && e.key === 'Enter' && !e.ctrlKey && !e.shiftKey) {
				this.send(e);
			} else if (this.sendType === 2 && e.key === 'Enter' && e.ctrlKey) {
				this.send(e);
			}
		},
		/**
		 * @Desc 切换搜索
		 * @Param {String} type 选中的类型
		 * */
		internetSearch(type, mode) {
			if (!type) {
				return;
			}
			// 深度思考切换逻辑
			if (type === 'deepThink') {
				this.deepThink = !this.deepThink;
				// 如果切换到深度思考，则关闭联网搜索
				if (this.searchType === 'IsInternetSearch') {
					this.searchType = '';
				}
			} else if (type === 'currentThinkMode') {
				this.deepThink = false;
				this.searchType = '';
			}
			// 相同类型切换时取消选择
			else if (this.searchType === type) {
				this.searchType = '';
			}
			// 选择新类型
			else {
				this.searchType = type;
				// 如果选择联网搜索则关闭深度思考
				if (type === 'IsInternetSearch') {
					this.agentObj = {};
					this.deepThink = false;
				}
			}
			// 只要不是切换的思考模式，就要重置
			this.currentThinkMode = type === 'currentThinkMode' ? mode : 'base';
			// 不管点击什么都关闭选择知识库弹窗
			this.knowledgeBase = false;
		},
		/**
		 * @method 添加临时消息
		 * @param {String} message 追加消息
		 * @param {Boolean} addAnswer 是否追加答案
		 * */
		addTemporaryMessage(message, addAnswer = true) {
			let answerId = 'coos-answer-' + this.messageId;
			let queryId = 'coos-query-' + this.messageId;
			// 添加自己发送的消息
			if (message) {
				this.messageList.push({
					type: 'query',
					id: queryId,
					query: message,
					createTime: new Date().toLocaleString()
				});
			}
			// 添加答案占位消息
			if (addAnswer) {
				this.messageList.push({
					type: 'answer',
					isAll: true, // 是否在全部知识库查询
					query: message, // 问题
					answerFrom: [], // 文档来源
					answerFromDone: this.checkList.length === 0, // 文档来源数据加载完毕
					id: answerId, // 问答ID
					startThink: false, // 是否开始思考
					isThink: false, // 是否深度思考
					thinkCon: '', // 思考内容
					answer: '', // ai答案
					answerMulti: null, // 组合答案
					answerDone: false, // 答案数据加载完毕
					isDgg: false // 是否点赞(经信局特有)
				});
			}
			this.messageId++;
			this.toBottom();
			return { queryId, answerId };
		},
		/**滚动到最底部*/
		toBottom() {
			this.$nextTick(() => {
				let messageListElement = this.$refs.messageContent;
				if (messageListElement) {
					messageListElement.scroll({
						top: messageListElement.scrollHeight,
						left: 0,
						behavior: 'auto'
					});
				}
			});
		},
		/**滚动加载*/
		scrollLoad: debounce(
			function () {
				if (this.contentEl.scrollTop < 10) {
					this.$emit('boxScrollLoad');
				}
			},
			500,
			false
		),
		/**获取coos的历史聊天记录*/
		async getCoosMessage(type) {
			let params = {
				pageNo: this.params.pageNo,
				pageSize: this.params.pageSize
			};
			// 全局组件不是单独针对某种模式的，只是把页面当成了组件使用
			if (this.isComponent && this.componentType !== 'glob') {
				params.chatType =
					this.componentType === 'wait' ? '2' : this.componentType === 'other-system' ? '3' : ''; // 组件模式2为待办，3为其他系统的列表
				params.chatObjectId = this.sessionId;
			} else {
				params.sessionId = this.sessionId;
			}
			if (this.coosType === 'space') {
				params.chatType = '5';
			}
			if (this.coosType === 'waitList') {
				params.chatType = '4';
			}
			// if (params.chatType === '3') return;
			if (!params.chatType) {
				// params.sessionTypes = '1,2,3';
			}
			this.loading = true;
			if (this.$route.query.viewAllHistory && this.$route.query.viewAllHistory === 'true') {
				params.viewAllHistory = true;
			}
			let res = await getCoosHistory(params);
			this.loading = false;
			if (res.code === 200) {
				if (this.params.pageNo >= res.result.pages) {
					this.lock = true;
				}
				if (type && type == 101) {
					// ppt
					let data = res.result.records[0];
					this.$refs.pptCoos.setAnswer(data.answer, data.query);
					return;
				}
				let message = [];
				res.result?.records
					.sort((a, b) => {
						return new Date(a.createTime).getTime() - new Date(b.createTime).getTime();
					})
					.forEach(item => {
						message.push({
							type: 'query',
							query: item.query,
							createTime: item.createTime
						});
						let answer = item.answer;
						let answerType = 'normal';
						let isSpecialAnswer = false;
						if (/^centerControlDataJson:/.test(answer)) {
							answer = answer.split('centerControlDataJson:')[1];
							answerType = Array.isArray(answer) ? 'MULTI' : answer.functionType; // 1是表单模式  2数据列表模式  3应用  MULTI组合模式
							answer = answerType === 3 ? [JSON.parse(answer)] : JSON.parse(answer); // 应用模式转成数组按照组合模式进行渲染
							// answer.data = JSON.parse(answer.data)
							isSpecialAnswer = this.getIsSpecial(answer); // 答案是特殊渲染
						}
						// 获取知识库来源
						let answerFrom = item.answerFrom ? JSON.parse(item.answerFrom) : [];
						// 处理来源中的链接
						answerFrom = answerFrom.map(form => {
							if (form.disk_file && form.disk_file.links) {
								let links = JSON.parse(form.disk_file.links);
								form.disk_file.links = links.filter(link => {
									return link.pc;
								});
								form.openLink = true; // 如果有三方链接，设一个收起打开状态
							}
							return form;
						});
						// 获取推荐问题
						let recommendedQuestions = answerFrom.reduce((base, form) => {
							base = base.concat(form.questions || []);
							return base;
						}, []);
						let data = {
							...item,
							type: 'answer',
							answer,
							answerType,
							title: this.getSpecialTitle(answerType),
							isSpecialAnswer, // 答案是特殊渲染
							isHistory: true, // 加一个历史记录的标识
							answerFrom,
							answerDone: true
						};
						// 如果有推荐问题
						if (recommendedQuestions.length) {
							data.recommendedQuestions = [...new Set(recommendedQuestions)].slice(0, 3);
						}
						// 看历史记录是否有深度思考内容
						if (/(<think>|<\/think>)/gi.test(answer)) {
							data.isThink = true;
							let i = answer.indexOf('</think>');
							// 思考结束前属于思考过程 (有可能没有结束标记)
							data.thinkCon = i > -1 ? answer.slice(0, i).replace(/<think>/gi, '') : '';
							// 思考结束后属于答案 (有可能没有结束标记，但以下写法均兼容)
							data.answer = answer.slice(i + 8);
							data.startThink = false;
						}
						// 新版本，支持各种答案混合渲染，可能都是通过流式慢输出的字符串，自己解析
						if (typeof data.answer === 'string') {
							data.answerMulti = this.handleMultiAnswer(data.answer, item.id);
						}
						message.push(data);
					});
				this.messageList = message.concat(this.messageList);
				if (this.params.pageNo === 1) {
					this.toBottom();
				}
			} else {
				this.$message.error(res.message);
			}
		},
		// 暂停
		pause() {
			try {
				this.pauseAllTyping();
				this.controllerArr.forEach(enevt => {
					enevt.abort();
				});
			} catch (e) {
				console.log(e);
			}
			try {
				this.controller = null;
				this.isPause = true;
				this.IsSend = false;
				this.controllerArr = [];
				this.messageList[this.messageList.length - 1].currentStep = 4;
				this.messageList[this.messageList.length - 1].answerDone = true;
			} catch (e) {
				console.log(e);
			}
		},
		setInput(text) {
			this.send('', text);
		},
		/**
		 * @description 发送消息
		 * @param {Object} e 点击元素
		 * @param {Object} message 发送的消息（默认输入框输入的）可供全局调用的通用方法
		 * */
		send(e, message = this.sendCon, clearType = true) {
			if (this.IsSend) {
				return;
			}
			this.isPause = false;
			this.isPaused = false; // 没有搞懂这两个变量的区别。。。

			if (message.replace(/(\n|\s)/gi, '')) {
				this.inputMessage = message;
				this.IsSend = true;
				if (clearType) {
					this.sendCon = ''; // 清空
				}
				let { answerId } = this.addTemporaryMessage(message); // 添加消息队列
				this.lastMessageId = answerId;

				this.getZnwsAiAnswer(answerId, message);
			} else {
				this.sendCon = '';
				this.$message.error('发送消息不能为空！');
			}
		}
	}
};
</script>

<style scoped lang="scss">
.znws {
	height: 100%;
	display: flex;
	flex: 1;
	align-items: center;
	flex-direction: column;
	//@include noScrollBar;
	.header {
		margin-top: 120px;
		width: 75%;
		&-img {
			width: 100%;
		}
		&-title {
			margin-top: 0px;
			font-family: PingFangSC, PingFang SC;
			font-weight: 600;
			font-size: 30px;
			color: #222222;
			line-height: 42px;
			text-align: center;
		}
		&-dec {
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			font-size: 18px;
			color: #6a7b9e;
			line-height: 40px;
			text-align: center;
		}
	}
	.content {
		width: 75%;
		display: flex;
		flex-direction: column;
		flex: 1;
		padding: 16px 0;
	}
	.message-send {
		flex-shrink: 0;
		margin-top: 30px;
		min-height: 135px;
		padding: 20px 0 10px;
		background: #ffffff;
		box-shadow: 0px 4px 5px 0px rgba(21, 34, 76, 0.06);
		border-radius: 16px 16px 16px 16px;
		border: 1px solid #d9e2ec;
		&-input {
			height: 100%;
			::v-deep .el-textarea__inner {
				height: 100% !important;
				overflow-y: auto;
				border-radius: 16px 16px 16px 16px;
				background: #ffffff;
				border: none;
				&::placeholder {
					color: $holderTextColor;
				}

				@include noScrollBar;

				&:focus {
					border-color: transparent;
					box-shadow: none;
					@include scrollBar;
				}

				&:hover {
					border-color: transparent;
					box-shadow: none;
					@include scrollBar;
				}
			}
		}
	}
	.message-utils {
		height: 42px;
		padding: 6px 10px;
		position: relative;
		display: flex;
		align-items: center;
		.more-icon {
			cursor: pointer;
			margin-right: 9px;
		}
		.folder-file-icon {
			font-size: 20px !important;
		}
		.send-pause {
			position: absolute;
			right: 10px;
			top: 8px;
			.send-pause-send-icon {
				cursor: pointer;
				width: 30px;
				height: 30px;
				background: #d1d3db;
				border-radius: 18px 18px 18px 18px;
				color: #fff;
				@include flexBox();
			}
			.active-send-icon {
				background: var(--brand-6);
			}
			.send-pause-pause-icon {
				width: 30px;
				height: 30px;
				background: var(--brand-6);
				box-shadow: 0px 4px 4px 0px rgba(61, 159, 245, 0.25);
				@include flexBox();
				border-radius: 18px 18px 18px 18px;
				.pause-icon {
					width: 10px;
					height: 10px;
					background: #ffffff;
					border-radius: 3px 3px 3px 3px;
				}
			}
		}
		&-mode {
			display: inline-flex;
			align-items: center;
			background: #f5f7fa;
			border-radius: 3px;
			padding: 5px 8px;
			font-weight: 400;
			font-size: 12px;
			color: $textColor;
			line-height: 20px;
			text-align: center;
			font-style: normal;
			text-transform: none;
			margin-right: 8px;
			cursor: pointer;

			&-icon {
				width: 20px;
				height: 20px;
				margin-right: 2px;
			}
		}

		&-select {
			background: #f5f7fa;
			padding: 4px 8px;
			border-radius: 6px;
			border: 1px solid #f5f7fa;
			display: inline-flex;
			align-items: center;
			margin-right: 8px;
			.plugin-choose {
				display: flex;
				align-items: center;
			}

			.icon {
				font-size: 20px;
				margin-right: 2px;
			}

			.zklh-icon {
				font-size: 20px;
				margin-right: 2px;
				color: #035dff;
			}

			.text {
				font-size: 12px;
				font-weight: 400;
				color: $textColor;
				line-height: 20px;
				cursor: pointer;
			}

			.plugin-text {
				color: var(--brand-6);
				margin: 0 5px;
			}

			.plugin-reset {
				margin-left: 20px;
				font-size: 12px;
				color: var(--brand-6);
				line-height: 20px;
			}
		}

		.quick-button {
			position: absolute;
			top: calc(50% - 12px);
			right: 20px;
			cursor: pointer;
			@include flexBox();

			&-icon {
				margin-left: 12px;
				font-size: 20px;
			}
		}

		.mode-popup {
			position: absolute;
			bottom: calc(100% + 17px);
			left: 13px;
			min-width: 163px;
			height: auto;
			border-radius: 12px;
			background: #ffffff;
			box-shadow: 0px 4px 14px 0 rgba(0, 0, 0, 0.1);
			@include flexBox(flex-start, flex-start);
			flex-direction: column;

			.mode-title {
				width: 100%;
				font-weight: 500;
				font-size: 14px;
				color: $primaryTextColor;
				line-height: 22px;
				text-align: center;
				font-style: normal;
				text-transform: none;
				padding: 10px 10px 5px;
				border-bottom: 1px solid #f0f0f0;
			}

			.mode-list {
				width: 100%;
				padding: 0 8px 8px;

				&-item {
					padding: 9px 8px;
					cursor: pointer;
					@include flexBox(flex-start);

					&-img {
						height: 20px;
						width: 20px;
						margin-right: 3px;
					}

					&-text {
						font-weight: 400;
						font-size: 14px;
						color: $textColor;
						line-height: 22px;
						text-align: left;
						font-style: normal;
						text-transform: none;
					}
				}
			}
		}
	}
	.dropdown-active {
		background: #f5f7fa;
		padding: 4px 8px;
		border-radius: 6px;
		border: 1px solid #f5f7fa;
		display: inline-flex;
		align-items: center;
		margin-right: 8px;
		.dropdown-icon {
			font-size: 20px;
		}
	}
}
::v-deep .dropdown-item-active {
	border: 1px solid #187bf3;
}
::v-deep .dropdown-item {
	display: flex;
	align-items: center;
	background: rgba(24, 123, 243, 0.05);
	border-radius: 6px;
	padding: 10px;
	&-img {
		width: 20px;
	}
	&-text {
		margin-left: 4px;
		font-family: PingFangSC, PingFang SC;
		font-weight: 400;
		font-size: 14px;
		color: #444444;
		line-height: 20px;
	}
}

::v-deep .el-dropdown-menu__item {
	margin-bottom: 7px;
	padding: 0 7px !important;
	&:hover {
		background-color: transparent;
	}
}
::v-deep .el-dropdown-menu {
	background-color: red !important; /* 使用 !important 提高优先级 */
}
.IsInternetSearch {
	color: var(--brand-6) !important;
}
.IsInternetSearch-box {
	background: var(--brand-1) !important;
	border: 1px solid var(--brand-3) !important;
}
.message-list {
	overflow-y: auto;
	flex: 1;
	@include flexBox(flex-start, flex-start);
	flex-direction: column;
	//@include noScrollBar;
}
.recommend {
	margin-top: 30px;
	&-title {
		display: flex;
		justify-content: space-between;
		&-dec {
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			font-size: 14px;
			cursor: pointer;
			color: #2f446b;
		}
		&-text {
			font-family: AlimamaShuHeiTi, AlimamaShuHeiTi;
			font-weight: bold;
			font-size: 20px;
			line-height: 28px;
			background-image: linear-gradient(90deg, var(--brand-3) 0%, var(--brand-6) 100%);
			-webkit-background-clip: text;
			-webkit-text-fill-color: transparent;
		}
	}
	&-box {
		margin-top: 17px;
		display: flex;
		flex-wrap: wrap;
		&-item {
			display: flex;
			align-items: center;
			padding: 12px 13px;
			border-radius: 6px;
			border: 1px solid #d9e2ec;
			cursor: pointer;
			background: #fff;
			margin-right: 11px;
			margin-bottom: 11px;
			&-img {
				width: 16px;
				margin-right: 8px;
			}
			&-text {
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 14px;
				color: #424242;
				line-height: 20px;
			}
		}
	}
}
.content-box {
	margin: 35px 0;
	padding: 10px;
	background: rgba(255, 255, 255, 0.5);
	border-radius: 9px;
	display: flex;
	justify-content: center;
	align-items: center;
	&-img {
		width: 16px;
		margin-right: 6px;
	}
	&-dec {
		font-family: PingFangSC, PingFang SC;
		font-weight: 400;
		font-size: 14px;
		color: #5b6aa3;
		line-height: 20px;
	}
}
.bottom {
	flex-shrink: 0;
	font-family: PingFangSC, PingFang SC;
	font-weight: 400;
	font-size: 13px;
	color: #5b6aa3;
	line-height: 18px;
	padding-bottom: 18px;
}
.file-tip {
	margin-top: 30px;
	//position: absolute;
	//top: 0;
	//left: 0;
	width: 100%;
	background: rgba(172, 200, 255, 0.24);
	padding: 8px 20px;
	@include flexBox(space-between);

	.file-tip-left {
		flex: 1;
		overflow: hidden;
		@include flexBox(flex-start);

		.file-tip-image {
			width: 24px;
			height: 24px;
			flex-shrink: 0;
		}

		.file-tip-text {
			font-size: 14px;
			flex-shrink: 0;
		}

		.file-tip-name {
			max-width: 400px;
			font-size: 14px;
			@include aLineEllipse;
			color: var(--brand-6);
		}
	}

	.file-tip-btn {
		cursor: pointer;
		color: var(--brand-6);
	}

	.file-tip-process {
		position: absolute;
		bottom: 0;
		width: 100%;
	}
}
</style>
<style lang="scss">
.znws-type-dropdown-bg.el-dropdown-menu {
	background: url('~@/assets/them-coos/coos/znws-type-dropdown-bg.png') no-repeat center center !important;
	background-size: cover !important;
	border-radius: 8px;
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}
</style>
