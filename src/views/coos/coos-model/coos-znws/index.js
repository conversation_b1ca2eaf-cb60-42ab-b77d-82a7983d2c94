import {
	getAnswerFrom,
	getNetworkAnswer,
	getPoliticsBySocket,
	getAgentsId,
	createMessage,
	saveAiQueryAnswer,
	newV2AiQuery,
	getAnswerExtend
} from '@/api/modules/coos';
import { isEmpty } from '@/utils';
import { Base64 } from 'js-base64';
import { commonEchartsOptions } from '@/views/coos/utils/echart-config';
import { existsCache } from '@/api/modules/coos-znwz';

export default {
	data() {
		return {
			promiseA: null, //  拓展列表请求的promise对象
			answerFromId: '', // 获取文档来源的id
			transformIndex: 0, // 转换图表数据的次数
			cacheAnswer: '', // 缓存答案
			tempAgentList: [], // 临时智能体列表
			tempAgentObj: {}, // 临时智能体对象
			jxjAnswer: {},
			modelId: '',
			isFakeStreaming: false,
			modelArr: [],
			isPaused: false, // 控制是否全局暂停
			typingIntervals: {}, // 存储每个任务的 interval
			pausedTasks: {}, // 存储被中断的任务信息
			typingSpeed: 50, // 打字速度，单位：毫秒
			currentCharIndex: 0, // 当前字符索引
			isTyping: false, // 是否正在打字
			controllerArr: [],
			activeStreamCount: 0, // 当前活跃的流式任务数量
			allStreamsCompletedCallback: null, // 所有任务完成后要执行的回
			jxjAnswerKey: 1, //  jxj答案加载的key =1时候并且智能体没有答案时候在加载大模型 联网数据
			pendingDocContent: '', // 缓存未完成的 <docAnswerFiles> 内容
			pendingRecContent: '', // 缓存未完成的 <recommendedQuestions> 内容
			executionPathContent: ''
		};
	},
	methods: {
		/**
		 * 模拟流式输出
		 * 支持多段 normal 内容逐字显示 + 图表等其他类型内容渲染
		 * 支持全局暂停状态控制
		 */
		simulateTyping(answerId, modelType) {
			try {
				const targetItem = this.messageList.find(item => item.id === answerId);
				if (!targetItem) return;

				let segments = targetItem['answerMulti' + modelType] || [];
				if (!Array.isArray(segments)) segments = [segments];
				const normalSegments = segments.filter(seg => seg.answerType === 'normal').slice(0, 1);

				// 清空所有 normal 段的答案
				normalSegments.forEach(seg => (seg.answer = ''));
				this.$set(targetItem, 'answerMulti' + modelType, [...segments]);
				this.isFakeStreaming = true;
				if (!this.lockAutoScroll) {
					this.toBottom();
				}

				if (normalSegments.length === 0) {
					// 没有 normal 段落，直接渲染其他类型内容
					const otherSegments = segments.filter(s => s.answerType !== 'normal');
					if (otherSegments.length > 0) {
						otherSegments.forEach(event => {
							event.answer = event.copyAnswer;
						});
						targetItem['answerMulti' + modelType] = [...otherSegments];
						const key = 'answerMulti' + modelType;
						const value = targetItem[key];
						this.$set(targetItem, key, [...(Array.isArray(value) ? value : [])]);
					}
					this.handleStreamCompletion(answerId, targetItem);
					return;
				}

				let segmentIndex = 0;
				let charIndex = 0;

				const key = `${answerId}-${modelType}`;
				this.typingIntervals = this.typingIntervals || {};
				this.pausedTasks = this.pausedTasks || {};
				this.activeStreamCount++;

				const currentSegment = normalSegments[segmentIndex];
				const copyAnswer = currentSegment.copyAnswer;

				// 动态计算 typingSpeed，确保3秒内完成
				const totalChars = copyAnswer.length;
				const typingSpeed = Math.max(10, Math.floor(3000 / totalChars)); // 最快10ms/字符

				const typingInterval = setInterval(() => {
					if (this.isPaused) {
						this.pausedTasks[key] = { answerId, modelType, segmentIndex, charIndex };
						clearInterval(typingInterval);
						delete this.typingIntervals[key];
						this.activeStreamCount--;
						return;
					}

					if (charIndex < copyAnswer.length) {
						currentSegment.answer += copyAnswer[charIndex];
						charIndex++;
						targetItem.currentStep = 3;
						this.$set(targetItem, 'answerMulti' + modelType, [...segments]);
						if (!this.lockAutoScroll) {
							this.toBottom();
						}
					} else {
						segmentIndex++;
						charIndex = 0;

						if (segmentIndex >= normalSegments.length) {
							clearInterval(typingInterval);
							delete this.typingIntervals[key];

							// 合并其他内容
							let normalSegmentsAnswerId = normalSegments[0].id;
							const otherSegments = segments.filter(s => s.id !== normalSegmentsAnswerId);
							otherSegments.forEach(event => {
								event.answer = event.copyAnswer;
							});

							targetItem['answerMulti' + modelType] = [...normalSegments, ...otherSegments];
							const key = 'answerMulti' + modelType;
							const value = targetItem[key];
							this.$set(targetItem, key, [...(Array.isArray(value) ? value : [])]);
							targetItem['answerDone' + modelType] = true;

							this.handleStreamCompletion(answerId, targetItem);
						}
					}
				}, typingSpeed);

				this.typingIntervals[key] = typingInterval;
			} catch (e) {
				console.error(e, 'simulateTyping error');
				this.activeStreamCount--;
			}
		},
		/**
		 * 处理单个流式任务完成后的逻辑
		 */
		handleStreamCompletion(answerId, targetItem) {
			this.activeStreamCount--; // 流式任务减少一个

			// 如果还有未完成的任务，直接返回
			if (this.activeStreamCount > 0) return;

			// 所有任务已完成，执行最终操作
			this.onAllStreamsCompleted && this.onAllStreamsCompleted(answerId, targetItem);
		},
		/**
		 * 设置所有流式输出完成后要执行的操作
		 */
		onAllStreamsCompleted(answerId, targetItem) {
			// #selectedCode 开始
			targetItem.currentStep = 4;
			this.IsSend = false;
			// 保存当前所有消息的状态（包括未完成的部分）
			this.saveAnswer(targetItem);
			// #selectedCode 结束
			// 可选：重置 callback
			this.allStreamsCompletedCallback = null;
		},

		/**
		 * 暂停所有正在运行的 simulateTyping 输出
		 */
		pauseAllTyping() {
			if (this.isFakeStreaming) {
				Object.entries(this.typingIntervals).forEach(([key, interval]) => {
					if (interval) {
						clearInterval(interval);
					}
				});
				this.saveAnswer(this.messageList[this.messageList.length - 1]);
				this.isPaused = true; // 设置为已暂停状态
				this.typingIntervals = {}; // 清空 interval 记录
			}
		},
		/**
		 * @method getZnwsAiAnswer Ai对话交互
		 * @param {String} answerId 临时生成答案消息的id
		 * @param {String} message 发送的消息
		 * @param {Boolean} functionId 当前选中的功能的id
		 * */
		async getZnwsAiAnswer(answerId, message, functionId = '') {
			//  清空智能体
			this.jxjAnswerKey = 1;
			this.tempAgentList = [];
			this.jxjAnswer = {};
			this.modelId = '';
			this.isFakeStreaming = false;
			this.controllerArr = [];
			let newItem;
			// 找到实例，利用堆内存的特性进行赋值
			let index = this.messageList.findIndex(m => m.id === answerId);
			let item = this.messageList[index];
			// 由于拓展列表数据不同步，直接完成第一步
			item.hasStep = true;
			item.currentStep = 1;
			item.answerFrom = [];
			item.recommendAgent = []; // 推荐智能体列表
			newItem = item;

			// 上传了文件
			if (this.fileQuery) {
				this.modelArr = [];
				this.modelArr.push({
					name: 'DS',
					img: require(`@/assets/them-coos/coos/ds.png`),
					id: 1
				});
				// 不让他重新走大模型联网搜索
				this.jxjAnswerKey = 2;
				this.getAgentAnswer(answerId, message, false, 1); // 异步获取消息回复
				return;
			}
			//  联网搜索
			if (this.searchType == 'IsInternetSearch') {
				this.modelArr = [];
				this.modelArr.push({
					name: '联网搜索',
					img: require(`@/assets/images/coos/kimi.png`),
					id: 2
				});
				// 不让他重新走大模型联网搜索
				this.jxjAnswerKey = 2;
				this.getAgentAnswer(answerId, message, false, 2); // 异步获取消息回复
				return;
			}

			// 等待判断是否有缓存
			let res = await existsCache({ problem: message });
			// 如果暂停，不继续执行
			if (this.isPause) return;
			if (res.code === 200) {
				// 如果有缓存
				if (res.result) {
					let result;
					try {
						result = JSON.parse(res.result);
					} catch (e) {
						console.error('JSON 解析失败:', e);
						return; // 或者根据业务需求处理错误
					}
					let dataJxjInterpretationJson = result.dataJxjInterpretationJson || {};
					dataJxjInterpretationJson.id = answerId;
					const recommendAgent = dataJxjInterpretationJson.recommendAgent || [];
					const modelList = dataJxjInterpretationJson.modelList || [];
					// 公共处理函数
					const processItems = items => {
						items.forEach(item => {
							const arr = dataJxjInterpretationJson['answerMulti' + item.id] || [];
							arr.forEach(event => {
								event.copyAnswer = event.answer;
								event.answer = '';
							});
						});
					};
					processItems(recommendAgent);
					processItems(modelList);
					dataJxjInterpretationJson.currentStep = 2;
					this.messageList = this.messageList.map((item, index) => {
						if (item.id === answerId) {
							return {
								...item,
								...dataJxjInterpretationJson,
								isDgg: true // 更新点赞状态
							};
						}
						return item;
					});
					// simulateTyping 的调用
					[...recommendAgent, ...modelList].forEach(item => {
						if (item && item.id) {
							this.simulateTyping(answerId, item.id);
						}
					});
					return;
				}
			} else {
				this.$message.error(res.message);
			}
			// 获取拓展列表的数据（不同步）
			this.promiseA = new Promise((resolve, reject) => {
				getAnswerExtend({ query: message }).then(AnswerExtend => {
					if (AnswerExtend.code === 200) {
						this.messageList[index].answerExtendList = AnswerExtend.result || [];
						resolve();
					} else {
						this.$message.error(AnswerExtend.message);
						resolve();
					}
				});
			});
			// 选择了智能体直接请求
			if (this.agentObj.id) {
				//  添加智能体
				this.tempAgentList.push(this.agentObj);
				this.getAgentAnswer(answerId, message, false, this.agentObj.id, true, 0); // 异步获取消息回复
			}
			// 如果没有选中智能体id
			else {
				// 不是联网搜索
				if (this.searchType !== 'IsInternetSearch') {
					// 获取答案匹配到的智能体
					await this.getAgentType(message);
					// 如果暂停，不继续执行
					if (this.isPause) return;
					// 多个智能体同时发起请求
					if (this.tempAgentList && this.tempAgentList.length > 0) {
						this.tempAgentList.forEach((event, index) => {
							this.getAgentAnswer(answerId, message, false, event.id, true, index); // 异步获取消息回复
						});
					}
					// 没有匹配到智能体的时候，联网搜索和大模型同步进行
					else {
						this.jxjAnswerKey = 2;
						this.jxjAiInternet(newItem, answerId, message, this.modelArr);
					}
				}
				// 联网搜索
				else {
					this.getAgentAnswer(answerId, message, false); // 异步获取消息回复
				}
			}
		},
		//  jxj 答案没有匹配到智能体
		async jxjAiInternet(item, answerId, message, modelArr) {
			this.modelArr = [];
			// 走大模型
			if (this.currentModeButtons.includes('deepThink')) {
				this.modelArr.push({
					name: 'DS',
					img: require(`@/assets/them-coos/coos/ds.png`),
					id: 1
				});
				this.getAgentAnswer(answerId, message, false, 1); // 异步获取消息回复
			}
			// 有联网搜索
			if (this.currentModeButtons.includes('onlineSearch')) {
				this.modelArr.push({
					name: '联网搜索',
					img: require(`@/assets/images/coos/kimi.png`),
					id: 2
				});
				this.getAgentAnswer(answerId, message, false, 2); // 异步获取消息回复
			}
		},
		/**智能体判断*/
		async getAgentType(msg) {
			let res = await getAgentsId({
				identityId: this.userInfo.identityId || '',
				message: msg,
				sessionId: this.sessionId,
				tenantId: this.rentInfo.id,
				userId: this.userInfo.id
			});
			if (res.code === 200) {
				this.tempAgentList = res.result
					.map(_ => {
						let obj = this.typeList.find(el => el.id == _.id);
						return obj || null;
					})
					.filter(_ => _); // 数组对象 多个智能体
				// this.tempAgentObj = this.tempAgentList[0]; // 默认第一个智能体回答
			} else {
				// TODO 不提示错误
				// uni.$u.toast(res.message);
			}
		},
		/**获取智能体答案（问政综合版）*/
		async getAgentAnswer(id, message, isAll = false, modelType, isAgent, index) {
			// 初始化的请求参数
			let requestParmas = {
				query: message,
				chatType: '0', //对话类型
				deepSeek: this.deepThink, // 是否是深度思考
				sessionId: this.sessionId // 会话id
			};
			if (this.fileQuery) {
				requestParmas.fileDialogId = this.fileQuery.id;
			}
			// 是否是联网搜索
			let aiRes;
			const controller = new AbortController();
			const { signal } = controller;
			this.controllerArr.push(controller);
			if (this.searchType === 'IsInternetSearch' || modelType === 2) {
				aiRes = await getNetworkAnswer(requestParmas, signal);
			} else {
				let request;
				// 有无选择智能体，请求参数接口不同（以临时选择的智能体为主）
				if (isAgent && (!this.isAdmin || this.tempAgentObj.id || this.agentObj.id || modelType)) {
					request = getPoliticsBySocket;
					requestParmas.agentId = this.tempAgentObj.id || this.agentObj.id || modelType;
				} else {
					request = newV2AiQuery;
					requestParmas.chatType = 103;
				}
				aiRes = await request(requestParmas, signal);
			}
			// // 获取AI答案
			const reader = aiRes.body.getReader(); // 流式读取器
			const decoder = new TextDecoder('utf-8');
			let readDone = false; // 读取完毕
			this.answerFromId = ''; // 清空上次对话的answerId
			this.pendingDocContent = ''; // 清空缓存未完成的 <docAnswerFiles> 内容
			this.pendingRecContent = ''; // 清空缓存未完成的 <recommendedQuestions> 内容
			let docAnswerFilesIndex = 0;
			let recommendedQuestionsIndex = 0;
			let executionPathIndex = 0;
			// 流式输出
			while (!readDone) {
				let { value, done } = await reader.read();
				readDone = done;
				let res;
				try {
					res = decoder.decode(value);
					if (/"code":50/.test(res)) {
						// let r = JSON.parse(res.replace('data:', ''));
						// this.messageList.forEach(item => {
						// 	if (item.id === id) {
						// 		item.answerDone = true;
						// 		item.currentStep = 4;
						// 		item.answer = res?.result?.message;
						// 	}
						// });
						res = ['noAnswer'];
					} else {
						// TODO请求成功

						// res = res.replace(/data:/gi, ''); // 解析成JSON，去掉data:开头的字符串
						res = res.split('data:');
						res = res.map(item => {
							if (docAnswerFilesIndex === 1) {
								this.pendingDocContent += item;
								if (item.includes('</docAnswerFiles>')) {
									docAnswerFilesIndex = 2;
									this.handleDocument(item, modelType);
								}
								return '';
							}

							if (recommendedQuestionsIndex === 1) {
								this.pendingRecContent += item;
								if (item.includes('</recommendedQuestions>')) {
									recommendedQuestionsIndex = 2;
									this.handleDocument(item, modelType);
								}
								return '';
							}
							if (executionPathIndex === 1) {
								this.executionPathContent += item;
								if (item.includes('</executionPath>')) {
									executionPathIndex = 2;
									this.handleDocument(item, modelType);
								}
								return '';
							}

							// 处理 <docAnswerFiles> 标签
							if (item.includes('docAnswerFiles')) {
								this.pendingDocContent += item;
								if (item.includes('</docAnswerFiles>')) {
									docAnswerFilesIndex = 2;
									this.handleDocument(item, modelType);
								} else {
									docAnswerFilesIndex += 1;
								}
								return '';
							}
							// 处理 <recommendedQuestions> 标签
							if (item.includes('recommendedQuestions')) {
								this.pendingRecContent += item;
								if (item.includes('</recommendedQuestions>')) {
									recommendedQuestionsIndex = 2;
									this.handleDocument(item, modelType);
								} else {
									recommendedQuestionsIndex += 1;
								}

								return '';
							}
							// 处理 <executionPath> 标签
							if (item.includes('executionPath')) {
								this.executionPathContent += item;
								if (item.includes('</executionPath>')) {
									executionPathIndex = 2;
									this.handleDocument(item, modelType);
								} else {
									executionPathIndex += 1;
								}
								return '';
							}
							// 完成字段不渲染
							if (item.indexOf('complete') > -1) {
								this.tempAgentObj = {};
								// this.messageList.forEach(item => {
								// 	if (item.id === id) {
								// 		item.currentStep = 4;
								// 	}
								// });
								return '';
							} else {
								if (item.indexOf('noAnswer') > -1) {
									this.tempAgentObj = {};
									return 'noAnswer';
								}
								return Base64.decode(item);
							}
						});
						if (readDone && !res.includes('complete')) {
							res.push('complete');
						}
					}

					await this.handleAiRes(res, id, isAll, message, modelType, index);
				} catch (err) {
					console.log('解析流式报错', err, '--------', res);
				}
			}
		},
		handleDocument(str, modelType) {
			this.processRecommendedQuestions(modelType).catch(e => {
				console.log('推荐问题处理失败', e);
			});
			this.processExecutionPath(modelType).catch(e => {
				console.log('processExecutionPath处理失败', e);
			});
			this.processDocAnswerFiles(modelType).catch(e => {
				console.log('文档来源处理失败', e);
			});
		},
		async processTagContent({ tag, contentKey, targetField, isFlag = false, modelType }) {
			const regex = new RegExp(`<${tag}>([\\s\\S]*?)<\\/${tag}>`);
			const match = this[contentKey].match(regex);
			if (match && match[1]) {
				try {
					const parsedData = JSON.parse(match[1].trim());
					const current = this.messageList.find(msg => msg.id === this.lastMessageId);
					if (current) {
						if (isFlag) {
							this.$set(current, targetField + modelType, true);
						} else {
							this.$set(current, targetField + modelType, parsedData);
						}
					}
					// 更新 pending 内容，移除已处理部分
					this[contentKey] = this[contentKey].substring(match.index + match[0].length);
					return parsedData;
				} catch (e) {
					console.error(`解析 ${tag} 失败:`, e, 111, match);
					// 删除所有匹配项，防止重复报错
					this[contentKey] = this[contentKey].replace(regex, '');
				}
			}
			return null;
		},

		// 推荐问题处理
		async processRecommendedQuestions(modelType) {
			return this.processTagContent({
				tag: 'recommendedQuestions',
				contentKey: 'pendingRecContent',
				targetField: 'recommendedQuestions',
				modelType
			});
		},

		// 文档来源处理
		async processDocAnswerFiles(modelType) {
			return this.processTagContent({
				tag: 'docAnswerFiles',
				contentKey: 'pendingDocContent',
				targetField: 'answerFrom',
				modelType
			});
		},
		// executionPath处理
		async processExecutionPath(modelType) {
			return this.processTagContent({
				tag: 'executionPath',
				contentKey: 'executionPathContent',
				targetField: 'executionPath',
				modelType
			});
		},
		/**
		 * @method AI对话的数据处理
		 * @param {Array} values 接收到的答案
		 * @param {String} messageId 答案对应的消息队列id
		 * @param {Boolean} isAll 是否全选知识库
		 * @param {String} message 当前的问题
		 * */
		async handleAiRes(values, messageId, isAll, message, modelType, index) {
			let text = ''; // 回答消息
			let answerFrom = ''; // 当类型变为数组的时候标志着对话结束
			let answerFromId = ''; // 文档来源
			values.forEach(value => {
				if (value && value !== 'noAnswer' && value !== 'complete') {
					if (modelType) {
						let arr = this.tempAgentList.filter(item => item.id === modelType);
						let newArr = this.modelArr.filter(item => item.id === modelType);
						const agent = arr && arr.length ? arr[0] : newArr[0];
						//       item.answerDone = true;
						// 只有当 agent 存在，并且 recommendAgent 中没有相同 id 的 agent 时才 push
						if (agent) {
							this.messageList.forEach(item => {
								if (item.id === messageId) {
									// 对单个回答保存智能体
									item.tempAgentList = this.tempAgentList;
									if (item.currentStep < 3) {
										item.currentStep = 2; // 更新进度条
									}
									item['answerDone' + modelType] = true; // 这个字段我没看懂哪里要用
									if (newArr && newArr.length) {
										item.modelList = item.modelList || [];
										const exists = item.modelList.some(a => a.id === agent.id);
										if (!exists) {
											item.modelList.push(agent);
										}
										if (!item.modelId) {
											item.modelId = agent.id;
										}
									} else {
										item.recommendAgent = item.recommendAgent || [];
										this.tempAgentList.forEach(event => {
											if (event.id === modelType) {
												event.answer = true; // 对应智能体有答案
											}
										});
										let allowPush = true; // 控制是否允许后续的 push 操作
										this.tempAgentList.forEach(event => {
											// 如果当前 event.answer 不存在，不允许后续 push
											if (event.answer === undefined) {
												allowPush = false;
												return; // 跳出当前循环
											}
											// 只有当 recommendAgent 中不存在相同 id 的 agent 时才 push
											const exists = item.recommendAgent.some(a => a.id === event.id);
											if (!exists && allowPush && event.answer) {
												item.recommendAgent.push(event);
											}
										});
										if (item.recommendAgent && item.recommendAgent.length) {
											item.recommendAgentId = item.recommendAgent[0].id; // 当前智能体的id
											item.modelId = item.recommendAgent[0].id;
										}
									}
								}
							});
						}
					}
				}
				// 检测到noAnswer标志对话结束（有时候后端结束没有返回noAnswer，前端模拟统一处理，但是也有可能已经返回过了，所以根据是否已经处理过answerId来判断是否还要进行处理）
				if ((value === 'noAnswer' || value === 'complete') && !this.answerFromId) {
					answerFrom = [];
					this.messageList.forEach(item => {
						if (item.id === messageId) {
							item['answerEnd' + modelType] = true;
							if (value === 'noAnswer') {
								// jxj对应没有答案
								//  判断是否有值
								this.tempAgentList.forEach(event => {
									if (event.id === modelType) {
										event.answer = false; // 对应智能体没有答案
									}
								});
								item[modelType] = modelType;
							}
						}
					});
				}

				// 以前联调最后一句话会是对象结构，标志对话结束
				try {
					if (/^{/.test(value)) {
						let obj = JSON.parse(value);
						if (!this.isComponent && obj.sessionId) {
							this.sessionId = obj.sessionId;
						}
						if (
							(!this.isComponent || this.componentType === 'glob') &&
							this.currentMode.type === 'zdbk'
						) {
							answerFromId = obj.id;
						} else {
							answerFrom = [];
						}
						if (this.coosType === 'space') {
							// 我的空间有文档来源
							answerFromId = obj.id;
						}
					} else {
						text += value === 'noAnswer' || value === 'complete' ? '' : value;
					}
				} catch (err) {
					console.log('单次接受解析报错：', value);
					text += value;
				}
			});

			if (answerFromId) {
				this.answerFromId = answerFromId;
				let answerFromRes = await getAnswerFrom(answerFromId);
				if (answerFromRes.code === 200) {
					answerFrom = answerFromRes.result.answerFrom
						? JSON.parse(answerFromRes.result.answerFrom)
						: [];
				} else {
					this.$message.error(answerFromRes.message);
				}
			}
			this.messageList.forEach((item, index) => {
				if (item.id === messageId) {
					// 思考结束往答案中累积，未结束，往思考过程中加
					if (
						(item.recommendAgent && item.recommendAgent.length) ||
						(item.modelList && item.modelList.length)
					) {
						item.currentStep = 3; // 更新进度条
					}
					let newText;
					if (modelType) {
						// jxj特定模式
						let cacheText = this.jxjAnswer['cacheAnswer' + modelType] || '';
						newText = (item.startThink ? item.thinkCon : cacheText) + text;
					} else {
						newText = (item.startThink ? item.thinkCon : this.cacheAnswer) + text;
					}

					// 检测到了完整的开始标记，说明开始思考  并且替换掉标记
					if (/<think>/gi.test(newText)) {
						// 有深度思考
						item.isThink = true;
						item.startThink = true;
						newText = newText.replace(/<think>/gi, '');
						// 深度思考的答案赋值给思考内容
						item.thinkCon = newText;
					}
					// 检测到了完整的结束标记，说明思考结束  并且替换掉标记
					else if (/<\/think>/gi.test(newText)) {
						item.startThink = false;
						let i = newText.indexOf('</think>');
						// 思考结束前属于思考过程
						item.thinkCon = newText.slice(0, i);
						// 思考结束后属于答案
						if (modelType) {
							// jxj特定模式
							this.jxjAnswer['cacheAnswer' + modelType] = newText.slice(i + 8);
						} else {
							this.cacheAnswer = newText.slice(i + 8);
						}
					}
					// 如果思考并未结束，返回内容为思考的内容
					else if (item.startThink) {
						item.thinkCon = newText;
					} else {
						if (modelType) {
							// jxj特定模式
							this.jxjAnswer['cacheAnswer' + modelType] = newText;
						} else {
							this.cacheAnswer = newText;
						}
					}
					if (this.jxjAnswer['cacheAnswer' + modelType]) {
						//jxj
						// 新版本，支持各种答案混合渲染，可能都是通过流式慢输出的字符串，自己解析
						let answerMulti = this.handleMultiAnswer(
							this.jxjAnswer['cacheAnswer' + modelType],
							item.id
						);
						// 减少渲染负担，如果正在转换就不重复赋值
						answerMulti[0].currentStep = item.currentStep;
						answerMulti[0].hasStep = item.hasStep;
						if (this.transformIndex < 2) {
							item['answerMulti' + modelType] = answerMulti;
						}
					}
					if (this.cacheAnswer) {
						// 新版本，支持各种答案混合渲染，可能都是通过流式慢输出的字符串，自己解析
						let answerMulti = this.handleMultiAnswer(this.cacheAnswer, item.id);
						// 减少渲染负担，如果正在转换就不重复赋值
						answerMulti[0].currentStep = item.currentStep;
						answerMulti[0].hasStep = item.hasStep;
						if (this.transformIndex < 2) {
							item.answerMulti = answerMulti;
						}
					}
					if (Array.isArray(answerFrom)) {
						// 获取推荐问题
						let recommendedQuestions = answerFrom.reduce((base, form) => {
							base = base.concat(form.questions || []);
							return base;
						}, []);
						if (recommendedQuestions.length) {
							item.recommendedQuestions = [...new Set(recommendedQuestions)].slice(0, 3);
						}
						answerFrom = answerFrom.concat(item.answerFrom);
						// 处理文档来源
						item.answerFrom = answerFrom.map(form => {
							if (form.disk_file && form.disk_file.links) {
								let links = JSON.parse(form.disk_file.links);
								form.disk_file.links = links.filter(link => {
									return link.pc;
								});
								form.openLink = true; // 如果有三方链接，设一个收起打开状态
							}
							if (!form.disk_file) {
								form.disk_file = {};
							}
							if (form.fileName) {
								form.disk_file.name = form.fileName;
							}
							if (form.spaceName) {
								form.disk_file.spaceName = form.spaceName;
							}
							if (form.createByName) {
								form.disk_file.createByName = form.createByName;
							}
							if (form.createTime) {
								form.disk_file.createTime = form.createTime;
							}
							if (form.fileUrlPath) {
								form.disk_file.fileUrlPath = form.fileUrlPath;
							}
							return form;
						});
						// jxj
						if (modelType) {
							item['cacheAnswer' + modelType] = this.jxjAnswer['cacheAnswer' + modelType];
							this.jxjAnswer['cacheAnswer' + modelType] = ''; // 清空缓存
						} else {
							item.answer = this.cacheAnswer;
							this.cacheAnswer = ''; // 清空缓存
						}
						item.isAll =
							this.checkList.length === 0 || // 未选中知识库，直接打模型对话的情况
							this.checkList.length === this.knowledge.length || // 选中全部知识库查找的情况
							isAll; // 指定知识库未查找，传参isAll从全部查找的情况
						if (!modelType) {
							item.answerDone = true;
							item.currentStep = 4; // 更新进度条
							this.IsSend = false;
						}
						item.answerFromDone = true;
						// 没有答案的时候要判断是解析出错还是没有文档来源
						if (modelType) {
							//   jxj特定模式
						} else {
							if (!item.answer) {
								if (this.currentMode.type === 'zdbk' && this.searchType === 'isKnowledge') {
									// 来源为空，知识库有选中，说明在知识库中没有找到答案
									// 来源不是空的/知识库未选中（直接对话大模型），说明解析出粗
									item.answer =
										isEmpty(item.answerFrom) && this.checkList && this.checkList.length > 0
											? item.isAll
												? `很抱歉，小助手在全部知识库中，未查找到"${message}"相关内容。`
												: `很抱歉，小助手在你指定的知识库中，未查找到"${message}"相关内容。点击下方“全部查询”按钮，${this.coosTitle}将为你在全部知识库中进行查询。`
											: '解析出错，请及时联系管理员，并打开F12复制错误发送管理员！';
								} else {
									item.answer = `很抱歉，小助手未查找到"${message}"相关内容`;
								}
							}
						}
					}
					this.setJxjAnswer(item, index);
				}
			});
			if (!this.lockAutoScroll) {
				this.toBottom();
			}
		},
		//  保存历史
		async saveAnswer(item) {
			if (!this.sessionId) {
				let res = await createMessage({
					title: item.query,
					type: this.currentMode.sessionTypes
				});
				if (res.code === 200) {
					this.sessionId = res.result || '';
				} else {
					this.$message.error(res.message);
				}
			}
			//  所有智能体
			let tempAgentList = item.tempAgentList || [];
			let agentIdStr = tempAgentList.map(agent => agent.id).join(',');
			let agentNames = tempAgentList.map(agent => agent.agentName).join(',');
			let executionPath = {
				agentIds: agentIdStr,
				agentNames: agentNames
			};
			//  有答案的智能体
			if (item.recommendAgent && item.recommendAgent.length) {
				executionPath.actualAgentIds = item.recommendAgent.map(agent => agent.id).join(',');
				executionPath.actualAgentNames = item.recommendAgent
					.map(agent => agent.agentName)
					.join(',');

				executionPath.info = tempAgentList
					// eslint-disable-next-line no-prototype-builtins
					.filter(event => item.hasOwnProperty('executionPath' + event.id))
					.map(event => item['executionPath' + event.id]);
			} else {
				executionPath.actualAgentIds = 'chat,kimi';
				executionPath.actualAgentNames = 'chat,kimi';
			}

			let obj = {
				query: item.query,
				answer: JSON.stringify({ dataJxjInterpretationJson: item, executionPath: executionPath }),
				sessionId: this.sessionId
			};
			let res = await saveAiQueryAnswer(obj);
			if (res.code !== 200) {
				this.$message.error(res.message);
			}
			// 更新历史记录
			this.getChatRecords();
		},
		setPromiseA(index) {
			this.promiseA = null;
			//  智能体加载完成 有答案
			this.messageList[index].currentStep = 4;
			this.saveAnswer(this.messageList[index]);
			this.IsSend = false;
			this.messageList[index].answerDone = true;
		},
		setJxjAnswer(item, index) {
			let newIsAnswer = this.tempAgentList?.every(agent => {
				const answerEndKey = 'answerEnd' + agent.id;
				return item[answerEndKey] === true;
			});
			let newModelArr = this.modelArr?.every(agent => {
				const answerEndKey = 'answerEnd' + agent.id;
				return item[answerEndKey] === true;
			});
			if (newIsAnswer && item.recommendAgent.length === 0 && this.jxjAnswerKey === 1) {
				this.jxjAnswerKey += 1;
				this.jxjAiInternet(item, item.id, item.query, this.modelArr);
			}
			if (newIsAnswer && item.recommendAgent.length > 0) {
				// 必须等待拓展列表请求完毕，但又不是同步，因为接口可能会很慢
				try {
					this.promiseA.then(r => {
						this.setPromiseA(index);
					});
				} catch (e) {
					this.setPromiseA(index);
				}
			}
			if (newModelArr && item.modelList && item.modelList.length > 0) {
				// 必须等待拓展列表请求完毕，但又不是同步，因为接口可能会很慢
				try {
					this.promiseA.then(r => {
						this.setPromiseA(index);
					});
				} catch (e) {
					this.setPromiseA(index);
				}
			}
			if (this.modelId) {
				item.modelId = this.modelId;
			}
			if (this.searchType === 'IsInternetSearch' || (this.fileQuery && this.fileQuery.id)) {
				item.isShowDgg = false;
			} else {
				item.isShowDgg = true;
			}
			// 这样存储，产生了新的堆内存地址
			this.$set(this.messageList, index, {
				...item
			});
		},
		/**获取特殊渲染的标题(2可能有图表、表格、文字，但是目前没有存储聊天记录，如果有需要再联调)*/
		getSpecialTitle(answerType) {
			let title = '';
			switch (answerType) {
				case 2: {
					title = '表格';
					break;
				}
				case 3: {
					title = '为你推荐以下应用';
					break;
				}
				case 'MULTI': {
					title = '为你推荐以下应用';
					break;
				}
			}
			return title;
		},
		/**判断是不是特殊渲染数据*/
		getIsSpecial(result_json) {
			// 只有智控模式  1表单  2列表  只有列表需要特殊判断，很多种数据格式
			let isSpecialAnswer = Array.isArray(result_json) || result_json.functionType !== 2; // 默认其他类型为true
			// 答案不是普通答案并且不是模式2 如果是模式2就要满足多数据模式
			if (!isSpecialAnswer) {
				if (Array.isArray(result_json.data) && result_json.data.length > 0) {
					isSpecialAnswer =
						result_json.data.length > 1 || Object.keys(result_json.data[0]).length > 1;
				} else {
					isSpecialAnswer = false;
				}
			}
			return isSpecialAnswer;
		},
		/**处理字符串md中包含多种类型的答案*/
		handleMultiAnswer(text, id, otherConfig) {
			// 使用正则表达式匹配 ````chart` 包裹的内容
			const chartRegex = /```chart\n([\s\S]*?)\n```/g;
			const segments = [];
			let lastIndex = 0;
			let match;

			// 遍历所有匹配的 ````chart` 部分
			while ((match = chartRegex.exec(text)) !== null) {
				// 添加 ````chart` 之前的部分
				if (match.index > lastIndex) {
					segments.push({
						answerType: 'normal',
						answer: text.slice(lastIndex, match.index),
						id: id + '-' + segments.length
					});
				}

				try {
					// 尝试解析 JSON 内容
					// const chartData = JSON.parse(match[1]);
					const chartData = eval('(' + match[1] + ')');
					let options = commonEchartsOptions(chartData);
					segments.push({
						answerType: 'chart',
						answer: options,
						height: (options?.customHeight || 0) + (otherConfig?.echartsHeight || 400) + 'px', // 画布的高度
						hideUtils: true,
						id: id + '-' + segments.length
					});
				} catch (e) {
					console.log('解析图表报错-------------------', match[1], e);
					// 如果解析失败，保留原始字符串
					segments.push({
						answerType: 'normal',
						answer: '【图表数据转换失败】',
						id: id + '-' + segments.length
					});
				}

				lastIndex = match.index + match[0].length;
			}

			// 检查是否有未闭合的chart标记
			const lastChartIndex = text.lastIndexOf('```chart');
			const lastCloseIndex = text.lastIndexOf('```');
			let hasNoClose = false;
			// 如果有开始的chart标记但没有对应的结束标记
			if (lastChartIndex > -1 && lastChartIndex >= lastCloseIndex) {
				// 只处理到最后一个完整chart之前的部分
				hasNoClose = true;
			}

			// 添加最后一个 ````chart` 之后的部分
			if (lastIndex < text.length) {
				segments.push({
					answerType: 'normal',
					answer: hasNoClose
						? text.slice(lastIndex, lastChartIndex).replace(/\\n/gi, '')
						: text.slice(lastIndex),
					id: id + '-' + segments.length
				});
			}
			if (hasNoClose) {
				this.transformIndex += 1;
				segments.push({
					answerType: 'normal',
					answer: '【图表数据转换中，请稍后...】',
					id: id + '-' + segments.length
				});
			} else {
				this.transformIndex = 0;
			}
			return segments;
		}
	}
};
