<template>
	<div class="extend">
		<div class="header">扩展阅读：</div>
		<div class="list">
			<div v-for="(item, index) in answerExtendList" :key="index" class="list-item">
				<div class="list-item-header">
					<div class="list-item-header-left">
						<img src="../../../../assets/images/coos/extend.png" alt="" class="img" />
						<div class="title">{{ item.query }}</div>
					</div>
					<div class="list-item-header-right" @click="toggleExpand(index)">
						<div>{{ expandedIndex === index ? '收起' : '展开' }}</div>
						<i :class="expandedIndex === index ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
					</div>
				</div>
				<div class="list-item-dec" :class="{ expanded: expandedIndex === index }">
					<span v-if="!expandedIndex !== index">{{ truncateText(item.answer) }}</span>
					<span v-else>{{ item.answer }}</span>
					<span
						v-if="shouldTruncate(item.answer)"
						class="detail-link"
						@click="toggleExpandDiaLog(item)"
					>
						{{ expandedIndex === index ? '展开详情' : '' }}
					</span>
				</div>
			</div>
		</div>
		<el-dialog
			:title="details.query"
			:visible.sync="dialogVisible"
			width="60%"
			:before-close="handleClose"
		>
			<div class="dialog-dec">{{ details.answer }}</div>
		</el-dialog>
	</div>
</template>
<script>
export default {
	name: 'MessageExtend',
	props: {
		answerExtendList: {
			type: Array,
			default() {
				return [];
			}
		}
	},
	data() {
		return {
			dialogVisible: false,
			details: {},
			expandedIndex: 0 // 记录当前展开项的索引
		};
	},
	mounted() {},
	methods: {
		truncateText(text) {
			if (this.isTextTruncated(text)) {
				const lines = text.split('\n');
				return lines.slice(0, 5).join('\n') + '...';
			}
			return text;
		},
		shouldTruncate(text) {
			const lines = text.split('\n');
			return lines.length > 5;
		},
		isTextTruncated(text) {
			const lines = text.split('\n');
			return lines.length > 5;
		},
		toggleExpand(index) {
			this.expandedIndex = this.expandedIndex === index ? null : index;
		},
		toggleExpandDiaLog(item) {
			this.details = item;
			this.dialogVisible = true;
		},
		handleClose() {
			this.dialogVisible = false;
		}
	}
};
</script>

<style scoped lang="scss">
.extend {
	padding: 0px 30px;
	width: 100%;
	.header {
		padding: 10px 0;
		border-top: 1px solid #c0d3dd;
		width: 100%;
		font-family: PingFangSC, PingFang SC;
		font-weight: 400;
		font-size: 16px;
		color: #2f446b;
		line-height: 22px;
	}
	.list {
		&-item {
			padding: 12px 0;
			&-header {
				display: flex;
				justify-content: space-between;
				align-items: center;
				color: #187bf3;
				&-left {
					display: flex;
					align-items: center;
					flex: 1;
					.img {
						width: 20px;
						margin-right: 10px;
					}
					.title {
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						font-size: 14px;
						line-height: 20px;
						@include aLineEllipse;
					}
				}
				&-right {
					cursor: pointer;
					font-size: 14px;
					display: flex;
					align-items: center;
					justify-content: space-between;
					width: 50px;
				}
			}
			.list-item-dec {
				margin-top: 10px;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 14px;
				color: #737a94;
				line-height: 28px;
				overflow: hidden;
				position: relative;
				max-height: 0;
				@include countEllipse(5);
				transition: max-height 0.3s ease-out;
				&.expanded {
					max-height: 500px; // 设置一个足够大的值以容纳内容
					transition: max-height 0.3s ease-in;
				}
			}
			.detail-link {
				color: #187bf3; /* 与主题色保持一致 */
				cursor: pointer;
				margin-left: 5px;
			}
		}
	}
}
.dialog-dec {
	font-family: PingFangSC, PingFang SC;
	font-weight: 400;
	font-size: 14px;
	max-height: 600px;
	overflow-y: auto;
	color: #737a94;
	line-height: 28px;
}
</style>
