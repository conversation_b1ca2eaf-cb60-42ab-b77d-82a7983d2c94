<template>
	<div class="doc">
		<div
			v-for="(detail, detailIndex) of newArr"
			:key="detail.id"
			:style="{ paddingBottom: newArr.length - 1 === detailIndex ? '0' : '16px' }"
			class="doc-item"
		>
			<div class="doc-item-detail">
				<div class="doc-item-detail-title">
					<div>
						<span>{{ detailIndex + 1 }}.</span>
					</div>
					<i class="coos-iconfont icon-cz-gzzd icon"></i>
					<el-tooltip
						effect="light"
						:open-delay="500"
						placement="bottom"
						class="text"
						:content="detail.disk_file ? detail.disk_file.name : '未知来源'"
					>
						<span>
							{{ detail.disk_file ? detail.disk_file.name : '未知来源' }}
						</span>
					</el-tooltip>
				</div>
				<div class="doc-item-detail-button" @click="toDetail(detail)">查看详情</div>
			</div>
			<el-tooltip
				effect="light"
				:open-delay="500"
				placement="bottom"
				class="doc-item-text"
				:content="detail.page_content"
			>
				<div slot="content" style="max-width: 300px">{{ detail.page_content }}</div>
				<span>{{ detail.page_content }}</span>
			</el-tooltip>
			<div v-if="detail.content" class="doc-dec">
				{{ detail.content }}
			</div>
			<div v-if="detail.disk_file" class="doc-item-source">
				<div class="doc-item-source-block">
					<i class="coos-iconfont icon icon-laiyuan"></i>
					<span class="title">来源：</span>
					<span class="value">
						{{ detail.disk_file.businessFrom || detail.disk_file.spaceName }}
					</span>
				</div>
				<div v-if="!detail.disk_file.isFromBusiness" class="doc-item-source-block">
					<i class="coos-iconfont icon icon-yonghu"></i>
					<span class="title">所有人：</span>
					<span class="value">{{ detail.disk_file.createByName }}</span>
				</div>
				<div class="doc-item-source-block">
					<i class="coos-iconfont icon icon-shijian1"></i>
					<span class="title">日期：</span>
					<span class="value">
						{{
							new Date(detail.disk_file.updateTime || detail.disk_file.createTime).toLocaleString()
						}}
					</span>
				</div>
			</div>
			<div
				v-if="detail.disk_file && detail.disk_file.links && detail.disk_file.links.length"
				class="link"
			>
				<div class="link-title" @click="detail.openLink = !detail.openLink">
					<span class="link-title-text">{{ detail.disk_file.links.length }}条相关链接</span>
					<span v-if="detail.openLink" class="coos-iconfont link-title-icon">&#xe762;</span>
					<span v-else class="coos-iconfont link-title-icon">&#xe760;</span>
				</div>
				<div v-if="detail.openLink">
					<div
						v-for="(link, linkIndex) of detail.disk_file.links"
						:key="linkIndex"
						class="link-item"
						@click="openLinkUrl(link)"
					>
						<div class="link-item-top">
							<span class="coos-iconfont link-item-top-icon">&#xe687;</span>
							<el-tooltip effect="light" :open-delay="500" placement="bottom" :content="link.title">
								<span class="link-item-top-title">{{ link.title }}</span>
							</el-tooltip>
						</div>
						<text class="link-item-desc">{{ link.remark }}</text>
					</div>
				</div>
			</div>
		</div>
		<!--				<div class="doc-item">-->
		<!--					<div class="doc-item-detail">-->
		<!--						<div class="doc-item-detail-title">-->
		<!--							<i class="coos-iconfont icon-zhuliucheng icon"></i>-->
		<!--							<span class="text">流程：资金报销流程</span>-->
		<!--						</div>-->
		<!--						<div class="doc-item-detail-button">发起流程</div>-->
		<!--					</div>-->
		<!--					<div class="doc-item-text">快速发起报销流程</div>-->
		<!--				</div>-->
	</div>
</template>
<script>
import { previewFile } from '@/utils';

export default {
	name: 'MessageSource',
	props: {
		item: {
			type: Object,
			default: () => {
				return {};
			}
		}
	},
	data() {
		return {};
	},
	computed: {
		newArr() {
			const modelId = this.item.modelId;
			let answerFromKey = 'answerFrom' + modelId;
			const answerFrom = this.item[answerFromKey] || [];
			let list;
			if (modelId) {
				list = answerFrom;
			} else {
				list = this.item.answerFrom || [];
			}
			// 深拷贝 list 避免修改原始数据
			list = JSON.parse(JSON.stringify(list));
			list.forEach(form => {
				if (form.disk_file && form.disk_file.links) {
					let links;
					try {
						links = JSON.parse(form.disk_file.links) || [];
					} catch (e) {
						links = form.disk_file.links || [];
					}
					form.disk_file.links = links.filter(link => {
						return link.pc;
					});
					form.openLink = true; // 如果有三方链接，设一个收起打开状态
				}
				if (!form.disk_file) {
					form.disk_file = {};
				}
				if (form.fileName) {
					form.disk_file.name = form.fileName;
				}
				if (form.spaceName) {
					form.disk_file.spaceName = form.spaceName;
				}
				if (form.createByName) {
					form.disk_file.createByName = form.createByName;
				}
				if (form.createTime) {
					form.disk_file.createTime = form.createTime;
				}
				if (form.fileUrlPath) {
					form.disk_file.fileUrlPath = form.fileUrlPath;
				}
				if (form.fromViewUrl) {
					form.disk_file.fromViewUrl = form.fromViewUrl;
				}
			});
			return list;
		}
	},
	methods: {
		/**查看详情*/
		toDetail(detail) {
			try {
				let urlJson = JSON.parse(detail.disk_file.fromViewUrl);
				if (urlJson.PC) {
					window.open(urlJson.PC);
					return;
				}
				throw new Error('没有Url');
			} catch (e) {
				// 直接预览
				if (detail.disk_file && detail.disk_file.fileUrlPath) {
					// 处理是否全路径
					// let url = /http/.test(detail.disk_file.fileUrlPath)
					// 	? detail.disk_file.fileUrlPath
					// 	: window.location.origin + detail.disk_file.fileUrlPath;
					previewFile(detail.disk_file.fileUrlPath);
				}
			}

			// 跳转文档所在地址
			// let { id, fileType, spaceType, parentId, spaceId } = detail.disk_file;
			// let params = { id, fileType, spaceType, parentId, spaceId, chooseIndex: '4' };
			// params = qs.stringify(params);
			// this.$router.push(`/document-content?${params}`);
		},
		openLinkUrl(item) {
			// todo 这儿暂时不需要处理全路径半路径问题  后续有后端需求改动后再处理
			if (item.pc.indexOf('http') != -1) {
				window.open(item.pc);
				return;
			}
			this.$router.push(`/other-system?url=${encodeURIComponent(item.pc || '')}`);
		}
	}
};
</script>
<style scoped lang="scss">
.doc {
	padding: 0 12px 24px;
	width: 100%;

	&-item {
		border-top: 1px solid #f0f0f0;
		padding: 12px 0 16px;
		width: 100%;

		&-detail {
			width: 100%;
			@include flexBox(space-between);

			&-title {
				flex: 1;
				font-size: 14px;
				font-weight: 800;
				color: $textColor;
				line-height: 22px;
				@include flexBox(flex-start);
				@include aLineEllipse;

				.icon {
					margin-right: 8px;
					font-size: 20px;
				}

				& > .text {
					flex: 1;
					cursor: pointer;
					@include aLineEllipse;
				}
			}

			&-button {
				margin-left: 8px;
				flex-shrink: 0;
				border-radius: 6px;
				border: 1px solid var(--brand-6);
				padding: 5px 15px;
				font-size: 14px;
				font-weight: 400;
				color: var(--brand-6);
				line-height: 22px;
				cursor: pointer;
			}
		}

		&-text {
			margin: 12px 0 16px 28px;
			//padding-right: 120px;
			font-size: 14px;
			font-weight: 400;
			color: $textColor;
			line-height: 22px;
			word-break: break-all;
			-webkit-background-clip: text;
			cursor: pointer;
			@include countEllipse(2);
		}

		&-source {
			padding-left: 28px;
			width: 100%;
			@include aLineEllipse;
			@include flexBox(flex-start);

			&-block {
				width: 33%;
				@include flexBox(flex-start);

				.icon {
					color: #96a0b4;
					font-size: 14px;
					line-height: 14px;
				}

				.title {
					font-size: 12px;
					font-weight: 400;
					color: #96a0b4;
					line-height: 20px;
					margin: 0 4px;
				}

				.value {
					font-size: 12px;
					font-weight: 400;
					color: $subTextColor;
					line-height: 20px;
					margin-right: 26px;
					flex: 1;
					@include aLineEllipse;
				}
			}
		}
	}
}
.link {
	padding-top: 8px;
	border-top: 1px dashed #e3ebf2;
	margin: 16px 0 0;
}
.link-title {
	display: flex;
	align-items: center;
	cursor: pointer;
}
.link-title-icon {
	font-size: 16px;
	color: var(--brand-6);
}
.link-title-text {
	font-weight: 400;
	font-size: 12px;
	color: var(--brand-6);
	line-height: 22px;
}
.link-item {
	margin-top: 8px;
	padding: 4px;
	background: #f7f7f7;
	border-radius: 3px;
}
.link-item-top {
	display: flex;
	align-items: center;
}
.link-item-top-icon {
	font-size: 16px;
	margin-right: 8px;
	color: $primaryTextColor;
}
.link-item-top-title {
	font-weight: 400;
	font-size: 12px;
	color: $primaryTextColor;
	line-height: 22px;
	cursor: pointer;
	@include aLineEllipse;
}
.doc-dec {
	font-size: 14px;
	font-weight: 400;
	color: #96a0b4;
	line-height: 20px;
	margin: 0 4px 12px;
}
</style>
