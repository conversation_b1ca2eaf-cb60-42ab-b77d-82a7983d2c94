<template>
	<div>
		<div class="chart">
			<chartView
				:ref="'chartView-' + itemIndex"
				:height="item.height || '400px'"
				:option="item.answer"
			></chartView>
		</div>
		<div v-if="!item.hideUtils" class="ech_btn">
			<el-button :disabled="disabled" :loading="loadingChart" @click="getChartByData">
				<span class="coos-iconfont status-icon">&#xe71c;</span>
				换张图表
			</el-button>
		</div>
	</div>
</template>
<script>
import chartView from '@/views/coos/components/coos-chart.vue';
import { getChartConfig } from '@/api/modules/coos';

export default {
	name: 'MessageChart',
	components: { chartView },
	props: {
		item: {
			type: Object,
			default: () => {
				return {};
			}
		},
		itemIndex: {
			type: Number,
			default: () => {
				return 0;
			}
		}
	},
	data() {
		return {
			disabled: false,
			loadingChart: false
		};
	},
	methods: {
		getImage() {
			let key = `chartView-` + this.itemIndex;
			return this.$refs[key].getImage();
		},
		saveImage() {
			let key = `chartView-` + this.itemIndex;
			this.$refs[key].saveImage();
		},
		/**通过数据获取图表配置*/
		getChartByData() {
			this.loadingChart = true;
			let data = {
				data: this.item.answer.data || [],
				haveChats: this.item.haveChats ? this.item.haveChats.join(',') : '',
				message: this.item.answerType === 'chart' ? '换一种echart图表' : '生成echart图表',
				sessionId: this.sessionId
			};
			getChartConfig(data).then(res => {
				this.loadingChart = false;
				this.disabled = true;
				if (res.code === 200) {
					let message =
						this.item.haveChats && this.item.haveChats.length
							? '不支持更多类型的图表！'
							: '该数据不支持图表生成！';
					console.log('-----message', message);
					this.$emit('addMessage', res.result.chartData || message, this.item.haveChats || []);
				}
			});
		}
	}
};
</script>

<style scoped lang="scss">
.chart {
	width: 100%;
}
.ech_btn {
	padding-top: 20px;
}
</style>
