<template>
	<div ref="container" style="user-select: text">
		<v-md-preview :text="processedAnswer"></v-md-preview>
	</div>
</template>

<script>
export default {
	name: 'MessageNormal',
	props: {
		item: {
			type: Object,
			default: () => ({})
		}
	},
	computed: {
		processedAnswer() {
			return this.item.answer.replace(/@(\d+)@/g, (match, id) => {
				return `<a href="javascript:void(0)" class="source-tag" data-source-id="${id}">${id}</a>`;
			});
		}
	},
	watch: {
		processedAnswer() {
			this.$nextTick(() => {
				this.bindClickEvents();
			});
		}
	},
	mounted() {
		this.bindClickEvents();
	},
	methods: {
		bindClickEvents() {
			const container = this.$refs.container;
			if (!container) return;

			container.querySelectorAll('.source-tag').forEach(link => {
				link.addEventListener('click', e => {
					e.preventDefault();
					const sourceId = e.target.getAttribute('data-source-id');
					if (sourceId) {
						this.$emit('source-click', sourceId);
					}
				});
			});
		}
	}
};
</script>

<style scoped lang="scss">
::v-deep .github-markdown-body {
	padding: 0 !important;
	font-size: 14px;

	& table {
		border-radius: 6px;
		& tr:nth-child(2n) {
			background: #ffffff;
		}
		& thead {
			background: linear-gradient(90deg, var(--brand-2) 0%, var(--brand-1) 100%, #ffffff 100%);
			& tr {
				background: transparent;
			}
		}
	}
}

// 新增的来源标签样式，不干扰原有 markdown 样式
::v-deep .source-tag {
	display: inline-block;
	width: 20px;
	height: 20px;
	border-radius: 50%;
	background-color: #a5c3e7;
	color: white;
	font-size: 12px;
	text-align: center;
	line-height: 20px;
	margin-left: 4px;
	text-decoration: none;
	cursor: pointer;
	user-select: none;
	transition: background-color 0.2s ease;

	&:hover {
		background-color: #777;
	}
}
</style>
