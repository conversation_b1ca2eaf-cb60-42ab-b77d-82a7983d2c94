<template>
	<div class="multi">
		<div v-for="(answerItem, answerIndex) of item.answer" :key="answerIndex" class="multi-item">
			<div class="multi-item-top">
				<img
					v-if="answerItem.functionLogoUrl"
					:src="answerItem.functionLogoUrl"
					class="multi-item-icon"
				/>
				<svg-icon v-else icon-class="default-icon" class="multi-item-icon"></svg-icon>
				<div class="multi-item-con">
					<div class="multi-item-title">
						<span>{{ answerItem.functionName }}</span>
						<div class="plugin-logo" :class="'plugin-logo-' + answerItem.functionType">
							{{
								answerItem.functionType === 1
									? '智能填表'
									: answerItem.functionType === 2
									? '智能查询'
									: '功能直连'
							}}
						</div>
					</div>
					<div class="multi-item-desc">{{ answerItem.functionDigest }}</div>
				</div>
			</div>
			<div class="multi-item-button" @click="toApplication(answerItem)">
				{{
					answerItem.functionType === 1
						? '打开表单'
						: answerItem.functionType === 2
						? '立即查询'
						: '进入应用'
				}}
			</div>
		</div>
	</div>
</template>
<script>
import { preUrl } from '@/config';

export default {
	name: 'MessageApplication',
	props: {
		item: {
			type: Object,
			default: () => {
				return {};
			}
		}
	},
	data() {
		return {};
	},
	methods: {
		/**进入应用*/
		toApplication(answerItem) {
			console.log(answerItem, 'answerItemanswerItem');
			let { functionType, functionId, functionLogoUrl, functionName } = answerItem;
			switch (functionType) {
				// 表单
				case 1: {
					this.$emit('addMoreApplicationAnswer', this.item.query, functionId);
					break;
				}
				// 表格
				case 2: {
					break;
				}
				// 应用
				case 3: {
					this.$confirm('请确认是否打开此应用?', '提示', {
						confirmButtonText: '确定',
						cancelButtonText: '取消',
						type: 'warning'
					})
						.then(() => {
							let { PC, openType = 1, isExternal } = answerItem.data;
							if (isExternal) {
								let url = PC || '';
								let mainUrl = /http/gi.test(url) ? url : (preUrl || window.location.origin) + url;
								if (openType === 1) {
									this.$router.push(
										`/other-system?url=${encodeURIComponent(
											mainUrl || ''
										)}&name=${functionName}&logoUrlPath=${functionLogoUrl}`
									);
								} else {
									window.open(PC);
								}
							} else {
								this.$router.push(PC);
							}
						})
						.catch(() => {});
					break;
				}
			}
		}
	}
};
</script>

<style scoped lang="scss">
.multi {
	margin: -20px -12px;
}

.multi-item {
	padding: 8px 12px 20px;
	display: flex;
	align-items: flex-end;
	justify-content: space-between;
}
.multi-item-top {
	display: flex;
	align-items: center;
}
.multi-item-icon {
	margin-right: 8px;
	width: 40px;
	height: 40px;
}
.multi-item-con {
}
.multi-item-title {
	font-weight: 500;
	font-size: 16px;
	color: $textColor;
	line-height: 24px;
	text-align: left;
	font-style: normal;
	text-transform: none;
	@include flexBox(flex-start);
}
.multi-item-desc {
	font-weight: 400;
	font-size: 12px;
	color: $subTextColor;
	line-height: 20px;
	text-align: left;
	font-style: normal;
	text-transform: none;
}
.multi-item-button {
	width: 86px;
	height: 32px;
	border-radius: 6px;
	border: 1px solid var(--brand-6);
	display: flex;
	align-items: center;
	justify-content: center;
	font-weight: 400;
	font-size: 14px;
	color: var(--brand-6);
	line-height: 22px;
	text-align: center;
	font-style: normal;
	text-transform: none;
	cursor: pointer;
}
.plugin-logo {
	border-radius: 3px;
	padding: 4px 5px;
	font-weight: 400;
	font-size: 12px;
	line-height: 12px;
	margin-left: 8px;
}
.plugin-logo-1 {
	background: rgba(0, 168, 112, 0.1);
	color: #00a870;
}
.plugin-logo-2 {
	background: rgba(116, 88, 228, 0.1);
	color: #7458e4;
}
.plugin-logo-3 {
	background: rgba(48, 136, 255, 0.1);
	color: #3088ff;
}
</style>
