<template>
	<div>
		<!--     数据格式为数组，单数据单字段     -->
		<div v-if="Array.isArray(item.answer.data) && item.answer.data.length > 0">
			{{ Object.entries(item.answer.data[0])[0][0] }}：{{
				Object.entries(item.answer.data[0])[0][1]
			}}
		</div>
		<!--     数据格式为对象     -->
		<div v-else-if="getOb(item.answer.data)">
			{{ Object.entries(item.answer.data)[0][0] }}：{{ Object.entries(item.answer.data)[0][1] }}
		</div>
		<!--     数据格式为字符串    -->
		<div v-else>很抱歉，小助手未查找到"{{ item.query }}"相关内容</div>
	</div>
</template>
<script>
export default {
	name: 'MessageKeyMap',
	props: {
		item: {
			type: Object,
			default: () => {
				return {};
			}
		}
	},
	data() {
		return {};
	},
	methods: {
		getOb(data) {
			return data instanceof Object;
		}
	}
};
</script>

<style scoped lang="scss"></style>
