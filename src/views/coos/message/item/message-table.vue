<template>
	<div>
		<el-table :border="true" :data="item.answer.data" style="width: 100%">
			<el-table-column
				v-for="itemData in tableThead(item.answer.data)"
				:key="itemData.field"
				:prop="itemData.field"
				:label="itemData.title"
			></el-table-column>
		</el-table>
		<div class="ech_btn">
			<el-button :disabled="disabled" :loading="loadingChart" @click="getChartByData">
				<span class="coos-iconfont status-icon">&#xe71c;</span>
				生成图表
			</el-button>
		</div>
	</div>
</template>
<script>
import { getChartConfig } from '@/api/modules/coos';

export default {
	name: 'MessageTable',
	props: {
		item: {
			type: Object,
			default: () => {
				return {};
			}
		}
	},
	data() {
		return {
			tableData: [],
			loadingChart: false,
			disabled: false
		};
	},
	methods: {
		tableThead(data) {
			let arr;
			if (data.length === 0) {
				arr = [];
			} else {
				arr = Object.keys(data[0]).map(key => {
					return {
						title: key,
						field: key
					};
				});
			}
			this.tableData = arr;
			return arr;
		},
		/**通过数据获取图表配置*/
		getChartByData() {
			this.loadingChart = true;
			let data = {
				data: this.item.answer.data || [],
				haveChats: this.item.haveChats ? this.item.haveChats.join(',') : '',
				message: this.item.answerType === 'chart' ? '换一种echart图表' : '生成echart图表',
				sessionId: this.sessionId
			};
			getChartConfig(data).then(res => {
				this.loadingChart = false;
				this.disabled = true;
				if (res.code === 200) {
					let message =
						this.item.haveChats && this.item.haveChats.length
							? '不支持更多类型的图表！'
							: '该数据不支持图表生成！';
					console.log('-----message', message);
					this.$emit('addMessage', res.result.chartData || message, this.item.haveChats || []);
				}
			});
		}
	}
};
</script>
<style scoped lang="scss">
.ech_btn {
	padding-top: 20px;
}
</style>
