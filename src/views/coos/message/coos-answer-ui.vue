<template>
	<div :class="{ 'mask-bg': showBig }" @click="showBig = false">
		<div>
			<div
				class="coos-answer"
				:class="{ componentBg: isComponent, bigUi: showBig, znwsStyleBox: znwsStyle }"
				:style="{
					border: isComponent ? '' : '1px solid #dce3e7'
				}"
				@click.stop
			>
				<div v-if="isTitle && (!isComponent || typeof item.answer === 'object')" class="title">
					<!--				<span>{{ item.title || 'COOS智能助手与你协同' }}</span>-->
					<span>{{ coosConfig.robotName || 'COOS智能助手' }}与你协同</span>
					<!--   item.isSpecialAnswer  pc端只有特殊渲染的表单才支持放大效果     -->
					<i
						v-if="item.isSpecialAnswer && (!item.answerType || item.answerType === 1)"
						class="coos-iconfont title-icon"
						:class="showBig ? 'icon-shouqiquanping' : 'icon-quanping'"
						@click="openBigIframe"
					></i>
				</div>
				<!-- 切换回答 推荐智能体回答 -->
				<div
					v-if="item.recommendAgent && item.recommendAgent.length > 0"
					class="recommend-item-box"
				>
					<!--          v-show="setShow(item, recommend)"-->

					<div
						v-for="(recommend, i) in item.recommendAgent"
						:key="i"
						:class="['recommend-item', { active: item.modelId == recommend.id }]"
						@click="handleRecommendChange(recommend)"
					>
						<img :src="urlHttp(recommend.logoUrlPath)" class="coos-iconfont item-icon" />
						<span class="item-name">{{ recommend.agentName }}</span>
					</div>
				</div>
				<!-- 切换回答 推荐模型回答 -->
				<div v-if="item.modelList && item.modelList.length > 0" class="recommend-item-box">
					<div
						v-for="(recommend, i) in item.modelList"
						:key="i"
						:class="['recommend-item', { active: item.modelId === recommend.id }]"
						@click="handleModelChange(recommend)"
					>
						<!--						<img-->
						<!--							v-if="recommend.id == 1"-->
						<!--							src="@/assets/them-coos/coos/ds.png"-->
						<!--							class="coos-iconfont item-icon"-->
						<!--						/>-->
						<!--						<img-->
						<!--							v-if="recommend.id == 2"-->
						<!--							src="../../../assets/images/coos/kimi.png"-->
						<!--							class="coos-iconfont item-icon"-->
						<!--						/>-->
						<span class="item-name">{{ recommend.name }}</span>
					</div>
				</div>
				<div class="con" :style="{ paddingBottom: contentPaddingBottom }">
					<!--   深度思考过程   -->
					<div v-if="item.isThink && item.thinkCon.trim()" class="think">
						<div class="think-title" @click="openThinkCon = !openThinkCon">
							<i class="coos-iconfont icon-shendusikaonew think-title-icon" />
							<span class="think-title-text">
								{{ item.startThink ? '思考中...' : '已深度思考' }}
							</span>
							<i v-if="openThinkCon" class="coos-iconfont think-title-icon icon-nav-top" />
							<i v-else class="coos-iconfont think-title-icon icon-nav-bottom" />
						</div>
					</div>
					<div v-if="item.thinkCon && openThinkCon && status" class="answer-text answer-think">
						{{ item.thinkCon }}
					</div>
					<div
						v-for="answerItem of renderAnswer"
						:id="'answer' + itemIndex"
						:key="answerItem.__uniqueKey"
						:ref="el => setAnswerRef(el, answerItem)"
						:class="{ maxHeight: showHandle && !status }"
						:style="
							answerItem.answerType === 'chart' ? '' : 'margin-top: 8px; padding-bottom: 20px'
						"
					>
						<div>
							<!--     普通字符串    -->
							<messageNormal
								v-if="typeof answerItem.answer === 'string'"
								:item="answerItem"
								@source-click="sourceClick"
							></messageNormal>
							<!--     列表特殊渲染     -->
							<messageTable
								v-else-if="answerItem.answerType === 2 && answerItem.isSpecialAnswer"
								:item="answerItem"
								v-on="$listeners"
							></messageTable>
							<!--     列表单数据渲染     -->
							<messageKeyMap
								v-else-if="answerItem.answerType === 2"
								:item="answerItem"
							></messageKeyMap>
							<!--     图表渲染    -->
							<messageChart
								v-else-if="answerItem.answerType === 'chart'"
								ref="messageChart"
								:item="answerItem"
								:item-index="itemIndex"
								v-on="$listeners"
							></messageChart>
							<!--   应用模式或者多种组合    -->
							<messageApplication
								v-else-if="['MULTI', 3].includes(answerItem.answerType)"
								:item="answerItem"
								v-on="$listeners"
							></messageApplication>
							<!--     表单渲染    -->
							<!--v-show="item.answer.data && (item.answer.data.PC || item.answer.data.APP)"-->
							<webviewBox
								v-else
								:ref="'webview-' + answerItem.answer.dataInstanceId"
								:style="{ height: showBig ? 'calc(90vh - 80px)' : '400px' }"
								:webview-config="{ dataInstanceId: answerItem.answer.dataInstanceId }"
								:view-url="answerItem.answer.data.PC || answerItem.answer.data.APP"
								@goBack="goBack"
							></webviewBox>
						</div>
					</div>
					<!--   动效   -->
					<span
						v-for="(text, i) of '...'"
						v-show="getAnswerDone(item)"
						:key="i"
						:style="{ animationDelay: i * 0.5 + 's' }"
						class="con-empty"
					>
						{{ text }}
					</span>
					<!--   收起过后的遮罩层   -->
					<div v-if="showHandle" :class="status ? '' : 'mask'"></div>
					<!--   工具栏   -->
					<div
						v-if="showHandleContent"
						class="con-button"
						:style="{ bottom: showSearchAll ? '60px' : '20px' }"
					>
						<div v-if="showHandle" class="con-handle" @click="changeStatus">
							{{ status ? '收起' : '展开' }}
						</div>
						<div
							v-if="newAnswerFrom.length"
							:key="newAnswerFrom.length"
							class="con-handle"
							@click="changeDocStatus"
						>
							{{ docStatus ? '收起来源' : '展开来源' }}
						</div>
					</div>
				</div>
				<!--    扩展阅读    -->
				<message-extend
					v-if="
						item.answerExtendList &&
						item.answerExtendList.length &&
						renderAnswer.length &&
						renderAnswer[0].answer
					"
					:answer-extend-list="item.answerExtendList"
				></message-extend>
				<!--  文档来源  -->
				<messageSource
					v-show="item.answerFromDone !== false && newAnswerFrom.length && docStatus"
					:key="newAnswerFrom.length"
					:item="item"
				></messageSource>
				<!--   如果不是历史记录 && 文档来源为空 && 不是从全部知识库中查询的时候   -->
				<div v-if="showSearchAll" class="select-all-button" @click="continueQuery">
					查询全部知识库
				</div>
			</div>

			<!--  大屏-弃用该逻辑  -->
			<!--			<iframe-popup-->
			<!--				v-if="item.answer.data"-->
			<!--				ref="bigScreen"-->
			<!--				:webview-config="{ dataInstanceId: item.answer.dataInstanceId }"-->
			<!--				:view-url="item.answer.data.PC || item.answer.data.APP"-->
			<!--				@goBack="goBack"-->
			<!--			></iframe-popup>-->
		</div>
	</div>
</template>
<script>
import webviewBox from '@/components/webview-box/index.vue';
// import iframePopup from '@/views/coos/components/coos-iframe-popup.vue';
import { mapGetters } from 'vuex';
import messageNormal from '@/views/coos/message/item/message-normal.vue';
import messageTable from '@/views/coos/message/item/message-table.vue';
import messageKeyMap from '@/views/coos/message/item/message-key-map.vue';
import messageChart from '@/views/coos/message/item/message-chart.vue';
import messageApplication from '@/views/coos/message/item/message-application.vue';
import messageSource from '@/views/coos/message/item/message-source.vue';
import { previewFile, urlHttp } from '@/utils';
import MessageExtend from '@/views/coos/message/item/message-extend.vue';
// import MessageStep from '@/views/coos/components/message-step.vue';
export default {
	name: 'CoosAnswerUi',
	components: {
		MessageExtend,
		// MessageStep,
		// iframePopup,
		webviewBox,
		messageNormal,
		messageTable,
		messageKeyMap,
		messageChart,
		messageApplication,
		messageSource
	},
	props: {
		isComponent: {
			type: Boolean,
			default: () => {
				return false;
			}
		},
		znwsStyle: {
			type: Boolean,
			default: () => {
				return false;
			}
		},
		isTitle: {
			type: Boolean,
			default: () => {
				return true;
			}
		},
		item: {
			type: Object,
			default: () => {
				return {};
			}
		},
		itemIndex: {
			type: Number,
			default: () => {
				return 0;
			}
		},
		sessionId: {
			type: String,
			default: () => {
				return '';
			}
		}
	},
	data() {
		return {
			showBig: false, // 大屏显示
			status: true,
			openThinkCon: true,
			answerRefs: {}, // 存储每个 answerItem 对应的 DOM 元素
			showHandle: false,
			modelId: null,
			answerExtendList: [],
			docStatus: false
		};
	},
	computed: {
		...mapGetters(['coosConfig']),
		newAnswerFrom() {
			const modelId = this.item.modelId;
			let answerFromKey = 'answerFrom' + modelId;
			const answerFrom = this.item[answerFromKey] || [];
			let list;
			if (modelId) {
				list = Array.isArray(answerFrom) ? answerFrom : [];
			} else {
				list = Array.isArray(this.item.answerFrom) ? this.item.answerFrom : [];
			}
			// 深拷贝 list 避免修改原始数据
			list = JSON.parse(JSON.stringify(list));
			list.forEach(form => {
				if (form.disk_file && form.disk_file.links) {
					let links;
					try {
						links = JSON.parse(form.disk_file.links) || [];
					} catch (e) {
						links = form.disk_file.links || [];
					}
					form.disk_file.links = links.filter(link => {
						return link.pc;
					});
					form.openLink = true; // 如果有三方链接，设一个收起打开状态
				}
				if (!form.disk_file) {
					form.disk_file = {};
				}
				if (form.fileName) {
					form.disk_file.name = form.fileName;
				}
				if (form.spaceName) {
					form.disk_file.spaceName = form.spaceName;
				}
				if (form.createByName) {
					form.disk_file.createByName = form.createByName;
				}
				if (form.createTime) {
					form.disk_file.createTime = form.createTime;
				}
				if (form.fileUrlPath) {
					form.disk_file.fileUrlPath = form.fileUrlPath;
				}
				if (form.fromViewUrl) {
					form.disk_file.fromViewUrl = form.fromViewUrl;
				}
			});
			return list;
		},
		renderAnswer() {
			const modelId = this.item.modelId;
			let answerMultiKey = 'answerMulti' + modelId;
			let answerKey = 'answer' + modelId;
			const answerMulti = this.item[answerMultiKey] || [];
			const answer = this.item[answerKey] ? this.item[answerKey] : this.item.answer;
			let list;
			if (modelId) {
				list = answerMulti && answerMulti.length ? answerMulti : [{ ...this.item, answer }];
			} else {
				list = this.item.answerMulti ? this.item.answerMulti : [this.item];
			}
			// const list = this.item.answerMulti ? this.item.answerMulti : [this.item];
			return list.map((item, index) => ({
				...item,
				height:
					item && item.answer && item.answer.customHeight
						? (item.answer.customHeight || 0) + 400 + 'px'
						: '400px', // 画布的高度
				__uniqueKey: `answer-item-${this.itemIndex}-${index}`
			}));
		},
		// 是否显示查询全部
		showSearchAll() {
			return !this.item.isHistory && this.newAnswerFrom.length === 0 && !this.item.isAll;
		},
		// 是否显示操作区域
		showHandleContent() {
			return this.showHandle || this.newAnswerFrom.length;
		},
		// 内容区域下边距
		contentPaddingBottom() {
			return this.showHandleContent && this.showSearchAll
				? '80px'
				: this.showHandleContent || this.showSearchAll
				? '50px'
				: '20px';
		},
		viewUrl() {
			return this.item.answer.data ? this.item.answer.data.PC || this.item.answer.data.APP : '';
		}
	},
	watch: {
		'item.startThink'(newVal, oldVal) {
			if (oldVal && !newVal) {
				this.openThinkCon = false;
			}
		},
		'item.query'(newVal) {
			if (this.znwsStyle) {
				this.answerExtendList = [];
			}
		},
		'item.answer'(newVal) {
			this.$nextTick(() => {
				const key = this.item.id || this.item._uid; // 确保唯一 key
				const el = this.answerRefs[key];

				if (el) {
					const height = el.clientHeight;
					console.log('高度:', height);
					if (height > 66 && typeof this.item.answer === 'string') {
						this.showHandle = true;
					}
				}
			});
		}
	},
	mounted() {
		if (
			document.getElementById('answer' + this.itemIndex).clientHeight > 66 &&
			typeof this.item.answer === 'string'
		) {
			this.showHandle = true;
		}
		this.openThinkCon = !this.item.isHistory;
	},
	methods: {
		urlHttp,
		setAnswerRef(el, answerItem) {
			if (el) {
				this.answerRefs[answerItem.id || answerItem._uid] = el;
			}
		},
		setShow(item, listKey) {
			return item['answerEnd' + listKey.id] && !item[listKey.id];
		},
		getAnswerDone(item) {
			// if (item.modelId) {
			// 	return !item['answerDone' + item.modelId];
			// }
			return item.answerDone === false;
		},
		saveImage() {
			let refs = this.$refs.messageChart;
			// 多张表
			if (Array.isArray(refs)) {
				refs.forEach(ref => {
					ref.saveImage();
				});
			} else {
				refs.saveImage();
			}
		},
		sourceClick(sourceId) {
			let detail = this.newAnswerFrom[sourceId - 1];
			try {
				let urlJson = JSON.parse(detail.disk_file.fromViewUrl);
				if (urlJson.PC) {
					window.open(urlJson.PC);
					return;
				}
				throw new Error('没有Url');
			} catch (e) {
				// 直接预览
				if (detail.disk_file && detail.disk_file.fileUrlPath) {
					// 处理是否全路径
					// let url = /http/.test(detail.disk_file.fileUrlPath)
					// 	? detail.disk_file.fileUrlPath
					// 	: window.location.origin + detail.disk_file.fileUrlPath;
					previewFile(detail.disk_file.fileUrlPath);
				}
			}
		},
		/**返回刷新*/
		goBack() {
			// this.$refs.bigScreen && this.$refs.bigScreen.close();
			const webviewRef = this.$refs['webview-' + this.item.answer.dataInstanceId];
			const target = Array.isArray(webviewRef) ? webviewRef[0] : webviewRef;
			target?.updateWebview?.();
		},
		changeStatus() {
			this.status = !this.status;
		},

		changeDocStatus() {
			this.docStatus = !this.docStatus;
		},
		/**打开大屏webview*/
		openBigIframe() {
			this.showBig = !this.showBig;
			// this.$refs.bigScreen.open();
		},
		// 切换智能体回答
		handleRecommendChange(recommend) {
			// this.modelId = recommend.id;
			this.$emit('modelIdTypeChange', recommend.id);
			if (this.item.recommendAgentId === recommend.id) return;
			this.item.modelId = recommend.id;
			this.item.recommendAgentId = recommend.id;
		},
		handleModelChange(recommend) {
			// this.modelId = recommend.id;
			this.$emit('modelIdTypeChange', recommend.id);
			if (this.item.modelId === recommend.id) return;
			this.item.modelId = recommend.id;
		},
		/**继续问答*/
		continueQuery() {
			this.$emit('continueQuery', this.item.query);
		}
	}
};
</script>
<style scoped lang="scss">
.componentBg {
	background: var(--brand-1) !important;
}
.mask-bg {
	position: fixed;
	left: 0;
	top: 0;
	height: 100vh;
	width: 100vw;

	z-index: 999;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
}
.bigUi {
	width: 60vh;
	height: 90vh;
}
.coos-answer {
	@include flexBox(flex-start, flex-start);
	background: #ffffff;
	flex-direction: column;
	border-radius: 6px;
	position: relative;

	& > .title {
		@include flexBox(space-between);
		padding: 8px 12px;
		font-size: 16px;
		font-weight: 800;
		color: $primaryTextColor;
		line-height: 22px;
		width: 100%;
		background: linear-gradient(90deg, var(--brand-2) 0%, var(--brand-1) 100%, #ffffff 100%);
		//background: linear-gradient(
		//	90deg,
		//	rgba(213, 227, 255, 0.45) 0%,
		//	rgba(232, 244, 255, 0.45) 100%,
		//	rgba(229, 243, 255, 0.45) 100%
		//);

		.title-icon {
			font-size: 20rpx;
			color: $subTextColor;
			cursor: pointer;
		}
	}

	.maxHeight {
		max-height: 68px;
		overflow: hidden;
	}

	.con {
		padding: 20px 12px 0;
		font-size: 14px;
		font-weight: 400;
		color: $textColor;
		line-height: 22px;
		width: 100%;
		position: relative;
		overflow: hidden;

		&-empty {
			font-size: 28px;
			color: var(--brand-4);
			margin: 0 1px;
			animation: icon-loading 1s infinite;
		}

		.mask {
			background: linear-gradient(180deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 1) 100%);
			height: 68px;
			position: absolute;
			bottom: 50px;
			width: 100%;
			left: 0;
		}

		&-button {
			position: absolute;
			right: 12px;
			font-size: 14px;
			font-weight: 400;
			color: var(--brand-6);
			line-height: 22px;
			@include flexBox();
		}

		&-handle {
			cursor: pointer;
			margin-left: 8px;
		}
	}
	.select-all-button {
		border-radius: 6px;
		border: 1px solid var(--brand-6);
		padding: 5px 15px;
		font-size: 14px;
		font-weight: 400;
		color: var(--brand-6);
		line-height: 22px;
		text-align: center;
		margin-left: 8px;
		cursor: pointer;
		position: absolute;
		bottom: 10px;
		right: 10px;
		@include countEllipse(2);
	}
}

.think {
	display: flex;
	align-items: center;
}
.think-title {
	display: flex;
	cursor: pointer;
	border-radius: 6px;
	background: #f5f5f5;
	align-items: center;
	padding: 2px 8px;
}
.think-title-icon {
	font-size: 20px;
	color: $primaryTextColor;
}
.think-title-text {
	color: $primaryTextColor;
	font-weight: 400;
	font-size: 14px;
	line-height: 22px;
	margin: 0 4px;
}
.answer-think {
	margin-top: 12px;
	border-left: 1px solid #8b8b8b;
	color: #8b8b8b !important;
	padding-left: 13px;
}
.answer-text {
	font-weight: 400;
	font-size: 14px;
	color: #000000;
	line-height: 22px;
}
.recommend-item-box {
	margin: 23px 0 0 30px;
	display: flex;
	flex-direction: row;
	overflow-x: auto;
}

.recommend-item {
	padding: 8px 6px;
	background: rgba(24, 123, 243, 0.05);
	border-radius: 4px;
	display: flex;
	align-items: center;
	flex-direction: row;
	border: 1px solid transparent;
	margin-right: 8px;
	background: #ffffff;
	i {
		margin-right: 4px;
	}
	&.active {
		border-color: var(--brand-6);
		background: #ffffff;
		i {
			color: #187bf3;
		}

		.item-name {
			color: var(--brand-6);
		}
	}

	.item-icon {
		width: 20px;
		height: 20px;
		margin-right: 4px;
	}

	.item-name {
		font-family: PingFangSC, PingFang SC;
		font-weight: 500;
		font-size: 14px;
		color: #444444;
		text-align: justify;
	}
}
.znwsStyleBox {
	background: rgba(255, 255, 255, 0.6) !important;
	border-radius: 12px !important;
	border: 2px solid #ffffff !important;
	::v-deep .con {
		padding: 18px 30px;
	}
}
</style>
