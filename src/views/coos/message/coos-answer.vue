<template>
	<div class="final-answer-message">
		<img
			v-if="coosAvatar"
			class="coos-avatar"
			:src="require(`@/assets/${rentThem}/coos/coos-avatar.png`)"
			alt=""
		/>
		<div
			:class="{ znwsStyle: znwsStyle }"
			class="coos-answer-parent"
			:style="{
				width:
					item && item.answer && item.answer.data && (item.answer.data.PC || item.answer.data.APP)
						? '330px'
						: ''
			}"
		>
			<coosAnswerUi
				ref="coosAnswerUi"
				v-bind="$attrs"
				:item="item"
				:znws-style="znwsStyle"
				:item-index="itemIndex"
				:is-component="isComponent"
				@handleRefresh="refresh"
				v-on="$listeners"
			></coosAnswerUi>
			<!--          操作-->
			<div class="operation">
				<div class="operation-box" @click="handleRefresh">
					<img src="../../../assets/images/coos/refresh.png" alt="" class="operation-box-img" />
				</div>
				<div class="operation-box" @click="handleCopy">
					<img src="../../../assets/images/coos/copy.png" alt="" class="operation-box-img" />
				</div>
				<!--        -->
				<div
					v-if="item && item.isShowDgg && item.currentStep"
					:class="{ disabled: !item.answerDone }"
					class="operation-box"
					@click="handleDgg"
				>
					<i v-if="!item.isDgg" class="coos-iconfont operation-box-icon icon-dianzan_huaban"></i>
					<i v-else class="coos-iconfont operation-box-select icon-dianzan_kuai"></i>
				</div>
			</div>
			<!--			<div v-if="isPause" class="refresh" @click="refresh">-->
			<!--				<i class="coos-iconfont icon-refresh icon"></i>-->
			<!--				<div>重新生成</div>-->
			<!--			</div>-->
			<!--	更多推荐		-->
			<div v-if="newRecommendedQuestions.length" class="more-recommend">
				<span class="more-recommend-title">
					{{ znwsStyle ? '你可能还想问： ' : '更多相关问题： ' }}
				</span>
				<div
					v-for="(recommend, rIndex) of newRecommendedQuestions"
					:key="rIndex"
					class="more-recommend-item"
					@click="handleMoreRecommend(recommend)"
				>
					<div class="more-recommend-item-box">
						<img
							src="@/assets/them-coos/coos/recommend.png"
							alt=""
							class="more-recommend-item-box-img"
						/>
						<span class="more-recommend-text">{{ recommend }}</span>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import coosAnswerUi from '@/views/coos/message/coos-answer-ui.vue';
import { urlHttp } from '@/utils';
import { addDgg, delDgg } from '@/api/modules/coos-znwz';
export default {
	name: 'CoosAnswer',
	components: {
		coosAnswerUi
	},
	props: {
		/**待办的时候是组件模式*/
		isComponent: {
			type: Boolean,
			default: () => {
				return false;
			}
		},
		//  是否展示头像
		coosAvatar: {
			type: Boolean,
			default: () => {
				return true;
			}
		},
		item: {
			type: Object,
			default: () => {
				return {};
			}
		},
		znwsStyle: {
			type: Boolean,
			default: () => {
				return false;
			}
		},
		isPause: {
			type: Boolean,
			default: () => {
				return false;
			}
		},
		itemIndex: {
			type: Number,
			default: () => {
				return 0;
			}
		}
	},
	data() {
		return {};
	},
	computed: {
		newRecommendedQuestions() {
			const modelId = this.item.modelId;
			let recommendedQuestionsKey = 'recommendedQuestions' + modelId;
			const recommendedQuestions = this.item[recommendedQuestionsKey] || [];
			let list;
			if (modelId) {
				list = Array.isArray(recommendedQuestions) ? recommendedQuestions : [];
			} else {
				list = Array.isArray(this.item.recommendedQuestions) ? this.item.recommendedQuestions : [];
			}
			return list;
		},
		answer() {
			const modelId = this.item.modelId;
			let answerMultiKey = 'answerMulti' + modelId;
			let answerKey = 'answer' + modelId;
			const answerMulti = this.item[answerMultiKey] || [];
			const answer = this.item[answerKey] ? this.item[answerKey] : this.item.answer;
			let list;
			if (modelId) {
				list = answerMulti && answerMulti.length ? answerMulti : [{ ...this.item, answer }];
			} else {
				list = this.item.answerMulti ? this.item.answerMulti : [this.item];
			}
			// answerType normal
			const firstNormalAnswer = list.find(item => item.answerType === 'normal');
			return firstNormalAnswer.answer || '';
		}
	},
	mounted() {},
	methods: {
		urlHttp,
		handleDgg() {
			if (!this.item.answerDone) return;
			let fn = !this.item.isDgg ? addDgg : delDgg;
			//  所有智能体
			let tempAgentList = this.item.tempAgentList || [];
			let agentIdStr = tempAgentList.map(agent => agent.id).join(',');
			let agentNames = tempAgentList.map(agent => agent.agentName).join(',');
			let executionPath = {
				agentIds: agentIdStr,
				agentNames: agentNames
			};
			//  有答案的智能体
			if (this.item.recommendAgent && this.item.recommendAgent.length) {
				executionPath.actualAgentIds = this.item.recommendAgent.map(agent => agent.id).join(',');
				executionPath.actualAgentNames = this.item.recommendAgent
					.map(agent => agent.agentName)
					.join(',');

				executionPath.info = tempAgentList
					// eslint-disable-next-line no-prototype-builtins
					.filter(event => this.item.hasOwnProperty('executionPath' + event.id))
					.map(event => this.item['executionPath' + event.id]);
			} else {
				executionPath.actualAgentIds = 'chat,kimi';
				executionPath.actualAgentNames = 'chat,kimi';
			}
			let item = {
				...this.item,
				isDgg: !this.item.isDgg
			};
			fn({
				problem: this.item.query,
				answer: JSON.stringify({
					dataJxjInterpretationJson: item,
					executionPath: executionPath
				})
			}).then(res => {
				if (res.code === 200) {
					this.item.isDgg = !this.item.isDgg;
				} else {
					this.$message.error(res.message);
				}
			});
		},
		/**点击更多问题*/
		handleMoreRecommend(recommend) {
			this.$emit('handleMoreRecommend', '', recommend);
		},
		refresh() {
			this.$emit('refresh', this.item.query);
		},
		/**保存echar为图片*/
		saveImage() {
			this.$refs.coosAnswerUi.saveImage();
		},
		handleRefresh() {
			this.refresh();
		},
		handleCopy() {
			let text = this.answer || this.item.answer;
			text = text.split('```chart')[0].trim();

			if (navigator.clipboard) {
				navigator.clipboard
					.writeText(text)
					.then(() => {
						this.$message.success('复制成功！');
					})
					.catch(err => {
						console.error('复制失败: ', err);
						this.fallbackCopyText(text);
					});
			} else {
				this.fallbackCopyText(text);
			}
		},
		fallbackCopyText(text) {
			const textarea = document.createElement('textarea');
			textarea.value = text;
			document.body.appendChild(textarea);
			textarea.select();
			try {
				document.execCommand('copy');
				this.$message.success('复制成功');
			} catch (err) {
				this.$message.error('复制失败，请手动选择复制');
			} finally {
				document.body.removeChild(textarea);
			}
		},
		// 切换智能体回答
		handleRecommendChange(recommend) {
			if (this.item.recommendAgentId === recommend.id) return;
			this.$emit('agentTypeChange', this.item, recommend);
		}
	}
};
</script>

<style scoped lang="scss">
.final-answer-message {
	@include flexBox(flex-start, flex-start);
	margin-bottom: 14px;
	.coos-avatar {
		border-radius: 6px;
		margin: 0 10px 0 0;
		width: 40px;
		height: 40px;
	}
	.coos-answer-parent {
		width: 78%;
	}
	.empty {
		@include flexBox();
		font-size: 14px;
		width: 100%;
		height: 200px;
		color: #c3c3c3;
		flex-shrink: 0;

		&-text {
			animation: text-loading 1s infinite;
			margin: 0 1px;
		}
	}
}

.more-recommend {
	margin-top: 3px;
}
.more-recommend-title {
	font-weight: 400;
	font-size: 14px;
	color: #737a94;
	line-height: 22px;
}
.more-recommend-item {
	display: flex;
}
.more-recommend-item-box {
	cursor: pointer;
	margin-top: 8px;
	padding: 4px 12px;
	background: #ffffff;
	border-radius: 6px;
	display: flex;
	align-items: center;
	&-img {
		width: 20px;
		margin-right: 8px;
	}
}
.more-recommend-text {
	font-weight: 400;
	font-size: 14px;
	color: #2f446b;
	line-height: 22px;
}
.refresh {
	margin-top: 12px;
	width: 106px;
	height: 32px;
	background: #ffffff;
	border-radius: 6px 6px 6px 6px;
	border: 1px solid #d9e2ec;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	color: #2f446b;
	font-size: 14px;
	font-weight: 400;
	i {
		margin-right: 6px;
	}
}
.znwsStyle {
	width: 100% !important;
	.more-recommend {
		margin-top: 20px;
		padding-top: 20px;
		border-top: 1px solid #c0d3dd;
	}
}
.operation {
	margin: 8px 0;
	display: flex;
	&-box {
		margin-right: 15px;
		width: 24px;
		height: 24px;
		background: #ffffff;
		box-shadow: 0px 0px 12px 0px rgba(0, 0, 0, 0.06);
		border-radius: 6px;
		display: flex;
		align-items: center;
		justify-content: center;
		cursor: pointer;
		&-img {
			width: 18px;
		}
		&-icon {
			font-size: 18px;
			color: $primaryTextColor;
		}
		&-select {
			font-size: 18px;
			color: var(--brand-6);
		}
	}
}
.disabled {
	cursor: not-allowed;
}
</style>
