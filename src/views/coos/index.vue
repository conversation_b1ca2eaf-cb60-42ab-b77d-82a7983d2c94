<template>
	<div class="im-content">
		<div
			v-if="modeList.length > 0 || isComponent"
			class="message"
			:style="{ background: isComponent ? '#ffffff' : '#f3f5f6' }"
		>
			<div class="message-detail" :style="{ background: isComponent ? '#ffffff' : '#f3f5f6' }">
				<!-- 头部 -->
				<coosHeader
					:is-component="isComponent"
					:coos-title="coosTitle"
					:current-mode="currentMode"
					:file-query="fileQuery"
					:process-status="processStatus"
					v-on="$listeners"
					@clearFile="clearFile"
					@createNewChat="createNewChat"
				/>
				<!--  消息模块  -->
				<div id="coos-detail-con" ref="messageContent" class="message-detail-con">
					<div v-show="loading" class="load-more">
						<span
							v-for="(text, index) of '加载中...'"
							:key="index"
							:style="{ animationDelay: index * 0.5 + 's' }"
							class="load-more-text"
						>
							{{ text }}
						</span>
					</div>
					<coosWelcome
						v-show="!isComponent && !loading && messageList.length === 0"
						:mode-list="modeList"
						:current-mode="currentMode"
						:coos-title="coosTitle"
						:is-component="isComponent"
						:questions-list="questionsList"
						:question-loading="questionLoading"
						:questions-num="questionsNum"
						:current-plugin="currentPlugin"
						:current-data-drilling="currentPlugin"
						:plugin-or-data-drilling="pluginOrDataDrilling"
						@changeMode="changeMode"
						@otherQuestions="otherQuestions"
						@questionClick="send"
						@lookMore="lookMore"
						@clickSome="clickSome"
					></coosWelcome>
					<div
						v-for="(item, index) of messageList"
						:key="index"
						style="width: 100%"
						:style="{ marginTop: index === 0 ? FirstAnswerMargin : '' }"
						@contextmenu="contextmenu($event, item, index)"
					>
						<!--  提问的展示、选择知识库的展示  -->
						<CoosQuery
							v-if="item.type === 'query'"
							:ref="'message-item-' + index"
							:is-component="isComponent"
							:item="item"
							:index="index"
						></CoosQuery>
						<!--  选择知识库后的回答  -->
						<CoosAnswer
							v-else-if="item.type === 'answer'"
							:ref="'message-item-' + index"
							:is-component="isComponent"
							:item="item"
							:index="index"
							:session-id="sessionId"
							@addMoreApplicationAnswer="addMoreApplicationAnswer"
							@continueQuery="continueQuery"
							@addMessage="addMessage"
							@handleMoreRecommend="send"
						></CoosAnswer>
					</div>
				</div>
			</div>
			<!--  拖拽调整输入框大小  -->
			<div class="message-resize" @mousedown="resize"></div>
			<!--  工具栏  -->
			<div v-if="!isComponent" class="message-utils">
				<div class="message-utils-mode" @click.stop="showModePopup">
					<img class="message-utils-mode-icon" :src="currentMode.logoUrl" alt="" />
					<div>{{ currentMode.name }}</div>
				</div>
				<div v-show="currentMode.type === 'zdbk'" class="message-utils-select" @click.stop>
					<i class="coos-iconfont icon-caidanlan-bangong-zhishiku icon"></i>
					<span class="text" @click="showKnowledgeBase">
						{{ checkList.length ? `将从${checkList.length}个知识库中为你回答` : '选择知识库' }}
					</span>
				</div>
				<div v-show="['zklh', 'sjzx'].includes(currentMode.type)" class="message-utils-select">
					<i class="coos-iconfont icon-chajiangongneng zklh-icon"></i>
					<div v-if="currentPlugin || currentDataDrilling" class="plugin-choose">
						<div @click="lookMore">
							<span class="text">选择了</span>
							<span class="text plugin-text">
								{{
									currentMode.type === 'zklh' ? currentPlugin.name : currentDataDrilling.moduleName
								}}
							</span>
							<span class="text">{{ currentMode.type === 'zklh' ? '功能' : '业务数据' }}</span>
						</div>
						<span class="text plugin-reset" @click="handlePluginReset">重置</span>
					</div>
					<span v-else class="text" @click="lookMore">
						{{ currentMode.type === 'zklh' ? '请选择功能' : '请选择业务数据' }}
					</span>
				</div>
				<div class="quick-button">
					<i class="coos-iconfont icon-lishi quick-button-icon" @click="openHistory(false)"></i>
					<i class="coos-iconfont icon-qingli-L-copy quick-button-icon" @click="clear"></i>
				</div>
				<i
					v-show="currentMode.type === 'cyzh'"
					class="coos-iconfont icon-tupian2 more-icon"
					@click="pickFile('img')"
				></i>
				<div v-show="currentMode.type === 'cyzh'">
					<i class="coos-iconfont icon-dakai more-icon" @click="pickFile('file')" />
					<input
						ref="fileInput"
						type="file"
						:accept="accept"
						style="display: none"
						@change="onPickFile($event)"
					/>
				</div>
				<!--  模式切换  -->
				<div v-show="showModeChange" class="mode-popup">
					<div class="mode-title">{{ coosConfig.robotName || 'COOS助手' }}模式切换</div>
					<div class="mode-list">
						<div
							v-for="(item, index) of modeList"
							:key="index"
							class="mode-list-item"
							@click="changeMode(item)"
						>
							<img :src="item.logoUrl" alt="" class="mode-list-item-img" />
							<div class="mode-list-item-text">{{ item.name }}</div>
						</div>
					</div>
				</div>
				<!--  知识库选择  -->
				<div v-show="knowledgeBase" class="knowledge-base" @click.stop>
					<div class="knowledge-base-title">
						<span>已选择：</span>
						<span class="data">
							<span class="data-select">{{ checkList.length }}</span>
							/{{ knowledge.length }}个知识库
						</span>
						<span class="button" @click="clearCheck">清空</span>
					</div>
					<el-checkbox-group v-model="checkList" class="knowledge-base-con">
						<el-checkbox
							v-for="item of knowledge"
							:key="item.id"
							:label="item.id"
							:disabled="checkList.length === 1 && checkList.includes(item.id)"
							class="knowledge-base-item"
						>
							<i class="coos-iconfont icon-cz-gzzd icon"></i>
							<span class="title">
								{{ item.spaceName }}
							</span>
						</el-checkbox>
					</el-checkbox-group>
				</div>
			</div>
			<!--  输入内容  -->
			<div :class="isComponent ? 'send-input-content' : ''">
				<div v-if="isComponent" class="component-input">
					<!--				<i class="coos-iconfont icon-yuyin component-input-icon" style="margin-right: 8px"></i>-->
					<el-input
						v-model="sendCon"
						class="component-input-textarea"
						resize="none"
						type="textarea"
						:rows="1"
						placeholder="请输入问题"
						@keydown.native="preventEnter"
						@keyup.native="sendMessage"
					></el-input>
					<!--				<i class="coos-iconfont icon-add component-input-icon" style="margin-left: 8px"></i>-->
				</div>
				<div v-else class="message-send" :style="{ height: height + 'px' }">
					<el-input
						v-model="sendCon"
						class="message-send-input"
						resize="none"
						type="textarea"
						autosize
						placeholder="请输入问题"
						@keydown.native="preventEnter"
						@keyup.native="sendMessage"
					></el-input>
				</div>
				<div
					:class="isComponent ? '' : 'send-box-flot'"
					class="send-box"
					:style="{ borderLeft: isComponent ? 'none' : '' }"
				>
					<div class="send-btn" @click="send">发送</div>
					<el-popover ref="popover" placement="top-start" width="220" trigger="click">
						<div>
							<div class="send-select" @click="handleSendType(1)">
								<div class="sand-select-box">
									<i v-show="sendType === 1" class="coos-iconfont icon-dagou dagou"></i>
								</div>
								<div class="send-dec-text">按Enter键发送消息</div>
							</div>
							<div class="send-select" @click="handleSendType(2)">
								<div class="sand-select-box">
									<i v-show="sendType === 2" class="coos-iconfont icon-dagou dagou"></i>
								</div>
								<div class="send-dec-text">按Ctrl+Enter键发送消息</div>
							</div>
						</div>
						<div slot="reference" class="send-icon-box">
							<i class="el-icon-arrow-down send-icon"></i>
						</div>
					</el-popover>
				</div>
			</div>
			<!--  消息右键菜单  -->
			<div v-show="menuShow" class="menu" :style="{ top: menuTop + 'px', left: menuLeft + 'px' }">
				<div
					v-for="(item, index) of contextContent"
					:key="index"
					class="menu-item"
					@click="handleContext(item.type)"
				>
					{{ item.name }}
				</div>
			</div>
		</div>
		<div v-else class="empty-block">
			<BasicEmpty description="暂无功能" />
		</div>
		<coosHistory
			v-show="historyShow"
			ref="coosHistory"
			:history-key="historyKey"
			:session-id="sessionId"
			@changeSessionId="changeSessionId"
			@closeHistory="openHistory"
		></coosHistory>
		<PluginPopup
			ref="PluginPopup"
			:init-plugin="currentPlugin"
			@selectPlugin="selectPlugin"
		></PluginPopup>
		<dataDrillingPopup
			ref="dataDrillingPopup"
			:init-data-drilling="currentDataDrilling"
			@selectDataDrilling="selectDataDrilling"
		></dataDrillingPopup>
	</div>
</template>

<script>
import coosHeader from '@/views/coos/layout/coos-header.vue';
import coosWelcome from '@/views/coos/layout/coos-welcome.vue';
import queryMixins from '@/views/coos/utils/query-mixins';
import {
	delCoosHistory,
	getChangePluginInit,
	getCoosHistory,
	getKnowledge,
	getUUID,
	somePlugins,
	uploadCoosFile,
	getQuestionsList,
	getDataDrilling
} from '@/api/modules/coos';
import PluginPopup from '@/views/coos/components/coos-plugin-popup.vue';
import CoosQuery from '@/views/coos/message/coos-query.vue';
import CoosAnswer from '@/views/coos/message/coos-answer.vue';
import { mapGetters, mapMutations } from 'vuex';
import { debounce, isEmpty } from '@/utils';
import { copyText } from '@/wile-fire/ui/util/clipboard';
import dataDrillingPopup from '@/views/coos/components/coos-drilling-popup.vue';
import coosHistory from '@/views/coos/components/coos-history.vue';
import { getDictionary } from '@/utils/data-dictionary';
import { preUrl } from '@/config';
const { customUploadFileHandler } = uploadCoosFile();
export default {
	name: 'ImContent',
	components: {
		coosHeader,
		coosWelcome,
		CoosQuery,
		CoosAnswer,
		PluginPopup,
		dataDrillingPopup,
		coosHistory
	},
	mixins: [queryMixins],
	props: {
		/**组件模式的第一句欢迎语*/
		welcomeText: {
			type: String,
			default: () => {
				return '';
			}
		},
		/**组件模式待办传过来的参数*/
		robotDataContent: {
			type: String,
			default: () => {
				return '';
			}
		},
		/**数据id*/
		chatObjectId: {
			type: String,
			default: () => {
				return '';
			}
		},
		/**元数据ids*/
		metadataIds: {
			type: String,
			default: () => {
				return '';
			}
		},
		/**组件模式*/
		isComponent: {
			type: Boolean,
			default: () => {
				return false;
			}
		},
		/**组件类型*/
		componentType: {
			type: String,
			default: 'wait' // other-system其他系统  glob全局组件（和页面使用一样）
		},
		/**列表对话的表结构数据*/
		listParams: {
			type: Object,
			default: () => {
				return {};
			}
		},
		/**其他传参，和其余数据解耦，可以随意取*/
		otherParams: {
			type: Object,
			default: () => {
				return {};
			}
		},
		/**列表对话的表id集合*/
		listIds: {
			type: Array,
			default: () => {
				return [];
			}
		},
		/**对话类型*/
		modeType: {
			type: String,
			default: ''
		},
		coosType: {
			type: String,
			default: ''
		}
	},
	data() {
		return {
			searchType: 'isKnowledge',
			historyKey: '',
			currentDataDrilling: null, // 当前数据智析
			processStatus: '#19be6b', // 进度条颜色
			fileQuery: null, // 当前上传的文件
			accept: '.pdf,.doc,.docx,.txt,.xlsx,.png,.jpg,.jpeg,.gif,.png,.jpg,.jpeg,.gif',
			plugins: [], // 推荐功能列表
			pluginUUID: '', // 智控对话的id
			currentPlugin: null, // 当前选中功能
			showModeChange: false, // 模式切换
			currentMode: {}, // 当前模式
			sessionId: '', // 当前对话的ID
			historyShow: false, // 对话记录
			checkList: [], // 选中的知识库
			knowledge: [], // 知识库
			knowledgeBase: false, // 知识库显示隐藏
			loading: false, // 数据加载loading
			messageId: 0, // 发送消息临时的id，方便异步数据回来回显到对应的为止
			params: {
				pageSize: 3,
				pageNo: 1
			},
			/**右键菜单选项*/
			menuLeft: 0,
			menuTop: 0,
			menuShow: false,
			menuIndex: 0, // 右键菜单所在消息索引
			clickRightMessage: '', // 右键操作的菜单消息
			initHeight: 100, // 聊天框初始高度
			height: 100, // 变化的高度
			sendCon: '', // 发送内容
			messageList: [],
			contentEl: null, // 内容区域
			lock: false, //还有没有更多消息，请求锁
			lockAutoScroll: false, // 锁住自动滚动
			questionsList: [],
			questionParams: { pageNo: 1, pageSize: 4, model: 'cyzh', title: '' },
			questionLoading: false,
			questionsNum: 0,
			sendType: 1,
			dataDrillingList: [],
			showMode: 'cyzh,zdbk,zklh,sjzx' // 路由控制显示的类型集合
		};
	},
	computed: {
		...mapGetters(['rentInfo', 'coosConfig', 'aiEnterList']),
		// 标题
		coosTitle() {
			return this.coosConfig.robotName || 'COOS智能助手';
		},
		// 对话模式类型数据源（有部分智能对话入口不需要展示在coos智能助手，比如我的空间、知识库等）
		originModeList() {
			return [
				{
					type: 'cyzh',
					code: getDictionary('AI编码/创意智绘'),
					logoUrl: require(`@/assets/${this.rentThem}/coos/cyzh.png`),
					name: '创意智绘',
					digest: '文案编写和内容创作',
					prologue: '我可以为你答疑解惑，创作文案，生成创意和代码'
				},
				{
					type: 'zdbk',
					code: getDictionary('AI编码/智答宝库'),
					logoUrl: require(`@/assets/${this.rentThem}/coos/zdbk.png`),
					name: '智答宝库',
					digest: '公司知识库搜索与问答',
					prologue: '我将从公司所有知识库中为你查询答案，你也可以在输入框左上角指定知识库进行查询'
				},
				{
					type: 'zklh',
					code: getDictionary('AI编码/智控领航'),
					logoUrl: require(`@/assets/${this.rentThem}/coos/zklh.png`),
					name: '智控领航',
					digest: '业务调度与事务办理',
					prologue: '我可以为你发起流程、查询流程、处理待办事务，是你的业务中控助手'
				},
				{
					type: 'sjzx',
					code: getDictionary('AI编码/数据智析'),
					logoUrl: require(`@/assets/${this.rentThem}/coos/sjzx.png`),
					name: '数据智析',
					digest: '业务数据分析与钻取',
					prologue: '选择业务板块，可为你分析和钻取相关业务数据'
				}
			];
		},
		// 后端权限的类型
		permsList() {
			return this.aiEnterList
				.map(item => item.permsCode)
				.filter(permsCode => {
					return [
						getDictionary('AI编码/创意智绘'),
						getDictionary('AI编码/智答宝库'),
						getDictionary('AI编码/智控领航'),
						getDictionary('AI编码/数据智析')
					].includes(permsCode);
				});
		},
		// 实际展示的类型
		modeList() {
			let arr = this.originModeList
				.filter(item => {
					// 路由控制加后端权限控制
					return this.showMode.indexOf(item.type) > -1 && this.permsList.includes(item.code);
				})
				.map(item => {
					// 将后端前端数据整合
					let newItem =
						this.aiEnterList.find(ai => {
							return ai.permsCode === item.code;
						}) || {};
					return {
						...item,
						sortIndex: newItem.sortIndex || item.sortIndex,
						name: newItem.name || item.name,
						digest: newItem.digest || item.digest,
						prologue: newItem.prologue || item.prologue,
						logoUrl: newItem.logoUrl || item.logoUrl
					};
				});
			arr.sort((a, b) => a.sortIndex - b.sortIndex);
			return arr;
		},
		// 显示一些功能
		pluginOrDataDrilling() {
			return this.currentMode.type === 'zklh' ? this.plugins : this.dataDrillingList;
		},
		// 右键菜单选项
		contextContent() {
			let arr = [];
			let isText = !this.clickRightMessage.isSpecialAnswer; // 答案是特殊渲染
			let isChart = this.clickRightMessage.answerType === 'chart'; // 答案是特殊渲染
			if (isText) {
				arr.push({
					name: '复制',
					type: 'copy'
				});
			}
			if (isChart) {
				arr.push({
					name: '保存为图片',
					type: 'saveImage'
				});
			}
			arr.push({
				name: '删除',
				type: 'delete'
			});
			return arr;
		},
		// 第一条消息是答案的时候加上间距
		FirstAnswerMargin() {
			return this.messageList.length > 0 && this.messageList[0].type === 'answer' ? '24px' : 0;
		}
	},
	watch: {
		// 监听ai入口列表请求回来之后，判断默认的模式是否有权限，如果没有，就重新默认
		modeList: {
			deep: true,
			handler(newVal, oldVal) {
				let index = newVal.findIndex(
					item => this.currentMode && item.type === this.currentMode.type
				);
				this.changeMode(newVal[Math.max(0, index)]);
			}
		}
	},
	/**弥补生命周期只执行了一次*/
	activated() {
		let options = this.$route.query;
		// 根据需求切换对话模式数据源  默认全部模式
		if (options.mode) {
			this.showMode = options.mode;
		}
		// 组件模式this.modeType  ||  集成网页的模式this.$route.query.modeType      可以控制其默认模式
		if (options.modeType) {
			let modeType = this.modeType || options.modeType;
			let obj = this.modeList.find(item => {
				return item.type === modeType;
			});
			this.changeMode(obj || this.modeList[0]);
		}
		// 工作台跳转过来带了知识库id，要改为智答宝库模式，并设置知识库id
		if (options.knowlageId) {
			let obj = this.modeList.find(item => {
				return item.type === 'zdbk';
			});
			this.changeMode(obj || this.modeList[0]);
			// 当需要的模式里面没有zdbk的时候，默认选择第一种模式
			this.checkList = options.knowlageId.split(',');
		}
	},
	async mounted() {
		this.historyKey = [
			getDictionary('智能助手/创意智绘'),
			getDictionary('智能助手/智答宝库'),
			getDictionary('智能助手/智控领航'),
			getDictionary('智能助手/数值智析')
		].join(',');
		let options = this.$route.query;
		// 根据需求切换对话模式数据源  默认全部模式
		if (options.mode) {
			this.showMode = options.mode;
		}
		// 默认选中第一个
		this.currentMode = this.modeList[0] || {};

		// 组件模式this.modeType  ||  集成网页的模式this.$route.query.modeType      可以控制其默认模式
		if (this.modeType || options.modeType) {
			let modeType = this.modeType || options.modeType;
			this.currentMode =
				this.modeList.find(item => {
					return item.type === modeType;
				}) ||
				this.modeList[0] ||
				{};
		}

		// 工作台跳转过来带了知识库id，要改为智答宝库模式，并设置知识库id
		if (options.knowlageId) {
			let obj = this.modeList.find(item => {
				return item.type === 'zdbk';
			});
			this.currentMode = obj || this.modeList[0] || {};
			// 当需要的模式里面没有zdbk的时候，默认选择第一种模式
			this.checkList = options.knowlageId.split(',');
		}
		// 待办的组件模式
		if (this.isComponent && this.chatObjectId) {
			this.sessionId = this.chatObjectId; // 组件模式用数据的id
		} else {
			this.getPluginData();
			this.getKnowledge(); // 获取知识库
			this.getQuestionList(); //获取问题
			this.getDataDrilling(); // 获取数据智析的功能
		}

		window.addEventListener('click', this.caleMenu);
		this.contentEl = document.getElementById('coos-detail-con');
		if (this.contentEl) {
			this.contentEl.addEventListener('scroll', this.scroll);
		}
		if (this.sessionId || this.coosType) {
			await this.getCoosMessage();
			if (this.isComponent && this.welcomeText) {
				let { answerId } = this.addTemporaryMessage('');
				this.messageList.forEach(item => {
					if (item.id === answerId) {
						item.answer = this.welcomeText;
						item.answerDone = true;
					}
				});
			}
		}
	},
	beforeDestroy() {
		window.removeEventListener('click', this.caleMenu);
	},
	methods: {
		...mapMutations('settings', ['SET_COOS_CONFIG']),
		changeSessionId(id, type) {
			//   会话类型 创意智慧1 智答宝库2 智控领航3 数值智析4 ppt 101 智能问数102
			const typeMapping = {
				1: 'cyzh', // 创意智慧
				2: 'zdbk', // 智答宝库
				3: 'zklh', // 智控领航
				4: 'szfx' // 数值智析
			};
			if (typeMapping[type]) {
				this.changeModeType(typeMapping[type]);
			}

			this.sessionId = id;
			this.messageList = [];
			this.fileQuery = null;
			this.params.pageNo = 1;
			this.lock = false;
			this.getCoosMessage();
		},
		handleSendType(item) {
			this.sendType = item;
			this.$refs.popover.doClose();
		},

		/**提供给父级调用的模式切换*/
		changeModeType(type) {
			let obj = this.modeList.find(item => {
				return item.type === type;
			});
			// 实际展示的类型中找到了就切换，无效类型不做处理
			obj && this.changeMode(obj);
		},
		/**选择数据智析的业务*/
		selectDataDrilling(item) {
			this.currentDataDrilling = item;
		},
		/**更多应用点击之后的生成答案框*/
		addMoreApplicationAnswer(message, functionId) {
			let { answerId } = this.addTemporaryMessage('');
			this.getPluginAiAnswer(answerId, message, functionId);
		},
		/**清空文件上下文*/
		clearFile() {
			this.fileQuery = null;
		},
		/**选择文件完成*/
		onPickFile(event) {
			let file = event.target.files[0];
			let { name } = file;
			let typeArr = name.split('.');
			let type = typeArr[typeArr.length - 1];
			event.target.value = '';
			this.processStatus = '#19be6b';
			this.fileQuery = {
				name,
				type,
				process: 0
			};
			customUploadFileHandler({ file }, process => {
				// 根据定位的文件所在索引位置，赋值进度条
				this.fileQuery.process =
					((process.loaded / process.total) * 100).toFixed(2) > 99
						? 99
						: ((process.loaded / process.total) * 100).toFixed(2);
			}).then(res => {
				if (res.code === 200) {
					this.fileQuery.id = res.result.filenames;
					this.fileQuery.process = 100;
				} else {
					this.processStatus = '#ff0000';
					this.$message.error(res.message);
				}
			});
		},
		/**选择文件*/
		pickFile(type) {
			this.accept = type === 'img' ? 'image/*' : '*';
			this.$nextTick(() => {
				this.$refs['fileInput'].click();
			});
		},
		/**重置当前选中功能 */
		handlePluginReset() {
			if (this.currentMode.type === 'zklh') {
				this.currentPlugin = null;
				this.pluginUUID = null; // 清除智控对话id
			} else if (this.currentMode.type === 'sjzx') {
				this.currentDataDrilling = null;
			}
		},

		/**点击推荐的功能或者数据智析*/
		clickSome(item) {
			if (this.currentMode.type === 'zklh') {
				this.selectPlugin(item);
			} else if (this.currentMode.type === 'sjzx') {
				this.selectDataDrilling(item);
			}
		},
		/**选择功能*/
		async selectPlugin(item) {
			if (this.currentPlugin && item.id === this.currentPlugin.id) return;
			try {
				// type为3的时候，为打开应用，直接提示跳转
				if (item.type === 3) {
					this.$confirm('请确认是否打开此应用?', '提示', {
						confirmButtonText: '确定',
						cancelButtonText: '取消',
						type: 'warning'
					})
						.then(() => {
							let { extend, name, logoUrl, isExternal } = item;
							extend = JSON.parse(extend);
							let page = extend.PC;
							if (isExternal) {
								let url = page || '';
								let mainUrl = /http/gi.test(url) ? url : (preUrl || window.location.origin) + url;
								if (extend.openType === 2) {
									window.open(mainUrl);
								} else {
									this.$router.push(
										`/other-system?url=${encodeURIComponent(
											mainUrl || ''
										)}&name=${name}&logoUrlPath=${logoUrl}`
									);
								}
							} else {
								this.$router.push(page);
							}
						})
						.catch(() => {});
					return;
				}
				this.pluginUUID = null; // 清除智控对话id
				this.currentPlugin = item;
				let uuidRes = await getUUID();
				if (uuidRes.code !== 200) {
					throw new Error(uuidRes.message);
				}
				this.pluginUUID = uuidRes.result[0]; // 智控模式单独存储
				// 选择功能之后第一句提示语
				if (item.prologue) {
					let FirstAnswerId = this.addTemporaryMessage('').answerId;
					this.messageList.forEach(message => {
						if (message.id === FirstAnswerId) {
							message.answer = item.prologue;
							message.answerDone = true;
						}
					});
				}
				// type为2的时候是查询列表，只提示，不回答
				// type为1展示初始表单
				if (item.type === 1) {
					// 选择功能展示初始表单
					let { answerId } = this.addTemporaryMessage('');
					let data = {
						dialogChatId: this.pluginUUID,
						functionId: item.id,
						sessionId: this.sessionId
					};
					let res = await getChangePluginInit(data);
					if (res.code !== 200) {
						throw new Error(res.message);
					}
					// 更新sessionId
					if (res.result.sessionId) {
						this.sessionId = res.result.sessionId;
					}
					let result = res.result.answer;
					if (/^centerControlDataJson:/.test(result)) {
						let result_json = result.split('centerControlDataJson:')[1];
						result_json = JSON.parse(result_json);
						// result_json.data = JSON.parse(result_json.data)
						this.messageList.forEach(message => {
							if (message.id === answerId) {
								message.answer = result_json;
								message.isSpecialAnswer = true; // 答案是特殊渲染
								message.answerDone = true;
							}
						});
					} else {
						let arr = result.split('');
						let i = 0;
						this.messageList.forEach(item => {
							if (item.id === answerId) {
								let T = setInterval(() => {
									if (i === arr.length) {
										item.answerDone = true;
										clearInterval(T);
									} else {
										item.answer += arr[i];
										i++;
										if (!this.lockAutoScroll) {
											this.toBottom();
										}
									}
								}, 50);
							}
						});
					}
				}
				if (!this.lockAutoScroll) {
					this.toBottom();
				}
			} catch (err) {
				this.$message.error(err.message || '请求超时');
			}
		},
		/**查看更多功能*/
		lookMore() {
			if (this.currentMode.type === 'zklh') {
				this.$refs.PluginPopup.open();
			} else if (this.currentMode.type === 'sjzx') {
				this.$refs.dataDrillingPopup.open();
			}
		},
		/**获取功能的数据*/
		getPluginData() {
			somePlugins({ clientType: '1' }).then(res => {
				this.plugins = res.result;
			});
		},
		/**获取知识钻取的功能*/
		getDataDrilling() {
			getDataDrilling({ pageNo: 1, pageSize: 5 }).then(res => {
				if (res.code === 200) {
					this.dataDrillingList = res.result.records || [];
				} else {
					this.$message.error(res.message);
				}
			});
		},
		/**打开模式切换*/
		showModePopup() {
			this.knowledgeBase = false;
			this.showModeChange = !this.showModeChange;
		},
		/**改变模式*/
		changeMode(item) {
			if (isEmpty(item)) return;
			if (item.type === this.currentMode.type) return;
			this.currentMode = item; // 可能切换租户改了名字
			this.questionParams.pageNo = 1;
			if (['cyzh', 'zdbk'].includes(this.currentMode.type)) {
				this.getQuestionList();
			}
			this.reset();
			if (item.type === 'zklh') {
				this.getPluginData();
			}
			this.getKnowledge(); // 获取知识库
			if (item.type === 'sjzx') {
				this.getDataDrilling(); // 获取数据智析的功能
			}
		},
		/**继续在全部知识库中查找*/
		continueQuery(message) {
			let { answerId } = this.addTemporaryMessage('在全部知识库中查询'); // 添加消息队列
			// 区分智控模式和普通对话模式
			this.getQuery(answerId, message, true); // 异步获取消息回复
		},
		/**
		 * 添加消息
		 * @param {Object} answer 图表配置答案
		 * @param {Array} haveChats 历史对话的图标名称
		 * */
		addMessage(answer, haveChats = []) {
			let { answerId } = this.addTemporaryMessage('');
			let options;
			let name;
			if (Array.isArray(answer)) {
				options = answer[0].data;
				name = answer[0].name;
			} else {
				options = answer.data || answer;
				name = answer.name || '图表';
			}
			let newHaveChats = [...haveChats, name];
			this.messageList.forEach(item => {
				if (item.id === answerId) {
					item.answer = options;
					item.title = name;
					item.haveChats = newHaveChats;
					item.answerType = 'chart';
					item.isSpecialAnswer = true; // 答案是特殊渲染
					item.answerDone = true;
				}
			});
		},

		/**创建新对话*/
		createNewChat() {
			this.$confirm(`确定创建新对话吗?`, '提示', {
				confirmButtonText: '确认',
				cancelButtonText: '取消',
				type: 'warning'
			})
				.then(() => {
					this.reset();
				})
				.catch(() => {});
		},
		/**重置数据*/
		reset(reload = true) {
			this.fileQuery = null; // 清空基于文件对话的文件
			this.messageList = [];
			this.lock = true;
			this.sessionId = '';
			this.$refs.coosHistory.reset(); // 历史记录
			this.params.pageNo = 1;
			if (this.currentMode.type === 'zdbk') {
				this.checkList = this.knowledge.map(item => item.id);
			} else {
				this.checkList = []; // 清空知识库选中
			}
			this.currentPlugin = null; // 清除当前选中的功能
			this.currentDataDrilling = null; // 清除当前选中的功能
			this.pluginUUID = null; // 清除智控对话id
			if (this.historyShow && reload) {
				this.openHistory(true);
			}
		},
		/**清空选中*/
		clearCheck() {
			this.$message.warning('该模式下至少需要选中一个知识库！');
			this.checkList = [this.knowledge[0].id];
		},
		/**清空消息队列*/
		clear() {
			this.messageList = [];
			this.params.pageNo = 1;
			this.lock = true;
			// setTimeout(() => {
			// 	this.lock = false;
			// }, 3000);
		},
		/**获取知识库*/
		getKnowledge() {
			getKnowledge().then(res => {
				if (res.code === 200) {
					this.knowledge = res.result || [];
					this.checkList = this.checkList.length
						? this.checkList
						: this.knowledge.map(item => item.id);
				} else {
					this.$message.error(res.message);
				}
			});
		},

		/**打开历史记录*/
		openHistory(keepOpen) {
			if (!keepOpen) {
				this.historyShow = !this.historyShow;
			}
			if (this.historyShow) {
				this.$refs.coosHistory.openHistory(keepOpen);
			}
		},

		/**显示知识库选择*/
		showKnowledgeBase() {
			this.knowledgeBase = !this.knowledgeBase;
			this.showModeChange = false;
		},
		/**滚动到最底部*/
		toBottom() {
			this.$nextTick(() => {
				let messageListElement = this.$refs.messageContent;
				if (messageListElement) {
					messageListElement.scroll({
						top: messageListElement.scrollHeight,
						left: 0,
						behavior: 'auto'
					});
				}
			});
		},
		/**
		 * @method 添加临时消息
		 * @param {String} message 追加消息
		 * @param {Boolean} addAnswer 是否追加答案
		 * */
		addTemporaryMessage(message, addAnswer = true) {
			let answerId = 'coos-answer-' + this.messageId;
			let queryId = 'coos-query-' + this.messageId;
			// 添加自己发送的消息
			if (message) {
				this.messageList.push({
					type: 'query',
					id: queryId,
					query: message,
					createTime: new Date().toLocaleString()
				});
			}
			// 添加答案占位消息
			if (addAnswer) {
				this.messageList.push({
					type: 'answer',
					isAll: true, // 是否在全部知识库查询
					query: message, // 问题
					answerFrom: [], // 文档来源
					answerFromDone: this.checkList.length === 0, // 文档来源数据加载完毕
					id: answerId, // 问答ID
					isThink: false, // 是否深度思考
					thinkCon: '', // 思考内容
					startThink: false, // 是否开始思考
					answer: '', // ai答案
					isDgg: false, // 是否点赞（经信局才有）
					answerMulti: null, // 组合答案
					answerDone: false // 答案数据加载完毕
				});
			}
			this.messageId++;
			this.toBottom();
			return { queryId, answerId };
		},

		/**监听滚动*/
		scroll() {
			this.scrollLoad();
			if (
				this.contentEl.scrollTop + this.contentEl.clientHeight >=
				this.contentEl.scrollHeight - 10
			) {
				this.lockAutoScroll = false;
			} else {
				this.lockAutoScroll = true;
			}
		},
		/**滚动加载*/
		scrollLoad: debounce(
			function () {
				if (!this.loading && !this.lock && this.contentEl.scrollTop < 10) {
					this.params.pageNo += 1;
					this.getCoosMessage();
				}
			},
			500,
			false
		),
		/**获取coos的历史聊天记录*/
		async getCoosMessage() {
			let params = {
				pageNo: this.params.pageNo,
				pageSize: this.params.pageSize
			};
			// 全局组件不是单独针对某种模式的，只是把页面当成了组件使用
			if (this.isComponent && this.componentType !== 'glob') {
				params.chatType =
					this.componentType === 'wait' ? '2' : this.componentType === 'other-system' ? '3' : ''; // 组件模式2为待办，3为其他系统的列表
				params.chatObjectId = this.sessionId;
			} else {
				params.sessionId = this.sessionId;
			}
			if (this.coosType === 'space') {
				params.chatType = '5';
			}
			if (this.coosType === 'waitList') {
				params.chatType = '4';
			}
			// if (params.chatType === '3') return;
			if (!params.chatType) {
				params.sessionTypes = '1,2,3';
			}
			this.loading = true;
			if (this.$route.query.viewAllHistory && this.$route.query.viewAllHistory === 'true') {
				params.viewAllHistory = true;
			}
			let res = await getCoosHistory(params);
			this.loading = false;
			if (res.code === 200) {
				if (this.params.pageNo >= res.result.pages) {
					this.lock = true;
				}
				let message = [];
				res.result?.records
					.sort((a, b) => {
						return new Date(a.createTime).getTime() - new Date(b.createTime).getTime();
					})
					.forEach(item => {
						message.push({
							type: 'query',
							query: item.query,
							createTime: item.createTime
						});
						let answer = item.answer;
						let answerType = 'normal';
						let isSpecialAnswer = false;
						if (/^centerControlDataJson:/.test(answer)) {
							answer = answer.split('centerControlDataJson:')[1];
							answerType = Array.isArray(answer) ? 'MULTI' : answer.functionType; // 1是表单模式  2数据列表模式  3应用  MULTI组合模式
							answer = answerType === 3 ? [JSON.parse(answer)] : JSON.parse(answer); // 应用模式转成数组按照组合模式进行渲染
							// answer.data = JSON.parse(answer.data)
							isSpecialAnswer = this.getIsSpecial(answer); // 答案是特殊渲染
						}
						// 获取知识库来源
						let answerFrom = item.answerFrom ? JSON.parse(item.answerFrom) : [];
						// 处理来源中的链接
						answerFrom = answerFrom.map(form => {
							if (form.disk_file && form.disk_file.links) {
								let links = JSON.parse(form.disk_file.links);
								form.disk_file.links = links.filter(link => {
									return link.pc;
								});
								form.openLink = true; // 如果有三方链接，设一个收起打开状态
							}
							return form;
						});
						// 获取推荐问题
						let recommendedQuestions = answerFrom.reduce((base, form) => {
							base = base.concat(form.questions || []);
							return base;
						}, []);
						let data = {
							...item,
							type: 'answer',
							answer,
							answerType,
							title: this.getSpecialTitle(answerType),
							isSpecialAnswer, // 答案是特殊渲染
							isHistory: true, // 加一个历史记录的标识
							answerFrom,
							answerDone: true
						};
						// 如果有推荐问题
						if (recommendedQuestions.length) {
							data.recommendedQuestions = [...new Set(recommendedQuestions)].slice(0, 3);
						}
						// 看历史记录是否有深度思考内容
						if (/(<think>|<\/think>)/gi.test(answer)) {
							data.isThink = true;
							let i = answer.indexOf('</think>');
							// 思考结束前属于思考过程 (有可能没有结束标记)
							data.thinkCon = i > -1 ? answer.slice(0, i).replace(/<think>/gi, '') : '';
							// 思考结束后属于答案 (有可能没有结束标记，但以下写法均兼容)
							data.answer = answer.slice(i + 8);
							data.startThink = false;
						}
						message.push(data);
					});
				this.messageList = message.concat(this.messageList);
				if (this.params.pageNo === 1) {
					console.log('滚动到底部');
					this.toBottom();
				}
			} else {
				this.$message.error(res.message);
			}
		},
		/**操作右键*/
		handleContext(type) {
			switch (type) {
				case 'delete': {
					this.coosDelete();
					break;
				}
				case 'copy': {
					this.copy();
					break;
				}
				case 'saveImage': {
					let key = `message-item-` + this.menuIndex;
					this.$refs[key][0].saveImage(); // chart类型才有保存图片的方法
					break;
				}
				default:
					this.$message.warning('开发中，敬请期待！');
			}
		},
		/**复制*/
		copy() {
			let selectedText = window.getSelection().toString();
			if (selectedText) {
				copyText(selectedText);
				this.$message.success('复制成功');
			} else {
				let text = '';
				let message = this.clickRightMessage;
				if (this.clickRightMessage.type === 'query') {
					text = message.query;
				} else {
					text = typeof message.answer === 'object' ? '' : message.answer;
				}
				copyText(text);
				this.$message.success('复制成功');
			}
		},
		/**coos删除聊天记录*/
		coosDelete() {
			let id = this.clickRightMessage.id;
			delCoosHistory(id).then(res => {
				if (res.code === 200) {
					this.messageList.splice(this.menuIndex, 1);
					this.$message.success('操作成功！');
				} else {
					this.$message.error(res.message);
				}
			});
		},
		/**关闭右键菜单*/
		caleMenu() {
			this.menuShow = false;
			this.knowledgeBase = false;
			this.$refs.coosHistory.closeEdit();
			this.showModeChange = false;
		},
		/**右键菜单*/
		contextmenu(event, item, index) {
			this.clickRightMessage = item;
			this.menuIndex = index;
			// 根据需要进行位置调整等操作
			this.menuLeft = event.clientX;
			this.menuTop = event.clientY;
			this.menuShow = true;
		},
		/**阻止回车默认行为*/
		preventEnter(e) {
			if (!e.shiftKey && e.key === 'Enter') {
				e.preventDefault();
			}
		},

		/**发送消息*/
		sendMessage(e) {
			if (this.sendType === 1 && e.key === 'Enter' && !e.ctrlKey && !e.shiftKey) {
				this.send();
			}
			if (this.sendType === 2 && e.key === 'Enter' && e.ctrlKey) {
				this.send();
			}
		},
		/**
		 * @description 发送消息
		 * @param {Object} e 点击元素
		 * @param {Object} message 发送的消息（默认输入框输入的）可供全局调用的通用方法
		 * */
		send(e, message = this.sendCon) {
			if (message.replace(/(\n|\s)/gi, '')) {
				this.sendCon = ''; // 清空
				let { answerId } = this.addTemporaryMessage(message); // 添加消息队列
				this.getAiAnswer(answerId, message);
			} else {
				this.sendCon = '';
				this.$message.error('发送消息不能为空！');
			}
		},

		resize(e) {
			if (this.isComponent) return;
			this.initHeight = this.height;
			this.startY = e.clientY;
			document.addEventListener('mousemove', this.handleMouseMove);
			document.addEventListener('mouseup', this.stopSelection);
		},
		handleMouseMove(e) {
			this.moveHeight = this.startY - e.clientY;
			this.height = this.initHeight + this.moveHeight;
		},
		stopSelection() {
			document.removeEventListener('mousemove', this.handleMouseMove);
			document.removeEventListener('mouseup', this.stopSelection);
		},
		otherQuestions() {
			if (this.questionsNum > this.questionParams.pageNo * 4) this.questionParams.pageNo++;
			else this.questionParams.pageNo = 1;
			this.getQuestionList();
		},
		getQuestionList() {
			if (isEmpty(this.currentMode)) return;
			this.questionLoading = true;
			this.questionParams.model = this.currentMode.type;
			getQuestionsList(this.questionParams).then(res => {
				if (res.code === 200) {
					this.questionsList = res.result.records;
					this.questionsNum = res.result.total;
				} else {
					this.$message.error(res.message);
				}
				this.questionLoading = false;
			});
		}
	}
};
</script>

<style scoped lang="scss">
.more-icon {
	font-size: 20px;
	color: #515b6e;
	margin-right: 8px;
	cursor: pointer;
}

.im-content {
	flex: 1;
	height: 100%;
	overflow: hidden;
	border-radius: 12px;
	@include flexBox();
}

.message {
	flex: 1;
	height: 100%;
	display: flex;
	flex-direction: column;
	position: relative;
	overflow: hidden;

	&-detail {
		flex: 1;
		display: flex;
		flex-direction: column;
		position: relative;
		overflow: hidden;
		&-con {
			padding: 0 20px;
			flex: 1;
			overflow-y: auto;
			@include flexBox(flex-start, flex-start);
			flex-direction: column;

			@include noScrollBar;

			.load-more {
				@include flexBox();
				font-size: 12px;
				width: 100%;
				height: 40px;
				color: #c3c3c3;
				flex-shrink: 0;

				&-text {
					animation: text-loading 1s infinite;
					margin: 0 1px;
				}
			}
		}
	}

	&-resize {
		height: 4px;
		cursor: ns-resize;
	}

	&-utils {
		height: 42px;
		border-top: 1px solid #dce3e7;
		border-bottom: 1px solid #dce3e7;
		padding: 6px 10px;
		position: relative;
		background: #ffffff;
		display: flex;
		align-items: center;

		&-mode {
			display: inline-flex;
			align-items: center;
			background: #f5f7fa;
			border-radius: 3px;
			padding: 5px 8px;
			font-weight: 400;
			font-size: 12px;
			color: $textColor;
			line-height: 20px;
			text-align: center;
			font-style: normal;
			text-transform: none;
			margin-right: 8px;
			cursor: pointer;

			&-icon {
				width: 20px;
				height: 20px;
				margin-right: 2px;
			}
		}

		&-select {
			background: #f5f7fa;
			padding: 4px 8px;
			border-radius: 3px;
			display: inline-flex;
			align-items: center;

			.plugin-choose {
				display: flex;
				align-items: center;
			}

			.icon {
				font-size: 20px;
				margin-right: 2px;
			}

			.zklh-icon {
				font-size: 20px;
				margin-right: 2px;
				color: #035dff;
			}

			.text {
				font-size: 12px;
				font-weight: 400;
				color: $textColor;
				line-height: 20px;
				cursor: pointer;
			}

			.plugin-text {
				color: var(--brand-6);
				margin: 0 5px;
			}

			.plugin-reset {
				margin-left: 20px;
				font-size: 12px;
				color: var(--brand-6);
				line-height: 20px;
			}
		}

		.quick-button {
			position: absolute;
			top: calc(50% - 12px);
			right: 20px;
			cursor: pointer;
			@include flexBox();

			&-icon {
				margin-left: 12px;
				font-size: 24px;
			}
		}

		.mode-popup {
			position: absolute;
			bottom: calc(100% + 17px);
			left: 13px;
			min-width: 163px;
			height: auto;
			border-radius: 12px;
			background: #ffffff;
			box-shadow: 0px 4px 14px 0 rgba(0, 0, 0, 0.1);
			@include flexBox(flex-start, flex-start);
			flex-direction: column;

			.mode-title {
				width: 100%;
				font-weight: 500;
				font-size: 14px;
				color: $primaryTextColor;
				line-height: 22px;
				text-align: center;
				font-style: normal;
				text-transform: none;
				padding: 10px 10px 5px;
				border-bottom: 1px solid #f0f0f0;
			}

			.mode-list {
				width: 100%;
				padding: 0 8px 8px;

				&-item {
					padding: 9px 8px;
					cursor: pointer;
					@include flexBox(flex-start);

					&-img {
						height: 20px;
						width: 20px;
						margin-right: 3px;
					}

					&-text {
						font-weight: 400;
						font-size: 14px;
						color: $textColor;
						line-height: 22px;
						text-align: left;
						font-style: normal;
						text-transform: none;
					}
				}
			}
		}

		.knowledge-base {
			position: absolute;
			top: -403px;
			left: 13px;
			width: 339px;
			height: 390px;
			border-radius: 12px;
			background: #ffffff;
			padding: 12px;
			box-shadow: 0px 4px 14px 0 rgba(0, 0, 0, 0.1);
			@include flexBox(flex-start, flex-start);
			flex-direction: column;
			max-height: 600px;
			&-title {
				width: 100%;
				flex-shrink: 0;
				height: 44px;
				@include flexBox(flex-start);
				font-size: 16px;
				font-weight: 400;
				color: $subTextColor;
				line-height: 22px;
				position: relative;

				.data {
					font-size: 16px;
					font-weight: 400;
					color: $primaryTextColor;

					&-select {
						color: var(--brand-6);
					}
				}

				& > .button {
					font-size: 14px;
					font-weight: 400;
					color: var(--brand-6);
					line-height: 22px;
					position: absolute;
					right: 8px;
					cursor: pointer;
				}
			}

			&-con {
				width: 100%;
				flex: 1;
				overflow-y: auto;
				overflow-x: hidden;

				@include noScrollBar;
			}

			&-item {
				height: 46px;
				width: 100%;
				padding: 0 8px;
				border-bottom: 1px solid #f0f0f0;
				overflow: hidden;
				@include flexBox(flex-start);

				::v-deep .el-checkbox__label {
					flex: 1;
					overflow: hidden;
					@include flexBox(flex-start);
				}

				.icon {
					font-size: 20px;
					line-height: 20px;
					margin-right: 3px;
				}

				.title {
					font-size: 14px;
					font-weight: 800;
					color: $textColor;
					line-height: 14px;
					@include aLineEllipse;
				}
			}
		}
	}
	.send-input-content {
		display: flex;
	}
	.component-input {
		@include flexBox(flex-start);
		padding: 12px 16px 12px 12px;
		border-top: 1px solid #dce3e7;
		flex: 1;
		&-textarea {
			flex: 1;

			::v-deep .el-textarea__inner {
				overflow-y: auto;
				background: #ffffff;
				border-radius: 6px;
				border: 1px solid #dce3e7;

				&::placeholder {
					color: $holderTextColor;
				}

				@include noScrollBar;

				&:focus {
					box-shadow: none;
					@include scrollBar;
				}

				&:hover {
					box-shadow: none;
					@include scrollBar;
				}
			}
		}

		&-icon {
			flex-shrink: 0;
			font-size: 24px;
		}
	}

	&-send {
		&-input {
			height: 100%;

			::v-deep .el-textarea__inner {
				height: 100% !important;
				overflow-y: auto;
				background: #ffffff;
				border: none;

				&::placeholder {
					color: $holderTextColor;
				}

				@include noScrollBar;

				&:focus {
					border-color: transparent;
					box-shadow: none;
					@include scrollBar;
				}

				&:hover {
					border-color: transparent;
					box-shadow: none;
					@include scrollBar;
				}
			}
		}
	}

	.menu {
		position: fixed;
		background: #ffffff;
		border-radius: 3px;
		padding: 5px 0;
		width: 70px;
		text-align: center;
		box-shadow: 0px 3px 4px 0px rgba(0, 0, 0, 0.3);

		&-item {
			padding: 8px 0;
			font-size: 12px;
			cursor: default;

			&:hover {
				background: #f0f0f0;
			}
		}
	}
}
.plugin-logo {
	border-radius: 3px;
	padding: 4px 5px;
	font-weight: 400;
	font-size: 12px;
	line-height: 12px;
	margin-left: 8px;
}
.plugin-logo-1 {
	background: rgba(0, 168, 112, 0.1);
	color: #00a870;
}
.plugin-logo-2 {
	background: rgba(116, 88, 228, 0.1);
	color: #7458e4;
}
.plugin-logo-3 {
	background: rgba(48, 136, 255, 0.1);
	color: #3088ff;
}
::v-deep .el-checkbox__inner {
	width: 16px;
	height: 16px;
	border-radius: 2px;
	border: 1px solid #dcdfe6;
}

::v-deep .el-checkbox__label {
	font-weight: 400;
}
.send-box-flot {
	position: absolute;
	bottom: 10px;
	right: 10px;
}
.send-box {
	cursor: pointer;
	display: flex;
	align-items: center;
	width: 88px;
	background: #ffffff;
	border-radius: 3px 3px 3px 3px;
	border: 1px solid #dce3e7;
	//border-left: none;
	.send-btn {
		padding: 3px 15px;
		font-family: PingFang SC, PingFang SC;
		font-weight: 400;
		font-size: 14px;
		color: #2f446b;
		line-height: 22px;
		text-align: left;
		font-style: normal;
		text-transform: none;
	}
	.send-icon-box {
		width: 28px;
		height: 28px;
		border-radius: 0px 0px 0px 0px;
		border-left: 1px solid #dce3e7;
		display: flex;
		align-items: center;
		justify-content: center;
		.send-icon {
			font-size: 16px;
		}
	}
}
.send-dec-text {
	height: 20px;
	font-family: PingFang SC, PingFang SC;
	font-weight: 400;
	font-size: 14px;
	color: #2f446b;
	line-height: 20px;
}
.dagou {
	color: var(--brand-6);
	font-size: 16px;
}
.send-select {
	display: flex;
	align-items: center;
	cursor: pointer;
	height: 32px;
	.sand-select-box {
		width: 16px;
		cursor: pointer;
		border-radius: 0px 0px 0px 0px;
		margin-right: 8px;
	}
}
.empty-block {
	height: 100%;
	background: #ffffff;
	flex: 1;
	@include flexBox();
}
</style>
