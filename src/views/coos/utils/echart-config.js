let colorArr = [
	'#5470c6',
	'#349507',
	'#fac858',
	'#ee6666',
	'#73c0de',
	'#3ba272',
	'#fc8452',
	'#9a60b4',
	'#ea7ccc'
];

// 悬浮tooltip
const tooltipsConfig = (params, title, unit) => {
	let str = title
		? `<span style="color: #fff; font-size: 14px; font-weight: 600;">${title}</span>`
		: '';
	if (str && params[0].seriesName) {
		str += '<br>';
	} else {
		str += '：';
	}
	params.forEach(item => {
		let currentValue = item.value;
		if (item.seriesName) {
			str += `<span style="font-size: 12px;color: #fff;">${item.seriesName}：</span>`;
		}
		str += `<span style="font-size: 12px;color: #fff;">${
			!currentValue && currentValue !== 0 ? '无数据' : currentValue
		}${unit && (currentValue || currentValue === 0) ? unit : ''}</span><br>`;
	});
	return str;
};

/**
 * 通用 ECharts 配置模板（支持柱状图自动适配 x 轴显示）
 */
export function commonEchartsOptions(options) {
	let series = options?.series;

	let unit = null;

	// 自动处理 xAxis 配置 bar
	if (options.xAxis && Array.isArray(series) && ['bar'].includes(series[0]?.type)) {
		const xAxisData = options.xAxis.data || [];
		const maxLabelLength = Math.max(...xAxisData.map(item => item.toString().length));
		const labelCount = xAxisData.length;

		// 设置阈值：超过 5 个标签 或 单个标签长度超过 6 个字符则启用竖向排列
		const shouldVertical = labelCount > 5 || (labelCount > 4 && maxLabelLength > 10);

		if (shouldVertical && !options.xAxis.axisLabel) {
			options.xAxis.axisLabel = {
				formatter: function (value) {
					return value.split('').join('\n'); // 每字一行
				},
				margin: 30,
				width: 50,
				overflow: 'break'
			};
		}
	}

	let tooltip = {
		trigger: 'axis',
		triggerOn: 'click',
		confine: true,
		backgroundColor: 'rgba(0,0,0,.8)',
		formatter: params => {
			params = Array.isArray(params) ? params : [params];
			// 获取标题 默认空字符串 没有时会series0
			let title = (params[0] && params[0].name) || '';
			return tooltipsConfig(params, title, unit);
		}
	};

	let legend = {
		selectedMode: true,
		itemWidth: 10,
		itemHeight: 10,
		right: 0,
		top: options.title && options.title.subtext ? 60 : options.title ? 30 : 20,
		textStyle: {
			fontSize: 10
		}
	};

	// 对饼状图是无效的
	let grid = {
		left: '6%',
		right: '6%',
		bottom: '6%',
		top:
			series.length * 32 + (options.title && options.title.subtext ? 64 : options.title ? 32 : 0),
		containLabel: true
	};

	// 动态调整底部边距（如果启用了竖向排列）
	if (
		options.xAxis?.axisLabel?.formatter ||
		(options.xAxis?.axisLabel?.rotate && options.xAxis.axisLabel.rotate !== 0)
	) {
		grid.bottom = '5%';
	}

	let title = {
		textStyle: {
			width: 450,
			overflow: 'truncate'
		}
	};

	let ops = {
		color: colorArr,
		...options,
		title: { ...title, ...options.title },
		grid: { ...grid, ...options.grid },
		legend: { ...legend, ...options.legend },
		tooltip: { ...tooltip, ...options.tooltip }
	};

	let legendLine =
		ops.legend && ops.legend.orient === 'vertical'
			? typeof ops.series[0].data[0] === 'object'
				? ops.series[0].data.length
				: ops.series.length
			: 1;

	ops.customHeight = legendLine * 32 + (ops.title && ops.title.subtext ? 64 : ops.title ? 32 : 16);

	// 根据 x 轴数据长度动态调整高度
	if (options.xAxis && Array.isArray(options.xAxis.data)) {
		const xAxisDataLength = options.xAxis.data.length;
		ops.customHeight += xAxisDataLength * 10;
	}

	if (Array.isArray(ops.series)) {
		ops.series[0].bottom = -ops.customHeight / 2;
	} else {
		ops.series.bottom = -ops.customHeight / 2;
	}

	return ops;
}
