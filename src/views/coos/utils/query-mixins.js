import {
	geDataDrillingAiAnswer,
	getAnswerFrom,
	getListAiQuery,
	getSpaceAiAnswerBySocket,
	getTableDataBySql,
	getUUID,
	getWorkAiAnswerBySocket,
	newAiQuery,
	getNetworkAnswer,
	// getModel,
	pluginModelAi,
	// dataInterpretationV2,
	getDataInterpretationBySocket,
	getPoliticsBySocket,
	getQueryNumber,
	getModelV2,
	getAgentsId,
	getQueryByAdministrativeAssistant,
	newAiQueryStreamToString
} from '@/api/modules/coos';
import { isEmpty } from '@/utils';
import { Base64 } from 'js-base64';
import { v4 as uuidv4 } from 'uuid';
import { commonEchartsOptions } from '@/views/coos/utils/echart-config';

export default {
	data() {
		return {
			answerFromId: '', // 获取文档来源的id
			transformIndex: 0, // 转换图表数据的次数
			cacheAnswer: '', // 缓存答案
			tempAgentList: [], // 临时智能体列表
			tempAgentObj: {}, // 临时智能体对象
			pendingDocContent: '', // 缓存未完成的 <docAnswerFiles> 内容
			pendingRecContent: '', // 缓存未完成的 <recommendedQuestions> 内容
			pendingAnswerFromContent: '', // 缓存未完成的 <answerFrom> 内容
			executionPathContent: '', //缓存未完成的 <executionPath> 内容
			sseUUID: uuidv4() // 办公助手的sessionId
		};
	},
	methods: {
		/**混淆模式-获取具体模式*/
		async getModel(answerId, message) {
			// 重置sessionId
			this.sessionId = '';

			// 区别对话模式
			let res = await getModelV2({
				query: message,
				createBy: this.createBy[this.currentVersion],
				version: 'v3'
			});

			if (res.code === 200) {
				// 通过next的值判断
				const resData = res.result;

				this.getModalData = resData;
				let next = resData.next;
				let dataInterpretationObjectId = resData.dataInterpretationObjectId;
				let dataInterpretationTemplateId = resData.dataInterpretationTemplateId;
				let docAnswerFiles = resData.docAnswerFiles;
				return {
					next,
					dataInterpretationObjectId,
					dataInterpretationTemplateId,
					docAnswerFiles
				};
			} else {
				return {
					next: 0
				};
				// this.messageList.forEach(item => {
				// 	if (item.id === answerId) {
				// 		item.answerDone = true;
				// 		item.answer = res.message;
				// 	}
				// });
				// uni.$u.toast(res.message);
			}
		},
		/**
		 * @method requestSocket 发起socket答案请求
		 * @param {String} message 发送的消息
		 * @param {String} answerId  答案ID
		 * */
		async requestSocket(message, answerId) {
			let defaultNext = 1; // 默认调用问政企业端
			let { next } = await this.getModel(answerId, message);
			if (this.currentVersion === 1) {
				defaultNext = next;
			}
			//  0 没有答案
			if (!next) {
				this.messageList.forEach(item => {
					if (item.id === answerId) {
						item.answerDone = true;
						item.answer = '服务器繁忙中，请稍后再试。';
					}
				});
				this.IsSend = false;
				this.toBottom();
				return;
			}
			this.nextType = defaultNext;
			// 通过next 判断调用什么接口
			// 调用接口  1知识库 2专家  3tosql
			let requestApi = null;
			// 初始化的请求参数
			let requestParmas = {
				query: message,
				chatType: '0', //对话类型
				deepSeek: this.deepThink,
				sessionId: this.sessionId, // 会话id
				// chatObjectId: this.chatObjectId //业务对话id
				createBy: this.createBy[this.currentVersion]
			};
			// 是否是联网搜索
			if (this.searchType === 'IsInternetSearch') {
				this.nextType = null;
				getNetworkAnswer(requestParmas);
				return;
			}
			// 不是联网搜索
			switch (next) {
				case 1:
					// 调用知识库接口
					requestApi = getPoliticsBySocket;
					break;
				case 2:
					// 问数
					requestParmas.dataInterpretationObjectId = this.getModalData?.dataInterpretationObjectId;
					requestParmas.version = 'v3';
					requestParmas.dataInterpretationTemplateId =
						this.getModalData.dataInterpretationTemplateId;
					// 走指标查询接口
					// requestApi = dataInterpretationV2;
					requestApi = getQueryNumber;
					break;
				default:
					// 3 直接走toSql接口
					requestParmas.dataInterpretationObjectId = this.getModalData?.dataInterpretationObjectId;
					requestApi = getDataInterpretationBySocket;
					break;
			}
			return { requestApi, requestParmas };
			// if (resData.code !== 200) {
			// 	// 报错
			// 	this.messageList.forEach(item => {
			// 		if (item.id === answerId) {
			// 			item.answerDone = true;
			// 			item.answer = resData.message;
			// 		}
			// 	});
			// 	Message.error(resData.message);
			// 	this.toBottom();
			// 	this.IsSend = false;
			// } else {
			// 	this.messageList.forEach(item => {
			// 		if (item.id === answerId) {
			// 			item.nextType = this.nextType;
			// 			item.showLookInfo = next != 1;
			// 		}
			// 	});
			// }
		},
		/**
		 * @method getZnwsAiAnswer Ai对话交互
		 * @param {String} answerId 临时生成答案消息的id
		 * @param {String} message 发送的消息
		 * @param {Boolean} functionId 当前选中的功能的id
		 * */
		async getZnwsAiAnswer(answerId, message, functionId = '') {
			//  清空智能体
			this.tempAgentList = [];
			// 步骤条更新
			this.messageList.forEach(item => {
				if (item.id === answerId) {
					item.currentStep = 1;
					item.hasStep = true; // 调整后，都要走智能体对话，不再判断是否需要步骤条。!!this.agentObj.id || !!this.tempAgentObj.id;
				}
			});
			// 如果没有选中智能体id，根据答案进行判断
			if (
				this.searchType !== 'IsInternetSearch' &&
				!this.deepThink &&
				this.isAdmin &&
				!this.agentObj.id &&
				!this.tempAgentObj.id
			) {
				await this.getAgentType(message);
				// 初始化智能体列表
				this.messageList.forEach(item => {
					if (item.id === answerId) {
						item.answerFrom = [];
						if (this.tempAgentList && this.tempAgentList.length > 0) {
							item.recommendAgent = this.tempAgentList; // 推荐智能体列表
							item.recommendAgentId = this.tempAgentObj.id; // 当前智能体的id
						}
					}
				});
			}
			this.getAgentAnswer(answerId, message); // 异步获取消息回复
		},
		/**
		 * @method getAiAnswer Ai对话交互
		 * @param {String} answerId 临时生成答案消息的id
		 * @param {String} message 发送的消息
		 * @param {Boolean} functionId 当前选中的功能的id
		 * */
		async getAiAnswer(answerId, message, functionId = '') {
			this.cacheAnswer = '';
			// 区分智控模式和其他模式
			if (this.currentMode.type === 'zklh') {
				// 其他系统列表智能问答 组件模式用专用接口  this.isComponent &&
				if (this.componentType === 'other-system') {
					this.getListAiQuery(answerId, message);
				} else {
					this.getPluginAiAnswer(answerId, message, functionId);
				}
			}
			// 对接socket问答的其他模式，统一创建socket发送请求
			else {
				// 如果没有选中智能体id，根据答案进行判断
				if (!this.deepThink && this.isAdmin && !this.agentObj.id && !this.tempAgentObj.id) {
					await this.getAgentType(message);
					// 初始化智能体列表
					this.messageList.forEach(item => {
						if (item.id === answerId) {
							if (this.tempAgentList && this.tempAgentList.length > 0) {
								item.recommendAgent = this.tempAgentList; // 推荐智能体列表
								item.recommendAgentId = this.tempAgentObj.id; // 当前智能体的id
							}
						}
					});
				}

				this.getQuery(answerId, message); // 异步获取消息回复
			}
		},
		/**智能体判断*/
		async getAgentType(msg) {
			let res = await getAgentsId({
				identityId: this.userInfo.identityId || '',
				message: msg,
				sessionId: this.sessionId,
				tenantId: this.rentInfo.id,
				userId: this.userInfo.id
			});
			if (res.code === 200) {
				this.tempAgentList = res.result
					.map(_ => {
						let obj = this.typeList.find(el => el.id == _.id);
						return obj || null;
					})
					.filter(_ => _); // 数组对象 多个智能体
				this.tempAgentObj = this.tempAgentList[0]; // 默认第一个智能体回答
			} else {
				// TODO 不提示错误
				// uni.$u.toast(res.message);
			}
		},
		/**
		 * @method getListAiQuery 智控-组件专用列表智能对话的逻辑
		 * @param {String} answerId 临时生成答案消息的id
		 * @param {String} queryMessage 回答的问题消息
		 * */
		async getListAiQuery(answerId, queryMessage) {
			try {
				let data = {
					...this.listParams,
					message: queryMessage
				};
				let res = await getListAiQuery(data);
				if (res.code !== 200) throw new Error(res.message);
				let { sql, message } = res.result;
				if (sql) {
					let param = { sql, ...this.otherParams };
					if (!isEmpty(this.listIds)) param.cfgIds = this.listIds;
					let sqlRes = await getTableDataBySql(param);
					if (sqlRes.code !== 200) throw new Error(sqlRes.message);
					let result_json = { data: sqlRes.result, functionType: 2 }; // 为了和之前的数据处理接轨，统一处理
					let isSpecialAnswer = this.getIsSpecial(result_json); // 答案是特殊渲染
					this.messageList.forEach(item => {
						if (item.id === answerId) {
							item.answer = result_json; // 固定位列表数据格式
							item.answerType = 2; // 固定位列表数据格式
							item.isSpecialAnswer = isSpecialAnswer; // 答案是特殊渲染
							item.title = this.getSpecialTitle(2);
							item.answerDone = true;
							this.IsSend = false;
						}
					});
					if (!this.lockAutoScroll) {
						this.toBottom();
					}
				} else {
					let i = 0;
					let arr = message.split('');
					// 流式输出假效果
					this.messageList.forEach(item => {
						if (item.id === answerId) {
							let T = setInterval(() => {
								if (i === arr.length) {
									this.IsSend = false;
									item.answerDone = true;
									clearInterval(T);
								} else {
									item.answer += arr[i];
									i++;
								}
								if (!this.lockAutoScroll) {
									this.toBottom();
								}
							}, 50);
						}
					});
				}
			} catch (err) {
				this.messageList.forEach(item => {
					if (item.id === answerId) {
						item.answerDone = true;
						this.IsSend = false;
						item.answer = err.message;
					}
				});
				this.$message.error(err.message);
			}
		},
		/**
		 * @method getPluginAiAnswer 智控模式普通对话的逻辑
		 * @param {String} messageId 临时消息队列id
		 * @param {String} message 发送的消息
		 * @param {Boolean} functionId 当前选中的功能的id
		 * */
		async getPluginAiAnswer(messageId, message, functionId) {
			let result = '你提供的信息不足，请提供更多的信息！';
			try {
				if (!this.pluginUUID) {
					let uuidRes = await getUUID();
					if (uuidRes.code !== 200) {
						throw new Error(uuidRes.message);
					}
					this.pluginUUID = uuidRes.result[0]; // 智控模式单独存储
				}
				let data = {
					dialogChatId: this.pluginUUID,
					query: message,
					chatType: this.currentMode.sessionTypes,
					functionId: functionId ? functionId : this.currentPlugin ? this.currentPlugin.id : '',
					sessionId: this.sessionId
				};
				let res = await pluginModelAi(data);
				if (res.code !== 200) {
					throw new Error(res.message);
				}
				// 更新sessionId
				if (res.result.sessionId) {
					this.sessionId = res.result.sessionId;
				}
				result = res.result.answer;
			} catch (err) {
				console.log(err);
				this.$message.error(err.message || '请求超时');
			}
			if (/^centerControlDataJson:/.test(result)) {
				let result_json = result.split('centerControlDataJson:')[1];
				result_json = JSON.parse(result_json);
				// result_json.data = JSON.parse(result_json.data)
				let isSpecialAnswer = this.getIsSpecial(result_json); // 答案是特殊渲染
				let answerType = Array.isArray(result_json) ? 'MULTI' : result_json.functionType; // 1是表单模式  2数据列表模式  3应用  MULTI组合模式

				this.messageList.forEach(item => {
					if (item.id === messageId) {
						item.answer = answerType === 3 ? [result_json] : result_json; // 应用模式转成数组按照组合模式进行渲染
						item.answerType = answerType;
						item.isSpecialAnswer = isSpecialAnswer; // 答案是特殊渲染
						item.title = this.getSpecialTitle(answerType);
						item.answerDone = true;
						this.IsSend = false;
					}
				});
				if (!this.lockAutoScroll) {
					this.toBottom();
				}
			} else {
				let arr = result.split('');
				let i = 0;
				this.messageList.forEach(item => {
					if (item.id === messageId) {
						let T = setInterval(() => {
							if (i === arr.length) {
								item.answerDone = true;
								this.IsSend = false;
								clearInterval(T);
							} else {
								item.answer += arr[i];
								i++;
								if (!this.lockAutoScroll) {
									this.toBottom();
								}
							}
						}, 50);
					}
				});
			}
		},
		/**获取智能体答案（问政综合版）*/
		async getAgentAnswer(id, message, isAll = false) {
			// 初始化的请求参数
			let requestParmas = {
				query: message,
				chatType: '0', //对话类型
				deepSeek: this.deepThink, // 是否是深度思考
				sessionId: this.sessionId // 会话id
			};
			// 是否是联网搜索
			let aiRes;
			this.messageList = this.messageList.map(item => {
				if (item.id === id && item.currentStep < 3) {
					return { ...item, currentStep: 2 };
				}
				return item;
			});
			this.controller = new AbortController();
			const { signal } = this.controller;

			if (this.searchType === 'IsInternetSearch') {
				aiRes = await getNetworkAnswer(requestParmas, signal);
			} else {
				let request;
				// 有无选择智能体，请求参数接口不同（以临时选择的智能体为主）
				if (!this.isAdmin || this.tempAgentObj.id || this.agentObj.id) {
					request = getPoliticsBySocket;
					requestParmas.agentId = this.tempAgentObj.id || this.agentObj.id;
				} else {
					request = newAiQuery;
					requestParmas.chatType = 103;
				}

				aiRes = await request(requestParmas, signal);
			}
			// if (aiRes.code === 200) {
			// 	// TODO请求成功
			// 	this.messageList.forEach(item => {
			// 		if (item.id === messageId && item.currentStep < 3) {
			// 			item.currentStep = 2;
			// 		}
			// 	});
			// } else {
			// 	// 报错
			// 	this.messageList.forEach(item => {
			// 		if (item.id === messageId) {
			// 			item.answerDone = true;
			// 			item.answer = res?.result?.message;
			// 		}
			// 	});
			// 	this.sended = false;
			// }
			// // 获取AI答案
			const reader = aiRes.body.getReader(); // 流式读取器
			const decoder = new TextDecoder('utf-8');
			let readDone = false; // 读取完毕
			this.answerFromId = ''; // 清空上次对话的answerId
			this.pendingDocContent = ''; // 清空缓存未完成的 <docAnswerFiles> 内容
			this.pendingRecContent = ''; // 清空缓存未完成的 <recommendedQuestions> 内容
			let docAnswerFilesIndex = 0;
			let recommendedQuestionsIndex = 0;
			let executionPathIndex = 0;
			// 流式输出
			while (!readDone) {
				let { value, done } = await reader.read();
				readDone = done;
				let res;
				try {
					res = decoder.decode(value);
					if (/"code":50/.test(res)) {
						let r = JSON.parse(res.replace('data:', ''));
						this.messageList.forEach(item => {
							if (item.id === id) {
								item.answerDone = true;
								item.currentStep = 4;
								item.answer = res?.result?.message;
							}
						});
						res = [r.message || '服务器异常', 'noAnswer'];
					} else {
						// TODO请求成功

						// res = res.replace(/data:/gi, ''); // 解析成JSON，去掉data:开头的字符串
						res = res.split('data:');
						res = res.map(item => {
							this.messageList = this.messageList.map(item => {
								if (item.id === id && item.currentStep < 3) {
									return { ...item, currentStep: 3 };
								}
								return item;
							});
							if (docAnswerFilesIndex === 1) {
								this.pendingDocContent += item;
								if (item.includes('</docAnswerFiles>')) {
									docAnswerFilesIndex = 2;
									this.handleDocument(item);
								}
								return '';
							}

							if (recommendedQuestionsIndex === 1) {
								this.pendingRecContent += item;
								if (item.includes('</recommendedQuestions>')) {
									recommendedQuestionsIndex = 2;
									this.handleDocument(item);
								}
								return '';
							}
							if (executionPathIndex === 1) {
								this.executionPathContent += item;
								if (item.includes('</executionPath>')) {
									executionPathIndex = 2;
									this.handleDocument(item);
								}
								return '';
							}
							// 处理 <docAnswerFiles> 标签
							if (item.includes('docAnswerFiles')) {
								this.pendingDocContent += item;
								if (item.includes('</docAnswerFiles>')) {
									docAnswerFilesIndex = 2;
									this.handleDocument(item);
								} else {
									docAnswerFilesIndex += 1;
								}
								return '';
							}
							// 处理 <recommendedQuestions> 标签
							if (item.includes('recommendedQuestions')) {
								this.pendingRecContent += item;
								if (item.includes('</recommendedQuestions>')) {
									recommendedQuestionsIndex = 2;
									this.handleDocument(item);
								} else {
									recommendedQuestionsIndex += 1;
								}

								return '';
							}
							// 处理 <executionPath> 标签
							if (item.includes('executionPath')) {
								this.executionPathContent += item;
								if (item.includes('</executionPath>')) {
									executionPathIndex = 2;
									this.handleDocument(item);
								} else {
									executionPathIndex += 1;
								}
								return '';
							}
							// 完成字段不渲染
							if (item.indexOf('complete') > -1) {
								this.tempAgentObj = {};
								this.messageList.forEach(item => {
									if (item.id === id) {
										item.currentStep = 4;
									}
								});
								return '';
							} else {
								if (item.indexOf('noAnswer') > -1) {
									this.tempAgentObj = {};
									return 'noAnswer';
								}
								return Base64.decode(item);
							}
						});
						if (readDone && !res.includes('noAnswer')) {
							res.push('noAnswer');
						}
					}
					// 老接口处理，会断流
					// res =
					// 	'[' +
					// 	res
					// 		.replaceAll('data:', '')
					// 		.replace(/[\r\n\s]/gi, '')
					// 		.replaceAll('}{', '},{') +
					// 	']';
					// res = JSON.parse(res); // 转换成数组
					await this.handleAiRes(res, id, isAll, message);
				} catch (err) {
					// let resStr = decoder.decode(value);
					// resStr = resStr.split('data:');
					// this.handleDocument(resStr);
					console.log('解析流式报错', err, '--------', res);
				}
			}
		},
		handleDocument(str) {
			this.processRecommendedQuestions().catch(e => {
				console.log('推荐问题处理失败', e);
			});
			this.processExecutionPath().catch(e => {
				console.log('processExecutionPath处理失败', e);
			});
			this.processDocAnswerFiles().catch(e => {
				console.log('文档来源处理失败', e);
			});
		},
		async processTagContent({ tag, contentKey, targetField, isFlag = false }) {
			const regex = new RegExp(`<${tag}>([\\s\\S]*?)<\\/${tag}>`);
			const match = this[contentKey].match(regex);
			if (match && match[1]) {
				try {
					const parsedData = JSON.parse(match[1].trim());
					const current = this.messageList.find(msg => msg.id === this.lastMessageId);
					if (current) {
						if (isFlag) {
							this.$set(current, targetField, true);
							// current[targetField] = true;
						} else {
							this.$set(current, targetField, parsedData);
							// current[targetField] = parsedData;
						}
					}
					// 更新 pending 内容，移除已处理部分
					this[contentKey] = this[contentKey].substring(match.index + match[0].length);
					return parsedData;
				} catch (e) {
					console.error(`解析 ${tag} 失败:`, e, 111, match);
					// 删除所有匹配项，防止重复报错
					this[contentKey] = this[contentKey].replace(regex, '');
				}
			}
			return null;
		},

		// 推荐问题处理
		async processRecommendedQuestions() {
			return this.processTagContent({
				tag: 'recommendedQuestions',
				contentKey: 'pendingRecContent',
				targetField: 'recommendedQuestions'
			});
		},

		// 文档来源处理
		async processDocAnswerFiles() {
			return this.processTagContent({
				tag: 'docAnswerFiles',
				contentKey: 'pendingDocContent',
				targetField: 'answerFrom'
			});
		},
		// executionPath处理
		async processExecutionPath(modelType) {
			return this.processTagContent({
				tag: 'executionPath',
				contentKey: 'executionPathContent',
				targetField: 'executionPath'
			});
		},
		/**
		 * @method 通过知识库获取答案
		 * @param {String} id 临时消息队列id
		 * @param {String} message 发送的消息
		 * @param {Boolean} isAll 是否全部知识库查询
		 * */
		async getQuery(id, message, isAll = false) {
			// if (!this.sessionId) {
			// 	let res = await createMessage({
			// 		title: message,
			// 		type: ~~getDictionary(`智能助手/${this.currentMode.title}`)
			// 	});
			// 	if (res.code === 200) {
			// 		this.sessionId = res.result || '';
			// 	} else {
			// 		this.$message.error(res.message);
			// 	}
			// }
			let allIds = this.knowledge.map(item => {
				return item.id;
			});
			let aiRes;
			// 组件模式
			if (this.isComponent) {
				// coosType(waitList:待办列表  space:我的空间)
				if (this.coosType === 'space') {
					aiRes = await getSpaceAiAnswerBySocket({
						query: message,
						dialogChatId: this.uuId
						// chatType: '5' //对话类型 2待办智能问答 4待办列表知识库对话
					});
				} else {
					aiRes = await getWorkAiAnswerBySocket({
						query: message,
						chatType: this.coosType === 'waitList' ? '4' : '2', //对话类型 2待办智能问答 4待办列表知识库对话
						robotDataContent: this.robotDataContent,
						chatObjectId: this.chatObjectId, //业务对话id
						metadataIds: [this.metadataIds] // 元数据ids
					});
				}
			}
			// 联网搜索的逻辑
			else if (this.searchType === 'IsInternetSearch') {
				aiRes = await getNetworkAnswer({
					query: message,
					chatType: this.currentMode.sessionTypes,
					sessionId: this.sessionId // 会话id
				});
			}
			// 数据智析模式
			else if (this.currentMode.type === 'sjzx') {
				aiRes = await geDataDrillingAiAnswer({
					query: message,
					chatType: '3', //对话类型 3数据智析模式
					sessionId: this.sessionId, // 会话id
					metadataIds: this.currentDataDrilling ? [this.currentDataDrilling.id] : [] // 元数据ids
				});
			}
			// 问政（知识库、问策、tosql）
			else if (this.currentThinkMode === 'number') {
				let res = await this.requestSocket(message, id);
				if (!res) return;
				let { requestApi, requestParmas } = res;
				aiRes = await requestApi(requestParmas);
			}
			// 其他模式的通用接口
			else {
				// 办公助手 只在第一个我要提问模块中使用
				if (
					this.currentModeButtons &&
					this.searchType === 'administrativeAssistant' &&
					this.currentModeButtons.includes('administrativeAssistant')
				) {
					if (!this.sessionId) {
						this.sessionId = uuidv4();
					}
					this.controller = new AbortController();
					const { signal } = this.controller;
					aiRes = await getQueryByAdministrativeAssistant(
						{
							message: message,
							// sessionId: this.sseUUID,
							sessionId: this.sessionId,
							sessionType: this.currentMode.sessionTypes,
							// agentId: '1915686262640025600'
							agentId: '1922830784707207168' // 测试环境
						},
						signal
					);
				} else {
					let aiQuery = newAiQuery;
					let data = {
						query: message,
						chatType: this.currentMode.sessionTypes,
						sessionId: this.sessionId // 会话id
					};
					// 深度思考
					data.deepSeek = this.deepThink;
					// 智答宝库模式   并且   选择了知识库
					if (this.currentMode.type === 'zdbk' && this.searchType === 'isKnowledge') {
						data.metadataIds = isAll ? allIds : this.checkList; // 知识库id集合
					}
					if (
						this.currentModeButtons &&
						this.currentModeButtons.includes('defaultKnowledge') &&
						this.searchType !== 'defaultKnowledge'
					) {
						// 经信局默认选择知识库默认
						aiQuery = newAiQueryStreamToString;
						data.metadataIds = this.checkList; // 知识库id集合
					}
					// 上传了文件
					if (this.fileQuery) {
						data.fileDialogId = this.fileQuery.id;
						data.metadataIds = [];
						aiQuery = newAiQuery;
					}
					this.controller = new AbortController();
					const { signal } = this.controller;
					aiRes = await aiQuery(data, signal);
				}
			}

			// 获取AI答案
			const reader = aiRes.body.getReader(); // 流式读取器
			const decoder = new TextDecoder('utf-8');
			let readDone = false; // 读取完毕
			this.answerFromId = ''; // 清空上次对话的answerId
			this.pendingDocContent = ''; // 清空缓存未完成的 <docAnswerFiles> 内容
			this.pendingRecContent = ''; // 清空缓存未完成的 <recommendedQuestions> 内容
			this.pendingAnswerFromContent = ''; // 清空缓存未完成的 <answerFrom> 内容
			let docAnswerFilesIndex = 0;
			let recommendedQuestionsIndex = 0;
			let answerFromIndex = 0;
			let executionPathIndex = 0;
			// 流式输出
			while (!readDone) {
				let { value, done } = await reader.read();
				readDone = done;
				let res;
				try {
					res = decoder.decode(value);
					if (/"code":50/.test(res)) {
						let r = JSON.parse(res.replace('data:', ''));
						res = [r.message || '服务器异常', 'noAnswer'];
					} else {
						// res = res.replace(/data:/gi, ''); // 解析成JSON，去掉data:开头的字符串
						res = res.split('data:');
						res = res.map(item => {
							// 办公助手 处理 <answerFrom> 标签
							if (this.searchType === 'administrativeAssistant') {
								if (answerFromIndex > 0) {
									const spliceItem = this.pendingAnswerFromContent + item;
									if (spliceItem.includes('answerFrom')) {
										try {
											JSON.parse(spliceItem);
											answerFromIndex = 0;
											item = spliceItem;
										} catch (error) {
											answerFromIndex += 1;
											this.pendingAnswerFromContent += item;
											return '';
										}
									}
								} else {
									if (item.includes('answerFrom')) {
										try {
											JSON.parse(item);
											answerFromIndex = 0;
										} catch (error) {
											answerFromIndex += 1;
											this.pendingAnswerFromContent += item;
											return '';
										}
									}
								}
							}
							this.messageList = this.messageList.map(item => {
								if (item.id === id && item.currentStep < 3) {
									return { ...item, currentStep: 3 };
								}
								return item;
							});

							// 处理 <executionPath> 标签
							if (executionPathIndex === 1) {
								this.executionPathContent += item;
								if (item.includes('</executionPath>')) {
									executionPathIndex = 2;
									this.handleDocument(item);
								}
								return '';
							}
							if (docAnswerFilesIndex === 1) {
								this.pendingDocContent += item;
								if (item.includes('</docAnswerFiles>')) {
									docAnswerFilesIndex = 2;
									this.handleDocument(item);
								}
								return '';
							}

							if (recommendedQuestionsIndex === 1) {
								this.pendingRecContent += item;
								if (item.includes('</recommendedQuestions>')) {
									recommendedQuestionsIndex = 2;
									this.handleDocument(item);
								}
								return '';
							}

							// 处理 <docAnswerFiles> 标签
							if (item.includes('docAnswerFiles')) {
								this.pendingDocContent += item;
								if (item.includes('</docAnswerFiles>')) {
									docAnswerFilesIndex = 2;
									this.handleDocument(item);
								} else {
									docAnswerFilesIndex += 1;
								}
								return '';
							}
							// 处理 <recommendedQuestions> 标签
							if (item.includes('recommendedQuestions')) {
								this.pendingRecContent += item;
								if (item.includes('</recommendedQuestions>')) {
									recommendedQuestionsIndex = 2;
									this.handleDocument(item);
								} else {
									recommendedQuestionsIndex += 1;
								}
								return '';
							}
							// 处理 <executionPath> 标签
							if (item.includes('executionPath')) {
								this.executionPathContent += item;
								if (item.includes('</executionPath>')) {
									executionPathIndex = 2;
									this.handleDocument(item);
								} else {
									executionPathIndex += 1;
								}
								return '';
							}
							// 完成字段不渲染
							if (item.indexOf('complete') > -1) {
								this.tempAgentObj = {};
								return '';
							} else {
								if (item.indexOf('noAnswer') > -1) {
									this.tempAgentObj = {};
									return 'noAnswer';
								}
								return item.includes('answerFrom') || item.includes('transactionForm') // 来源 、流式表单提交
									? item
									: Base64.decode(item);
							}
						});
						if (readDone && !res.includes('noAnswer')) {
							res.push('noAnswer');
						}
					}
					// 老接口处理，会断流
					// res =
					// 	'[' +
					// 	res
					// 		.replaceAll('data:', '')
					// 		.replace(/[\r\n\s]/gi, '')
					// 		.replaceAll('}{', '},{') +
					// 	']';
					// res = JSON.parse(res); // 转换成数组await this.handleAiRes(res, id, isAll, message);
					if (this.searchType === 'administrativeAssistant') {
						await this.handleAiRes1(res, id, isAll, message);
					} else {
						await this.handleAiRes(res, id, isAll, message);
					}
				} catch (err) {
					console.log('流式解析报错--------------', err);
				}
			}
		},
		/**
		 * @method AI对话的数据处理
		 * @param {Array} values 接收到的答案
		 * @param {String} messageId 答案对应的消息队列id
		 * @param {Boolean} isAll 是否全选知识库
		 * @param {String} message 当前的问题
		 * */
		async handleAiRes(values, messageId, isAll, message) {
			let text = ''; // 回答消息
			let answerFrom = ''; // 当类型变为数组的时候标志着对话结束
			let answerFromId = ''; // 文档来源
			values.forEach(value => {
				// 检测到noAnswer标志对话结束（有时候后端结束没有返回noAnswer，前端模拟统一处理，但是也有可能已经返回过了，所以根据是否已经处理过answerId来判断是否还要进行处理）
				if (value === 'noAnswer' && !this.answerFromId) {
					console.log('回答结束');
					answerFrom = [];
					this.messageList.forEach(item => {
						if (item.id === messageId) {
							item.currentStep = 4;
							item.answerDone = true;
							this.IsSend = false;
							item.answerFromDone = true;
						}
					});
				}
				// 以前联调最后一句话会是对象结构，标志对话结束
				try {
					if (/^{/.test(value)) {
						let obj = JSON.parse(value);
						if (!this.isComponent && obj.sessionId) {
							this.sessionId = obj.sessionId;
						}
						if (
							(!this.isComponent || this.componentType === 'glob') &&
							this.currentMode.type === 'zdbk'
						) {
							answerFromId = obj.id || '';
						} else {
							answerFrom = [];
						}
						if (this.coosType === 'space') {
							// 我的空间有文档来源
							answerFromId = obj.id;
						}
					} else {
						text += value === 'noAnswer' ? '' : value;
					}
				} catch (err) {
					console.log('单次接受解析报错：', value);
					text += value;
				}
			});
			if (answerFromId) {
				this.answerFromId = answerFromId;
				let answerFromRes = await getAnswerFrom(answerFromId);
				if (answerFromRes.code === 200) {
					answerFrom = answerFromRes.result.answerFrom
						? JSON.parse(answerFromRes.result.answerFrom)
						: [];
				} else {
					this.$message.error(answerFromRes.message);
				}
			}
			this.messageList.forEach(item => {
				if (item.id === messageId) {
					// 思考结束往答案中累积，未结束，往思考过程中加
					let newText = (item.startThink ? item.thinkCon : this.cacheAnswer) + text;
					// 检测到了完整的开始标记，说明开始思考  并且替换掉标记
					if (/<think>/gi.test(newText)) {
						// 有深度思考
						item.isThink = true;
						item.startThink = true;
						newText = newText.replace(/<think>/gi, '');
						// 深度思考的答案赋值给思考内容
						item.thinkCon = newText;
					}
					// 检测到了完整的结束标记，说明思考结束  并且替换掉标记
					else if (/<\/think>/gi.test(newText)) {
						item.startThink = false;
						let i = newText.indexOf('</think>');
						// 思考结束前属于思考过程
						item.thinkCon = newText.slice(0, i);
						// 思考结束后属于答案
						this.cacheAnswer = newText.slice(i + 8);
					}
					// 如果思考并未结束，返回内容为思考的内容
					else if (item.startThink) {
						item.thinkCon = newText;
					} else {
						this.cacheAnswer = newText;
					}
					if (this.cacheAnswer) {
						// 新版本，支持各种答案混合渲染，可能都是通过流式慢输出的字符串，自己解析
						let answerMulti = this.handleMultiAnswer(this.cacheAnswer, item.id);
						// 减少渲染负担，如果正在转换就不重复赋值
						answerMulti[0].currentStep = item.currentStep;
						answerMulti[0].hasStep = item.hasStep;
						if (this.transformIndex < 2) {
							item.answerMulti = answerMulti;
						}
					}
					//&& answerFrom.length
					if (Array.isArray(answerFrom)) {
						// 获取推荐问题
						let recommendedQuestions = answerFrom.reduce((base, form) => {
							base = base.concat(form.questions || []);
							return base;
						}, []);
						if (recommendedQuestions.length) {
							item.recommendedQuestions = [...new Set(recommendedQuestions)].slice(0, 3);
						}
						answerFrom = answerFrom.concat(item.answerFrom);
						// 处理文档来源
						item.answerFrom = answerFrom.map(form => {
							if (form && form.disk_file && form.disk_file.links) {
								let links = JSON.parse(form.disk_file.links);
								form.disk_file.links = links.filter(link => {
									return link.pc;
								});
								form.openLink = true; // 如果有三方链接，设一个收起打开状态
							}
							if (!form.disk_file) {
								form.disk_file = {};
							}
							if (form.fileName) {
								form.disk_file.name = form.fileName;
							}
							if (form.spaceName) {
								form.disk_file.spaceName = form.spaceName;
							}
							if (form.createByName) {
								form.disk_file.createByName = form.createByName;
							}
							if (form.createTime) {
								form.disk_file.createTime = form.createTime;
							}
							if (form.fileUrlPath) {
								form.disk_file.fileUrlPath = form.fileUrlPath;
							}
							return form;
						});
						item.answer = this.cacheAnswer;
						this.cacheAnswer = ''; // 清空缓存
						item.isAll =
							this.checkList.length === 0 || // 未选中知识库，直接打模型对话的情况
							this.checkList.length === this.knowledge.length || // 选中全部知识库查找的情况
							isAll; // 指定知识库未查找，传参isAll从全部查找的情况
						item.answerDone = true;
						this.IsSend = false;
						item.answerFromDone = true;
						// 没有答案的时候要判断是解析出错还是没有文档来源
						if (!item.answer) {
							if (this.currentMode.type === 'zdbk' && this.searchType === 'isKnowledge') {
								// 来源为空，知识库有选中，说明在知识库中没有找到答案
								// 来源不是空的/知识库未选中（直接对话大模型），说明解析出粗
								item.answer =
									isEmpty(item.answerFrom) && this.checkList && this.checkList.length > 0
										? item.isAll
											? `很抱歉，小助手在全部知识库中，未查找到"${message}"相关内容。`
											: `很抱歉，小助手在你指定的知识库中，未查找到"${message}"相关内容。点击下方“全部查询”按钮，${this.coosTitle}将为你在全部知识库中进行查询。`
										: '解析出错，请及时联系管理员，并打开F12复制错误发送管理员！';
							} else {
								item.answer = `很抱歉，小助手未查找到"${message}"相关内容`;
							}
						}
					}
				}
			});
			if (!this.lockAutoScroll) {
				this.toBottom();
			}
		},
		/**
		 * @method 办公助手AI对话的数据处理
		 * @param {Array} values 接收到的答案
		 * @param {String} messageId 答案对应的消息队列id
		 * @param {Boolean} isAll 是否全选知识库
		 * @param {String} message 当前的问题
		 * */
		async handleAiRes1(values, messageId, isAll, message) {
			let text = ''; // 回答消息
			let answerFrom = ''; // 文档来源
			let transactionForm = null; // 表单
			values.forEach(value => {
				// 检测到noAnswer标志对话结束（有时候后端结束没有返回noAnswer，前端模拟统一处理，但是也有可能已经返回过了，所以根据是否已经处理过answerId来判断是否还要进行处理）
				if (value === 'noAnswer') {
					answerFrom = [];
					this.messageList.forEach(item => {
						if (item.id === messageId) {
							item.currentStep = 4;
						}
					});
				}
				try {
					if (/^{/.test(value)) {
						let obj = JSON.parse(value);
						// 非组件模式才存储sessionId
						if (!this.isComponent && obj.sessionId) {
							this.sessionId = obj.sessionId;
						}
						// 有文档来源，就赋值包括会话id
						if (obj.answerFrom) {
							answerFrom = JSON.parse(obj.answerFrom);
						}
						// 表单
						if (obj.transactionForm) {
							transactionForm = obj;
						}
					}
					// 否则累积答案文字
					else {
						text += value === 'noAnswer' ? '' : value;
					}
				} catch (e) {
					console.log('单次接受解析报错：', e, '--------', value);
					text += value;
				}
			});

			this.messageList.forEach(item => {
				if (item.id === messageId) {
					// 办公助手 嵌套表单提交
					if (transactionForm) {
						let result_json = JSON.parse(transactionForm.transactionForm);
						// result_json.data = JSON.parse(result_json.data)
						let isSpecialAnswer = this.getIsSpecial(result_json); // 答案是特殊渲染
						let answerType = Array.isArray(result_json) ? 'MULTI' : result_json.functionType; // 1是表单模式  2数据列表模式  3应用  MULTI组合模式
						let newResult_json = {
							data: result_json.urls,
							dataInstanceId: result_json.dataInstanceId,
							answerType: result_json.answerType
						};
						item.answer = answerType === 3 ? [newResult_json] : newResult_json; // 应用模式转成数组按照组合模式进行渲染
						item.answerType = answerType;
						item.isSpecialAnswer = isSpecialAnswer; // 答案是特殊渲染
						item.title = this.getSpecialTitle(answerType);
						item.isForm = true; // 流式表单提交 流式会输入多段 新增字段判断不处理其他逻辑
						item.answerDone = true;
						this.IsSend = false;
					} else {
						// 流式表单下面不执行
						if (item.isForm) {
							return;
						}
						// 思考结束往答案中累积，未结束，往思考过程中加
						let newText = (item.startThink ? item.thinkCon : this.cacheAnswer) + text;
						// 检测到了完整的开始标记，说明开始思考  并且替换掉标记
						if (/<think>/gi.test(newText)) {
							// 有深度思考
							item.isThink = true;
							item.startThink = true;
							newText = newText.replace(/<think>/gi, '');
							// 深度思考的答案赋值给思考内容
							item.thinkCon = newText;
						}
						// 检测到了完整的结束标记，说明思考结束  并且替换掉标记
						else if (/<\/think>/gi.test(newText)) {
							item.startThink = false;
							let i = newText.indexOf('</think>');
							// 思考结束前属于思考过程
							item.thinkCon = newText.slice(0, i);
							// 思考结束后属于答案
							this.cacheAnswer = newText.slice(i + 8);
						}
						// 如果思考并未结束，返回内容为思考的内容
						else if (item.startThink) {
							item.thinkCon = newText;
						} else {
							this.cacheAnswer = newText;
						}
						if (this.cacheAnswer) {
							// 新版本，支持各种答案混合渲染，可能都是通过流式慢输出的字符串，自己解析
							let answerMulti = this.handleMultiAnswer(this.cacheAnswer, item.id);
							// 减少渲染负担，如果正在转换就不重复赋值
							if (this.transformIndex < 2) {
								item.answerMulti = answerMulti;
							}
							// 更新步骤条
							if (this.currentStep < 3) {
								this.currentStep = 3;
								item.currentStep = 3;
							}
						}
						if (Array.isArray(answerFrom)) {
							// 获取推荐问题
							let recommendedQuestions = answerFrom.reduce((base, form) => {
								base = base.concat(form.questions || []);
								return base;
							}, []);
							if (recommendedQuestions.length) {
								item.recommendedQuestions = [...new Set(recommendedQuestions)].slice(0, 3);
							}
							answerFrom = answerFrom.concat(item.answerFrom);
							answerFrom = answerFrom.map(form => {
								if (form.disk_file.links) {
									let links = JSON.parse(form.disk_file.links);
									form.disk_file.links = links.filter(link => {
										return link.app;
									});
									form.openLink = true; // 如果有三方链接，设一个收起打开状态
								}
								return form;
							});
							item.answerFrom = answerFrom;
							item.answer = this.cacheAnswer;
							this.cacheAnswer = ''; // 清空缓存
							item.answerDone = true;
							this.IsSend = false;
							item.answerFromDone = true;

							// 没有答案的时候要判断是解析出错还是没有文档来源
							if (!item.answer) {
								if (this.currentMode.type === 'zdbk' && this.searchType === 'isKnowledge') {
									// 来源为空，知识库有选中，说明在知识库中没有找到答案
									// 来源不是空的/知识库未选中（直接对话大模型），说明解析出粗
									item.answer =
										isEmpty(item.answerFrom) && this.checkList && this.checkList.length > 0
											? item.isAll
												? `很抱歉，小助手在全部知识库中，未查找到"${message}"相关内容。`
												: `很抱歉，小助手在你指定的知识库中，未查找到"${message}"相关内容。点击下方“全部查询”按钮，${this.coosTitle}将为你在全部知识库中进行查询。`
											: '解析出错，请及时联系管理员，并打开F12复制错误发送管理员！';
								} else {
									item.answer = `很抱歉，小助手未查找到"${message}"相关内容`;
								}
							}
						}
					}
				}
			});
			if (!this.lockAutoScroll) {
				this.toBottom();
			}
		},
		/**获取特殊渲染的标题(2可能有图表、表格、文字，但是目前没有存储聊天记录，如果有需要再联调)*/
		getSpecialTitle(answerType) {
			let title = '';
			switch (answerType) {
				case 2: {
					title = '表格';
					break;
				}
				case 3: {
					title = '为你推荐以下应用';
					break;
				}
				case 'MULTI': {
					title = '为你推荐以下应用';
					break;
				}
			}
			return title;
		},
		/**判断是不是特殊渲染数据*/
		getIsSpecial(result_json) {
			// 只有智控模式  1表单  2列表  只有列表需要特殊判断，很多种数据格式
			let isSpecialAnswer = Array.isArray(result_json) || result_json.functionType !== 2; // 默认其他类型为true
			// 答案不是普通答案并且不是模式2 如果是模式2就要满足多数据模式
			if (!isSpecialAnswer) {
				if (Array.isArray(result_json.data) && result_json.data.length > 0) {
					isSpecialAnswer =
						result_json.data.length > 1 || Object.keys(result_json.data[0]).length > 1;
				} else {
					isSpecialAnswer = false;
				}
			}
			return isSpecialAnswer;
		},
		/**处理字符串md中包含多种类型的答案*/
		handleMultiAnswer(text, id) {
			// 使用正则表达式匹配 ````chart` 包裹的内容
			const chartRegex = /```chart\n([\s\S]*?)\n```/g;
			const segments = [];
			let lastIndex = 0;
			let match;

			// 遍历所有匹配的 ````chart` 部分
			while ((match = chartRegex.exec(text)) !== null) {
				// 添加 ````chart` 之前的部分
				if (match.index > lastIndex) {
					segments.push({
						answerType: 'normal',
						answer: text.slice(lastIndex, match.index),
						id: id + '-' + segments.length
					});
				}

				try {
					// 尝试解析 JSON 内容
					// const chartData = JSON.parse(match[1]);
					const chartData = eval('(' + match[1] + ')');
					let options = commonEchartsOptions(chartData);
					segments.push({
						answerType: 'chart',
						answer: options,
						height: (options.customHeight || 0) + 400 + 'px', // 画布的高度
						hideUtils: true,
						id: id + '-' + segments.length
					});
				} catch (e) {
					console.log('解析图表报错-------------------', match[1], e);
					// 如果解析失败，保留原始字符串
					segments.push({
						answerType: 'normal',
						answer: '【图表数据转换失败】',
						id: id + '-' + segments.length
					});
				}

				lastIndex = match.index + match[0].length;
			}

			// 检查是否有未闭合的chart标记
			const lastChartIndex = text.lastIndexOf('```chart');
			const lastCloseIndex = text.lastIndexOf('```');
			let hasNoClose = false;
			// 如果有开始的chart标记但没有对应的结束标记
			if (lastChartIndex > -1 && lastChartIndex >= lastCloseIndex) {
				// 只处理到最后一个完整chart之前的部分
				hasNoClose = true;
			}

			// 添加最后一个 ````chart` 之后的部分
			if (lastIndex < text.length) {
				segments.push({
					answerType: 'normal',
					answer: hasNoClose
						? text.slice(lastIndex, lastChartIndex).replace(/\\n/gi, '')
						: text.slice(lastIndex),
					id: id + '-' + segments.length
				});
			}
			if (hasNoClose) {
				this.transformIndex += 1;
				segments.push({
					answerType: 'normal',
					answer: '【图表数据转换中，请稍后...】',
					id: id + '-' + segments.length
				});
			} else {
				this.transformIndex = 0;
			}
			return segments;
		}
	}
};
