<template>
	<div class="documents-container">
		<div class="documents-header">
			<h1 class="title">智能文书</h1>
			<p class="subtitle">写好材料，轻松办公</p>
		</div>

		<div class="documents-content">
			<div
				v-for="(item, index) of forwardLookSections"
				:key="index"
				class="document-card"
				:style="{ background: backList[index] }"
				@click="handleCardClick(item)"
			>
				<div class="card-content" :style="{ backgroundImage: `url(${urlHttp(item.image)})` }">
					<h3>{{ item.name }}</h3>
					<p>{{ item.describe }}</p>
					<div v-if="!item.url" class="card-tips">开发中</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import { urlHttp } from '@/utils';

export default {
	name: 'DocumentsIndex',

	components: {},

	props: {
		forwardLookSections: {
			type: Array,
			default: () => {
				return [];
			}
		}
	},

	data() {
		return {
			backList: [
				'linear-gradient( 90deg, #EDEFFF 0%, #E6F0F8 69%)',
				'linear-gradient( 90deg, #EDEFFF 0%, #E6F0F8 69%)',
				'linear-gradient( 90deg, #EDEFFF 0%, #E6F0F8 69%)',
				'linear-gradient( 90deg, #EDEFFF 0%, #E6F0F8 69%)'
			]
		};
	},

	computed: {},

	watch: {},

	created() {},

	mounted() {},

	methods: {
		urlHttp,
		handleCardClick(item) {
			this.$emit('changeType', item);
		}
	}
};
</script>

<style lang="scss" scoped>
.documents-container {
	height: 100%;
	width: 100%;
	background-color: #fff;
	padding: 20px;
}

.documents-header {
	text-align: center;
	margin-bottom: 40px;

	.title {
		font-size: 28px;
		font-weight: bold;
		color: #333;
		margin-bottom: 10px;
	}

	.subtitle {
		font-size: 16px;
		color: #666;
	}
}

.documents-content {
	max-width: 1200px;
	margin: 0 auto;
	display: flex;
	gap: 20px;
	flex-wrap: wrap;
}

.document-card {
	width: calc(50% - 10px);
	display: flex;
	justify-content: space-between;
	background-color: #f0f5ff;
	cursor: pointer;
	transition: all 0.3s;
	position: relative;
	overflow: hidden;
	border-radius: 12px;

	.card-tips {
		position: absolute;
		left: 0;
		top: 0;
		right: 0;
		bottom: 0;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 16px;
		background-color: #fff;
		opacity: 0;
		cursor: not-allowed;
		z-index: 9;
		color: rgba(0, 0, 0, 0.25);
	}
	&:hover {
		transform: translateY(-5px);
		box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
		.card-tips {
			opacity: 0.7;
		}
	}

	.card-content {
		width: 100%;
		padding: 20px;
		height: 170px;
		display: flex;
		flex-direction: column;
		justify-content: center;
		background-position: 92% bottom;
		background-repeat: no-repeat;

		h3 {
			font-size: 18px;
			color: #333;
			margin-bottom: 10px;
		}

		p {
			font-size: 14px;
			color: #666;
		}
	}
}
</style>
