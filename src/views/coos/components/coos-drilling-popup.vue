<template>
	<el-dialog
		:visible.sync="show"
		:append-to-body="true"
		:modal-append-to-body="true"
		class="desk-el-dialog"
		top="0"
	>
		<div v-loading="loading" class="content">
			<div class="title">
				<div>全部业务数据</div>
				<i class="coos-iconfont icon-guanbi1 close-icon" @click="show = false"></i>
			</div>
			<div class="search">
				<i class="coos-iconfont icon-search search-icon"></i>
				<el-input
					v-model="params.moduleName"
					class="search-input"
					placeholder="搜索查找插件"
					autocomplete="off"
					@input="search"
				></el-input>
			</div>
			<div class="list">
				<div class="list-content">
					<div
						v-for="(item, index) of dataDrillingList"
						:key="index"
						:style="{ marginRight: (index + 1) % 3 === 0 ? 0 : '16px' }"
						class="list-item"
						:class="{ select: currentDataDrilling && item.name === currentDataDrilling.name }"
						@click="selectDataDrilling(item)"
					>
						<div class="list-item-top">
							<img v-if="item.icon" class="list-item-top-img" :src="item.icon" />
							<svg-icon v-else icon-class="default-icon" class="list-item-top-img"></svg-icon>
							<div class="list-item-top-text">{{ item.moduleName }}</div>
						</div>
						<div class="list-item-desc">{{ item.remark }}</div>
					</div>
				</div>
			</div>
			<el-pagination
				class="desk-el-pagination"
				background
				:current-page="params.pageNo"
				:page-sizes="[10, 20, 50, 100]"
				:page-size="params.pageSize"
				layout="total, sizes, ->, prev, pager, next, jumper"
				:total="total"
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
			></el-pagination>
		</div>
	</el-dialog>
</template>

<script>
import { getDataDrilling } from '@/api/modules/coos';
import { debounce } from '@/utils';

export default {
	name: 'DataDrillingPopup',
	props: {
		initDataDrilling: {
			type: Object,
			default: () => {
				return null;
			}
		}
	},
	data() {
		return {
			loading: false,
			params: {
				pageNo: 1,
				pageSize: 10,
				moduleName: ''
			},
			total: 0,
			show: false,
			currentDataDrilling: null, // 当前插件
			dataDrillingList: [] // 插件原始数据
		};
	},
	watch: {
		initDataDrilling(newVal) {
			this.currentDataDrilling = newVal;
		}
	},
	mounted() {
		this.getData();
	},
	methods: {
		/**选择插件*/
		selectDataDrilling(item) {
			this.show = false;
			this.$emit('selectDataDrilling', item);
		},
		/**获取全部插件*/
		getData() {
			this.loading = true;
			getDataDrilling(this.params).then(res => {
				this.loading = false;
				this.total = res.result.total;
				this.dataDrillingList = res.result.records || [];
			});
		},
		open() {
			this.show = true;
		},
		/**改变页码*/
		handleCurrentChange(i) {
			this.params.pageNo = i;
			this.getData();
		},
		/**改变分页大小*/
		handleSizeChange(i) {
			this.params.pageNo = 1;
			this.params.pageSize = i;
			this.getData();
		},
		search: debounce(
			function () {
				this.params.pageNo = 1;
				console.log('请求');
				this.getData();
			},
			500,
			false
		)
	}
};
</script>

<style scoped lang="scss">
.desk-el-dialog {
	display: flex;
	align-items: center;
	justify-content: center;

	::v-deep .el-dialog {
		border-radius: 16px;
		min-width: 530px;
		margin: 0;

		.el-dialog__header {
			display: none;
		}

		.el-dialog__body {
			padding: 18px 30px;
			border-radius: 16px;
			background: linear-gradient(180deg, #e5f3ff 0%, #ffffff 30%, #ffffff 100%),
				linear-gradient(148deg, #afcaff 0%, rgba(175, 202, 255, 0) 100%),
				linear-gradient(203deg, #deffe7 0%, rgba(222, 255, 231, 0) 100%), rgba(255, 255, 255, 0.2);
		}

		.el-dialog__headerbtn {
			top: 31px;
			right: 30px;
		}
	}
}

.content {
	max-height: 80vh;
	min-height: 560px;
	display: flex;
	flex-direction: column;

	.title {
		@include flexBox(space-between);
		font-weight: 800;
		font-size: 18px;
		color: #303133;
		line-height: 24px;
		text-align: left;
		font-style: normal;
		text-transform: none;

		.close-icon {
			font-size: 16px;
			color: #606266;
			cursor: pointer;
		}
	}

	.search {
		margin: 15px 0 14px;
		height: 40px;
		display: flex;
		align-items: center;
		padding: 7px 12px;
		background: #ffffff;
		border-radius: 32px;
		width: 100%;

		&-icon {
			flex-shrink: 0;
			font-size: 16px;
			cursor: pointer;
			color: var(--brand-6);
			margin-right: 12px;
		}

		&-input {
			flex: 1;
			height: 100%;
			width: 100%;
			border-radius: 6px;

			::v-deep .el-input__inner {
				height: 100%;
				width: 100%;
				border-radius: 6px;
				border: none;
				outline: none;
				padding: 0 0 0 4px;

				&:focus {
					box-shadow: none;
				}

				&::placeholder {
					font-family: PingFang SC, PingFang SC;
					font-weight: 400;
					font-size: 14px;
					color: $holderTextColor;
					line-height: 22px;
					text-align: left;
					font-style: normal;
					text-transform: none;
				}
			}
		}
	}

	.tabs {
		padding-left: 47px;
	}

	.line {
		height: 1px;
		background: #dce3e7;
		margin: 0 -30px;
	}

	.list {
		flex: 1;
		overflow-y: auto;
		&-content {
			display: flex;
			flex-wrap: wrap;
		}
		@include noScrollBar;

		&-item {
			width: calc((100% - 32px) / 3);
			height: 96px;
			background: #f6f9fd;
			border-radius: 9px;
			margin-top: 16px;
			padding: 10px 12px;
			cursor: pointer;

			&-top {
				@include flexBox(flex-start);

				&-img {
					height: 32px;
					width: 32px;
					margin-right: 6px;
					border-radius: 6px;
				}

				&-text {
					font-weight: 500;
					font-size: 14px;
					color: $primaryTextColor;
					line-height: 24px;
					text-align: left;
					font-style: normal;
					text-transform: none;
					@include countEllipse(2);
				}
			}

			&-desc {
				margin-top: 8px;
				font-weight: 400;
				font-size: 12px;
				color: $subTextColor;
				line-height: 20px;
				text-align: left;
				font-style: normal;
				text-transform: none;
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
			}
		}

		.select {
			border: 1px solid var(--brand-6);
			padding: 9px 11px;
		}
	}
}

::v-deep .el-tabs__nav-wrap::after {
	display: none;
}

::v-deep .el-tabs__header {
	margin-bottom: 0 !important;
}
</style>

<style lang="scss" scoped>
::v-deep .el-dialog {
	width: 960px;
}
</style>
