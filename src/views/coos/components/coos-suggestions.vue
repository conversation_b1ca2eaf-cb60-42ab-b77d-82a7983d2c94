<template>
	<div class="page">
		<div class="page-title">意见反馈</div>
		<div class="page-desc">感谢您的反馈，您的关注是我们前进的动力！</div>
		<el-form
			ref="form"
			label-position="top"
			:model="form"
			:rules="rules"
			class="form"
			label-width="100px"
		>
			<el-form-item prop="orgName" label="公司名称">
				<el-input v-model="form.orgName" placeholder="请输入公司名称"></el-input>
			</el-form-item>
			<el-form-item prop="content" label="反馈内容">
				<el-input
					v-model="form.content"
					type="textarea"
					:autosize="{ minRows: 6, maxRows: 12 }"
					placeholder="请输入反馈内容"
				></el-input>
			</el-form-item>
			<el-form-item prop="concat" label="联系方式">
				<el-input v-model="form.concat" placeholder="请输入联系方式"></el-input>
			</el-form-item>
			<el-form-item>
				<div class="submit-content">
					<el-button type="primary" :loading="loading" @click="submitForm">提交</el-button>
				</div>
			</el-form-item>
		</el-form>
	</div>
</template>
<script>
import { submitSuggestions } from '@/api/modules/coos';

export default {
	name: 'CoosSuggestions',
	data() {
		return {
			loading: false,
			form: {
				orgName: '',
				content: '',
				concat: ''
			},
			rules: {
				orgName: [{ required: true, message: '请输入公司名称', trigger: ['blur', 'change'] }],
				content: [{ required: true, message: '请输入反馈内容', trigger: ['blur', 'change'] }],
				concat: [{ required: true, message: '请输入联系方式', trigger: ['blur', 'change'] }]
			}
		};
	},
	methods: {
		/**表单提交*/
		submitForm() {
			this.$refs.form.validate(valid => {
				if (valid) {
					this.loading = true;
					submitSuggestions(this.form).then(res => {
						this.loading = false;
						if (res.code === 200) {
							this.$refs.form.resetFields();
							this.$message.success(`${res.result}已为您提交！`);
						} else {
							this.$message.error(res.message);
						}
					});
				}
			});
		}
	}
};
</script>
<style scoped lang="scss">
.page {
	width: 100%;
	height: 100%;
	background: #ffffff;
	padding: 80px 120px;
	overflow: auto;
	&-title {
		font-size: 18px;
		text-align: center;
		font-weight: 600;
	}
	&-desc {
		font-size: 14px;
		margin: 24px 0;
	}
	.submit-content {
		width: 100%;
		display: flex;
		justify-content: center;
	}
}
</style>
