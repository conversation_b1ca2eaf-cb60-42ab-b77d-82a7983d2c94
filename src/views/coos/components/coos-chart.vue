<template>
	<div ref="bar" class="bar" :style="{ height: height, width: width }"></div>
</template>

<script>
import * as echarts from 'echarts';
export default {
	name: 'Bar',
	props: {
		option: {
			type: Object,
			default: () => {
				return {};
			}
		},
		height: {
			type: String,
			default: '300px'
		},
		width: {
			type: String,
			default: '100%'
		}
	},
	data() {
		return {
			myChart: null
		};
	},
	mounted() {
		this.getEcharts();
	},
	methods: {
		getImage() {
			return this.myChart
				? this.myChart.getDataURL({
						type: 'png',
						pixelRatio: 2,
						height: 300,
						width: 460,
						backgroundColor: '#fff'
				  })
				: '';
		},
		saveImage() {
			if (this.myChart) {
				const src = this.myChart.getDataURL({
					pixelRatio: 2,
					backgroundColor: '#fff'
				});
				const a = document.createElement('a');
				a.href = src;
				a.download = 'chart-img';
				a.click();
			}
		},
		getEcharts() {
			// 竖向换行问题
			let option = { ...this.option };
			try {
				if (option.xAxis?.axisLabel?.margin === 30) {
					option.xAxis.axisLabel.formatter = function (value) {
						return value.split('').join('\n'); // 每字一行
					};
				}
			} catch (e) {
				console.log(e);
			}
			let chartDom = this.$refs.bar;
			this.myChart = echarts.init(chartDom);
			this.myChart.clear();
			this.option && this.myChart.setOption(option);
		}
	}
};
</script>
