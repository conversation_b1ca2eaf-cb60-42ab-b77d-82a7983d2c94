<template>
	<el-menu
		v-if="defaultActive"
		background-color="#ffffff"
		:default-active="defaultActive"
		@select="selectChange"
	>
		<el-menu-item v-for="(item, index) of tagOptions" :key="index" :index="item.value">
			<i class="coos-iconfont svg-icon" :class="item.icon"></i>
			<span slot="title">{{ item.label }}</span>
		</el-menu-item>
		<el-menu-item v-if="extend && extend.menus && extend.menus.includes('mySpace')" index="999">
			<i class="coos-iconfont icon-wenjianjia svg-icon"></i>
			<span slot="title">个人知识库</span>
		</el-menu-item>
		<el-menu-item
			v-if="extend && extend.menus && extend.menus.includes('fileSearch')"
			index="99999"
		>
			<i class="coos-iconfont icon-search svg-icon"></i>
			<span slot="title">文档搜索</span>
		</el-menu-item>
		<el-menu-item v-if="extend && extend.menus && extend.menus.includes('myFavorite')" index="4">
			<i class="coos-iconfont icon-shoucang1 svg-icon"></i>
			<span slot="title">收藏</span>
		</el-menu-item>
		<el-menu-item v-if="extend && extend.menus && extend.menus.includes('recycleBin')" index="5">
			<i class="coos-iconfont icon-shanchu svg-icon"></i>
			<span slot="title">回收站</span>
		</el-menu-item>
	</el-menu>
</template>
<script>
import { CoosEventTypes } from '@/utils/bus';
import { getDictionary } from '@/utils/data-dictionary';
import { geMatrixtDicts } from '@/api/modules/common';

export default {
	props: {
		extend: {
			type: Object,
			default: () => {}
		}
	},
	data() {
		return {
			tagOptions: [],
			defaultActive: '' // 知识库默认选中
		};
	},
	mounted() {
		this.getTagOptions();
		this._BUS.$emit(CoosEventTypes.changeTempAiEnter, [getDictionary('AI编码/智答私库')]);
	},
	destroyed() {
		this._BUS.$emit(CoosEventTypes.changeTempAiEnter, []);
	},
	methods: {
		/**获取标签下拉选择数据*/
		getTagOptions() {
			geMatrixtDicts('disk_space_category').then(res => {
				if (res.code === 200) {
					let icons = ['icon-yunxiazai', 'icon-xinwen', 'icon-linggan'];
					this.tagOptions = res.result.disk_space_category.map((item, index) => {
						return { ...item, icon: icons[index % 3] };
					});
					this.defaultActive = this.tagOptions[0].value;
					this.selectChange();
				} else {
					this.$message.error(res.message);
				}
			});
		},
		/**选择知识库类型*/
		selectChange(index) {
			if (index) {
				this.defaultActive = index;
			}
			if (index === '99999') {
				// 文档搜索
				this.$emit('searchDialog');
				return;
			}
			let isKnowladge = !['4', '5'].includes(this.defaultActive);
			if (index === '999') {
				// 我的空间
				this.$emit('selectChange', '2', '2');
				return;
			}
			this.$emit(
				'selectChange',
				isKnowladge ? '3' : this.defaultActive,
				isKnowladge ? this.defaultActive : ''
			);
		}
	}
};
</script>
<style scoped lang="scss">
.el-menu-item:hover .svg-icon {
	color: var(--brand-6) !important;
}
.el-menu-item {
	padding-left: 32px !important;
}
</style>
