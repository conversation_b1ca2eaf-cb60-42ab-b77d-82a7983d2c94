<template>
	<el-dialog
		title=""
		:append-to-body="true"
		:modal-append-to-body="true"
		:visible.sync="show"
		class="desk-el-dialog"
		top="0"
		width="300px"
	>
		<i class="coos-iconfont icon-shouqiquanping close-icon" @click="show = false"></i>
		<webviewBox v-bind="$attrs"></webviewBox>
	</el-dialog>
</template>

<script>
import webviewBox from '@/components/webview-box/index.vue';
export default {
	name: 'IframePopup',
	components: {
		webviewBox
	},
	data() {
		return {
			show: false
		};
	},
	methods: {
		open() {
			this.show = true;
		},
		close() {
			this.show = false;
		}
	}
};
</script>

<style scoped lang="scss">
.desk-el-dialog {
	display: flex;
	align-items: center;
	justify-content: center;
	::v-deep .el-dialog {
		height: 90%;
		border-radius: 16px;
		min-width: 530px;
		margin: 0;
		.el-dialog__header {
			display: none;
		}
		.el-dialog__body {
			padding: 18px 30px;
			border-radius: 16px;
			height: 100%;
		}
		.el-dialog__headerbtn {
			top: 31px;
			right: 30px;
		}
	}
}
.close-icon {
	font-size: 20px;
	color: $subTextColor;
	cursor: pointer;
	position: absolute;
	right: 20px;
	height: 20px;
	z-index: 999;
}
</style>
