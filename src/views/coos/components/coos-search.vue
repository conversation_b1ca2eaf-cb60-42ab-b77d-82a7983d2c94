<template>
	<div>
		<el-dialog
			:visible="dialogVisible"
			top="44px"
			width="80%"
			:close-on-click-modal="false"
			class="search-dialog"
			:modal-append-to-body="false"
			:before-close="beforeclose"
		>
			<div class="search-dialog-navbar">
				<i class="coos-iconfont icon-search search-dialog-navbar-icon" />
				<el-input
					ref="global-search"
					v-model="keywords"
					class="search-dialog-navbar-input"
					placeholder="请输入关键字搜索"
					@input="searchResult()"
				/>
				<i
					class="coos-iconfont icon-guanbi1 search-dialog-navbar-icon"
					style="color: #b9bdc9"
					@click="clearSearch()"
				/>
				<div class="search-handle" @click="toggleExpand">
					{{ isExpanded ? '收起' : '展开' }}
					<i class="el-icon-arrow-down" :class="{ 'is-rotate': isExpanded }"></i>
				</div>
			</div>
			<transition name="height-fade">
				<div v-if="isExpanded" class="search-box">
					<el-form
						:inline="true"
						label-width="120px"
						label-position="left"
						size="medium"
						:model="formInline"
						class="demo-form-inline optimized-form"
					>
						<el-form-item label="更新时间" class="optimized-item">
							<el-date-picker
								v-model="formInline.date"
								type="datetimerange"
								:picker-options="pickerOptions"
								range-separator="至"
								start-placeholder="开始日期"
								end-placeholder="结束日期"
								value-format="yyyy-MM-dd HH:mm:ss"
								align="right"
								style="width: 100%"
							></el-date-picker>
						</el-form-item>
						<el-form-item label="是否个人知识库" class="optimized-item">
							<el-radio-group v-model="formInline.spaceType">
								<el-radio :label="false">是</el-radio>
								<el-radio :label="true">否</el-radio>
							</el-radio-group>
						</el-form-item>
						<el-form-item label="所属知识库" class="optimized-item">
							<el-select
								v-model="formInline.spaceId"
								:disabled="!formInline.spaceType"
								clearable
								placeholder="请选择所属知识库"
								style="width: 100%"
							>
								<el-option
									v-for="item of tagOptions"
									:key="item.id"
									:label="item.spaceName"
									:value="item.id"
								></el-option>
							</el-select>
						</el-form-item>
						<el-form-item>
							<el-button type="primary" @click="onSubmit(1)">查询</el-button>
							<el-button @click="onSubmit(0)">重置</el-button>
						</el-form-item>
					</el-form>
				</div>
			</transition>
			<div class="search-dialog-body">
				<div v-loading="loading">
					<div ref="container" class="list" @scroll="handleScroll">
						<!-- v-if="activeName == '文档'" -->
						<div v-if="comprehensiveList.length" class="searchList">
							<div
								v-for="(item, index) in comprehensiveList"
								:key="index"
								class="searchList-item"
								@click.stop="handleSearchFile(item)"
							>
								<!-- <svg-icon icon-class="search-doc" class="icon"></svg-icon> -->

								<el-image v-if="item.fileType.length > 10" :src="item.icon" class="icon"></el-image>
								<i
									v-if="item.fileType == 'icon-shop'"
									class="coos-iconfont icon-shop"
									style="font-size: 40px"
								></i>
								<svg-icon
									v-if="iconType.includes(item.fileType)"
									class="icon"
									:icon-class="item.fileType"
								></svg-icon>
								<div v-if="'#last' == item.fileType" class="img">
									{{ item.title.charAt(item.title.length - 1) }}
								</div>
								<div v-if="'#first' == item.fileType" class="img">{{ item.title.charAt(0) }}</div>
								<div class="docInfo">
									<div class="docName" @click.stop="handleSearch(item)">
										<span
											v-for="(event, eventI) in getHilightStrArray(item.name, keywords)"
											:key="'docName-' + eventI"
											class="title-class-text"
											:style="event == keywords ? 'color:#0f45ea' : ''"
										>
											{{ event }}
										</span>
									</div>
									<div class="docAbstract">
										<span
											v-for="(event, eventI) in getHilightStrArray(item.fileDescription, keywords)"
											:key="'docAbstract-' + eventI"
											class="summary-text"
											:style="event == keywords ? 'color:#0f45ea' : ''"
										>
											{{ event }}
										</span>
									</div>
									<div
										class="docBy"
										v-html="item.footer.join('&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp')"
									></div>
								</div>
							</div>
						</div>
						<div v-if="!comprehensiveList.length && !keywords" class="empty">
							<img
								style="width: 144px; height: 144px"
								:src="require(`@/assets/${rentThem}/home/<USER>"
							/>
							<div class="prompt">你好，欢迎来到智能搜索页</div>
						</div>
						<div v-if="!comprehensiveList.length && keywords" class="empty">
							<BasicEmpty :data="comprehensiveList" name="no-search" />
						</div>
					</div>
				</div>
			</div>
		</el-dialog>
	</div>
</template>
<script>
import { debounce, previewFile } from '@/utils';
import { categorySearch } from '@/api/modules/search';
import { documentList } from '@/api/modules/document-content';
import { serveUrl } from '@/config';
import { getKnowledge } from '@/api/modules/coos';
export default {
	props: {
		dialogVisible: {
			type: Boolean,
			default: () => false
		}
	},
	data() {
		return {
			loading: false,
			keywords: '',
			totalNum: '',
			activeName: '综合',
			defaultName: '综合',
			accessApplicationId: '',
			//标签列表
			tabsList: [],
			tagOptions: [],
			//综合数据
			comprehensiveList: [],
			//分类列表
			categoryList: [],
			//分类搜索参数
			categoryPageInfo: {
				keyword: '',
				pageNo: 1,
				pageSize: 10
			},
			pickerOptions: {
				shortcuts: [
					{
						text: '最近一周',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
							picker.$emit('pick', [start, end]);
						}
					},
					{
						text: '最近一个月',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
							picker.$emit('pick', [start, end]);
						}
					},
					{
						text: '最近三个月',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
							picker.$emit('pick', [start, end]);
						}
					}
				]
			},
			isExpanded: false, // 控制展开/收起状态
			formInline: {
				date: [],
				spaceId: '',
				spaceType: true
			},
			iconType: [
				'folder',
				'doc',
				'image',
				'pp',
				'excel',
				'pdf',
				'text',
				'word',
				'unknown',
				'zip',
				'video'
			]
		};
	},
	watch: {
		dialogVisible(newVal) {
			if (newVal) {
				this.$nextTick(() => {
					this.$refs['global-search'].focus();
				});
			}
		}
	},
	mounted() {
		this.getTagOptions();
		// this.getComprehensiveList();
	},
	methods: {
		//关闭弹窗
		beforeclose() {
			this.$emit('update:dialogVisible', false);
		},
		/**获取标签下拉选择数据*/
		getTagOptions() {
			getKnowledge().then(res => {
				if (res.code === 200) {
					this.tagOptions = res.result || [];
				} else {
					this.$message.error(res.message);
				}
			});
		},
		toggleExpand() {
			this.isExpanded = !this.isExpanded;
		},
		/** 清空搜索内容 */
		clearSearch() {
			this.keywords = '';
			if (this.activeName == '综合') {
				// this.getComprehensiveList();
				this.comprehensiveList = [];
			} else {
				// this.getAppointCategory(this.accessApplicationId);
				this.categoryList = [];
			}
		},

		getHilightStrArray(str, key) {
			if (!str) return [];
			let arr = str.replace(new RegExp(`${key}`, 'g'), `%%${key}%%`).split('%%');
			arr = arr.filter(element => element !== '');
			return arr;
		},
		parseQueryString(url) {
			// 获取查询字符串部分
			const queryString = url.split('?')[1];
			// 将查询字符串分割成键值对
			const params = queryString.split('&');
			// 构建对象
			const result = {};
			params.forEach(param => {
				const [key, value] = param.split('=');
				result[key] = decodeURIComponent(value);
			});
			return result;
		},
		handleSearch(item) {
			let { fileUrlPath } = item;
			// 处理是否全路径
			let url = /http/.test(fileUrlPath)
				? fileUrlPath
				: (serveUrl || window.location.origin) + fileUrlPath;
			previewFile(url);
		},
		handleSearchFile(item) {
			let {
				id,
				folder = false,
				spaceType,
				parentId,
				spaceId,
				fileUrlPath,
				orgSize,
				canEdit
			} = item;
			this.$emit('handleSearchFile', {
				id,
				folder,
				parentId,
				spaceType,
				spaceId,
				fileUrlPath,
				orgSize,
				canEdit
			});
		},
		onSubmit(type) {
			if (type) {
				let params = {};
				const dateArray = this.formInline.date;
				if (Array.isArray(dateArray) && dateArray.length >= 1) {
					params.createTimeStart = dateArray[0];
					params.createTimeEnd = dateArray.length >= 2 ? dateArray[1] : dateArray[0];
				}
				params.spaceId = this.formInline.spaceId;
				params.spaceType = this.formInline.spaceType;
				this.getComprehensiveList(params);
			} else {
				this.formInline = {
					date: [],
					spaceId: '',
					spaceType: true
				};
			}
		},
		// --- a/coos-search.vue
		// +++ b/coos-search.vue
		// @@ -353,10 +353,16 @@ export default {
		//   this.comprehensiveList = arr.map(item => {
		//     let footer = [
		//       `文档所有者：${item.createByName}`,
		//       `更新时间：${item.updateTime || item.createTime}`
		//     ];
		//     if (item.label) {
		//       -              let label=item.label
		//         +              let label = item.label;
		//       +              // 判断label中是否包含this.keywords字符串 包含就用span标签包裹住
		//         +              if (this.keywords && label.includes(this.keywords)) {
		//         +                const escapedKeywords = this.keywords.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
		//         +                const regex = new RegExp(`(${escapedKeywords})`, 'g');
		//         +                label = label.replace(regex, '<span style="color:#0f45ea">$1</span>');
		//         +              }
		//       footer = [
		//         -								`文档标签：${item.label}`,
		//         +								`文档标签：${label}`,
		//         `文档所有者：${item.createByName}`,
		//         `更新时间：${item.updateTime || item.createTime}`
		//       ];

		/** 综合搜索 */
		getComprehensiveList(params) {
			this.loading = true;
			if (this.keywords) {
				documentList({
					name: this.keywords,
					delFlag: false,
					order: 'globalSearch',
					...params
				}).then(res => {
					let arr = res.result.records || [];
					this.comprehensiveList = arr.map(item => {
						let footer = [
							`文档所有者：${item.createByName}`,
							`更新时间：${item.updateTime || item.createTime}`
						];
						if (item.label) {
							let label = item.label;
							// 判断label中是否包含this.keywords字符串 包含就用span标签包裹住
							if (this.keywords && label.includes(this.keywords)) {
								const escapedKeywords = this.keywords.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
								const regex = new RegExp(`(${escapedKeywords})`, 'g');
								label = label.replace(regex, '<span style="color:#0f45ea">$1</span>');
							}
							footer = [
								`文档标签：${label}`,
								`文档所有者：${item.createByName}`,
								`更新时间：${item.updateTime || item.createTime}`
							];
						}
						return {
							...item,
							footer: footer
						};
					});
					this.loading = false;
				});
			} else {
				this.comprehensiveList = [];
				this.loading = false;
			}
		},
		/**指定分类搜索 */
		getAppointCategory(applicationId) {
			this.loading = true;
			this.categoryPageInfo.keyword = this.keywords;
			categorySearch(applicationId, { ...this.categoryPageInfo }).then(res => {
				this.categoryList = this.categoryList.concat(res.result.records);
				this.totalNum = res.result.total;
				this.loading = false;
			});
		},
		/** 搜索事件 */
		// searchResult() {
		// 	if (this.keywords) {
		// 		if (this.activeName == '综合') {
		// 			this.getComprehensiveList();
		// 		} else {
		// 			this.getAppointCategory(this.accessApplicationId);
		// 		}
		// 	} else {
		// 		this.comprehensiveList = [];
		// 		this.categoryList = [];
		// 	}
		// },
		searchResult: debounce(
			function () {
				if (this.isExpanded) return;
				this.categoryList = [];
				this.categoryPageInfo.pageNo = 1;
				if (this.keywords) {
					this.getComprehensiveList();
				} else {
					this.comprehensiveList = [];
				}
			},
			500,
			false
		),

		/** 滚动条触底加载更多 */
		// handleScroll() {
		// 	const container = this.$refs.container;
		// 	if (
		// 		container.clientHeight + container.scrollTop + 2 >= container.scrollHeight &&
		// 		this.activeName != '综合' &&
		// 		this.categoryList.length < this.totalNum
		// 	) {
		// 		this.categoryPageInfo.pageSize += 10;
		// 		this.getAppointCategory(this.accessApplicationId);
		// 	}
		// }
		/** 滚动条触底加载更多 */
		handleScroll: debounce(
			function () {
				const container = this.$refs.container;
				if (
					container.clientHeight + container.scrollTop + 2 >= container.scrollHeight &&
					this.activeName != '综合' &&
					this.categoryList.length < this.totalNum
				) {
					this.categoryPageInfo.pageNo += 1;
					this.getAppointCategory(this.accessApplicationId);
				}
			},
			500,
			false
		)
	}
};
</script>
<style lang="scss" scoped>
.search-dialog {
	width: 100%;
	z-index: 100000;
	::v-deep .el-dialog {
		border-radius: 16px;
		min-width: 530px;
		background: linear-gradient(180deg, #e5f3ff 0%, #ffffff 100%),
			linear-gradient(148deg, #afcaff 0%, rgba(175, 202, 255, 0) 100%),
			linear-gradient(203deg, #deffe7 0%, rgba(222, 255, 231, 0) 100%) rgba(255, 255, 255, 0.2);
		opacity: 1;
		.el-dialog__header {
			padding: 17px;
		}
		.el-dialog__body {
			padding: 37px 0px 15px 0px;
		}
	}
	&-navbar {
		display: flex;
		align-items: center;
		display: flex;
		align-items: center;
		height: 40px;
		background: #ffffff;
		border-radius: 32px;
		margin: 0px 30px;
		border: 1px solid #dce3e7;
		padding: 9px 16px;
		&-icon {
			flex-shrink: 0;
			width: 16px;
			height: 16px;
			color: var(--brand-6);
			cursor: pointer;
		}
		&-input {
			::v-deep .el-input__inner {
				height: 38px;
				font-size: 14px;
				font-weight: 400;
				// color: $holderTextColor;
				line-height: 22px;
				border: none;
				&:focus {
					box-shadow: none;
				}
			}
		}
	}
	&-body {
		margin-top: 20px;
		width: 100%;
		.tabs {
			width: 100%;
			line-height: 24px;
			color: $textColor;
			font-weight: 500;
			font-size: 16px;
			border-bottom: 1px solid #dce3e7;
			padding: 12px 37px;
			.tabs-item {
				cursor: pointer;
				margin-right: 37px;
				padding: 2px;
			}
			.clicked {
				padding-bottom: 13px;
				color: var(--brand-6);
				border-bottom: 2px solid var(--brand-6);
			}
		}
		.list {
			height: 452px;
			overflow-y: auto;
			@include scrollBar;
		}
	}
}
.searchList {
	margin-left: 7px;

	.searchList-item {
		padding: 12px 8px;
		border-bottom: 1px solid #f0f0f0;
		display: flex;
		cursor: pointer;
		.icon {
			width: 40px;
			height: 40px;
			flex-shrink: 0;
			border-radius: 6px;
		}
		.img {
			width: 40px;
			height: 40px;
			background: var(--brand-6);
			border-radius: 6px;
			//margin-right: 8px;
			color: #fff;
			text-align: center;
			line-height: 40px;
		}
		.docInfo {
			display: flex;
			justify-content: space-between;
			flex-direction: column;
			font-size: 12px;
			font-weight: 400;
			margin-left: 8px;
			color: $subTextColor;
			overflow: hidden;
			line-height: 20px;
			.docAbstract {
				@include aLineEllipse;
			}
			.docName {
				font-weight: 500;
				font-size: 14px;
				line-height: 22px;
				color: $primaryTextColor;
				@include aLineEllipse;
			}
			.docBy {
				// letter-spacing: 3px;
				@include aLineEllipse;
				span {
					color: $primaryTextColor;
				}
			}
		}
	}
}
.empty {
	margin-top: 61px;
	display: flex;
	flex-direction: column;
	align-items: center;
	.prompt {
		margin-top: 13px;
		font-weight: 600;
		color: $primaryTextColor;
		line-height: 28px;
		font-size: 20px;
	}
}
.height-fade-enter-active,
.height-fade-leave-active {
	will-change: height, opacity;
	transition: height 0.3s ease, opacity 0.3s ease;
	overflow: hidden;
}

.height-fade-enter,
.height-fade-leave-to {
	height: 0;
	opacity: 0;
}

.height-fade-enter-to,
.height-fade-leave {
	height: auto;
	opacity: 1;
}

.search-handle {
	width: 50px;
	margin-left: 15px;
	cursor: pointer;
	color: var(--brand-6);
	display: flex;
	align-items: center;

	.el-icon-arrow-down {
		transition: transform 0.3s;
		&.is-rotate {
			transform: rotate(180deg);
		}
	}
}

.search-box {
	margin: 0px 50px;
	padding: 9px 16px;
	background: rgb(255 255 255 / 70%);
}

.optimized-form {
	display: flex;
	flex-wrap: wrap;
	width: 100%;
}

.optimized-item {
	width: 48%;
	::v-deep .el-form-item__content {
		width: calc(100% - 200px);
	}
}
::v-deep .el-input__suffix {
	height: 32px;
}
</style>
