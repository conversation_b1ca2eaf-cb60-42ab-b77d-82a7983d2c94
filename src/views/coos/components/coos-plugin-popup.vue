<template>
	<el-dialog
		:visible.sync="show"
		:append-to-body="true"
		:modal-append-to-body="true"
		class="desk-el-dialog"
		top="0"
	>
		<div class="content">
			<div class="title">
				<div>功能管理</div>
				<i class="coos-iconfont icon-guanbi1 close-icon" @click="show = false"></i>
			</div>
			<div class="search">
				<i class="coos-iconfont icon-search search-icon"></i>
				<el-input
					v-model="keywords"
					class="search-input"
					placeholder="搜索查找功能"
					autocomplete="off"
					@change="search"
				></el-input>
			</div>
			<div class="tabs">
				<el-tabs v-model="activeName">
					<el-tab-pane label="全部" name="all"></el-tab-pane>
					<el-tab-pane
						v-for="(item, index) of pluginNames"
						:key="index"
						:label="item"
						:name="item"
					></el-tab-pane>
				</el-tabs>
			</div>
			<div class="line"></div>
			<div class="list">
				<div class="list-content">
					<div
						v-for="(item, index) of showPlugin"
						:key="index"
						:style="{ marginRight: (index + 1) % 3 === 0 ? 0 : '16px' }"
						class="list-item"
						:class="{ select: currentPlugin && item.name === currentPlugin.name }"
						@click="selectPlugin(item)"
					>
						<div class="list-item-top">
							<img v-if="item.logoUrl" class="list-item-top-img" :src="item.logoUrl" />
							<svg-icon v-else icon-class="default-icon" class="list-item-top-img"></svg-icon>
							<div class="list-item-top-text">{{ item.name }}</div>
							<div class="plugin-logo" :class="'plugin-logo-' + item.type">
								{{ item.type === 1 ? '智能填表' : item.type === 2 ? '智能查询' : '功能直连' }}
							</div>
						</div>
						<div class="list-item-desc">{{ item.digest }}</div>
					</div>
				</div>
			</div>
		</div>
	</el-dialog>
</template>

<script>
import { allPlugins } from '@/api/modules/coos';

export default {
	name: 'PluginPopup',
	props: {
		initPlugin: {
			type: Object,
			default: () => {
				return null;
			}
		}
	},
	data() {
		return {
			activeName: 'all', //当前选中的功能类型
			show: false,
			keywords: '',
			currentPlugin: null, // 当前功能
			pluginNames: [], // 功能类型
			pluginsList: [], // 功能原始数据
			allPlugin: [] // 全部的功能
		};
	},
	computed: {
		// 展示的功能列表
		showPlugin() {
			let arr = this.activeName === 'all' ? this.allPlugin : this.pluginsList[this.activeName];
			if (this.keywords) {
				arr = arr.filter(item => {
					return item.name.indexOf(this.keywords) > -1;
				});
			}
			return arr;
		}
	},
	watch: {
		initPlugin(newVal) {
			this.currentPlugin = newVal;
		}
	},
	mounted() {
		this.getData();
	},
	methods: {
		/**选择功能*/
		selectPlugin(item) {
			this.$emit('selectPlugin', item);
			this.show = false;
		},
		/**获取全部功能*/
		getData() {
			allPlugins({ keyWord: this.keyword, clientType: '1' }).then(res => {
				this.loading = false;
				this.pluginsList = res.result;
				this.pluginNames = Object.keys(this.pluginsList);
				Object.keys(this.pluginsList).forEach(key => {
					this.allPlugin = this.allPlugin.concat(this.pluginsList[key]);
				});
				this.chooseList = res.result;
			});
		},
		open() {
			this.show = true;
		},
		search() {}
	}
};
</script>

<style scoped lang="scss">
.desk-el-dialog {
	display: flex;
	align-items: center;
	justify-content: center;

	::v-deep .el-dialog {
		border-radius: 16px;
		min-width: 530px;
		margin: 0;

		.el-dialog__header {
			display: none;
		}

		.el-dialog__body {
			padding: 18px 30px;
			border-radius: 16px;
			background: linear-gradient(180deg, #e5f3ff 0%, #ffffff 30%, #ffffff 100%),
				linear-gradient(148deg, #afcaff 0%, rgba(175, 202, 255, 0) 100%),
				linear-gradient(203deg, #deffe7 0%, rgba(222, 255, 231, 0) 100%), rgba(255, 255, 255, 0.2);
		}

		.el-dialog__headerbtn {
			top: 31px;
			right: 30px;
		}
	}
}

.content {
	max-height: 80vh;
	min-height: 560px;
	display: flex;
	flex-direction: column;

	.title {
		@include flexBox(space-between);
		font-weight: 800;
		font-size: 18px;
		color: #303133;
		line-height: 24px;
		text-align: left;
		font-style: normal;
		text-transform: none;

		.close-icon {
			font-size: 16px;
			color: #606266;
			cursor: pointer;
		}
	}

	.search {
		margin: 15px 0 14px;
		height: 40px;
		display: flex;
		align-items: center;
		padding: 7px 12px;
		background: #ffffff;
		border-radius: 32px;
		width: 100%;

		&-icon {
			flex-shrink: 0;
			font-size: 16px;
			cursor: pointer;
			color: var(--brand-6);
			margin-right: 12px;
		}

		&-input {
			flex: 1;
			height: 100%;
			width: 100%;
			border-radius: 6px;

			::v-deep .el-input__inner {
				height: 100%;
				width: 100%;
				border-radius: 6px;
				border: none;
				outline: none;
				padding: 0 0 0 4px;

				&:focus {
					box-shadow: none;
				}

				&::placeholder {
					font-family: PingFang SC, PingFang SC;
					font-weight: 400;
					font-size: 14px;
					color: $holderTextColor;
					line-height: 22px;
					text-align: left;
					font-style: normal;
					text-transform: none;
				}
			}
		}
	}

	.tabs {
		padding-left: 47px;
	}

	.line {
		height: 1px;
		background: #dce3e7;
		margin: 0 -30px;
	}

	.list {
		flex: 1;
		overflow-y: auto;
		&-content {
			display: flex;
			flex-wrap: wrap;
		}
		@include noScrollBar;

		&-item {
			width: calc((100% - 32px) / 3);
			height: 96px;
			background: #f6f9fd;
			border-radius: 9px;
			margin-top: 16px;
			padding: 10px 12px;
			cursor: pointer;
			position: relative;

			&-top {
				@include flexBox(flex-start);

				&-img {
					height: 32px;
					width: 32px;
					margin-right: 6px;
					border-radius: 6px;
				}

				&-text {
					font-weight: 500;
					font-size: 14px;
					color: $primaryTextColor;
					line-height: 24px;
					text-align: left;
					font-style: normal;
					text-transform: none;
					@include countEllipse(2);
				}
			}

			&-desc {
				margin-top: 8px;
				font-weight: 400;
				font-size: 12px;
				color: $subTextColor;
				line-height: 20px;
				text-align: left;
				font-style: normal;
				text-transform: none;
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
			}
		}

		.select {
			border: 1px solid var(--brand-6);
			padding: 9px 11px;
		}
	}
}

.plugin-logo {
	border-radius: 3px;
	padding: 4px 5px;
	font-weight: 400;
	font-size: 12px;
	line-height: 12px;
	position: absolute;
	top: 0;
	right: 0;
}
.plugin-logo-1 {
	background: rgba(0, 168, 112, 0.1);
	color: #00a870;
}
.plugin-logo-2 {
	background: rgba(116, 88, 228, 0.1);
	color: #7458e4;
}
.plugin-logo-3 {
	background: rgba(48, 136, 255, 0.1);
	color: #3088ff;
}

::v-deep .el-tabs__nav-wrap::after {
	display: none;
}

::v-deep .el-tabs__header {
	margin-bottom: 0 !important;
}
</style>

<style lang="scss" scoped>
::v-deep .el-dialog {
	width: 960px;
}
</style>
