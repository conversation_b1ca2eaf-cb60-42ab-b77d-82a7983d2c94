<template>
	<webviewBox
		ref="webviewBox"
		class="webviewBox"
		:style="{ width: viewWidth }"
		:need-socket="false"
		:view-url="url"
		@load="loaded"
	></webviewBox>
</template>
<script>
import webviewBox from '@/components/webview-box/index.vue';
export default {
	name: 'CoosH5',
	components: {
		webviewBox
	},
	props: {
		webviewUrl: {
			type: String,
			default: () => {
				return '';
			}
		}
	},
	data() {
		return {
			viewWidth: '1200px'
		};
	},
	computed: {
		url() {
			return /webview=/.test(this.webviewUrl)
				? decodeURIComponent(this.webviewUrl.split('webview=')[1])
				: this.webviewUrl;
		}
	},
	methods: {
		loaded() {
			this.viewWidth = '100%';
		},
		reset() {
			this.$refs.webviewBox.resetSession();
		}
	}
};
</script>
<style scoped lang="scss">
.webviewBox {
	height: 100%;
	overflow: hidden;
}
</style>
