<template>
	<div class="history">
		<div class="history-title">
			<div class="history-title-text">
				历史消息
				<span class="count">{{ `(${listTotal})条` }}</span>
			</div>
			<div class="history-title-btn" @click="closeHistory">关闭</div>
		</div>
		<div class="history-search">
			<svg-icon icon-class="search" class-name="history-search-icon"></svg-icon>
			<el-input
				v-model="chatListParam.title"
				class="history-search-input"
				placeholder="搜索历史对话"
				color="@d1d3db"
				autocomplete="off"
				@input="searchRecord"
			></el-input>
		</div>
		<el-checkbox-group
			v-model="checkChat"
			v-infinite-scroll="scrollBottom"
			v-loading="delLoading"
			class="history-content"
			:infinite-scroll-disabled="disabled"
		>
			<div
				v-for="(item, index) of historyList"
				:key="item.id"
				:class="{ select: currentHistory === item.id || editInput === item.id }"
				class="history-content-item"
				@click="editInput !== item.id && !handleBatch && selectHistory(item.id, item.type)"
			>
				<el-checkbox v-if="handleBatch" :label="item.id" class="block">
					<i class="coos-iconfont icon-chat icon"></i>
					<span class="text">{{ item.title }}</span>
				</el-checkbox>
				<div v-else class="block">
					<i v-show="editInput !== item.id" class="coos-iconfont icon-chat icon"></i>
					<el-input
						v-if="editInput === item.id"
						v-model="updateValue"
						v-loading="editLoading"
						@click.stop.native
						@keydown.enter.native="updateTittle(item.id, index)"
					></el-input>
					<span v-else class="text">{{ item.title }}</span>
					<div v-if="editInput === item.id" class="handle show-handle">
						<i
							class="coos-iconfont icon-selected handle-icon"
							@click.stop="updateTittle(item.id, index)"
						></i>
						<i class="coos-iconfont icon-close_circle handle-icon" @click.stop="editInput = ''"></i>
					</div>
					<div v-else class="handle">
						<i class="coos-iconfont icon-edit handle-icon" @click.stop="edit(item)"></i>
						<i class="coos-iconfont icon-trash handle-icon" @click.stop="delChatList(item.id)"></i>
					</div>
				</div>
			</div>
			<!--				<div v-show="delLoading" class="loading-text">加载中...</div>-->
			<div v-show="!delLoading && historyList.length === 0" class="empty">
				<img class="empty-image" :src="emptyImg" alt="" />
			</div>
		</el-checkbox-group>
		<div class="history-utils">
			<div v-if="!handleBatch" class="button" @click="handleBatch = true">
				<i class="coos-iconfont icon-trash icon"></i>
				批量删除
			</div>
			<div v-else class="more-handle">
				<el-checkbox v-model="allSelect" label="all">全选</el-checkbox>
				<div class="more-handle-button">
					<div class="delete" @click="deleteChatRecord">删除</div>
					<div class="cale" @click="handleBatch = false">取消</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import { delChatList, getChatList, updateTittle } from '@/api/modules/coos';
import { debounce } from '@/utils';
import { assetsUrl } from '@/config';

export default {
	name: 'CoosHistory',
	components: {},
	props: {
		sessionId: {
			type: String,
			default: () => {
				return '';
			}
		},
		historyKey: {
			type: String,
			default: () => {
				return '';
			}
		}
	},
	data() {
		return {
			emptyImg: assetsUrl + '/desk-app/empty.png',
			editLoading: false,
			updateValue: '', // 更新对话标题的信息
			editInput: '', // 编辑的数据id
			currentHistory: '', // 当前的历史索引
			historyList: [], // 对话数据
			delLoading: false, // 删除的异步状态
			checkChat: [], // 被选中的对话记录
			allSelect: false, // 全选
			// 右侧对话记录列表的分页参数
			chatListParam: {
				pageSize: 20,
				pageNo: 1,
				title: '' //搜索字段
			},
			listTotal: 0, //总条数
			handleBatch: false // 批量操作
		};
	},
	computed: {
		// 没有更多
		noMore() {
			return this.historyList.length >= this.listTotal;
		},
		// 是否可以继续出发滚动触底
		disabled() {
			return this.delLoading || this.noMore;
		}
	},
	watch: {
		// 监听全选
		allSelect(newVal) {
			if (newVal) {
				this.checkChat = this.historyList.map(item => item.id);
			} else {
				this.checkChat = [];
			}
		}
	},
	methods: {
		/**重置历史记录*/
		reset() {
			this.currentHistory = '';
		},
		/**关闭编辑*/
		closeEdit() {
			this.editInput = '';
		},
		/**点击历史记录*/
		selectHistory(id, type) {
			if (this.sessionId === id) return;
			this.currentHistory = id;
			this.$emit('changeSessionId', id, type);
		},
		/**关闭历史记录*/
		closeHistory() {
			this.$emit('closeHistory', false);
		},
		/**打开历史记录*/
		openHistory() {
			this.chatListParam.pageNo = 1;
			this.chatListParam.title = '';
			this.historyList = [];
			this.getChatRecords();
		},
		/**获取对话记录*/
		getChatRecords() {
			this.delLoading = true;
			let params = { ...this.chatListParam, sessionTypes: this.historyKey };
			if (this.$route.query.viewAllHistory && this.$route.query.viewAllHistory === 'true') {
				params.viewAllHistory = true;
			}
			getChatList(params).then(res => {
				this.delLoading = false;
				if (res.code === 200) {
					this.historyList = this.historyList.concat(res.result.records);
					this.listTotal = res.result.total;
				} else {
					this.$message.error(res.message);
				}
			});
		},
		/**搜索右边的记录*/
		searchRecord: debounce(
			function () {
				this.historyList = [];
				this.chatListParam.pageNo = 1;
				this.getChatRecords();
			},
			500,
			false
		),
		/**右边对话记录滚动加载*/
		scrollBottom() {
			this.chatListParam.pageNo += 1;
			this.getChatRecords();
		},
		/**更新对话标题*/
		updateTittle(id, index) {
			this.editLoading = true;
			updateTittle(id, { title: this.updateValue }).then(res => {
				this.editLoading = false;
				if (res.code === 200) {
					this.editInput = '';
					this.historyList[index].title = this.updateValue;
					this.$message.success('更新成功！');
				} else {
					this.$message.error(res.message);
				}
			});
		},
		/**点击编辑*/
		edit(item) {
			this.editInput = item.id;
			this.updateValue = item.title;
		},
		/**删除右侧对话记录*/
		delChatList(ids) {
			this.delLoading = true;
			delChatList(ids).then(res => {
				if (res.code === 200) {
					this.openHistory(true);
					this.allSelect = false;
					if (ids.indexOf(this.currentHistory) > -1) {
						this.reset(false);
					}
					this.$message.success('操作成功');
				} else {
					this.$message.error(res.message);
				}
			});
		},
		/**批量删除对话记录*/
		deleteChatRecord() {
			this.delChatList(this.checkChat.join(','));
		}
	}
};
</script>

<style scoped lang="scss">
.history {
	border-left: 1px solid #dce3e7;
	width: 30%;
	min-width: 240px;
	max-width: 330px;
	background: #ffffff;
	height: 100%;
	@include flexBox(flex-start);
	flex-direction: column;

	&-title {
		width: 100%;
		flex-shrink: 0;
		padding: 0 12px 0 22px;
		height: 54px;
		@include flexBox(space-between);
		border-bottom: 1px solid #dce3e7;

		&-text {
			font-size: 16px;
			font-weight: 800;
			color: $primaryTextColor;
			line-height: 22px;

			.count {
				font-size: 14px;
				font-weight: 800;
				color: $textColor;
				line-height: 22px;
			}
		}

		&-btn {
			cursor: pointer;
			font-size: 14px;
			font-weight: 400;
			color: $subTextColor;
			line-height: 22px;
		}
	}

	&-search {
		flex-shrink: 0;
		height: 40px;
		width: calc(100% - 48px);
		margin: 16px 24px;
		padding: 9px 12px;
		@include flexBox(flex-start);
		background: #ffffff;
		border: 1px solid $borderColor;
		border-radius: 6px;

		&-icon {
			flex-shrink: 0;
			width: 16px;
			height: 16px;
			cursor: pointer;
		}

		&-input {
			flex: 1;
			height: 100%;
			width: 100%;
			border-radius: 6px;

			::v-deep .el-input__inner {
				height: 100%;
				width: 100%;
				background: #ffffff;
				border-radius: 6px;
				border: none;
				outline: none;
				padding: 0 0 0 4px;

				&:focus {
					box-shadow: none;
				}

				&::placeholder {
					font-size: 14px;
					font-weight: 400;
					color: $disabledTextColor;
					line-height: 22px;
				}
			}
		}
	}

	&-content {
		width: 100%;
		flex: 1;
		overflow: auto;
		padding: 0 9px;

		.loading-text {
			height: 40px;
			line-height: 40px;
			text-align: center;
			color: #c0c0c0;
		}

		.empty {
			height: 100%;
			@include flexBox();

			&-image {
				width: 246px;
				height: 142px;
			}
		}

		@include noScrollBar;

		&-item {
			cursor: pointer;
			padding: 0 72px 0 17px;
			height: 54px;
			border-radius: 6px;
			position: relative;
			@include flexBox(flex-start);

			.block {
				overflow: hidden;
				@include flexBox(flex-start);

				::v-deep .el-checkbox__label {
					overflow: hidden;
					@include flexBox(flex-start);
				}
			}

			&-check {
				width: 16px;
				height: 16px;
				border-radius: 2px;
				border: 1px solid #dcdfe6;
				margin-right: 9px;
				cursor: pointer;
			}

			::v-deep .el-input__inner {
				border: none;

				&:hover {
					border: none;
				}

				&:focus {
					border: none;
					box-shadow: none;
				}
			}

			&:hover {
				background: #f5f7fa;

				.handle {
					display: block;
				}
			}

			& .icon {
				flex-shrink: 0;
				font-size: 20px;
				margin-right: 8px;
				color: $primaryTextColor;
			}

			& .text {
				flex: 1;
				font-size: 14px;
				font-weight: 400;
				color: $textColor;
				line-height: 22px;
				@include aLineEllipse;
			}

			.handle {
				display: none;
				position: absolute;
				right: 16px;

				&-icon {
					font-size: 18px;
					margin-left: 10px;
					color: $subTextColor;
				}
			}

			.show-handle {
				display: block;
			}
		}

		.select {
			background: #f5f7fa;
		}
	}

	&-utils {
		flex-shrink: 0;
		height: 54px;
		background: #ffffff;
		border-top: 1px solid #dce3e7;
		width: 100%;
		@include flexBox(flex-start);

		.button {
			margin-left: 16px;
			width: 100px;
			height: 32px;
			@include flexBox();
			font-size: 14px;
			font-weight: 400;
			color: $textColor;
			line-height: 22px;
			cursor: pointer;
			border-radius: 6px;
			background: #f7f7f7;

			& > .icon {
				font-size: 16px;
				margin-right: 6px;
				color: $textColor;
			}
		}

		.more-handle {
			width: 100%;
			padding: 0 12px 0 26px;
			@include flexBox(space-between);

			&-button {
				@include flexBox();
				font-size: 14px;
				font-weight: 400;
				line-height: 22px;

				& > .delete {
					background: #f7f7f7;
					border-radius: 6px;
					color: #ff4d4f;
					padding: 5px 12px;
					cursor: pointer;
					@include flexBox();
				}

				& > .cale {
					@include flexBox();
					margin-left: 11px;
					background: #f7f7f7;
					border-radius: 6px;
					color: $textColor;
					padding: 5px 12px;
					cursor: pointer;
				}
			}
		}
	}
}
</style>
