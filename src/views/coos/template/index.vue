<template>
	<div class="message">
		<div class="message-detail">
			<div class="message-detail-title">
				<div class="message-detail-title-item">
					<div>{{ currentTab.name }}</div>
					<div class="message-detail-title-org">@川大智胜</div>
				</div>
				<div class="message-detail-title-right">
					<svg-icon icon-class="user-add" class="message-detail-title-icon"></svg-icon>
					<svg-icon icon-class="black-ellipse" class="message-detail-title-icon"></svg-icon>
				</div>
			</div>
			<!--  消息模块  -->
			<div id="message-detail-con" class="message-detail-con">
				<div v-show="loading" class="load-more">
					<span
						v-for="(text, index) of '加载中...'"
						:key="index"
						:style="{ animationDelay: index * 0.5 + 's' }"
						class="load-more-text"
					>
						{{ text }}
					</span>
				</div>
				<!--  普通聊天消息展示  -->
				<PersonItem
					v-for="(item, index) of message"
					:key="index"
					:src-list="srcList"
					:item="item"
					:index="index"
					@contextmenu="contextmenu"
				></PersonItem>
				<!--  保持消息展示最新的块级元素  -->
				<div id="message-bottom"></div>
			</div>
		</div>
		<!--  拖拽调整输入框大小  -->
		<div class="message-resize" @mousedown="resize"></div>
		<!--  工具栏  -->
		<div class="message-utils">
			<div
				v-for="(item, index) of utils"
				:key="index"
				:style="{ marginRight: item.right + 'px', cursor: 'pointer' }"
			>
				<svg-icon
					v-if="item.type === 'svg'"
					:icon-class="item.class"
					:style="{ width: item.size + 'px', height: item.size + 'px' }"
				></svg-icon>
				<i
					v-else
					:class="item.class"
					class="coos-iconfont"
					:style="{ fontSize: item.size || 20 + 'px', color: '#515B6E' }"
				></i>
			</div>
			<div class="quick-button">
				<div class="block">
					<div class="code" @click="sendCode">
						<i class="coos-iconfont icon-a-daimakuai3x"></i>
					</div>
					<span class="btn" @click="sendCode">发送代码</span>
				</div>

				<div class="block">
					<div class="huiyi">
						<i class="coos-iconfont icon-caidanlan-bangong-kuaisulaidianjilu"></i>
					</div>
					<span class="btn">快速会议</span>
				</div>
			</div>
		</div>
		<!--  输入内容  -->
		<div class="message-send" :style="{ height: height + 'px' }">
			<el-input
				v-model="sendCon"
				class="message-send-input"
				resize="none"
				type="textarea"
				autosize
				placeholder="请输入消息"
				@keydown.native="preventEnter"
				@keyup.native="sendMessage"
			></el-input>
		</div>
		<!--  消息右键菜单  -->
		<div v-show="menuShow" class="menu" :style="{ top: menuTop + 'px', left: menuLeft + 'px' }">
			<div
				v-for="(item, index) of menuList"
				:key="index"
				class="menu-item"
				@click="handleContext(item.type)"
			>
				{{ item.name }}
			</div>
		</div>
	</div>
</template>

<script>
import PersonItem from '@/views/coos/template/coos-message.vue';
export default {
	name: 'ImContent',
	components: {
		PersonItem
	},
	props: {
		/**当前对话框对象*/
		currentTab: {
			type: Object,
			default: () => {
				return {};
			}
		}
	},
	data() {
		return {
			loading: false,
			params: {
				pageSize: 10,
				pageNo: 1
			},
			/**右键菜单选项*/
			menuLeft: 0,
			menuTop: 0,
			menuShow: false,
			menuIndex: 0, // 右键菜单所在消息索引
			menuList: [
				// { name: '复制', type: 'copy' },
				// { name: '转发', type: 'forward' },
				// { name: '引用', type: 'quote' },
				// { name: '收藏', type: 'collect' },
				// { name: '撤回', type: 'withdraw' },
				{ name: '删除', type: 'delete' }
			],
			initHeight: 100, // 聊天框初始高度
			height: 100, // 变化的高度
			sendCon: '', // 发送内容
			message: [],
			preMessage: [
				{
					time: '2024/01/05',
					list: [
						{
							id: '1',
							avatar: '',
							name: '黄',
							type: 'message',
							content: '阿巴巴啊巴巴阿巴阿巴阿巴,土豆哪里去挖，土豆山上去挖',
							isSelf: false
						},
						{
							id: '2',
							avatar: '',
							name: '王',
							type: 'message',
							content:
								'阿巴巴啊巴巴阿巴阿巴阿巴阿巴巴啊巴巴阿巴阿巴阿巴阿巴巴啊巴巴阿巴阿巴阿巴,一挖一麻袋一挖一麻袋，',
							isSelf: true
						},
						{
							id: '1',
							avatar: '',
							name: '黄',
							type: 'audio',
							content: '通话时长00:30',
							isSelf: false
						}
					]
				},
				{
					time: '14:18',
					list: [
						{
							id: '1',
							avatar: '',
							name: '黄',
							type: 'message',
							content: '你的头像怪好看嘞',
							isSelf: false
						},
						{
							id: '2',
							avatar: '',
							name: '王',
							type: 'message',
							content: '我看看你的嘞',
							isSelf: true
						}
					]
				}
			],
			utils: [
				{
					name: 'biaoqing',
					class: 'icon-xiaolian-line',
					right: 12
				},
				{
					name: 'jietu',
					class: 'icon-jianqie',
					right: 2
				},
				{
					name: 'down1',
					class: 'down',
					size: '10',
					type: 'svg',
					right: 11
				},
				{
					name: 'image',
					class: 'icon-tupian2',
					right: 10
				},
				{
					name: 'doc',
					class: 'icon-wendang1',
					right: 14
				},
				{
					name: 'date',
					class: 'icon-calendar-copy-copy',
					right: 13
				},
				{
					name: 'dir',
					class: 'icon-dakai',
					right: 2
				},
				{
					name: 'down',
					class: 'down',
					size: '10',
					type: 'svg',
					right: 11
				},
				{
					name: 'message',
					class: 'icon-tubiaozhizuomoban'
				}
			],
			el: null,
			lock: false //还有没有更多消息，请求锁
		};
	},
	computed: {
		contextMenu() {
			return [];
		},
		// 普通消息图片预览，获取全部图片
		srcList() {
			let arr = [];
			this.message.forEach(item => {
				item.list.forEach(detail => {
					if (detail.type === 'image') {
						arr.push(detail.url);
					}
				});
			});
			return arr;
		}
	},
	mounted() {
		window.addEventListener('click', this.caleMenu);
		this.el = document.getElementById('message-bottom');
		this.getPersonMessage();
	},
	beforeDestroy() {
		window.removeEventListener('click', this.caleMenu);
	},
	methods: {
		/**普通的聊天记录*/
		getPersonMessage() {
			this.message = this.preMessage;
			this.toBottom();
		},
		/**获取更多聊天记录*/
		getMore() {
			this.params.pageNo += 1;
			console.log('请求');
		},
		/**操作右键*/
		handleContext(type) {
			switch (type) {
				case 'delete': {
					console.log('删除');
					break;
				}
				default:
					this.$message.warning('开发中，敬请期待！');
			}
		},
		/**关闭右键菜单*/
		caleMenu() {
			this.menuShow = false;
		},
		/**右键菜单*/
		contextmenu(event, index) {
			this.menuIndex = index;
			// 根据需要进行位置调整等操作
			this.menuLeft = event.clientX;
			this.menuTop = event.clientY;
			this.menuShow = true;
		},
		/**阻止回车默认行为*/
		preventEnter(e) {
			if (!e.shiftKey && e.key === 'Enter') {
				e.preventDefault();
			}
		},
		/**发送普通消息*/
		sendMessage(e) {
			// 是否按住shift键
			if (e.shiftKey) {
				console.log('按住了shift键');
			} else {
				switch (e.key) {
					case 'Enter': {
						if (this.sendCon.replace(/(\n|\s)/gi, '')) {
							let obj = {
								id: '2',
								avatar: '',
								name: '王',
								type: 'message',
								isSelf: true,
								content: this.sendCon
							};
							this.message.push({
								time: '刚刚',
								list: [obj]
							});
							this.sendCon = '';
							this.toBottom();
						} else {
							this.sendCon = '';
							this.$message.error('发送消息不能为空！');
						}
						break;
					}
				}
			}
		},
		/**滚动到最底部*/
		toBottom() {
			this.$nextTick(() => {
				this.el.scrollIntoView();
			});
		},
		/**发送代码*/
		sendCode() {
			if (this.sendCon.replace(/(\n|\s)/gi, '')) {
				let obj = {
					id: '2',
					avatar: '',
					name: '王',
					type: 'code',
					language: 'javascript',
					isSelf: true,
					content: this.sendCon
				};
				this.message.push({
					time: '刚刚',
					list: [obj]
				});
				this.sendCon = '';
				this.toBottom();
			} else {
				this.$message.error('发送消息不能为空！');
				this.sendCon = '';
			}
		},
		resize(e) {
			this.initHeight = this.height;
			this.startY = e.clientY;
			document.addEventListener('mousemove', this.handleMouseMove);
			document.addEventListener('mouseup', this.stopSelection);
		},
		handleMouseMove(e) {
			this.moveHeight = this.startY - e.clientY;
			this.height = this.initHeight + this.moveHeight;
		},
		stopSelection() {
			document.removeEventListener('mousemove', this.handleMouseMove);
			document.removeEventListener('mouseup', this.stopSelection);
		}
	}
};
</script>

<style scoped lang="scss">
.message {
	flex: 1;
	background: #f3f5f6;
	height: 100%;
	display: flex;
	flex-direction: column;
	position: relative;
	overflow: hidden;
	&-detail {
		flex: 1;
		background: #f3f5f6;
		display: flex;
		flex-direction: column;
		position: relative;
		overflow: hidden;
		&-title {
			height: 54px;
			padding: 0 23px 0 18px;
			@include flexBox(space-between);
			font-size: 18px;
			font-weight: 800;
			color: $primaryTextColor;
			line-height: 24px;
			border-top: 1px solid #f0f0f0;
			border-bottom: 1px solid #dce3e7;
			&-item {
				@include flexBox();
			}
			&-org {
				font-size: 12px;
				font-weight: 400;
				color: var(--brand-6);
				line-height: 20px;
				margin-left: 5px;
			}
			&-right {
				@include flexBox();
			}
			&-icon {
				width: 24px;
				height: 24px;
				margin-left: 13px;
				cursor: pointer;
			}
		}
		&-con {
			padding: 0 20px;
			flex: 1;
			overflow-y: auto;
			@include flexBox(flex-start, flex-start);
			flex-direction: column;
			@include noScrollBar;
			.load-more {
				@include flexBox();
				font-size: 12px;
				width: 100%;
				height: 40px;
				color: #c3c3c3;
				flex-shrink: 0;
				&-text {
					animation: text-loading 1s infinite;
					margin: 0 1px;
				}
			}
		}
	}
	&-resize {
		height: 4px;
		cursor: ns-resize;
	}
	&-utils {
		height: 40px;
		border-top: 1px solid #dce3e7;
		padding: 0 14px;
		@include flexBox(flex-start);
		position: relative;
		.quick-button {
			position: absolute;
			right: 24px;
			cursor: pointer;
			@include flexBox();
			.block {
				@include flexBox();
				.code {
					width: 15px;
					height: 11px;
					color: #3088ff;
					border: 1px solid #2b2f36;
					font-size: 10px;
					line-height: 10px;
					@include flexBox();
				}
				.huiyi {
					margin-left: 21px;
					font-size: 10px;
				}
				.btn {
					font-size: 12px;
					font-weight: 400;
					color: $textColor;
					line-height: 20px;
					margin-left: 4px;
				}
			}
		}
	}
	&-send {
		&-input {
			height: 100%;
			::v-deep .el-textarea__inner {
				height: 100% !important;
				overflow-y: auto;
				background: #f3f5f6;
				border: none;
				@include noScrollBar;
				&:focus {
					border-color: transparent;
					box-shadow: none;
					@include scrollBar;
				}
				&:hover {
					border-color: transparent;
					box-shadow: none;
					@include scrollBar;
				}
			}
		}
	}
	.menu {
		position: fixed;
		background: #ffffff;
		border-radius: 3px;
		padding: 5px 0;
		width: 70px;
		text-align: center;
		box-shadow: 0px 3px 4px 0px rgba(0, 0, 0, 0.3);
		&-item {
			padding: 8px 0;
			font-size: 12px;
			cursor: default;
			&:hover {
				background: #f0f0f0;
			}
		}
	}
}
</style>
