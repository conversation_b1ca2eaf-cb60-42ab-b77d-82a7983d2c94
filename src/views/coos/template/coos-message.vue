<template>
	<div class="person-item">
		<div class="person-item-time" :style="{ marginTop: index === 0 ? '12px' : '28px' }">
			{{ item.time }}
		</div>
		<div v-for="(detail, i) of item.list" :key="index + '-' + i">
			<!--  撤回消息  -->
			<div v-if="detail.type === 'reBack'" class="reBack">
				{{ detail.content }}
			</div>
			<!--  消息展示  -->
			<div v-else class="person-item-message" :class="{ noSelf: !detail.isSelf }">
				<div
					class="detail"
					:class="{ codeDetail: detail.type === 'code' }"
					@contextmenu="contextmenu($event, index, i)"
				>
					<!--  图片预览  -->
					<el-image
						v-if="detail.type === 'image'"
						:src="detail.url"
						:preview-src-list="srcList"
					></el-image>
					<!--  代码块  -->
					<pre
						v-else-if="detail.type === 'code'"
						:key="index + '-' + i + '-' + detail.language"
						v-highlightjs="detail.content"
						class="detail-pre"
					><code class="detail-pre-code hljs" :class="'language-'+detail.language"></code></pre>
					<!--  语音以及文本消息  -->
					<div v-else class="detail-text">
						<i
							v-if="detail.type === 'audio'"
							class="coos-iconfont icon-14guaduan-1 detail-text-icon"
						></i>
						<pre class="detail-pre">{{ detail.content }}</pre>
					</div>
					<!--  代码块展示时候的操作按钮  -->
					<div v-if="detail.type === 'code'" class="detail-utils">
						<div class="detail-utils-btn">
							<div @click="changeLang(index + '-' + i + '-select')">
								{{ detail.language }}
							</div>
							<i class="coos-iconfont icon-nav-bottom"></i>
							<el-select
								:ref="index + '-' + i + '-select'"
								v-model="detail.language"
								placeholder="请选择"
							>
								<el-option
									v-for="lang in options"
									:key="lang.value"
									:label="lang.label"
									:value="lang.value"
								></el-option>
							</el-select>
						</div>
						<div class="detail-utils-btn" @click="copy(detail.content)">
							<i class="coos-iconfont icon-Dialog"></i>
							复制
						</div>
					</div>
				</div>
				<FileById
					:more-style="{
						borderRadius: '6px',
						margin: detail.isSelf ? '0 0 0 10px' : '0 10px 0 0'
					}"
					:size="40"
					:default-font-icon="detail.name"
				></FileById>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	name: 'PersonHistory',
	props: {
		index: {
			type: Number,
			default: () => {
				return 0;
			}
		},
		item: {
			type: Object,
			default: () => {
				return {};
			}
		},
		srcList: {
			type: Array,
			default: () => {
				return [];
			}
		}
	},
	data() {
		return {
			/**语言种类*/
			options: [
				{
					label: 'java',
					value: 'java'
				},
				{
					label: 'javascript',
					value: 'javascript'
				},
				{
					label: 'jsp',
					value: 'jsp'
				},
				{
					label: 'xml',
					value: 'xml'
				},
				{
					label: 'html',
					value: 'html'
				},
				{
					label: 'stylus',
					value: 'stylus'
				},
				{
					label: 'nginx',
					value: 'nginx'
				},
				{
					label: 'apache',
					value: 'apache'
				}
			]
		};
	},
	methods: {
		/**右键菜单*/
		contextmenu(event, index, i) {
			this.$emit('contextmenu', event, index, i);
		},
		/**复制*/
		copy(context) {
			navigator.clipboard.writeText(context).then(() => {
				this.$message.success('复制成功！');
			});
		},
		/**切换语言*/
		changeLang(ref) {
			this.$refs[ref][0].visible = true;
		}
	}
};
</script>

<style scoped lang="scss">
.person-item {
	width: 100%;
	&-time {
		font-size: 12px;
		font-weight: 400;
		color: #515b6e;
		line-height: 20px;
		text-align: center;
		margin-bottom: 24px;
	}
	.noSelf {
		flex-direction: row-reverse;
		.detail {
			border-radius: 0px 6px 6px 6px;
		}
	}
	.reBack {
		font-size: 12px;
		color: $subTextColor;
		text-align: center;
		margin-bottom: 14px;
	}
	&-message {
		@include flexBox(flex-end, flex-start);
		margin-bottom: 14px;
		.codeDetail {
			min-width: 50%;
		}
		.detail {
			padding: 12px;
			max-width: 50%;
			background: #ffffff;
			border-radius: 6px 0px 6px 6px;
			font-size: 14px;
			font-weight: 400;
			color: #000000;
			line-height: 22px;
			position: relative;
			&-pre {
				margin: 0;
				white-space: pre-wrap; //pre-line
				word-wrap: break-word;
				&-code {
					@include scrollBar;
				}
			}
			&-utils {
				position: absolute;
				top: 12px;
				right: 12px;
				@include flexBox();
				&-btn {
					padding: 2px 5px;
					color: #f0f0f0;
					border-radius: 3px;
					font-size: 12px;
					cursor: pointer;
					@include flexBox();
					&:hover {
						background: rgba(255, 255, 255, 0.3);
					}
					::v-deep .el-select {
						visibility: hidden;
						width: 40px;
						position: absolute;
						right: 80px;
						top: 0;
					}
				}
			}

			&-text {
				@include flexBox(flex-start);
				&-icon {
					font-size: 21px;
					margin-right: 11px;
					color: $textColor;
				}
			}
		}
	}
}
</style>
