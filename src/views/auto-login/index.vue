<!--第三方系统跳转过来自动同步登录,同步登录中...-->
<template>
	<div class="auto-login"></div>
</template>

<script>
import { autoLogin } from '@/api/modules/login';
import { handleLoginRes } from '@/utils/handle-login';

export default {
	name: 'AutoLogin',
	created() {
		if (this.$route.query.code) {
			autoLogin(this.$route.query.code).then(res => {
				if (res.code === 200) {
					handleLoginRes(res, decodeURIComponent(this.$route.query.redirectUrl), true);
				} else {
					this.$message.error(res.message);
				}
			});
		}
	}
};
</script>

<style scoped>
.auto-login {
	height: 100vh;
	width: 100vw;
	display: flex;
	align-items: center;
	justify-content: center;
}
</style>
