<template>
	<div class="document" @click="whichId = ''">
		<vue2-water-marker
			v-if="isWaterMarker"
			width="420"
			font-size="14"
			:text="waterMarker"
			:font="'SimSun'"
			opacity="0.25"
		></vue2-water-marker>
		<div v-if="!hideMenu" class="document-menu">
			<div class="document-menu-title">
				<span>云文档</span>
				<div v-if="showDownLoad && !downLoaded" class="download-text" @click="openDownload">
					<span class="download-text-label">下载中</span>
					<i class="el-icon-bottom down-icon"></i>
				</div>
			</div>
			<div class="document-menu-main">
				<el-menu
					background-color="#F0F0F0"
					:default-active="defaultActive"
					@select="
						index => {
							selectChange(index);
						}
					"
				>
					<!-- <el-menu-item index="1">
						<svg-icon icon-class="main-home" class="icon-svg icon-one"></svg-icon>
						<svg-icon icon-class="main-home-selected" class="icon-svg icon-two"></svg-icon>
						<span slot="title">主页</span>
					</el-menu-item> -->
					<el-menu-item index="2">
						<i class="coos-iconfont icon-wenjianjia svg-icon"></i>
						<span slot="title">我的空间</span>
					</el-menu-item>
					<el-menu-item index="3">
						<i class="coos-iconfont icon-linggan svg-icon"></i>
						<span slot="title">知识库</span>
					</el-menu-item>
					<el-menu-item index="4">
						<i class="coos-iconfont icon-shoucang1 svg-icon"></i>
						<span slot="title">收藏</span>
					</el-menu-item>
					<el-menu-item index="5">
						<i class="coos-iconfont icon-shanchu svg-icon"></i>
						<span slot="title">回收站</span>
					</el-menu-item>
				</el-menu>
			</div>
		</div>
		<div v-loading="contentLoading" class="content">
			<titleInfo
				:key="isAdministrator"
				ref="titleInfo"
				:doc-total="docTotal"
				:module-name="moduleName"
				:show-but="showBut"
				:space-name="spaceName"
				:bread-name="breadName"
				:choose-index="chooseIndex"
				:parent-id="parentId"
				:space-id="spaceId"
				:is-administrator="isAdministrator"
				:file-can-edit="fileCanEdit"
				@backHome="backHome"
				@backSpaceList="backSpaceList"
				@backFolderList="backFolderList"
				@newFolder="newFolder"
				@onProcess="onProcess"
				@handleClear="handleClear"
			></titleInfo>
			<knowledge
				v-if="chooseIndex === 3"
				ref="knowledge"
				:show-label="showLabel"
				:tag-options="tagOptions"
				:choose-index="chooseIndex"
				:parent-id.sync="parentId"
				:new-files="newFiles"
				:folder-data="folderData"
				:is-fav="isFav"
				:which-id="whichId"
				:select-file-id="parentId"
				@changeShowBut="changeShowBut"
				@getBreadName="getBreadName"
				@getSpaceName="getSpaceName"
				@handleEdit="handleEdit"
				@detailsFile="detailsFile"
				@changeDefalut="changeDefalut"
				@deleteNewFile="deleteNewFile"
				@updateTotal="updateTotal"
			></knowledge>
			<detailList
				v-if="[2, 4, 5].includes(chooseIndex)"
				ref="detailRef"
				:choose-index="chooseIndex"
				:space-data="spaceData"
				:space-loading="spaceLoading"
				:is-create="isCreate"
				:which-id="whichId"
				:is-administrator="isAdministrator"
				:new-files="newFiles"
				:select-file-id="parentId"
				@changeShowBut="changeShowBut"
				@selectChange="selectChange"
				@cancleFolder="cancleFolder"
				@getBreadName="getBreadName"
				@changeLoadingg="changeLoadingg"
				@changeDefalut="changeDefalut"
				@deleteNewFile="deleteNewFile"
				@handleEdit="handleEdit"
				@detailsFile="detailsFile"
				@moreData="moreData"
				@updateTotal="updateTotal"
			></detailList>
		</div>
		<downloadContent v-if="showDownLoad" ref="downloadContent" @loaded="loaded"></downloadContent>
	</div>
</template>

<script>
import knowledge from './document-info/knowledge-info.vue';
import titleInfo from './document-info/title-info.vue';
import detailList from './document-info/detail-list/index.vue';
import downloadContent from '@/components/download-content/index.vue';
import {
	documentList,
	getParentName,
	clearAll,
	getFileParent,
	adminAuthority
} from '@/api/modules/document-content';
import { deepClone, tabSize } from '@/utils';
import { mapGetters } from 'vuex';
import { getDocWater } from '@/utils/get-water';
import { geMatrixtDicts } from '@/api/modules/common';
import { CoosEventTypes } from '@/utils/bus';
import { getDictionary } from '@/utils/data-dictionary';
import { isEmpty } from '@/utils';
export default {
	components: { knowledge, titleInfo, detailList, downloadContent },
	props: {
		// 是否组件模式
		isComponents: {
			type: Boolean,
			default: () => {
				return false;
			}
		},

		// 组件是否隐藏菜单
		componentHideMenu: {
			type: Boolean,
			default: () => {
				return false;
			}
		},
		// 知识空间是否显示标签筛选
		showLabel: {
			type: Boolean,
			default: () => {
				return true;
			}
		}
	},
	data() {
		return {
			downLoaded: true,
			hideMenu: false, // 是否隐藏菜单
			chooseIndex: 1,
			defaultActive: '2',
			moduleName: '我的空间',
			spaceName: '',
			breadName: [], // 面包屑数组
			spaceData: [], //详情list
			listParams: {}, //list接口参数
			spaceLoading: true,
			showBut: true,
			parentId: '', // 用于查询上一级列表,
			isCreate: false,
			spaceId: '',
			isAdministrator: false,
			folderData: [],
			isFav: false, // 是否是收藏条状
			newFiles: [],
			totalSize: 0,
			docTotal: 0, // 面包屑上面的数量统计
			contentLoading: false,
			//文件跳转是否高亮显示
			whichId: '',
			routeQuery: {},
			fileCanEdit: true,
			isComponentsSearch: false,
			tagOptions: []
		};
	},
	computed: {
		...mapGetters(['waterConfig']),
		isWaterMarker() {
			return this.waterConfig.im && this.waterConfig.im.isWaterMarker;
		},
		waterMarker() {
			return this.waterConfig.im && this.waterConfig.im.waterMarker;
		},
		// 显示下载组件
		showDownLoad() {
			return this.$route.path === '/document' || this.$route.query.hideLoayout === 'true';
		}
	},
	watch: {
		chooseIndex: {
			handler(newVal, oldVal) {
				// 我的空间都有操作权限   其余的根据业务判断
				this.isAdministrator = newVal === 2 || this.isAdministrator;
				if (newVal === 4) {
					this.isAdministrator = false;
				}
			}
		},
		defaultActive(newVal) {
			this.setParams();
		},
		$route(newVal) {
			this.searchFolder(newVal);
		}
	},
	activated() {
		if (this.defaultActive === '2') {
			this._BUS.$emit(CoosEventTypes.changeTempAiEnter, [getDictionary('AI编码/智答私库')]);
		}
		getDocWater();
	},
	mounted() {
		if (this.showLabel) {
			this.getTagOptions(); // 获取标签下拉选择数据
		}
		getDocWater();
		this.hideMenu = !!this.$route.query.hideMenu || this.componentHideMenu;
		this.searchFolder(this.$route, true);
	},
	deactivated() {
		this._BUS.$emit(CoosEventTypes.changeTempAiEnter, []);
	},
	methods: {
		// 组件模式 父组件 重新请求数据 带参
		getSearchList(item) {
			let obj = {
				query: item
			};
			this.isComponentsSearch = true;
			this.searchFolder(obj, true);
		},
		// 打开全局下载弹窗
		openDownload() {
			this.$refs.downloadContent.open();
		},
		// 下载完成
		loaded(downLoaded) {
			this.downLoaded = downLoaded;
		},
		setParams() {
			if (this.defaultActive === '2') {
				this._BUS.$emit(CoosEventTypes.changeTempAiEnter, [getDictionary('AI编码/智答私库')]);
				this._BUS.$emit(CoosEventTypes.aiSessionParams, {
					coosType: 'space',
					coosTypeCode: getDictionary('AI编码/智答私库'),
					applicationId: getDictionary('应用ID/文档'),
					modeType: 'zdbk',
					canDbClick: true
				});
			} else if (this.defaultActive === '3') {
				this._BUS.$emit(CoosEventTypes.changeTempAiEnter, []);
				this._BUS.$emit(CoosEventTypes.aiSessionParams, {
					// coosType: 'space',
					// coosTypeCode: getDictionary('AI编码/智答私库'),
					// applicationId: getDictionary('应用ID/文档'),
					// modeType: 'zdbk',
					// clickCallback: () => {
					// 	setTimeout(() => {
					// 		this._BUS.$emit(CoosEventTypes.changeIm, 'zdbk');
					// 	}, 1000);
					// 	this.$router.push('/wile-fire/home/<USER>');
					// }
				});
			} else {
				this._BUS.$emit(CoosEventTypes.changeTempAiEnter, []);
				this._BUS.$emit(CoosEventTypes.aiSessionParams, {});
			}
		},
		/**获取标签下拉选择数据*/
		getTagOptions() {
			geMatrixtDicts('disk_space_category').then(res => {
				if (res.code === 200) {
					this.tagOptions = res.result.disk_space_category;
				} else {
					this.$message.error(res.message);
				}
			});
		},
		/**搜索进入文件*/
		searchFolder(newVal, isMounted = false) {
			// 监听本页面路由参数发生改变
			if (/\/document/.test(newVal.path) || this.isComponents) {
				this.setParams();
				if (!this.isComponentsSearch && this.isComponents) return;
				this.routeQuery = deepClone(newVal.query);
				// 初始化 和 路由变化带参过来  需要改变选中menu
				if (isMounted || newVal.query.chooseIndex) {
					this.selectChange(newVal.query.chooseIndex || '2', !newVal.query.chooseIndex);
				}
				if (newVal.query.id) {
					this.contentLoading = true;
					const { id, folder, spaceId, parentId } = newVal.query;
					this.whichId = id;
					// 根据文件或者目录的id，获取文件或者目录的的上一级目录
					if (folder === 'true') {
						getParentName(id).then(res => {
							this.getBreadName(res.result, id);
						});
					} else {
						getFileParent(id).then(res => {
							this.getBreadName(res.result, id);
						});
					}
					// 如果是知识空间id，要判断有无权限
					if (spaceId) {
						this.defaultActive = '3';
						this.chooseIndex = 3;
						adminAuthority({ spaceId: spaceId }).then(result => {
							this.isAdministrator = result.result;
							documentList({
								spaceType: true,
								parentId: folder === 'true' ? id : parentId,
								spaceId: spaceId
							}).then(res => {
								let interval = res.result.records;
								interval = this.transformSize(interval);
								this.changeDefalut('3', interval, result.result, spaceId, id, parentId);
								this.contentLoading = false;
							});
						});
					}
					// 如果是我的空间，直接获取列表
					else {
						this.defaultActive = '2';
						this.changeShowBut(true);
						documentList({ spaceType: false, parentId: folder === 'true' ? id : parentId }).then(
							res => {
								this.chooseIndex = 2;
								this.$nextTick(() => {
									this.$refs.detailRef.currentId = id;
									this.$refs.detailRef.breadNum = 2;
									this.changeDefalut('2');
									this.$refs.detailRef.FolderData = res.result.records;
									this.$refs.detailRef.transformFileSize(res.result.records);
								});
								this.contentLoading = false;
							}
						);
					}
					// 清除路由上面的参数
					this.$router.replace({ path: newVal.path, query: {} });
				}
			}
		},
		moreData() {
			if (this.totalSize > this.spaceData.length) {
				this.listParams.pageNo++;
				this.getDetailList();
			}
		},
		/**清空回收站*/
		handleClear() {
			let sp = this.spaceData.length;
			this.$confirm(sp ? '你确定要清空回收站吗?' : '你的回收站为空', '提示', {
				confirmButtonText: sp ? '确定清空' : '确认',
				showCancelButton: sp,
				type: 'warning'
			})
				.then(() => {
					if (sp) {
						clearAll().then(() => {
							this.selectChange('5');
							this.$message({
								type: 'success',
								message: '清除成功!'
							});
						});
					}
				})
				.catch(() => {});
		},
		/**更新数量*/
		updateTotal(totalSize) {
			this.docTotal = totalSize;
		},
		deleteNewFile(index) {
			this.$refs.titleInfo.deleteNewFile(index);
		},
		handleEdit(row) {
			this.$refs.titleInfo.handleEdit(row);
		},
		detailsFile(row) {
			this.$refs.titleInfo.detailsFile(row);
		},
		/**上传文件进度条事件*/
		onProcess(newVal) {
			this.newFiles = newVal;
		},
		changeDefalut(index, val, isAdmin, spaceId, id, parentId) {
			this.spaceId = spaceId;
			this.isAdministrator = isAdmin;
			this.defaultActive = index;
			this.spaceLoading = false;
			this.chooseIndex = parseInt(index);
			this.$nextTick(() => {
				if (index == '3') {
					this.$refs.knowledge.isAdministrator = isAdmin;
					this.$refs.knowledge.spaceId = spaceId;

					if (parentId == 0) {
						this.parentId = parentId;
					} else {
						this.parentId = id;
					}
				}
			});
			this.folderData = val;
			this.isFav = true;
			this.moduleName = index == '2' ? '我的空间' : '知识库';
		},
		changeLoadingg(val) {
			this.spaceLoading = val;
		},
		changeShowBut(isAdministrator) {
			this.showBut = true;
			this.isAdministrator = isAdministrator;
		},
		/**
		 * @Desc 标签菜单模式自定义触发改变
		 * @Param {String} index 菜单选中项
		 * @Param {String} tag 标签
		 * */
		tagMenuChange(index, tag = '') {
			this.selectChange(index, true, tag);
		},
		/**
		 * @Desc 菜单选中项发生变化
		 * @Param {String} index 菜单选中项
		 * @Param {Boolean} isRequest 是否发起请求
		 * @Param {String} tag 标签
		 * */
		selectChange(index, isRequest = true, tag = '') {
			this.parentId = '';
			this.defaultActive = index;
			this.breadName = [];
			this.spaceName = '';
			this.chooseIndex = parseInt(index);
			this.spaceData = [];
			switch (this.chooseIndex) {
				case 1:
					this.moduleName = '主页';
					break;
				case 2:
					this.parentId = 0;
					this.isAdministrator = true;
					this.fileCanEdit = true;
					this.moduleName = '我的空间';
					this.listParams = {
						pageNo: 1,
						spaceType: false,
						parentId: 0
					};
					this.getDetailList();
					this.$nextTick(() => {
						if (this.$refs.detailRef) this.$refs.detailRef.currentId = 0;
					});
					break;
				case 3:
					this.moduleName = '知识库';
					this.isAdministrator = false;
					this.$nextTick(() => {
						this.$refs.knowledge.searchList(this.routeQuery, tag);
					});
					break;
				case 4:
					this.moduleName = '收藏';
					this.isAdministrator = false;
					this.listParams = {
						pageNo: 1,
						isFavorite: true
					};
					isRequest && this.getDetailList();
					break;
				default:
					this.moduleName = '回收站';
					this.isAdministrator = false;

					this.listParams = {
						pageNo: 1,
						delFlag: true,
						spaceType: false
					};
					this.getDetailList();
			}
		},
		getDetailList() {
			this.spaceLoading = true;
			this.$nextTick(() => {
				if (this.$refs.detailRef) this.$refs.detailRef.breadNum = 1;
			});
			documentList(this.listParams).then(res => {
				this.totalSize = res.result.total;
				this.docTotal = this.totalSize;
				let datalist = [];
				this.spaceData = this.listParams.pageNo > 1 ? this.spaceData : [];
				datalist = res.result.records;
				this.transformSize(datalist);
				// datalist.forEach(item => {
				// 	if (item.fileSize < 1024 * 1024) {
				// 		item.fileSize = (item.fileSize / 1024).toFixed(2) + 'KB';
				// 	} else if (1024 * 1024 < item.fileSize < 1024 * 1024 * 1024) {
				// 		item.fileSize = (item.fileSize / (1024 * 1024)).toFixed(2) + 'MB';
				// 	} else {
				// 		item.fileSize = (item.fileSize / (1024 * 1024 * 1024)).toFixed(2) + 'GB';
				// 	}
				// 	// this.$refs.detailRef.FolderData.push(item)
				// 	this.spaceData.push(item);
				// });
				// this.$refs.detailRef.FolderData = this.spaceData;
				this.spaceLoading = false;
			});
		},
		// 获取面包屑名称
		getBreadName(breadName, id, canEdit = true) {
			this.spaceName = isEmpty(breadName) ? '' : breadName[0].name;
			this.parentId = id;
			this.breadName = breadName;
			this.fileCanEdit = this.chooseIndex === 2 || canEdit;
		},
		// 获取空间名称
		getSpaceName(spaceName, spaceId) {
			this.spaceName = spaceName;
			this.spaceId = spaceId;
			this.parentId = 0;
		},
		// 空间列表
		backHome() {
			// this.spaceLoading = true
			this.isAdministrator = false;
			this.parentId = 0;
			this.breadName = [];
			this.spaceName = '';
			this.chooseIndex = this.moduleName === '知识库' ? 3 : 2;
			if (this.chooseIndex === 3) {
				this.$nextTick(() => {
					this.$refs.knowledge.isEnter = true;
					this.$refs.knowledge.getTableList();
				});
			} else {
				this.isAdministrator = true;
				this.$refs.detailRef.getFolderData(0, false);
			}
		},
		// 点击面包屑进入空间后的列表
		backSpaceList(id) {
			this.breadName = [];
			this.parentId = 0;
			this.$refs.knowledge.resetSpaceData(id);
			if (id) {
				adminAuthority({ spaceId: id }).then(res => {
					this.fileCanEdit = res.result;
				});
			}
		},
		backFolderList(id) {
			this.parentId = id;
			if (this.chooseIndex === 3) {
				this.$refs.knowledge.backList(id, this.spaceId);
			} else {
				getParentName(id).then(res => {
					this.breadName = res.result;
				});
				this.$refs.detailRef.getFolderData(id);
			}
		},
		newFolder() {
			if (this.chooseIndex === 3) {
				this.$refs.knowledge.newFolder();
			} else {
				this.isCreate = true;
			}
		},
		// 取消新建
		cancleFolder() {
			this.isCreate = false;
		},
		// 处理文件大小方法
		transformSize(size) {
			size.forEach(item => {
				item.orgSize = item.fileSize;
				item.fileSize = tabSize(item.fileSize);
				// this.$refs.detailRef.FolderData.push(item)
				this.spaceData.push(item);
			});
			return size;
		}
	}
};
</script>

<style lang="scss" scoped>
@keyframes toBottom {
	from {
		transform: translateY(-14px);
	}
	to {
		transform: translateY(14px);
	}
}
.download-text {
	cursor: pointer;
	overflow: hidden;
	display: flex;
	align-items: center;
	justify-content: center;
	color: var(--brand-6);
	margin-right: 4px;
}
.download-text-label {
	margin-right: 4px;
	font-size: 14px;
}
.down-icon {
	animation: toBottom 1s infinite;
}
.document {
	display: flex;
	height: 100%;
	.document-menu {
		flex-shrink: 0;
		width: 284px;
		background: #f0f0f0;
		height: 100%;
		padding: 16px 0px 8px 10px;
		.document-menu-title {
			padding: 0 3px;
			line-height: 24px !important;
			color: $primaryTextColor !important;
			font-weight: 800 !important;
			font-size: 18px !important;
			margin-bottom: 8px !important;
			display: flex;
			align-items: center;
			justify-content: space-between;
		}
		.document-menu-main {
			margin-right: 6px;
			.el-menu {
				border-right: none;
			}
			.svg-icon {
				font-size: 20px;
			}
			.el-menu-item:hover {
				border-radius: 6px;
			}
			.el-menu-item:hover .svg-icon {
				color: var(--brand-6) !important;
			}
			.el-menu-item {
				margin: 2px 0px;
				padding-left: 8px !important;
				height: 46px;
				line-height: 46px;
				color: $primaryTextColor;

				.icon-two {
					display: none;
				}
				span {
					margin-left: 4px;
				}
			}
			.el-menu-item.is-active {
				border-radius: 6px;
				.icon-two {
					display: inline-block;
				}
				.icon-one {
					display: none;
				}
			}
		}
	}
	.content {
		background: #fff;
		flex: 1;
		overflow: hidden;
		display: flex;
		flex-direction: column;
	}
}
</style>
