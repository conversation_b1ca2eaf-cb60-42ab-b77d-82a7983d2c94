<template>
	<div class="document-management-pop">
		<div class="document-management-pop-title">空间信息</div>
		<div class="document-management-pop-form">
			<el-form
				ref="From"
				:model="Data"
				:rules="rules"
				:validate-on-rule-change="false"
				:hide-required-asterisk="true"
			>
				<el-form-item label="空间名称" prop="spaceName">
					<el-input v-model="Data.spaceName" placeholder="请输入"></el-input>
				</el-form-item>
				<el-form-item label="绑定标签" prop="spaceCategory">
					<el-select
						v-model="Data.spaceCategory"
						multiple
						class="custom-el-select"
						clearable
						placeholder="请选择"
					>
						<el-option
							v-for="(item, index) of tagOptions"
							:key="index"
							:label="item.label"
							:value="item.value"
						></el-option>
					</el-select>
				</el-form-item>
				<!--				<el-form-item>-->
				<!--					<div class="spaceStatus">-->
				<!--						<label style="margin-right: 10px">空间状态:</label>-->
				<!--						<el-switch v-model="Data.status" style="margin-right: 10px"></el-switch>-->
				<!--						<el-checkbox v-model="Data.isTop" style="margin-bottom: 7px">置顶此空间</el-checkbox>-->
				<!--					</div>-->
				<!--				</el-form-item>-->
				<el-form-item label="空间说明" prop="description">
					<el-input
						v-model="Data.description"
						type="textarea"
						:autosize="{ minRows: 2, maxRows: 4 }"
						placeholder="请输入"
					></el-input>
				</el-form-item>
				<!--				<el-form-item>-->
				<!--					<div style="padding: 0 0 8px 3px">分配存储空间</div>-->
				<!--					<el-select v-model="Data.limitMemory">-->
				<!--						<el-option-->
				<!--							v-for="item in options"-->
				<!--							:key="item.value"-->
				<!--							:label="item.label"-->
				<!--							:value="item.value"-->
				<!--						/>-->
				<!--					</el-select>-->
				<!--				</el-form-item>-->

				<el-form-item>
					<div class="document-management-pop-form-label">设置空间管理员</div>
					<div class="document-management-pop-form-Users">
						<div style="color: #2f446b">{{ spaceManager }}</div>
						<div class="document-management-pop-form-configuration" @click="openConfig(1)">
							配置
						</div>
					</div>
				</el-form-item>
				<el-form-item>
					<div class="document-management-pop-form-label">设置空间可见范围</div>
					<el-checkbox v-model="Data.isAllVisible" @change="checkBoxChange">
						设为所有成员均可见
					</el-checkbox>
				</el-form-item>
				<el-form-item>
					<div class="document-management-pop-form-Users">
						<div style="color: #2f446b">{{ spacialScale }}</div>
						<div
							v-if="!Data.isAllVisible"
							class="document-management-pop-form-configuration"
							@click="openConfig(2)"
						>
							配置
						</div>
					</div>
				</el-form-item>
				<el-form-item>
					<div class="document-management-pop-form-label">设置文档下载范围</div>
					<el-checkbox v-model="Data.isAllDown" @change="checkBoxDonwChange">
						设为可见成员均可下载
					</el-checkbox>
				</el-form-item>
				<el-form-item>
					<div class="document-management-pop-form-Users">
						<div style="color: #2f446b">{{ isAllDowntext }}</div>
						<div
							v-if="!Data.isAllDown"
							class="document-management-pop-form-configuration"
							@click="openConfig(3)"
						>
							配置
						</div>
					</div>
				</el-form-item>
			</el-form>
		</div>
		<orgPersonnelDialog
			:title="dialogTitle"
			:visible="visible"
			:init-values="initValue"
			:data-source="dataSource"
			:need-all-data="true"
			:sure-loading="false"
			:is-all="isAllVisible"
			:disable-org="false"
			@change="changeSelect"
			@sure="sure"
			@close="close"
			@changeRadio="changeRadio"
		></orgPersonnelDialog>
		<!-- :is-all="(Data.isAllVisible = true)" -->
	</div>
</template>
<script>
import orgPersonnelDialog from '@/components/org-personnel-dialog'; //配置人员弹窗
import { getConfigList } from '@/api/modules/document-content';
import { deepClone } from '@/utils';
import { geMatrixtDicts } from '@/api/modules/common';
export default {
	components: { orgPersonnelDialog },
	props: {
		/**接收父组件form表单的初始化数据 */
		formData: {
			type: Object,
			default: () => {}
		}
	},

	data() {
		return {
			dialogTitle: '设置空间可见范围',
			Data: deepClone(this.formData), //在弹窗初始化加载时进行的赋值
			tagOptions: [],
			options: [
				{
					label: '不限制',
					value: '不限制'
				},
				{
					label: '1GB',
					value: '1GB'
				},
				{
					label: '5GB',
					value: '5GB'
				},
				{
					label: '10GB',
					value: '10GB'
				}
			], //多选框options配置项
			rules: {
				spaceName: [{ required: true, message: '请填写空间名称', trigger: 'blur' }],
				description: [{ required: true, message: '请填写空间说明', trigger: 'blur' }]
			}, //表单校验
			dataSource: ['org', 'depart', 'user', 'label'], //部分人员可选类型
			visible: false, //配置人员 弹窗开关
			shareAndAdmin: 1, //区分 空间管理员 1 空间可见范围2 空间下载范围 3
			shareObjectList: [], //接收空间可见范围 选择人员后的数据 在保存时再统一对Data进行修改
			downObjectList: [], //接收空间下载范围 选择人员后的数据 在保存时再统一对Data进行修改
			adminObjectList: [], //接收空间管理员 选择人员后的数据 在保存时再统一对Data进行修改
			spacialScale: '暂无', //空间可见范围回显字段
			spaceManager: '暂无', //空间管理员回显字段
			isAllDowntext: '暂无',
			isAllVisible: false, //是否禁用配置弹窗全部人员
			configList: [] //配置人员 初始数据
		};
	},
	computed: {
		/**配置部分人员 数据初始化*/
		initValue() {
			return this.configList;
		}
	},
	watch: {
		/**打开弹窗时 进行数据初始化 */
		formData: {
			deep: true,
			handler: function (newValue) {
				this.Data = deepClone(newValue);
				this.configData();
			}
		}
	},
	mounted() {
		this.configData();
		this.getTagOptions();
	},
	methods: {
		/**获取标签下拉选择数据*/
		getTagOptions() {
			geMatrixtDicts('disk_space_category').then(res => {
				if (res.code === 200) {
					this.tagOptions = res.result.disk_space_category;
				} else {
					this.$message.error(res.message);
				}
			});
		},
		/**保存空间  */
		saveHandleclick() {
			return new Promise((resolve, reject) => {
				this.$refs.From.validate(valid => {
					if (valid) {
						if (this.Data.adminObjectList) {
							resolve(deepClone(this.Data));
						} else {
							this.$message.error('空间管理员至少选择一项');
						}
					} else {
						reject('校验失败');
						return false;
					}
				});
			});
		},
		/**文字及数据初始化处理 */
		async configData() {
			await getConfigList({ type: 1, id: this.Data.id }).then(res => {
				this.Data.adminObjectList = this.configProcess(res.result);
				this.adminObjectList = this.configProcess(res.result);
			});
			await getConfigList({ type: 2, id: this.Data.id }).then(res => {
				this.Data.shareObjectList = this.configProcess(res.result);
				this.shareObjectList = this.configProcess(res.result);
			});
			await getConfigList({ type: 3, id: this.Data.id }).then(res => {
				this.Data.downObjectList = this.configProcess(res.result);
				this.downObjectList = this.configProcess(res.result);
			});
			if (this.Data.isAllVisible == true) {
				this.spacialScale = '所有成员均可见';
			} else if (this.Data.shareObjectList) {
				this.spacialScale = this.DataProcess(this.Data.shareObjectList);
			} else {
				this.spacialScale = '暂无';
			}
			if (this.Data.isAllDown == true) {
				this.isAllDowntext = '可见成员均可下载';
			} else if (this.Data.downObjectList) {
				this.isAllDowntext = this.DataProcess(this.Data.downObjectList);
			} else {
				this.isAllDowntext = '暂无';
			}
			if (this.Data.adminObjectList) {
				this.spaceManager = this.DataProcess(this.Data.adminObjectList);
			} else {
				this.spaceManager = '暂无';
			}
		},
		/** 空间可见范围 复选框 */
		checkBoxChange(val) {
			if (val) {
				this.Data.shareObjectList = [];
				this.spacialScale = '所有成员均可见';
				this.isAllVisible = val;
			} else {
				this.spacialScale = '暂无';
			}
		},
		/** 空间可见范围 复选框 */
		checkBoxDonwChange(val) {
			if (val) {
				this.Data.downObjectList = [];
				this.isAllDowntext = '可见成员均可下载';
				this.isAllDown = val;
			} else {
				this.isAllDowntext = '暂无';
			}
		},
		/**人员配置 open */
		openConfig(type) {
			this.dialogTitle =
				type === 1 ? '设置空间管理员' : type === 2 ? '设置空间可见范围' : '设置文档下载范围';
			this.configList = []; //初始化
			this.shareAndAdmin = type;
			if (this.shareAndAdmin == 2 && this.Data.isAllVisible) {
				this.isAllVisible = true;
			} else {
				this.isAllVisible = false;
			}
			//设置空间管理员
			if (this.shareAndAdmin == 1 && this.Data.adminObjectList) {
				this.Data.adminObjectList.forEach(item => {
					this.configList.push({
						dataType:
							item.objectType === 4
								? 'org'
								: item.objectType === 1
								? 'depart'
								: item.objectType === 3
								? 'label'
								: 'user',
						id: item.objectId,
						title: item.objectName,
						avatarUrl: item.objectavatar
					});
				});
			} else if (this.shareAndAdmin == 2 && this.Data.shareObjectList) {
				//设置空间管理员
				this.Data.shareObjectList.forEach(item => {
					this.configList.push({
						dataType:
							item.objectType === 4
								? 'org'
								: item.objectType === 1
								? 'depart'
								: item.objectType === 3
								? 'label'
								: 'user',
						id: item.objectId,
						title: item.objectName,
						avatarUrl: item.objectavatar
					});
				});
			} else if (this.shareAndAdmin == 3 && this.Data.downObjectList) {
				//设置空间管理员
				this.Data.downObjectList.forEach(item => {
					this.configList.push({
						dataType:
							item.objectType === 4
								? 'org'
								: item.objectType === 1
								? 'depart'
								: item.objectType === 3
								? 'label'
								: 'user',
						id: item.objectId,
						title: item.objectName,
						avatarUrl: item.objectavatar
					});
				});
			}
			this.visible = true;
		},
		/**配置人员 选择全部人员时  */
		changeRadio(val) {
			if (val == 2) {
				this.isAllVisible = true;
			} else {
				this.isAllVisible = false;
			}
		},
		/**配置人员 选择部分人员时 */
		changeSelect(val) {
			if (this.shareAndAdmin == 1) {
				this.adminObjectList = this.configProcess(val);
			}
			if (this.shareAndAdmin == 2) {
				this.shareObjectList = this.configProcess(val);
			}
			if (this.shareAndAdmin == 3) {
				this.downObjectList = this.configProcess(val);
			}
		},
		/**配置人员 确认 */
		sure(e, bool) {
			this.visible = false;
			if (this.shareAndAdmin == 3) {
				console.log(this.Data, 'this.Data');
				if (bool) {
					this.Data.isAllDown = true;
					this.Data.downObjectList = [];
					this.isAllDowntext = '可见成员均可下载';
					this.isAllDown = true;
				} else {
					if (this.downObjectList.length > 0) {
						this.Data.downObjectList = this.downObjectList;
						this.isAllDowntext = this.DataProcess(this.Data.downObjectList);
					} else {
						this.isAllDowntext = '暂无';
						this.Data.downObjectList = null;
					}
				}
			}
			if (this.shareAndAdmin == 2) {
				this.Data.isAllVisible = this.isAllVisible;
				if (this.Data.isAllVisible) {
					this.spacialScale = '所有成员均可见';
					this.Data.shareObjectList = null;
				} else if (this.shareObjectList.length > 0) {
					this.Data.shareObjectList = this.shareObjectList;
					this.spacialScale = this.DataProcess(this.Data.shareObjectList);
				} else {
					this.spacialScale = '暂无';
					this.Data.shareObjectList = null;
				}
			}
			if (this.shareAndAdmin == 1) {
				if (this.adminObjectList.length > 0) {
					this.Data.adminObjectList = this.adminObjectList;
					this.spaceManager = this.DataProcess(this.Data.adminObjectList);
				} else {
					this.spaceManager = '暂无';
					this.Data.adminObjectList = null;
				}
			}
		},
		/**配置人员 取消*/
		close() {
			this.visible = false;
		},
		/**配置 数据处理 */
		configProcess(val) {
			return val.map(item => {
				return {
					objectType:
						item.dataType === 'org'
							? 4
							: item.dataType === 'label'
							? 3
							: item.dataType === 'depart'
							? 1
							: 2,
					objectName: item.title,
					objectId: item.id,
					objectavatar: item.avatarUrl || item.logo
				};
			});
		},
		/** 文字回显(文字处理) */
		DataProcess(arr) {
			let name = [
				{
					objectNames: [],
					objectTypeDesc: '部门'
				},
				{
					objectNames: [],
					objectTypeDesc: '成员'
				},
				{
					objectNames: [],
					objectTypeDesc: '标签'
				},
				{
					objectNames: [],
					objectTypeDesc: '组织'
				}
			];
			arr.map(item => {
				switch (item.objectType) {
					case 1: {
						name[0].objectNames.push(item.objectName);
						break;
					}
					case 3: {
						name[2].objectNames.push(item.objectName);
						break;
					}
					case 4: {
						name[3].objectNames.push(item.objectName);
						break;
					}
					default: {
						name[1].objectNames.push(item.objectName);
						break;
					}
				}
			});
			return name
				.filter(item => item.objectNames.length > 0)
				.map(item => {
					return (
						(item.objectNames.length > 2
							? [item.objectNames[0], item.objectNames[1]].join('、')
							: item.objectNames.join('、')) +
						(item.objectNames.length > 2 ? '等' : '') +
						item.objectNames.length +
						(item.objectTypeDesc == '成员' ? '名' : '个') +
						(item.objectTypeDesc === '标签' ? '成员标签' : item.objectTypeDesc) +
						'可用'
					);
				})
				.join(';');
		}
	}
};
</script>
<style lang="scss" scoped>
.spaceStatus {
	display: flex;
	align-items: center;
}
.document-management-pop {
	height: 100%;
	&-title {
		@include verticalStripe();
		display: flex;
		align-items: center;
		height: 18px;
		font-size: 16px;
		font-weight: 800;
		color: $primaryTextColor;
		line-height: 22px;
	}
	&-form {
		&-label {
			line-height: 22px;
		}
		&-Users {
			margin: 9px 0 15px;
			font-size: 14px;
			font-family: PingFang SC, PingFang SC;
			font-weight: 400;
			color: $primaryTextColor;
			line-height: 22px;
			display: flex;
			width: 100%;
			align-items: flex-start;
		}
		&-configuration {
			cursor: pointer;
			margin-left: 8px;
			display: flex;
			align-items: center;
			font-size: 14px;
			font-weight: 400;
			color: var(--brand-6, #0f45ea);
			line-height: 22px;
			flex-shrink: 0;
		}
		&el-checkbox {
			width: 100%;
		}
	}
	& ::v-deep.el-form-item__label {
		font-size: 14px;
		font-weight: 400;
		color: $textColor;
		line-height: 22px;
		padding: 0 0 8px 3px;
		margin: 0;
	}
	& ::v-deep.el-input__inner {
		width: 100%;
		border-radius: 6px;
		border: 1px solid $borderColor;
		height: 40px;
		background: #ffffff;
		opacity: 1;
	}
	& ::v-deep.el-select {
		width: 100%;
		border-radius: 6px;
		height: 40px;
		background: #ffffff;
		opacity: 1;
	}
	& ::v-deep.el-form-item {
		margin: 16px 0 0;
		font-size: 14px;
		font-weight: 400;
		color: $textColor;
		line-height: 22px;
	}
	& ::v-deep.el-checkbox__label {
		height: 32px;
		padding: 9px 0 9px 10px;
		font-size: 14px;
		font-family: PingFang SC, PingFang SC;
		font-weight: 400;
		color: $textColor;
	}
	& ::v-deep .is-required .el-form-item__label::after {
		content: '*';
		color: #ff0000;
		margin-left: 4px;
	}
	& ::v-deep .el-form-item.is-success {
		.el-input__inner,
		.el-input__inner:focus,
		.el-textarea__inner,
		.el-textarea__inner:focus {
			border-color: $borderColor;
		}
	}
}
</style>
