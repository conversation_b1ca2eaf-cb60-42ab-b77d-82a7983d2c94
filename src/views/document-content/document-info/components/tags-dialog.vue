<template>
	<el-dialog title="设置文档标签" :visible.sync="dialogVisible" width="60%" :before-close="close">
		<div>
			<el-tag
				v-for="tag in dynamicTags"
				:key="tag"
				closable
				:disable-transitions="false"
				@close="handleClose(tag)"
			>
				{{ tag }}
			</el-tag>
			<el-input
				v-if="inputVisible"
				ref="saveTagInput"
				v-model="inputValue"
				class="input-new-tag"
				size="small"
				@keyup.enter.native="handleInputConfirm"
				@blur="handleInputConfirm"
			></el-input>
			<el-button v-else class="button-new-tag" size="small" @click="showInput">+ New Tag</el-button>
		</div>
		<span slot="footer" class="dialog-footer">
			<el-button @click="close">取 消</el-button>
			<el-button type="primary" @click="addTags">确 定</el-button>
		</span>
	</el-dialog>
</template>

<script>
import { setLabel } from '@/api/modules/document-content';

export default {
	name: 'TagsDialog',
	data() {
		return {
			dialogVisible: false,
			dynamicTags: [],
			inputVisible: false,
			row: {},
			inputValue: ''
		};
	},
	methods: {
		open(data) {
			this.row = data;
			if (data.label) {
				this.dynamicTags = data.label.split(',');
			}
			this.dialogVisible = true;
		},
		addTags() {
			let data = {
				id: this.row.id,
				label: this.dynamicTags.join(',')
			};
			setLabel(data).then(res => {
				if (res.code === 200) {
					this.$message.success('添加成功');
					this.$emit('refreshList', this.row);
					this.close();
				} else {
					this.$message.warning(res.message);
				}
			});
		},
		close() {
			this.dynamicTags = [];
			this.dialogVisible = false;
		},
		handleClose(tag) {
			this.dynamicTags.splice(this.dynamicTags.indexOf(tag), 1);
		},

		showInput() {
			this.inputVisible = true;
			this.$nextTick(_ => {
				this.$refs.saveTagInput.$refs.input.focus();
			});
		},
		handleInputConfirm() {
			let inputValue = this.inputValue.trim(); // 去除首尾空格
			if (inputValue) {
				// 如果 dynamicTags 中不存在该标签，则添加
				if (!this.dynamicTags.includes(inputValue)) {
					this.dynamicTags.push(inputValue);
				} else {
					this.$message.warning('标签已存在');
				}
			}
			this.inputVisible = false;
			this.inputValue = '';
		}
	}
};
</script>

<style scoped lang="scss">
.el-tag + .el-tag {
	margin-left: 10px;
}
.button-new-tag {
	margin-left: 10px;
	height: 32px;
	line-height: 30px;
	padding-top: 0;
	padding-bottom: 0;
}
.input-new-tag {
	width: 190px;
	margin-left: 10px;
	vertical-align: bottom;
}
::v-deep .el-dialog__body {
	padding-top: 6px;
}
</style>
