<template>
	<div class="knowledge" :class="isEnter ? 'knowledge-p' : ''">
		<div v-if="isEnter" class="knowledge-main">
			<div class="search-content">
				<el-form
					ref="searchForm"
					class="searchForm desk-el-form"
					label-suffix=":"
					:model="searchForm"
					label-width="90px"
				>
					<el-row>
						<el-col :span="12">
							<el-form-item prop="spaceName" label="空间名字">
								<el-input
									v-model="searchForm.spaceName"
									class="search-input"
									clearable
									placeholder="请输入空间名字"
								></el-input>
							</el-form-item>
						</el-col>
						<el-col v-if="showLabel" :span="12">
							<el-form-item prop="spaceCategory" label="空间标签">
								<el-select
									v-model="searchForm.spaceCategory"
									class="search-input"
									clearable
									placeholder="请选择标签"
								>
									<el-option
										v-for="(item, index) of tagOptions"
										:key="index"
										:label="item.label"
										:value="item.value"
									></el-option>
								</el-select>
							</el-form-item>
						</el-col>
					</el-row>
				</el-form>
				<div class="search-buttons">
					<div class="button-search" @click="search">查询</div>
					<div class="button-reset" @click="reset">重置</div>
				</div>
			</div>
			<div class="table-content">
				<el-table
					v-loading="tableLoading"
					class="knowledge-main-table"
					:data="tableData"
					height="100%"
					style="width: 100%"
					:header-cell-style="headerCellStyle"
				>
					<el-table-column label="空间名称" width="250px">
						<template slot-scope="{ row }">
							<div class="spaceName">
								<svg-icon
									icon-class="cloud"
									style="color: var(--brand-6); flex-shrink: 0"
								></svg-icon>
								<span>{{ row.spaceName }}</span>
								<div v-if="row.isTop" class="topping">置顶</div>
							</div>
						</template>
					</el-table-column>
					<el-table-column prop="size" label="空间容量" width="200px">
						<template slot-scope="{ row }">
							<el-progress
								ref="progressRef"
								class="spaceProgress"
								:text-inside="true"
								:format="percentage => format(percentage, row.proportion)"
								:stroke-width="22"
								:percentage="row.percentage >= 100 ? 100 : row.percentage"
								:class="{ waring: row.percentage >= 90, zero: row.percentage < 90 }"
								style="width: 154px; height: 22px"
							></el-progress>
						</template>
					</el-table-column>
					<el-table-column
						prop="spaceCategoryName"
						label="空间标签"
						min-width="160"
						show-overflow-tooltip
					></el-table-column>
					<el-table-column label="空间管理员" min-width="160" show-overflow-tooltip>
						<template slot-scope="{ row }">
							<div class="admin" style="margin-right: 30px">{{ row.admin }}</div>
						</template>
					</el-table-column>

					<el-table-column label="操作" fixed="right" width="200">
						<template slot-scope="{ row }">
							<el-button type="text" class="handleBut" @click="enterSpace(row.spaceName, row.id)">
								进入空间
							</el-button>
							<el-button v-if="row.isAdmin" type="text" class="handleBut" @click="editClick(row)">
								设置
							</el-button>
						</template>
					</el-table-column>
					<template slot="empty">
						<BasicEmpty :loading="tableLoading" :data="tableData" name="no-data" />
					</template>
				</el-table>
			</div>
		</div>
		<detailList
			v-else
			ref="detailList"
			style="height: 100%"
			:space-data="spaceData"
			:space-loading="spaceLoading"
			:is-administrator="isAdministrator"
			:space-id="spaceId"
			:choose-index="chooseIndex"
			:is-create="isCreate"
			:new-files="newFiles"
			:check-down="checkDown"
			v-bind="$attrs"
			:type="'space'"
			@getDetailList="getDetailList"
			@getDocumentList="resetSpaceData"
			@getBreadName="getBreadName"
			@changeLoading="changeLoading"
			@newFolder="newFolder"
			@cancleFolder="cancleFolder"
			@moreData="moreData"
			@handleEdit="handleEdit"
			@detailsFile="detailsFile"
			v-on="$listeners"
		></detailList>
		<el-dialog
			title="设置"
			:visible.sync="dialogVisible"
			class="custom-el-dialog-footer"
			append-to-body
			width="960px"
			top="8vh"
		>
			<documentManagementPop ref="documentManagementPop" :form-data="formData" />
			<span slot="footer" class="dialog-footer">
				<el-button @click="dialogVisible = false">取 消</el-button>
				<el-button v-loading="saveLoading" type="primary" @click="saveHandleclick">
					保存空间
				</el-button>
			</span>
		</el-dialog>
	</div>
</template>

<script>
import detailList from '@/views/document-content/document-info/detail-list/index.vue';
import documentManagementPop from '@/views/document-content/document-info/components/document-management-pop.vue';
import {
	knowledgeList,
	documentList,
	adminAuthority,
	putEditUsersList
} from '@/api/modules/document-content';
import { tabSize, debounce, deepClone } from '@/utils';
export default {
	components: {
		detailList,
		documentManagementPop
	},
	props: {
		// 知识空间是否显示标签筛选
		showLabel: {
			type: Boolean,
			default: () => {
				return true;
			}
		},
		tagOptions: {
			type: Array,
			default: () => {
				return [];
			}
		},
		chooseIndex: {
			type: Number,
			default: 1
		},
		parentId: {
			type: [Number, String],
			default: ''
		},
		folderData: {
			type: Array,
			default: () => {
				return [];
			}
		},
		isFav: {
			type: Boolean,
			default: false
		},
		newFiles: {
			type: Array,
			default: () => []
		}
	},
	data() {
		return {
			tag: '', // 外部标签作为菜单的方式，外部传入的标签
			headerCellStyle: {
				fontSize: '14px',
				color: '#000',
				lineHight: '22px',
				fontWeight: '500'
			},
			searchForm: {
				spaceName: '',
				spaceCategory: ''
			},
			tableData: [], //表格数据
			tableLoading: true, //table表格loading效果
			spaceName: '', //空间名称
			moduleName: '知识库',
			isEnter: true, // 是否初次进入，判断空间列表还是目录列表
			spaceData: [], //空间列表数据
			spaceId: '',
			spaceLoading: true,
			isAdministrator: true, //是否有管理权限
			isCreate: false,
			checkDown: true,
			pageInfo: {
				pageNo: 1,
				pageSize: 50,
				spaceId: '',
				spaceType: true,
				parentId: 0
			},
			dialogVisible: false,
			saveLoading: false,
			formData: {},
			totalSize: 0
		};
	},
	watch: {
		folderData: {
			handler(newVal) {
				this.isEnter = false;
				this.$nextTick(() => {
					this.$refs.detailList.breadNum = 2;
					this.$refs.detailList.currentId = this.parentId;
					this.$refs.detailList.FolderData = newVal;
				});
			}
		}
	},
	mounted() {
		// if(this.isEnter=true){
		//   this.getTableList();
		// }
		if (this.isFav) {
			this.isEnter = false;
			this.$nextTick(() => {
				this.$refs.detailList.FolderData = this.folderData;
				this.$refs.detailList.breadNum = 2;
				this.$refs.detailList.currentId = this.parentId;
			});
		}
		// this.$nextTick(()=>{
		//   this.$refs.detailList.breadNum =2
		// })
		this.spaceLoading = false;
	},
	methods: {
		// 设置知识库我的空间权限
		editClick(row) {
			this.formData = deepClone(row);
			if (this.formData.spaceCategory && this.formData.spaceCategory.length) {
				this.formData.spaceCategory = this.formData?.spaceCategory?.split(',') || '';
			}
			this.formData.limitMemory = this.formData.isUnlimit
				? '不限制'
				: this.formData.limitMemory + 'GB';
			this.dialogVisible = true;
		},
		// 保存 设置知识库我的空间权限
		async saveHandleclick() {
			if (this.saveLoading) return;
			let res = await this.$refs.documentManagementPop.saveHandleclick();
			this.saveLoading = true;
			res.spaceCategory = res.spaceCategory.join(',');
			res.isUnlimit = res.limitMemory === '不限制' ? true : false;
			res.limitMemory = !res.isUnlimit ? Number(res.limitMemory.split('G')[0]) : null;
			await putEditUsersList(res);
			this.$message({
				message: '编辑成功',
				type: 'success'
			});
			this.getTableList();
			this.dialogVisible = false;
			this.saveLoading = false;
		},
		// 编辑功能
		handleEdit(row) {
			if (row.infoId) {
				this.$emit('handleEdit', row);
			} else {
				this.$message.warning('当前数据禁止编辑');
			}
		},
		detailsFile(row) {
			this.$emit('detailsFile', row);
		},
		search: debounce(
			function () {
				this.getTableList();
			},
			500,
			true
		),
		reset() {
			this.$refs.searchForm.resetFields();
			this.getTableList();
		},
		moreData(id) {
			if (this.totalSize > this.spaceData.length) {
				// this.pageInfo.pageSize += 100;
				this.pageInfo.pageNo++;
				this.getDocumentList(id);
			}
		},
		// 返回列表
		backList(id, spaceId) {
			this.$refs.detailList.enterFloder({
				id,
				fileType: 'folder',
				spaceType: true,
				spaceId
			});
		},
		cancleFolder() {
			this.isCreate = false;
		},
		// 新建文件夹
		newFolder() {
			this.isCreate = true;
		},
		// 获取面包屑名称
		getBreadName(breadName, id) {
			this.$emit('getBreadName', breadName, id);
		},
		changeLoading(val) {
			this.spaceLoading = val;
		},
		/**重置空间数据的请求*/
		resetSpaceData(id) {
			this.spaceData = [];
			this.pageInfo.pageNo = 1;
			this.getDocumentList(id);
		},
		// 获取列表数据
		getDocumentList(id) {
			this.pageInfo.spaceId = id;
			this.spaceLoading = true;
			// this.spaceData = [];
			documentList(this.pageInfo).then(res => {
				this.$refs.detailList.breadNum = 1;
				this.totalSize = res.result.total;
				this.$emit('updateTotal', this.totalSize);
				this.spaceData = this.spaceData.concat(
					res.result.records.map(item => {
						item.orgSize = item.fileSize;
						item.fileSize = tabSize(item.fileSize);
						return item;
					})
				);
				this.spaceLoading = false;
			});
		},
		// 板块名称
		// 进入空间
		enterSpace(spaceName, id) {
			this.pageInfo.pageNo = 1;
			this.spaceData = [];
			this.spaceLoading = true;
			this.$emit('getSpaceName', spaceName, id);
			this.spaceId = id;
			this.getDocumentList(id);
			// 是否有权限操作
			adminAuthority({ spaceId: id }).then(res => {
				this.isAdministrator = res.result;
				this.$emit('changeShowBut', this.isAdministrator);
			});
			this.isEnter = false;
		},
		// 获取空间 数据
		getDetailList(id) {
			this.spaceLoading = true;
			// this.$emit("update:parenId",val)
			documentList({ spaceId: id, spaceType: true, parentId: 0 }).then(res => {
				this.spaceData = res.result.records;
				this.spaceData.forEach(item => {
					item.orgSize = item.fileSize;
					item.fileSize = tabSize(item.fileSize);
				});
				this.spaceLoading = false;
			});
		},

		format(percentage, size) {
			return `${size}`;
		},
		/**路由赋值，搜索数据*/
		searchList({ spaceName = '', spaceCategory = '' }, tag = '') {
			this.searchForm.spaceName = spaceName;
			this.searchForm.spaceCategory = spaceCategory;
			this.tag = tag;
			this.isEnter = true;
			this.getTableList();
		},
		/**外部tag模式，赋值调用搜索*/
		/**获取列表数据*/
		getTableList() {
			this.tableLoading = true;
			knowledgeList({
				spaceName: this.searchForm.spaceName ? '*' + this.searchForm.spaceName + '*' : '',
				spaceCategory:
					this.tag || this.searchForm.spaceCategory
						? '*' + (this.tag || this.searchForm.spaceCategory) + '*'
						: '',
				pageSize: -1
			}).then(res => {
				let result = res.result.records;
				this.$emit('updateTotal', res.result.total || result.length);
				// let count = 1;
				this.tableData = result.map(item => {
					let admin = '';
					let percentage = isNaN(item.usedMemory / item.limitMemory)
						? 0
						: item.usedMemory / item.limitMemory;
					let proportion = item.usedMemory + 'GB' + '/' + item.limitMemory + 'GB';
					item.adminObjectNames.forEach((item1, index) => {
						admin +=
							item1.objectNames.join('、') +
							(item1.objectCount > 2 ? '等' : '') +
							item1.objectCount +
							(item1.objectTypeDesc == '成员' ? '名' : '个') +
							item1.objectTypeDesc;
						if (index + 1 != item.adminObjectNames.length) {
							admin += '；';
						}
					});
					// count++;
					item.percentage = percentage * 100;
					item.proportion = proportion;
					if (item.isUnlimit) {
						item.proportion = '容量无限制';
						item.percentage = 0;
					} else {
						item.percentage = percentage * 100;
						item.proportion = proportion;
					}
					item.admin = admin;
					return item;
				});
				this.tableLoading = false;
			});
		}
	}
};
</script>

<style lang="scss" scoped>
::v-deep .el-table__header-wrapper {
	border-bottom: 1px solid #f0f0f0;
}
::v-deep.el-table td,
.el-table th.is-leaf {
	border-bottom: 1px solid #f0f0f0;
}
.admin {
	@include aLineEllipse;
}
.knowledge-p {
	padding: 12px 18px;
}
.knowledge {
	flex: 1;
	overflow: hidden;
	.knowledge-main {
		height: 100%;
		display: flex;
		flex-direction: column;
		.table-content {
			flex: 1;
			overflow: hidden;
		}
		&-table {
			::v-deep .el-table__body-wrapper {
				@include scrollBar;
				&::-webkit-scrollbar {
					height: 8px;
				}
			}
		}
		::v-deep .el-form-item__label {
			font-weight: 400;
			font-size: 14px;
			color: rgba(0, 0, 0, 0.6);
			line-height: 40px;
		}
	}
	&-main {
		border-radius: 9px;
		padding: 12px 12px 24px 12px;
		border: 1px solid #f0f0f0;
		.spaceName {
			display: flex;
			align-items: center;
			span {
				margin-left: 8px;
				line-height: 22px;
				font-size: 14px;
				color: $textColor;
				@include aLineEllipse;
			}
			.topping {
				width: 34px;
				height: 20px;
				background: #fff6df;
				border-radius: 3px;
				line-height: 20px;
				text-align: center;
				color: #ad7a04;
				font-size: 12px;
				margin-left: 8px;
			}
		}
		.handleBut {
			font-size: 14px;
			color: var(--brand-6);
			line-height: 22px;
		}
	}
}

.spaceProgress {
	::v-deep .el-progress-bar__inner {
		text-align: left !important;
		background: linear-gradient(90deg, #467de6 0%, #4ca9ff 100%) !important;
		line-height: 20px !important;
	}
}
.waring {
	::v-deep .el-progress-bar__inner {
		text-align: left !important;
		background: linear-gradient(90deg, #ff4c4c 0%, #ff9c7c 100%) !important;
		line-height: 20px !important;
	}
}

.zero {
	::v-deep .el-progress-bar__innerText {
		color: #b0d1ff;
	}
}
.search-content {
	display: flex;
	border-bottom: 1px solid #f0f0f0;
}
.desk-el-form {
	::v-deep .el-input__inner {
		height: 32px !important;
	}
}
.searchForm {
	flex: 1;
}
.search-input {
	width: 100%;
}
.search-buttons {
	height: 40px;
	width: 188px;
	@include flexBox(flex-end);
	.button-search {
		width: 68px;
		height: 32px;
		background: #ffffff;
		border-radius: 6px;
		border: 1px solid var(--brand-6);
		font-weight: 400;
		font-size: 14px;
		color: var(--brand-6);
		line-height: 22px;
		margin-right: 4px;
		cursor: pointer;
		@include flexBox();
	}
	.button-reset {
		width: 68px;
		height: 32px;
		background: #ffffff;
		border-radius: 6px;
		border: 1px solid $borderColor;
		font-weight: 400;
		font-size: 14px;
		color: $textColor;
		line-height: 22px;
		cursor: pointer;
		@include flexBox();
	}
}
</style>
