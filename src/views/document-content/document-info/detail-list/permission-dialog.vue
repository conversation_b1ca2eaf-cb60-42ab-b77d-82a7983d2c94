<template>
	<div>
		<el-dialog title="权限管理" :visible.sync="dialogVisible" width="80%">
			<div class="top-items">
				<div class="search">
					<span>关键字：</span>
					<div>
						<el-input
							v-model="pageInfo.objectName"
							placeholder="请输入关键字"
							max-length="20"
							clearable
							@clear="search"
						></el-input>
					</div>
					<span class="btn" @click="search">查询</span>
				</div>
				<div class="btn add-permission" @click="addPermission">
					<i class="el-icon-plus"></i>
					添加权限
				</div>
			</div>
			<div class="table">
				<el-table ref="table" :loading="isLoading" width="100%" height="400" :data="tableData">
					<el-table-column label="序号" width="50">
						<template slot-scope="scope">
							{{ scope.$index + 1 }}
						</template>
					</el-table-column>
					<el-table-column label="对象" width="300" prop="objectNames">
						<template slot-scope="scope">
							<div class="persons">
								<div v-if="scope.row.isAdd" class="add-box">
									<span v-if="scope.row.isAll">全员可用</span>
									<template v-else>
										<el-tooltip
											class="item"
											effect="dark"
											:content="DataProcess2(scope.row.adds)"
											placement="top"
										>
											<span>
												{{
													DataProcess2(scope.row.adds).length > 15
														? DataProcess2(scope.row.adds).slice(0, 15) + '...'
														: DataProcess2(scope.row.adds)
												}}
											</span>
										</el-tooltip>
									</template>
									<div class="set" @click="setRange(scope.row, scope.$index)">配置</div>
								</div>
								<div v-else-if="scope.row.isEdit" class="add-box">
									<span v-if="scope.row.isAll">全员可用</span>
									<template v-else>
										<el-tooltip
											class="item"
											effect="dark"
											:content="DataProcess2(scope.row.adds)"
											placement="top"
										>
											<span>
												{{
													DataProcess2(scope.row.adds).length > 15
														? DataProcess2(scope.row.adds).slice(0, 15) + '...'
														: DataProcess2(scope.row.adds)
												}}
											</span>
										</el-tooltip>
									</template>
									<div class="set" @click="setRange(scope.row, scope.$index)">配置</div>
								</div>
								<div v-else>
									<span v-if="scope.row.isAll">全员可用</span>
									<template v-else>
										<el-tooltip
											class="item"
											effect="dark"
											:content="DataProcess(scope.row.objectNames)"
											placement="top"
										>
											<span>
												{{
													DataProcess(scope.row.objectNames).length > 15
														? DataProcess(scope.row.objectNames).slice(0, 15) + '...'
														: DataProcess(scope.row.objectNames)
												}}
											</span>
										</el-tooltip>
									</template>
								</div>
							</div>
						</template>
					</el-table-column>
					<el-table-column label="权限" width="240" prop="authorityType">
						<template slot-scope="{ row }">
							<div v-if="row.isAdd || row.isEdit" class="selectBox">
								<el-checkbox-group
									v-model="row.authorityType"
									@change="authorityTypeChange($event, row)"
								>
									<el-checkbox label="1">编辑</el-checkbox>
									<el-checkbox label="2">查看</el-checkbox>
									<el-checkbox label="3">下载</el-checkbox>
								</el-checkbox-group>
							</div>
							<div v-else class="">
								{{ reubildType(row.authorityType) }}
							</div>
						</template>
					</el-table-column>
					<el-table-column label="创建人/来源" min-width="150" prop="createByName">
						<template slot-scope="{ row }">
							<div v-if="row.createByName" class="authority-body-tableUser-type">
								<FileById
									:size="18"
									:value="row.avatarUrl"
									:more-style="{ borderRadius: '3px', marginLeft: '1px' }"
									:default-font-icon="row.createByName.slice(-1)"
									class="authority-AddRoleMember-img"
								></FileById>

								<div :style="{ width: row.createByName.length * 14 + 'px' }">
									{{ row.createByName }}
								</div>
							</div>
						</template>
					</el-table-column>
					<el-table-column label="创建时间" width="160" prop="createTime"></el-table-column>
					<el-table-column label="更新人" width="100" prop="updateByName"></el-table-column>
					<el-table-column label="更新时间" width="160" prop="updateTime"></el-table-column>
					<el-table-column label="操作" width="160">
						<template slot-scope="scope">
							<template v-if="scope.row.isEdit || scope.row.isAdd">
								<el-button type="text" @click="handleEdit(scope.row, 'save')">保存</el-button>
							</template>
							<el-button v-else type="text" @click="handleEdit(scope.row, 'edit', scope.$index)">
								编辑
							</el-button>
							<el-button
								v-if="scope.row.isEdit"
								type="text"
								@click="handleEdit(scope.row, 'cancel')"
							>
								取消
							</el-button>
							<el-button type="text" @click="handleDelete(scope.row, scope.$index)">删除</el-button>
						</template>
					</el-table-column>
				</el-table>
			</div>
			<span slot="footer" class="dialog-footer">
				<el-button @click="dialogVisible = false">关 闭</el-button>
			</span>
		</el-dialog>

		<orgPersonnelDialog
			:visible="selectVisible"
			:init-params="initParams"
			:init-values="initValues"
			need-all-data
			:is-all="formDetails.isAll"
			@sure="sure"
			@close="close"
		></orgPersonnelDialog>
	</div>
</template>

<script>
import {
	authoritySearch,
	authoritySet,
	authorityDelete,
	authorityEdit,
	authorityObjectsSearch
} from '@/api/modules/document-content';
import orgPersonnelDialog from '@/components/org-personnel-dialog';
export default {
	components: { orgPersonnelDialog },
	props: {
		targetInfo: {
			type: Object,
			default: () => {}
		}
	},
	data() {
		return {
			dialogVisible: false,
			pageInfo: {
				pageNo: 1,
				pageSize: 100,
				documentId: '',
				objectName: ''
			},
			isLoading: true,
			headerCellStyle: {
				color: '#000',
				fontSize: '14px',
				fontWeight: '500',
				height: '54px'
			},
			tableData: [],
			isAdd: false,
			isAll: false,
			authorityType: [],
			formDetails: { isAll: false },
			selectVisible: false,
			formId: null,
			currRowIndex: null,
			initValues: [],
			initParams: {
				authorityType: 1, //类型;1.可用成员范围,2.禁止使用范围
				applicationId: ''
			},
			submitting: false,
			isEdit: false
		};
	},
	watch: {
		dialogVisible: {
			handler(newVal) {
				if (newVal) {
					this.getList();
				} else {
					this.pageInfo.objectName = '';
					this.isEdit = false;
					this.isAdd = false;
					this.tableData = [];
				}
			},
			immediate: true
		}
	},
	mounted() {},
	methods: {
		handleEdit(row, type, index) {
			// console.log(row);

			// 点击编辑时候 是否有数据在 新增 如果有 那么就提示只能操作一条
			if (type == 'edit' && this.isAdd) {
				this.$message.info('只能同时编辑/新增一条数据!');
				return;
			}
			// 如果点击编辑,有数据正在编辑,并且不是当前数据 也不能编辑
			if (type == 'edit' && this.isEdit && !row.isEdit) {
				this.$message.info('只能同时编辑/新增一条数据!');
				return;
			}

			if (type == 'save') {
				const { documentId, isAll, isFolder, objectList, authorityType, id } = row;
				// 如果没有选择 全部成员 或者 成员选择为空
				if (!isAll && objectList.length == 0) {
					this.$message.error('请选择对象!');
					return;
				}
				if (authorityType.length == 0) {
					this.$message.error('至少选择一种权限!');
					return;
				}
				if (this.submitting) return;
				this.submitting = true;
				// 如果有id 就是编辑
				const loading = this.$loading({
					lock: true,
					text: '数据提交中',
					spinner: 'el-icon-loading',
					background: 'rgba(0, 0, 0, 0.7)'
				});
				if (id) {
					authorityEdit(id, {
						documentId,
						isAll,
						isFolder,
						objectList,
						authorityType: authorityType.sort().join()
					}).then(res => {
						this.submitting = false;
						loading.close();
						if (res.code == 200) {
							this.$message.success('保存成功!');
							this.isEdit = false;
							this.getList();
						} else {
							this.$message.error(res.message);
						}
					});
				} else {
					authoritySet({
						documentId,
						isAll,
						isFolder,
						objectList,
						authorityType: authorityType.sort().join()
					}).then(res => {
						this.submitting = false;
						loading.close();
						if (res.code == 200) {
							this.$message.success('保存成功!');
							this.isAdd = false;
							this.getList();
						} else {
							this.$message.error(res.message);
						}
					});
				}
			} else if (type == 'cancel') {
				row.isEdit = false;
				this.isEdit = false;
			} else {
				row.isEdit = true;
				this.isEdit = true;
				authorityObjectsSearch({ authorityId: row.id }).then(res => {
					if (res.code == 200) {
						const adds = {};
						const objectList = [];
						res.result.map(v => {
							if (adds[v.dataType]) {
								adds[v.dataType].push(v);
							} else {
								adds[v.dataType] = [];
								adds[v.dataType].push(v);
							}
							objectList.push({
								objectId: v.id,
								objectType: v.dataType === 'depart' ? 1 : v.dataType === 'label' ? 3 : 2
							});
						});
						if (row.authorityType instanceof Array) {
							// row.authorityType = row.authorityType;
						} else {
							row.authorityType = row.authorityType.split(',');
						}
						row.objectList = [...objectList];
						row.addSelected = [...res.result];
						row.adds = { ...adds };
					}
				});
			}
		},
		// 删除
		handleDelete(row, index) {
			if (row.isAdd) {
				const tableData = [...this.tableData];
				tableData.splice(index, 1);
				this.tableData = [...tableData];
				this.isAdd = false;
			} else {
				authorityDelete(row.id).then(res => {
					if (res.code == 200) {
						const tableData = [...this.tableData];
						tableData.splice(index, 1);
						this.tableData = [...tableData];
						this.$message.success('数据已删除!');
						if (row.isEdit) {
							this.isEdit = false;
						}
					} else {
						this.$message.error(res.message);
					}
				});
				//就是 直接删除一条数据
			}
		},
		addPermission() {
			if (this.isAdd || this.isEdit) {
				this.$message.info('只能同时编辑一条数据!');
			} else {
				this.isAdd = true;
				let addData = {
					authorityType: [],
					documentId: this.targetInfo.id,
					isAll: false,
					isFolder: this.targetInfo.isFolder,
					objectList: [],
					objectNames: [],
					adds: {},
					addSelected: [],
					isAdd: true
				};
				this.tableData.push(addData);
			}
		},
		//获取文档列表
		getList() {
			// console.log('this.targetInfo:', this.targetInfo);
			this.pageInfo.documentId = this.targetInfo.id;
			authoritySearch(this.pageInfo).then(res => {
				if (res.code == 200) {
					this.tableData = res.result.records.map(v => {
						v.isEdit = false;
						v.adds = {};
						v.addSelected = [];
						v.objectList = [];
						return v;
					});
					// this.tableData = res.result.records;
					// console.log('res:', this.tableData);
				} else {
					this.$message.error(res.message);
				}
			});
		},

		setRange(row, index) {
			this.formDetails = { ...row };
			this.initValues = this.formDetails.addSelected;
			// this.getFormCustomAuthoritys(this.formId);
			this.currRowIndex = index;
			this.selectVisible = true;
		},
		close() {
			this.rowIndex = null;
			this.selectVisible = false;
			this.formId = null;
		},
		sure(e, bool) {
			const tableData = [...this.tableData];
			this.formDetails.isAll = bool;
			if (!bool) {
				let objectList = e.map(t => {
					return {
						objectId: t.id,
						objectType: t.dataType === 'depart' ? 1 : t.dataType === 'label' ? 3 : 2
					};
				});
				this.formDetails.objectList = [...objectList];
				if (this.isAdd || tableData[this.currRowIndex].isEdit) {
					// 新增的时候 渲染的数据和后台返回的数据不一致 就需要自定义
					let depart = [],
						user = [],
						label = [];
					e.map(t => {
						if (t.dataType == 'depart') {
							depart.push(t);
						}
						if (t.dataType == 'user' || t.dataType == undefined) {
							user.push(t);
						}
						if (t.dataType == 'label') {
							label.push(t);
						}
					});
					this.formDetails.adds = { depart, user, label };
					this.formDetails.addSelected = e;
				}
			} else {
				this.formDetails.objectList = [];
			}

			tableData[this.currRowIndex] = { ...this.formDetails };
			this.tableData = [...tableData];
			// console.log(tableData);
			this.selectVisible = false;
		},

		DataProcess(arr) {
			const names = arr.map(v => {
				let str = v.objectNames.join('、');
				if (v.objectCount > 2) {
					str += '等';
				}
				str += v.objectCount;
				if (v.objectType == 2) {
					str += '名';
				} else {
					str += '个';
				}
				str += v.objectTypeDesc;
				return str;
			});
			return names.join('；');
		},
		DataProcess2(adds) {
			let name = '';
			if (!adds) return '';
			const { user, depart, label } = adds;
			if (user && user.length > 0) {
				user.map((v, i) => {
					if (i < 2) {
						name += v.title;
						if (i == 0 && user.length > 1) {
							name += '、';
						}
					}
				});
				if (user.length > 2) {
					name += `等${user.length}名成员可用;`;
				} else {
					name += `${user.length}名成员可用;`;
				}
			}

			if (depart && depart.length > 0) {
				depart.map((v, i) => {
					if (i < 2) {
						name += v.title;
						if (i == 0 && depart.length > 1) {
							name += '、';
						}
					}
				});
				if (depart.length > 2) {
					name += `等${depart.length}个部门可用;`;
				} else {
					name += `${depart.length}个部门可用;`;
				}
			}
			if (label && label.length > 0) {
				label.map((v, i) => {
					if (i < 2) {
						name += v.title;
						if (i == 0 && label.length > 1) {
							name += '、';
						}
					}
				});
				if (label.length > 2) {
					name += `等${label.length}个标签可用;`;
				} else {
					name += `${label.length}个标签可用;`;
				}
			}

			return name;
		},
		reubildType(type) {
			if (type) {
				let t = [];
				if (type instanceof Array) {
					t = type;
				} else {
					t = type.split(',');
				}

				let name = '';
				// label="2">查看</el-checkbox>
				// 					<el-checkbox label="1">编辑</el-checkbox>
				// 					<el-checkbox label="3">下载
				let auth = { 1: '编辑', 2: '查看', 3: '下载' };
				t.map((v, i) => {
					if (i > 0) {
						name += `、${auth[v]}`;
					} else {
						name += auth[v];
					}
				});
				return name;
			}
		},
		// 有下载必然有查看 有查看不一定有下载
		authorityTypeChange(e, row) {
			let selected = [...e];
			if (e.includes('3')) {
				if (!selected.includes('2')) {
					this.$message.info('当有下载权限时,必须有查看权限!');
					selected.push('2');
					row.authorityType.push('2');
				}
			} else {
				row.authorityType = e;
			}
			this.$forceUpdate();
		},
		search() {
			this.getList();
		}
	}
};
</script>

<style lang="scss" scoped>
.persons,
.add-box {
	display: flex;
	grid-gap: 5px;
}
.set {
	color: var(--brand-6);
	cursor: pointer;
}
::v-deep {
	.el-checkbox {
		margin-right: 20px;
		font-weight: normal;
	}
}
.top-items {
	display: flex;
	justify-content: space-between;
	align-items: center;
	.search {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}
	.btn {
		border: 1px solid var(--brand-6);
		color: var(--brand-6);
		border-radius: 6px;
		padding: 0px 10px;
		line-height: 30px;
		cursor: pointer;
		margin-left: 8px;
	}
}
.authority-body-tableUser-type {
	display: flex;
	align-items: center;
	margin-right: 4px;
	padding: 3px 4px;
}
</style>
