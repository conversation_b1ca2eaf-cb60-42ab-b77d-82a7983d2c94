<template>
	<el-dialog title="移动到" :visible.sync="dialogVisible" width="800px">
		<div class="tabbar">
			<div v-if="pageInfo.parentId == 0">全部文件</div>
			<div v-else class="breadcrumb">
				<div>
					<span class="tabbarSpan span1" @click="toBack()">返回上一级</span>
					<span class="tabbarSpan" @click="toAll">
						全部文件
						<span style="color: #c4d8f4; margin-right: 4px">></span>
					</span>
				</div>
				<el-breadcrumb separator-class="el-icon-arrow-right">
					<el-breadcrumb-item v-for="item in breadName" :key="item">{{ item }}</el-breadcrumb-item>
				</el-breadcrumb>
			</div>
		</div>
		<div v-if="listInfo.length" v-loading="isLoading" class="floderList">
			<div v-for="item in listInfo" :key="item.id" class="folderItem" @click="handleChoose(item)">
				<svg-icon icon-class="folder" class="icon"></svg-icon>
				<div class="name">{{ item.name }}</div>
			</div>
		</div>
		<div v-else class="floderList empty">
			<span>移动到此文件夹</span>
		</div>
		<span slot="footer" class="dialog-footer">
			<el-button @click="dialogVisible = false">取 消</el-button>
			<el-button type="primary" @click="sureRemove()">移动到此</el-button>
		</span>
	</el-dialog>
</template>

<script>
import { floderList, isTopping, isFileTopping } from '@/api/modules/document-content';
export default {
	props: {
		targetInfo: {
			type: Object,
			default: () => {}
		}
	},
	data() {
		return {
			dialogVisible: false,
			pageInfo: {
				pageNo: 1,
				pageSize: 100,
				spaceType: false,
				parentId: 0
			},
			listInfo: [],
			isLoading: true,
			parentId: [], //用于返回上一级
			breadName: []
		};
	},
	watch: {
		dialogVisible: {
			handler(newVal) {
				if (newVal) {
					const { spaceId } = this.targetInfo;
					if (spaceId) {
						this.pageInfo = {
							pageNo: 1,
							pageSize: 100,
							spaceId: spaceId,
							spaceType: true,
							parentId: 0
						};
					} else {
						this.pageInfo = { pageNo: 1, pageSize: 100, spaceType: false, parentId: 0 };
					}
					this.parentId = [];
					this.breadName = [];
					this.getList();
				}
			},
			immediate: true
		}
	},
	methods: {
		//获取文档列表
		getList() {
			this.isLoading = true;
			floderList(this.pageInfo).then(res => {
				this.listInfo = res.result.records;
				this.isLoading = false;
			});
		},
		//获取某个文件夹下的文档列表
		handleChoose(item) {
			const { id } = item;
			this.parentId.push(item.parentId); // 用户存储上一级id
			this.breadName.push(item.name);
			this.pageInfo.parentId = id;
			this.getList();
		},
		// 返回上一级
		toBack() {
			this.pageInfo.parentId = this.parentId[this.parentId.length - 1];
			this.parentId.pop();
			this.breadName.pop();
			if (this.pageInfo.parentId == 0) {
				this.parentId = [];
				this.breadName = [];
			}
			this.getList();
		},
		//返回全部
		toAll() {
			this.parentId = [];
			this.breadName = [];
			this.pageInfo.parentId = 0;
			this.getList();
		},
		//文件/文件夹移动
		sureRemove() {
			console.log(this.targetInfo);
			const { parentId, spaceType, spaceId } = this.targetInfo;
			if (this.targetInfo.isFolder) {
				isTopping(this.targetInfo.id, { parentId: this.pageInfo.parentId }).then(res => {
					if (res.code == 500) {
						this.$message.warning(res.message);
					} else {
						this.$message.success('移动成功');
						//this.getList();
						this.dialogVisible = false;
						this.$emit('getFolderData', parentId, spaceType, spaceId);
					}
				});
			} else {
				isFileTopping(this.targetInfo.id, { parentId: this.pageInfo.parentId }).then(res => {
					if (res.code == 500) {
						this.$message.warning(res.message);
					} else {
						this.$message.success('移动成功');
						this.dialogVisible = false;
						this.$emit('getFolderData', parentId, spaceType, spaceId);
					}
				});
			}
		}
	}
};
</script>

<style lang="scss" scoped>
.tabbar {
	background: #fafafc;
	padding: 10px 20px;
	color: #afb3bf;
	line-height: 40px;
	.breadcrumb {
		display: flex;
		align-items: center;
	}
}
.tabbarSpan {
	color: var(--brand-6);
	cursor: pointer;
}
.span1 {
	padding-right: 10px;
	border-right: 2px solid #c4d8f4;
	margin-right: 10px;
}
::v-deep .el-dialog__title {
	font-weight: 600;
}
::v-deep .el-dialog__body {
	padding: 0px;
}
.floderList {
	height: 500px;
	overflow-y: auto;
}
.empty {
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 20px;
	color: #afb3bf;
}
.folderItem {
	display: flex;
	align-items: center;
	cursor: pointer;
	border-radius: 6px;
	padding: 10px 30px;
	font-size: 16px;
	margin-bottom: 8px;
	.icon {
		margin-right: 16px;
		width: 25px;
	}
	.name {
		margin-top: 3px;
	}
}
.folderItem:hover {
	background: #f5f7fa;
}
</style>
