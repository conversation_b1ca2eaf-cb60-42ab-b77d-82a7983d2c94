<template>
	<div ref="scrollContainer" class="detailList" @scroll="handleScroll">
		<!-- v-loading="spaceLoading" -->
		<div v-loading="tableData.length < 10 && spaceLoading" style="position: relative">
			<div class="detailInfo dimension">
				<div class="floderName dimension">名称</div>
				<div class="line" @mousedown="mousedown"></div>
				<div class="createBy dimension" :style="{ width: createWidth + 'px' }">创建者</div>
				<div class="updateTime">最近更新</div>
				<div style="width: 120px">文件大小</div>
			</div>
			<div v-if="isCreate" class="newItem">
				<div class="createName">
					<el-input
						ref="newInput"
						v-model="newFloderName"
						placeholder="新建文件夹"
						style="width: 200px"
						@keyup.enter.native="handleFolder"
					></el-input>
					<!-- 		@blur="cancleFocus" -->
					<el-button
						type="primary"
						class="newFolderButton"
						icon="el-icon-check"
						@click="handleFolder"
					></el-button>
					<el-button
						type="primary"
						class="newFolderButton"
						icon="el-icon-close"
						@click="cancleFocus"
					></el-button>
				</div>
			</div>
			<div v-if="tableData.length">
				<div
					v-for="item in tableData"
					:key="item.id"
					class="detailInfo"
					:class="{ 'select-file': selectFileId === item.id }"
					:data-id="item.id"
				>
					<div class="detailItem" :class="{ whichOne: item.id == whichId }">
						<div class="floderName">
							<div
								class="floderName-left"
								:class="{ cancleClick: [2, 3, 4].includes(chooseIndex) }"
							>
								<i
									v-show="loadId === item.id && fileLoading"
									class="el-icon-loading"
									style="color: var(--brand-6)"
								></i>
								<svg-icon
									v-if="iconType.includes(item.fileType)"
									class="fileTypes"
									:icon-class="item.fileType"
									@click="enterFloder(item)"
								></svg-icon>
								<el-popover
									v-if="!(resetName.id == item.id)"
									placement="top-start"
									trigger="hover"
									:content="item.name"
									:open-delay="500"
									popper-class="customPopper"
								>
									<span slot="reference" @click="enterFloder(item)">
										{{ item.title || item.name }}
									</span>
								</el-popover>
								<div v-else>
									<el-input ref="inputRef" v-model="resetName.name" style="width: 170px"></el-input>
									<!-- @keyup.enter.native="handleResetName(item.id, item.isFolder)"
                  @blur="sureReset(item.id, item.isFolder)" -->
									<el-button
										type="primary"
										class="newFolderButton"
										icon="el-icon-check"
										@click="
											handleResetName(
												item.id,
												item.isFolder,
												item.parentId,
												item.spaceType,
												item.spaceId
											)
										"
									></el-button>
									<el-button
										type="primary"
										class="newFolderButton"
										icon="el-icon-close"
										@click="sureReset(item.id, item.isFolder)"
									></el-button>
								</div>
								<div v-if="item.label" class="">
									<el-popover
										placement="top-start"
										trigger="hover"
										:content="item.label"
										:open-delay="500"
										popper-class="customPopper"
									>
										<i
											slot="reference"
											class="coos-iconfont icon-biaoqian1 svgIcon interval"
											@click.stop="addTags(item)"
										></i>
									</el-popover>
								</div>
								<div
									v-if="item.fileType !== 'folder' && [0, 1, 2].includes(item.vectorizationStatus)"
									class=""
								>
									<el-popover
										placement="top-start"
										trigger="hover"
										:content="vectorizationStatusText(item)"
										:open-delay="500"
										popper-class="customPopper"
									>
										<i
											slot="reference"
											class="coos-iconfont svgIcon interval"
											:class="vectorizationStatusClass(item)"
										></i>
									</el-popover>
								</div>

								<div v-if="item.isTop && [2, 3].includes(chooseIndex)" class="topping">置顶</div>
								<el-image
									ref="preview"
									class="pre-box"
									:src="imgUrl"
									:preview-src-list="imgUrlList"
								/>
							</div>
							<div v-if="[2, 3, 4].includes(chooseIndex)" class="floderName-right">
								<!-- <el-popover
                  placement="top-start"
                  trigger="hover"
                  content="复制链接"
                  :open-delay="500"
                  popper-class="customPopper"
                >
                  <i
                    v-if="!item.isFolder"
                    slot="reference"
                    icon-class="link"
                    class="coos-iconfont icon-link svgIcon interval"
                    @click="copyUrl(item.fileUrlPath)"
                  ></i>
                </el-popover> -->
								<el-popover
									placement="top-start"
									trigger="hover"
									:content="item.fileType == 'image' ? '预览图片' : '查看摘要'"
									:open-delay="500"
									popper-class="customPopper"
								>
									<i
										v-if="
											['image', 'doc', 'pdf', 'text', 'excel'].includes(item.fileType) &&
											item.spaceType
										"
										slot="reference"
										icon-class="eye"
										class="coos-iconfont icon-yanjing svgIcon interval"
										@click="checkInfo(item.id, item.fileType, item.fileUrlPath)"
									></i>
								</el-popover>
								<i
									v-if="item.canViewText"
									class="coos-iconfont icon-chakanxiangqing details-file-icon"
									@click="detailsFile(item)"
								></i>
								<el-dropdown>
									<span>
										<svg-icon icon-class="more" class="svgIcon moreIcon"></svg-icon>
									</span>
									<el-dropdown-menu slot="dropdown">
										<el-dropdown-item
											v-if="isAdministrator || item.canEdit || chooseIndex == 2"
											@click.native="
												handleTop(
													item.id,
													item.isFolder,
													item.isTop,
													item.parentId,
													item.spaceType,
													item.spaceId
												)
											"
										>
											{{ item.isTop ? '取消置顶' : '置顶' }}
										</el-dropdown-item>
										<el-dropdown-item
											v-if="
												documentConfig.version != 2 &&
												(isAdministrator || item.canEdit || chooseIndex == 2)
											"
											@click.native="handleReset(item.id, item.isFolder, item.name, item.canEdit)"
										>
											重命名
										</el-dropdown-item>
										<!-- getIsDown  -->
										<el-dropdown-item
											v-if="
												([2, 3, 4].includes(chooseIndex) &&
													item.fileType != 'folder' &&
													getIsDown(item)) ||
												chooseIndex == 2
											"
											@click.native="downFolder(item)"
										>
											下载
										</el-dropdown-item>
										<el-dropdown-item
											@click.native="
												handleLike(
													item.id,
													item.isFolder,
													item.isFavorite,
													item.parentId,
													item.spaceType,
													item.spaceId
												)
											"
										>
											{{ item.isFavorite ? '取消收藏' : '收藏' }}
										</el-dropdown-item>
										<el-dropdown-item
											v-if="isAdministrator || item.canEdit || chooseIndex == 2"
											@click.native="openRemove(item)"
										>
											移动
										</el-dropdown-item>
										<template v-if="chooseIndex != 2">
											<el-dropdown-item
												v-if="(isAdministrator && chooseIndex == 3) || item.canEdit"
												@click.native="openPermissionSetting(item)"
											>
												权限设置
											</el-dropdown-item>
										</template>
										<el-dropdown-item
											v-if="
												documentConfig.version == 2 &&
												(isAdministrator || item.canEdit || chooseIndex == 2)
											"
											@click.native="handleEdit(item)"
										>
											编辑
										</el-dropdown-item>
										<el-dropdown-item
											v-if="isAdministrator || item.canEdit || chooseIndex == 2"
											@click.native="
												handleDelete(
													item.id,
													item.isFolder,
													item.parentId,
													item.spaceType,
													item.spaceId
												)
											"
										>
											删除
										</el-dropdown-item>
										<el-dropdown-item
											v-if="
												['doc', 'pdf', 'text', 'excel'].includes(item.fileType) &&
												item.spaceType &&
												(isAdministrator || item.canEdit)
											"
										>
											<div v-if="!item.supplementalFileUrl">
												<uploadFile
													ref="uploadFile"
													:show-loading="true"
													:can-download="false"
													mode="file"
													:limit="1"
													:custom-button="true"
													:custom-file="true"
													:is-preview="false"
													@success="
														successUpload(
															$event,
															item.id,
															item.parentId,
															item.spaceType,
															item.spaceId
														)
													"
												>
													<template #custom-button>上传补充文件</template>
												</uploadFile>
											</div>

											<div
												v-else
												@click="delSupeFile(item.id, item.parentId, item.spaceType, item.spaceId)"
											>
												删除补充文件
											</div>
										</el-dropdown-item>

										<el-dropdown-item
											v-if="item.supplementalFileUrl && (isAdministrator || item.canEdit)"
										>
											<div @click="downloadSuppeFile(item.supplementalFileUrl)">下载补充文件</div>
										</el-dropdown-item>
										<el-dropdown-item
											v-if="item.fileType !== 'folder' && (isAdministrator || item.canEdit)"
										>
											<div @click="addTags(item)">设置文档标签</div>
										</el-dropdown-item>
										<el-dropdown-item
											v-if="
												item.spaceType &&
												['doc', 'pdf', 'text', 'excel'].includes(item.fileType) &&
												item.canEdit
											"
											@click.native="handleSetting(item)"
										>
											配置相关链接
										</el-dropdown-item>
									</el-dropdown-menu>
								</el-dropdown>
							</div>
							<div v-if="chooseIndex == 5" class="floderName-right">
								<el-popover
									placement="top-start"
									trigger="hover"
									content="还原"
									:open-delay="500"
									popper-class="customPopper"
								>
									<i
										slot="reference"
										class="coos-iconfont icon-xiaobenjiaoan svgIcon interval"
										@click="handleTakeBack(item.id, item.isFolder)"
									></i>
								</el-popover>
								<el-popover
									placement="top-start"
									trigger="hover"
									content="删除"
									:open-delay="500"
									popper-class="customPopper"
								>
									<i
										slot="reference"
										class="coos-iconfont icon-trash svgIcon"
										@click="deleteCompletely(item.id, item.isFolder)"
									></i>
								</el-popover>
							</div>
						</div>
						<div class="line" @mousedown="mousedown"></div>
						<div class="createBy" :style="{ width: createWidth + 'px' }">
							<el-popover
								placement="top-start"
								trigger="hover"
								:content="item.createByName"
								:open-delay="500"
								popper-class="customPopper"
							>
								<span slot="reference" class="createName">{{ item.createByName }}</span>
							</el-popover>
						</div>
						<div class="updateTime">
							<!-- <span class="default">最近更新：</span> -->
							<span>{{ item.updateTime ? item.updateTime : item.createTime }}</span>
						</div>
						<div style="width: 120px">
							<!-- <span v-if="item.fileType != 'folder'" class="default">文件大小：</span> -->
							<span v-if="item.fileType != 'folder'" class="createName">{{ item.fileSize }}</span>
						</div>
					</div>
				</div>
				<!--  -->
				<div v-for="(item, index) in newFiles" :key="item.id">
					<div v-if="item.status != 'success'" class="newFile">
						<div class="newFileInfo">
							<svg-icon v-if="iconType.includes(item.url)" :icon-class="item.url"></svg-icon>
							<span style="margin-left: 8px">
								{{ item.name }}
							</span>
						</div>
						<el-progress
							style="flex: 2.7"
							:text-inside="true"
							:stroke-width="20"
							:percentage="parseFloat(item.process)"
							:status="item.status == 'fail' ? 'exception' : 'success'"
						></el-progress>
						<i v-show="item.process != '100%'" class="el-icon-loading"></i>
						<el-button
							class="cancleUpdate"
							type="default"
							icon="el-icon-close"
							circle
							@click="handleCancleUp(index)"
						></el-button>
					</div>
				</div>
				<div v-show="tableData.length > 10" class="load-more">
					<i v-show="spaceLoading" class="el-icon-loading load-more-con"></i>
					<div>{{ spaceLoading ? '--加载中--' : '--没有更多--' }}</div>
				</div>
			</div>
			<div v-else class="ElEmpty">
				<BasicEmpty :loading="spaceLoading" :data="tableData" name="no-data" />
			</div>
		</div>
		<el-card v-if="cardShow" v-loading="cardLoading" class="box-card">
			<div class="box-card-title">
				<div class="card-title">文档摘要</div>
				<div class="closeIcon" @click="cardShow = false">
					<svg-icon icon-class="close"></svg-icon>
				</div>
			</div>
			<div class="card-content">
				<div class="step">
					<div class="stepTitle">{{ fileContent }}</div>
					<div v-if="fileContent == '解析中…'" class="stepItem">
						COOS助手正在解析,若文档页面较多或较大,可能会占用更多时间。
					</div>
				</div>
			</div>
			<el-button class="checkOrigin" @click="viewOrigin()">查看原文</el-button>
		</el-card>
		<div
			v-if="showAnimation"
			class="animation-content"
			:class="
				['1', '4'].includes(clientSystemMode) || showDownLoad
					? 'animation-content1'
					: 'animation-content2'
			"
		>
			<svg-icon
				class="animation-file"
				:class="
					['1', '4'].includes(clientSystemMode) || showDownLoad
						? 'animation-file1'
						: 'animation-file2'
				"
				:icon-class="animationFileType"
			></svg-icon>
		</div>
		<removeDialog
			ref="removeDialogRef"
			:target-info="targetInfo"
			@getFolderData="getFolderData"
		></removeDialog>
		<permission-dialog
			ref="permissionRef"
			:target-info="targetInfo"
			@getFolderData="getFolderData"
		></permission-dialog>
		<HandelSetting ref="handelSetting" @close="close" />
		<tagsDialog ref="tagsDialog" @close="close" @refreshList="refreshList" />
	</div>
</template>

<script>
import {
	adminAuthority,
	cancleFavorite,
	createFolder,
	deleteFile,
	deleteFolder,
	delSuppFile,
	documentList,
	fileDeleteCom,
	fileRetrieve,
	fileSummary,
	folderDeleteCom,
	folderRetrieve,
	getFileParent,
	getParentName,
	handleFavorite,
	isFileTopping,
	isTopping,
	searchSuppFile,
	suppFile
} from '@/api/modules/document-content';
import { downloadFile } from '@/utils/down-load';
import { debounce, previewFile, tabSize } from '@/utils';
import removeDialog from './remove-dialog.vue';
import PermissionDialog from './permission-dialog.vue';

import { mapGetters, mapState } from 'vuex';
import HandelSetting from './handel-setting.vue';
import { CoosEventTypes } from '@/utils/bus';
import { serveUrl } from '@/config';
import TagsDialog from '@/views/document-content/document-info/components/tags-dialog.vue';
export default {
	components: { TagsDialog, removeDialog, PermissionDialog, HandelSetting },
	props: {
		spaceData: {
			type: Array,
			default: () => {
				return [];
			}
		},
		spaceLoading: {
			type: Boolean,
			default: true
		},
		spaceId: {
			type: String,
			default: ''
		},
		chooseIndex: {
			type: Number,
			default: 0
		},
		isCreate: {
			type: Boolean,
			default: false
		},
		/** 判断是否有操作权限 */
		isAdministrator: {
			type: Boolean,
			default: true
		},
		/** 上传文件相关 */
		newFiles: {
			type: Array,
			default: () => {
				return [];
			}
		},
		/**当前高亮选中文件id*/
		selectFileId: {
			type: [String, Number],
			default: () => {
				return '';
			}
		},
		whichId: {
			type: String,
			default: '666'
		}
	},
	data() {
		return {
			fileLoading: false, // 进入文档的loading效果
			loadId: '', // 正在加载的文件夹id
			animationFileType: '',
			showAnimation: false,
			parentId: 0, // 收藏中用于查询其所在位置
			cardShow: false,
			isRestting: false, //重命名判断
			resetName: {
				id: '',
				name: ''
			}, // 重命名名称
			extension: '',
			iconType: [
				'folder',
				'doc',
				'image',
				'pp',
				'excel',
				'pdf',
				'text',
				'word',
				'unknown',
				'zip',
				'video'
			],
			FolderData: [], //记录文件夹内容
			breadName: [],
			breadNum: 1,
			newFloderName: '',
			currentId: '',
			fileType: '',
			fileContent: '', //摘要
			cardLoading: true,
			isEnter: true, //是否可进入
			imgUrlList: [],
			imgUrl: '',
			fileUrl: '',
			param: {},
			totalSize: 0,
			spaceType: false,
			pageNo: 1,
			checkDown: false,
			rowSpaceIds: [],
			targetInfo: {}, // 移动文件相关
			createWidth: 120, // 创建列宽
			createInitWidth: 120, // 每次计算的初始数据
			startX: '', // 开始时候的X
			endX: '' // 结束时候的X
		};
	},
	computed: {
		...mapGetters(['fileMax', 'clientSystemMode']),
		...mapState('user', ['documentConfig']),

		// 显示下载组件
		showDownLoad() {
			return this.$route.path === '/document' || this.$route.query.hideLoayout === 'true';
		},
		tableData() {
			let arr = this.breadNum > 1 ? this.FolderData : this.spaceData;
			// 按 id 去重
			const ids = new Set();
			arr = arr.filter(item => {
				return !ids.has(item.id) && ids.add(item.id);
			});
			if (arr.length) {
				let seenIds = new Set();
				let uniqueArr = arr.reduce((accumulator, current) => {
					if (!seenIds.has(current.spaceId)) {
						seenIds.add(current.spaceId);
						accumulator.push(current);
					}
					return accumulator;
				}, []);
				uniqueArr.forEach(item => {
					this.isDown(item);
				});
			}
			return [...arr];
		}
	},
	watch: {
		whichId() {
			this.scrollTo();
		},
		tableData: {
			handler(newVal) {
				if (newVal.length > 0) {
					this.$nextTick(() => {
						this.scrollTo();
					});
				}
			},
			deep: true,
			immediate: true
		},
		isCreate(newVal) {
			if (newVal) {
				this.$nextTick(() => {
					// 在组件渲染完毕后聚焦输入框
					this.$refs.newInput.focus();
				});
			}
		}
	},
	mounted() {
		window.addEventListener('mousemove', this.mousemove);
		window.addEventListener('mouseup', this.mouseup);
		// setTimeout(() => {
		// 	this.scrollTo();
		// 	// 判断是否能下载
		// }, 500);
	},
	destroyed() {
		window.removeEventListener('mousemove', this.mousemove);
		window.removeEventListener('mouseup', this.mouseup);
	},
	methods: {
		mouseup() {
			if (this.draging) {
				this.draging = false;
				this.createInitWidth = this.createWidth;
			}
		},
		mousedown(e) {
			let { clientX } = e;
			this.startX = clientX;
			this.draging = true;
		},
		addTags(item) {
			if (this.isAdministrator || item.canEdit) {
				this.$refs.tagsDialog.open(item);
			}
		},
		mousemove(e) {
			if (this.draging) {
				let { clientX } = e;
				this.endX = clientX;
				let offX = this.startX - this.endX;
				let createInitWidth = this.createInitWidth + offX;
				if (createInitWidth > 120 && createInitWidth < 500) {
					this.createWidth = createInitWidth;
				}
			}
		},
		getIsDown(item) {
			if (!item.spaceType) {
				return true;
			}
			let isDown = false;
			if (this.rowSpaceIds.length) {
				this.rowSpaceIds.forEach(event => {
					if (event.spaceId == item.spaceId) {
						isDown = event.isDown;
					} else {
						isDown = true;
					}
				});
			} else {
				isDown = true;
			}
			if (item.canDown) {
				isDown = true;
			}
			return isDown;
		},
		isDown(item) {
			if (!item.spaceType) {
				return true;
			}
			// let isDown = false;
			// 如果没有 检查下载权限
			// const res = await checkDownAuthority({ spaceId: item.spaceId });
			// if (res.code === 200 && res.success) {
			// 	isDown = res.result;
			// }
			// item.isDown = isDown;
			this.rowSpaceIds.push({ ...item });
		},
		vectorizationStatusText(item) {
			switch (item.vectorizationStatus) {
				case 0:
					return '解析中';
				case 1:
					return '解析成功';
				case 2:
					return '解析失败';
				default:
					return '';
			}
		},
		vectorizationStatusClass(item) {
			switch (item.vectorizationStatus) {
				case 0:
					return 'icon-jiexizhong class_jinhangzhong';
				case 1:
					return 'icon-selected class_selected';
				case 2:
					return 'icon-close_circle class_circle';
				default:
					return '';
			}
		},
		//下载补充文件
		downloadSuppeFile(fileId) {
			searchSuppFile(fileId).then(res => {
				if (res.code == 200) {
					downloadFile({ url: res.result[0].fileUrl, name: res.result[0].originalFileName });
				}
			});
		},
		//补充文件操作
		successUpload(val, id, parentId, spaceType, spaceId) {
			const { fileId, fileExt } = val[val.length - 1];
			suppFile({ id: id, fileUrl: fileId, fileExt }).then(res => {
				if (res.code == 200) {
					this.$message.success('上传成功');
					this.getFolderData(parentId, spaceType, spaceId);
				} else {
					this.$message.warning('上传失败');
				}
			});
		},
		// 编辑功能
		handleEdit(row) {
			if (row.infoId) {
				this.$emit('handleEdit', row);
			} else {
				this.$message.warning('当前数据禁止编辑');
			}
		},
		detailsFile(row) {
			this.$emit('detailsFile', row);
		},
		// 删除补充文件
		delSupeFile(id, parentId, spaceType, spaceId) {
			delSuppFile(id).then(res => {
				if (res.code == 200) {
					this.getFolderData(parentId, spaceType, spaceId);
					this.$message.success('删除成功');
				} else {
					this.$message.warning('删除失败');
				}
			});
		},
		//移动文件操作
		openRemove(item) {
			this.targetInfo = item;
			this.$refs.removeDialogRef.dialogVisible = true;
		},
		openPermissionSetting(item) {
			this.targetInfo = item;
			this.$refs.permissionRef.dialogVisible = true;
		},
		scrollTo() {
			this.$nextTick(() => {
				if (this.$refs.scrollContainer) {
					console.log(this.whichId, typeof this.whichId, 'this.whichId');
					const targetElement = this.$refs.scrollContainer.querySelector(
						`[data-id='${this.whichId}']`
					);
					console.log(targetElement, 'targetElementtargetElement');
					if (targetElement) {
						const container = this.$refs.scrollContainer;
						const offsetTop = targetElement.offsetTop;
						container.scrollTop = offsetTop - container.offsetTop;
					}
				}
			});
		},
		handleScroll() {
			const container = this.$refs.scrollContainer; // 获取包含内容的元素
			if (container.clientHeight + container.scrollTop + 1 >= container.scrollHeight) {
				if (this.breadNum == 1) {
					this.$emit('moreData', this.spaceId);
				} else {
					if (this.totalSize > this.FolderData.length) {
						this.pageNo++;
						this.getFolderData(this.currentId, this.spaceType, this.parentId);
					}
				}
			}
		},
		// 预览文件
		viewOrigin() {
			previewFile(this.fileUrl);
			// window.open('https://view.xdocin.com/view?src=' + encodeURIComponent(`${this.fileUrl}`));
		},
		// 上传失败后取消上传
		handleCancleUp(index) {
			// this.newFiles.splice(index, 1);
			this.$emit('deleteNewFile', index);
		},
		// 文件下载
		downFolder(item) {
			let { name, fileUrlPath, orgSize, id, fileType = '' } = item;
			this.animationFileType = fileType;
			this.showAnimation = true;
			setTimeout(() => {
				this.showAnimation = false;
			}, 500);
			if (fileUrlPath) {
				// 处理是否全路径(相对路径带了coos_api，所以拼全之后，在开发环境方便统一replace替换掉)
				let url = /http/.test(fileUrlPath) ? fileUrlPath : window.location.origin + fileUrlPath;
				this._BUS.$emit(CoosEventTypes.addLoadFile, { name, url, orgSize, id, fileType });
			}
		},
		// 复制链接
		copyUrl(fileUrlPath) {
			// 处理是否全路径
			let url = /http/.test(fileUrlPath)
				? fileUrlPath
				: (serveUrl || window.location.origin) + fileUrlPath;
			// 创建一个Clipboard API的写入权限请求
			navigator.clipboard
				.writeText(url)
				.then(() => {
					this.$message({
						message: '复制成功',
						type: 'success'
					});
				})
				.catch(err => {
					console.error('复制失败', err);
				});
		},
		cancleFocus() {
			this.$emit('cancleFolder');
		},
		// 新建文件夹
		handleFolder() {
			if (this.chooseIndex == 3) {
				if (this.spaceId) {
					if (this.breadNum < 2) {
						createFolder({
							name: this.newFloderName,
							parentId: 0,
							spaceId: this.spaceId,
							spaceType: true
						}).then(res => {
							if (res.code == 500) {
								this.$message({
									message: '该文件夹已存在',
									type: 'warning'
								});
							} else {
								this.$emit('cancleFolder');
								this.newFloderName = '';
								this.$emit('getDocumentList', this.spaceId);
							}
						});
					} else {
						createFolder({
							name: this.newFloderName,
							parentId: this.currentId,
							spaceId: this.spaceId,
							spaceType: true
						}).then(res => {
							if (res.code == 500) {
								this.$message({
									message: '该文件夹已存在',
									type: 'warning'
								});
							} else {
								this.$emit('cancleFolder');
								this.newFloderName = '';
								// this.enterFloder(this.currentId, this.fileType);
								this.getFolderData(this.currentId, true, this.spaceId);
							}
						});
					}
				}
			} else {
				createFolder({
					name: this.newFloderName,
					spaceType: false,
					parentId: this.currentId ? this.currentId : 0
				}).then(() => {
					this.$emit('cancleFolder');
					this.newFloderName = '';
					this.getFolderData(this.currentId, false);
				});
			}
		},
		// 进入文件夹
		enterFloder: debounce(
			function (item) {
				let { id, fileType, spaceType, parentId, spaceId, fileUrlPath, orgSize, canEdit } = item;
				// 处理是否全路径
				let url = /http/.test(fileUrlPath)
					? fileUrlPath
					: (serveUrl || window.location.origin) + fileUrlPath;
				if (this.isEnter && (this.chooseIndex == 3 || this.chooseIndex == 2) && !fileUrlPath) {
					this.loadId = id;
					this.fileLoading = true;
					getParentName(id).then(res => {
						this.fileLoading = false;
						this.breadName = res.result;
						this.$emit('getBreadName', this.breadName, id, canEdit);
						this.currentId = id;
						this.fileType = fileType;
						if (fileType == 'folder') {
							// this.$emit('changeLoadingg', true);
							if (this.chooseIndex == 2) {
								this.breadNum++;
								// documentList({ spaceType: false, parentId: id }).then(res => {
								// 	this.breadNum++;
								// 	this.FolderData = res.result.records;
								// 	this.$emit('changeLoadingg', false);
								// });
								this.getFolderData(id, false);
							} else {
								// documentList({ spaceType: true, parentId: id, spaceId: spaceId }).then(res => {
								// 	this.breadNum++;
								// 	this.FolderData = res.result.records;
								// 	this.$emit('changeLoadingg', false);
								// });
								this.breadNum++;
								this.getFolderData(id, true, spaceId);
							}
						}
						// }
					});
				}
				// 回到文件见的位置
				if (this.chooseIndex == 4) {
					this.$emit('changeLoadingg', true);
					this.breadNum = 2;
					if (fileType == 'folder') {
						getParentName(id).then(res => {
							this.breadName = res.result;
							this.$emit('getBreadName', this.breadName, id);
						});
					} else {
						getFileParent(id).then(res => {
							this.breadName = res.result;
							this.$emit('getBreadName', this.breadName, id);
						});
					}
					if (spaceType) {
						// 是否有权限操作
						adminAuthority({ spaceId: spaceId }).then(result => {
							// this.isAdministrator = ;
							// this.$emit('getBreadName', this.breadName.pop(), id);
							this.breadNum = 1;
							documentList({
								spaceType: true,
								parentId: fileType == 'folder' ? id : parentId,
								spaceId: spaceId
							}).then(res => {
								let interval = res.result.records;
								this.transformFileSize(interval);
								// this.$emit('changeLoadingg', false);
								this.$emit('changeDefalut', '3', interval, result.result, spaceId, id, parentId);
								// this.$emit('changeShowBut',result.result)
							});
						});
					} else {
						this.currentId = id;
						// this.$emit('getBreadName', this.breadName, parentId);
						this.$emit('changeDefalut', '2');
						this.$emit('changeShowBut', true);
						documentList({ spaceType: false, parentId: fileType == 'folder' ? id : parentId }).then(
							res => {
								this.FolderData = res.result.records;
								this.transformFileSize(this.FolderData);
								// this.$emit('changeLoadingg', false);
							}
						);
					}
				}
				if (fileType == 'image' && [2, 3].includes(this.chooseIndex)) {
					this.imgUrlList = [url];
					this.imgUrl = url;
					this.$nextTick(() => {
						this.$refs.preview[0].clickHandler();
					});
				}
				//文件预览
				// ['doc', 'pdf', 'text', 'excel', 'pp', 'video', 'unknown'].includes(fileType)
				else if (item.canViewFile) {
					if ([2, 3].includes(this.chooseIndex)) {
						if (fileType !== 'folder') {
							let max = this.fileMax.previewMaxSize || 50 * 1024 * 1024; // 默认上限50mb
							if (orgSize <= max) {
								this.fileUrl = url;
								previewFile(this.fileUrl);
								// window.open('https://view.xdocin.com/view?src=' + encodeURIComponent(`${this.fileUrl}`));
							} else {
								this.$message.error('预览文件超过大小限制！');
							}
						}
					}
				}
			},
			500,
			true
		),
		// 是否置顶
		handleTop(id, isFolder, isTop, parentId, spaceType, spaceId) {
			if (isFolder) {
				isTopping(id, { isTop: !isTop }).then(() => {
					if (this.chooseIndex == 3) {
						this.breadNum < 2
							? this.$emit('getDetailList', spaceId)
							: this.getFolderData(parentId, spaceType, spaceId);
					} else {
						this.getFolderData(parentId, spaceType, spaceId);
					}
				});
			} else {
				isFileTopping(id, { isTop: !isTop }).then(() => {
					if (this.chooseIndex == 3) {
						this.breadNum < 2
							? this.$emit('getDetailList', spaceId)
							: this.getFolderData(parentId, spaceType, spaceId);
					} else {
						this.getFolderData(parentId, spaceType, spaceId);
					}
				});
			}
		},
		// 重命名操作
		async handleReset(id, isFolder, name, canEdit) {
			if (this.isAdministrator || canEdit || this.chooseIndex == 2) {
				this.isEnter = false;
				this.resetName = { id, name };
				if (!isFolder) {
					const extensionMatch = /\.[^.]+$/; // 匹配最后一个点之后的部分
					this.extension = this.resetName.name.match(extensionMatch)[0];
					this.resetName.name = this.resetName.name.split('.')[0];
				}
				await this.$nextTick();
				this.$refs.inputRef[0].select();
			} else {
				this.$message({
					message: '你暂无权限操作',
					type: 'warning'
				});
			}
		},
		sureReset(id, isFolder) {
			// this.handleResetName(id, isFolder);
			this.resetName.id = -1;
		},
		handleResetName(id, isFolder, parentId, spaceType, spaceId) {
			if (isFolder) {
				isTopping(id, { name: this.resetName.name + this.extension }).then(res => {
					if (res.code == 200) {
						this.resetName.id = '';
						if (this.chooseIndex == 3) {
							this.breadNum < 2
								? this.$emit('getDetailList', spaceId)
								: this.getFolderData(parentId, spaceType, spaceId);
						} else {
							this.getFolderData(parentId, spaceType, spaceId);
						}
					} else {
						this.$message({
							message: '文件夹名重复！',
							type: 'warning'
						});
					}
				});
			} else {
				isFileTopping(id, { name: this.resetName.name + this.extension }).then(res => {
					if (res.code == 200) {
						this.resetName.id = '';
						if (this.chooseIndex == 3) {
							this.breadNum < 2
								? this.$emit('getDetailList', spaceId)
								: this.getFolderData(parentId, spaceType, spaceId);
						} else {
							this.getFolderData(parentId, spaceType, spaceId);
						}
					} else {
						this.$message({
							message: '文件名重复！',
							type: 'warning'
						});
					}
				});
			}
			this.isEnter = true;
		},
		// 是否收藏操作
		handleLike(id, isFolder, isFavorite, parentId, spaceType, spaceId) {
			if (!isFavorite) {
				handleFavorite(isFolder, id).then(() => {
					if (this.chooseIndex == 3) {
						this.breadNum < 2
							? this.$emit('getDetailList', spaceId)
							: this.getFolderData(parentId, spaceType, spaceId);
					} else {
						this.getFolderData(parentId, spaceType, spaceId);
					}
				});
				this.$message({
					message: '收藏成功',
					type: 'success'
				});
			} else {
				if (spaceType) {
					// adminAuthority({ spaceId: spaceId }).then(res => {
					// if (res.result) {
					cancleFavorite(isFolder, id).then(() => {
						if (this.chooseIndex == 3) {
							this.breadNum < 2
								? this.$emit('getDetailList', spaceId)
								: this.getFolderData(parentId, spaceType, spaceId);
						} else {
							this.getFolderData(parentId, spaceType, spaceId);
						}
					});
					this.$message({
						message: '取消收藏',
						type: 'success'
					});
					// } else {
					// 	this.$message({
					// 		message: '你暂无权限操作',
					// 		type: 'warning'
					// 	});
					// }
					// });
				} else {
					cancleFavorite(isFolder, id).then(() => {
						this.getFolderData(parentId, spaceType, spaceId);
						this.$message({
							message: '取消收藏',
							type: 'success'
						});
					});
				}
				// if (this.isAdministrator) {
				// 	cancleFavorite(isFolder, id).then(() => {
				// 		if (this.chooseIndex == 3) {
				// 			this.breadNum < 2
				// 				? this.$emit('getDetailList', spaceId)
				// 				: this.getFolderData(parentId, spaceType, spaceId);
				// 		} else {
				// 			this.getFolderData(parentId, spaceType, spaceId);
				// 		}
				// 	});
				// 	this.$message({
				// 		message: '取消收藏',
				// 		type: 'success'
				// 	});
				// } else {
				// 	this.$message({
				// 		message: '你暂无权限操作',
				// 		type: 'warning'
				// 	});
				// }
			}
		},
		// 删除操作
		handleDelete(id, isFolder, parentId, spaceType, spaceId) {
			switch (this.isAdministrator) {
				case true:
					this.$confirm(`你确定要删除该${isFolder ? '文件夹' : '文件'}吗?`, '提示', {
						confirmButtonText: '确定删除',
						cancelButtonText: '取消',
						type: 'warning'
					})
						.then(() => {
							if (isFolder) {
								deleteFolder(id).then(() => {
									this.$message({
										type: 'success',
										message: '删除成功!'
									});
									if (this.chooseIndex == 3) {
										this.breadNum < 2
											? this.$emit('getDetailList', spaceId)
											: this.getFolderData(parentId, spaceType, spaceId);
									} else {
										this.getFolderData(parentId, spaceType, spaceId);
									}
								});
							} else {
								deleteFile(id).then(() => {
									this.$message({
										type: 'success',
										message: '删除成功!'
									});
									if (this.chooseIndex == 3) {
										this.breadNum < 2
											? this.$emit('getDetailList', spaceId)
											: this.getFolderData(parentId, spaceType, spaceId);
									} else {
										this.getFolderData(parentId, spaceType, spaceId);
									}
								});
							}
						})
						.catch(() => {});
					break;
				case false:
					this.$message({
						message: '你暂无权限操作',
						type: 'warning'
					});
			}
		},
		// 是否有操作权限

		// 彻底删除
		deleteCompletely(id, folder) {
			this.$confirm(`你确定要删除该${folder ? '文件夹' : '文件'}吗?`, '提示', {
				confirmButtonText: '确定删除',
				cancelButtonText: '取消',
				type: 'warning'
			})
				.then(() => {
					if (folder) {
						folderDeleteCom(id).then(() => {
							this.$message({
								type: 'success',
								message: '删除成功!'
							});
							this.$emit('selectChange', '5');
						});
					} else {
						fileDeleteCom(id).then(() => {
							this.$message({
								type: 'success',
								message: '删除成功!'
							});
							this.$emit('selectChange', '5');
						});
					}
				})
				.catch(() => {});
		},
		// 回收站取回
		handleTakeBack(id, folder) {
			if (folder) {
				folderRetrieve(id).then(() => {
					this.$emit('selectChange', '5');
				});
			} else {
				fileRetrieve(id).then(() => {
					this.$emit('selectChange', '5');
				});
			}
		},
		// 查看信息
		checkInfo(id, fileType, fileUrlPath) {
			// 处理是否全路径
			let url = /http/.test(fileUrlPath)
				? fileUrlPath
				: (serveUrl || window.location.origin) + fileUrlPath;
			if (fileType != 'image') {
				this.cardLoading = true;
				this.fileUrl = url;
				this.fileContent = '';
				fileSummary(id).then(res => {
					if (res.result) {
						this.fileContent = res.result == 'waiting' ? '解析中…' : res.result;
					} else {
						this.fileContent.push({
							title: '数据为空',
							info: []
						});
					}
					this.cardLoading = false;
				});
				this.cardShow = true;
			} else {
				this.imgUrlList = [url];
				this.imgUrl = url;
				this.$refs.preview[0].clickHandler();
			}
		},
		// 获取文件（夹）列表
		getFolderData(id, spaceType, spaceId) {
			if (this.totalSize <= this.FolderData.length) {
				this.pageNo = 1;
			} else {
				this.FolderData = [];
			}
			this.spaceType = spaceType;
			this.currentId = id;
			this.$emit(spaceType ? 'changeLoading' : 'changeLoadingg', true);
			this.breadNum = 2;
			// spaceType = this.chooseIndex == 3 ? true : false;
			// let param = {};
			// && this.chooseIndex == 3
			if (spaceType) {
				this.param = {
					pageNo: this.pageNo,
					spaceType: spaceType,
					parentId: id,
					spaceId: spaceId
				};
			} else if (this.chooseIndex == 2) {
				this.param = {
					pageNo: this.pageNo,
					spaceType: spaceType,
					parentId: id
				};
			} else {
				this.param = {
					pageNo: this.pageNo,
					isFavorite: true
				};
			}
			documentList(this.param).then(res => {
				this.totalSize = res.result.total;
				this.$emit('updateTotal', this.totalSize);
				// this.FolderData = res.result.records;
				this.transformFileSize(res.result.records);
				// this.$emit('changeLoadingg', false);
				this.$emit(spaceType ? 'changeLoading' : 'changeLoadingg', false);
			});
		},
		transformFileSize(size) {
			this.FolderData = this.pageNo > 1 ? this.FolderData : [];
			size.forEach(item => {
				item.orgSize = item.fileSize;
				item.fileSize = tabSize(item.fileSize);
				this.FolderData.push(item);
			});
		},
		handleSetting(item) {
			this.$refs.handelSetting.open(item);
		},
		refreshList(item) {
			this.getFolderData(item.parentId, item.spaceType, item.spaceId);
		},
		close(e) {
			if (e.isChange) {
				const item = e.item;
				this.getFolderData(item.parentId, item.spaceType, item.spaceId);
			}
		}
	}
};
</script>

<style lang="scss" scoped>
.newFolderButton {
	background: var(--brand-6) !important;
	margin-left: 5px;
}

.default {
	line-height: 22px;
	color: $subTextColor;
	font-size: 14px;
}

// 隐藏滚动条
// .detailList::-webkit-scrollbar {
// 	// display: none;
// }
.newItem {
	height: 56px;
	display: flex;
	align-items: center;
	border-bottom: 1px solid #f0f0f0;

	.createName {
		flex: 1;
		margin-right: 30px;
	}

	.createBy {
		//width: 120px;
		margin-right: 30px;
		color: $primaryTextColor;
		font-weight: 400;
	}

	.updateTime {
		width: 220px;
	}
}

.detailList {
	font-size: 14px;
	line-height: 22px;
	padding: 0px 24px 12px 12px;
	height: 100%;
	overflow-y: auto;

	.ElEmpty {
		height: calc(100vh - 170px);
		display: flex;
		align-items: center;
		justify-content: center;
		// position: sticky;
	}

	.detailInfo {
		display: flex;
		align-items: center;
		height: 54px;
		border-bottom: 1px solid #f0f0f0;
		position: relative;

		.detailItem {
			display: flex;
			align-items: center;
			width: 100%;
			height: 38px;
		}

		.floderName {
			display: flex;
			flex: 1;
			justify-content: space-between;
			overflow: hidden;
			align-items: center;
			color: $primaryTextColor;
			margin-right: 30px;
			font-weight: 500;
			padding-left: 12px;
			.floderName-left {
				display: flex;
				align-items: center;
				margin-right: 10px;
				width: calc(100% - 100px);
				// width: 100%;
				span {
					margin-left: 8px;
					// flex: 1;
					max-width: 100%;
					@include aLineEllipse;
				}

				.fileTypes {
					flex-shrink: 0;
				}

				.pre-box {
					width: 0;
					height: 0;
				}

				.topping {
					flex-shrink: 0;
					width: 34px;
					overflow: hidden;
					height: 20px;
					background: #fff6df;
					border-radius: 3px;
					line-height: 20px;
					text-align: center;
					color: #ad7a04;
					font-size: 12px;
					margin-left: 8px;
				}
			}

			.floderName-left:hover.cancleClick {
				cursor: pointer;
			}

			.floderName-right {
				margin-top: 7px;
				display: flex;
				opacity: 0;
				align-items: center;

				.svgIcon {
					cursor: pointer;
					color: $holderTextColor;
					font-size: 20px;
				}

				.moreIcon {
					margin-top: 2px;
				}

				.interval {
					margin-right: 10px;
				}
			}
		}

		.createBy {
			// width: 153px;
			margin-right: 30px;
			//flex: 0.6;
			//width: 120px;
			color: $primaryTextColor;
			font-weight: 400;
			@include aLineEllipse;
		}

		.updateTime {
			width: 220px;
			//flex: 0.6;
			@include aLineEllipse;
		}
	}

	.whichOne {
		background: #f5f7fa;
		border-radius: 6px;
	}

	.dimension {
		font-weight: 600 !important;
		font-size: 14px !important;
		color: #2f446b !important;
		position: sticky;
		top: 0px;
		z-index: 2;
		background: #fff;
	}

	.newFile {
		height: 56px;
		display: flex;
		align-items: center;
		border-bottom: 1px solid #f0f0f0;

		.newFileInfo {
			display: flex;
			align-items: center;
			flex: 2;
			margin-right: 8px;
		}

		.cancleUpdate {
			margin-left: 8px;
			width: 25px;
			height: 25px;
			background: $borderColor;
			color: #fff;
			display: flex;
			align-items: center;
			justify-content: space-between;
		}
	}

	.detailInfo:hover .floderName-right {
		opacity: 1;
		transition: opacity 0.5s ease;
	}
}

.box-card {
	width: 358px;
	height: 446px;
	margin-left: 25%;
	position: absolute;
	z-index: 998;
	left: 15%;
	bottom: 25%;

	.box-card-title {
		display: flex;

		.card-title {
			width: 280px;
			height: 24px;
			color: #303133;
			font-weight: 800;
			font-size: 18px;
			line-height: 22px;
			text-align: center;
			margin-right: 24px;
		}

		.closeIcon {
			cursor: pointer;
		}
	}

	.card-content {
		.step {
			margin-top: 24px;

			.stepTitle {
				line-height: 22px;
				color: $primaryTextColor;
				font-weight: 400;
				font-size: 16px;
				margin-top: 24px;
				max-height: 308px;
				overflow-y: auto;
				text-align: justify;
				// @include countEllipse(14);
			}

			.stepTitle::-webkit-scrollbar {
				display: none;
			}

			.stepContent {
				line-height: 20px;
				color: $textColor;
				font-weight: 400;
				font-size: 14px;

				.stepItem {
					margin-top: 8px;
				}
			}
		}
	}

	.checkOrigin {
		border: 1px solid var(--brand-6);
		border-radius: 6px;
		color: var(--brand-6);
		width: 86px;
		height: 32px;
		margin-top: 16px;
	}
}

::v-deep .el-card__body {
	padding: 16px 22px 16px 16px;
}

::v-deep .custom-upload {
	padding: 0px;
}

.download {
	position: absolute;
	bottom: 0;
	width: 100%;
}

@keyframes download1 {
	100% {
		transform: translateY(-380px);
	}
}

@keyframes download-content1 {
	to {
		transform: translateX(-500px);
		opacity: 0.3;
	}
}

@keyframes download2 {
	100% {
		transform: translateY(-440px);
	}
}

@keyframes download-content2 {
	to {
		transform: translateX(520px);
		opacity: 0.3;
	}
}

.animation-content {
	left: 50%;
	top: 50%;
	position: fixed;
	z-index: 999;
}

.animation-content1 {
	animation: download-content1 0.5s linear forwards;
}

.animation-content2 {
	animation: download-content2 0.5s linear forwards;
}

.animation-file {
	width: 40px;
	height: 40px;
	border-radius: 6px;
}

.animation-file1 {
	animation: download1 0.5s cubic-bezier(0, 0.72, 0.25, 1) forwards;
}

.animation-file2 {
	animation: download2 0.5s cubic-bezier(0, 0.72, 0.25, 1) forwards;
}

.line {
	height: 40px;
	background: #f2f2f2;
	width: 2px;
	cursor: col-resize;
	margin-right: 12px;
}
.details-file-icon {
	color: #b9bdc9;
	margin-right: 12px;
	font-size: 18px;
	margin-top: -4px;
	cursor: pointer;
}
.load-more {
	padding: 12px 0;
	font-size: 14px;
	display: flex;
	align-items: center;
	justify-content: center;
}
.load-more-con {
	font-size: 16px;
	margin-right: 8px;
	color: var(--brand-6);
}
.select-file {
	background: var(--brand-1);
}
.class_jinhangzhong {
	color: #3088ff;
}
.class_circle {
	color: #e34d59;
}
.class_selected {
	color: #00a870;
}
</style>
<style lang="scss">
.customPopper {
	min-width: 20px !important;
	height: 20px;
	display: flex;
	align-items: center;
}
</style>
