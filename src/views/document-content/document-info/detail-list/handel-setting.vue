<template>
	<el-dialog
		title="配置链接"
		:visible.sync="dialogVisible"
		width="800px"
		:destroy-on-close="true"
		:append-to-body="true"
	>
		<el-form
			ref="form"
			:model="form"
			:rules="rules"
			:label-position="`right`"
			label-width="80px"
			:show-message="false"
		>
			<div v-for="(item, index) in form.links" :key="index" class="links-box">
				<div class="links">
					<el-form-item label="标题" :prop="`links.${index}.title`" :rules="rules.title">
						<el-input v-model="item.title" placeholder="请输入标题" />
					</el-form-item>
					<el-form-item label="链接" :prop="`links.${index}.pc`" :rules="rules.pc">
						<el-input v-model="item.pc" placeholder="请输入">
							<template slot="prepend">网页链接</template>
						</el-input>
						<el-input v-model="item.app" placeholder="请输入">
							<template slot="prepend">H5链接</template>
						</el-input>
					</el-form-item>
					<el-form-item label="备注" :prop="`links.${index}.remark`">
						<el-input v-model="item.remark" placeholder="请输入标题" />
					</el-form-item>
				</div>
				<div class="operation" @click="delLinks(index)">
					<i class="el-icon-minus"></i>
				</div>
			</div>
			<div class="add" @click="addLinks">
				<i class="el-icon-plus" style="font-size: 10px"></i>
				添加
			</div>
		</el-form>
		<span slot="footer" class="dialog-footer">
			<el-button @click="close">取 消</el-button>
			<el-button type="primary" :loading="loading" @click="sure()">确定</el-button>
		</span>
	</el-dialog>
</template>

<script>
import { editLinks } from '@/api/modules/document-content';
export default {
	props: {
		targetInfo: {
			type: Object,
			default: () => {}
		}
	},
	data() {
		return {
			dialogVisible: false,
			id: '',
			loading: false,
			form: {
				links: [{ title: '', pc: '', app: '', remark: '' }]
			},
			rules: {
				title: [{ required: true, message: '请输入标题', trigger: 'blur' }],
				pc: [{ required: true, message: '请输入链接', trigger: 'blur' }]
			},
			currentItem: {}
		};
	},
	methods: {
		addLinks() {
			this.form.links.push({ title: '', pc: '', app: '', remark: '' });
		},
		delLinks(index) {
			this.form.links.splice(index, 1);
		},
		sure() {
			this.$refs.form.validate((valid, object) => {
				if (valid) {
					this.loading = true;
					const links = this.form.links.length ? JSON.stringify(this.form.links) : null;
					editLinks({ id: this.id, links })
						.then(res => {
							this.loading = false;
							if (res.code == 200) {
								this.close(true);
							} else {
								this.$message.error(res.message);
							}
						})
						.catch(e => {
							this.$message.error(e.message);
						});
				}
			});
		},
		open(item) {
			this.id = item.id;
			if (item.links) {
				this.form.links = JSON.parse(item.links);
			} else {
				this.form.links = [{ title: '', pc: '', app: '', remark: '' }];
			}
			console.log('id:', item.id);
			this.dialogVisible = true;
			this.currentItem = item;
		},
		close(bool = false) {
			this.loading = false;
			this.$refs.form.clearValidate();
			this.dialogVisible = false;
			this.$refs.form.links = [];
			this.$emit('close', { isChange: bool == true, item: this.currentItem });
		}
	}
};
</script>

<style lang="scss" scoped>
.links-box {
	display: flex;
	grid-gap: 12px;
	background: #f3f4f6;
	margin-bottom: 12px;
	padding: 12px;
	padding-bottom: 4px;
	border-radius: 6px;
	.links {
		flex: 1;
	}
	.operation {
		display: flex;
		height: 20px;
		width: 20px;
		align-items: center;
		justify-content: center;
		border-radius: 3px;
		background: #fff;
		border: 1px solid #bccadb;
		cursor: pointer;
		margin-top: 3px;
	}
}
.add {
	color: var(--brand-6);
	border: 1px solid var(--brand-6);
	height: 32px;
	padding: 0 12px;
	border-radius: 3px;
	display: flex;
	align-items: center;
	grid-gap: 4px;
	cursor: pointer;
	width: 72px;
	justify-content: center;
}
::v-deep {
	.el-dialog__body {
		max-height: 500px;
		overflow: auto;
	}
	.el-form-item {
		margin-bottom: 8px !important;
	}
	.el-input-group--prepend {
		padding: 4px 0;
		.el-input__inner {
			border-top-left-radius: 0;
			border-bottom-left-radius: 0;
		}
	}
	.el-input-group__prepend {
		width: 90px;
		padding-left: 8px;
		background: #fff;
		border-top-left-radius: 6px;
		border-bottom-left-radius: 6px;
		border: 1px solid #bccadb;
		border-right: 0;
		color: #2f446b;
	}
	.el-input__inner {
		border: 1px solid #bccadb;
		height: 40px;
		line-height: 40px;
		border-radius: 6px;
	}
}
</style>
