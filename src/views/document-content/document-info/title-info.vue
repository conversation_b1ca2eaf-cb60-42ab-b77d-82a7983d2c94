<template>
	<div class="knowledge-title">
		<div class="knowledge-title-left">
			<el-breadcrumb separator-class="el-icon-arrow-right">
				<el-breadcrumb-item @click.native="backMenu">{{ moduleName }}</el-breadcrumb-item>
				<el-breadcrumb-item v-if="spaceName && !breadName.length">
					{{ spaceName }}
				</el-breadcrumb-item>
				<el-breadcrumb-item
					v-for="(item, index) in breadName"
					:key="index"
					@click.native="toFolder(item)"
				>
					{{ item.name }}
				</el-breadcrumb-item>
			</el-breadcrumb>
			<span class="doc-count">（{{ docTotal }}）</span>
		</div>
		<div class="butLeft">
			<div v-if="chooseIndex == 5" class="knowledge-title-right newCreat" @click="clearRecycle">
				清空回收站
			</div>
			<div
				v-if="isAdministrator && fileCanEdit"
				class="knowledge-title-right newCreat"
				@click="newFolder"
			>
				新建文件夹
			</div>
			<div
				v-if="isAdministrator && fileCanEdit && documentConfig && documentConfig.version == 2"
				class="knowledge-title-right"
				@click="handleUpload"
			>
				上传文件
			</div>
			<uploadFile
				v-if="isAdministrator && fileCanEdit && documentConfig && documentConfig.version != 2"
				ref="uploadFile"
				v-model="ids"
				:show-loading="true"
				:can-download="false"
				:multiple="multiple"
				mode="file"
				:limit="999"
				:custom-button="true"
				:custom-file="true"
				:is-preview="false"
				@success="success"
				v-on="$listeners"
			>
				<template #custom-button>
					<div class="knowledge-title-right">上传文件</div>
				</template>
			</uploadFile>
		</div>
		<upload-dialog ref="uploadDialog" @getList="getList" />
	</div>
</template>
<script>
import uploadFile from '@/components/upload-file';
import { uploadFiles } from '@/api/modules/document-content';
import UploadDialog from '@/components/upload-dialog/index.vue';
import { mapState } from 'vuex';
export default {
	components: { UploadDialog, uploadFile },
	props: {
		showBut: {
			type: Boolean,
			default: true
		},
		isEnter: {
			type: Boolean,
			default: true
		},
		moduleName: {
			type: String,
			default: '测试'
		},
		breadName: {
			type: Array,
			default: () => {
				return [];
			}
		},
		spaceName: {
			type: String,
			default: ' '
		},
		chooseIndex: {
			type: [String, Number],
			default: 0
		},
		parentId: {
			type: [String, Number],
			default: 0
		},
		spaceId: {
			type: [String, Number],
			default: 0
		},
		isAdministrator: {
			type: Boolean,
			default: true
		},
		fileCanEdit: {
			type: Boolean,
			default: true
		},
		docTotal: {
			type: [String, Number],
			default: 0
		}
	},
	data() {
		return {
			ids: '',
			multiple: true
			// uploadLoading: false
			// parentId:0
		};
	},
	computed: {
		...mapState('user', ['documentConfig'])
	},
	watch: {
		fileCanEdit(val) {
			console.log(val);
		}
	},
	methods: {
		handleUpload() {
			let row = {
				parentId: this.parentId,
				spaceId: this.spaceId,
				chooseIndex: this.chooseIndex
			};
			this.$refs.uploadDialog.open(row);
		},
		handleEdit(row) {
			row.chooseIndex = this.chooseIndex;
			this.$refs.uploadDialog.open(row);
		},
		detailsFile(row) {
			row.chooseIndex = this.chooseIndex;
			this.$refs.uploadDialog.open(row, 'details');
		},
		getList() {
			if (this.chooseIndex == 2) {
				this.parentId == 0 ? this.$emit('backHome') : this.$emit('backFolderList', this.parentId);
			} else {
				this.parentId == 0
					? this.$emit('backSpaceList', this.spaceId)
					: this.$emit('backFolderList', this.parentId);
			}
		},
		/** 清空回收站 */
		clearRecycle() {
			this.$emit('handleClear');
		},
		deleteNewFile(index) {
			this.$refs.uploadFile.del(index);
			this.$message.success('取消上传！');
		},
		isUpload() {},
		// 新建文件夹
		newFolder() {
			if (this.isAdministrator) {
				this.$emit('newFolder');
			} else {
				this.$message({
					message: '你暂无操作权限',
					type: 'warning'
				});
			}
		},
		// 返回点击菜单初始展示
		backMenu() {
			this.$emit('backHome', 3);
		},
		toFolder(item) {
			if (this.chooseIndex == 3 && !item.isFolder) {
				this.$emit('backSpaceList', item.id);
			} else {
				this.$emit('backFolderList', item.id);
			}
		},
		// 上传文件操作
		success(val) {
			// this.uploadLoading = true;
			const { fileExt, fileSize, fileType, fileId, originalFileName } = val[val.length - 1];
			let newObject = { fileExt, fileSize, fileType, fileId, originalFileName };
			const { originalFileName: name, fileId: fileUrl, ...rest } = newObject;
			const newFileObject = { name, fileUrl, ...rest };
			if (this.chooseIndex == 2) {
				uploadFiles({ ...newFileObject, parentId: this.parentId, spaceType: false }).then(() => {
					this.ids = '';
					this.parentId == 0 ? this.$emit('backHome') : this.$emit('backFolderList', this.parentId);
					// this.uploadLoading = false;
				});
			} else {
				uploadFiles({
					...newFileObject,
					parentId: this.parentId,
					spaceId: this.spaceId,
					spaceType: true
				}).then(() => {
					this.ids = '';
					this.parentId == 0
						? this.$emit('backSpaceList', this.spaceId)
						: this.$emit('backFolderList', this.parentId);
					// this.uploadLoading = false;
				});
			}
		}
	}
};
</script>

<style lang="scss" scoped>
.knowledge-title {
	display: flex;
	justify-content: space-between;
	align-items: center;
	font-size: 14px;
	font-weight: 800;
	color: $primaryTextColor;
	line-height: 24px;
	height: 54px;
	padding: 15px 20px;
	border-top: 1px solid #f0f0f0;
	border-bottom: 1px solid #f0f0f0;
	.knowledge-title-left {
		display: flex;
		align-items: center;
	}
	.doc-count {
		font-size: 14px;
		color: $primaryTextColor;
	}
	.butLeft {
		display: flex;
		align-items: center;
	}
	.knowledge-title-right {
		border-radius: 6px;
		padding: 0px 10px;
		background: var(--brand-6);
		color: #fff;
		font-size: 14px;
		line-height: 32px;
		height: 32px;
		text-align: center;
		font-weight: 400;
		cursor: pointer;
	}
	.newCreat {
		border: 1px solid var(--brand-6);
		background: #fff;
		color: var(--brand-6);
		margin-right: 10px;
		display: flex;
		align-items: center;
		text-align: center;
	}

	.entered {
		color: $subTextColor;
		font-weight: 400;
		margin-right: 5px;
		cursor: pointer;
	}
}
::v-deep .el-breadcrumb {
	.el-breadcrumb__inner {
		cursor: pointer;
	}
	.el-breadcrumb__item:last-of-type {
		.el-breadcrumb__inner {
			font-weight: 600 !important;
		}
	}
}
</style>
