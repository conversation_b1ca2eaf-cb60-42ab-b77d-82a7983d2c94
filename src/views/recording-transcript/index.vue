<template>
	<div v-loading="loading" class="recording-transcript">
		<div v-if="goBackName" class="go-back" @click="goBack">
			<el-button icon="el-icon-arrow-left">{{ goBackName }}</el-button>
		</div>
		<div class="add">
			<el-button type="primary" @click="addDialog">
				<i class="coos-iconfont icon-upload1"></i>
				上传会议文件
			</el-button>
		</div>
		<div class="table">
			<el-table
				ref="table"
				class="table-box"
				:data="tableData"
				style="width: 100%"
				height="100%"
				@header-click="headerClick"
			>
				<el-table-column label="文件名" prop="title">
					<template slot-scope="scope">
						<div class="file-name">
							<i class="coos-iconfont icon-luyinwenjian"></i>
							<div>{{ scope.row.title }}</div>
						</div>
					</template>
				</el-table-column>
				<el-table-column label="解析状态" prop="statusDictText" width="160">
					<template slot="header">
						<div class="table-header-cell">
							<span>解析状态</span>
							<i class="coos-iconfont icon-refresh" style="cursor: pointer" @click="getList"></i>
						</div>
					</template>
					<template slot-scope="scope">
						<div
							:class="
								scope.row.status == 1 || scope.row.status == 0
									? 'statusDictText'
									: scope.row.status == 3
									? 'errorDictText'
									: 'successStatusDictText'
							"
						>
							<i
								v-if="scope.row.status == 1 || scope.row.status == 0"
								class="coos-iconfont icon-luyinzhong"
							></i>
							<i v-else-if="scope.row.status == 3" class="coos-iconfont icon-shibai"></i>
							<i v-else class="coos-iconfont icon-selected"></i>
							<div>{{ scope.row.statusDictText }}</div>
							<i
								v-if="scope.row.status == 3"
								class="coos-iconfont icon-refresh"
								style="cursor: pointer"
								@click="reParser(scope.row.id, 'parser')"
							></i>
						</div>
					</template>
				</el-table-column>
				<el-table-column label="记录状态" width="160" prop="recordStatusDictText">
					<template slot-scope="scope">
						<div
							:class="
								scope.row.recordStatus == 1 || scope.row.recordStatus == 0
									? 'statusDictText'
									: scope.row.recordStatus == 3
									? 'errorDictText'
									: 'successStatusDictText'
							"
						>
							<div v-if="scope.row.recordStatus == 2">
								<span style="cursor: pointer" @click="handleClick(scope.row, 3)">
									{{ scope.row.recordStatusDictText }}
								</span>
								<i
									class="coos-iconfont icon-downland"
									style="cursor: pointer"
									@click="exportData(scope.row.id, 'record')"
								></i>
							</div>
							<div v-else>{{ scope.row.recordStatusDictText }}</div>
							<i
								v-if="scope.row.recordStatus == 3"
								class="coos-iconfont icon-refresh"
								style="cursor: pointer"
								@click="reParser(scope.row.id, 'record')"
							></i>
						</div>
					</template>
				</el-table-column>
				<el-table-column label="纪要状态" width="160" prop="abstractStatusDictText">
					<template slot-scope="scope">
						<div
							:class="
								scope.row.abstractStatus == 1 || scope.row.abstractStatus == 0
									? 'statusDictText'
									: scope.row.abstractStatus == 3
									? 'errorDictText'
									: 'successStatusDictText'
							"
						>
							<div v-if="scope.row.abstractStatus == 2">
								<span style="cursor: pointer" @click="handleClick(scope.row, 2)">
									{{ scope.row.abstractStatusDictText }}
								</span>
								<i
									class="coos-iconfont icon-downland"
									style="cursor: pointer"
									@click="exportData(scope.row.id, 'abstract')"
								></i>
							</div>
							<div v-else>{{ scope.row.abstractStatusDictText }}</div>
							<i
								v-if="scope.row.abstractStatus == 3"
								class="coos-iconfont icon-refresh"
								style="cursor: pointer"
								@click="reParser(scope.row.id, 'abstract')"
							></i>
						</div>
					</template>
				</el-table-column>
				<el-table-column label="创建时间" width="180" prop="createTime"></el-table-column>
				<el-table-column fixed="right" label="操作" width="140">
					<template slot-scope="scope">
						<div class="btn-icon">
							<el-tooltip class="item" effect="dark" content="编辑" placement="top">
								<i
									v-if="scope.row.canOpts.includes('edit')"
									class="coos-iconfont icon-edit"
									@click="handleClick(scope.row, 1)"
								></i>
							</el-tooltip>
							<el-tooltip class="item" effect="dark" content="会议记录" placement="top">
								<i
									v-if="scope.row.canOpts.includes('viewText')"
									class="coos-iconfont icon-huiyijilu"
									@click="handleClick(scope.row, 3)"
								></i>
							</el-tooltip>
							<el-tooltip class="item" effect="dark" content="会议纪要" placement="top">
								<i
									v-if="scope.row.canOpts.includes('viewAbstract')"
									class="coos-iconfont icon-huiyijiyao"
									@click="handleClick(scope.row, 2)"
								></i>
							</el-tooltip>
							<el-tooltip class="item" effect="dark" content="删除" placement="top">
								<i
									v-if="scope.row.canOpts.includes('del')"
									class="coos-iconfont icon-shanchu"
									@click="handleClick(scope.row, 4)"
								></i>
							</el-tooltip>
						</div>
					</template>
				</el-table-column>
			</el-table>
			<div class="page">
				<el-pagination
					class="desk-el-pagination"
					background
					:current-page="pageNo"
					:page-sizes="[10, 20, 50, 100]"
					:page-size="pageSize"
					layout="total, sizes, ->, prev, pager, next, jumper"
					:total="total"
					@size-change="handleSizeChange"
					@current-change="handleCurrentChange"
				></el-pagination>
			</div>
		</div>
		<el-dialog title="上传会议文件" :visible.sync="dialogFormVisible">
			<el-form v-if="dialogFormVisible" ref="formRules" :model="form" :rules="rules">
				<el-form-item label="文件标题" prop="fileTitle">
					<el-input v-model="form.fileTitle" autocomplete="off"></el-input>
				</el-form-item>
				<el-form-item label="文件上传" prop="contentFileUrl">
					<div class="upload-file">
						<div class="upload-file-header">
							<div class="flex">
								<i class="coos-iconfont icon-zhuanxiewenjian"></i>
								<div>转写文件</div>
							</div>
							<div class="flex bg-upload" @click="uploadAdd(1)">
								<i class="coos-iconfont icon-upload1"></i>
								<div>上传文件</div>
							</div>
						</div>

						<!-- 进度条 -->
						<div v-if="uploadProgress !== 'success'" class="progress-bar-container">
							<div class="progress-bar" :style="{ width: uploadProgress }"></div>
						</div>
						<div v-for="(item, index) in form.files" :key="index" class="upload-file-pre">
							<svg-icon class="fileTypes" :icon-class="item.fileType"></svg-icon>
							<span class="custom-upload-file-pre-text">{{ item.originalFileName }}</span>
							<span class="size">{{ formatFileSize(item.fileSize) }}</span>
							<span class="file-status">
								<i class="coos-iconfont icon-chenggong"></i>
								上传成功
							</span>
							<span>
								<i class="coos-iconfont icon-shanchu" @click="delFile(item, 'files')"></i>
							</span>
						</div>
					</div>
				</el-form-item>
				<el-form-item label="录音上传">
					<div class="upload-file">
						<div class="upload-file-header">
							<div class="flex">
								<i class="coos-iconfont icon-zhuanxiewenjian"></i>
								<div>录音文件</div>
							</div>
							<div class="flex bg-upload" @click="uploadAdd(2)">
								<i class="coos-iconfont icon-upload1"></i>
								<div>上传录音</div>
							</div>
						</div>
						<!-- 进度条 -->
						<div v-if="uploadProgressVoicefiles !== 'success'" class="progress-bar-container">
							<div class="progress-bar" :style="{ width: uploadProgressVoicefiles }"></div>
						</div>
						<div v-for="(item, index) in form.voicefiles" :key="index" class="upload-file-pre">
							<svg-icon class="fileTypes" :icon-class="item.fileType"></svg-icon>
							<span class="custom-upload-file-pre-text">{{ item.originalFileName }}</span>
							<span class="size">{{ formatFileSize(item.fileSize) }}</span>
							<span class="file-status">
								<i class="coos-iconfont icon-chenggong"></i>
								上传成功
							</span>
							<span>
								<i class="coos-iconfont icon-shanchu" @click="delFile(item, 'voicefiles')"></i>
							</span>
						</div>
					</div>
				</el-form-item>
			</el-form>

			<UploadFile
				v-show="false"
				:key="fileType"
				ref="upload"
				v-model="UploadFile"
				:accept.sync="accept"
				:show-loading="true"
				:custom-file="true"
				:can-download="true"
				:multiple="true"
				:custom-button="true"
				:no-flex="true"
				@onProcess="onProcess"
				@success="uploadInput"
			></UploadFile>
			<div slot="footer" class="dialog-footer">
				<el-button @click="dialogFormVisible = false">取消</el-button>
				<el-button type="primary" @click="add">开始转写</el-button>
			</div>
		</el-dialog>
	</div>
</template>
<script>
import {
	meetingRecordAdd,
	meetingRecordDel,
	meetingRecordList,
	meetingRecordReParser
} from '@/api/modules/recording-transcript';
import { downloadFile } from '@/utils/down-load';
export default {
	name: 'RecordingTranscript',
	props: {
		goBackName: {
			type: String,
			default: () => {
				return '';
			}
		},
		customGoBack: {
			type: Function,
			default: () => {
				return null;
			}
		}
	},
	data() {
		return {
			dialogFormVisible: false,
			pageNo: 1, //页码
			pageSize: 10, // 分页
			total: 0, // 总数据
			form: {
				fileTitle: '',
				contentFileUrl: '',
				voicefiles: [],
				files: []
			},
			accept: [],
			fileType: '',
			UploadFile: '',
			rules: {
				fileTitle: [{ required: true, message: '请输入文件标题', trigger: 'blur' }],
				contentFileUrl: [{ required: true, message: '请选择文件', trigger: 'blur' }]
			},
			tableData: [],
			isGoBack: '',
			uploadProgress: 'success', // 新增：记录上传进度
			uploadProgressVoicefiles: 'success',
			loading: false
		};
	},
	created() {
		this.getList();
	},
	methods: {
		add() {
			this.$refs.formRules.validate(valid => {
				if (valid) {
					this.loading = true;
					let params = {
						contentFileUrl: this.form.contentFileUrl,
						voiceFileUrl: this.form.voiceFileUrl,
						title: this.form.fileTitle
					};
					meetingRecordAdd(params).then(res => {
						this.loading = false;
						if (res.code === 200 && res.success) {
							this.$message.success('创建成功');
							this.pageNo = 1;
							this.pageSize = 10;
							this.dialogFormVisible = false;
							this.getList();
						} else {
							this.$message.error('创建失败');
						}
					});
				}
			});
		},

		getList() {
			this.loading = true;
			let params = {
				pageNo: this.pageNo,
				pageSize: this.pageSize
			};
			meetingRecordList(params).then(res => {
				this.loading = false;
				if (res.code === 200) {
					this.tableData = res.result.records || [];
					this.total = res.result.total;
				} else {
					this.$message.error(res.message);
				}
			});
		},
		headerClick(column, event) {
			if (column.label === '状态') {
				this.getList();
			}
		},
		formatFileSize(bytes) {
			if (bytes < 1024) return bytes + ' B';
			else if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
			else if (bytes < 1024 * 1024 * 1024) return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
			else return (bytes / (1024 * 1024 * 1024)).toFixed(1) + ' GB';
		},
		onProcess(arr) {
			if (this.fileType === 1) {
				this.uploadProgress = arr[0].process;
			} else {
				this.uploadProgressVoicefiles = arr[0].process;
			}
		},
		uploadAdd(type) {
			if (type == 1) {
				this.accept = ['doc', 'docx', 'pdf', 'txt'];
			} else {
				this.accept = ['mp3', 'wav', 'ogg', 'aac', 'flac', 'm4a'];
			}
			this.UploadFile = '';
			this.fileType = type;
			this.$nextTick(() => {
				// this.$refs.upload.$data.previewArr = [];
				// this.$refs.upload.$data.cancelUpload = [];
				// this.$refs.upload.$data.showPreviewArr = [];
				// this.$refs.upload.$data.upLoadFile = [];
				console.log(this.$refs.upload, 'this.$refs.uploadthis.$refs.upload');
				this.$refs.upload.uploadFile();
			});
		},
		uploadInput(row) {
			if (this.fileType == 1) {
				this.form.files = row;
				this.UploadFile = row[0].fileId;
				if (row && row.length) {
					this.form.contentFileUrl = row[0].fileId;
					this.$refs.formRules.validateField('contentFileUrl');
				}
			} else {
				this.UploadFile = row[0].fileId;
				this.form.voicefiles = row;
				if (row && row.length) {
					this.form.voiceFileUrl = row[0].fileId;
					this.$refs.formRules.validateField('voiceFileUrl');
				}
			}
		},
		/**改变页码*/
		handleCurrentChange(i) {
			this.pageNo = i;
			this.getList();
		},
		/**改变分页大小*/
		handleSizeChange(i) {
			this.pageNo = 1;
			this.pageSize = i;
			this.getList();
		},
		goBack() {
			if (this.customGoBack) {
				this.customGoBack();
			} else {
				this.$router.go(-1);
			}
		},
		delFile(item, type) {
			this.form[type] = [];
			if (type === 'files') {
				this.form.contentFileUrl = '';
			} else {
				this.form.voiceFileUrl = '';
			}
			this.fileType = '';
			this.UploadFile = '';
		},
		addDialog() {
			this.form = {
				fileTitle: '',
				contentFileUrl: '',
				voicefiles: [],
				files: []
			};
			this.fileType = '';
			this.UploadFile = '';
			this.dialogFormVisible = true;
		},
		handleClick(row, type) {
			const ROUTE_PATHS = {
				TRANSCRIBE_FILE: '/recording-transcript/transcribe-file',
				DETAILS: '/recording-transcript/details'
			};
			if (!row || !('id' in row) || !('title' in row)) {
				console.warn('无效的 row 数据', row);
				return;
			}

			switch (type) {
				case 1:
					this.$router.push({
						path: ROUTE_PATHS.TRANSCRIBE_FILE,
						query: {
							id: row.id,
							title: row.title,
							recordStatus: row.recordStatus,
							abstractStatus: row.abstractStatus
						}
					});
					break;

				case 2:
				case 3:
					this.$router.push({
						path: ROUTE_PATHS.DETAILS,
						query: {
							id: row.id,
							type: type, // 动态传入 type
							title: row.title
						}
					});
					break;

				case 4:
					this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
						confirmButtonText: '确定',
						cancelButtonButtonText: '取消',
						type: 'warning'
					})
						.then(() => {
							this.loading = true;
							meetingRecordDel([row.id])
								.then(res => {
									this.loading = false;
									if (res.code === 200 && res.success) {
										this.$message.success('删除成功');
										this.pageNo = 1;
										this.pageSize = 10;
										this.getList();
									} else {
										this.$message.error('删除失败');
									}
								})
								.catch(err => {
									this.loading = false;
									console.error('删除请求异常:', err);
								});
						})
						.catch(() => {
							this.$message({
								type: 'info',
								message: '已取消删除'
							});
						});
					break;
				default:
					console.warn('未知的操作类型:', type);
					this.$message.warning('未知的操作类型');
					break;
			}
		},
		reParser(id, optType) {
			meetingRecordReParser(id, optType).then(res => {
				if (res.code !== 200) {
					this.$message.error(res.message);
					return;
				}
				this.getList();
			});
		},
		exportData(id, optType) {
			downloadFile({
				url: `/api/robot/meetingRecord/exportContent`,
				params: {
					id: id,
					optType
				}
			});
		}
	}
};
</script>
<style scoped lang="scss">
.recording-transcript {
	background: #fcfcfc;
	display: flex;
	flex-direction: column;
	flex: 1;
	height: 100%;
	padding: 11px 24px;
	.go-back {
		padding-bottom: 16px;
	}
	.add {
		padding: 16px 0;
	}
	.table {
		flex: 1;
		background: #ffffff;
		box-shadow: 0px 7px 20px 0px rgba(232, 234, 237, 0.4);
		border-radius: 9px 9px 9px 9px;
		padding: 20px;
		flex-direction: column;
		display: flex;
		&-box {
			flex: 1;
		}
		.page {
			padding-top: 20px;
		}
		::v-deep .file-name {
			display: flex;
			align-items: center;
			i {
				color: var(--brand-6);
				margin-right: 10px;
			}
		}
	}
	::v-deep .el-button {
		padding: 7px 12px 7px 9px;
		span {
			margin-left: 0;
		}
	}
}
.icon-shanchu {
	color: #737a94;
	font-size: 18px;
}
::v-deep .el-dialog {
	width: 520px !important;
	background: #ffffff;
	box-shadow: 0px 8px 10px -5px rgba(21, 34, 76, 0.08), 0px 16px 24px 2px rgba(15, 69, 234, 0.04),
		0px 6px 30px 5px rgba(21, 34, 76, 0.05);
	border-radius: 16px 16px 16px 16px;
	.el-dialog__title {
		font-family: PingFang SC, PingFang SC;
		font-weight: 600;
		font-size: 18px;
		color: #15224c;
		line-height: 24px;
	}
	.el-dialog__body {
		padding: 0 20px 20px !important;
	}
	.upload-file {
		display: flex;
		flex-direction: column;
		width: 100%;
		padding: 0 12px;
		border-radius: 6px 6px 6px 6px;
		border: 1px solid #e3ebf2;
		.upload-file-pre {
			display: flex;
			align-items: center;
			.size {
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 10px;
				color: #737a94;
				line-height: 22px;
				margin-left: 38px;
			}
			.file-status {
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 12px;
				color: #2f446b;
				line-height: 22px;
				margin-left: 38px;
				margin-right: 40px;
				display: flex;
				align-items: center;
				i {
					color: #00a870;
				}
			}
			.custom-upload-file-pre-text {
				max-width: 190px;
				min-width: 190px;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}
		}
		&-header {
			width: 100%;
			display: flex;
			justify-content: space-between;
			align-items: center;
		}
		.flex {
			display: flex;
			align-items: center;
			i {
				margin-right: 4px;
				color: var(--brand-6);
			}
		}
		.bg-upload {
			padding: 3px 12px;
			height: 28px;
			background: #edf1fa;
			border-radius: 6px 6px 6px 6px;
			i {
				color: #2f446b;
			}
		}
	}
}
::v-deep .el-form {
	.el-input__inner {
		height: 38px !important;
		line-height: 38px !important;
	}
	.el-form-item {
	}
}
.statusDictText {
	display: flex;
	align-items: center;
	color: #3088ff;
	i {
		margin-right: 4px;
	}
}
.errorDictText {
	display: flex;
	align-items: center;
	color: #f56c6c;
	i {
		margin-right: 4px;
	}
}
.successStatusDictText {
	display: flex;
	align-items: center;
	color: #00a870;
	i {
		margin-right: 4px;
	}
}
.btn-icon {
	i {
		margin-right: 14px;
	}
}
.table-header-cell {
	i {
		margin-left: 5px;
		font-size: 14px;
	}
}
.progress-bar-container {
	width: 100%;
	height: 6px;
	background-color: #e0e0e0;
	border-radius: 3px;
	overflow: hidden;
	margin-bottom: 10px;
}

.progress-bar {
	height: 100%;
	background-color: #4caf50;
	transition: width 0.3s ease;
}
</style>
