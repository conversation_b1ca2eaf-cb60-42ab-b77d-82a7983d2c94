<template>
	<div v-loading="loading" class="transcribe-file">
		<div class="transcribe-file-content">
			<div class="header">
				<div class="back" @click="goBack">
					<svg-icon class="title-icon" icon-class="seal-left"></svg-icon>
					<span class="title-text">{{ title }}</span>
				</div>
				<div class="btn">
					<el-button
						v-if="membersSpeakAdjust && membersSpeakAdjust.length"
						@click="setUserList(isMembersSpeakAdjust)"
					>
						{{ isMembersSpeakAdjust ? '切换为原文' : '切换为调整后' }}
					</el-button>
					<el-button :disabled="recordStatus == 1" @click="edit('record')">
						{{
							recordStatus == 1
								? recordStatusDictText
								: recordStatus == 2
								? '重新生成会议记录'
								: '生成会议记录'
						}}
					</el-button>
					<el-button :disabled="abstractStatus == 1" type="primary" @click="edit('abstract')">
						{{
							abstractStatus == 1
								? abstractStatusDictText
								: abstractStatus == 2
								? '重新生成会议纪要'
								: '生成会议纪要'
						}}
					</el-button>
				</div>
			</div>
			<div class="box">
				<div class="box-left">
					<div class="box-left-title">录音文件</div>
					<div v-show="audioUrl" class="box-left-file">
						<audio ref="audioPlayer" :key="audioUrl" controls @canplay="onAudioCanPlay">
							<source :src="audioUrl" type="audio/mp3" />
							您的浏览器不支持音频播放。
						</audio>
					</div>
					<div class="box-left-box">
						<div v-for="(event, index) in userList" :key="index" class="box-left-item">
							<div :key="event.editName" class="user">
								<i class="coos-iconfont icon-yonghu1"></i>
								<div class="user-name">
									<span v-if="!event.editName">{{ event.name }}</span>
									<el-input v-else v-model="userRow.name" placeholder="请输入内容"></el-input>
								</div>
								<i
									v-if="!event.editName"
									class="coos-iconfont icon-edit"
									@click="editName(event)"
								></i>
								<i
									v-else
									class="coos-iconfont icon-anniu_xuanzhong edit-name"
									@click="handleName"
								></i>
							</div>
							<!--              @mouseenter="hoveredUserIndex = index"-->
							<!--              @mouseleave="hoveredUserIndex = null"-->
							<div class="line">
								<div ref="lineItem" class="line-item">
									<div v-for="(item, itemIndex) in timeSegments[index].ranges" :key="itemIndex">
										<el-tooltip
											:content="`时间:${item.start} ~ ${item.end};时长：${formatDuration(item)}`"
											placement="top"
										>
											<div
												class="time-block"
												:class="'block-' + (index % 3)"
												:style="{ left: item.startPercent + '%', width: item.widthPercent + '%' }"
												@click="handleClick(item)"
											></div>
										</el-tooltip>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="box-right">
					<div class="box-right-title">录音内容</div>
					<div class="box-right-box">
						<div
							v-for="(item, index) in rightList"
							:key="index"
							:data-id="item.id"
							:class="
								item.decEdit
									? 'box-right-item box-right-item-edit'
									: item.id === actId
									? 'box-right-item-act box-right-item'
									: 'box-right-item'
							"
							@click="actId = item.id"
						>
							<div class="box-right-item-header">
								<div class="header-left">
									<span>{{ item.date[0] }}</span>
									<span>{{ item.name }}</span>
									<!-- 播放时显示的图标v-if="index === playingIndex" -->
									<template v-if="index === playingIndex">
										<div class="audio-wave-wrapper">
											<div class="audio-wave-animation">
												<span class="bar"></span>
												<span class="bar"></span>
												<span class="bar"></span>
												<span class="bar"></span>
												<span class="bar"></span>
												<span class="bar"></span>
												<span class="bar"></span>
												<span class="bar"></span>
												<span class="bar"></span>
											</div>
										</div>
									</template>
								</div>
								<div class="header-right">
									<i
										class="coos-iconfont icon-bofang font-icon-css"
										style="cursor: pointer"
										@click="audioUrlClick(item)"
									></i>
									<i
										v-if="rightListHaveMore"
										class="coos-iconfont icon-trash font-icon-css"
										style="cursor: pointer"
										@click="delDec(item)"
									></i>
									<i
										v-if="!item.decEdit"
										class="coos-iconfont icon-edit dec-edit font-icon-css"
										style="cursor: pointer"
										@click="editDec(item)"
									></i>
									<i
										v-else
										class="coos-iconfont icon-anniu_xuanzhong edit-dec font-icon-css"
										style="cursor: pointer"
										@click="handleDec"
									></i>
								</div>
							</div>
							<div
								v-if="!item.decEdit"
								class="dec"
								style="cursor: pointer"
								@click="handleRight(item)"
								v-html="formatText(item.dec)"
							></div>
							<el-input
								v-show="item.decEdit"
								:ref="'inputTextarea_' + item.id"
								v-model="decRow.dec"
								:data-id="item.id"
								type="textarea"
								autosize
								placeholder="请输入内容"
								@blur="blurInput"
							></el-input>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import { meetingRecordDetails, meetingRecordEdit } from '@/api/modules/recording-transcript';

export default {
	name: 'TranscribeFile',
	data() {
		return {
			actId: 1,
			rightList: [],
			decRow: {},
			userRow: {},
			title: '',
			isAudioReady: false,
			loading: false,
			timeSegmentsCache: null, // 缓存非 hover 状态下的 timeSegments 数据
			hoverLayoutCache: null, // 缓存 hover 用户的等宽布局
			hoveredUserIndex: null,
			id: '',
			recordStatus: '',
			membersSpeakAdjust: [],
			membersSpeak: [],
			recordStatusDictText: '',
			abstractStatusDictText: '',
			audioUrl: '',
			isMembersSpeakAdjust: false,
			currentPlayTime: 0, // 当前音频播放时间
			playingIndex: -1, // 当前播放中的发言索引
			abstractStatus: '',
			userList: []
		};
	},
	computed: {
		timeSegments() {
			return this.calculateTimeRanges();
		},
		rightListHaveMore() {
			return this.rightList.length > 1;
		}
	},
	created() {
		this.id = this.$route.query.id || '';
		this.title = this.$route.query.title || '';
		this.getDetails(this.id);
	},
	mounted() {},
	beforeUnmount() {
		const audio = this.$refs.audioPlayer;
		if (audio) {
			// 移除 timeupdate 监听器
			audio.removeEventListener('timeupdate', this.handleAudioTimeUpdate);
			// 停止播放并释放资源
			audio.pause();
			audio.src = ''; // 清空音频源
		}
		// 清空数据
		this.userList = [];
		this.rightList = [];
		this.audioUrl = '';
		this.playingIndex = -1; // 重置播放索引
		this.isAudioReady = false;
	},
	methods: {
		edit(type) {
			this.loading = true;
			let data = {
				id: this.id,
				saveOptType: type,
				contentTextInfo: this.userList
			};
			meetingRecordEdit(data).then(res => {
				this.loading = false;
				if (res.code === 200 && res.success) {
					this.getDetails(this.id);
					this.$message.success('操作成功');
				} else {
					this.$message.error('操作失败');
				}
			});
		},
		setUserList(isMembersSpeakAdjust) {
			if (isMembersSpeakAdjust) {
				this.userList = this.membersSpeak;
			} else {
				this.userList = this.membersSpeakAdjust;
			}
			this.setList();
			this.isMembersSpeakAdjust = !isMembersSpeakAdjust;
		},
		setList() {
			const allSpeeches = [];
			this.userList.forEach(user => {
				user.list.forEach(speech => {
					speech.startTimeSec = this.toSeconds(speech.date[0]);
					speech.endTimeSec = this.toSeconds(speech.date[1]);
					allSpeeches.push({
						name: user.name,
						memberId: user.memberId,
						date: speech.date,
						id: speech.id,
						decEdit: false,
						dec: speech.dec
					});
				});
			});
			// 按时间排序（使用起始时间）
			const sortedList = JSON.parse(JSON.stringify(allSpeeches)).sort((a, b) => {
				const aStart = this.toSeconds(a.date?.[0]);
				const bStart = this.toSeconds(b.date?.[0]);
				if (isNaN(aStart)) console.warn('Invalid start time for a:', a);
				if (isNaN(bStart)) console.warn('Invalid start time for b:', b);
				return aStart - bStart || a.id - b.id; // 加上 id 作为第二排序条件
			});
			this.$set(this, 'rightList', sortedList);
		},
		getDetails() {
			this.loading = true;
			meetingRecordDetails(this.id).then(res => {
				this.loading = false;
				if (
					res.code === 200 &&
					res.result &&
					res.result.membersSpeak &&
					res.result.membersSpeak.length
				) {
					this.membersSpeak = res.result.membersSpeak || [];
					this.membersSpeakAdjust = res.result.membersSpeakAdjust || [];
					const hasMembersSpeakAdjust = this.membersSpeakAdjust.length > 0;
					this.userList = hasMembersSpeakAdjust ? this.membersSpeakAdjust : this.membersSpeak;
					this.isMembersSpeakAdjust = hasMembersSpeakAdjust;
					this.setList();
					this.audioUrl = res.result.voiceFileUrlPath || '';
					this.recordStatus = res.result.recordStatus || '';
					this.recordStatusDictText = res.result.recordStatusDictText || '';
					this.abstractStatusDictText = res.result.abstractStatusDictText || '';
					this.abstractStatus = res.result.abstractStatus || '';
				} else {
					this.userList = [];
					const errorMessage = res.message ? res.message : '获取数据失败，请稍后再试';
					this.$message.error(errorMessage);
				}
			});
		},
		goBack() {
			this.$router.go(-1);
		},
		formatText(text) {
			return text.replace(/\n/g, '<br>');
		},
		delDec(item) {
			// 判断是否只剩下一个用户，并且该用户只有一个发言
			const isOnlyUser = this.userList.length === 1;
			let isOnlySpeech = false;

			if (isOnlyUser) {
				const currentUser = this.userList[0];
				isOnlySpeech = currentUser.list.length === 1;
			}

			if (isOnlyUser && isOnlySpeech) {
				this.$message.warning('至少保留一个发言记录');
				return;
			}
			this.$confirm('确定要删除该发言内容吗？', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			})
				.then(() => {
					const { id, memberId } = item;

					// 1. 从 userList 中删除对应发言
					const user = this.userList.find(u => u.memberId === memberId);
					if (user) {
						const speechIndex = user.list.findIndex(speech => speech.id === id);
						if (speechIndex > -1) {
							user.list.splice(speechIndex, 1); // 使用 splice 触发 Vue 响应式更新
						}

						// 👇 新增逻辑：如果 list 为空，则删除整个用户
						if (user.list.length === 0) {
							const userIndex = this.userList.indexOf(user);
							if (userIndex > -1) {
								this.userList.splice(userIndex, 1);
							}
						}
					}

					// 2. 从 rightList 中删除
					const rightIndex = this.rightList.findIndex(i => i.id === id);
					if (rightIndex > -1) {
						this.rightList.splice(rightIndex, 1);
					}

					// 3. 清空编辑状态（如果有）
					if (this.decRow && this.decRow.id === id) {
						this.decRow = {};
					}
					this.clearTimeSegmentCache(); // 清除缓存，强制下次重新计算
					// 接口更新数据
					this.edit('adjust');
				})
				.catch(() => {
					// 用户取消删除
				});
		},
		blurInput() {
			this.handleDec();
		},
		handleRight(item) {
			if (Object.keys(this.decRow).length === 0) {
				item.decEdit = true;
				this.actId = item.id;
				this.decRow = { ...item };
				this.$nextTick(() => {
					this.$refs['inputTextarea_' + item.id][0].focus();
				});
			}
		},
		// 计算时间段持续多少秒
		formatDuration(item) {
			const startSec = this.toSeconds(item.start);
			const endSec = this.toSeconds(item.end);
			const duration = endSec - startSec;

			const hours = Math.floor(duration / 3600);
			const minutes = Math.floor((duration % 3600) / 60);
			const seconds = duration % 60;

			let result = '';
			if (hours > 0) {
				result += `${hours}小时`;
			}
			if (minutes > 0) {
				result += `${minutes}分`;
			}
			result += `${seconds}秒`;

			return result;
		},
		handleAudioTimeUpdate() {
			const audio = this.$refs.audioPlayer;
			if (!audio) return;

			const currentTime = Math.floor(audio.currentTime); // 获取当前播放时间（秒）
			let foundIndex = -1;

			// 遍历所有发言记录，查找当前播放时间所属的发言
			for (let i = 0; i < this.rightList.length; i++) {
				const item = this.rightList[i];
				const startSec = this.toSeconds(item.date[0]);

				if (i === 0 && currentTime < startSec) {
					// 如果是第一条，并且当前播放时间小于它的开始时间，则不显示任何图标
					break;
				}

				if (i > 0) {
					const prevStartSec = this.toSeconds(this.rightList[i - 1].date[0]);
					const currStartSec = startSec;

					// 时间在上一条和当前之间 -> 显示上一条
					if (currentTime >= prevStartSec && currentTime < currStartSec) {
						foundIndex = i - 1;
						break;
					}
				}

				// 时间在当前发言范围内 -> 显示当前这条
				const endSec = this.toSeconds(item.date[1]);
				if (currentTime >= startSec && currentTime <= endSec) {
					foundIndex = i;
					break;
				}
			}
			this.actId = this.rightList[foundIndex].id;
			this.playingIndex = foundIndex;
		},
		onAudioCanPlay() {
			this.isAudioReady = true;
			this.$refs.audioPlayer.addEventListener('timeupdate', this.handleAudioTimeUpdate);
			// 用户首次点击页面时允许播放
			this.$el.addEventListener(
				'click',
				() => {
					const audio = this.$refs.audioPlayer;
					if (audio && !audio.src) return;

					// 尝试播放并暂停以解除浏览器限制
					audio
						.play()
						.then(() => audio.pause())
						.catch(() => {});
				},
				{ once: true }
			);
		},
		audioUrlClick(item) {
			const audio = this.$refs.audioPlayer;
			if (!audio) return;

			const seconds = this.toSeconds(item.date[0]);
			if (typeof seconds === 'number' && !isNaN(seconds)) {
				audio.currentTime = seconds;
				if (this.isAudioReady) {
					// 不管当前是否播放，都尝试播放
					const playPromise = audio.play();
					if (playPromise !== undefined) {
						playPromise.catch(error => {
							if (error.name === 'NotAllowedError') {
								this.$message.error('请先与页面互动后再播放音频');
							} else if (error.name !== 'AbortError') {
								console.error('播放错误:', error);
							}
						});
					}
				} else {
					this.$message.warning('音频尚未加载完成，请稍后重试');
				}
			}
		},
		closeAllUserEdits() {
			this.userList.forEach(user => {
				if (user.editName) {
					this.$set(user, 'editName', false);
				}
			});
		},
		editName(item) {
			this.closeAllUserEdits(); // 关闭其他用户的编辑状态
			const foundUser = this.userList.find(user => user.memberId === item.memberId);
			if (foundUser) {
				this.$set(foundUser, 'editName', !item.editName);
			}
			this.userRow = { ...item };
		},
		handleName() {
			const { memberId, name } = this.userRow;
			// 找到当前正在编辑的用户
			const foundUser = this.userList.find(user => user.memberId === memberId);
			if (!foundUser) return;
			if (!name || /^\s*$/.test(name)) {
				this.$set(foundUser, 'editName', false);
				return;
			}
			this.$set(foundUser, 'name', name);
			// 查找是否有相同 name 的用户（排除自己）
			const nameUser = this.userList.find(user => user.name === name && user.memberId !== memberId);
			if (nameUser) {
				// 合并发言记录
				const mergedList = [...nameUser.list, ...foundUser.list].sort((a, b) =>
					this.toSeconds(a.date[0]) > this.toSeconds(b.date[0]) ? 1 : -1
				);
				this.$set(foundUser, 'list', mergedList);
				// 删除原用户
				const indexToRemove = this.userList.findIndex(u => u.memberId === nameUser.memberId);
				if (indexToRemove > -1) {
					this.userList.splice(indexToRemove, 1); // 删除该用户
				}
			}
			// 更新 rightList 中对应发言的 name 和 memberId
			this.rightList.forEach(item => {
				if (item.memberId === memberId || (nameUser && item.memberId == nameUser.memberId)) {
					item.memberId = memberId;
					item.name = name;
				}
			});
			// 关闭编辑状态
			this.$set(foundUser, 'editName', false);

			// 清空临时对象 & 清除缓存
			this.userRow = {};
			this.clearTimeSegmentCache();
			// 接口更新数据
			this.edit('adjust');
		},
		editDec(item) {
			item.decEdit = !item.decEdit;
			this.decRow = { ...item };
		},
		clearTimeSegmentCache() {
			this.timeSegmentsCache = null;
			this.hoverLayoutCache = null;
		},
		handleDec() {
			// 找到对应 id 的 speech 对象，并更新 dec
			const { id, dec } = this.decRow;
			// 遍历 userList 更新 list 中的内容
			for (let user of this.userList) {
				const found = user.list.find(speech => speech.id === id);
				if (found) {
					if (dec && !/^\s*$/.test(dec)) {
						found.dec = dec;
					}
					break;
				}
			}
			// 同时更新 rightList
			const rightItem = this.rightList.find(item => item.id === id);
			if (rightItem) {
				if (dec && !/^\s*$/.test(dec)) {
					this.$set(rightItem, 'dec', dec);
				}
				this.$set(rightItem, 'decEdit', false); // 关闭编辑态
			}
			this.decRow = {};
			// 接口更新数据
			this.edit('adjust');
		},
		handleClick(item) {
			this.actId = item.id;
			// 找到 rightList 中对应的索引
			const index = this.rightList.findIndex(i => i.id === item.id);
			if (index >= 0) {
				this.scrollToIndex(index);
			}
		},
		scrollToIndex(index) {
			const container = this.$el.querySelector('.box-right-box');
			if (!container) return;
			const targets = container.querySelectorAll('.box-right-item');
			const target = targets && targets[index];
			if (target) {
				// 使用 scrollIntoView 实现平滑滚动
				target.scrollIntoView({
					behavior: 'smooth',
					block: 'start', // 滚动到顶部对齐
					inline: 'nearest'
				});
			}
		},
		getGlobalTimeRange() {
			const allTimes = [];
			this.userList.forEach(user => {
				user.list.forEach(item => {
					allTimes.push(...item.date);
				});
			});

			const minTime = Math.min(...allTimes.map(this.toSeconds));
			const maxTime = Math.max(...allTimes.map(this.toSeconds));

			return { minTime, maxTime };
		},
		calculateTimeRanges() {
			const isHovering = this.hoveredUserIndex !== null;

			if (!isHovering && this.timeSegmentsCache) {
				return this.timeSegmentsCache;
			}
			if (
				isHovering &&
				this.hoverLayoutCache &&
				this.hoverLayoutCache.index === this.hoveredUserIndex
			) {
				return this.hoverLayoutCache.data;
			}

			const { minTime, maxTime } = this.getGlobalTimeRange();
			const duration = maxTime - minTime;

			const GAP_PERCENT = 0.5; // 每个发言块之间的最小间隙
			const MIN_WIDTH_PERCENT = 0.2; // 最小宽度

			const result = this.userList.map((user, userIndex) => {
				const ranges = [];
				let prevEndPercent = 0; // 上一个块结束的位置

				if (isHovering && userIndex === this.hoveredUserIndex) {
					// 👇 HOVER 状态：等宽 + 间距 布局
					const totalItems = user.list.length;
					const totalGap = GAP_PERCENT * (totalItems - 1);
					const itemWidthPercent = (100 - totalGap) / totalItems;
					user.list.forEach(({ date, id, startTimeSec }, idx) => {
						const [start, end] = date;
						const startPercent = idx * (itemWidthPercent + GAP_PERCENT);
						const widthPercent = itemWidthPercent;
						ranges.push({
							start,
							end,
							id,
							startPercent,
							widthPercent
						});
					});
				} else {
					// 👇 默认状态：时间比例 + 间隙 布局
					user.list.forEach(({ date, id, startTimeSec, endTimeSec }, idx) => {
						const [start, end] = date;
						const startSec = startTimeSec;
						const endSec = endTimeSec;

						let startPercent = ((startSec - minTime) / duration) * 100;
						let widthPercent = ((endSec - startSec) / duration) * 100;

						// 强制添加最小间隙（非 hover 状态下也生效）
						if (idx > 0) {
							startPercent = Math.max(startPercent, prevEndPercent + GAP_PERCENT);
						}

						// 设置最小宽度，防止太窄
						widthPercent = Math.max(widthPercent, MIN_WIDTH_PERCENT);

						// 如果起始位置超过 100%，跳过渲染该块（避免溢出）
						if (startPercent >= 100) return;

						// 更新当前块结束位置
						prevEndPercent = startPercent + widthPercent;

						ranges.push({
							start,
							end,
							id,
							startPercent,
							widthPercent
						});
					});
				}

				return { name: user.name, ranges };
			});
			// 归一化处理：如果所有块加起来超过 100%，按比例缩放
			result.forEach(user => {
				if (user.ranges.length === 0) return;

				const totalWidth =
					user.ranges[user.ranges.length - 1].startPercent +
					user.ranges[user.ranges.length - 1].widthPercent;
				if (totalWidth > 100) {
					const scale = 100 / totalWidth;
					let currentPos = 0;
					user.ranges.forEach(range => {
						range.startPercent = currentPos;
						range.widthPercent *= scale;
						currentPos += range.widthPercent;

						// 添加最小间隙
						currentPos += GAP_PERCENT;
					});
				}
			});
			// 缓存结果
			if (!isHovering) {
				this.timeSegmentsCache = result;
			} else {
				this.hoverLayoutCache = {
					index: this.hoveredUserIndex,
					data: result
				};
			}
			return result;
		},
		toSeconds(timeStr) {
			if (!/^\d{2}:\d{2}:\d{2}$/.test(timeStr)) {
				return 0;
			}
			const [h, m, s] = timeStr.split(':').map(Number);
			return h * 3600 + m * 60 + s;
		}
	}
};
</script>

<style scoped lang="scss">
.transcribe-file {
	padding: 23px 24px;
	height: 100%;
	background: #fcfcfc;
	border-radius: 0px 0px 0px 0px;

	&-content {
		height: 100%;
		background: #fafbfb;
		box-shadow: 0px -1px 20px 0px rgba(232, 234, 237, 0.4);
		border-radius: 16px 16px 16px 16px;
		display: flex;
		flex-direction: column;

		.header {
			background: #ffffff;
			border-radius: 12px 12px 0px 0px;
			padding: 10px 16px;
			border: 1px solid #d9e2ec;
			display: flex;
			justify-content: space-between;
			align-items: center;
			height: 56px;

			.back {
				display: flex;
				align-items: center;

				.title-text {
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
					font-size: 20px;
					color: #15224c;
					line-height: 22px;
				}
			}
		}

		.box {
			flex: 1;
			border: 1px solid #d9e2ec;
			border-top: none;
			display: flex;
			border-radius: 0px 0px 16px 16px;
			justify-content: space-between;
			overflow: hidden;

			&-left {
				width: 50%;
				padding: 24px 12px;
				border-radius: 0px 0px 0px 16px;
				flex: 1;
				display: flex;
				flex-direction: column;
				&-title {
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
					font-size: 24px;
					color: #000000;
					line-height: 32px;
					padding-bottom: 60px;
				}
				.box-left-box {
					flex: 1;
					overflow-y: auto;
				}
				.box-left-item {
					margin-bottom: 24px;
				}

				.user {
					display: flex;
					padding-left: 24px;
					align-items: center;

					i {
						font-size: 13px;
					}

					.user-name {
						margin: 0 6px;
						font-family: PingFang SC, PingFang SC;
						font-weight: 400;
						font-size: 14px;
						color: #2f446b;
						line-height: 32px;
					}

					.icon-edit {
						font-size: 16px;
					}

					.edit-name {
						color: #00a870;
						font-size: 22px;
					}
				}

				.line {
					margin-top: 10px;
					padding: 16px;
					background: #ffffff;
					border-radius: 9px 9px 9px 9px;
					border: 1px solid #d9e2ec;
					position: relative;

					.line-item {
						position: relative;
						width: 100%;
						height: 7px;
						background: #f3f6f6;
						border-radius: 17px 17px 17px 17px;
					}
				}
			}

			&-right {
				width: 50%;
				background: #ffffff;
				border-radius: 0px 0px 16px 0px;
				border-left: 1px solid #e3ebf2;
				padding: 24px 0px 24px 16px;
				display: flex;
				height: 100%;
				flex-direction: column;
				overflow: hidden;
				.box-right-box {
					overflow-y: auto;
					flex: 1;
				}

				.box-right-title {
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
					font-size: 24px;
					color: #000000;
					line-height: 32px;
					padding-bottom: 60px;
				}

				.box-right-item-edit {
					background: #ffffff;
					box-shadow: 0px -1px 11px 0px rgba(152, 222, 251, 0.4);
					border-radius: 6px 6px 6px 6px;
					border: 1px solid var(--brand-6);
				}

				.box-right-item-act {
					padding: 12px;
					margin-bottom: 12px;
					background: linear-gradient(90deg, #d5e3ff 0%, #e8f4ff 100%, #e5f3ff 100%);
					border-radius: 6px 6px 6px 6px;
					margin-right: 12px;
				}

				.box-right-item {
					padding: 12px;
					margin-right: 12px;
					margin-bottom: 12px;

					&-header {
						font-family: PingFang SC, PingFang SC;
						font-weight: 500;
						font-size: 14px;
						color: #737a94;
						line-height: 22px;
						display: flex;
						align-items: center;
						justify-content: space-between;

						.header-left {
							display: flex;
							align-items: center;
							gap: 8px; /* 可选：增加间距 */
						}
						.dec-edit {
							color: var(--brand-6);
						}

						span {
							margin-right: 8px;
						}
					}

					.dec {
						font-family: PingFang SC, PingFang SC;
						font-weight: 400;
						font-size: 16px;
						color: #15224c;
						line-height: 24px;
					}
				}
			}
		}
	}
}

::v-deep .is-plain {
	border: 1px solid var(--brand-6) !important;
	color: var(--brand-6) !important;
}

.transcribe-file {
	.time-block {
		position: absolute;
		height: 7px;
		top: 0;
		background-color: var(--brand-6);
		opacity: 0.7;
		border-radius: 5px;
		will-change: transform, opacity; /* 启用硬件加速 */
		transition: all 0.3s ease;
	}
	.block-0,
	.block-1,
	.block-2 {
		position: absolute;
		height: 7px;
		top: 0;
		border-radius: 5px;
		opacity: 0.7;
		transition: all 0.3s ease;
	}
	.block-0 {
		background-color: #409eff;
	}

	.block-1 {
		background-color: #67c23a;
	}

	.block-2 {
		background-color: #e6a23c;
	}
}

.edit-dec {
	color: #00a870;
	//font-size: 24px !important;
}

::v-deep.el-textarea__inner {
	border: none;
	padding: 0;
	font-size: 16px;
}
.box-left-file {
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 12px 0;
	margin-bottom: 24px;
	audio {
		width: 100%;
	}
}
.icon-shanchu-del {
	color: #f56c6c;
	font-size: 20px;
	margin-right: 12px;
}
.font-icon-css {
	font-size: 18px;
	margin-left: 6px;
}
.audio-wave-wrapper {
	width: 36px;
	vertical-align: middle;
	display: flex;
	align-items: center;
}

.audio-wave-animation {
	display: inline-flex;
	align-items: center;
	justify-content: space-between;
	width: 100%;
	height: 100%;

	.bar {
		display: block;
		width: 2px;
		margin-right: 0 !important;
		background-color: #14b354;
		border-radius: 2px;
		animation: wave 1s infinite ease-in-out;
		&:nth-child(1) {
			height: 4px;
			animation-delay: -0.4s;
		}
		&:nth-child(2) {
			height: 8px;
			animation-delay: -0.375s;
		}
		&:nth-child(3) {
			height: 5px;
			animation-delay: -0.35s;
		}
		&:nth-child(4) {
			height: 10px;
			animation-delay: -0.325s;
		}
		&:nth-child(5) {
			height: 12px;
			animation-delay: -0.3s;
		}
		&:nth-child(6) {
			height: 10px;
			animation-delay: -0.275s;
		}
		&:nth-child(7) {
			height: 5px;
			animation-delay: -0.25s;
		}
		&:nth-child(8) {
			height: 8px;
			animation-delay: -0.225s;
		}
		&:nth-child(9) {
			height: 4px;
			animation-delay: -0.2s;
		}
	}
}

@keyframes wave {
	0%,
	40%,
	100% {
		transform: scaleY(1);
	}
	20% {
		transform: scaleY(1.5);
	}
}
</style>
