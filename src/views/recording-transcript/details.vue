<template>
	<div v-loading="loading" class="details">
		<div class="box">
			<div class="header">
				<div class="back" @click="goBack">
					<svg-icon class="title-icon" icon-class="seal-left"></svg-icon>
					<span class="title-text">{{ detailsType === 2 ? '会议纪要' : '会议记录' }}</span>
				</div>
				<div class="btn">
					<el-button v-if="detailsType == 2" type="primary" @click="exportData('abstract')">
						<i class="coos-iconfont icon-downland"></i>
						下载会议纪要
					</el-button>
					<el-button v-else type="primary" @click="exportData('record')">
						<i class="coos-iconfont icon-downland"></i>
						下载会议记录
					</el-button>
				</div>
			</div>
			<div class="content">
				<div class="content-title">{{ title }}</div>
				<div class="content-box">
					<div class="content-box-title">会议时间</div>
					<div class="content-box-value">{{ details.meetingTime || '——————' }}</div>
				</div>
				<div v-if="detailsType == 2" class="content-box">
					<div class="content-box-title">参会人员</div>
					<div class="content-box-value">{{ details.meetingMember }}</div>
				</div>
				<div v-if="detailsType == 2" class="content-box flex">
					<div class="content-box-title">纪要内容</div>
					<div class="content-box-event">
						<div class="content-box-item">
							<div v-html="formattedText(details.contentAbstract)"></div>
						</div>
					</div>
				</div>
				<div v-if="detailsType == 3" class="content-box flex">
					<div class="content-box-title">记录内容</div>
					<div class="content-box-event">
						<div class="content-box-item">
							<div v-html="formattedText(details.contentRecord)"></div>
						</div>
						<!--						<div v-for="(item, index) in rightList" :key="index" class="content-box-item">-->
						<!--							<div class="content-box-item-header">-->
						<!--								<span>{{ item.date[0] }}</span>-->
						<!--								<span>{{ item.name }}</span>-->
						<!--							</div>-->
						<!--							<div class="content-box-item-dec">-->
						<!--								{{ item.dec }}-->
						<!--							</div>-->
						<!--						</div>-->
					</div>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import { meetingRecordDetails } from '@/api/modules/recording-transcript';
import { downloadFile } from '@/utils/down-load';

export default {
	name: 'Details',
	data() {
		return {
			id: '',
			userList: [],
			loading: false,
			title: '',
			details: {},
			rightList: [],
			detailsType: 2 // 2 会议纪要 3 会议记录
		};
	},
	created() {
		this.id = this.$route.query.id || '';
		this.detailsType = this.$route.query.type || 2;
		this.title = this.$route.query.title || '';
		this.getDetails(this.id);
	},
	methods: {
		getDetails() {
			this.loading = true;
			meetingRecordDetails(this.id).then(res => {
				this.loading = false;
				if (res.code === 200) {
					this.details = res.result || {};
					this.userList = res.result.contentTextInfo;
					const allSpeeches = [];
					this.userList.forEach(user => {
						user.list.forEach(speech => {
							allSpeeches.push({
								name: user.name,
								memberId: user.memberId,
								date: speech.date,
								id: speech.id,
								decEdit: false,
								dec: speech.dec
							});
						});
					});
					// 按时间排序（使用起始时间）
					this.rightList = allSpeeches.sort((a, b) => {
						const aStart = this.toSeconds(a.date[0]);
						const bStart = this.toSeconds(b.date[0]);
						return aStart - bStart;
					});
				} else {
					this.userList = [];
					this.$message.error(res.message);
				}
			});
		},
		exportData(optType) {
			downloadFile({
				url: `/api/robot/meetingRecord/exportContent`,
				params: {
					id: this.id,
					optType
				}
			});
		},
		formattedText(text) {
			if (text) {
				return text.replace(/\n/g, '<br>');
			}
			return '';
		},
		goBack() {
			this.$router.go(-1);
		},
		toSeconds(timeStr) {
			if (!/^\d{2}:\d{2}:\d{2}$/.test(timeStr)) {
				return 0;
			}
			const [h, m, s] = timeStr.split(':').map(Number);
			return h * 3600 + m * 60 + s;
		}
	}
};
</script>

<style scoped lang="scss">
.details {
	padding: 24px;
	background: #fcfcfc;
	border-radius: 0px 0px 0px 0px;
	height: 100%;
	.box {
		display: flex;
		flex-direction: column;
		height: 100%;
		.header {
			background: #ffffff;
			border-radius: 12px 12px 0px 0px;
			padding: 10px 16px;
			border: 1px solid #d9e2ec;
			display: flex;
			justify-content: space-between;
			align-items: center;
			height: 56px;

			.back {
				display: flex;
				align-items: center;

				.title-text {
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
					font-size: 20px;
					color: #15224c;
					line-height: 22px;
				}
			}
		}
		.content {
			flex: 1;
			padding: 32px 0 32px 32px;
			display: flex;
			overflow: hidden;
			flex-direction: column;
			border-radius: 0px 0px 16px 16px;
			border: 1px solid #d9e2ec;
			border-top: none;
			.content-title {
				font-family: PingFang SC, PingFang SC;
				font-weight: 500;
				font-size: 24px;
				color: #15224c;
				padding-bottom: 38px;
				line-height: 22px;
			}
			.content-box {
				padding-bottom: 24px;
				.content-box-title {
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
					font-size: 20px;
					color: #15224c;
					line-height: 22px;
					padding-bottom: 8px;
				}
				.content-box-value {
					font-family: PingFang SC, PingFang SC;
					font-weight: 400;
					font-size: 14px;
					color: #2f446b;
					line-height: 22px;
				}
				.content-box-item {
					font-family: PingFang SC, PingFang SC;
					font-weight: 400;
					font-size: 14px;
					color: #2f446b;
					line-height: 22px;
					padding: 12px 0;
				}
				.content-box-item-header {
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
					font-size: 14px;
					color: #737a94;
					line-height: 22px;
					margin-bottom: 8px;
					span {
						margin-right: 8px;
					}
				}
				.content-box-item-dec {
					font-family: PingFang SC, PingFang SC;
					font-weight: 400;
					font-size: 16px;
					color: #15224c;
					line-height: 24px;
				}
			}
		}
	}
}
.flex {
	display: flex;
	flex-direction: column;
	flex: 1;
	overflow: hidden;
	.content-box-event {
		display: flex;
		flex-direction: column;
		flex: 1;
		overflow-y: auto;
		padding-right: 24px;
	}
}
</style>
