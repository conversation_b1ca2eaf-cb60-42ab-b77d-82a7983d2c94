<template>
	<div class="workSetting">
		<render v-loading="loading" :list="layout"></render>
		<div v-if="tabs.length > 1" class="slide" :class="status ? 'openAnimation' : 'closeAnimation'">
			<div class="slide-btn" @click="changeOpen">
				<i class="coos-iconfont icon-rilishouqi slide-btn-icon" :class="{ open: !status }"></i>
			</div>
			<div class="slide-content">
				<div
					v-for="(item, index) of tabs"
					:key="index"
					:class="{ select: currentTab === item.title }"
					class="slide-content-item"
					@click="changeMode(item)"
				>
					{{ status ? item.title : '' }}
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import render from '@/components/config-page/render';
import { getConfigByModeId, getModeList } from '@/api/modules/work-setting';
export default {
	name: 'Index',
	components: {
		render
	},
	data() {
		return {
			status: false,
			layout: [],
			loading: false,
			id: '',
			currentTab: '',
			currentMode: {}, // 当前视图
			tabs: [] // 视图列表
		};
	},
	async mounted() {
		this.id = this.$route.query.id;
		this.getTabs();
	},
	methods: {
		/**改变状态*/
		changeOpen() {
			this.status = !this.status;
		},
		/**切换视图*/
		changeMode(item) {
			this.currentMode = item;
			this.currentTab = item.title;
			this.getConfig();
		},
		/**获取具体的视图配置*/
		getConfig() {
			this.layout = [];
			this.loading = true;
			getConfigByModeId('workbenchPc', this.currentMode.id, { applicationId: this.id }).then(
				res => {
					this.loading = false;
					if (res.code === 200) {
						this.layout = res.result.map(item => {
							let obj = JSON.parse(item.paramsJson);
							obj.key = 0; // 组件刷新
							return obj;
						});
					} else {
						this.$message.error(res.message);
					}
				}
			);
		},
		/**获取工作态视图列表*/
		getTabs() {
			//workbenchApp：移动端、workbenchPc：pc端
			getModeList({ objectType: 'workbenchPc' }).then(res => {
				if (res.code === 200) {
					this.tabs = res.result;
					this.currentMode = res.result[0] || {};
					this.currentTab = this.currentMode.title;
					this.getConfig();
				} else {
					this.$message.error(res.message);
				}
			});
		}
	}
};
</script>

<style scoped lang="scss">
.workSetting {
	height: 100%;
	background: rgba(255, 255, 255, 0.9);
}
::v-deep .render {
	margin: 0;
	height: 100%;
}
.slide {
	display: flex;
	align-items: center;
	position: fixed;
	z-index: 666;
	top: 50%;
	right: 0;
	transition: all 0.5s;
	&-btn {
		width: 14px;
		height: 24px;
		background: var(--brand-6);
		border-radius: 6px 0px 0px 6px;
		display: flex;
		align-items: center;
		cursor: pointer;
		justify-content: flex-end;
		&-icon {
			font-size: 16px;
			position: relative;
			z-index: 667;
			color: #ffffff;
			transform: rotateZ(90deg);
		}
		.open {
			transform: rotateZ(-90deg);
		}
	}
	&-content {
		background: #ffffff;
		box-shadow: -2px 0px 4px 0px var(--brand-1);
		border-radius: 9px 0px 0px 9px;
		padding: 12px 8px 8px;
		&-item {
			min-width: 80px;
			height: 26px;
			border-radius: 3px;
			background: #ffffff;
			font-weight: 400;
			font-size: 14px;
			color: $primaryTextColor;
			line-height: 26px;
			margin-bottom: 4px;
			text-align: center;
			cursor: pointer;
		}
		.select {
			color: var(--brand-6);
			background: var(--brand-1);
		}
	}
}
.closeAnimation {
	transform: translateX(90px);
}
.openAnimation {
	transform: translateX(0);
}
</style>
