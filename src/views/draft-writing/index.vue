<template>
	<div
		v-loading="loading"
		class="page"
		:style="{
			backgroundImage: `url(${require(`@/assets/${rentThem}/write/write-bg.png`)})`
		}"
	>
		<step1
			v-show="current === 1"
			ref="step1"
			:perms-code="permsCode"
			:writee-type="writeeType"
			:history="history"
			:write-setting-info="writeSettingInfo"
			:knowledge-arr="knowledgeArr"
			:has-next="hasNext"
			:version="version"
			@pauseData="pauseData"
			@changeCurrent="changeCurrent"
			@requestOutlineData="requestOutlineData"
			@quicklyGenerateFullText="quicklyGenerateFullText"
		></step1>
		<step2
			v-show="current === 2"
			ref="step2"
			:history="history"
			:is-history="isHistory"
			:write-setting-info="writeSettingInfo"
			:knowledge-arr="knowledgeArr"
			:doc-title="title"
			:current="current"
			:version="version"
			@changeTitle="changeTitle"
			@proOutline="proOutline"
			@changeCurrent="changeCurrent"
		></step2>
		<step3
			v-show="current === 3"
			ref="step3"
			:current="current"
			:writee-type="writeeType"
			:is-history="isHistory"
			:knowledge-arr="knowledgeArr"
			:doc-title="title"
			:version="version"
			@changeTitle="changeTitle"
			@updateOutline="updateOutline"
			@changeCurrent="changeCurrent"
		></step3>
		<TipPopup ref="TipPopup"></TipPopup>
	</div>
</template>
<script>
import step1 from '@/views/draft-writing/components/step1.vue';
import step2 from '@/views/draft-writing/components/step2.vue';
import step3 from '@/views/draft-writing/components/step3.vue';
import { getMemberList } from '@/api/modules/common';
import { getKnowledge } from '@/api/modules/coos';
import { getSourceDetail } from '@/api/modules/coos-write';
import { getProofreadDetail } from '@/api/modules/error-correction';
import TipPopup from '@/views/draft-writing/components/tip-popup.vue';
import { CoosEventTypes } from '@/utils/bus';
export default {
	name: 'DraftWriting',
	components: {
		step1,
		step2,
		step3,
		TipPopup
	},
	data() {
		return {
			permsCode: '',
			version: 'V2',
			history: [],
			writeSettingInfo: {},
			title: '', // 标题
			current: 1, // 当前步骤
			hasNext: false,
			loading: false,
			clickHistory: false, // 点击历史
			knowledgeArr: [],
			isHistory: false, // 是否历史记录
			writeeType: '' // 区分深度写文还是快速写文
		};
	},
	watch: {
		current(newVal) {
			this.$refs.step2.cale();
			this.$refs.step3.cale();
		}
	},
	created() {
		if (this.$route.query.id) {
			this.current = 3;
			if (this.$route.query.pathType === 'error') {
				this.getProofreadDetail(this.$route.query.id);
				return;
			}
			this.getDetail(this.$route.query.id);
		}
		this._BUS.$emit(CoosEventTypes.aiSessionParams, {
			clickCallback: () => {
				this.$refs.TipPopup.open();
			}
		});
	},
	mounted() {
		this.permsCode = this.$route.query.permsCode || '';
		this.version = this.$route.query.version || 'V2';
		this.getKnowledge();
		this.getConfig();
	},
	methods: {
		/**获取纠错详情*/
		getProofreadDetail(id) {
			getProofreadDetail(id).then(res => {
				if (res.code === 200) {
					let data = {
						...res.result,
						content: res.result.text
					};

					this.changeCurrent(3, data);
				}
			});
		},

		/**获取数据详情*/
		getDetail(id) {
			this.loading = true;
			getSourceDetail(id).then(res => {
				if (res.code === 200) {
					if (res.result.content) {
						this.changeCurrent(3, res.result);
					} else {
						this.changeCurrent(2, res.result);
					}
				} else {
					this.$message.error(res.message);
				}
				this.loading = false;
			});
		},
		/**获取知识库*/
		getKnowledge() {
			getKnowledge().then(res => {
				if (res.code === 200) {
					this.knowledgeArr = res.result || [];
					this.$nextTick(() => {
						this.$refs.step1.initKnowledge();
					});
				} else {
					this.$message.error(res.message);
				}
			});
		},
		/**获取配置*/
		getConfig() {
			getMemberList('A1013').then(res => {
				if (res.code === 200) {
					let config = JSON.parse(res.result.draftWriting);
					this.history = config.exampleTitle;
				} else {
					this.$message.error(res.message);
				}
			});
		},
		/**更新大纲*/
		updateOutline(data) {
			this.$refs.step2.updateOutline(data);
		},
		/**当删除的时候暂,如果是正在生成的文章，停生成操作*/
		pauseData(id) {
			this.$refs[this.current === 2 ? 'step2' : 'step3'].delPause(id);
		},
		/**
		 * @Description 快速生成全文
		 * @Param  {Boolean} isJumpOutline 是否跳过大纲
		 * */
		quicklyGenerateFullText(data, isJumpOutline = false) {
			this.title = '';
			this.writeeType = 'quickly';
			//  生成大纲时候 屏蔽操作按钮 、 大纲智能问答、 暂停、 生成完成后直接跳转到全文
			if (this.version === 'V2' || isJumpOutline) {
				this.current = 3;
				this.$refs.step3.generateFullText(data);
			} else {
				this.current = 2;
				this.$refs.step2.requestOutlineData(data, false);
			}
		},
		/**
		 * @Descript 保存大纲
		 * @Param  {String,Number}  id 文章id
		 * @Param  {Object}  data 大纲对象
		 * */
		proOutline(id, data) {
			this.$refs.step3.proOutline(id, data);
		},
		/**改变步骤*/
		changeCurrent(current, data) {
			// 从其他页面进入首页，那就有下一步
			if (current === 1) {
				this.$refs.step1.updateList();
				if (this.clickHistory) {
					this.clickHistory = false;
				} else {
					this.hasNext = true;
				}
			}
			// 是不是从历史点击赋值的数据
			if (data) {
				this.writeeType = data.answerModel === 'domain' ? 'deepWrite' : 'quickly';
				this.clickHistory = true;
				this.title = data.title;
				// 根据历史存储是大纲还是全文，打开对应的步骤
				if (current === 2 && data) {
					this.$refs.step2.initData(data);
				} else if (current === 3 && data) {
					this.$refs.step3.initData(data);
				}
				this.isHistory = true;
			} else {
				this.isHistory = false;
			}
			this.current = current;
		},
		/**
		 * @Descript 深度写文
		 * @Param  {Object} data 配置对象
		 * */
		requestOutlineData(data) {
			this.writeeType = 'deepWrite';
			this.writeSettingInfo = data;
			this.title = data.title;
			// 跳过大纲（第二版本没有跳过大纲的需求）
			if (data.whetherToSkipOrNot) {
				// TODO 跳过大纲的操作
				this.$refs.step3.proOutline('', data, false);
			} else {
				this.$refs.step2.requestOutlineData(data);
			}
		},
		changeTitle(title) {
			this.title = title;
		}
	}
};
</script>
<style scoped lang="scss">
.page {
	width: 100%;
	height: 100%;
	background-size: 100% 100%;
}
</style>
