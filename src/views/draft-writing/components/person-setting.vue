<template>
	<el-dialog
		title="设置"
		:visible.sync="visible"
		class="desk-el-custom-dialog"
		top="0"
		:before-close="cancel"
	>
		<el-form
			ref="form"
			v-loading="loading"
			class="form"
			:model="form"
			label-width="180px"
			:rules="rules"
		>
			<el-form-item label="数据底座" prop="originDataFoundation" class="form-item">
				<el-switch v-model="form.originDataFoundation"></el-switch>
			</el-form-item>
			<el-form-item label="知识库" prop="originMetadata" class="form-item">
				<el-switch v-model="form.originMetadata"></el-switch>
			</el-form-item>
			<el-form-item label="全文缓存" prop="contentDrawsHistory" class="form-item">
				<el-switch v-model="form.contentDrawsHistory"></el-switch>
			</el-form-item>
			<el-form-item label="跳过大纲" prop="skipOutlineGen" class="form-item">
				<el-switch v-model="form.skipOutlineGen"></el-switch>
			</el-form-item>
		</el-form>
		<div slot="footer">
			<el-button @click="handleClose">取消</el-button>
			<el-button v-loading="saveLoading" type="primary" @click="handleSave">保存</el-button>
		</div>
	</el-dialog>
</template>
<script>
import { getSettings, saveSettings } from '@/api/modules/coos-write';
import { deepClone } from '@/utils';

export default {
	name: 'PersonSetting',
	data() {
		return {
			visible: false,
			loading: false,
			saveLoading: false,
			orgForm: {
				originDataFoundation: true, // 是否从数据底座检索参考
				originMetadata: true, //是否从知识库检索参考
				contentDrawsHistory: false, //生成内容是否借鉴历史
				skipOutlineGen: false // 长文撰写是否跳过大纲},
			},
			form: {},
			rules: {}
		};
	},
	watch: {
		visible(val) {
			if (val) {
				this.form = deepClone(this.orgForm);
				this.getSetting();
			}
		}
	},
	methods: {
		/**获取设置*/
		getSetting() {
			if (this.$route.query.pathType !== 'error') {
				this.loading = true;
				getSettings().then(res => {
					if (res.code === 200) {
						Object.keys(this.form).forEach(key => {
							this.form[key] = res.result[key] || this.form[key];
						});
						this.$emit('getSettings', deepClone(this.form));
						this.loading = false;
					} else {
						this.$message.error(res.message);
					}
				});
			}
		},
		/**打开*/
		open() {
			this.visible = true;
		},
		/**关闭*/
		cancel() {
			this.visible = false;
		},
		/**关闭*/
		handleClose() {
			this.visible = false;
		},
		/**保存*/
		handleSave() {
			if (this.saveLoading) return;
			this.saveLoading = true;
			saveSettings(this.form).then(res => {
				this.saveLoading = false;
				if (res.code === 200) {
					this.visible = false;
					this.$message.success('保存成功！');
					this.$emit('getSettings', deepClone(this.form));
				} else {
					this.$message.error(res.message);
				}
			});
		}
	}
};
</script>
<style scoped lang="scss">
.form {
	display: flex;
	flex-wrap: wrap;
}
.form-item {
	width: 50%;
}
</style>
