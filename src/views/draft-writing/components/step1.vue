<template>
	<div class="page">
		<div class="welcome">
			<i class="coos-iconfont home-icon icon-home" title="首页" @click="toHome"></i>
			<i
				v-if="version === 'V3'"
				class="coos-iconfont icon-shezhi1 setting-icon"
				title="设置"
				@click="openPersonSetting"
			></i>
			<div class="logo">
				<img class="logo-bg" src="@/assets/images/write/write-logo.png" alt="" />
				<img class="logo-ai" :src="require(`@/assets/${rentThem}/write/write-ai.png`)" alt="" />
			</div>
			<div class="title">{{ extend.title || `您好，我是${modeConfig.name || '写作助手'}` }}</div>
			<div class="sub-title">{{ modeConfig.digest || '我可以帮助您进行文档总结、报告生成' }}</div>
			<div class="message-input">
				<div class="border-content">
					<div class="input-content">
						<el-input
							v-model="form.answerQuestion"
							type="textarea"
							:autosize="{ minRows: 3, maxRows: 3 }"
							resize="none"
							class="textarea"
							:placeholder="modeConfig.prologue || '帮我写一篇文章'"
							@keydown.native="generateFullText"
						></el-input>
						<div class="input-content-bottom">
							<div class="input-content-bottom-left">
								<div
									v-if="version === 'V2'"
									class="network"
									:class="{ select: settingStatus }"
									@click="openWriteSetting"
								>
									<i class="coos-iconfont network-icon icon--logo"></i>
									<span class="network-text">深度写稿</span>
								</div>
								<div
									v-if="version === 'V2'"
									class="network"
									:class="{ select: form.notFromMeta }"
									@click="selectNotFromMeta"
								>
									<i class="coos-iconfont network-icon icon-zhuanxie"></i>
									<span class="network-text">通用写稿</span>
								</div>
								<div class="network" :class="{ select: form.answerFormWeb }" @click="selectNetwork">
									<i class="coos-iconfont network-icon icon-lianwangsousuo"></i>
									<span class="network-text">联网搜索</span>
								</div>
								<div class="line"></div>
								<uploadFile
									v-show="contentDemoFileUrl.length === 0"
									ref="upload"
									:value="form.contentDemoFileUrl"
									:disabled="contentDemoFileUrl > 0"
									:always-show-upload-button="true"
									:limit="1"
									:multiple="false"
									:custom-file="true"
									:accept="['image', 'xlsx', 'doc', 'docx', 'pdf', 'txt']"
									@onProcess="onProcess"
									@change="change"
								>
									<div slot="custom-button" style="display: flex">
										<img
											src="@/assets/images/write/file-upload.png"
											class="upload-icon"
											alt=""
											title="上传文件"
											@click="changeAccept('file')"
										/>
										<!--										<img-->
										<!--											src="@/assets/images/write/image-upload.png"-->
										<!--											class="upload-icon"-->
										<!--											alt=""-->
										<!--											title="上传图片"-->
										<!--											@click="changeAccept('image')"-->
										<!--										/>-->
									</div>
								</uploadFile>
								<div v-show="contentDemoFileUrl.length > 0" class="preview-file">
									<div
										v-for="(item, index) of contentDemoFileUrl"
										:key="index"
										class="file"
										@click="preview(item)"
									>
										<div class="file-content">
											<svg-icon :icon-class="item.cover" class="cover"></svg-icon>
											<div class="file-name">{{ item.name }}</div>
											<div class="file-size">{{ tabSize(item.size) }}</div>
											<i class="coos-iconfont file-icon icon-shanchu" @click="del(index)"></i>
											<el-progress
												v-if="item.status === 'fail'"
												class="process"
												:percentage="item.process"
												status="exception"
											></el-progress>
											<el-progress
												v-else-if="item.process < 100"
												class="process"
												:percentage="item.process"
												status="success"
											></el-progress>
										</div>
									</div>
								</div>
							</div>
							<div class="right-buttons">
								<div
									v-if="version === 'V3'"
									class="input-content-bottom-right"
									:class="{ 'disabled-button': settingStatus || !form.answerQuestion }"
									@click="proOutline"
								>
									<i class="coos-iconfont icon-dagang send-icon"></i>
									<span class="send-text">大纲写作</span>
								</div>
								<div
									class="input-content-bottom-right"
									:class="{ 'disabled-button': settingStatus || !form.answerQuestion.trim() }"
									@click="generateFullText($event, true)"
								>
									<i class="coos-iconfont icon-fasong send-icon"></i>
									<span class="send-text">
										{{ version === 'V3' && settings.skipOutlineGen ? '通用写作' : '生成全文' }}
									</span>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="history-p">
				<div v-if="historyList.length" class="history">
					<img
						:src="require(`@/assets/${rentThem}/write/history-title.png`)"
						class="history-title"
					/>
					<el-table
						:key="historyList.length"
						v-loading="loading"
						:row-style="rowStyle"
						:header-row-style="headerRowStyle"
						:data="historyList"
						height="auto"
						style="width: 100%"
					>
						<el-table-column label="名称" show-overflow-tooltip>
							<template slot-scope="{ row }">
								<el-input
									v-if="historyEditItem === row.id"
									v-model="orginInputContent"
									placeholder="请输入标题"
								>
									<div slot="suffix" class="suffix-content">
										<i
											class="coos-iconfont icon-chehui suffix-content-icon"
											@click="backInput(row)"
										></i>
										<i
											v-loading="editLoading"
											class="coos-iconfont icon-baocun suffix-content-icon"
											@click="saveInput(row)"
										></i>
									</div>
								</el-input>
								<div v-else class="history-item-title">
									<svg-icon icon-class="word" class="history-item-svg"></svg-icon>
									<div class="history-item-text" @click="getDetail(row.id)">
										{{ row.title || '未命名文档' }}
									</div>
									<i v-show="loadingId === row.id" class="el-icon-loading loading-icon"></i>
								</div>
							</template>
						</el-table-column>
						<el-table-column label="类型" prop="answerModelDesc" width="140"></el-table-column>
						<el-table-column label="创作时间" width="180" prop="createTime"></el-table-column>
						<el-table-column label="操作" width="80">
							<template slot-scope="{ row }">
								<el-popconfirm
									confirm-button-text="确定"
									cancel-button-text="取消"
									icon="el-icon-info"
									title="确定删除吗？"
									@confirm="handle('del', row)"
								>
									<i slot="reference" class="coos-iconfont history-item-icon icon-trash"></i>
								</el-popconfirm>
								<el-popover popper-class="write-step1-popper" placement="top" trigger="click">
									<i slot="reference" class="coos-iconfont history-item-icon icon-more2"></i>
									<div class="history-handle">
										<div
											v-for="util in utils"
											:key="util.type"
											class="history-handle-item"
											@click="handle(util.type, row)"
										>
											<span>{{ util.label }}</span>
											<i
												v-if="
													util.type !== 'editTitle' &&
													loadingMap[util.type + 'Loading'].includes(row.id)
												"
												class="el-icon-loading loading-icon"
											></i>
										</div>
									</div>
								</el-popover>
							</template>
						</el-table-column>
					</el-table>
				</div>
			</div>
		</div>
		<transition name="fade">
			<writeSetting ref="writeSetting" v-bind="$attrs" v-on="$listeners">
				<div slot="title" class="setting-title">
					<div class="setting-title-left">
						<div class="main-title">深度写稿</div>
						<div class="desc-title">借鉴参考文档进行稿件撰写</div>
					</div>
					<i class="setting-title-right coos-iconfont icon-guanbi1" @click="close"></i>
				</div>
			</writeSetting>
		</transition>
		<personSetting ref="personSetting" @getSettings="getSettings"></personSetting>
	</div>
</template>
<script>
import writeSetting from '@/views/draft-writing/components/write-setting.vue';
import { previewFile, tabSize, urlHttp } from '@/utils';
import { getHistory } from '@/api/modules/coos-znwz';
import { deleteDoc, getSourceDetail, saveOutline } from '@/api/modules/coos-write';
import { downloadFile } from '@/utils/down-load';
import { mapGetters } from 'vuex';
import personSetting from '@/views/draft-writing/components/person-setting.vue';
export default {
	name: 'Step1',
	components: {
		writeSetting,
		personSetting
	},
	props: {
		permsCode: {
			type: String,
			default: () => {
				return '';
			}
		},
		writeSettingInfo: {
			type: Object,
			default: () => {
				return {};
			}
		},
		version: {
			type: String,
			default: () => {
				return 'V2';
			}
		}
	},
	data() {
		return {
			settings: {}, // 个人用户设置
			loadingId: '',
			loadingMap: {
				wordLoading: [],
				pdfLoading: []
			},
			orginInputContent: '', // 输入框改变之前的内容
			historyEditItem: '', // 正在编辑的输入框
			utils: [
				{
					label: '导出word',
					type: 'word'
				},
				{
					label: '导出pdf',
					type: 'pdf'
				},
				{
					label: '重命名',
					type: 'editTitle'
				}
			],
			loading: false,
			params: {
				pageNo: 1,
				pageSize: -1
			},
			total: 0,
			accept: [],
			form: {
				answerModel: 'fast',
				answerQuestion: '',
				answerFormWeb: false, // 联网
				contentDemoFileUrl: '', // 文档
				notFromMeta: false // 是否通用写作
			},
			contentDemoFileUrl: [],
			historyList: [],
			settingStatus: false, // 长文写作设置的状态
			content: '', //输入框的内容
			rowStyle: {
				'font-size': '14px',
				color: '#2F446B',
				'line-height': '22px',
				padding: '17px 0'
			},
			headerRowStyle: {
				'font-weight': '500',
				'font-size': '14px',
				color: '#2F446B',
				'line-height': '22px'
			},
			editLoading: false
		};
	},
	computed: {
		...mapGetters(['aiEnterList']),
		modeConfig() {
			return this.aiEnterList.find(item => item.permsCode === this.permsCode) || {};
		},
		extend() {
			return this.modeConfig.extend ? JSON.parse(this.modeConfig.extend) : {};
		}
	},
	watch: {
		writeSettingInfo: {
			deep: true,
			handler(newVal, oldVal) {
				this.version === 'V2' && this.$refs.writeSetting.open(newVal);
			}
		}
	},
	mounted() {
		this.getHistory();
	},
	methods: {
		tabSize,
		/**获取设置*/
		getSettings(settings) {
			this.settings = settings;
		},
		/**打开个人设置*/
		openPersonSetting() {
			this.$refs.personSetting.open();
		},
		/**生成大纲（第二版本，没有深度写稿，这里改参数生成大纲）*/
		proOutline() {
			if (!this.form.answerQuestion) return;
			let data = { ...this.form };
			delete data.answerModel;
			// this.settings.skipOutlineGen是否跳过大纲
			this.$emit('requestOutlineData', data);
			this.$emit('changeCurrent', 2);
		},
		/**初始化默认选中知识库*/
		initKnowledge() {
			this.$refs.writeSetting.initKnowledge();
		},
		backInput(row) {
			if (this.editLoading) {
				this.$message.warning('保存中，请勿频繁操作！');
				return;
			}
			this.historyEditItem = '';
		},
		/**保存标题*/
		saveInput(row) {
			if (this.editLoading) {
				this.$message.warning('保存中，请勿频繁操作！');
				return;
			}
			if (!this.orginInputContent) {
				this.$message.warning('标题不能为空！');
				return;
			}
			this.editLoading = true;
			saveOutline(
				{
					saveType: 'title',
					title: this.orginInputContent
				},
				row.id
			).then(res => {
				this.editLoading = false;
				this.historyEditItem = '';
				if (res.code === 200) {
					this.updateList();
					this.$message.success('保存成功！');
				} else {
					this.$message.error(res.message);
				}
			});
		},
		/**选择通用写作*/
		selectNotFromMeta() {
			this.form.notFromMeta = !this.form.notFromMeta;
			if (this.form.notFromMeta) {
				this.form.answerFormWeb = false;
				this.settingStatus = false;
				if (this.form.contentDemoFileUrl) {
					this.del(0);
				}
			}
		},
		/**选择网络*/
		selectNetwork() {
			this.form.answerFormWeb = !this.form.answerFormWeb;
			if (this.form.answerFormWeb) {
				this.form.notFromMeta = false;
				this.settingStatus = false;
				if (this.form.contentDemoFileUrl) {
					this.del(0);
				}
			}
		},
		/**更新历史*/
		updateList() {
			this.params.pageNo = 1;
			this.getHistory();
		},
		/**操作*/
		handle(type, row) {
			switch (type) {
				case 'del':
					this.loading = true;
					deleteDoc([row.id]).then(res => {
						this.loading = false;
						if (res.code === 200) {
							this.$emit('pauseData', row.id);
							this.updateList();
							this.$message.success('操作成功！');
						} else {
							this.$message.error(res.message);
						}
					});
					break;
				case 'editTitle':
					this.orginInputContent = row.title;
					this.historyEditItem = row.id;
					break;
				case 'word':
					this.downloadFile(row, 'word');
					break;
				case 'pdf':
					this.downloadFile(row, 'pdf');
					break;
			}
		},
		/**下载*/
		downloadFile(row, type) {
			this.loadingMap[type + 'Loading'].push(row.id);
			downloadFile({
				url: '/api/robot/writeManuscript/export',
				params: {
					type,
					// html: '',
					id: row.id,
					title: row.title
				},
				reqFn: true,
				success: () => {
					this.loadingMap[type + 'Loading'] = this.loadingMap[type + 'Loading'].filter(item => {
						return item !== row.id;
					});
					this.$message.success('导出成功！');
				},
				fail: error => {
					this.loadingMap[type + 'Loading'] = this.loadingMap[type + 'Loading'].filter(
						item => item !== row.id
					);
					this.$message.error(error);
				}
			});
		},
		/**获取来源*/
		getDetail(id) {
			this.loadingId = id;
			getSourceDetail(id).then(res => {
				this.loadingId = '';
				if (res.code === 200) {
					if (res.result.content) {
						this.$emit('changeCurrent', 3, res.result);
					} else {
						this.$emit('changeCurrent', 2, res.result);
					}
				} else {
					this.$message.error(res.message);
				}
			});
		},
		/**获取历史记录*/
		getHistory() {
			this.loading = true;
			getHistory(this.params).then(res => {
				this.loading = false;
				if (res.code === 200) {
					this.historyList = res.result.records;
					this.total = res.result.total;
				} else {
					this.$message.error(res.message);
				}
			});
		},
		/**去首页*/
		toHome() {
			this.$router.push('/');
		},
		changeAccept(type) {
			this.accept = type === 'image' ? [type] : ['xlsx', 'doc', 'docx', 'pdf', 'txt'];
		},
		/**删除*/
		del(index) {
			this.$refs.upload.del(index);
			this.form.contentDemoFileUrl = '';
			this.contentDemoFileUrl = '';
		},
		/**预览*/
		preview(item) {
			previewFile(urlHttp(item.url));
		},
		/**上传文件重新赋值*/
		change(ids) {
			this.$set(this.form, 'contentDemoFileUrl', ids);
		},
		/**
		 * @Description 上传监听进度的数据
		 * @Param {Array} showPreviewArr 上传的文件数据
		 * */
		onProcess(showPreviewArr) {
			if (showPreviewArr.length) {
				this.form.answerFormWeb = false;
				this.form.notFromMeta = false;
				this.settingStatus = false;
			}
			this.contentDemoFileUrl = showPreviewArr.map(item => {
				return {
					...item,
					process:
						item.process === 'success'
							? 100
							: typeof item.process === 'number'
							? item.process
							: Number(item.process.replace('%', ''))
				};
			});
		},
		generateFullText(e, click = false) {
			// 单独按 Enter - 发送消息
			if (click || (e.key === 'Enter' && !e.ctrlKey && !e.shiftKey)) {
				e.preventDefault(); // 阻止默认的换行行为
				if (this.settingStatus || !this.form.answerQuestion) {
					if (this.settingStatus && this.form.answerQuestion) {
						this.$message.warning('请先关闭深度写稿！');
					}
					return;
				}
				// 是否跳过大纲
				this.$emit(
					'quicklyGenerateFullText',
					this.form,
					this.version === 'V3' && this.settings.skipOutlineGen
				);
			}
		},
		/**关闭长文写作的设置*/
		close() {
			this.settingStatus = false;
			this.$refs.writeSetting.close();
		},
		/**打开长文写作的设置*/
		openWriteSetting() {
			if (!this.settingStatus) {
				this.form.answerFormWeb = false;
				this.form.notFromMeta = false;
				if (this.form.contentDemoFileUrl) {
					this.del(0);
				}
				this.settingStatus = true;
				this.$refs.writeSetting.open();
			} else {
				this.settingStatus = false;
				this.$refs.writeSetting.close();
			}
		}
	}
};
</script>
<style scoped lang="scss">
.page {
	display: flex;
	width: 100%;
	height: 100%;
}
.welcome {
	flex: 1;
	position: relative;
	display: flex;
	flex-direction: column;
	align-items: center;
}
.home-icon {
	font-size: 20px;
	top: 36px;
	left: 40px;
	position: absolute;
	cursor: pointer;
}
.logo {
	margin-top: 53px;
	width: 403.98px;
	height: 195px;
	position: relative;
	display: flex;
	align-items: center;
	justify-content: center;
	&-bg {
		height: 100%;
		width: 100%;
		position: absolute;
		top: 0;
		left: 0;
	}
	&-ai {
		width: 160px;
	}
}
.title {
	font-weight: 600;
	font-size: 36px;
	color: #15224c;
	line-height: 22px;
	text-align: center;
	font-style: normal;
	text-transform: none;
	margin: -12px 0 30px;
}
.sub-title {
	font-weight: 400;
	font-size: 20px;
	color: #737a94;
	line-height: 22px;
	font-style: normal;
	text-transform: none;
	margin-bottom: 52px;
}

.message-input {
	border-radius: 16px;
	max-width: 880px;
	width: 70%;
	padding: 20px;
	background: #ffffff;
	box-shadow: 0px 0px 30px 0px rgba(83, 136, 166, 0.2);
}
.border-content {
	width: 100%;
	padding: 1px;
	//height: 110px;
	overflow: hidden;
	border-radius: 16px;
	background: linear-gradient(180deg, rgba(194, 161, 255, 1), rgba(140, 192, 255, 1));
}
.input-content {
	border-radius: 16px;
	padding: 18px 20px;
	background: #ffffff;
	&-bottom {
		display: flex;
		align-items: center;
		margin-top: 8px;
		&-left {
			display: flex;
			flex: 1;
			overflow: hidden;
			align-items: center;
			margin-right: 8px;
		}
		&-right {
			flex-shrink: 0;
			padding: 5px 15px;
			background: #2591f7;
			box-shadow: 0px 3px 4px 0px rgba(186, 219, 255, 0.8);
			border-radius: 6px;
			color: #ffffff;
			cursor: pointer;
			.send-icon {
				font-size: 16px;
				margin-right: 4px;
			}
			.send-text {
				font-weight: 400;
				font-size: 14px;
				line-height: 22px;
			}
		}
	}
	.network {
		margin-right: 8px;
		border-radius: 6px;
		border: 1px solid #e7edf5;
		color: #2f446b;
		padding: 6px;
		cursor: pointer;
		&-icon {
			font-size: 16px;
			margin-right: 2px;
		}
		&-text {
			font-weight: 400;
			font-size: 12px;
			line-height: 22px;
		}
	}
	.select {
		color: #2591f7;
		border-color: #2591f7;
	}

	.line {
		height: 16px;
		background: #d9e2ec;
		width: 1px;
	}
	.upload-icon {
		width: 20px;
		height: 20px;
		margin-left: 8px;
		cursor: pointer;
	}
}
.textarea {
	::v-deep .el-textarea__inner {
		border: none;
		&:focus {
			box-shadow: none;
		}
	}
}
.input-bottom {
	margin-top: 16px;
	display: flex;
	.mode {
		background: linear-gradient(95deg, #e3f8ff 0%, #cbe2ff 100%);
		border-radius: 6px;
		padding: 4px 6px;
		font-weight: 400;
		font-size: 12px;
		color: #15224c;
		line-height: 20px;
		cursor: pointer;
		display: flex;
		align-items: center;
		&-icon {
			height: 20px;
			width: 20px;
			margin-right: 2px;
		}
	}
}
.disabled-button {
	cursor: not-allowed;
	background: $holderTextColor;
	box-shadow: none;
}
.setting-title {
	padding: 14px 16px;
	display: flex;
	align-items: center;
	justify-content: space-between;
	border-bottom: 1px solid #e3ebf2;
	&-left {
		display: flex;
		align-items: center;
		.main-title {
			font-weight: 600;
			font-size: 20px;
			color: #15224c;
			line-height: 23px;
			margin-right: 12px;
		}
		.desc-title {
			font-weight: 400;
			font-size: 14px;
			color: #737a94;
			line-height: 22px;
		}
	}
	&-right {
		font-size: 16px;
		cursor: pointer;
		color: #606266;
	}
}
.file {
	display: inline-block;
	max-width: 100%;
	&-content {
		width: 100%;
		border: 1px solid #e3ebf2;
		display: flex;
		align-items: center;
		position: relative;
		background: #ffffff;
		border-radius: 6px;
		padding: 4px 8px;
	}
	.process {
		position: absolute;
		bottom: -16px;
		left: 18px;
		width: 100%;
	}
	.file-left {
		display: flex;
		align-items: center;
	}
	.cover {
		width: 24px;
		height: 24px;
		margin-right: 8px;
	}
	&-name {
		flex: 1;
		font-weight: 400;
		font-size: 12px;
		color: #15224c;
		line-height: 22px;
		@include aLineEllipse;
		&:hover {
			&::after {
				z-index: 555;
				content: '预览';
				width: calc(100% + 2px);
				height: calc(100% + 2px);
				display: flex;
				align-items: center;
				justify-content: center;
				background: rgba(0, 0, 0, 0.4);
				color: #ffffff;
				border-radius: 4px;
				position: absolute;
				top: -1px;
				left: -1px;
				cursor: pointer;
			}
		}
	}
	&-size {
		font-weight: 400;
		font-size: 10px;
		color: #737a94;
		line-height: 22px;
		margin: 0 12px;
	}
	&-icon {
		font-size: 16px;
		color: #737a94;
		cursor: pointer;
	}
}
.history-p {
	flex: 1;
	overflow: hidden;
	max-width: 880px;
	width: 70%;
	margin: 24px 0;
}
.history {
	background: #ffffff;
	border-radius: 16px;
	max-height: 100%;
	overflow: auto;
	width: 100%;
	padding: 20px;
	display: flex;
	flex-direction: column;
	&-title {
		width: 108px;
		height: 24px;
		margin-bottom: 8px;
		color: #2f446b;
	}
	&-item {
		&-svg {
			height: 24px;
			width: 24px;
			margin-right: 8px;
		}
		&-icon {
			margin-left: 8px;
			font-size: 16px;
			color: #2f446b;
			cursor: pointer;
		}
		&-title {
			cursor: pointer;
			display: flex;
			align-items: center;
			font-weight: 400;
			font-size: 14px;
			color: #2f446b;
			line-height: 22px;
		}
		&-text {
			flex: 1;
			@include aLineEllipse;
		}
	}
}
.history-handle {
	border-radius: 6px;
	&-item {
		padding: 8px 20px;
		cursor: pointer;
		&:hover {
			background: #ecf5ff;
		}
	}
}
.suffix-content {
	display: flex;
	align-items: center;
	&-icon {
		cursor: pointer;
		font-size: 16px;
		margin-left: 8px;
		color: $primaryTextColor;
		line-height: 32px;
	}
}
.loading-icon {
	font-size: 16px;
	margin-left: 8px;
	color: var(--brand-6);
}
.right-buttons {
	display: flex;
	align-items: center;
	gap: 8px;
}
.setting-icon {
	position: absolute;
	right: 40px;
	top: 36px;
	cursor: pointer;
	font-size: 20px;
}
.preview-file {
	flex: 1;
	overflow: hidden;
	padding-left: 8px;
}
</style>
<style>
.write-step1-popper {
	border-radius: 12px !important;
	padding: 0 !important;
}
</style>
