<template>
	<el-dialog
		title="说明"
		:visible.sync="visible"
		class="desk-el-custom-dialog"
		top="0"
		:before-close="cancel"
		width="60%"
	>
		<div v-loading="loading" class="content">
			<v-md-preview :text="tipContent"></v-md-preview>
		</div>
		<div slot="footer">
			<el-button @click="handleClose">关闭</el-button>
		</div>
	</el-dialog>
</template>
<script>
import { getConfigJson } from '@/api/modules/coos-write';

export default {
	name: 'TipPopup',
	data() {
		return {
			tipContent: '', // 提示信息
			visible: false,
			loading: false
		};
	},
	mounted() {
		this.getConfigJson();
	},
	methods: {
		/**获取配置*/
		getConfigJson() {
			this.loading = true;
			getConfigJson('draftWritingDescribe').then(res => {
				this.loading = false;
				if (res.code === 200) {
					this.tipContent = res.result;
				} else {
					this.$message.error(res.message);
				}
			});
		},
		/**打开弹窗*/
		open() {
			this.visible = true;
		},
		/**弹窗关闭*/
		cancel() {
			this.visible = false;
		},
		/**关闭弹窗*/
		handleClose() {
			this.visible = false;
		}
	}
};
</script>
<style scoped lang="scss">
.desk-el-custom-dialog {
	::v-deep .el-dialog {
		display: flex;
		flex-direction: column;
		&__body {
			flex: 1;
			overflow: hidden;
			display: flex;
			flex-direction: column;
		}
	}
}
.content {
	flex: 1;
	overflow: auto;
	@include noScrollBar;
}
</style>
