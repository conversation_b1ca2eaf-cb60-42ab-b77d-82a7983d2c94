<template>
	<div v-show="show" class="setting">
		<slot name="title"></slot>
		<el-form ref="ruleForm" class="desk-el-form" :model="form" :rules="rules">
			<el-form-item class="form-item" prop="title">
				<div class="input-label">
					<span class="label-text required">文章标题</span>
					<div class="input-label-right">
						<span class="input-label-right-number">{{ form.title.length }}/50</span>
						<div class="input-label-right-clear" @click="form.title = ''">清空</div>
					</div>
				</div>
				<el-input v-model="form.title" clearable placeholder="请输入" :maxlength="50"></el-input>
				<div class="history">
					<span
						v-for="(item, index) of history"
						:key="index"
						class="history-item"
						@click="selectHistory(item)"
					>
						{{ item }}
					</span>
				</div>
			</el-form-item>
			<el-form-item class="form-item" prop="structDemoDesc">
				<!--				<div class="label-text">-->
				<!--					<span>参考文稿</span>-->
				<!--					<i class="coos-iconfont icon-warn tip-icon"></i>-->
				<!--					<span class="input-label-tip">（可任选其一或两者均选）</span>-->
				<!--				</div>-->
				<div class="content">
					<div class="content-item">
						<div class="content-top-title">
							<div class="content-top-title-left">
								<span>结构参考</span>
								<span class="content-top-title-tip">（参考文稿的结构和逻辑框架）</span>
							</div>
							<i
								v-if="form.topStatus"
								class="coos-iconfont icon-minus open-icon"
								@click="openContent('topStatus')"
							></i>
							<i
								v-else
								class="coos-iconfont icon-add open-icon"
								@click="openContent('topStatus')"
							></i>
						</div>
						<!--结构参考表单区域START-->
						<transition name="fade">
							<div v-show="form.topStatus">
								<div
									v-show="form.top && (form.top !== 'upload' || structDemoFileUrl)"
									class="input-label"
									style="margin: 10px 0 4px"
								>
									<div class="label-text" style="margin: 0; cursor: pointer" @click="form.top = ''">
										<i class="coos-iconfont icon-nav-left go-back"></i>
										<span>返回</span>
									</div>
									<div v-show="form.top === 'input'" class="input-label-right">
										<span class="input-label-right-number">
											{{ form.structDemoDesc.length }}/500
										</span>
										<div class="input-label-right-clear" @click="form.structDemoDesc = ''">
											清空
										</div>
									</div>
								</div>
								<div v-show="form.top === 'input'" class="form-item">
									<el-input
										v-model="form.structDemoDesc"
										type="textarea"
										style="margin-bottom: 12px"
										:autosize="{ minRows: 4 }"
										:maxlength="500"
										placeholder="输入内容"
										clearable
									></el-input>
								</div>
								<div v-show="form.top === 'upload' && structDemoFileUrl">
									<div
										v-for="(item, index) of structDemoFileUrl"
										:key="index"
										class="file"
										@click="preview(item)"
									>
										<div class="file-content">
											<div class="file-left">
												<svg-icon :icon-class="item.cover" class="cover"></svg-icon>
												<div>
													<div class="file-name">{{ item.name }}</div>
													<div class="file-size">{{ tabSize(item.size) }}</div>
												</div>
											</div>
											<i
												class="coos-iconfont file-icon icon-shanchu"
												@click="del('structDemoFileUrl', index, 'top')"
											></i>
											<el-progress
												v-if="item.status === 'fail'"
												class="process"
												:percentage="item.process"
												status="exception"
											></el-progress>
											<el-progress
												v-else-if="item.process < 100"
												class="process"
												:percentage="item.process"
												status="success"
											></el-progress>
										</div>
									</div>
								</div>
								<div
									v-show="!form.top || (form.top === 'upload' && !structDemoFileUrl)"
									class="buttons"
								>
									<uploadFile
										ref="structDemoFileUrl"
										:accept="['xlsx', 'doc', 'docx', 'pdf', 'png', 'jpg', 'txt']"
										:disabled="structDemoFileUrl.length > 0"
										:limit="1"
										:value="form.structDemoFileUrl"
										style="width: 48%"
										:always-show-upload-button="true"
										:multiple="false"
										:custom-file="true"
										@onProcess="onProcess($event, 'structDemoFileUrl', 'top')"
										@change="change($event, 'structDemoFileUrl')"
									>
										<div
											slot="custom-button"
											class="buttons-item"
											style="width: 100%"
											@click="select('top', 'upload', 'structDemoFileUrl')"
										>
											<i class="coos-iconfont icon-shangchuan button-icon"></i>
											<span>上传参考文档</span>
										</div>
									</uploadFile>
									<div class="buttons-item" @click="select('top', 'input')">
										<i class="coos-iconfont icon-shuru button-icon"></i>
										<span>输入参考内容</span>
									</div>
								</div>
							</div>
						</transition>
						<!--结构参考表单区域END-->
					</div>
				</div>
			</el-form-item>
			<el-form-item class="form-item">
				<div class="content-item">
					<div class="content-top-title">
						<div class="content-top-title-left">
							<span>内容参考</span>
							<span class="content-top-title-tip">（参考文稿中的观点、案列和数据）</span>
						</div>
						<i
							v-if="form.descTypeStatus"
							class="coos-iconfont icon-minus open-icon"
							@click="openContent('descTypeStatus')"
						></i>
						<i
							v-else
							class="coos-iconfont icon-add open-icon"
							@click="openContent('descTypeStatus')"
						></i>
					</div>
					<!--			内容参考区域START			-->
					<transition name="fade">
						<div v-show="form.descTypeStatus">
							<div
								v-show="form.descType && (form.descType !== 'upload' || contentDemoFileUrl)"
								class="input-label"
								style="margin: 10px 0 4px"
							>
								<div
									class="label-text"
									style="margin: 0; cursor: pointer"
									@click="form.descType = ''"
								>
									<i class="coos-iconfont icon-nav-left go-back"></i>
									<span>返回</span>
								</div>
								<div v-show="form.descType === 'input'" class="input-label-right">
									<span class="input-label-right-number">{{ form.title.length }}/500</span>
									<div class="input-label-right-clear" @click="form.title = ''">清空</div>
								</div>
							</div>
							<div v-show="form.descType === 'input'" class="form-item">
								<el-input
									v-model="form.contentDemoDesc"
									:maxlength="500"
									type="textarea"
									style="margin-bottom: 12px"
									show-word-limit
									:autosize="{ minRows: 4 }"
									placeholder="输入内容"
									clearable
								></el-input>
							</div>
							<div v-show="form.descType === 'upload' && contentDemoFileUrl">
								<div
									v-for="(item, index) of contentDemoFileUrl"
									:key="index"
									class="file"
									@click="preview(item)"
								>
									<div class="file-content">
										<div class="file-left">
											<svg-icon :icon-class="item.cover" class="cover"></svg-icon>
											<div>
												<div class="file-name">{{ item.name }}</div>
												<div class="file-size">{{ tabSize(item.size) }}</div>
											</div>
										</div>
										<i
											class="coos-iconfont file-icon icon-shanchu"
											@click="del('contentDemoFileUrl', index, 'descType')"
										></i>
										<el-progress
											v-if="item.status === 'fail'"
											class="process"
											:percentage="item.process"
											status="exception"
										></el-progress>
										<el-progress
											v-else-if="item.process < 100"
											class="process"
											:percentage="item.process"
											status="success"
										></el-progress>
									</div>
								</div>
							</div>
							<div v-show="form.descType === 'select'" class="knowledge-content">
								<div
									v-for="(item, index) of knowledgeArr"
									:key="index"
									:class="{
										'knowledge-item-select':
											form.contentDemoMetadata && form.contentDemoMetadata.includes(item.id)
									}"
									class="knowledge-item"
									@click="joinKnowledge(item)"
								>
									<i class="coos-iconfont icon-bangding knowledge-icon"></i>
									<div class="aline">{{ item.spaceName }}</div>
								</div>
							</div>
							<div
								v-show="!form.descType || (form.descType === 'upload' && !contentDemoFileUrl)"
								class="content-buttons"
							>
								<uploadFile
									ref="contentDemoFileUrl"
									:accept="['xlsx', 'doc', 'docx', 'pdf', 'png', 'jpg', 'txt']"
									:value="form.contentDemoFileUrl"
									:disabled="contentDemoFileUrl.length > 0"
									:always-show-upload-button="true"
									:limit="1"
									style="flex: 1"
									:multiple="false"
									:custom-file="true"
									@onProcess="onProcess($event, 'contentDemoFileUrl', 'descType')"
									@change="change($event, 'contentDemoFileUrl')"
								>
									<div
										slot="custom-button"
										class="buttons-item"
										style="width: 100%"
										@click="select('descType', 'upload', 'contentDemoFileUrl')"
									>
										<i class="coos-iconfont icon-shangchuan button-icon"></i>
										<span>上传文档</span>
									</div>
								</uploadFile>
								<div class="content-buttons-item" @click="select('descType', 'input')">
									<i class="coos-iconfont icon-shuru content-buttons-item-icon"></i>
									<span>输入内容</span>
								</div>
								<div class="content-buttons-item" @click="select('descType', 'select')">
									<i class="coos-iconfont icon-zhishiku content-buttons-item-icon"></i>
									<span>绑定知识库</span>
								</div>
							</div>
							<!--								<el-form-item class="form-item" style="margin-top: 10px" prop="styleRequire">-->
							<!--									<div class="input-label">-->
							<!--										<span class="label-text">文章风格要求</span>-->
							<!--										<div class="input-label-right">-->
							<!--											<span class="input-label-right-number">-->
							<!--												{{ form.styleRequire.length }}/10-->
							<!--											</span>-->
							<!--											<div class="input-label-right-clear" @click="form.styleRequire = ''">-->
							<!--												清空-->
							<!--											</div>-->
							<!--										</div>-->
							<!--									</div>-->
							<!--									<el-input-->
							<!--										v-model="form.styleRequire"-->
							<!--										clearable-->
							<!--										placeholder="如:工作计划、行业报告"-->
							<!--									></el-input>-->
							<!--								</el-form-item>-->
						</div>
					</transition>
					<!--			内容参考区域END			-->
				</div>
			</el-form-item>
			<el-form-item class="form-item">
				<div class="input-label">
					<span class="label-text">文章补充信息</span>
					<div class="input-label-right">
						<span class="input-label-right-number">{{ form.otherRequire.length }}/500</span>
						<div class="input-label-right-clear" @click="form.otherRequire = ''">清空</div>
					</div>
				</div>
				<el-input
					v-model="form.otherRequire"
					placeholder="除了参考文稿中的内容，还希望生成的文章中包含的内容"
					type="textarea"
					:autosize="{ minRows: 4, maxRows: 10 }"
					resize="none"
					clearable
				></el-input>
			</el-form-item>
			<el-form-item class="form-item">
				<div class="input-label">
					<span class="label-text">是否跳过大纲生成</span>
				</div>
				<el-switch v-model="form.whetherToSkipOrNot"></el-switch>
			</el-form-item>
		</el-form>
		<div v-if="isHome" class="bottom">
			<el-button v-if="!form.whetherToSkipOrNot" type="primary" class="submit" @click="submit">
				<i class="coos-iconfont submit-icon icon-dagang"></i>
				<span>生成大纲</span>
			</el-button>
			<el-button v-else type="primary" class="submit" @click="submit">
				<i class="coos-iconfont icon-fasong submit-icon"></i>
				<span class="send-text">生成全文</span>
			</el-button>
			<el-button
				v-if="hasNext && writeeType === 'deepWrite'"
				type="primary"
				class="pre-button"
				@click="next"
			>
				下一步
			</el-button>
		</div>
		<div v-else class="bottom">
			<el-button class="cale" @click="cale">取消</el-button>
			<el-button type="primary" class="sure" :disabled="disable" @click="confirm">确定</el-button>
		</div>
	</div>
</template>
<script>
import uploadFile from '@/components/upload-file/index.vue';
import { previewFile, tabSize, urlHttp } from '@/utils';
import { de } from '@/wile-fire/vendor/pinyin/data/dict-zi-web';

export default {
	name: 'WriteSetting',
	components: {
		uploadFile
	},
	props: {
		disable: {
			type: Boolean,
			default: () => {
				return false;
			}
		},
		isHome: {
			type: Boolean,
			default: () => {
				return true;
			}
		},
		history: {
			type: Array,
			default: () => {
				return [];
			}
		},
		hasNext: {
			type: Boolean,
			default: () => {
				return false;
			}
		},
		knowledgeArr: {
			type: Array,
			default: () => {
				return [];
			}
		},
		// 写作类型
		writeeType: {
			type: String,
			default: () => {
				return '';
			}
		}
	},
	data() {
		return {
			whetherToSkipOrNot: false,
			show: false,
			form: {
				whetherToSkipOrNot: true, // 是否跳过大纲生成
				descTypeStatus: false,
				topStatus: false,
				top: null, // 参考结构的类型
				descType: 'select', // 默认选中知识库
				contentDemoMetadata: [], // 参考内容的知识库
				title: '', // 标题
				structDemoDesc: '', // 参考结构
				structDemoFileUrl: '', // 参考结构文件
				contentDemoDesc: '', // 参考内容文件
				contentDemoFileUrl: '', // 参考内容
				otherRequire: '', // 更多描述
				styleRequire: '' // 主题
			},
			// 表单校验规则
			rules: {
				title: [{ required: true, message: '请输入文稿标题', trigger: ['blur', 'change'] }]
			},
			// bottom: null, // 参考内容的类型
			structDemoFileUrl: [], // 上传的参考结构文件对象
			contentDemoFileUrl: [] // 上传的参考内容文件对象
		};
	},
	methods: {
		tabSize,
		confirm() {
			this.$confirm(
				'根据修改的写作要求重新生成大纲，会导致章节绑定的知识库等内容失效，是否继续?',
				'提示',
				{
					confirmButtonText: '继续',
					cancelButtonText: '取消',
					type: 'warning'
				}
			).then(res => {
				this.submit();
			});
		},
		cale() {
			this.show = false;
		},
		de() {
			return de;
		},
		close() {
			this.show = false;
		},
		/**展开输入内容区域*/
		openContent(type) {
			this.form[type] = !this.form[type];
		},
		/**异步获取回来的知识库，默认全选中*/
		initKnowledge() {
			this.form.contentDemoMetadata = this.knowledgeArr.map(item => item.id);
		},
		/**打开弹窗*/
		open(data) {
			if (data) {
				Object.keys(this.form).forEach(key => {
					// 数组特殊数据的处理
					if (key === 'contentDemoMetadata') {
						this.form.contentDemoMetadata = data.contentDemoMetadata
							? data.contentDemoMetadata.split(',')
							: [];
					} else {
						this.form[key] = data[key] || '';
					}
				});
			}
			this.show = true;
		},
		/**加入知识库*/
		joinKnowledge(item) {
			if (!this.form.contentDemoMetadata) {
				this.form.contentDemoMetadata = [];
			}
			let index = this.form.contentDemoMetadata.indexOf(item.id);
			if (index > -1) {
				this.form.contentDemoMetadata.splice(index, 1);
			} else {
				this.form.contentDemoMetadata.push(item.id);
			}
		},
		del(refKey, index, type) {
			this.$refs[refKey].del(index);
			this[refKey] = '';
			this.form[refKey] = '';
			this.form[type] = '';
		},
		preview(item) {
			previewFile(urlHttp(item.url));
		},
		/**上传文件重新赋值*/
		change(ids, key) {
			this.$set(this.form, key, ids);
		},
		/**
		 * @Description 上传监听进度的数据
		 * @Param {Array} showPreviewArr 上传的文件数据
		 * @Param {String} type 类型
		 * @Param {String} key 字段
		 *
		 * */
		onProcess(showPreviewArr, type, key) {
			if (showPreviewArr.length !== 0 && this.form[key] !== 'upload') {
				this.form[key] = 'upload';
			}
			this[type] = showPreviewArr.map(item => {
				return {
					...item,
					status: item.status === 'pending' ? 'exception' : item.status,
					process:
						item.process === 'success'
							? 100
							: typeof item.process === 'number'
							? item.process
							: Number(item.process.replace('%', ''))
				};
			});
		},
		/**
		 * @Description 选择上传还是输入
		 * @Param {String} place 位置
		 * @Param {String} type 类型
		 * @Param {String} key 字段
		 * */
		select(place, type, key) {
			if (type === 'upload' && !this.form[key]) return;
			this.form[place] = type;
		},
		next() {
			this.$emit('changeCurrent', this.writeeType === 'quickly' || this.whetherToSkipOrNot ? 3 : 2);
		},
		/**生成大纲*/
		submit() {
			this.$refs.ruleForm.validate(async valid => {
				if (valid) {
					this.id = '';
					let data = {
						...this.form
					};
					// 选择参考结构还是参考结构文件
					if (this.form.top === 'input') {
						delete data.structDemoFileUrl;
					} else {
						delete data.structDemoDesc;
					}
					// 选择参考内容还是参考内容文件
					if (this.form.descType === 'input') {
						delete data.contentDemoFileUrl;
						delete data.contentDemoMetadata;
					} else if (this.form.descType === 'select') {
						delete data.contentDemoDesc;
						delete data.contentDemoFileUrl;
						data.contentDemoMetadata = data.contentDemoMetadata.join(',');
					} else {
						delete data.contentDemoDesc;
						delete data.contentDemoMetadata;
					}
					this.whetherToSkipOrNot = data.whetherToSkipOrNot;
					this.$emit('requestOutlineData', data);
					this.$emit('changeCurrent', this.form.whetherToSkipOrNot ? 3 : 2);
				}
			});
		},
		/**选择历史标题*/
		selectHistory(item) {
			this.$set(this.form, 'title', item);
		}
	}
};
</script>
<style scoped lang="scss">
.form-item {
	width: 100%;
}
.desk-el-form {
	padding: 24px 16px;
	flex: 1;
	overflow: auto;
	&::-webkit-scrollbar {
		display: none;
	}
}
.buttons {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-top: 10px;
	gap: 13px;
	&-item {
		flex: 1;
		height: 58px;
		background: linear-gradient(158deg, #f1f5f8 0%, #f1f5f8 100%);
		border-radius: 6px;
		cursor: pointer;
		display: flex;
		align-items: center;
		justify-content: center;
		flex-direction: column;
		font-weight: 400;
		font-size: 14px;
		color: #2f446b;
		line-height: 22px;
	}
}
.file {
	background: #f9f9f9;
	border-radius: 6px;
	border: 1px solid #e3ebf2;
	padding: 8px 16px;
	.process {
		position: absolute;
		bottom: 0;
		left: 24px;
		width: 100%;
	}
	.cover {
		width: 32px;
		height: 32px;
		margin-right: 8px;
	}
	&-name {
		font-weight: 400;
		font-size: 12px;
		color: #15224c;
		line-height: 22px;
	}
	&-size {
		font-weight: 400;
		font-size: 10px;
		color: #737a94;
		line-height: 22px;
	}
	&-icon {
		font-size: 16px;
		color: #737a94;
		cursor: pointer;
	}
}
.bottom {
	display: flex;
	justify-content: center;
	border-top: 1px solid #e3ebf2;
	padding: 8px 0;
	.cale {
		width: 100px;
		height: 36px;
		background: #ffffff;
		border-radius: 6px;
		border: 1px solid #bccadb;
		font-weight: 400;
		font-size: 14px;
		color: #2f446b;
		line-height: 22px;
	}

	.submit {
		width: 106px;
		height: 36px;
		background: #2591f7;
		box-shadow: 0px 3px 4px 0px rgba(186, 219, 255, 0.8);
		border-radius: 6px 6px 6px 6px;
		border: none;
		font-weight: 400;
		font-size: 14px;
		color: #ffffff;
		line-height: 22px;
		&-icon {
			font-size: 16px;
			margin-right: 4px;
		}
	}
}
.history {
	display: flex;
	align-items: center;
	flex-wrap: wrap;
	margin-top: 4px;
	&-item {
		color: $subTextColor;
		font-size: 12px;
		line-height: 12px;
		margin: 8px 8px 0 0;
		cursor: pointer;
	}
}
.content {
	&-item {
		//background: #ffffff;
		//border-radius: 6px;
		//border: 1px solid #d9e2ec;
		//padding: 16px 8px;
		&-title {
			display: flex;
			align-items: center;
		}
	}
	&-top-title {
		display: flex;
		align-items: center;
		justify-content: space-between;
		&-tip {
			font-weight: 400;
			font-size: 12px;
			color: #b9bdc9;
			line-height: 22px;
		}
		&-left {
			margin-right: 10px;
			font-weight: 500;
			font-size: 14px;
			color: #2f446b;
			line-height: 22px;
		}
		.open-icon {
			cursor: pointer;
			font-size: 20px;
			color: #737a94;
		}
	}
}
.file-content {
	display: flex;
	align-items: center;
	justify-content: space-between;
	position: relative;
	background: #ffffff;
	border-radius: 6px;
	padding: 14px 8px;
}
.file-left {
	display: flex;
	align-items: center;
	flex: 1;
	overflow: hidden;
	&:hover {
		&::after {
			z-index: 555;
			content: '预览';
			width: calc(100% + 2px);
			height: calc(100% + 2px);
			display: flex;
			align-items: center;
			justify-content: center;
			background: rgba(0, 0, 0, 0.4);
			color: #ffffff;
			border-radius: 4px;
			position: absolute;
			top: -1px;
			left: -1px;
			cursor: pointer;
		}
	}
	.file-name {
		flex: 1;
		@include aLineEllipse;
	}
}
::v-deep .custom-upload-file-content {
	width: 100%;
}
.content-buttons {
	display: flex;
	align-items: center;
	flex-wrap: wrap;
	margin-top: 10px;
	gap: 12px;
	&-item {
		flex: 1;
		height: 58px;
		background: linear-gradient(158deg, #f1f5f8 0%, #f1f5f8 100%);
		border-radius: 6px;
		cursor: pointer;
		display: flex;
		align-items: center;
		justify-content: center;
		flex-direction: column;
		font-weight: 400;
		font-size: 14px;
		color: #2f446b;
		line-height: 22px;
		&-icon {
			font-size: 16px;
			margin-bottom: 4px;
			color: #2f446b;
		}
	}
}
.back-icon {
	font-size: 16px;
	color: var(--brand-6);
	margin-left: 4px;
	cursor: pointer;
	position: relative;
	z-index: 667;
	&:hover {
		font-size: 18px;
	}
}
.knowledge-content {
	background: #ffffff;
	border-radius: $borderRadius;
	padding: 6px;
	border: 1px solid $borderColor;
	display: flex;
	flex-wrap: wrap;
	gap: 8px;
}
.knowledge-icon {
	font-size: 14px;
	margin-right: 2px;
}
.knowledge-item {
	width: 48%;
	overflow: hidden;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 4px 8px;
	border-radius: $borderRadius;
	border: 1px solid $borderColor;
	color: $primaryTextColor;
	cursor: pointer;
	font-size: 14px;
	line-height: 14px;
	.aline {
		max-width: calc(100% - 14px);
		@include aLineEllipse;
	}
	&:hover {
		box-shadow: 0px 3px 4px 0px rgba(0, 0, 0, 0.1);
	}
}
.knowledge-item-select {
	background: var(--brand-6);
	color: #ffffff;
}
.setting {
	width: 350px;
	height: 100%;
	background: #ffffff;
	display: flex;
	flex-direction: column;
}
.input-label {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 8px;
	&-right {
		display: flex;
		align-items: center;
		&-number {
			font-weight: 400;
			font-size: 12px;
			color: #b9bdc9;
			line-height: 22px;
		}
		&-clear {
			margin-left: 16px;
			font-weight: 400;
			font-size: 12px;
			color: #737a94;
			line-height: 22px;
			cursor: pointer;
		}
	}
}
.label-text {
	display: flex;
	align-items: center;
	font-weight: 500;
	font-size: 14px;
	color: #2f446b;
	line-height: 22px;
	margin-bottom: 8px;
	.tip-icon {
		margin-left: 16px;
		color: #737a94;
		font-size: 16px;
	}
	.input-label-tip {
		margin-left: 4px;
		font-weight: 400;
		font-size: 12px;
		color: #737a94;
		line-height: 22px;
	}
}
.button-icon {
	color: #2f446b;
	font-size: 16px;
	margin-bottom: 4px;
}
.required {
	&:before {
		content: '*';
		font-weight: 400;
		font-size: 16px;
		color: #e34d59;
		margin-right: 4px;
	}
}
::v-deep .el-form-item {
	margin-bottom: 32px !important;
}
::v-deep .is-ghost {
	&:hover {
		border-color: #2591f7;
	}
}
.pre-button {
	width: 100px;
	height: 36px;
	border-radius: 6px;
	background: #ffffff;
	border: 1px solid #bccadb;
	font-weight: 400;
	font-size: 14px;
	color: #2f446b;
	line-height: 22px;
}
::v-deep .el-switch.is-checked {
	.el-switch__core {
		background: #2591f7 !important;
		border-color: #2591f7 !important;
	}
}
</style>
