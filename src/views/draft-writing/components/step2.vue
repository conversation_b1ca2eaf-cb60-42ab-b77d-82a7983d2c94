<template>
	<div class="step2">
		<div class="pro">
			<coosTitle
				:id="id"
				:doc-title="docTitle"
				:done="done"
				v-bind="$attrs"
				v-on="$listeners"
				@changeCurrent="pre"
			>
				<div slot="right" class="right">
					<div v-if="!isHistory && version === 'V2'" class="right-look" @click="lookWriteSetting">
						<img src="@/assets/images/write/writing-requirements.png" class="button-icon" alt="" />
						<span>查看写作要求</span>
					</div>
					<!--					<div-->
					<!--						class="right-auto"-->
					<!--						:class="{ 'disabled-button': !id || !done }"-->
					<!--						@click="getQuicklyOutline"-->
					<!--					>-->
					<!--						<img v-if="id" src="@/assets/images/write/auto.png" class="button-icon" alt="" />-->
					<!--						<span>自动推荐引用内容</span>-->
					<!--						<i v-if="loading" class="el-icon-loading loading-icon" style="margin-left: 8px"></i>-->
					<!--					</div>-->
				</div>
			</coosTitle>
			<div class="pro-content" :class="{ 'pro-content2': done && !abort, 'pro-content3': !done }">
				<!--    流式输出展示    -->
				<div v-if="!done || abort || isErr" class="drag-content normal-content">
					<messageNormal :item="{ answer: outlineOrgData }"></messageNormal>
				</div>
				<!--	自定义解析			-->
				<div v-else class="drag-content">
					<div v-if="showOutline[0]" class="doc-title">
						{{ showOutline[0][0].title }}
					</div>
					<draggable
						v-model="showOutline"
						:disabled="disableDrag"
						animation="300"
						drag-class="drag"
						:options="options"
						@change="handleDrag"
					>
						<div
							v-for="(outline, outlineIndex) of showOutline"
							v-show="outlineIndex > 0"
							:key="outlineIndex"
							class="outline-item"
						>
							<draggable
								v-model="showOutline[outlineIndex]"
								:disabled="disableDrag"
								animation="300"
								drag-class="drag"
								:options="options"
								@change="handleDrag"
							>
								<div
									v-for="(item, index) of outline"
									:key="item.id"
									class="hover-content"
									:class="{ 'select-item': currentIndex === `${outlineIndex}-${index}` }"
								>
									<div class="pro-content-item">
										<i
											class="coos-iconfont icon-tuozhuai drag-icon"
											:style="{ marginRight: 10 + (item.level - 1) * 30 + 'px' }"
										></i>
										<div class="content-box">
											<el-input
												v-if="item.editStatue"
												:ref="`editInput-${outlineIndex}-${index}`"
												v-model="item.title"
												class="no-border-input"
												placeholder="请输入想要添加的大纲标题"
												@blur="blurInput(item, 'editStatue')"
											></el-input>
											<div
												v-else
												style="width: 100%"
												@click="
													changeEdit(
														item,
														index,
														outlineIndex,
														`editInput-${outlineIndex}-${index}`,
														'editStatue'
													)
												"
											>
												<span v-if="item.title">{{ item.title }}</span>
												<span v-else class="placeholder">新增标题</span>
											</div>
										</div>
										<i
											class="coos-iconfont icon-xinzeng add-icon"
											@click="addOutline(outlineIndex, index, item)"
										></i>
										<i
											class="coos-iconfont icon-shanchu add-icon"
											@click="deleteOutline(outlineIndex, index)"
										></i>
									</div>
									<div
										v-if="item.descType"
										:style="{ marginLeft: 36 + (item.level - 1) * 30 + 'px' }"
									>
										<span v-if="item.descType === 'upload'" class="tag-icon">
											@{{ item.extraData.file[0].name }}
										</span>
										<div v-if="item.descType === 'input'" class="tag-icon input-content">
											@{{ item.desc }}
										</div>
										<div v-if="item.descType === 'select'" class="tag-content">
											<div
												v-for="(tag, tagIndex) of item.extraData.knowledgeInfo || []"
												:key="'tag-' + tagIndex"
												class="tag-icon"
											>
												@{{ tag.spaceName }}
											</div>
										</div>
									</div>
									<div
										class="desc-content"
										:style="{ paddingLeft: 36 + (item.level - 1) * 30 + 'px' }"
									>
										<el-input
											v-if="item.editDescExtraStatue && item.descExtra"
											:ref="`descExtraInput-${outlineIndex}-${index}`"
											v-model="item.descExtra"
											class="no-border-input desc-input"
											placeholder="除了参考文稿中的内容，还希望生成文章中包含的内容"
											@blur="blurInput(item, 'editDescExtraStatue')"
										></el-input>
										<div
											v-if="!item.editDescExtraStatue && item.descExtra"
											@click="
												changeEdit(
													item,
													index,
													outlineIndex,
													`descExtraInput-${outlineIndex}-${index}`,
													'editDescExtraStatue'
												)
											"
										>
											{{ item.descExtra }}
										</div>
									</div>
								</div>
							</draggable>
						</div>
					</draggable>
				</div>
				<div v-if="!done" class="pending">
					<div class="pending-left">
						<i class="el-icon-loading loading-icon"></i>
						<div>{{ jsonIndex > -1 ? '大纲解析中...' : '大纲生成中...' }}</div>
					</div>
					<el-button v-if="isBtn" @click="pause">
						<i class="coos-iconfont icon-zanting pending-icon"></i>
						<span>停止</span>
					</el-button>
				</div>
				<!--				<i class="coos-iconfont icon-xiaoxi message-icon" :class="{ disabled: !done }"></i>-->
				<!--					<i-->
				<!--						class="coos-iconfont icon-fuzhi copy-icon"-->
				<!--						:class="{ disabled: !done }"-->
				<!--						@click="copy"-->
				<!--					></i>-->
			</div>
			<!--   按钮集合   -->
			<div v-if="isBtn" class="pro-buttons">
				<el-button
					v-if="!isHistory"
					:disabled="!done"
					class="pre-button"
					type="primary"
					ghost
					:class="{ 'disabled-button': !done }"
					@click="pre"
				>
					上一步
				</el-button>
				<el-button
					:class="{ 'disabled-button': !done }"
					:disabled="!done"
					type="primary"
					ghost
					class="reload-button"
					@click="update"
				>
					重新生成大纲
				</el-button>
				<el-button
					:class="{ 'disabled-button': !done || abort }"
					:disabled="!done || abort"
					class="submit-button"
					type="primary"
					@click="pro"
				>
					生成全文
				</el-button>
				<el-button
					v-if="hasNext"
					class="pre-button"
					:class="{ 'disabled-button': !done }"
					:disabled="!done"
					@click="next"
				>
					下一步
				</el-button>
			</div>
			<div class="tip">本服务内容由人工智能整理生成，所提供的信息仅供参考</div>
			<!--  标题相关设置  -->
			<transition name="fade">
				<!--				<div v-show="showMore || showWriteSetting" style="height: 100%">-->
				<div v-if="version === 'V3'" v-show="showMore || showWriteSetting" class="flot">
					<div v-show="showMore" class="pro-right" style="border-radius: 0 16px 16px 0">
						<setting ref="setting" v-bind="$attrs" @save="save" @cale="resetCurrentIndex"></setting>
					</div>
					<div v-show="showWriteSetting" class="write-setting">
						<writeSetting
							ref="writeSetting"
							v-bind="$attrs"
							:is-home="false"
							:disable="!done"
							v-on="$listeners"
						>
							<div slot="title" class="setting-title">
								<div class="setting-title-left">
									<div class="main-title">写作要求</div>
								</div>
							</div>
						</writeSetting>
					</div>
				</div>
			</transition>
			<i
				v-if="version === 'V3'"
				class="coos-iconfont icon-tuozhuai drag-line"
				@mousedown="startDrag"
			></i>
		</div>
		<transition name="fade">
			<aiOutline
				v-if="version === 'V3' && isBtn"
				:id="id"
				ref="aiContent"
				:done="done"
				:ai-show="true"
				:width="rightWidth"
				@updateOutline="updateOutline"
			></aiOutline>
		</transition>
		<!--  标题相关设置  -->
		<transition name="fade">
			<div v-if="version === 'V2'" v-show="showMore || showWriteSetting" style="height: 100%">
				<div v-show="showMore" class="pro-right">
					<setting ref="setting" v-bind="$attrs" @save="save" @cale="resetCurrentIndex"></setting>
				</div>
				<div v-show="showWriteSetting" class="write-setting">
					<writeSetting
						ref="writeSetting"
						v-bind="$attrs"
						:is-home="false"
						:disable="!done"
						v-on="$listeners"
					>
						<div slot="title" class="setting-title">
							<div class="setting-title-left">
								<div class="main-title">写作要求</div>
							</div>
						</div>
					</writeSetting>
				</div>
			</div>
		</transition>
	</div>
</template>
<script>
import messageNormal from '@/views/coos/message/item/message-normal.vue';
import draggable from 'vuedraggable';
import { getQuicklyOutline, saveOutline } from '@/api/modules/coos-write';
import { getOutlineTitle } from '@/api/modules/coos-write';
import { copyText } from '@/wile-fire/ui/util/clipboard';
import { Base64 } from 'js-base64';
import setting from '@/views/draft-writing/components/setting.vue';
import writeSetting from '@/views/draft-writing/components/write-setting.vue';
import coosTitle from '@/views/draft-writing/components/title.vue';
import aiOutline from '@/views/draft-writing/components/ai-outline.vue';
import { mapMutations } from 'vuex';
export default {
	name: 'Step2Index',
	components: { draggable, messageNormal, setting, coosTitle, writeSetting, aiOutline },
	props: {
		writeSettingInfo: {
			type: Object,
			default: () => {
				return {};
			}
		},
		isHistory: {
			type: Boolean,
			default: () => {
				return false;
			}
		},
		docTitle: {
			type: String,
			default: () => {
				return '';
			}
		},
		version: {
			type: String,
			default: () => {
				return 'V2';
			}
		}
	},
	data() {
		return {
			isErr: false, // 报错
			rightWidth: 463, // 右边弹性宽度
			saveTitleStatus: false, // 存储标题状态
			showWriteSetting: false,
			disableDrag: false, // 是否拖动
			T: null,
			tempData: {},
			showMore: false, // 显示标题大纲的更多设置
			hasNext: false,
			jsonIndex: -1, // json开始的索引
			jsonCache: '', // json缓存数据
			controller: null,
			id: '', // 生成大纲使用的id
			options: {
				sort: true
			},
			currentIndex: '', // 当前选中的标题大纲
			done: true, // 是否完成了答案输出
			abort: false, // 中断
			outlineOrgData: '',
			loading: false,
			isBtn: true,
			showOutline: [], // 渲染的二维数组
			startX: 0, // 拖拽初始位置
			initWidth: 0 // 开始拖拽时候初始宽度
		};
	},
	methods: {
		...mapMutations('user', ['REMOVE_INFO']),
		/**拖拽调整宽度*/
		startDrag(e) {
			e.preventDefault();
			this.startX = e.clientX;
			this.initWidth = this.rightWidth;
			document.addEventListener('mousemove', this.drag);
			document.addEventListener('mouseup', this.dragEnd);
		},
		drag(e) {
			e.preventDefault();
			this.rightWidth = this.rightWidth < 350 ? 350 : this.initWidth - (e.clientX - this.startX);
		},
		dragEnd() {
			document.removeEventListener('mousemove', this.drag);
			document.removeEventListener('mouseup', this.dragEnd);
			try {
				this.$refs.aiContent.updateRender();
			} catch (e) {
				console.log(e);
			}
		},
		/**一键匹配*/
		getQuicklyOutline() {
			if (!this.id || !this.done) {
				this.$message.warning(this.done ? '大纲生成中断，无法一键匹配！' : '大纲生成中，请稍后...');
				return;
			}
			this.loading = true;
			getQuicklyOutline(this.id).then(res => {
				this.loading = false;
				if (res.code === 200) {
					this.generateTwoDimensionalArrays(res.result);
					this.$message.success('匹配完成！');
				} else {
					this.$message.error(res.message);
				}
			});
		},
		/**外部使用-是否暂停*/
		delPause(id) {
			if (id === this.id) {
				this.id = '';
				this.pause();
			}
		},
		/**重置*/
		reset() {
			this.pause(false);
			this.isErr = false;
			this.isBtn = true;
			this.id = '';
			this.hasNext = false;
			this.abort = false;
			this.outlineOrgData = '';
			this.showOutline = [];
			this.done = true;
			try {
				if (this.version === 'V3' && this.$refs.aiContent) {
					this.$refs.aiContent.reset();
				}
			} catch (e) {
				console.log(e);
			}
		},
		/**根据数据直接初始化*/
		initData(data) {
			this.reset();
			this.id = data.id;
			data.writeInfo && this.generateTwoDimensionalArrays(data.writeInfo);
		},
		lookWriteSetting() {
			if (!this.showWriteSetting) {
				this.showWriteSetting = true;
				// 关闭标题编辑
				this.showMore = false;
				this.$refs.writeSetting.open(this.writeSettingInfo);
			} else {
				this.showWriteSetting = false;
				this.$refs.writeSetting.close();
			}
		},
		blurInput(item, key) {
			item[key] = false;
			this.disableDrag = false;
		},
		/**更新二维数组*/
		updateOutline(data) {
			this.generateTwoDimensionalArrays(data || this.showOutline.flat());
		},
		/**生成二维数组*/
		generateTwoDimensionalArrays(outlineData) {
			const result = [];
			let currentGroup = [];
			for (const item of outlineData) {
				if (item.level === 1) {
					// 当遇到level为1时，开始新分组
					if (currentGroup.length > 0) {
						result.push(currentGroup);
					}
					currentGroup = [item];
				} else {
					// 否则将元素添加到当前分组
					currentGroup.push(item);
				}
			}
			// 添加最后一个分组
			if (currentGroup.length > 0) {
				result.push(currentGroup);
			}
			this.showOutline = result;
		},
		/**完成输出*/
		finish() {
			this.jsonCache = '';
			this.jsonIndex = -1;
			this.done = true;
		},
		/**暂停*/
		pause(pending = true) {
			try {
				this.controller && this.controller.abort();
			} catch (e) {
				console.log(e);
			}
			this.controller = null;
			this.abort = true;
			if (pending) {
				this.outlineOrgData += '\n回答中断，大纲不完整，无法使用，请重新生成...';
			}
			this.finish();
		},
		/**新增标题大纲*/
		addOutline(outlineIndex, index, item) {
			this.showOutline[outlineIndex].splice(index + 1, 0, {
				title: '',
				level: item.level,
				descMetadataIds: [],
				desc: '',
				descExtra: '',
				descUrl: '',
				descType: '',
				editStatue: false,
				extraData: {}
			});
			this.updateOutline();
		},
		/**删除大纲标题*/
		deleteOutline(outlineIndex, index) {
			this.showOutline[outlineIndex].splice(index, 1);
			// 如果所在层级为空，删除整个层级
			if (this.showOutline[outlineIndex].length === 0) {
				this.showOutline.splice(outlineIndex, 1);
			}
		},
		/**编辑标题*/
		changeEdit(item, index, outlineIndex, refKey, key) {
			this.disableDrag = true;
			item[key] = true;
			this.$nextTick(() => {
				this.$refs[refKey][0].focus();
			});
			this.currentIndex = `${outlineIndex}-${index}`;
			// 关闭写作要求
			this.showWriteSetting = false;
			this.$refs.writeSetting.close();
			this.showMore = true;
			this.$refs.setting.open(item);
		},
		/**拖拽完成*/
		handleDrag() {},
		/**复制*/
		copy() {
			if (this.done) {
				copyText(this.outlineOrgData);
				this.$message.success('复制成功！');
			}
		},
		/**保存默认标题*/
		saveTitle() {
			if (this.id && !this.docTitle && !this.saveTitleStatus && this.showOutline.length > 0) {
				let titleObj = this.showOutline.flat().find(item => item.level === 0);
				let title = titleObj ? titleObj.title : '未命名文档';
				this.saveTitleStatus = true;
				saveOutline(
					{
						saveType: 'title',
						title
					},
					this.id
				).then(res => {
					if (res.code === 200) {
						this.saveTitleStatus = false;
						this.$emit('changeTitle', title);
					} else {
						this.$message.error(res.message);
					}
				});
			}
		},
		/**请求标题大纲*/
		async requestOutlineData(data, isBtn = true) {
			this.reset();
			this.isBtn = isBtn;
			this.done = false;
			this.controller = new AbortController();
			const { signal } = this.controller;
			let aiRes;
			try {
				aiRes = await getOutlineTitle(data, signal);
				// 暂停时候特殊处理
				if (!aiRes) return;
			} catch (err) {
				// 报错的逻辑
				this.outlineOrgData += '\n大纲生成错误，请重新生成！';
				this.isErr = true;
				this.finish();
				this.$message.error(err.message || err);
				return;
			}
			this.tempData = data; // 临时的data
			// // 获取AI答案
			const reader = aiRes.body.getReader(); // 流式读取器
			const decoder = new TextDecoder('utf-8');
			let readDone = false; // 读取完毕
			// 流式输出
			while (!readDone) {
				let { value, done } = await reader.read();
				readDone = done;
				let res;
				try {
					res = decoder.decode(value);
					if (/"code":50/.test(res)) {
						this.isErr = true;
						let r = JSON.parse(res.replace('data:', ''));
						res = [r.message || '服务器异常', 'noAnswer'];
					} else if (/"code":401/.test(res)) {
						this.isErr = true;
						res = ['登录失效，请重新登录！', 'noAnswer'];
						this.REMOVE_INFO();
						this.$message.error('登录失效，请重新登录！');
						setTimeout(() => {
							this.$router.push('/login');
						}, 1500);
					} else {
						// res = res.replace(/data:/gi, ''); // 解析成JSON，去掉data:开头的字符串
						res = res.split('data:');
						res = res.map(item => {
							return item.indexOf('noAnswer') > -1 ? 'noAnswer' : Base64.decode(item);
						});
						if (readDone && !res.includes('noAnswer')) {
							res.push('noAnswer');
						}
					}
					// 累加数据
					res.forEach(value => {
						if (this.jsonIndex > -1) {
							this.jsonCache += value;
						} else {
							this.outlineOrgData += value;
						}
					});
					// 捕获json开始的时候
					if (this.outlineOrgData.indexOf('```') > -1) {
						this.jsonIndex = this.outlineOrgData.indexOf('```');
						this.jsonCache += this.outlineOrgData.slice(this.jsonIndex);
						// 截取json开始前的数据
						this.outlineOrgData = this.outlineOrgData.slice(0, this.jsonIndex);
					}
				} catch (err) {
					console.log('流式解析报错--------------', err);
				}
			}
			// 解析json数据
			if (/^```json/.test(this.jsonCache)) {
				// 提取json代码块内容
				const jsonBlockRegex = /```json([\s\S]*?)```/;
				const match = this.jsonCache.match(jsonBlockRegex);
				if (match) {
					const jsonContent = match[1];
					// 替换\n为实际换行（如果需要）
					const replacedContent = jsonContent.replace(/\\n/g, '\n');
					// 如果需要将字符串解析为JSON对象
					try {
						let outlineData = JSON.parse(replacedContent).map(item => {
							return {
								editStatue: false, // 加一个是否编辑状态;
								knowledge: '', // 知识库绑定
								desc: '', // 更多描述
								descFile: '', // 更多描述文件
								extraData: {},
								editDescExtraStatue: false,
								...item
							};
						});
						this.generateTwoDimensionalArrays(outlineData);
					} catch (e) {
						console.error('JSON解析错误:', e);
					}
				} else {
					console.log('未找到json代码块');
				}
			}
			// 保存大纲生成的业务id
			if (/id:/.test(this.jsonCache)) {
				this.id = this.jsonCache.split('id:')[1].replace('noAnswer', '');
			}
			this.saveTitle();
			// 完成输出
			this.finish();
			// 大纲生成完成直接跳转生成全文
			if (!this.isBtn) {
				this.pro();
			}
		},
		resetCurrentIndex() {
			this.showMore = false;
			this.currentIndex = '';
		},
		cale() {
			this.showWriteSetting = false;
			this.showMore = false;
			this.$refs.setting.cale();
			this.$refs.writeSetting.close();
		},
		/**保存大纲标题的设置*/
		save(setting) {
			let [outlineIndex, index] = this.currentIndex.split('-');
			Object.keys(setting).forEach(key => {
				if (setting[key]) {
					this.$set(this.showOutline[outlineIndex][index], key, setting[key]);
				}
			});
			this.currentIndex = '';
			this.$refs.setting.cale();
			this.updateOutline(); // 防止修改了层级
		},
		/**上一步*/
		pre() {
			this.pause();
			this.$emit('changeCurrent', 1);
		},
		/**重新生成大纲*/
		update() {
			if (this.id) {
				this.$confirm(
					'根据修改的写作要求重新生成大纲，会导致章节绑定的知识库等内容失效，是否继续?',
					'提示',
					{
						confirmButtonText: '继续',
						cancelButtonText: '取消',
						type: 'warning'
					}
				).then(res => {
					this.hasNext = false;
					this.requestOutlineData({ id: this.id });
				});
			} else {
				this.requestOutlineData(this.tempData);
			}
		},
		/**生成全文*/
		pro() {
			if (this.isErr) {
				this.$message.error('大纲生成错误，不可生成全文！');
				return;
			}
			let data = this.showOutline
				.flat()
				.filter(item => item.title)
				.map(item => {
					return {
						...item,
						level: parseInt(item.level)
					};
				});
			this.hasNext = true;
			this.$emit('changeCurrent', 3);
			this.$emit('proOutline', this.id, data);
		},
		next() {
			this.$emit('changeCurrent', 3);
		}
	}
};
</script>
<style scoped lang="scss">
.pro-content {
	margin-top: 24px;
	width: 70%;
	flex: 1;
	overflow: hidden;
	padding: 24px 0;
	position: relative;
	border-radius: 12px;
	border: 1px solid #d9e2ec;
	&::-webkit-scrollbar {
		display: none;
	}
}
.normal-content {
	padding: 0 20px;
}
.pro-content2 {
	padding: 0;
	border-radius: 0;
	border: none;
}
.pro-content3 {
	padding-bottom: 120px;
}
.message-icon {
	position: absolute;
	bottom: 12px;
	right: 36px;
	font-size: 16px;
	color: $primaryTextColor;
	cursor: pointer;
}
.copy-icon {
	position: absolute;
	bottom: 12px;
	right: 12px;
	font-size: 16px;
	color: $primaryTextColor;
	cursor: pointer;
}
.pro-buttons {
	display: flex;
	align-items: center;
	justify-content: center;
	margin-top: 20px;
}
.pro-content-item {
	display: flex;
	align-items: center;
	padding: 10px;
	border-radius: $borderRadius;
	color: $primaryTextColor;
	font-size: 14px;
}
.drag-icon {
	font-size: 16px;
	margin-right: 8px;
	cursor: move;
}
.add-icon {
	display: none;
}
.placeholder {
	color: $disabledTextColor;
}
.disabled {
	color: $disabledTextColor;
}
.pending {
	position: absolute;
	bottom: 48px;
	width: 90%;
	left: 5%;
	display: flex;
	align-items: center;
	justify-content: space-between;
	border-radius: $borderRadius;
	box-shadow: 0px 8px 10px rgba(0, 0, 0, 0.08), 0px 16px 24px rgba(0, 0, 0, 0.04);
	background: #ffffff;
	border: 1px solid $borderColor;
	padding: 12px;
}
.pending-left {
	display: flex;
	align-items: center;
	font-size: 14px;
}
.pending-icon {
	color: #ff7a7b;
	font-size: 16px;
	margin-right: 4px;
}
.loading-icon {
	font-size: 16px;
	margin-right: 12px;
	color: var(--brand-6);
}
.step2 {
	height: 100%;
	display: flex;
}
.pro {
	flex: 1;
	display: flex;
	align-items: center;
	justify-content: center;
	flex-direction: column;
	background: #ffffff;
	box-shadow: 0px -1px 20px 0px rgba(184, 194, 215, 0.3);
	border-radius: 16px;
	//border: 1px solid #e3ebf2;
	overflow: hidden;
	position: relative;
}

.pro-right {
	margin-left: 16px;
	width: 400px;
	height: 100%;
	background: #ffffff;
	box-shadow: 0px -1px 20px 0px rgba(184, 194, 215, 0.3);
	border-radius: 16px;
	border: 1px solid #d9e2ec;
}
.tag-content {
	display: flex;
	align-items: center;
}
.tag-icon {
	display: inline-block;
	margin: 8px 8px 8px 0;
	padding: 0 4px;
	background: #ecf5ff;
	border-radius: 3px;
	font-weight: 400;
	font-size: 12px;
	color: #2591f7;
	line-height: 22px;
}
.content-box {
	flex: 1;
	display: flex;
	align-items: center;
}
.right {
	&-look {
		display: flex;
		align-items: center;
		background: #f1f4f6;
		border-radius: 6px;
		padding: 7px 16px;
		font-weight: 400;
		font-size: 14px;
		color: #2f446b;
		line-height: 22px;
		cursor: pointer;
	}
	&-auto {
		display: flex;
		align-items: center;
		background: #ffffff;
		border-radius: 6px;
		border: 1px solid #2591f7;
		padding: 7px 16px;
		font-weight: 400;
		font-size: 14px;
		color: #2591f7;
		line-height: 22px;
		margin-left: 16px;
		cursor: pointer;
	}
}
.button-icon {
	width: 16px;
	height: 16px;
}
.right {
	display: flex;
	align-items: center;
}
.tip {
	margin: 20px 0;
	font-weight: 400;
	font-size: 12px;
	color: #b9bdc9;
	line-height: 22px;
}
.pre-button {
	width: 82px;
	height: 36px;
	border-radius: 6px;
	background: #ffffff;
	border: 1px solid #bccadb;
	font-weight: 400;
	font-size: 14px;
	color: #2f446b;
	line-height: 22px;
}
.reload-button {
	border-color: #2591f7;
	color: #2591f7;
	width: 124px;
	height: 36px;
	border-radius: 6px;
}
.submit-button {
	width: 96px;
	height: 36px;
	background: #2591f7;
	box-shadow: 0px 4px 16px 0px rgba(37, 145, 247, 0.3);
	border-radius: 6px;
	border: none;
}
.disabled-button {
	background: $holderTextColor;
	border-color: $holderTextColor;
	box-shadow: none;
	color: #ffffff !important;
	cursor: not-allowed;
}
.outline-item {
	padding: 24px 30px;
	border-radius: 12px;
	border: 1px solid #d9e2ec;
	margin-bottom: 12px;
}
.doc-title {
	margin-bottom: 12px;
	text-align: center;
	font-weight: 600;
}
.drag-content {
	height: 100%;
	width: 100%;
	overflow: auto;
	&::-webkit-scrollbar {
		display: none;
	}
}
.desc-content {
	padding-right: 12px;
	margin: 8px 5px;
	font-weight: 400;
	font-size: 12px;
	color: #737a94;
	line-height: 22px;
}
.desc-input {
	::v-deep .el-input__inner {
		font-weight: 400;
		font-size: 12px;
		color: #737a94;
		line-height: 22px;
	}
}
.input-content {
	max-width: 200px;
	@include aLineEllipse;
}
.select-item {
	box-shadow: 0px 1px 4px 0px #98defb;
	border: 1px solid #2591f7;
}
.no-border-input {
	::v-deep .el-input__inner {
		border: none;
		outline: none;
		box-shadow: none;
	}
}
.hover-content {
	border-radius: 6px;
	&:hover {
		background: #f3f7fa;
		box-shadow: 0px 0px 11px 0px rgba(131, 161, 188, 0.4);
		.add-icon {
			color: #737a94;
			display: block !important;
			cursor: pointer;
			margin-left: 10px;
			font-size: 16px;
		}
	}
}
.write-setting {
	height: 100%;
	box-shadow: 0px -1px 20px 0px rgba(184, 194, 215, 0.3);
	border-radius: 16px;
	border: 1px solid #d9e2ec;
	margin-left: 16px;
	overflow: hidden;
}
.setting-title {
	padding: 14px 16px;
	display: flex;
	align-items: center;
	justify-content: space-between;
	border-bottom: 1px solid #e3ebf2;
	&-left {
		display: flex;
		align-items: center;
		.main-title {
			font-weight: 600;
			font-size: 20px;
			color: #15224c;
			line-height: 23px;
			margin-right: 12px;
		}
		.sub-title {
			font-weight: 400;
			font-size: 14px;
			color: #737a94;
			line-height: 22px;
		}
	}
	&-right {
		font-size: 16px;
		cursor: pointer;
		color: #606266;
	}
}
::v-deep .is-ghost {
	&:hover {
		border-color: #2591f7;
	}
}
.flot {
	position: absolute;
	right: 0;
	top: 0;
	height: 100%;
	z-index: 2;
}
.drag-line {
	position: absolute;
	cursor: ew-resize;
	right: 4px;
	top: calc(50% - 9px);
	font-size: 16px;
}
</style>
