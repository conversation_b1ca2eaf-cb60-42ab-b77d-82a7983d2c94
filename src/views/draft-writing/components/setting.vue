<template>
	<div class="setting-content">
		<div class="title">
			<img src="@/assets/images/write/edit-icon.png" class="title-icon" alt="" />
			<span class="title-text">编辑</span>
		</div>
		<el-form ref="moreSetting" class="desk-el-form" :model="setting" :rules="ruleSettings">
			<el-form-item prop="title">
				<div class="input-label">
					<span class="label-text required">标题</span>
					<div class="input-label-right">
						<span class="input-label-right-number">{{ setting.title.length }}/50</span>
						<div class="input-label-right-clear" @click="setting.title = ''">清空</div>
					</div>
				</div>
				<el-input
					v-model="setting.title"
					:maxlength="50"
					clearable
					placeholder="请输入当前节点的标题"
				></el-input>
			</el-form-item>
			<el-form-item prop="level">
				<div class="input-label">
					<span class="label-text required">标题等级</span>
				</div>
				<el-select v-model="setting.level" placeholder="请选择标题等级">
					<el-option
						v-for="item in options"
						:key="item.value"
						:label="item.label"
						:value="item.value"
					></el-option>
				</el-select>
			</el-form-item>
			<el-form-item>
				<div class="label-text">
					<span>参考来源</span>
					<i class="coos-iconfont icon-warn tip-icon"></i>
					<span class="input-label-tip">三者可任选其一</span>
				</div>
				<div
					v-show="setting.descType && (setting.descType !== 'upload' || descUrl)"
					class="input-label"
					style="margin: 10px 0 4px"
				>
					<div class="label-text" style="margin: 0; cursor: pointer" @click="resetSetting">
						<i class="coos-iconfont icon-nav-left go-back"></i>
						<span>返回</span>
					</div>
					<div v-show="setting.descType === 'input'" class="input-label-right">
						<span class="input-label-right-number">{{ setting.desc.length }}/500</span>
						<div class="input-label-right-clear" @click="setting.desc = ''">清空</div>
					</div>
				</div>
				<el-input
					v-show="setting.descType === 'input'"
					v-model="setting.desc"
					type="textarea"
					style="margin-bottom: 12px"
					:autosize="{ minRows: 4 }"
					placeholder="输入参考内容"
					clearable
				></el-input>
				<div v-show="setting.descType === 'upload' && descUrl">
					<div v-for="(item, index) of descUrl" :key="index" class="file" @click="preview(item)">
						<div class="file-content">
							<div class="file-left">
								<svg-icon :icon-class="item.cover" class="cover"></svg-icon>
								<div>
									<div class="file-name">{{ item.name }}</div>
									<div class="file-size">{{ tabSize(item.size) }}</div>
								</div>
							</div>
							<i class="coos-iconfont file-icon icon-shanchu" @click="del(index)"></i>
							<el-progress
								v-if="item.status === 'fail'"
								class="process"
								:percentage="item.process"
								status="exception"
							></el-progress>
							<el-progress
								v-else-if="item.process < 100"
								class="process"
								:percentage="item.process"
								status="success"
							></el-progress>
						</div>
					</div>
				</div>
				<div v-show="setting.descType === 'select'" class="knowledge-content">
					<div
						v-for="(item, index) of knowledgeArr"
						:key="index"
						:class="{ 'knowledge-item-select': setting.descMetadataIds.includes(item.id) }"
						class="knowledge-item"
						@click="joinKnowledge(item)"
					>
						<i class="coos-iconfont icon-bangding knowledge-icon"></i>
						<div class="aline">{{ item.spaceName }}</div>
					</div>
				</div>
				<div
					v-show="!setting.descType || (setting.descType === 'upload' && !descUrl)"
					class="content-buttons"
				>
					<uploadFile
						ref="upload"
						:accept="['xlsx', 'doc', 'docx', 'pdf', 'png', 'jpg', 'txt']"
						:value="setting.descUrl"
						:disabled="descUrl.length > 0"
						:always-show-upload-button="true"
						:limit="1"
						:multiple="false"
						:custom-file="true"
						@onProcess="onProcess"
						@change="change"
					>
						<div
							slot="custom-button"
							class="buttons-item"
							style="width: 100%"
							@click="select('upload')"
						>
							<i class="coos-iconfont icon-shangchuan button-icon"></i>
							<span>上传文档</span>
						</div>
					</uploadFile>
					<div class="content-buttons-item" @click="select('input')">
						<i class="coos-iconfont icon-shuru content-buttons-item-icon"></i>
						<span>输入内容</span>
					</div>
					<div class="content-buttons-item" @click="select('select')">
						<i class="coos-iconfont icon-zhishiku content-buttons-item-icon"></i>
						<span>绑定知识库</span>
					</div>
				</div>
			</el-form-item>
			<el-form-item prop="descExtra">
				<div class="input-label">
					<span class="label-text required">文章补充信息</span>
					<div class="input-label-right">
						<span class="input-label-right-number">{{ setting.descExtra.length }}/500</span>
						<div class="input-label-right-clear" @click="setting.descExtra = ''">清空</div>
					</div>
				</div>
				<el-input
					v-model="setting.descExtra"
					type="textarea"
					:maxlength="500"
					:autosize="{ minRows: 4 }"
					placeholder="除了参考文稿中的内容，还希望生成文章中包含的内容"
					clearable
				></el-input>
			</el-form-item>
			<div ref="bottomBlock"></div>
		</el-form>
		<div class="pro-setting-buttons">
			<el-button class="cale-button" @click="cale">取消</el-button>
			<el-button :disabled="loading" class="sure-button" type="primary" @click="save">
				确定
			</el-button>
		</div>
	</div>
</template>
<script>
import { deepClone, previewFile, tabSize, urlHttp } from '@/utils';
import uploadFile from '@/components/upload-file/index.vue';
export default {
	name: 'SettingIndex',
	components: { uploadFile },
	props: {
		loading: {
			type: Boolean,
			default: () => {
				return false;
			}
		},
		// 重写的内容
		overWritContent: {
			type: String,
			default: () => {
				return '';
			}
		},
		// 处理类型
		handleType: {
			type: String,
			default: () => {
				return '';
			}
		},
		knowledgeArr: {
			type: Array,
			default: () => {
				return [];
			}
		}
	},
	data() {
		return {
			isBack: false,
			// 大纲标题设置原始的数据
			orgSetting: {},
			setting: {
				title: '',
				level: '', // 等级
				desc: '', // 参考内容
				descUrl: '', // 参考文档
				descMetadataIds: [], // 绑定知识库id
				descExtra: '', // 补充信息
				descType: '', // 三选一的类型
				extraData: {} // 拓展数据
			},
			descUrl: [],
			// 大纲标题设置的规则
			ruleSettings: {
				title: [{ required: true, message: '请输入当前节点标题', trigger: ['blur'] }],
				level: [{ required: true, message: '请输入标题等级', trigger: ['blur'] }]
			},
			T: null,
			options: [
				{
					label: '一级标题',
					value: 1
				},
				{
					label: '二级标题',
					value: 2
				},
				{
					label: '三级标题',
					value: 3
				},
				{
					label: '四级标题',
					value: 4
				},
				{
					label: '五级标题',
					value: 5
				}
			]
		};
	},
	watch: {
		overWritContent() {
			if (!this.T) {
				this.T = setTimeout(() => {
					this.$refs.bottomBlock.scrollIntoView({ behavior: 'smooth', block: 'start' });
					this.T = null;
				}, 500);
			}
		}
	},
	mounted() {
		this.orgSetting = deepClone(this.setting);
	},
	methods: {
		tabSize,
		/**
		 * @Description 选择上传还是输入
		 * @Param {String} type 类型
		 * */
		select(type) {
			if (type === 'upload' && !this.setting.descUrl) return;
			this.setting.descType = type;
		},
		/**替换*/
		replace() {
			this.$emit('replace', this.setting);
		},
		/**删除*/
		del(index) {
			this.$refs.upload.del(index);
			this.setting.descUrl = '';
			this.descUrl = '';
			delete this.setting.extraData.file;
			this.setting.descType = '';
		},
		/**预览*/
		preview(item) {
			previewFile(urlHttp(item.url));
		},
		/**重置设置*/
		resetSetting() {
			this.setting.descType = '';
		},
		/**加入知识库*/
		joinKnowledge(item) {
			if (!this.setting.descMetadataIds) {
				this.setting.descMetadataIds = [];
			}
			let index = this.setting.descMetadataIds.indexOf(item.id);
			if (index > -1) {
				this.setting.descMetadataIds.splice(index, 1);
			} else {
				this.setting.descMetadataIds.push(item.id);
			}
			this.$set(
				this.setting.extraData,
				'knowledgeInfo',
				this.knowledgeArr.filter(item => {
					return this.setting.descMetadataIds.includes(item.id);
				})
			);
		},
		/**打开弹窗*/
		open(item) {
			if (item) {
				Object.keys(this.setting).forEach(key => {
					this.$set(this.setting, key, item[key] || '');
				});
				if (!this.setting.descMetadataIds) this.$set(this.setting, 'descMetadataIds', []);
				if (!this.setting.extraData) this.$set(this.setting, 'extraData', {});
			}
		},
		/**取消大纲标题的设置*/
		cale() {
			this.$emit('cale');
			this.setting = deepClone(this.orgSetting);
			this.$refs.moreSetting.clearValidate();
		},
		/**保存大纲标题的设置*/
		save() {
			if (this.setting.descType === 'upload') {
				this.setting.desc = '';
				this.setting.descMetadataIds = [];
			} else if (this.setting.descType === 'input') {
				this.setting.descMetadataIds = [];
				this.setting.descUrl && this.del(0);
				delete this.setting.extraData.file;
			} else if (this.setting.descType === 'select') {
				this.setting.desc = '';
				this.setting.descUrl && this.del(0);
				delete this.setting.extraData.file;
			}
			if (
				!this.setting.desc &&
				this.setting.descMetadataIds.length === 0 &&
				!this.setting.descUrl
			) {
				this.setting.descType = '';
			}
			this.$refs.moreSetting.validate(valid => {
				if (valid) {
					this.$emit('save', this.setting);
					this.cale();
				}
			});
		},
		/**上传文件重新赋值*/
		change(ids) {
			this.$set(this.setting, 'descUrl', ids);
		},
		/**
		 * @Description 上传监听进度的数据
		 * @Param {Array} showPreviewArr 上传的文件数据
		 * */
		onProcess(showPreviewArr) {
			if (showPreviewArr.length !== 0 && this.setting.descType !== 'upload') {
				this.setting.descType = 'upload';
			}
			this.setting.extraData.file = this.descUrl = showPreviewArr.map(item => {
				return {
					...item,
					process:
						item.process === 'success'
							? 100
							: typeof item.process === 'number'
							? item.process
							: Number(item.process.replace('%', ''))
				};
			});
		}
	}
};
</script>
<style scoped lang="scss">
.file-content {
	display: flex;
	align-items: center;
	justify-content: space-between;
	position: relative;
	background: #ffffff;
	border-radius: 6px;
	padding: 14px 8px;
}
.file {
	background: #f9f9f9;
	border-radius: 6px;
	border: 1px solid #e3ebf2;
	padding: 8px 16px;
	.process {
		position: absolute;
		bottom: 0;
		left: 24px;
		width: 100%;
	}
	.file-left {
		display: flex;
		align-items: center;
		&:hover {
			&::after {
				z-index: 555;
				content: '预览';
				width: calc(100% + 2px);
				height: calc(100% + 2px);
				display: flex;
				align-items: center;
				justify-content: center;
				background: rgba(0, 0, 0, 0.4);
				color: #ffffff;
				border-radius: 4px;
				position: absolute;
				top: -1px;
				left: -1px;
				cursor: pointer;
			}
		}
	}
	.cover {
		width: 32px;
		height: 32px;
		margin-right: 8px;
	}
	&-name {
		font-weight: 400;
		font-size: 12px;
		color: #15224c;
		line-height: 22px;
	}
	&-size {
		font-weight: 400;
		font-size: 10px;
		color: #737a94;
		line-height: 22px;
	}
	&-icon {
		font-size: 16px;
		color: #737a94;
		cursor: pointer;
	}
}
.desk-el-form {
	flex: 1;
	overflow: auto;
	padding: 20px 16px;
	&::-webkit-scrollbar {
		display: none;
	}
}
::v-deep .custom-upload {
	width: 32%;
}
.pro-setting-buttons {
	padding: 8px 0;
	border-top: 1px solid #e3ebf2;
	display: flex;
	justify-content: center;
}
.cale-button {
	width: 100px;
	height: 36px;
	background: #ffffff;
	border-radius: 6px;
	border: 1px solid #bccadb;
	font-weight: 400;
	font-size: 14px;
	color: #2f446b;
	line-height: 22px;
}
.sure-button {
	width: 100px;
	height: 36px;
	background: #2591f7;
	border-radius: 6px;
	font-weight: 400;
	font-size: 14px;
	color: rgba(255, 255, 255, 0.9);
	line-height: 22px;
	border: none;
}
.knowledge-content {
	border-radius: $borderRadius;
	padding: 6px;
	border: 1px solid $borderColor;
}
.knowledge-icon {
	font-size: 14px;
	margin-right: 2px;
}
.knowledge-item {
	display: inline-flex;
	align-items: center;
	justify-content: center;
	padding: 4px 8px;
	border-radius: $borderRadius;
	border: 1px solid $borderColor;
	color: $primaryTextColor;
	cursor: pointer;
	margin: 4px;
	font-size: 12px;
	line-height: 12px;
	.aline {
		flex: 1;
		@include aLineEllipse;
	}
	&:hover {
		box-shadow: 0px 3px 4px 0px rgba(0, 0, 0, 0.1);
	}
}
.knowledge-item-select {
	background: var(--brand-6);
	color: #ffffff;
}
.back-icon {
	font-size: 16px;
	color: var(--brand-6);
	margin-left: 4px;
	cursor: pointer;
	position: relative;
	z-index: 667;
	&:hover {
		font-size: 18px;
	}
}
::v-deep .custom-upload-file-content {
	width: 100%;
}
::v-deep .el-input {
	box-sizing: border-box;
	.el-input__inner {
		box-sizing: border-box;
	}
}

.suffix {
	display: flex;
	align-items: center;
	justify-content: flex-end;
	margin-top: 8px;
}
.setting-title {
	color: var(--brand-6);
	font-weight: 600;
	margin-bottom: 12px;
	display: flex;
	align-items: center;
	&::before {
		content: '';
		width: 3px;
		height: 16px;
		border-radius: 3px;
		display: block;
		background: var(--brand-6);
		margin-right: 4px;
	}
}
.input-label {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 8px;
	&-right {
		display: flex;
		align-items: center;
		&-number {
			font-weight: 400;
			font-size: 12px;
			color: #b9bdc9;
			line-height: 22px;
		}
		&-clear {
			margin-left: 16px;
			font-weight: 400;
			font-size: 12px;
			color: #737a94;
			line-height: 22px;
			cursor: pointer;
		}
	}
}
.label-text {
	display: flex;
	align-items: center;
	font-weight: 500;
	font-size: 14px;
	color: #2f446b;
	line-height: 22px;
	margin-bottom: 8px;
	.tip-icon {
		margin-left: 16px;
		color: #737a94;
		font-size: 16px;
	}
	.input-label-tip {
		margin-left: 4px;
		font-weight: 400;
		font-size: 12px;
		color: #737a94;
		line-height: 22px;
	}
}
.required {
	&:before {
		content: '*';
		font-weight: 400;
		font-size: 16px;
		color: #e34d59;
		margin-right: 4px;
	}
}
.setting-content {
	height: 100%;
	display: flex;
	flex-direction: column;
}
.title {
	padding: 16px 20px;
	border-bottom: 1px solid #e3ebf2;
	display: flex;
	align-items: center;
}
.title-icon {
	width: 24px;
	height: 24px;
}
.title-text {
	font-weight: 600;
	font-size: 20px;
	color: #15224c;
	line-height: 23px;
	margin-left: 9px;
}
::v-deep .el-form-item {
	margin-bottom: 32px !important;
}
.content-buttons {
	display: flex;
	align-items: center;
	flex-wrap: wrap;
	margin-top: 10px;
	gap: 12px;
	&-item {
		flex: 1;
		height: 58px;
		background: linear-gradient(158deg, #f1f5f8 0%, #f1f5f8 100%);
		border-radius: 6px;
		cursor: pointer;
		display: flex;
		align-items: center;
		justify-content: center;
		flex-direction: column;
		font-weight: 400;
		font-size: 14px;
		color: #2f446b;
		line-height: 22px;
		&-icon {
			font-size: 16px;
			margin-bottom: 4px;
			color: #2f446b;
		}
	}
}
.buttons {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-top: 10px;
	gap: 13px;
	&-item {
		flex: 1;
		height: 58px;
		background: linear-gradient(158deg, #f1f5f8 0%, #f1f5f8 100%);
		border-radius: 6px;
		cursor: pointer;
		display: flex;
		align-items: center;
		justify-content: center;
		flex-direction: column;
		font-weight: 400;
		font-size: 14px;
		color: #2f446b;
		line-height: 22px;
	}
}
</style>
