<template>
	<div class="title">
		<div class="title-left">
			<i class="coos-iconfont home-icon icon-home" title="首页" @click="toHome"></i>
			<div class="line"></div>
			<span v-if="!isEdit && !loading" class="title-text" @click="focusInput">
				{{ docTitle || '未命名文档' }}
			</span>
			<el-input v-else ref="titleInput" v-model="newTitle" @blur="saveTitle"></el-input>
			<i v-if="loading" class="el-icon-loading loading-icon"></i>
			<div v-if="current === 3 && pathType !== 'error'" class="tip">
				<i class="coos-iconfont save-icon icon-baocun"></i>
				<span>文稿自动保存</span>
				<i v-if="saveSuccess" class="coos-iconfont save-success-icon icon-xuanzhong"></i>
			</div>
		</div>
		<div class="title-right">
			<slot name="right"></slot>
		</div>
	</div>
</template>
<script>
import { saveOutline } from '@/api/modules/coos-write';
import { updateProofreadTitle } from '@/api/modules/error-correction';

export default {
	name: 'TitleIndex',
	props: {
		docTitle: {
			type: String,
			default: () => {
				return '';
			}
		},
		id: {
			type: String,
			default: () => {
				return '';
			}
		},
		current: {
			type: Number,
			default: () => {
				return 1;
			}
		},
		saveSuccess: {
			type: Boolean,
			default: () => {
				return false;
			}
		},
		done: {
			type: Boolean,
			default: () => {
				return false;
			}
		}
	},
	data() {
		return {
			loading: false,
			isEdit: false,
			newTitle: ''
		};
	},
	computed: {
		pathType() {
			return this.$route.query.pathType;
		}
	},
	methods: {
		focusInput() {
			if (!this.done) {
				this.$message.warning('请耐心等待文章生成完毕');
				return;
			}
			this.newTitle = this.docTitle || '未命名文档';
			this.isEdit = true;
			this.$nextTick(() => {
				this.$refs.titleInput.focus();
			});
		},
		saveTitle() {
			if (this.newTitle === this.docTitle || !this.id) {
				this.isEdit = false;
				return;
			}
			if (!this.newTitle) {
				this.$message.error('文稿标题不能为空！');
				this.isEdit = false;
			}
			this.$confirm('确认修改标题?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			})
				.then(async () => {
					this.loading = true;
					if (this.$route.query.pathType === 'error') {
						await updateProofreadTitle({ title: this.newTitle, id: this.id });
					}
					await saveOutline(
						{
							saveType: 'title',
							title: this.newTitle
						},
						this.id
					);
					this.loading = false;
					this.$emit('changeTitle', this.newTitle);
					this.isEdit = false;
				})
				.catch(err => {
					this.newTitle = this.docTitle;
					this.isEdit = false;
				});
		},
		toHome() {
			if (this.pathType === 'error') {
				this.$router.go(-1);
			} else {
				this.$emit('changeCurrent', 1);
			}
		}
	}
};
</script>
<style scoped lang="scss">
.title {
	height: 56px;
	padding: 0 16px;
	border-bottom: 1px solid #e3ebf2;
	display: flex;
	align-items: center;
	justify-content: space-between;
	width: 100%;
	background: #ffffff;
	&-left {
		display: flex;
		align-items: center;
	}
}
.home-icon {
	font-size: 24px;
	margin-right: 14px;
	font-weight: 600;
	cursor: pointer;
}
.line {
	height: 22px;
	width: 1px;
	background: #d9e2ec;
	margin-right: 4px;
}
.title-text {
	font-weight: 500;
	font-size: 20px;
	color: #15224c;
	line-height: 22px;
	@include aLineEllipse;
}
.tip {
	flex-shrink: 0;
	font-weight: 400;
	font-size: 14px;
	color: #737a94;
	line-height: 22px;
	display: flex;
	align-items: center;
}
.save-icon {
	font-size: 16px;
	margin: 0 4px 0 12px;
}
.save-success-icon {
	font-size: 16px;
	color: #00bb00;
	margin-left: 8px;
}
</style>
