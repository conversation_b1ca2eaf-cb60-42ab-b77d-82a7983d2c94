<template>
	<el-dialog
		title="图片配置"
		:visible.sync="visible"
		class="desk-el-rent-dialog-footer"
		top="0"
		:before-close="cancel"
		width="80%"
	>
		<div class="content">
			<div class="content-left">
				<div v-if="!isEmpty(answerNewItem)" class="insert-button" @click="tabsActive">
					{{ activeAnswer === 2 ? '切换原始图表' : '切换替换后图表' }}
				</div>
				<div class="chart-content">
					<messageChart
						ref="messageChart"
						:key="chartKey"
						:item="activeAnswer === 2 ? answerNewItem : answerItem"
						:item-index="0"
						class="chart"
						:style="{ width: newWidth + 'px' }"
					></messageChart>
				</div>
				<div class="utils">
					<div class="utils-item">
						宽：
						<el-input v-model="width" @input="changeChart('width')">
							<div slot="suffix" class="suffix">px</div>
						</el-input>
					</div>
					<div class="utils-item">
						高：
						<el-input v-model="height" @input="changeChart('height')">
							<div slot="suffix" class="suffix">px</div>
						</el-input>
					</div>
				</div>
				<i class="coos-iconfont icon-tuozhuai drag-icon" @mousedown="startDrag"></i>
			</div>
			<div class="content-right" :style="{ width: rightWidth + 'px' }">
				<div ref="messageContent" class="ai-content" @scroll="scroll">
					<div
						v-for="(message, index) of messageList"
						:key="index"
						class="message-item"
						:class="message.type === 'query' ? 'out' : 'in'"
					>
						<OutMessage v-if="message.type === 'query'" :message="message"></OutMessage>
						<InMessage
							v-else
							:ref="'inmessage-' + index"
							:chartkey="key"
							:message-index="index"
							:message="message"
							@replaceEchart="replaceEchart"
							@changeCheckSource="changeCheckSource"
						></InMessage>
					</div>
				</div>
				<div class="message">
					<el-autocomplete
						v-model="answerQuestion"
						style="flex: 1"
						:fetch-suggestions="querySearch"
						placeholder="请输入你想对图表进行怎样的调整"
						@keydown.enter.native.prevent="send"
						@select="handleSelect"
					>
						<i slot="suffix" class="coos-iconfont icon-nav-bottom suffix-icon"></i>
						<template slot-scope="{ item }">
							<div class="select-name">{{ item }}</div>
						</template>
					</el-autocomplete>
					<div v-if="loading" class="submit" @click.stop="pause">
						<i class="coos-iconfont submit-icon icon-zanting"></i>
					</div>
					<div v-else class="submit" :class="{ disabled: !answerQuestion }" @click.stop="send">
						<i class="coos-iconfont submit-icon icon-fasong1"></i>
					</div>
				</div>
			</div>
		</div>
		<div slot="footer">
			<el-button @click="handleClose">取消</el-button>
			<el-button type="primary" @click="handleAdd">插入</el-button>
		</div>
	</el-dialog>
</template>
<script>
import messageChart from '@/views/coos/message/item/message-chart.vue';
import OutMessage from '@/views/draft-writing/components/out-message.vue';
import InMessage from '@/views/draft-writing/components/in-message.vue';
import { debounce, deepClone, isEmpty } from '@/utils';
import { newFetchAiQuery } from '@/api/modules/coos';
import queryMixins from '@/views/coos/utils/query-mixins';
import { Base64 } from 'js-base64';
export default {
	name: 'AiEchart',
	components: {
		OutMessage,
		messageChart,
		InMessage
	},
	mixins: [queryMixins],
	data() {
		return {
			key: 0,
			startX: 0, // 拖拽初始位置
			initWidth: 0, // 开始拖拽时候初始宽度
			rightWidth: 500, // 右边弹性宽度
			width: 300,
			newWidth: 300,
			height: 0,
			chartKey: 0,
			lockAutoScroll: false,
			loading: false,
			controller: null, // 请求实例
			messageId: 0, // 消息id
			restaurants: ['帮我换成折线图', '帮我换成柱状图', '帮我换成饼状图'], // 建议
			answerQuestion: '', // 输入框内容
			visible: false, // 弹窗显隐
			messageIndex: '', // 消息索引
			answerIndex: '', // 答案在消息中的索引
			computedId: '', // 选中的智能体id
			answerItem: {}, // 初始需要渲染的答案json 1
			answerNewItem: {}, // 替换渲染的答案json 2
			activeAnswer: 1, //  展示的答案
			messageList: [], // 答案列表
			newAnswerItems: [] // 选择的答案
		};
	},
	methods: {
		isEmpty,
		/**拖拽调整宽度*/
		startDrag(e) {
			e.preventDefault();
			this.startX = e.clientX;
			this.initWidth = this.rightWidth;
			document.addEventListener('mousemove', this.drag);
			document.addEventListener('mouseup', this.dragEnd);
		},
		drag(e) {
			e.preventDefault();
			this.rightWidth = this.rightWidth < 350 ? 350 : this.initWidth - (e.clientX - this.startX);
		},
		dragEnd() {
			document.removeEventListener('mousemove', this.drag);
			document.removeEventListener('mouseup', this.dragEnd);
			this.key += 1;
		},
		/**自定义插入图片*/
		handleAdd() {
			// 直接插入
			this.insertImage();
			// 替换原始数据
			// const chartAnswerItems = this.messageList.filter(item => item.answerType === 'chart');
			// if (chartAnswerItems.length === 0) {
			// 	this.$message.error('请先选择一个答案');
			// 	return;
			// }
			// if (chartAnswerItems.length > 1) {
			// 	if (!this.newAnswerItems || !this.newAnswerItems.length) {
			// 		this.$message.error('请选择一个答案');
			// 		return;
			// 	}
			// 	替换原始数据
			// 	this.$emit(
			// 		'setEchart',
			// 		this.messageIndex,
			// 		this.answerIndex,
			// 		this.answerItem,
			// 		this.computedId,
			// 		this.newAnswerItems[0].answer
			// 	);
			// 	this.visible = false;
			// 	return;
			// }
			// let obj = chartAnswerItems[0];
			// 	this.$emit(
			// 	'setEchart',
			// 	this.messageIndex,
			// 	this.answerIndex,
			// 	this.answerItem,
			// 	this.computedId,
			// 	obj.answer[0].answer
			// );
			this.visible = false;
		},
		/**插入图片*/
		insertImage(ref) {
			let html = [
				{
					type: 'html',
					content: `<img src='${this.$refs.messageChart.getImage()}'/></br>`
				}
			];
			this.$emit('replace', 'insert', html);
		},
		/**切换图表展示*/
		tabsActive() {
			this.activeAnswer = this.activeAnswer === 1 ? 2 : 1;
			this.chartKey++;
		},
		/**应用图表*/
		replaceEchart(source) {
			if (/转换失败/.test(JSON.stringify(source))) {
				return;
			}
			this.answerNewItem = { ...source, height: this.height + 'px' };
			this.activeAnswer = 2;
			this.chartKey++;
		},
		/**改变应用图表实例*/
		changeCheckSource(source, messageIndex) {
			this.newAnswerItems = source[0].answer || [];
		},
		/**处理关闭*/
		handleClose() {
			this.visible = false;
		},
		/**改变图表预览*/
		changeChart: debounce(
			function (type) {
				if (type === 'height') {
					this.answerItem = {
						...this.answerItem,
						height: this.height + 'px'
					};
					if (!isEmpty(this.answerNewItem)) {
						this.answerNewItem = {
							...this.answerNewItem,
							height: this.height + 'px'
						};
					}
				} else {
					this.newWidth = this.width;
				}
				this.chartKey += 1;
			},
			500,
			false
		),
		/**暂停*/
		pause() {
			try {
				if (this.controller) {
					this.controller.abort();
					let obj = this.messageList.find(item => item.id === this.currentAnswerId);
					obj.answer.push({
						answerType: 'normal',
						answer: '（回答中断，请重新生成！）'
					});
					obj.isPause = true;
					obj.done = true;
					this.agentCacheAnswer = {}; // 重置缓冲区
					this.cacheAnswer = ''; // 重置缓冲区
					this.loading = false;
				}
			} catch (e) {
				console.log(e);
			}
			this.controller = null;
			this.done = true;
		},
		/**发送*/
		send() {
			if (this.loading || !this.answerQuestion.trim()) return;
			let { answerId } = this.addTemporaryMessage(this.answerQuestion);
			this.currentAnswerId = answerId;
			this.lockAutoScroll = false;
			this.aiEchart(answerId);
			this.toBottom();
		},
		/** ai转图表 */
		async aiEchart(answerId) {
			this.loading = true;
			let params = {
				query:
					'现有如下的一个echarts json配置参数：\n' +
					`${JSON.stringify(this.answerNewItem.answer || this.answerItem.answer)}\n` +
					'\n' +
					'请根据用户问题，编写一个js修改json配置的方法，方法名固定叫changeConfig，输入参数为这个json配置，输出参数为新的JSON配置。\n' +
					'js函数要求如下：\n' +
					'1、处理过程不要直接在输入参数json配置上做赋值修改，deepClone一个新的json配置进行修改并返回。\n' +
					'2、在赋值修改新json配置中的字段的时候，赋值取用的数据还是在输入参数json配置里面获取。\n' +
					'3、防止直接修改对象某个字段，导致该字段数据结构发生变化，后面其他字段在赋值时候取用该字段就会引起报错\n' +
					'4、检查一下从原json配置对象中取数据的时候是否有对应的字段，给了你输入json，请结合数据结构进行操作，不要无中生有\n' +
					'5、将新的json配置和旧的json配置对比一下，检查是否按照要求修改了\n' +
					'\n' +
					`用户问题：${this.answerQuestion}\n` +
					'\n' +
					'注意：\n' +
					'-用户提问内容与图表处理是相关，则进行处理并使用```func```包裹完整的代码。\n' +
					'- 用户提问内容与图表处理无关，则直接根据大模型已有知识进行提问内容回答。\n' +
					'- 不添加任何解释性语句。'
			};
			this.answerQuestion = '';
			this.controller = new AbortController();
			const { signal } = this.controller;
			let obj = this.messageList.find(item => item.id === answerId);
			let aiRes;
			try {
				aiRes = await newFetchAiQuery(params, signal);
				// 暂停时候特殊处理
				if (!aiRes) return;
			} catch (err) {
				obj.answer = [
					{
						answerType: 'normal',
						answer: '回答异常，请重新尝试！'
					}
				];
				obj.done = true;
				this.finish();
				this.$message.error(err.message || err);
				return;
			}
			// // 获取AI答案
			const reader = aiRes.body.getReader(); // 流式读取器
			const decoder = new TextDecoder('utf-8');
			let readDone = false; // 读取完毕
			// 流式输出
			while (!readDone) {
				let { value, done } = await reader.read();
				readDone = done;
				let res;
				try {
					res = decoder.decode(value);
					if (/"code":50/.test(res)) {
						let r = JSON.parse(res.replace('data:', ''));
						res = [r.message || '服务器异常', 'noAnswer'];
					} else if (/"code":401/.test(res)) {
						res = ['登录失效，请重新登录！', 'noAnswer'];
						this.REMOVE_INFO();
						this.$message.error('登录失效，请重新登录！');
						setTimeout(() => {
							this.$router.push('/login');
						}, 1500);
					} else {
						// res = res.replace(/data:/gi, ''); // 解析成JSON，去掉data:开头的字符串
						res = res.split('data:');
						res = res.map(item => {
							return item.indexOf('noAnswer') > -1 ? 'noAnswer' : Base64.decode(item);
						});
						if (readDone && !res.includes('noAnswer')) {
							res.push('noAnswer');
						}
					}
					// 累加数据
					res.forEach(value => {
						this.cacheAnswer += value === 'noAnswer' ? '' : value;
						// 统一处理
						obj.answer = this.processStreamResponse(
							this.cacheAnswer,
							deepClone(this.answerNewItem.answer || this.answerItem.answer)
						);
					});
					this.toBottom();
				} catch (err) {
					console.log('流式解析报错--------------', err);
				}
			}
			// 如果回答完成之后都没有答案
			if (Object.keys(obj.agentAnswer).length === 0 && obj.answer.length === 0) {
				obj.answer = [
					{
						answerType: 'normal',
						answer: '抱歉，未找到你想要的答案！'
					}
				];
			}
			obj.done = true;
			// 为自己自定义标签添加事件监听
			// this.$nextTick(() => {
			// 	this.addEventLister();
			// });
			this.finish();
		},
		/**完成统一操作*/
		finish() {
			this.agentCacheAnswer = {}; // 重置缓冲区
			this.cacheAnswer = ''; // 重置缓冲区
			this.loading = false;
		},
		/**用函数处理图表数据进行答案结构处理*/
		processStreamResponse(streamText, jsonData) {
			const result = [];
			const functionStartMarker = '```func';
			const functionEndMarker = '```';

			// 查找函数块位置
			const functionStartIndex = streamText.indexOf(functionStartMarker);
			const functionEndIndex =
				functionStartIndex !== -1
					? streamText.indexOf(functionEndMarker, functionStartIndex + functionStartMarker.length)
					: -1;

			// 情况1: 没有检测到函数开始标记
			if (functionStartIndex === -1) {
				if (streamText.trim()) {
					result.push({
						answerType: 'normal',
						answer: streamText.trim()
					});
				}
				return result;
			}

			// 情况2: 检测到函数开始但未检测到结束标记
			if (functionEndIndex === -1) {
				// 添加函数开始前的普通文本
				if (functionStartIndex > 0) {
					const beforeText = streamText.substring(0, functionStartIndex).trim();
					if (beforeText) {
						result.push({
							answerType: 'normal',
							answer: beforeText
						});
					}
				}

				// 添加转换中提示
				result.push({
					answerType: 'normal',
					answer: '【图表数据转换中，请稍后...】'
				});

				return result;
			}

			// 情况3: 检测到完整的函数块
			// 添加函数开始前的普通文本
			if (functionStartIndex > 0) {
				const beforeText = streamText.substring(0, functionStartIndex).trim();
				if (beforeText) {
					result.push({
						answerType: 'normal',
						answer: beforeText
					});
				}
			}

			// 提取并执行函数代码
			const functionCode = streamText
				.substring(functionStartIndex + functionStartMarker.length, functionEndIndex)
				.trim();
			try {
				const chartFunction = new Function(
					'config',
					`
						${functionCode}
						return changeConfig(config);
					`
				);
				const chartResult = chartFunction(jsonData);
				result.push({
					answerType: 'chart',
					answer: chartResult,
					height: (jsonData.customHeight || 0) + 400 + 'px',
					hideUtils: true
				});
			} catch (error) {
				console.log('图表函数执行失败:', error);
				result.push({
					answerType: 'normal',
					answer: '【图表数据转换失败】'
				});
			}

			// 添加函数结束后的普通文本
			const afterText = streamText.substring(functionEndIndex + functionEndMarker.length).trim();
			if (afterText) {
				result.push({
					answerType: 'normal',
					answer: afterText
				});
			}

			return result;
		},
		/**调用 callback 返回建议列表的数据*/
		querySearch(queryString, cb) {
			cb(
				this.restaurants
					? this.restaurants.filter(item => item.indexOf(queryString) > -1)
					: this.restaurants
			);
		},
		/**输入框自定义补全*/
		handleSelect(item) {
			this.answerQuestion = item;
		},
		/**关闭*/
		cancel() {
			this.visible = false;
		},
		/**
		 * @method 添加临时消息
		 * @param {String} message 追加消息
		 * @param {Boolean} addAnswer 是否追加答案
		 * */
		addTemporaryMessage(message, addAnswer = true) {
			let answerId = 'answer-' + this.messageId;
			let queryId = 'query-' + this.messageId;
			// 添加自己发送的消息
			if (message) {
				this.messageList.push({
					type: 'query',
					id: queryId,
					query: message,
					createTime: new Date().toLocaleString()
				});
			}
			// 添加答案占位消息
			if (addAnswer) {
				this.messageList.push({
					type: 'answer',
					query: message, // 问题
					id: answerId, // 问答ID
					answer: [], // ai答案
					isReplaceEchart: true, // 是否替换图表
					agentAnswer: {}, // 多个智能体答案
					done: false, // 是否回答完毕
					showUtil: false, // 是否显示工具栏
					isPause: false, // 单条数据是否被暂停
					source: '' // 来源
				});
			}
			this.messageId++;
			this.toBottom();
			return { queryId, answerId };
		},
		/**打开*/
		open(messageIndex, answerIndex, answerItem, computedId) {
			// 还原问答
			this.activeAnswer = 1;
			this.messageList = [];
			this.loading = false;
			this.addTemporaryMessage('', true);
			this.messageList[0].answer = '你好，请问有什么可以帮你？';
			this.messageList[0].done = true;
			this.messageList[0].showUtil = false;
			this.answerQuestion = '';
			this.width = this.newWidth = 300;
			this.answerNewItem = {};
			// 配置信息
			this.messageIndex = messageIndex;
			this.computedId = computedId;
			this.answerIndex = answerIndex;
			this.answerItem = answerItem;
			this.height = parseInt(this.answerItem.height.replace('px', ''));
			this.chartKey += 1;
			this.visible = true;
		},
		/**滚动到最底部*/
		toBottom() {
			if (this.lockAutoScroll) return;
			this.$nextTick(() => {
				let messageListElement = this.$refs.messageContent;
				if (messageListElement) {
					messageListElement.scroll({
						top: messageListElement.scrollHeight,
						left: 0,
						behavior: 'auto'
					});
				}
			});
		},
		/**监听滚动*/
		scroll(e) {
			let event = e.srcElement;
			if (event.scrollTop + event.clientHeight >= event.scrollHeight - 10) {
				this.lockAutoScroll = false;
			} else {
				this.lockAutoScroll = true;
			}
		}
	}
};
</script>
<style scoped lang="scss">
.content {
	height: 100%;
	display: flex;
	align-items: center;
	background: #f5f6fa;
	&-left {
		border: 1px solid #f5f6fa;
		border-radius: 6px 0 0 6px;
		height: 100%;
		flex: 1;
		display: flex;
		flex-direction: column;
		position: relative;
		overflow: hidden;
	}
	&-right {
		border: 1px solid #f5f6fa;
		border-radius: 0 6px 6px 0;
		background: #ffffff;
		height: 100%;
		display: flex;
		flex-direction: column;
		.ai-content {
			flex: 1;
			overflow: auto;
			padding: 24px 20px;
		}
	}
}
.disabled {
	background: $holderTextColor !important;
	cursor: not-allowed !important;
}
.suffix-icon {
	font-size: 16px;
	color: #737a94;
	line-height: 32px;
}
.submit {
	cursor: pointer;
	width: 30px;
	height: 30px;
	background: #2591f7;
	border-radius: 18px;
	display: flex;
	align-items: center;
	justify-content: center;
	&-icon {
		font-size: 16px;
		color: #ffffff;
	}
}
.message-item {
	display: flex;
	margin-bottom: 24px;
}
.message {
	display: flex;
	align-items: center;
	gap: 12px;
	padding: 8px 20px;
	border-top: 1px solid #f5f6fa;
}
.out {
	justify-content: flex-end;
}
.utils {
	background: #ffffff;
	padding: 12px;
	gap: 24px;
	@include flexBox(space-around);
	&-item {
		@include flexBox();
	}
}
.chart {
	flex: 1;
	@include flexBox();
}
.suffix {
	line-height: 32px;
}
.insert-button {
	position: absolute;
	top: 10px;
	left: 10px;
	margin-bottom: 24px;
	font-size: 12px;
	color: #ffffff;
	border-radius: 6px;
	padding: 5px 12px;
	background: #187bf3;
	cursor: pointer;
	z-index: 66;
}
.chart-content {
	flex: 1;
	overflow: auto;
	padding: 48px 16px 16px;
}
.drag-icon {
	position: absolute;
	cursor: ew-resize;
	right: 4px;
	top: calc(50% - 9px);
	font-size: 16px;
}
</style>
