<template>
	<div class="source" :style="{ width: width + 'px' }">
		<div class="source-title">
			<img src="@/assets/images/write/info-source.png" class="logo-icon" alt="" />
			<span>引用资料</span>
			<i v-show="loading" class="el-icon-loading loading-icon"></i>
			<span v-show="loading" class="loading-text">更新中...</span>
		</div>
		<div class="source-content">
			<div
				v-for="(item, index) of sourceArr"
				:key="index"
				:ref="'info-' + item.flag"
				class="source-item"
				:class="{
					'source-item-select': selectSourceArr.includes(item.flag)
				}"
				@click="change(item.flag)"
			>
				<div
					class="flag-source"
					:class="{
						'flag-source-select': selectSourceArr.includes(item.flag)
					}"
				>
					<i class="coos-iconfont flag-source-icon" :class="getOriginText(item).icon"></i>
					<span>
						{{ getOriginText(item).text }}
					</span>
				</div>
				<div
					v-if="selectSourceArr.includes(item.flag) && currentOpenId === item.flag"
					class="source-item-title"
					:class="{ 'close-status': currentOpenId !== item.flag }"
				>
					<v-md-preview :text="mdContent"></v-md-preview>
				</div>
				<div
					v-else-if="selectSourceArr.includes(item.flag) && markContent"
					class="source-item-title"
					:class="{ 'close-status': currentOpenId !== item.flag }"
				>
					<span
						v-for="(markText, markIndex) of item.page_content.split(markContent)"
						:key="'mark-' + markIndex"
					>
						<span v-if="markIndex > 0" style="color: var(--brand-6); font-size: 18px">
							{{ markContent }}
						</span>
						<span>{{ markText }}</span>
					</span>
				</div>
				<div
					v-else
					class="source-item-title"
					:class="{ 'close-status': currentOpenId !== item.flag }"
				>
					{{ item.page_content }}
				</div>
				<div
					v-if="item.disk_file && item.disk_file.name"
					class="source-item-file"
					@click.stop="handleSource(item)"
				>
					{{ item.disk_file.name
					}}{{ item.disk_file.question ? `:${item.disk_file.question}` : '' }}
				</div>
				<div class="utils">
					<div class="utils-btn" @click="open(item.flag)">
						{{ currentOpenId === item.flag ? '收起' : '展开' }}
						<i v-if="currentOpenId === item.flag" class="coos-iconfont icon-nav-top"></i>
						<i v-else class="coos-iconfont icon-nav-bottom"></i>
					</div>
				</div>
			</div>
			<el-empty
				v-if="sourceArr.length === 0"
				:image="assetsUrl + '/common/img/no-data.png'"
				description="暂无数据"
			></el-empty>
		</div>
	</div>
</template>
<script>
import { previewFile } from '@/utils';
import { assetsUrl, serveUrl } from '@/config';

export default {
	name: 'SourceIndex',
	props: {
		width: {
			type: Number,
			default: () => {
				return 463;
			}
		},
		selectSourceArr: {
			type: Array,
			default: () => {
				return [];
			}
		},
		sourceArr: {
			type: Array,
			default: () => {
				return [];
			}
		},
		markContent: {
			type: String,
			default: () => {
				return '';
			}
		},
		loading: {
			type: Boolean,
			default: () => {
				return false;
			}
		}
	},
	data() {
		return {
			assetsUrl,
			currentOpenId: ''
		};
	},
	computed: {
		mdContent() {
			let md = this.sourceArr.find(item => item.flag === this.currentOpenId);
			md = md.page_content.replaceAll(
				this.markContent,
				`<span style='color:var(--brand-6);font-size:18px'>${this.markContent}</span>`
			);
			return md;
		}
	},
	watch: {
		selectSourceArr(newVal) {
			if (newVal.length) {
				this.currentOpenId = newVal[0];
				if (this.$refs['info-' + newVal[0]] && this.$refs['info-' + newVal[0]].length) {
					this.$refs['info-' + newVal[0]][0].scrollIntoView({ behavior: 'smooth', block: 'start' });
				}
			}
		}
	},
	methods: {
		change(id) {
			if (this.selectSourceArr.includes(id)) return;
			this.$emit('changeSourceSelect', id);
		},
		/**获取来源文本*/
		getOriginText(item) {
			let origin = item.origin;
			// text：参考文本、file：上传文档、metadata：知识库、agent:智能体
			let text = '';
			let icon = '';
			switch (origin) {
				case 'text':
					text = '参考文本';
					icon = 'icon-zhishiku1';
					break;
				case 'file':
					text = '上传文档';
					icon = 'icon-dakai';
					break;
				case 'metadata':
					text = item.disk_file && item.disk_file.spaceName ? item.disk_file.spaceName : '知识库';
					icon = 'icon-zhishiku1';
					break;
				case 'agent':
					text = '数据底座';
					icon = 'icon-shujuyuanpeizhi';
					break;
				case 'model':
					text = '大模型';
					icon = 'icon-shenduss';
					break;
				case 'web':
					text = '网络';
					icon = 'icon-lianwangsousuo';
					break;
				default:
					text = '未知来源';
			}
			return { text, icon };
		},
		open(flag) {
			this.currentOpenId = this.currentOpenId === flag ? '' : flag;
		},
		getUrl(url) {
			return (serveUrl || window.location.origin) + url;
		},
		handleSource(item) {
			if (item.origin === 'agent' && item.disk_file.question) {
				this.$emit('queryAi', item.disk_file.question);
			} else if (item.disk_file?.fileUrlPath) {
				previewFile(item.disk_file.fileUrlPath);
			}
		}
	}
};
</script>
<style scoped lang="scss">
.source {
	min-width: 350px;
	height: 100%;
	flex-shrink: 0;
	margin-left: 8px;
	background: #ffffff;
	display: flex;
	flex-direction: column;
	.logo-icon {
		height: 32px;
		width: 32px;
		margin-right: 4px;
	}
	&-title {
		height: 56px;
		padding: 0 20px;
		display: flex;
		align-items: center;
		font-weight: 600;
		font-size: 20px;
		color: #15224c;
		line-height: 23px;
		border-bottom: 1px solid #e3ebf2;
	}
	&-content {
		flex: 1;
		overflow: auto;
		padding: 26px 20px;
	}
	&-item {
		margin-bottom: 10px;
		padding: 24px 20px 20px;
		background: #f5f6fa;
		border-radius: 12px;
		position: relative;
		&-title {
			overflow: hidden;
			font-weight: 400;
			font-size: 14px;
			color: #15224c;
			line-height: 22px;
		}
		&-file {
			margin-top: 8px;
			font-weight: 400;
			font-size: 12px;
			color: #737a94;
			line-height: 14px;
			cursor: pointer;
		}
		.item-source {
			display: flex;
			align-items: center;
			font-weight: 400;
			font-size: 12px;
			color: #a6a9b7;
			line-height: 14px;
			cursor: pointer;
			margin-top: 17px;
			&-left {
				margin-right: 4px;
				width: 16px;
				height: 16px;
				background: #75a3f8;
				border-radius: 19px;
				text-align: center;
				&-icon {
					font-size: 12px;
					line-height: 16px;
					color: #ffffff;
				}
			}
			&-right {
				flex: 1;
				@include aLineEllipse;
			}
		}
	}
	&-item-select {
		background: #ffffff;
		border-radius: 9px;
		border: 1px solid #2591f7;
	}
}
.utils {
	display: flex;
	align-items: center;
	justify-content: flex-end;
	padding: 16px 0 0;
	&-btn {
		color: var(--brand-6);
		font-size: 14px;
		cursor: pointer;
	}
}
.close-status {
	@include countEllipse(5);
}
.flag-source {
	position: absolute;
	top: 0;
	left: 0;
	background: var(--brand-2);
	color: #ffffff;
	font-size: 12px;
	padding: 2px 4px;
	border-radius: 6px 0 0 0;
	@include flexBox();
	&-icon {
		margin-right: 2px;
	}
}
.flag-source-select {
	background: var(--brand-4);
}
.loading-icon {
	font-size: 16px;
	margin: 0 6px;
	color: var(--brand-6);
}
.loading-text {
	font-size: 12px;
	color: var(--brand-6);
}
::v-deep .github-markdown-body {
	padding: 0;
}
</style>
<style>
.write-step3-popper {
	max-width: 400px;
}
</style>
