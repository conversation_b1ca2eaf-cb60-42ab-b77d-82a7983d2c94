<template>
	<el-dialog title="添加到素材库" :visible.sync="dialogVisible" width="80%">
		<div class="header">
			<div class="header-btn">批量加入白名单</div>
			<div class="header-btn">批量加入黑名单</div>
		</div>
		<div class="table">
			<el-table :data="tableData" style="width: 100%">
				<el-table-column type="selection" width="55"></el-table-column>
				<el-table-column prop="originalContent" label="原文内容"></el-table-column>
				<el-table-column prop="correctedContent" label="修改建议"></el-table-column>
				<el-table-column label="操作" width="190">
					<template slot-scope="scope">
						<el-button type="text" @click="handleAddToWhiteList(scope.$index, scope.row, 'white')">
							加入白名单
						</el-button>
						<el-divider direction="vertical"></el-divider>
						<el-button type="text" @click="handleAddToWhiteList(scope.$index, scope.row, 'black')">
							加入黑名单
						</el-button>
					</template>
				</el-table-column>
			</el-table>
		</div>
		<span slot="footer" class="dialog-footer">
			<el-button @click="dialogVisible = false">取 消</el-button>
			<el-button type="primary" @click="dialogVisible = false">确 定</el-button>
		</span>
	</el-dialog>
</template>

<script>
import { saveWhiteBlackList } from '@/api/modules/error-correction';

export default {
	name: 'AddMaterial',
	data() {
		return {
			dialogVisible: false,
			id: '',
			tableData: []
		};
	},
	methods: {
		open(data, id) {
			this.id = id;
			this.tableData = data;
			this.dialogVisible = true;
		},
		handleAddToWhiteList(index, row, description) {
			let arr = [
				{
					remark: row.originalContent,
					tags: row.errorType,
					proofreadId: this.id,
					type: description,
					targetWord: row.originalContent
				}
			];
			saveWhiteBlackList(arr).then(res => {
				console.log(res, 111);
			});
		}
	}
};
</script>

<style scoped lang="scss">
::v-deep .el-dialog__body {
	padding-top: 6px;
}
.header {
	display: flex;
	justify-content: end;
	align-items: center;
	margin-bottom: 12px;
	&-btn {
		cursor: pointer;
		padding: 7px 9px;
		background: #ffffff;
		margin-left: 9px;
		border-radius: 6px 6px 6px 6px;
		border: 1px solid #bccadb;
		font-family: PingFang SC, PingFang SC;
		font-weight: 400;
		font-size: 14px;
		color: #2f446b;
		line-height: 22px;
		display: flex;
		align-items: center;
		justify-content: center;
	}
}
.table {
	background: #ffffff;
	border-radius: 9px 9px 9px 9px;
	border: 1px solid #f0f0f0;
	padding: 12px;
	::v-deep .el-table td,
	::v-deep .el-table th.is-leaf {
		border-bottom: 1px solid #f0f0f0 !important;
	}
}
</style>
