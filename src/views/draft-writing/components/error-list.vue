<template>
	<div class="source" :style="{ width: width + 'px' }">
		<div class="source-title">
			<div class="title-left">
				<img src="@/assets/images/write/error-correction-icon.png" class="logo-icon" alt="" />
				<span>文稿纠错</span>
			</div>
			<div class="title-right">
				<div class="title-dropdown">
					<el-dropdown>
						<span class="send-text">
							{{ dropdownItem.content ? dropdownItem.content : '纠错侧重点' }}
							<i class="el-icon-arrow-down el-icon--right"></i>
						</span>
						<el-dropdown-menu slot="dropdown">
							<!--                    官方command指令报错    -->
							<el-dropdown-item
								v-for="item of errorListDown"
								:key="item.id"
								@click.native="dropdownClick(item)"
							>
								<span>{{ item.content }}</span>
							</el-dropdown-item>
						</el-dropdown-menu>
					</el-dropdown>
				</div>
				<div class="anew" @click="toNewText">
					<i class="coos-iconfont icon-loop"></i>
					<span>重新纠错</span>
				</div>
			</div>
		</div>
		<div class="content">
			<div class="content-title">
				共识别到
				<span class="title-num">{{ errorList.length }}</span>
				条项目问题
			</div>
			<div class="content-tab">
				<div
					v-for="tab in errorTabs"
					:key="tab.key"
					class="tab-item"
					:class="{ active: tab.key === activeTab }"
					@click="handleTabClick(tab.key)"
				>
					{{ tab.label }}·{{ tab.count }}
				</div>
			</div>
			<div class="content-list">
				<div class="list">
					<div
						v-for="item in newList"
						:key="item.id"
						:data-id="item.id"
						class="error-item"
						:class="{ 'is-checked': item.isChecked }"
					>
						<div v-if="item.lastDealResult === 'replaced'" class="replaced">已替换</div>
						<!--						<div class="checkbox">-->
						<!--							<el-checkbox-->
						<!--								v-model="item.isChecked"-->
						<!--								:disabled="isCheckboxDisabled(item)"-->
						<!--								@change="handleCheckboxChange(item)"-->
						<!--							></el-checkbox>-->
						<!--						</div>-->
						<div
							class="item-box"
							:class="item.isChecked ? 'active-item-box' : ''"
							@click="activeItem(item)"
						>
							<div class="item-box-content">
								<div class="original-content">
									<span>原文内容：</span>
									<span class="original-content-text">{{ item.originalContent }}</span>
								</div>
								<div class="corrected-content">
									<span>修改建议：</span>
									<el-input
										v-if="editInput === item.id"
										v-model="updateValue"
										type="textarea"
										:rows="2"
										placeholder="请输入内容"
									></el-input>
									<div v-else>
										<span class="corrected-content-text">{{ item.correctedContent }}</span>
										<i
											class="coos-iconfont icon-bianji edit-icon-class"
											@click.stop="edit(item)"
										></i>
									</div>
									<div v-if="editInput === item.id" class="handle show-handle">
										<i
											class="coos-iconfont icon-selected handle-icon"
											@click.stop="updateText(item.id)"
										></i>
										<i
											class="coos-iconfont icon-close_circle handle-icon"
											@click.stop="editInput = ''"
										></i>
									</div>
								</div>
							</div>
							<div class="operation">
								<div :class="['error-type', item.errorTypeStyle]">
									<span>{{ item.errorType }}</span>
								</div>
								<div class="item-btm">
									<div @click.stop="handleCommandItem('ignore', item)">忽略</div>
									<el-divider direction="vertical"></el-divider>
									<div @click.stop="handleCommandItem('replace', item)">
										{{ item.lastDealResult === 'replaced' ? '撤回' : '替换' }}
									</div>
									<el-divider direction="vertical"></el-divider>
									<el-dropdown
										@command="
											command => {
												handleCommandItem(command, item);
											}
										"
									>
										<span class="el-dropdown-link">
											<i class="coos-iconfont icon-more2 handle-icon"></i>
										</span>
										<el-dropdown-menu slot="dropdown">
											<el-dropdown-item command="copy">复制修改建议</el-dropdown-item>
											<el-dropdown-item command="addToLibrary">添加到素材库</el-dropdown-item>
										</el-dropdown-menu>
									</el-dropdown>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="footer">
			<el-checkbox v-model="selectAll">全选</el-checkbox>
			<div>
				<el-dropdown @command="handleCommand">
					<span class="el-dropdown-link">
						更多
						<i class="el-icon-arrow-down el-icon--right"></i>
					</span>
					<el-dropdown-menu slot="dropdown">
						<el-dropdown-item command="ignoreAll">忽略所有</el-dropdown-item>
						<el-dropdown-item command="addToLibrary">添加到素材库</el-dropdown-item>
					</el-dropdown-menu>
				</el-dropdown>
				<el-button @click="ignoreSelected">忽略</el-button>
				<el-button type="primary" @click="replaceAndAdopt(lastDealResult)">
					{{ lastDealResult === 'replaced' ? '一键撤回' : '采纳并替换原文' }}
				</el-button>
			</div>
		</div>
		<add-material ref="AddMaterial"></add-material>
	</div>
</template>

<script>
import AddMaterial from '@/views/draft-writing/components/add-material.vue';
import { getProofreadQualityType, updateDealResult } from '@/api/modules/error-correction';
import { copyText } from '@/wile-fire/ui/util/clipboard';

export default {
	name: 'ErrorList',
	components: { AddMaterial },
	props: {
		width: {
			type: Number,
			default: () => {
				return 463;
			}
		},
		selectSourceArr: {
			type: Array,
			default: () => {
				return [];
			}
		},
		sourceArr: {
			type: Array,
			default: () => {
				return [];
			}
		},
		markContent: {
			type: String,
			default: () => {
				return '';
			}
		}
	},
	data() {
		return {
			errorTabs: [],
			activeTab: 0, // 默认激活的标签
			currentOpenId: '',
			selectAll: false,
			editInput: '',
			updateValue: '',
			id: '',
			content: '',
			lastDealResult: '',
			errorListDown: [], // 纠错类型
			dropdownItem: {}, // 选中的纠错
			errorList: []
		};
	},
	computed: {
		newList() {
			if (this.activeTab == 0) {
				return this.errorList;
			} else {
				return this.errorList.filter(item => item.errorTypeKey === this.activeTab);
			}
		}
	},
	watch: {
		selectAll(val) {
			if (val) {
				this.errorList.forEach(item => {
					if (item.lastDealResult !== 'replaced') {
						item.isChecked = val;
					}
				});
				this.$emit('selectErrorText', this.errorList);
			} else {
				this.$emit('selectErrorText', [], this.errorList);
			}
		},

		errorList: {
			handler(newVal) {
				this.selectAll = newVal.every(item => item.isChecked);
				const checkedItems = this.errorList.filter(item => item.isChecked);
				if (checkedItems && checkedItems.length == 0) {
					this.lastDealResult = '';
				}
				const newCheckedItems = this.errorList.filter(item => !item.isChecked);
				this.$emit('selectErrorText', checkedItems, newCheckedItems);
			},
			deep: true
		}
	},
	// 在组件创建时调用的方法，用于获取校对质量类型列表
	created() {
		if (this.$route.query.pathType === 'error') {
			this.getProofreadQualityTypeList();
		}
	},

	methods: {
		handleMark(text) {
			console.log(text, '123123123123');
			// 找到对应的数据项
			const targetItem = this.errorList.find(
				item => item.originalContent === text || item.correctedContent === text
			);

			if (targetItem) {
				// 取消其他数据项的 isChecked 状态
				this.errorList.forEach(item => {
					item.isChecked = item.id === targetItem.id;
				});

				// 滚动到对应数据的位置
				this.$nextTick(item => {
					this.scrollToItem(targetItem);
				});
			}
		},
		/** 滚动到指定数据项的位置 */
		scrollToItem(item) {
			const itemElement = document.querySelector(`.error-item[data-id="${item.id}"]`);
			console.log(itemElement, 'itemElementitemElement');
			if (itemElement) {
				itemElement.scrollIntoView({ behavior: 'smooth' });
			}
		},

		// 判断复选框是否禁用
		isCheckboxDisabled(item) {
			if (!this.lastDealResult) return false;
			return this.lastDealResult !== item.lastDealResult;
		},
		// 处理选中变化
		activeItem(item) {
			item.isChecked = !item.isChecked;
			if (item.isChecked && !this.lastDealResult) {
				this.lastDealResult = item.lastDealResult;
			}
		},
		// 获取校对质量类型列表
		getProofreadQualityTypeList() {
			getProofreadQualityType()
				.then(res => {
					if (res.code === 200) {
						// 将对象转换为数组
						this.errorListDown = Object.entries(res.result).map(([id, content]) => ({
							id,
							content
						}));
						// 确保 'all' 选项始终在列表顶部
						this.errorListDown = Object.entries(res.result)
							.map(([id, content]) => ({ id, content }))
							.sort((a, b) => (a.id === 'all' ? -1 : b.id === 'all' ? 1 : 0));
					} else {
						this.$message.error(res.message || '获取纠错类型失败');
					}
				})
				.catch(error => {
					this.$message.error('请求出错');
					console.error('获取纠错类型接口异常:', error);
				});
		},

		// 处理数据，生成错误列表和标签
		setData(data) {
			let { proofreadResultList, id, content } = data;
			this.id = id;
			this.content = content;
			const result = proofreadResultList.reduce((acc, item) => {
				// 查找是否已有相同的 errorType
				const existing = acc.find(i => i.label === item.errorType);
				if (existing) {
					existing.count += 1; // 存在则 count +1
				} else {
					acc.push({
						label: item.errorType,
						count: 1,
						key: acc.length + 1 // 使用当前数组长度作为 key（即下标）
					});
				}
				return acc;
			}, []);
			// 赋值给 errorTabs
			result.unshift({ label: '全部问题', count: proofreadResultList.length, key: 0 });
			this.errorTabs = result;
			// 移除引号并处理错误列表
			const removeQuotes = str => str.replace(/^“|”$/g, ''); // 去除开头和结尾的“”
			this.errorList = proofreadResultList.map((item, index) => {
				// 查找 errorTabs 中 label === errorType 的项
				// 提取一个函数来处理正则表达式匹配和替换
				const processContentText = (content, item) => {
					let originalContentText = removeQuotes(item.errorWord);
					let correctedContentText = removeQuotes(item.rightWord);
					// let originalContentText = removeQuotes(text);
					const regex = new RegExp(originalContentText, 'g');
					// 提取一个函数来处理文本替换
					const replaceQuotes = text => {
						if (text.includes('‘')) {
							return text.replace(/‘/g, '“');
						}
						return text;
					};
					const nweReplaceQuotes = text => {
						if (text.includes('’')) {
							return text.replace(/’/g, '”');
						}
						return text;
					};
					if (!content.match(regex)) {
						if (originalContentText.includes('‘')) {
							originalContentText = nweReplaceQuotes(replaceQuotes(originalContentText));
							correctedContentText = nweReplaceQuotes(replaceQuotes(correctedContentText));
							console.log('processedTextprocessedText', originalContentText);
						}
					}
					return {
						originalContentText,
						correctedContentText
					};
				};
				const matchedTab = this.errorTabs.find(tab => tab.label === item.errorType);
				const errorTypeKey = matchedTab ? matchedTab.key : -1; // 默认 -1 表示未找到
				const contentText = processContentText(this.content, item);
				// const originalContentText = processContentText(this.content, item);
				// const correctedContentText = processContentText(this.content, item);
				return {
					...item,
					id: index + 1,
					originalContent: contentText.originalContentText,
					correctedContent: contentText.correctedContentText,
					errorType: item.errorType,
					lastDealResult: 'untreated',
					errorTypeKey: errorTypeKey,
					originalContentText: contentText.originalContentText,
					correctedContentText: contentText.correctedContentText,
					errorTypeStyle:
						item.errorType === '常识错误'
							? 'common'
							: item.errorType === '用词不当'
							? 'spelling'
							: 'semantic',
					isChecked: false
				};
			});
			console.log(this.errorList, 'errorrrr');
			// 文档标记
			this.setErrorText();
		},

		// 设置错误文本
		setErrorText() {
			this.$emit('setErrorText', this.errorList);
		},

		// 下拉菜单点击事件
		dropdownClick(item) {
			this.dropdownItem = item;
		},

		// 处理标签点击
		handleTabClick(key) {
			// this.setErrorText();
			this.activeTab = key;
		},

		/**点击编辑*/
		edit(item) {
			this.editInput = item.id;
			this.updateValue = item.correctedContent;
		},

		// 更新
		updateText(id) {
			const item = this.errorList.find(item => item.id === id);
			if (item && this.updateValue.trim() !== '') {
				item.correctedContent = this.updateValue.trim();
				this.editInput = ''; // 关闭输入框
				this.updateValue = ''; // 清空输入值
			} else {
				this.$message.warning('修改内容不能为空');
			}
		},

		// 保存输入
		saveInput(row) {},

		handleCommandItem(command, row) {
			const checkedItems = this.errorList.filter(item => item.id === row.id);

			// 处理 dropdown 命令
			switch (command) {
				case 'ignore':
					// 忽略操作
					// 采纳并替换原文的逻辑
					// 设置 lastDealResult 为 'replaced'
					checkedItems.forEach(item => {
						item.lastDealResult = 'neglected';
					});
					this.updateDealResult();
					this.$emit('ignoreSelected', checkedItems);
					// 忽略选中的项的逻辑
					this.errorList.forEach(item => {
						if (item.isChecked) {
							item.isChecked = false;
						}
					});
					this.lastDealResult = '';
					break;
				case 'replace':
					// 替换到原文
					// 设置 lastDealResult 为 'replaced'
					checkedItems.forEach(item => {
						// 替换
						item.originalContentText = item.originalContent;
						item.correctedContentText = item.correctedContent;
						item.lastDealResult = 'replaced';
					});
					// 忽略选中的项的逻辑
					this.errorList.forEach(item => {
						if (item.isChecked) {
							item.isChecked = false;
						}
					});
					this.updateDealResult();
					this.$emit('replaceAndAdopt', checkedItems);
					this.lastDealResult = '';
					break;
				case 'copy':
					copyText(row.correctedContentText);
					this.$message.success('复制成功！');
					// 复制修改建议
					break;
				case 'addToLibrary':
					this.$refs.AddMaterial.open(checkedItems, this.id);
					// 添加到素材库
					break;
				default:
					console.warn('未知命令:', command);
			}
		},
		// 处理命令
		handleCommand(command) {
			if (command === 'ignoreAll') {
				this.errorList.forEach(item => {
					item.lastDealResult = 'neglected';
				});
				this.updateDealResult();
				this.$emit('ignoreSelected', this.errorList);
				// 忽略选中的项的逻辑
				this.errorList.forEach(item => {
					if (item.isChecked) {
						item.isChecked = false;
					}
				});
				this.lastDealResult = '';
			} else if (command === 'addToLibrary') {
				const checkedItems = this.errorList.filter(item => item.isChecked);
				this.$refs.AddMaterial.open(checkedItems, this.id);
				// 添加到素材库的逻辑
				console.log('添加到素材库');
			}
		},

		// 采纳并替换
		replaceAndAdopt(lastDealResult) {
			// 采纳并替换原文的逻辑
			const checkedItems = this.errorList.filter(item => item.isChecked);
			// 设置 lastDealResult 为 'replaced'
			checkedItems.forEach(item => {
				if (lastDealResult === 'replaced') {
					// 还原
					item.originalContentText = item.correctedContent;
					item.correctedContentText = item.originalContent;
				} else {
					// 替换
					item.originalContentText = item.originalContent;
					item.correctedContentText = item.correctedContent;
				}
				item.lastDealResult = lastDealResult === 'replaced' ? 'untreated' : 'replaced';
			});
			// 忽略选中的项的逻辑
			this.errorList.forEach(item => {
				if (item.isChecked) {
					item.isChecked = false;
				}
			});
			this.updateDealResult();
			this.$emit('replaceAndAdopt', checkedItems, lastDealResult);
			this.lastDealResult = '';
		},
		toNewText() {
			const qualityTypes = this.dropdownItem.id ? [this.dropdownItem.id] : ['all'];
			this.$emit('toNewText', qualityTypes);
		},
		// 更新处理结果
		async updateDealResult() {
			let obj = {
				id: this.$route.query.id,
				proofreadResult: JSON.stringify(this.errorList)
			};
			let res = await updateDealResult(obj);
			if (res.code == 200) {
				this.$message.success('处理成功');
			} else {
				this.$message.error(res.message);
			}
		},

		// 忽略选中的错误
		ignoreSelected() {
			// 采纳并替换原文的逻辑
			const checkedItems = this.errorList.filter(item => item.isChecked);
			// 设置 lastDealResult 为 'replaced'
			checkedItems.forEach(item => {
				item.lastDealResult = 'neglected';
			});
			this.updateDealResult();
			this.$emit('ignoreSelected', checkedItems);
			// 忽略选中的项的逻辑
			this.errorList.forEach(item => {
				if (item.isChecked) {
					item.isChecked = false;
				}
			});
			this.lastDealResult = '';
		}
	}
};
</script>

<style scoped lang="scss">
.source {
	min-width: 350px;
	height: 100%;
	display: flex;
	background: #ffffff;
	flex-shrink: 0;
	margin-left: 16px;
	flex-direction: column;

	.logo-icon {
		height: 32px;
		width: 32px;
		margin-right: 4px;
	}

	&-title {
		height: 56px;
		padding: 0 20px;
		display: flex;
		align-items: center;
		justify-content: space-between;
		font-weight: 600;
		font-size: 20px;
		color: #15224c;
		line-height: 23px;
		border-bottom: 1px solid #e3ebf2;
	}
	.title-left {
		display: flex;
		align-items: center;
	}
	.title-right {
		display: flex;
		align-items: center;
	}

	.content {
		flex: 1;
		overflow: auto;
		padding: 26px 0px;

		&-title {
			font-family: PingFang SC, PingFang SC;
			font-weight: 400;
			font-size: 16px;
			color: #15224c;
			line-height: 19px;
			padding: 0 20px;

			.title-num {
				color: #e34d59;
			}
		}

		.content-tab {
			display: flex;
			padding: 0 20px;
			flex-wrap: wrap;
			white-space: nowrap; /* 防止子元素换行 */
			margin: 16px 0;
			.tab-item {
				padding: 8px 16px;
				margin-right: 8px;
				margin-bottom: 12px;
				border-radius: 4px;
				background-color: #f5f6fa;
				color: #737a94;
				cursor: pointer;
				flex-shrink: 0; /* 防止子元素被压缩 */
				white-space: nowrap; /* 确保文本不换行 */

				&.active {
					background-color: rgba(37, 145, 247, 0.1);
					color: #2591f7;
				}
			}
		}
		&-list {
			height: calc(100% - 146px);
			overflow-y: auto;
		}
		.list {
			padding: 0 20px;
			margin-top: 16px;
			cursor: pointer;
			.error-item {
				display: flex;
				position: relative;
				margin-bottom: 16px;
				.item-box {
					flex: 1;
					padding: 26px 16px 16px;
					background: #ffffff;
					border-radius: 9px 9px 9px 9px;
					border: 1px solid #dfebf6;
					&-content {
						flex: 1;
						border-bottom: 1px solid #dfebf6;
						.original-content,
						.corrected-content {
							display: flex;
							margin-bottom: 8px;
							span {
								min-width: 88px;
								font-family: PingFang SC, PingFang SC;
								font-weight: 400;
								font-size: 16px;
								color: #2f446b;
							}
							.original-content-text {
								font-family: PingFang SC, PingFang SC;
								font-weight: 400;
								font-size: 14px;
								color: #e34d59;
								line-height: 20px;
							}
							.corrected-content-text {
								font-family: PingFang SC, PingFang SC;
								font-weight: 400;
								font-size: 14px;
								color: #2591f7;
								line-height: 20px;
							}
						}
					}
				}
				&.is-checked {
					border-color: #2591f7;
				}
				.checkbox {
					margin-right: 16px;
				}
				.operation {
					margin-top: 10px;
					display: flex;
					align-items: center;
					justify-content: space-between;
					.item-btm {
						display: flex;
						align-items: center;
						font-family: PingFang SC, PingFang SC;
						font-weight: 400;
						font-size: 12px;
						color: #2f446b;
						line-height: 20px;
					}
					.error-type {
						padding: 2px 8px;
						border-radius: 3px 3px 3px 3px;
						font-family: PingFang SC, PingFang SC;
						font-weight: 400;
						font-size: 12px;
						line-height: 20px;
					}
					select {
						padding: 4px 8px;
					}
				}
			}
			::v-deep .el-checkbox__inner {
				border-radius: 50%;
				&::after {
					left: 4px;
				}
			}
		}
	}
}

.footer {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 16px 20px;
	border-top: 1px solid #e3ebf2;

	.el-checkbox {
		margin-right: 16px;
	}

	.el-dropdown-link {
		margin-right: 16px;
		cursor: pointer;
	}

	.el-button {
		margin-left: 16px;
	}
}
.active-item-box {
	border: 1px solid var(--brand-6) !important;
}
.replaced {
	position: absolute;
	width: 49px;
	right: 0;
	border-top-right-radius: 9px;
	border-bottom-left-radius: 9px;
	height: 20px;
	background: radial-gradient(10% 10px at center left, transparent 100%, #ff850b 0) center left;
	//background: radial-gradient(50% 10px at center bottom, transparent 100%, #ff850b 0) center bottom;
	display: flex;
	align-items: center;
	justify-content: center;
	font-family: PingFang SC, PingFang SC;
	font-weight: 400;
	font-size: 10px;
	color: #ffffff;
}
.title-dropdown {
	padding: 5px 8px;
	display: flex;
	align-items: center;
	background: #f1f4f6;
	border-radius: 6px 6px 6px 6px;
	cursor: pointer;
	.send-text {
		font-family: PingFang SC, PingFang SC;
		font-weight: 400;
		font-size: 14px;
		color: #2f446b;
	}
}
.anew {
	margin-left: 8px;
	cursor: pointer;
	background: #ffffff;
	border-radius: 6px 6px 6px 6px;
	border: 1px solid #2591f7;
	padding: 4px 15px;
	display: flex;
	align-items: center;
	i {
		font-size: 14px;
		display: flex;
		align-items: center;
		margin-right: 4px;
		color: #2591f7;
		border-radius: 0px 0px 0px 0px;
	}
	span {
		font-family: PingFang SC, PingFang SC;
		font-weight: 400;
		font-size: 14px;
		color: #2591f7;
	}
}
.handle {
	margin-left: 10px;
	&-icon {
		font-size: 18px;
		margin-left: 4px;
		color: $subTextColor;
	}
}
.edit-icon-class {
	font-size: 14px;
	margin-left: 4px;
}

/* 常识错误 */
.common {
	color: #e34d59;
	background-color: rgba(227, 77, 89, 0.1);
}

/* 搭配不当 */
.spelling {
	color: #ffc012;
	background-color: rgba(255, 192, 18, 0.1);
}

/* 疑似错误 */
.semantic {
	color: #ed7b2f;
	background-color: rgba(237, 123, 47, 0.1);
}
</style>
