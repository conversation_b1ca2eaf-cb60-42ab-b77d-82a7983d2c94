<template>
	<div v-show="aiShow" class="ai" :style="{ width: width + 'px' }">
		<div class="ai-title">
			<i
				v-show="config.icon && config.type !== 'domain'"
				class="coos-iconfont icon"
				:class="config.icon"
			></i>
			<span class="text">{{ title }}</span>
		</div>
		<div ref="messageContent" class="ai-content" @scroll="scroll">
			<div
				v-for="(message, messageIndex) of messageList"
				:key="messageIndex"
				class="message-item"
				:class="message.type === 'query' ? 'out' : 'in'"
			>
				<OutMessage v-if="message.type === 'query'" :message="message"></OutMessage>
				<InMessage
					v-else
					:ref="'inmessage-' + messageIndex"
					:chartkey="key"
					:message-index="messageIndex"
					:type-list="typeList"
					:is-last-message="messageIndex === messageList.length - 1"
					:message="message"
					:select-content="config.selectContent"
					v-on="$listeners"
					@customInsertImage="customInsertImage"
					@changeCheckSource="changeCheckSource"
				></InMessage>
			</div>
		</div>
		<div v-for="(item, index) of file" :key="index" class="file">
			<div class="file-left">[上传文档]</div>
			<svg-icon class="file-center" :icon-class="item.cover"></svg-icon>
			<div class="file-right">{{ item.name }}</div>
			<i class="coos-iconfont file-icon icon-shanchu" @click="del(index)"></i>
			<el-progress
				v-if="item.status === 'fail'"
				class="process"
				:percentage="item.process"
				status="exception"
			></el-progress>
			<el-progress
				v-else-if="item.process < 100"
				class="process"
				:percentage="item.process"
				status="success"
			></el-progress>
		</div>
		<div v-if="source.length" class="select-content">
			<div class="select-show">
				<span>已选{{ source.length }}个来源</span>
				<i class="coos-iconfont select-show-icon icon-guanbi1" @click="clearSource"></i>
			</div>
		</div>
		<div class="footer" @click="focusInput">
			<div v-if="config.selectContent" class="footer-select">
				<span class="tip">已选{{ config.selectContent.length }}个字</span>
				<span class="text">{{ config.selectContent }}</span>
				<i class="coos-iconfont close icon-guanbi1" @click.stop="clearContent"></i>
			</div>
			<div
				v-if="
					!config.selectContent ||
					['overwrite', 'extend', 'rewrite', 'proofreadNum'].includes(config.type)
				"
				class="input-area"
			>
				<el-input
					ref="textarea"
					v-model="form.answerQuestion"
					type="textarea"
					:autosize="{ maxRows: 4 }"
					:maxlength="500"
					resize="none"
					:placeholder="
						source && source.length
							? '我将基于已选来源为你作答'
							: config.type
							? `请输入您的${title}方向`
							: '请输入你想问的问题'
					"
					clearable
					@keydown.native="send"
				></el-input>
			</div>
			<div v-else class="input">
				<span class="input-tip">请帮我把所选内容调整为</span>
				<el-autocomplete
					v-model="form.answerQuestion"
					:fetch-suggestions="querySearch"
					placeholder="输入文本"
					@keydown.native="send"
					@select="handleSelect"
				>
					<i slot="suffix" class="coos-iconfont icon-nav-bottom suffix-icon"></i>
					<template slot-scope="{ item }">
						<div class="select-name">{{ item }}</div>
					</template>
				</el-autocomplete>
			</div>
			<div class="buttons">
				<div class="buttons-left">
					<div v-for="(item, index) of buttons" :key="index">
						<uploadFile
							v-if="item.type === 'file'"
							ref="upload"
							:accept="['xlsx', 'doc', 'docx', 'pdf', 'png', 'jpg', 'txt']"
							:value="form.contentDemoFileUrl"
							:disabled="file.length > 0"
							:always-show-upload-button="true"
							:limit="1"
							:multiple="false"
							:custom-file="true"
							@onProcess="onProcess"
							@change="change"
							@click.native.stop
						>
							<div
								slot="custom-button"
								class="item"
								:class="{ 'item-select': form.contentDemoFileUrl }"
							>
								<i class="coos-iconfont item-icon" :class="item.icon"></i>
								<span>{{ item.label }}</span>
							</div>
						</uploadFile>
						<!--						<el-popover-->
						<!--							v-else-if="item.type === 'knowladge'"-->
						<!--						R	placement="top"-->
						<!--							width="340"-->
						<!--							trigger="click"-->
						<!--						>-->
						<!--							<div class="knowledge-base">-->
						<!--								<div class="knowledge-base-title">-->
						<!--									<span>已选择：</span>-->
						<!--									<span class="data">-->
						<!--										<span class="data-select">{{ form.checkList.length }}</span>-->
						<!--										/{{ knowledgeArr.length }}个知识库-->
						<!--									</span>-->
						<!--									<span class="button" @click.stop="clearCheck">清空</span>-->
						<!--								</div>-->
						<!--								<el-checkbox-group v-model="form.checkList" class="knowledge-base-con">-->
						<!--									<el-checkbox-->
						<!--										v-for="knowledge of knowledgeArr"-->
						<!--										:key="knowledge.id"-->
						<!--										:label="knowledge.id"-->
						<!--										class="knowledge-base-item"-->
						<!--									>-->
						<!--										<i class="coos-iconfont icon-cz-gzzd icon"></i>-->
						<!--										<span class="title">-->
						<!--											{{ knowledge.spaceName }}-->
						<!--										</span>-->
						<!--									</el-checkbox>-->
						<!--								</el-checkbox-group>-->
						<!--							</div>-->
						<!--							<div-->
						<!--								slot="reference"-->
						<!--								class="item"-->
						<!--								:class="{ 'item-select': form.checkList.length }"-->
						<!--								@click.stop="selectItem(item)"-->
						<!--							>-->
						<!--								<i class="coos-iconfont item-icon" :class="item.icon"></i>-->
						<!--								<span>{{ item.label }}</span>-->
						<!--							</div>-->
						<!--						</el-popover>-->
						<el-dropdown
							v-else-if="item.type === 'experts'"
							trigger="click"
							:popper-append-to-body="false"
							@command="activeType"
							@click.native.stop
						>
							<div :class="agentObj.id ? 'dropdown-active  item-select' : 'dropdown-active'">
								<i class="coos-iconfont icon-xuanxiang icon dropdown-icon"></i>
								<span>{{ agentObj.agentName }}</span>
							</div>
							<el-dropdown-menu slot="dropdown" class="znws-type-dropdown-bg">
								<el-dropdown-item v-for="type in typeList" :key="type.id" :command="type">
									<div
										:class="
											agentObj.id === type.id
												? 'dropdown-item dropdown-item-active'
												: 'dropdown-item'
										"
									>
										<img class="dropdown-item-img" :src="type.logoUrlPath" alt="" />
										<div class="dropdown-item-text">{{ type.agentName }}</div>
									</div>
								</el-dropdown-item>
							</el-dropdown-menu>
						</el-dropdown>
						<div
							v-else
							class="item"
							:class="{ 'item-select': form[item.type] }"
							@click.stop="selectItem(item)"
						>
							<i class="coos-iconfont item-icon" :class="item.icon"></i>
							<span>{{ item.label }}</span>
						</div>
					</div>
				</div>
				<div v-if="loading" class="submit" @click.stop="pause">
					<i class="coos-iconfont submit-icon icon-zanting"></i>
				</div>
				<div
					v-else
					class="submit"
					:class="{ disabled: !form.answerQuestion.trim() && config.type !== 'proofreadNum' }"
					@click.stop="send($event, true)"
				>
					<i class="coos-iconfont submit-icon icon-fasong1"></i>
				</div>
			</div>
		</div>
		<aiEchart ref="aiEchart" v-on="$listeners" @setEchart="setEchart"></aiEchart>
	</div>
</template>
<script>
import OutMessage from '@/views/draft-writing/components/out-message.vue';
import InMessage from '@/views/draft-writing/components/in-message.vue';
import { Base64 } from 'js-base64';
import { generateFullText } from '@/api/modules/coos-write';
import { getAgents } from '@/api/modules/coos';
import queryMixinsJxj from '@/views/coos/coos-model/coos-znws';
import { mapMutations } from 'vuex';
import AiEchart from '@/views/draft-writing/components/ai-echart.vue';
export default {
	name: 'AiIndex',
	components: {
		AiEchart,
		OutMessage,
		InMessage
	},
	mixins: [queryMixinsJxj],
	props: {
		width: {
			type: Number,
			default: () => {
				return 463;
			}
		},
		aiShow: {
			type: Boolean,
			default: () => {
				return false;
			}
		},
		// 知识库集合
		knowledgeArr: {
			type: Array,
			default: () => {
				return [];
			}
		},
		// 生成全文的id
		id: {
			type: String,
			default: () => {
				return '';
			}
		}
	},
	data() {
		return {
			key: 0,
			tableMap: [], // 表格数据映射
			lockAutoScroll: false,
			agentCacheAnswer: {}, // 智能体答案缓存
			cacheAnswer: '', // 缓存普通答案
			source: [],
			currentAnswerId: '', // 当前回答的答案id
			agentObj: {}, // 智能体
			controller: null, // 请求实例
			form: {
				// checkList: [],
				contentDemoFileUrl: '', // 上传的文件id
				answerFormWeb: false, // 是否联网
				agentId: '', // 智能体id
				answerQuestion: '', //提出的问题
				originDatalist: [] // 已选择来源
			},
			loading: false,
			messageId: 0, // 消息id
			messageList: [], // 消息集合
			file: [],
			outlineData: {}, // 大纲信息
			config: {}, // 配置
			buttons: [
				{
					icon: 'icon-zhuanjia',
					label: '专家',
					type: 'experts'
				},
				{
					icon: 'icon-lianwangsousuo',
					label: '联网搜索',
					type: 'answerFormWeb'
				},
				{
					icon: 'icon-upload',
					label: '文档',
					type: 'file'
				}

				// {
				// 	icon: 'icon-zhishiku1',
				// 	label: '知识库',
				// 	type: 'knowladge'
				// }
			],
			restaurants: ['正式一点', '通俗一点', '幽默一点', '简单一点'],
			typeList: [] // 智能体集合
		};
	},
	computed: {
		title() {
			return this.config.type === 'rewrite'
				? '改写'
				: this.config.type === 'extend'
				? '续写'
				: this.config.type === 'polish'
				? '润色'
				: this.config.type === 'overwrite'
				? '重写'
				: this.config.type === 'proofreadNum'
				? '校正'
				: '智能改写';
		}
	},
	watch: {
		aiShow(newVal) {
			if (!newVal) {
				this.reset();
			}
		}
	},
	mounted() {
		this.getAgents();
		this.addTemporaryMessage('', true);
		this.messageList[0].answer = '你好，请问有什么可以帮你？';
		this.messageList[0].done = true;
		this.messageList[0].showUtil = false;
	},
	methods: {
		...mapMutations('user', ['REMOVE_INFO']),
		updateRender() {
			this.key += 1;
		},
		focusInput() {
			this.$refs.textarea && this.$refs.textarea.focus();
		},
		/**
		 * 设置或更新图表相关的答案数据
		 * @param {number} messageIndex - 消息在 messageList 中的索引位置
		 * @param {number} answerIndex - 答案在当前消息的 answer 数组中的索引位置
		 * @param {Object} answerItem - 当前需要渲染的图表答案对象
		 * @param {string|number} computedId - 可选参数，用于标识智能体 ID 或其他唯一标识符
		 * @param {Array|Object} answerItems - 需要设置的答案数据，可能是数组或对象形式的数据
		 */
		setEchart(messageIndex, answerIndex, answerItem, computedId, answerItems) {
			if (computedId) {
				const itemToSet = Array.isArray(answerItems) ? answerItems[0] : answerItems;
				console.log(itemToSet, 'itemToSetitemToSetitemToSet');
				this.$set(this.messageList[messageIndex], 'agentAnswer', {
					...this.messageList[messageIndex].agentAnswer,
					[computedId]: {
						...this.messageList[messageIndex].agentAnswer?.[computedId],
						answer: (this.messageList[messageIndex].agentAnswer?.[computedId]?.answer || []).map(
							(ans, ansIdx) =>
								ansIdx === answerIndex
									? {
											...ans,
											answer: Array.isArray(itemToSet.answer) ? itemToSet : itemToSet
									  }
									: ans
						)
					}
				});
				this.updateRender();
			}
		},
		/**监听滚动*/
		scroll(e) {
			let event = e.srcElement;
			if (event.scrollTop + event.clientHeight >= event.scrollHeight - 10) {
				this.lockAutoScroll = false;
			} else {
				this.lockAutoScroll = true;
			}
		},
		/**图片配置*/
		customInsertImage(messageI, answerI, answerItem, computedId) {
			this.$refs.aiEchart.open(messageI, answerI, answerItem, computedId);
		},
		// 改变来源的选中
		changeCheckSource(source, messageIndex) {
			let newSource = this.source.filter(item => item.messageIndex !== messageIndex);
			this.source = [...newSource, ...source];
			if (this.form.contentDemoFileUrl) {
				this.del(0);
			}
			this.form.answerFormWeb = false;
			this.agentObj = {};
			this.form.agentId = '';
		},
		/**获取智能体*/
		getAgents() {
			getAgents({
				pageNo: 1,
				pageSize: 10
			}).then(res => {
				if (res.code === 200) {
					let data = res.result.records || [];
					this.typeList = data.map(_ => {
						return {
							..._,
							name: _.agentName
						};
					});
				} else {
					this.$message.error(res.message);
				}
			});
		},
		/**选中智能体*/
		activeType(item) {
			this.agentObj = this.agentObj.id === item.id ? {} : item;
			this.form.agentId = this.form.agentId === item.id ? {} : item.id;
			if (this.form.contentDemoFileUrl) {
				this.del(0);
			}
			this.clearSource();
			this.form.answerFormWeb = false;
		},
		/**清空文章来源*/
		clearSource() {
			this.messageList.forEach((item, index) => {
				// 清除其他选中的来源
				if (item.type === 'answer') {
					this.$refs['inmessage-' + index][0].clearCheck();
				}
			});
			this.source = [];
			this.originDatalist = [];
		},
		/**清空文本内容*/
		clearContent() {
			this.config = {};
			this.outlineData = {};
		},
		/**发送*/
		send(e, click) {
			// 单独按 Enter - 发送消息
			if (click || (e.key === 'Enter' && !e.ctrlKey && !e.shiftKey)) {
				e.preventDefault(); // 阻止默认的换行行为
				if (
					this.loading ||
					(!this.form.answerQuestion.trim() && this.config.type !== 'proofreadNum')
				)
					return;
				// 如果有来源
				if (this.source) {
					this.form.originDatalist = this.source.map(item => {
						let text = '';
						item.agentAnswer.forEach(answer => {
							text += JSON.stringify(answer.answer);
						});
						return text;
					});
				}
				this.queryAi(this.form.answerQuestion);
			}
		},
		clearCheck() {
			this.form.checkList = [];
		},
		/**供外部调用-主要是清空问答参数-发起问答*/
		queryAi(query) {
			this.form.answerQuestion = query;
			let { answerId } = this.addTemporaryMessage(query);
			this.currentAnswerId = answerId;
			this.lockAutoScroll = false;
			this.toBottom();
			this.rewriteAiDoc(answerId);
		},
		/**生成片段*/
		async rewriteAiDoc(answerId) {
			this.loading = true;
			let params = {
				id: this.id,
				rewriteType:
					!this.config.type || this.config.type === 'domain' ? 'aiQuestion' : this.config.type,
				...this.form
			};
			// 改写等操作才传参
			if (this.config.selectContent) {
				params.rewriteText = this.config.selectContent;
			}
			// 匹配到了大纲内容才传参
			if (this.outlineData.businessId) {
				params.outlineInfo = {
					id: this.outlineData.businessId
					// title: this.outlineData.title,
					// level: this.outlineData.level,
					// desc: this.form.answerQuestion,
					// descUrl: this.form.contentDemoFileUrl,
					// descMetadataIds: this.form.checkList
					// descExtra: data.descExtra,
				};
			}
			this.form.answerQuestion = '';
			this.controller = new AbortController();
			const { signal } = this.controller;
			let obj = this.messageList.find(item => item.id === answerId);
			let aiRes;
			try {
				aiRes = await generateFullText(params, signal);
				// 暂停时候特殊处理
				if (!aiRes) return;
			} catch (err) {
				obj.answer = [
					{
						answerType: 'normal',
						answer: '回答异常，请重新尝试！'
					}
				];
				obj.done = true;
				this.finish();
				this.$message.error(err.message || err);
				return;
			}
			// // 获取AI答案
			const reader = aiRes.body.getReader(); // 流式读取器
			const decoder = new TextDecoder('utf-8');
			let readDone = false; // 读取完毕
			// 流式输出
			while (!readDone) {
				let { value, done } = await reader.read();
				readDone = done;
				let res;
				try {
					res = decoder.decode(value);
					if (/"code":50/.test(res)) {
						let r = JSON.parse(res.replace('data:', ''));
						res = [r.message || '服务器异常', 'noAnswer'];
					} else if (/"code":401/.test(res)) {
						res = ['登录失效，请重新登录！', 'noAnswer'];
						this.REMOVE_INFO();
						this.$message.error('登录失效，请重新登录！');
						setTimeout(() => {
							this.$router.push('/login');
						}, 1500);
					} else {
						// res = res.replace(/data:/gi, ''); // 解析成JSON，去掉data:开头的字符串
						res = res.split('data:');
						res = res.map(item => {
							return item.indexOf('noAnswer') > -1 ? 'noAnswer' : Base64.decode(item);
						});
						if (readDone && !res.includes('noAnswer')) {
							res.push('noAnswer');
						}
					}
					// 累加数据
					res.forEach(value => {
						// [1:web]联网来源、[1:file]临时文件来源、[1:text]文本来源、[1:agent]智能体来源
						// [2:content]撰写文本可替换到文档中[2:answer]仅为问题的回答，不可插入文档中
						// 匹配来源
						if (/\[\d:.+]/.test(value)) {
							obj.source = value.replace(/\[\d:/, '').replace(']', '');
						}
						// 多个智能体的话，分开存储
						else if (/\[agent:.+]/.test(value)) {
							let agentId = value.replace(/\[agent:/, '').split(']')[0];
							// 初始化智能体答案对象
							if (!obj.agentAnswer[agentId]) {
								this.$set(obj.agentAnswer, agentId, { answer: [], answerDone: false });
								this.agentCacheAnswer[agentId] = '';
							}
							// 提取文本
							let text = value.replace(/\[agent:[^\]]+]/, '');
							if (text === 'DONE') {
								obj.agentAnswer[agentId].answerDone = true;
							} else {
								this.agentCacheAnswer[agentId] += text;
							}
							// 统一处理
							obj.agentAnswer[agentId].answer = this.handleMultiAnswer(
								this.agentCacheAnswer[agentId],
								obj.id,
								{
									echartsHeight: 150
								}
							);
						} else if (value === '[next]') {
							// 统一处理一次，智能体答案都输出完成
							Object.keys(obj.agentAnswer).forEach(key => {
								obj.agentAnswer[key].answerDone = true;
							});
							this.agentCacheAnswer = {}; // 重置缓冲区
							this.cacheAnswer = ''; // 重置缓冲区
							obj.done = true;
							let { answerId: newAnswerId } = this.addTemporaryMessage('', true);
							obj = this.messageList.find(item => item.id === newAnswerId);
							obj.source = 'answer';
						}
						// 统一存储
						else {
							this.cacheAnswer += value === 'noAnswer' ? '' : value;
							// 统一处理
							obj.answer = this.handleMultiAnswer(this.cacheAnswer, obj.id, {
								echartsHeight: 150
							});
						}
					});
					this.toBottom();
				} catch (err) {
					console.log('流式解析报错--------------', err);
				}
			}
			// 如果回答完成之后都没有答案
			if (Object.keys(obj.agentAnswer).length === 0 && obj.answer.length === 0) {
				obj.answer = [
					{
						answerType: 'normal',
						answer: '抱歉，未找到你想要的答案！'
					}
				];
			}
			// 业务特殊定制数据处理
			if (Object.keys(obj.agentAnswer).length > 0) {
				// 处理智能体答案
				Object.keys(obj.agentAnswer).forEach(agentId => {
					obj.agentAnswer[agentId].answer = obj.agentAnswer[agentId].answer.map(item => {
						if (item.answerType === 'normal') {
							return { ...item, answer: this.addTableInsertDiv(item.answer) };
						} else {
							return item;
						}
					});
				});
			} else {
				// 处理普通答案
				obj.answer = obj.answer.map(item => {
					if (item.answerType === 'normal') {
						return { ...item, answer: this.addTableInsertDiv(item.answer) };
					} else {
						return item;
					}
				});
			}
			obj.done = true;
			// 为自己自定义标签添加事件监听
			this.$nextTick(() => {
				this.addEventLister();
			});
			this.finish();
		},
		/**完成统一逻辑*/
		finish() {
			this.agentCacheAnswer = {}; // 重置缓冲区
			this.cacheAnswer = ''; // 重置缓冲区
			this.loading = false;
		},
		/**插入表格的自定义标签绑定事件*/
		addEventLister() {
			const buttons = document.querySelectorAll('.write-doc-insert-table');
			buttons.forEach(button => {
				button.removeEventListener('click', this.handleInsertTable);
				button.addEventListener('click', this.handleInsertTable);
			});
		},
		/**插入表格的处理函数*/
		handleInsertTable(e) {
			this.$emit('replace', 'insert', [
				{
					type: 'md',
					content: e.target.dataset.id ? this.tableMap[e.target.dataset.id] : ''
				}
			]);
		},
		/**单独处理md文档中的table代码*/
		addTableInsertDiv(mdText) {
			// 匹配 Markdown 表格（包括多行情况）
			const tableRegex = /^(\|.*\|.*)(?:\n\|\s*:?-+:?\s*\|.*)*(?:\n\|.*\|.*)*$/gm;

			// 替换函数
			return mdText.replace(tableRegex, match => {
				// 记录当前表格内容和位置
				const tableIndex = this.tableMap.length;
				this.tableMap[tableIndex] = match;
				// 创建插入按钮（使用模板字符串保持格式）
				const insertDiv = `\n\n<div style="display: flex; justify-content: flex-end; margin-top: 8px;">
														<div class="write-doc-insert-table"
																 data-id="${tableIndex}">
															插入表格
														</div>
													</div>`;
				return match + insertDiv;
			});
		},
		/**滚动到最底部*/
		toBottom() {
			if (this.lockAutoScroll) return;
			this.$nextTick(() => {
				let messageListElement = this.$refs.messageContent;
				if (messageListElement) {
					messageListElement.scroll({
						top: messageListElement.scrollHeight,
						left: 0,
						behavior: 'auto'
					});
				}
			});
		},
		/**
		 * @method 添加临时消息
		 * @param {String} message 追加消息
		 * @param {Boolean} addAnswer 是否追加答案
		 * */
		addTemporaryMessage(message, addAnswer = true) {
			let answerId = 'answer-' + this.messageId;
			let queryId = 'query-' + this.messageId;
			// 添加自己发送的消息
			if (message) {
				this.messageList.push({
					type: 'query',
					id: queryId,
					query: message,
					createTime: new Date().toLocaleString()
				});
			}
			// 添加答案占位消息
			if (addAnswer) {
				this.messageList.push({
					type: 'answer',
					query: message, // 问题
					id: answerId, // 问答ID
					answer: [], // ai答案
					agentAnswer: {}, // 多个智能体答案
					done: false, // 是否回答完毕
					showUtil: true, // 是否显示工具栏
					isPause: false, // 单条数据是否被暂停
					source: '' // 来源
				});
			}
			this.messageId++;
			this.toBottom();
			return { queryId, answerId };
		},
		selectItem(item) {
			this.form[item.type] = !this.form[item.type];
			if (item.type === 'answerFormWeb' && this.form.answerFormWeb) {
				this.agentObj = {};
				this.form.agentId = '';
				this.clearSource();
				if (this.form.contentDemoFileUrl) {
					this.del(0);
				}
			}
		},
		/**上传文件重新赋值*/
		change(ids) {
			this.form.contentDemoFileUrl = ids;
		},
		/**删除文件*/
		del(index) {
			this.$refs.upload[0].del(index);
			this.form.contentDemoFileUrl = '';
			this.file = [];
		},
		/**
		 * @Description 上传监听进度的数据
		 * @Param {Array} showPreviewArr 上传的文件数据
		 * */
		onProcess(showPreviewArr) {
			if (showPreviewArr.length) {
				this.agentObj = {};
				this.form.agentId = '';
				this.clearSource();
				this.form.answerFormWeb = false;
			}
			this.file = showPreviewArr.map(item => {
				return {
					...item,
					process:
						item.process === 'success'
							? 100
							: typeof item.process === 'number'
							? item.process
							: Number(item.process.replace('%', ''))
				};
			});
		},
		/**重置*/
		reset() {
			this.config = {}; // 清除配置信息
			this.outlineData = {}; // 清除大纲
			this.form.checkList = []; // 清除知识库选择
			this.file = []; // 清除上传文件
			if (this.form.contentDemoFileUrl) {
				this.del(0);
			}
			this.form.answerFormWeb = false; // 清除是否联网
			this.form.agentId = ''; // 清除智能体选中
			this.clearSource(); // 清除来源的选中
			this.form.answerQuestion = ''; // 清除问答消息
		},
		/**完全重置*/
		allReset() {
			this.messageList = this.messageList.slice(0, 1);
			this.tableMap = [];
			this.reset();
		},
		/**初始化配置数据*/
		init(obj, config) {
			this.outlineData = obj;
			this.config = config;
		},
		/**输入框自定义补全*/
		handleSelect(item) {
			this.form.answerQuestion = item;
		},
		/**调用 callback 返回建议列表的数据*/
		querySearch(queryString, cb) {
			cb(
				this.restaurants
					? this.restaurants.filter(item => item.indexOf(queryString) > -1)
					: this.restaurants
			);
		},
		/**暂停*/
		pause() {
			try {
				if (this.controller) {
					this.controller.abort();
					let obj = this.messageList.find(item => item.id === this.currentAnswerId);
					if (Object.keys(obj.agentAnswer).length > 0) {
						Object.keys(obj.agentAnswer).forEach(key => {
							obj.agentAnswer[key].answer.push({
								answerType: 'normal',
								isPause: true,
								answer: '（回答中断，请重新生成！）'
							});
							obj.agentAnswer[key].answerDone = true;
						});
					} else {
						obj.answer.push({
							answerType: 'normal',
							answer: '（回答中断，请重新生成！）'
						});
						obj.isPause = true;
						obj.done = true;
					}
					this.agentCacheAnswer = {}; // 重置缓冲区
					this.cacheAnswer = ''; // 重置缓冲区
					this.loading = false;
				}
			} catch (e) {
				console.log(e);
			}
			this.controller = null;
			this.done = true;
		}
	}
};
</script>
<style scoped lang="scss">
.ai {
	min-width: 350px;
	height: 100%;
	border-radius: 16px 0 0 16px;
	display: flex;
	background: #ffffff;
	flex-shrink: 0;
	margin-left: 16px;
	flex-direction: column;
	&-title {
		height: 56px;
		padding: 0 20px;
		border-bottom: 1px solid #e3ebf2;
		color: #15224c;
		display: flex;
		align-items: center;
		.icon {
			margin-right: 4px;
			font-size: 32px;
		}
		.text {
			font-weight: 600;
			font-size: 20px;
			line-height: 23px;
		}
	}
	&-content {
		flex: 1;
		overflow: auto;
		padding: 24px 20px;
	}
}
.footer {
	margin: 0 20px 24px;
	display: flex;
	flex-direction: column;
	height: 168px;
	background: #ffffff;
	border-radius: 12px;
	border: 1px solid #d9e2ec;
	padding: 16px 12px;
	&-select {
		background: #eff5fd;
		border-radius: 3px;
		padding: 2px;
		display: flex;
		align-items: center;
		.tip {
			font-weight: 400;
			font-size: 14px;
			color: #2591f7;
			line-height: 22px;
		}
		.text {
			font-weight: 400;
			font-size: 14px;
			color: #2f446b;
			line-height: 22px;
			margin: 0 10px;
			flex: 1;
			@include aLineEllipse;
		}
		.close {
			font-size: 16px;
			cursor: pointer;
			color: #737a94;
		}
	}
}
.input {
	flex: 1;
	padding: 12px 8px;
	display: flex;
	align-items: center;
	&-tip {
		font-weight: 400;
		font-size: 14px;
		color: #2f446b;
		line-height: 22px;
		margin-right: 8px;
		flex-shrink: 0;
	}
}
.buttons {
	display: flex;
	align-items: center;
	justify-content: space-between;
	&-left {
		display: flex;
		align-items: center;
		.item {
			cursor: pointer;
			border-radius: 6px;
			border: 1px solid #e7edf5;
			padding: 8px 10px;
			font-weight: 400;
			font-size: 14px;
			color: #2f446b;
			line-height: 22px;
			display: flex;
			align-items: center;
			margin-right: 8px;
			&-icon {
				font-size: 16px;
				margin-right: 2px;
			}
		}
		.item-select {
			border-color: #2591f7;
			color: #2591f7;
		}
	}
	.submit {
		cursor: pointer;
		width: 30px;
		height: 30px;
		background: #2591f7;
		border-radius: 18px;
		display: flex;
		align-items: center;
		justify-content: center;
		&-icon {
			font-size: 16px;
			color: #ffffff;
		}
	}
}
.suffix-icon {
	font-size: 16px;
	color: #737a94;
	line-height: 32px;
}
.select-name {
	color: #737a94;
}
.file {
	padding: 4px 8px;
	display: flex;
	align-items: center;
	position: relative;
	margin: 12px 20px;
	background: #f5f6fa;
	border-radius: 6px;
	&-left {
		font-weight: 400;
		font-size: 12px;
		color: #2591f7;
		line-height: 22px;
	}
	&-center {
		width: 22px;
		height: 22px;
		margin: 0 8px 0 4px;
	}
	&-right {
		flex: 1;
		@include aLineEllipse;
	}
	&-icon {
		font-size: 16px;
		color: #737a94;
		margin-left: 8px;
		cursor: pointer;
	}
}
.process {
	position: absolute;
	bottom: -12px;
	left: 20px;
	width: 100%;
}
.message-item {
	display: flex;
	margin-bottom: 24px;
}
.out {
	justify-content: flex-end;
}
.knowledge-base {
	&-title {
		width: 100%;
		flex-shrink: 0;
		height: 44px;
		@include flexBox(flex-start);
		font-size: 16px;
		font-weight: 400;
		color: $subTextColor;
		line-height: 22px;
		position: relative;

		.data {
			font-size: 16px;
			font-weight: 400;
			color: $primaryTextColor;

			&-select {
				color: var(--brand-6);
			}
		}

		& > .button {
			font-size: 14px;
			font-weight: 400;
			color: var(--brand-6);
			line-height: 22px;
			position: absolute;
			right: 8px;
			cursor: pointer;
		}
	}

	&-con {
		width: 100%;
		flex: 1;
		overflow-y: auto;
		overflow-x: hidden;

		@include noScrollBar;
	}

	&-item {
		height: 46px;
		width: 100%;
		padding: 0 8px;
		border-bottom: 1px solid #f0f0f0;
		overflow: hidden;
		@include flexBox(flex-start);

		::v-deep .el-checkbox__label {
			flex: 1;
			overflow: hidden;
			@include flexBox(flex-start);
		}

		.icon {
			font-size: 20px;
			line-height: 20px;
			margin-right: 3px;
		}

		.title {
			font-size: 14px;
			font-weight: 800;
			color: $textColor;
			line-height: 14px;
			@include aLineEllipse;
		}
	}
}
.disabled {
	background: $holderTextColor !important;
	cursor: not-allowed !important;
}
.select-content {
	display: flex;
}
.select-show {
	font-weight: 400;
	font-size: 14px;
	color: #ff763c;
	line-height: 22px;
	background: #eff5fd;
	border-radius: 3px;
	padding: 4px;
	margin: 12px 20px;
	&-icon {
		margin-left: 10px;
		font-size: 16px;
	}
}
.input-area {
	flex: 1;
	::v-deep .el-textarea__inner {
		border: none;
		outline: none;
		box-shadow: none;
	}
}
.dropdown-active {
	background: #ffffff;
	padding: 8px 10px;
	border-radius: 6px;
	border: 1px solid #e7edf5;
	display: inline-flex;
	align-items: center;
	margin-right: 8px;
	cursor: pointer;
	.dropdown-icon {
		font-size: 20px;
	}
}
::v-deep .dropdown-item-active {
	border: 1px solid #187bf3;
}
::v-deep .dropdown-item {
	display: flex;
	align-items: center;
	background: rgba(24, 123, 243, 0.05);
	border-radius: 6px;
	padding: 10px;
	&-img {
		width: 20px;
	}
	&-text {
		margin-left: 4px;
		font-family: PingFangSC, PingFang SC;
		font-weight: 400;
		font-size: 14px;
		color: #444444;
		line-height: 20px;
	}
}

::v-deep .el-dropdown-menu__item {
	margin-bottom: 7px;
	padding: 0 7px !important;
	&:hover {
		background-color: transparent;
	}
}
::v-deep .el-dropdown-menu {
	background-color: red !important; /* 使用 !important 提高优先级 */
}
::v-deep .write-doc-insert-table {
	margin-bottom: 24px;
	font-size: 12px;
	color: #ffffff;
	border-radius: 6px;
	padding: 3px 6px;
	background: #187bf3;
	cursor: pointer;
}
</style>
<style lang="scss">
.znws-type-dropdown-bg.el-dropdown-menu {
	background: url('~@/assets/them-coos/coos/znws-type-dropdown-bg.png') no-repeat center center !important;
	background-size: cover !important;
	border-radius: 8px;
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}
</style>
