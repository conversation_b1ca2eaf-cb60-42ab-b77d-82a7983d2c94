<template>
	<div class="in-message">
		<div
			ref="answer"
			class="message-content"
			:class="{ maxHeight: !status }"
			:style="{ paddingBottom: showHandle ? '20px' : loading ? '16px' : '' }"
		>
			<!--:disabled="!isLastMessage"-->
			<el-checkbox-group v-if="agentArr.length" v-model="checkList" @change="changeCheck">
				<el-checkbox v-for="(item, index) of agentArr" :key="index" :label="item">
					<div :class="item.id === computedId ? 'select-check-item' : 'check-item'">
						<img :src="urlHttp(item.logoUrlPath)" class="logoUrlPath" />
						<span class="text" @click.stop.prevent="changeAgent(item.id)">
							{{ item.agentName }}
						</span>
					</div>
				</el-checkbox>
			</el-checkbox-group>
			<div v-for="(answerItem, answerIndex) of renderAnswer" :key="answerIndex">
				<div>
					<!--     普通字符串    -->
					<messageNormal
						v-if="typeof answerItem.answer === 'string'"
						:item="answerItem"
					></messageNormal>
					<!--     图表渲染    -->
					<messageChart
						v-else-if="answerItem.answerType === 'chart'"
						:key="messageIndex + '-' + answerIndex + '-' + chartkey"
						:ref="'messageChart-' + answerIndex"
						:item="answerItem"
						:item-index="answerIndex"
						v-on="$listeners"
					></messageChart>
					<div
						v-if="answerItem.answerType === 'chart'"
						style="display: flex; justify-content: flex-end"
					>
						<div
							v-if="!message.isReplaceEchart"
							class="insert-button"
							style="margin-right: 8px"
							@click="customInsertImage(messageIndex, answerIndex, answerItem)"
						>
							图片配置
						</div>
						<div
							v-if="!message.isReplaceEchart"
							style="margin-right: 8px"
							class="insert-button"
							@click="insertImage('messageChart-' + answerIndex)"
						>
							插入图片
						</div>
						<div
							v-if="message.isReplaceEchart"
							class="insert-button"
							@click="replaceEchart(answerItem)"
						>
							应用
						</div>
					</div>
				</div>
			</div>
			<i v-if="loading" class="el-icon-loading loading-icon"></i>
			<div v-show="!status" class="mask"></div>
			<div v-if="showHandle" class="con-handle" @click="changeStatus">
				{{ status ? '收起' : '展开' }}
			</div>
		</div>
		<div v-if="message.showUtil" class="utils">
			<!-- :class="{ disabled: util.type !== 'copy' && (!selectContent || loading) }" -->
			<div
				v-for="(util, utilIndex) of customUtils.length === 0 ? utils : customUtils"
				:key="utilIndex"
				class="utils-item"
				:class="{ disabled: loading }"
				:title="loading ? '回答中，请稍后' : ''"
				@click="handleUtil(util.type)"
			>
				<i class="coos-iconfont utils-item-icon" :class="util.icon"></i>
				<span>{{ util.label }}</span>
			</div>
			<div
				v-if="customUtils.length === 0 && agentArr.length === 0"
				class="utils-item"
				:class="{ disabled: loading, select: checkStatus }"
				:title="loading ? '回答中，请稍后' : ''"
				@click="handleUtil('check')"
			>
				<i class="coos-iconfont utils-item-icon icon-dingwei1"></i>
				<span>选中</span>
			</div>
		</div>
		<div></div>
	</div>
</template>
<script>
import { copyText } from '@/wile-fire/ui/util/clipboard';
import messageChart from '@/views/coos/message/item/message-chart.vue';
import messageNormal from '@/views/coos/message/item/message-normal.vue';
import { urlHttp } from '@/utils';

export default {
	name: 'InMessage',
	components: {
		messageNormal,
		messageChart
	},
	props: {
		selectContent: {
			type: String,
			default: () => {
				return '';
			}
		},
		message: {
			type: Object,
			default: () => {
				return {};
			}
		},
		typeList: {
			type: Array,
			default: () => {
				return [];
			}
		},
		isLastMessage: {
			type: Boolean,
			default: () => {
				return false;
			}
		},
		messageIndex: {
			type: Number,
			default: () => {
				return 0;
			}
		},
		// 拖动布局重新渲染chart
		chartkey: {
			type: Number,
			default: () => {
				return 0;
			}
		},
		// 自定义工具类
		customUtils: {
			type: Array,
			default: () => {
				return [];
			}
		}
	},
	data() {
		return {
			reg: new RegExp(
				'<div\\s+style\\s*=\\s*["\']display:\\s*flex[^>]*>[\\s\\S]*?' +
					'<div\\s+class\\s*=\\s*["\']write-doc-insert-table["\'][\\s\\S]*?' +
					'data-id\\s*=\\s*["\']\\d+["\'][\\s\\S]*?>[\\s\\S]*?' +
					'插入表格[\\s\\S]*?<\\/div>[\\s\\S]*?<\\/div>',
				'g'
			),
			checkStatus: false,
			showHandle: false, // 是否显示展开收起按钮
			status: true, // 展开收起按钮的状态
			currentAnswerId: '', // 当前渲染答案的智能体id
			checkList: [], // 选择的智能体来源
			utils: [
				{
					icon: 'icon-fuzhi1',
					label: '复制',
					type: 'copy'
				},
				{
					icon: 'icon-charugengxin_1-copy',
					label: '插入',
					type: 'insert'
				},
				{
					icon: 'icon-tihuan',
					label: '替换',
					type: 'replace'
				}
				// {
				// 	icon: 'icon-gaixie',
				// 	label: '改写',
				// 	type: 'rewrite'
				// }
			]
		};
	},
	computed: {
		// 是否加载完成
		loading() {
			return this.message.source === 'agent' && this.computedId
				? !this.message.agentAnswer[this.computedId].answerDone
				: !this.message.done;
		},
		// 是否暂停
		isPause() {
			return this.message.source === 'agent' && this.computedId
				? !this.message.agentAnswer[this.computedId].isPause
				: !this.message.isPause;
		},
		// 当前渲染答案的智能体id
		computedId() {
			return this.currentAnswerId || Object.keys(this.message.agentAnswer)[0];
		},
		// 当前渲染答案的智能体id
		renderAnswer() {
			let arr = [];
			arr =
				this.message.source === 'agent' && this.computedId
					? this.message.agentAnswer[this.computedId].answer
					: this.message.answerMulti && this.message.answerMulti.length
					? this.message.answerMulti
					: Array.isArray(this.message.answer)
					? this.message.answer
					: [this.message];
			return arr;
		},
		// 渲染的智能体
		agentArr() {
			return this.typeList.filter(item => {
				return Object.keys(this.message.agentAnswer).includes(item.id);
			});
		}
	},
	watch: {
		// 根据答案判断收齐展开按钮
		renderAnswer: {
			deep: true,
			handler(newVal, oldVal) {
				this.$nextTick(() => {
					if (this.$refs.answer) {
						const height = this.$refs.answer.clientHeight;
						if (height > 200) {
							this.showHandle = true;
						}
					}
				});
			}
		},
		// 监听回答完毕
		'message.done'() {
			// 中途没有暂停
			if (!this.message.isPause) {
				// 不管多少张图表，找到第一张
				let obj = this.message.answer.find(item => item.answerType === 'chart');
				this.replaceEchart(obj);
			}
		}
	},
	methods: {
		urlHttp,
		/**图片配置*/
		customInsertImage(messageI, answerI, answerItem) {
			this.$emit('customInsertImage', messageI, answerI, answerItem, this.computedId);
		},
		replaceEchart(answerItem) {
			this.$emit('replaceEchart', answerItem);
		},
		/**图片插入*/
		insertImage(ref) {
			let html = [
				{
					type: 'html',
					content: `<img src='${this.$refs[ref][0].getImage()}'/></br>`
				}
			];
			this.$emit('replace', 'insert', html);
		},
		/**展开收起的按钮*/
		changeStatus() {
			this.status = !this.status;
		},
		clearCheck() {
			this.checkList = [];
			this.checkStatus = false;
		},
		check() {
			this.checkStatus = !this.checkStatus;
			this.$emit(
				'changeCheckSource',
				this.checkStatus
					? [{ ...this.message, messageIndex: this.messageIndex, agentAnswer: this.message.answer }]
					: [],
				this.messageIndex
			);
		},
		/**选择智能体来源*/
		changeCheck() {
			let source = this.checkList.map(item => {
				return {
					messageIndex: this.messageIndex, // 标识选中的来源属于哪个答案内容的
					// 有来源一定是智能体，将智能体的答案拼接到来源中
					agentAnswer: this.message.agentAnswer[item.id].answer,
					...item
				};
			});
			this.$emit('changeCheckSource', source, this.messageIndex);
		},
		/**切换智能体答案id*/
		changeAgent(id) {
			this.currentAnswerId = id;
		},
		/**操作菜单*/
		handleUtil(type) {
			if (this.loading) return;
			let text = '';
			switch (type) {
				case 'copy':
					this.renderAnswer.forEach((item, index) => {
						if (item.answerType !== 'chart') {
							text += item.answer.replace(this.reg, '');
						}
					});
					copyText(text);
					this.$message.success('复制成功！');
					break;
				case 'insert':
					this.$emit('replace', 'insert', this.getAnswerText());
					break;
				case 'replace':
					this.$emit('replace', 'replace', this.getAnswerText());
					break;
				case 'check':
					this.check();
					break;
				default:
					// 自定义的工具栏
					this.$emit('handleUtils', this.message, type);
			}
		},
		/**将md转换成数组*/
		parseMdToObjects(mdText) {
			const result = [];
			if (!mdText) return result;

			// 正则表达式匹配 ```代码块``` 或 Markdown 表格
			const mdRegex = /(```[\s\S]*?```)|(^\|.*\|\s*$(?:\n^\|.*\|\s*$)*)/gm;
			let lastIndex = 0;
			let match;

			while ((match = mdRegex.exec(mdText)) !== null) {
				// 处理匹配之前的普通文本
				if (match.index > lastIndex) {
					const normalText = mdText.slice(lastIndex, match.index);
					if (normalText.trim().length > 0 || normalText.includes('\n')) {
						result.push({
							type: 'html',
							content: normalText
						});
					}
				}

				// 判断是代码块还是表格
				if (match[1]) {
					// 代码块
					result.push({
						type: 'md',
						content: match[1] // 保留完整的 ```
					});
				} else if (match[2]) {
					// 表格
					result.push({
						type: 'md',
						content: match[2] // 完整的表格内容
					});
				}

				lastIndex = mdRegex.lastIndex;
			}

			// 处理剩余文本
			if (lastIndex < mdText.length) {
				const remainingText = mdText.slice(lastIndex);
				if (remainingText.trim().length > 0 || remainingText.includes('\n')) {
					result.push({
						type: 'html',
						content: remainingText
					});
				}
			}

			// 如果没有匹配到任何 MD 内容，返回整个文本作为 html
			if (result.length === 0) {
				result.push({
					type: 'html',
					content: mdText
				});
			}

			return result;
		},
		/**处理答案内容*/
		getAnswerText() {
			let answer = [];
			this.renderAnswer.forEach((item, index) => {
				// 处理普通消息
				if (item.answerType === 'normal') {
					answer.push(
						...this.parseMdToObjects(item.answer.replace('根据以上参考信息整理内容如下：', ''))
					);
				}
				// 处理图表消息
				else if (item.answerType === 'chart') {
					let src = this.$refs['messageChart-' + index][0].getImage();
					answer.push({
						type: 'html',
						content: `<img src='${src}'/></br>`
					});
				}
			});
			return answer;
		}
	}
};
</script>
<style scoped lang="scss">
.in-message {
	width: 100%;
}
.message-content {
	position: relative;
	padding: 16px 12px 0;
	max-width: 95%;
	background: #f5f6fa;
	border-radius: 12px;
	font-weight: 400;
	font-size: 14px;
	color: #2f446b;
	line-height: 22px;
	margin-bottom: 8px;
}
.utils {
	display: flex;
	align-items: center;
	&-item {
		padding: 0 5px;
		font-weight: 400;
		font-size: 12px;
		color: #2f446b;
		line-height: 22px;
		margin-right: 8px;
		cursor: pointer;
		background: #ffffff;
		border-radius: 6px;
		border: 1px solid #d9e2ec;
		&-icon {
			font-size: 12px;
			margin-right: 3px;
		}
	}
}
.loading-icon {
	margin-left: 4px;
	font-size: 16px;
	color: var(--brand-6);
}
.logoUrlPath {
	width: 20px;
	margin-right: 4px;
}
.el-checkbox {
	display: inline-flex;
	align-items: center;
}
.maxHeight {
	max-height: 200px;
	overflow: hidden;
}
.mask {
	background: linear-gradient(180deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 1) 100%);
	height: 200px;
	position: absolute;
	bottom: 0;
	width: 100%;
	left: 0;
	z-index: 2;
}
.con-handle {
	color: var(--brand-6);
	text-align: right;
	position: absolute;
	bottom: 12px;
	right: 12px;
	z-index: 3;
	cursor: pointer;
}

.check-item {
	display: flex;
	align-items: center;
	padding: 4px 6px;
	border-radius: 6px;
	.text {
		&:hover {
			text-decoration: underline;
		}
	}
}
.select-check-item {
	background: #187bf3;
	color: #ffffff;
	display: flex;
	align-items: center;
	padding: 4px 6px;
	border-radius: 6px;
}
.select {
	background: #187bf3;
	color: #ffffff;
}
.insert-button {
	margin-bottom: 24px;
	font-size: 12px;
	color: #ffffff;
	border-radius: 6px;
	padding: 3px 6px;
	background: #187bf3;
	cursor: pointer;
}

.disabled {
	background: #f5f6fa;
	border: none;
	cursor: not-allowed;
}
.disabled-insert {
	background: var(--brand-2);
	border: none;
	cursor: not-allowed;
}
</style>
