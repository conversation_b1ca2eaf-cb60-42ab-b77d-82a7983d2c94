<template>
	<div class="out-message">
		<div class="message-content">
			{{ message.query }}
		</div>
		<div class="utils">
			<div
				v-for="(util, utilIndex) of utils"
				:key="utilIndex"
				class="utils-item"
				@click="handleUtil(util.type)"
			>
				<i class="coos-iconfont utils-item-icon" :class="util.icon"></i>
				<span>{{ util.label }}</span>
			</div>
		</div>
	</div>
</template>
<script>
import { copyText } from '@/wile-fire/ui/util/clipboard';

export default {
	name: 'OutMessage',
	props: {
		message: {
			type: Object,
			default: () => {
				return {};
			}
		}
	},
	data() {
		return {
			utils: [
				{
					icon: 'icon-fuzhi1',
					label: '复制',
					type: 'copy'
				}
			]
		};
	},
	methods: {
		handleUtil(type) {
			switch (type) {
				case 'copy':
					copyText(this.message.query);
					this.$message.success('复制成功！');
					break;
			}
		}
	}
};
</script>
<style scoped lang="scss">
.out-message {
	width: 100%;
	display: flex;
	flex-direction: column;
	align-items: flex-end;
	.message-content {
		font-weight: 400;
		font-size: 14px;
		color: #ffffff;
		line-height: 22px;
		max-width: 392px;
		padding: 12px;
		background: #2591f7;
		border-radius: 12px;
	}
}
.utils {
	margin-top: 8px;
	display: flex;
	align-items: center;
	&-item {
		padding: 0 5px;
		font-weight: 400;
		font-size: 12px;
		color: #2f446b;
		line-height: 22px;
		margin-right: 8px;
		cursor: pointer;
		background: #ffffff;
		border-radius: 6px;
		border: 1px solid #d9e2ec;
		&-icon {
			font-size: 12px;
			margin-right: 3px;
		}
	}
}
</style>
