<template>
	<div v-show="aiShow" class="ai" :style="{ width: width + 'px' }">
		<div class="ai-title">
			<i class="coos-iconfont icon"></i>
			<span class="text">智能大纲</span>
		</div>
		<div ref="messageContent" class="ai-content" @scroll="scroll">
			<div
				v-for="(message, messageIndex) of messageList"
				:key="messageIndex"
				class="message-item"
				:class="message.type === 'query' ? 'out' : 'in'"
			>
				<OutMessage v-if="message.type === 'query'" :message="message"></OutMessage>
				<InMessage
					v-else
					:ref="'inmessage-' + messageIndex"
					:chartkey="key"
					:custom-utils="customUtils"
					:message-index="messageIndex"
					:is-last-message="messageIndex === messageList.length - 1"
					:message="message"
					v-on="$listeners"
					@handleUtils="handleUtils"
				></InMessage>
			</div>
		</div>
		<div class="footer" @click="focusInput">
			<div class="input-area">
				<el-input
					ref="textarea"
					v-model="answerQuestion"
					type="textarea"
					:autosize="{ maxRows: 4 }"
					:maxlength="500"
					resize="none"
					placeholder="请输入你想问的问题..."
					clearable
					@keydown.native="send"
				></el-input>
			</div>
			<div class="buttons">
				<div class="buttons-left"></div>
				<div v-if="loading" class="submit" @click.stop="pause">
					<i class="coos-iconfont submit-icon icon-zanting"></i>
				</div>
				<div
					v-else
					class="submit"
					:class="{ disabled: !answerQuestion.trim() || !done }"
					:title="!done ? '回答中，请稍后' : ''"
					@click.stop="send($event, true)"
				>
					<i class="coos-iconfont submit-icon icon-fasong1"></i>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import OutMessage from '@/views/draft-writing/components/out-message.vue';
import InMessage from '@/views/draft-writing/components/in-message.vue';
import { Base64 } from 'js-base64';
import queryMixinsJxj from '@/views/coos/coos-model/coos-znws';
import { outlineAi } from '@/api/modules/coos-write';
export default {
	name: 'AiOutlineIndex',
	components: {
		OutMessage,
		InMessage
	},
	mixins: [queryMixinsJxj],
	props: {
		width: {
			type: Number,
			default: () => {
				return 463;
			}
		},
		aiShow: {
			type: Boolean,
			default: () => {
				return false;
			}
		},
		// 生成全文的id
		id: {
			type: String,
			default: () => {
				return '';
			}
		},
		done: {
			type: Boolean,
			default: () => {
				return false;
			}
		}
	},
	data() {
		return {
			key: 0,
			customUtils: [
				{
					icon: 'icon-fuzhi1',
					label: '复制',
					type: 'copy'
				},
				{
					icon: 'icon-fuzhi1',
					label: '应用',
					type: 'apply'
				}
			],
			lockAutoScroll: false,
			cacheAnswer: '', // 缓存普通答案
			jsonIndex: -1, // json开始的索引
			jsonCache: '', // 缓存的json
			currentAnswerId: '', // 当前回答的答案id
			controller: null, // 请求实例
			answerQuestion: '', //提出的问题
			conversationId: '',
			loading: false,
			messageId: 0, // 消息id
			messageList: [] // 消息集合
		};
	},
	watch: {
		aiShow(newVal) {
			if (!newVal) {
				this.reset();
			}
		}
	},
	mounted() {
		this.addTemporaryMessage('', true);
		this.messageList[0].answer = '你好，请问有什么可以帮你？';
		this.messageList[0].done = true;
		this.messageList[0].showUtil = false;
	},
	methods: {
		/**操作菜单*/
		handleUtils(item, type) {
			if (this.loading) return;
			switch (type) {
				case 'apply':
					this.$emit('updateOutline', item.showOutline);
					break;
			}
		},
		updateRender() {
			this.key += 1;
		},
		focusInput() {
			this.$refs.textarea && this.$refs.textarea.focus();
		},
		/**监听滚动*/
		scroll(e) {
			let event = e.srcElement;
			if (event.scrollTop + event.clientHeight >= event.scrollHeight - 10) {
				this.lockAutoScroll = false;
			} else {
				this.lockAutoScroll = true;
			}
		},
		/**发送*/
		send(e, click) {
			// 单独按 Enter - 发送消息
			if (click || (e.key === 'Enter' && !e.ctrlKey && !e.shiftKey)) {
				e.preventDefault(); // 阻止默认的换行行为
				if (this.loading || !this.answerQuestion.trim()) return;
				let { answerId } = this.addTemporaryMessage(this.answerQuestion);
				this.currentAnswerId = answerId;
				this.lockAutoScroll = false;
				this.toBottom();
				this.getQuery(answerId);
			}
		},
		/**问答*/
		async getQuery(answerId) {
			this.loading = true;
			let params = {
				id: this.id,
				answerQuestion: this.answerQuestion,
				conversationId: this.conversationId
			};
			this.answerQuestion = '';
			this.controller = new AbortController();
			const { signal } = this.controller;
			let obj = this.messageList.find(item => item.id === answerId);
			let aiRes;
			try {
				aiRes = await outlineAi(params, signal);
				// 暂停时候特殊处理
				if (!aiRes) return;
			} catch (err) {
				obj.answer = [
					{
						answerType: 'normal',
						answer: '回答异常，请重新尝试！'
					}
				];
				obj.done = true;
				this.finish();
				this.$message.error(err.message || err);
				return;
			}
			// // 获取AI答案
			const reader = aiRes.body.getReader(); // 流式读取器
			const decoder = new TextDecoder('utf-8');
			let readDone = false; // 读取完毕
			// 流式输出
			while (!readDone) {
				let { value, done } = await reader.read();
				readDone = done;
				let res;
				try {
					res = decoder.decode(value);
					if (/"code":50/.test(res)) {
						let r = JSON.parse(res.replace('data:', ''));
						res = [r.message || '服务器异常'];
					} else if (/"code":401/.test(res)) {
						res = ['登录失效，请重新登录！'];
						this.REMOVE_INFO();
						this.$message.error('登录失效，请重新登录！');
						setTimeout(() => {
							this.$router.push('/login');
						}, 1500);
					} else {
						// res = res.replace(/data:/gi, ''); // 解析成JSON，去掉data:开头的字符串
						res = res.split('data:');
						// 这一步的目的是不对noAnswer明文再进行解码
						res = res.map(item => {
							return item.indexOf('noAnswer') > -1 ? 'noAnswer' : Base64.decode(item);
						});
						console.log('2=================', res);
					}
					console.log(res);
					// 累加数据
					res.forEach(value => {
						if (/^conversationId:/.test(value)) {
							this.conversationId = value.replace('conversationId:', '');
						} else if (this.jsonIndex > -1) {
							this.jsonCache += value === 'noAnswer' ? '' : value;
						} else {
							this.cacheAnswer += value === 'noAnswer' ? '' : value;
						}
					});
					// 捕获json开始的时候
					if (this.cacheAnswer.indexOf('```') > -1) {
						this.jsonIndex = this.cacheAnswer.indexOf('```');
						this.jsonCache += this.cacheAnswer.slice(this.jsonIndex);
						// 截取json开始前的数据
						this.cacheAnswer = this.cacheAnswer.slice(0, this.jsonIndex);
					}
					obj.answer = this.handleMultiAnswer(this.cacheAnswer, obj.id, {
						echartsHeight: 150
					});
					this.toBottom();
				} catch (err) {
					console.log('流式解析报错--------------', err);
				}
			}
			// 如果回答完成之后都没有答案
			if (obj.answer.length === 0) {
				obj.answer = [
					{
						answerType: 'normal',
						answer: '抱歉，未找到你想要的答案！'
					}
				];
			}
			// 解析json数据
			if (/^```json/.test(this.jsonCache)) {
				// 提取json代码块内容
				const jsonBlockRegex = /```json([\s\S]*?)```/;
				const match = this.jsonCache.match(jsonBlockRegex);
				if (match) {
					const jsonContent = match[1];
					// 替换\n为实际换行（如果需要）
					const replacedContent = jsonContent.replace(/\\n/g, '\n');
					// 如果需要将字符串解析为JSON对象
					try {
						obj.showOutline = JSON.parse(replacedContent).map(item => {
							return {
								editStatue: false, // 加一个是否编辑状态;
								knowledge: '', // 知识库绑定
								desc: '', // 更多描述
								descFile: '', // 更多描述文件
								extraData: {},
								editDescExtraStatue: false,
								...item
							};
						});
					} catch (e) {
						console.error('JSON解析错误:', e);
					}
				} else {
					console.log('未找到json代码块');
				}
			}
			obj.done = true;
			this.finish();
		},
		/**完成回答*/
		finish() {
			this.jsonIndex = -1;
			this.jsonCache = '';
			this.cacheAnswer = ''; // 重置缓冲区
			this.loading = false;
		},
		/**生成二维数组*/
		generateTwoDimensionalArrays(outlineData) {
			const result = [];
			let currentGroup = [];
			for (const item of outlineData) {
				if (item.level === 1) {
					// 当遇到level为1时，开始新分组
					if (currentGroup.length > 0) {
						result.push(currentGroup);
					}
					currentGroup = [item];
				} else {
					// 否则将元素添加到当前分组
					currentGroup.push(item);
				}
			}
			// 添加最后一个分组
			if (currentGroup.length > 0) {
				result.push(currentGroup);
			}
			return result;
		},
		/**滚动到最底部*/
		toBottom() {
			if (this.lockAutoScroll) return;
			this.$nextTick(() => {
				let messageListElement = this.$refs.messageContent;
				if (messageListElement) {
					messageListElement.scroll({
						top: messageListElement.scrollHeight,
						left: 0,
						behavior: 'auto'
					});
				}
			});
		},
		/**
		 * @method 添加临时消息
		 * @param {String} message 追加消息
		 * @param {Boolean} addAnswer 是否追加答案
		 * */
		addTemporaryMessage(message, addAnswer = true) {
			let answerId = 'answer-' + this.messageId;
			let queryId = 'query-' + this.messageId;
			// 添加自己发送的消息
			if (message) {
				this.messageList.push({
					type: 'query',
					id: queryId,
					query: message,
					createTime: new Date().toLocaleString()
				});
			}
			// 添加答案占位消息
			if (addAnswer) {
				this.messageList.push({
					type: 'answer',
					query: message, // 问题
					id: answerId, // 问答ID
					answer: [], // ai答案
					done: false, // 是否回答完毕
					showUtil: true, // 是否显示工具栏
					isPause: false, // 单条数据是否被暂停
					showOutline: []
				});
			}
			this.messageId++;
			this.toBottom();
			return { queryId, answerId };
		},
		/**重置*/
		reset() {
			this.pause();
			this.messageList = this.messageList.slice(0, 1);
			this.conversationId = '';
			this.answerQuestion = ''; // 清除问答消息
		},
		/**暂停*/
		pause() {
			try {
				if (this.controller) {
					this.controller.abort();
					let obj = this.messageList.find(item => item.id === this.currentAnswerId);
					obj.answer.push({
						answerType: 'normal',
						answer: '（回答中断，请重新生成！）'
					});
					obj.isPause = true;
					obj.done = true;
				}
			} catch (e) {
				console.log(e);
			}
			this.cacheAnswer = ''; // 重置缓冲区
			this.jsonIndex = -1;
			this.jsonCache = '';
			this.loading = false;
			this.controller = null;
		}
	}
};
</script>
<style scoped lang="scss">
.ai {
	min-width: 350px;
	height: 100%;
	border-radius: 16px 0 0 16px;
	display: flex;
	background: #ffffff;
	flex-shrink: 0;
	margin-left: 16px;
	flex-direction: column;
	&-title {
		height: 56px;
		padding: 0 20px;
		border-bottom: 1px solid #e3ebf2;
		color: #15224c;
		display: flex;
		align-items: center;
		.icon {
			margin-right: 4px;
			font-size: 32px;
		}
		.text {
			font-weight: 600;
			font-size: 20px;
			line-height: 23px;
		}
	}
	&-content {
		flex: 1;
		overflow: auto;
		padding: 24px 20px;
	}
}
.footer {
	margin: 0 20px 24px;
	display: flex;
	flex-direction: column;
	height: 168px;
	background: #ffffff;
	border-radius: 12px;
	border: 1px solid #d9e2ec;
	padding: 16px 12px;
	&-select {
		background: #eff5fd;
		border-radius: 3px;
		padding: 2px;
		display: flex;
		align-items: center;
		.tip {
			font-weight: 400;
			font-size: 14px;
			color: #2591f7;
			line-height: 22px;
		}
		.text {
			font-weight: 400;
			font-size: 14px;
			color: #2f446b;
			line-height: 22px;
			margin: 0 10px;
			flex: 1;
			@include aLineEllipse;
		}
		.close {
			font-size: 16px;
			cursor: pointer;
			color: #737a94;
		}
	}
}
.input {
	flex: 1;
	padding: 12px 8px;
	display: flex;
	align-items: center;
	&-tip {
		font-weight: 400;
		font-size: 14px;
		color: #2f446b;
		line-height: 22px;
		margin-right: 8px;
		flex-shrink: 0;
	}
}
.buttons {
	display: flex;
	align-items: center;
	justify-content: space-between;
	&-left {
		display: flex;
		align-items: center;
		.item {
			cursor: pointer;
			border-radius: 6px;
			border: 1px solid #e7edf5;
			padding: 8px 10px;
			font-weight: 400;
			font-size: 14px;
			color: #2f446b;
			line-height: 22px;
			display: flex;
			align-items: center;
			margin-right: 8px;
			&-icon {
				font-size: 16px;
				margin-right: 2px;
			}
		}
		.item-select {
			border-color: #2591f7;
			color: #2591f7;
		}
	}
	.submit {
		cursor: pointer;
		width: 30px;
		height: 30px;
		background: #2591f7;
		border-radius: 18px;
		display: flex;
		align-items: center;
		justify-content: center;
		&-icon {
			font-size: 16px;
			color: #ffffff;
		}
	}
}
.suffix-icon {
	font-size: 16px;
	color: #737a94;
	line-height: 32px;
}
.select-name {
	color: #737a94;
}
.file {
	padding: 4px 8px;
	display: flex;
	align-items: center;
	position: relative;
	margin: 12px 20px;
	background: #f5f6fa;
	border-radius: 6px;
	&-left {
		font-weight: 400;
		font-size: 12px;
		color: #2591f7;
		line-height: 22px;
	}
	&-center {
		width: 22px;
		height: 22px;
		margin: 0 8px 0 4px;
	}
	&-right {
		flex: 1;
		@include aLineEllipse;
	}
	&-icon {
		font-size: 16px;
		color: #737a94;
		margin-left: 8px;
		cursor: pointer;
	}
}
.process {
	position: absolute;
	bottom: 0;
	left: 0;
	width: 100%;
}
.message-item {
	display: flex;
	margin-bottom: 24px;
}
.out {
	justify-content: flex-end;
}
.knowledge-base {
	&-title {
		width: 100%;
		flex-shrink: 0;
		height: 44px;
		@include flexBox(flex-start);
		font-size: 16px;
		font-weight: 400;
		color: $subTextColor;
		line-height: 22px;
		position: relative;

		.data {
			font-size: 16px;
			font-weight: 400;
			color: $primaryTextColor;

			&-select {
				color: var(--brand-6);
			}
		}

		& > .button {
			font-size: 14px;
			font-weight: 400;
			color: var(--brand-6);
			line-height: 22px;
			position: absolute;
			right: 8px;
			cursor: pointer;
		}
	}

	&-con {
		width: 100%;
		flex: 1;
		overflow-y: auto;
		overflow-x: hidden;

		@include noScrollBar;
	}

	&-item {
		height: 46px;
		width: 100%;
		padding: 0 8px;
		border-bottom: 1px solid #f0f0f0;
		overflow: hidden;
		@include flexBox(flex-start);

		::v-deep .el-checkbox__label {
			flex: 1;
			overflow: hidden;
			@include flexBox(flex-start);
		}

		.icon {
			font-size: 20px;
			line-height: 20px;
			margin-right: 3px;
		}

		.title {
			font-size: 14px;
			font-weight: 800;
			color: $textColor;
			line-height: 14px;
			@include aLineEllipse;
		}
	}
}
.disabled {
	background: $holderTextColor !important;
	cursor: not-allowed !important;
}
.select-content {
	display: flex;
}
.select-show {
	font-weight: 400;
	font-size: 14px;
	color: #ff763c;
	line-height: 22px;
	background: #eff5fd;
	border-radius: 3px;
	padding: 4px;
	margin: 12px 20px;
	&-icon {
		margin-left: 10px;
		font-size: 16px;
	}
}
.input-area {
	flex: 1;
	::v-deep .el-textarea__inner {
		border: none;
		outline: none;
		box-shadow: none;
	}
}
.dropdown-active {
	background: #ffffff;
	padding: 8px 10px;
	border-radius: 6px;
	border: 1px solid #e7edf5;
	display: inline-flex;
	align-items: center;
	margin-right: 8px;
	cursor: pointer;
	.dropdown-icon {
		font-size: 20px;
	}
}
::v-deep .dropdown-item-active {
	border: 1px solid #187bf3;
}
::v-deep .dropdown-item {
	display: flex;
	align-items: center;
	background: rgba(24, 123, 243, 0.05);
	border-radius: 6px;
	padding: 10px;
	&-img {
		width: 20px;
	}
	&-text {
		margin-left: 4px;
		font-family: PingFangSC, PingFang SC;
		font-weight: 400;
		font-size: 14px;
		color: #444444;
		line-height: 20px;
	}
}

::v-deep .el-dropdown-menu__item {
	margin-bottom: 7px;
	padding: 0 7px !important;
	&:hover {
		background-color: transparent;
	}
}
::v-deep .el-dropdown-menu {
	background-color: red !important; /* 使用 !important 提高优先级 */
}
::v-deep .write-doc-insert-table {
	margin-bottom: 24px;
	font-size: 12px;
	color: #ffffff;
	border-radius: 6px;
	padding: 3px 6px;
	background: #187bf3;
	cursor: pointer;
}
</style>
<style lang="scss">
.znws-type-dropdown-bg.el-dropdown-menu {
	background: url('~@/assets/them-coos/coos/znws-type-dropdown-bg.png') no-repeat center center !important;
	background-size: cover !important;
	border-radius: 8px;
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}
</style>
