<template>
	<div class="content">
		<div class="content-menu">
			<div class="menuee-title">{{ defaultMenu.title }}</div>
			<el-menu
				class="menu-info"
				background-color="#F3F4F6"
				:default-active="defaultActive"
				@select="selectChange"
			>
				<!-- <el-menu-item index="1">
					<i class="coos-iconfont icon-shijian2 svg-icon"></i>
					<span slot="title">我的待办</span>
				</el-menu-item>
				<el-menu-item index="2">
					<i class="coos-iconfont icon-daibanrenwu svg-icon" style="color: #52c41a"></i>
					<span slot="title">已处理</span>
				</el-menu-item> -->

				<el-menu-item v-for="(item, index) in defaultMenu.menu" :key="index" :index="item.index">
					<i
						class="coos-iconfont svg-icon"
						:class="[item.icon]"
						:style="{ color: item.iconColor }"
					></i>
					<span slot="title">
						{{ item.name }}（{{ todoItemsCount ? todoItemsCount[item.code] : 0 }}）
					</span>
				</el-menu-item>
				<template v-if="showOriginCategory && workList.length">
					<div class="menu-itemTitle">待办来源</div>
					<el-menu-item v-for="item in workList" :key="item.name" :index="item.index">
						<!-- <i class="coos-iconfont icon-shuzhuangtu1 svg-icon" style="color: #faad14"></i> -->
						<el-image :src="item.logoUrlPath" class="svg-icon workSvg"></el-image>
						<span slot="title">{{ item.name }}</span>
					</el-menu-item>
					<!-- <el-menu-item index="4">
					<i class="coos-iconfont icon-xinwen svg-icon" style="color: #51d3a4"></i>
					<span slot="title">日志审核</span>
				</el-menu-item> -->
				</template>
			</el-menu>
		</div>
		<div class="handle-content">
			<!--   待办列表Start   -->
			<waitContent
				ref="waitContent"
				:open-detail-type="openDetailType"
				:work-list="workList"
				:default-menu="defaultMenu.menu"
				:choose-index="chooseIndex"
				:search-id="searchId"
				:application-id="applicationId"
			></waitContent>
			<!--   待办列表End   -->
		</div>
	</div>
</template>

<script>
import waitContent from '@/views/wait-handle/components/index.vue';
import { workHandleList, statsForCondition } from '@/api/modules/wait-handle';
import { assetsUrl } from '@/config';
import { updateCount } from '@/utils/update-help';
import { isEmpty } from '@/utils';
import { mapState } from 'vuex';
import { CoosEventTypes } from '@/utils/bus';
import { getDictionary } from '@/utils/data-dictionary';
export default {
	components: {
		waitContent
	},
	data() {
		return {
			openDetailType: '1', // 待办的打开方式
			chooseIndex: 'menu0',
			defaultActive: 'menu0', //默认选中id
			applicationId: '', //工作台id
			searchId: '', //全局搜索id
			workList: [],
			isSearch: false, // 判断是否是全局搜索
			defaultMenu: {
				title: '待办事项',
				menu: []
			},
			showOriginCategory: true,
			todoItemsCountParams: {},
			todoItemsCount: null // 待办项的数据统计
		};
	},
	computed: {
		...mapState('user', ['waitConfig'])
	},
	watch: {
		$route(newVal) {
			this.getDetail(newVal);
		},
		waitConfig: {
			deep: true,
			handler: function () {
				this.getDetail();
			}
		}
	},
	mounted() {
		this.getDetail();
		this._BUS.$on(CoosEventTypes.updateWaitCount, this.socketUpdateCount);
	},
	destroyed() {
		this._BUS.$off(CoosEventTypes.updateWaitCount, this.socketUpdateCount);
	},
	/**每次打开重新获取待办数量*/
	activated() {
		this._BUS.$emit(CoosEventTypes.changeTempAiEnter, [getDictionary('AI编码/智问待办')]);
		updateCount();
		this.defaultActive = this.chooseIndex;
	},
	deactivated() {
		this._BUS.$emit(CoosEventTypes.changeTempAiEnter, []);
	},
	methods: {
		socketUpdateCount() {
			this.getStatsForCondition();
		},
		/**全局搜索接口跳转 */
		getDetail(newVal = this.$route) {
			if (!this.waitConfig.customerTaskMenu) return; // 框架请求配置的接口还没有返回
			if (newVal.path === '/wait-handle') {
				if (Object.keys(newVal.query).length) {
					const { applicationId, id } = newVal.query;
					this.applicationId = applicationId;
					this.getWorkHandle(true, applicationId, id);
				} else {
					this.searchId = '';
					this.getWorkHandle();
				}
			}
		},
		// 刷新待办数量
		async getStatsForCondition() {
			const { result } = await statsForCondition(this.todoItemsCountParams);
			this.todoItemsCount = result;
		},
		/** 工作台待办选项 */
		async getWorkHandle(val, applicationId, id) {
			this.openDetailType = this.waitConfig.customerTaskMenu.detailOpenType || '1'; // 打开待办的方式
			const jsonMenu = this.waitConfig.customerTaskMenu.menu || '';
			if (jsonMenu) {
				const menu = JSON.parse(jsonMenu);
				const menus = menu.map((v, i) => {
					if (val == undefined) {
						this.defaultActive = 'menu0';
					}
					return {
						...v,
						index: `menu${i}`
					};
				});
				this.defaultMenu.menu = [...menus];
				this.showOriginCategory = this.waitConfig.customerTaskMenu.showOriginCategory !== 'false';
				this.defaultMenu.title = this.waitConfig.customerTaskMenu.title;
				this.todoItemsCountParams = this.defaultMenu.menu.reduce((acc, next) => {
					const condition = next.condition.reduce((acc1, next1) => {
						acc1[next1.key] = next1.value;
						return acc1;
					}, {});

					acc[next.code] = condition;
					return acc;
				}, {});
			} else {
				this.defaultMenu.menu = [
					{
						index: `menu0`,
						name: '我的待办',
						shortName: '待办',
						icon: 'icon-shijian2',
						specialField: '待办来源|上一步节点|创建时间|接收时间',
						code: 'notHandleTask',
						condition: [
							{
								key: 'status',
								value: 0
							}
						]
					},
					{
						index: `menu1`,
						name: '已处理',
						shortName: '已办',
						icon: 'icon-daibanrenwu',
						iconColor: '#52c41a',
						specialField: '当前节点|处理时间|接收时间',
						code: 'handledTask',
						condition: [
							{
								key: 'status',
								value: 1
							}
						]
					}
				];
				this.todoItemsCountParams = this.defaultMenu.menu.reduce((acc, next) => {
					const condition = next.condition.reduce((acc1, next1) => {
						acc1[next1.key] = next1.value;
						return acc1;
					}, {});

					acc[next.code] = condition;
					return acc;
				}, {});
			}
			this.getStatsForCondition();
			// 减少请求次数
			if (this.showOriginCategory && isEmpty(this.workList)) {
				let res = await workHandleList();
				let index = 2;
				this.workList = res.result.map(item => {
					index++;
					return {
						...item,
						index: index.toString(),
						url: `${assetsUrl}/workspace/client/workPlatformTaskAppsIcon/${item.applicationId}.png`
					};
				});
			}
			//是否跳转处理
			if (val) {
				this.defaultActive = this.workList.find(item => {
					return item.applicationId === applicationId;
				}).index;
				this.isSearch = true;
				this.selectChange(this.defaultActive, id);
			}
		},
		selectChange(index, id) {
			this.defaultActive = index;
			this.chooseIndex = index;
			let obj = this.workList.find(item => {
				return item.index == index;
			});
			this.applicationId = obj ? obj.applicationId : '';
			//获取查找待办的id
			if (this.isSearch) {
				this.searchId = id;
				this.isSearch = false;
			}
		}
	}
};
</script>

<style lang="scss" scoped>
.content {
	display: flex;
	width: 100%;
	height: 100%;
	.content-menu {
		height: 100%;
		width: 284px;
		background: #f3f4f6;
		.menuee-title {
			height: 48px;
			color: $primaryTextColor;
			font-size: 18px;
			font-weight: 800;
			padding: 16px;
		}
		.menu-info {
			.menu-itemTitle {
				height: 54px;
				color: #000;
				font-weight: 500;
				padding-left: 13px;
				padding-top: 16px;
				font-size: 16px;
			}
			.el-menu-item {
				font-size: 14px;
				color: $primaryTextColor;
				height: 46px;
				margin: 2px 10px;
				line-height: 46px;
				display: flex;
				align-items: center;
				padding-left: 10px !important;
			}
			.el-menu-item:hover {
				color: var(--brand-6) !important;
				background: var(--brand-1) !important;
				border-radius: 6px;
			}
			.el-menu-item.is-active {
				background-color: var(--brand-1) !important; //选中背景色
				color: var(--brand-6); //选中颜色
				border-radius: 6px;
			}
			.svg-icon {
				margin-right: 5px;
				font-size: 20px;

				color: var(--brand-6);
			}
			.workSvg {
				width: 15px;
				height: 15px;
				margin-bottom: 2px;
			}
		}
	}
	.handle-content {
		width: calc(100% - 284px);
		height: 100%; // 测试
	}
}

::v-deep .el-menu {
	border-right: none !important;
}
</style>
