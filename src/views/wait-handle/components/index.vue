<template>
	<div class="main-content">
		<div class="title">
			<span>{{ waitTitle }}</span>
			({{ handleNum }})
		</div>
		<search ref="search" @search="search"></search>
		<div ref="container" v-loading="isLoading" class="list-boder">
			<vxe-table
				v-if="!hideTable"
				v-show="!isLoading"
				ref="table"
				class="desk-el-table"
				height="100%"
				width="100%"
				:data="tableData"
				:border="false"
				:header-cell-style="headerCellStyle"
				@column-resizable-change="handleResizeColumn"
			>
				<vxe-column :key="Math.random()" title="序号" width="50" type="index" fixed="left">
					<template v-slot="{ rowIndex }">
						{{ rowIndex + 1 }}
					</template>
				</vxe-column>
				<vxe-column
					v-if="specialField.indexOf('关注操作') !== -1"
					:key="Math.random()"
					title="关注"
					width="50"
				>
					<template v-slot="{ row }">
						<span class="star-off" @click="toggleFavorite(row)">
							<svg-icon
								v-if="row.isFellow"
								icon-class="attention-red"
								class="list-item-top-img"
							></svg-icon>
							<svg-icon v-else icon-class="attention" class="list-item-top-img"></svg-icon>
							<!-- <i
								:class="
									row.isFellow ? 'el-icon-star-off star-off star-red' : 'el-icon-star-off star-off'
								"
							></i> -->
						</span>
					</template>
				</vxe-column>
				<vxe-column
					resizable
					:title="waitTitle + '标题'"
					show-overflow
					min-width="150"
					field="title"
					:width="columnWidths['title']"
					:render-overflow="renderOverflow"
				></vxe-column>
				<vxe-column
					v-if="specialField.indexOf('待办来源') !== -1"
					:key="Math.random()"
					resizable
					title="待办来源"
					min-width="150"
					field="resousre"
					:width="columnWidths['resousre']"
				>
					<template slot-scope="{ row }">
						<div class="nameInfo">
							<el-image
								v-if="row.createByLogoPath"
								:src="row.createByLogoPath"
								class="svg-icon workSvg"
							></el-image>
							<div v-else-if="row.createByName" class="nameLast">
								{{ row.createByName.slice(-1) }}
							</div>
							<!-- <i v-else class="coos-iconfont icon-shuzhuangtu1 svg-icon" style="color: #faad14"></i> -->
							<div class="nameDetail">
								{{ row.createByName }}
							</div>
						</div>
					</template>
				</vxe-column>
				<vxe-column
					resizable
					title="待办类型"
					min-width="100"
					field="businessType"
					:width="columnWidths['businessType']"
					show-overflow
					:render-overflow="renderOverflow"
				></vxe-column>
				<vxe-column title="紧急程度" width="88">
					<template slot-scope="{ row }">
						<div>
							<div
								:class="{
									common: row.urgencyLevel == 3,
									second: row.urgencyLevel == 2,
									ordinary: row.urgencyLevel == 1,
									urgent: row.urgencyLevel == 0,
									extraUrgent: row.urgencyLevel == -1
								}"
							>
								{{ row.urgencyLevelDictText }}
							</div>
						</div>
					</template>
				</vxe-column>
				<vxe-column
					v-if="specialField.indexOf('上一步节点') !== -1"
					:key="Math.random()"
					resizable
					title="上一步节点"
					min-width="150"
					field="lastNode"
					:width="columnWidths['lastNode']"
					show-overflow
					:render-overflow="renderOverflow"
				></vxe-column>
				<vxe-column
					v-if="specialField.indexOf('当前节点') !== -1"
					:key="Math.random()"
					resizable
					title="当前节点"
					min-width="150"
					field="transactionCurrentNode"
					:width="columnWidths['transactionCurrentNode']"
					show-overflow
					:render-overflow="renderOverflow"
				></vxe-column>
				<vxe-column v-if="specialField.indexOf('处理状态') !== -1" title="处理状态" width="100">
					<template slot-scope="{ row }">
						<div
							class="handleStatus"
							:class="{ over: row.status === 1 || row.waitHandleNotice, notover: row.status == 0 }"
						>
							{{ row.statusDesc }}
						</div>
					</template>
				</vxe-column>
				<vxe-column v-if="specialField.indexOf('办理状态') !== -1" title="办理状态" width="88">
					<template slot-scope="{ row }">
						<div
							class="handleStatus"
							:class="{
								over: !row.currentStatusHandle || row.currentStatusHandle !== 'handled',
								notover: row.currentStatusHandle && row.currentStatusHandle === 'handled'
							}"
						>
							{{ row.currentStatusHandleDesc }}
						</div>
					</template>
				</vxe-column>
				<vxe-column
					v-if="specialField.indexOf('当前状态') !== -1"
					title="当前状态"
					width="100"
					field="currentStatusDesc"
				></vxe-column>
				<vxe-column
					v-if="specialField.indexOf('主办人') !== -1"
					resizable
					min-width="150"
					title="主办人"
					show-overflow
					field="userName"
					:width="columnWidths['businessType']"
					:render-overflow="renderOverflow"
				></vxe-column>
				<vxe-column
					v-if="specialField.indexOf('创建时间') !== -1"
					resizable
					min-width="150"
					title="创建时间"
					show-overflow
					field="createTimeDesc"
					:width="columnWidths['createTimeDesc']"
					:render-overflow="renderOverflow"
				></vxe-column>
				<vxe-column
					v-if="specialField.indexOf('处理时间') !== -1"
					resizable
					min-width="150"
					title="处理时间"
					field="dealTimeDesc"
					:width="columnWidths['dealTimeDesc']"
					show-overflow
					:render-overflow="renderOverflow"
				></vxe-column>
				<vxe-column
					v-if="specialField.indexOf('接收时间') !== -1"
					resizable
					min-width="150"
					title="接收时间"
					field="receiveTimeDesc"
					:width="columnWidths['receiveTimeDesc']"
					show-overflow
					:render-overflow="renderOverflow"
				></vxe-column>
				<vxe-column
					v-if="specialField.indexOf('关注时间') !== -1"
					resizable
					min-width="150"
					title="关注时间"
					field="fellowTime"
					:width="columnWidths['fellowTime']"
					show-overflow
					:render-overflow="renderOverflow"
				></vxe-column>
				<vxe-column :key="Math.random()" title="操作" fixed="right" align="center" width="100">
					<template slot-scope="{ row }">
						<div class="handle-column" @click="goHandle(row)">
							{{ row.waitHandleNotice ? '处理中' : row.status == 1 ? '详情' : '办理' }}
						</div>
					</template>
				</vxe-column>
				<template slot="empty">
					<BasicEmpty :data="tableData" name="no-data" />
				</template>
			</vxe-table>
		</div>
		<div class="page">
			<el-pagination
				class="desk-el-pagination"
				background
				:current-page="pageInfo.pageNo"
				:page-sizes="[10, 20, 30, 50]"
				:page-size="pageInfo.pageSize"
				layout="total, sizes, ->, prev, pager, next, jumper"
				:total="handleNum"
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
			></el-pagination>
		</div>
		<!--  待办详情Start  -->
		<waitHandle ref="detailDialog" @updateList="updateList"></waitHandle>
		<!--  待办详情End  -->
	</div>
</template>

<script>
import { handleList, userInteraction } from '@/api/modules/wait-handle';
import { assetsUrl } from '@/config';
import search from '@/views/wait-handle/components/search';
import waitHandle from '@/components/wait-detail/index.vue';
import { CoosEventTypes } from '@/utils/bus';
import { getDictionary } from '@/utils/data-dictionary';
import { GetQueryString } from '@/utils';
export default {
	components: { search, waitHandle },
	props: {
		workList: {
			type: Array,
			default: () => {
				return [];
			}
		},
		openDetailType: {
			type: String,
			default: () => {
				return '1';
			}
		},
		defaultMenu: {
			type: Array,
			default: () => {
				return [];
			}
		},
		chooseIndex: {
			type: [String, Number],
			default: '1'
		},
		// 工作台列表参数
		applicationId: {
			type: String,
			default: ''
		},
		searchId: {
			type: String,
			default: ''
		}
	},
	data() {
		return {
			columnWidths: {},
			hideTable: false,
			isWebview: true, // 是否webview
			showCoos: false,
			metadataIds: '',
			robotDataContent: '',
			chatObjectId: '',
			assetsUrl,
			flowParams: {}, // 流程参数
			handleNum: 0,
			isLoading: true,
			tableData: [],
			waitTitle: '待办',
			headerCellStyle: {
				color: '#000',
				fontSize: '14px',
				fontWeight: '500',
				height: '54px'
			},
			pageInfo: {
				pageNo: 1,
				pageSize: 30,
				status: 0
			},
			needReload: false, //是否需要重新加载列表(当数据是【待处理】的情况下，在关闭对话框前重新加载列表数据来更新用户的操作结果)
			detailUrl: '', // webview的地址
			defaultMenuIndex: []
		};
	},
	computed: {
		// 特殊字段(不同事项类型需展示的不同表头)
		specialField() {
			let obj = null;
			if (this.chooseIndex && typeof this.chooseIndex === 'string') {
				const targetArray =
					this.chooseIndex.indexOf('menu') > -1 ? this.defaultMenu : this.workList;

				if (Array.isArray(targetArray)) {
					obj = targetArray.find(item => item && item.index === this.chooseIndex);
				}
			}
			if (obj && obj.specialSearch) {
				this.$nextTick(() => {
					this.$refs.search.setSearch(obj.specialSearch);
				});
			} else {
				this.$nextTick(() => {
					this.$refs.search.setSearch('');
				});
			}
			console.log(obj && obj.specialField ? obj.specialField : '', '====================');

			return obj && obj.specialField ? obj.specialField : '';
		}
	},
	watch: {
		$route(newVal) {
			this.openHandle();
		},

		defaultMenu(newVal) {
			newVal.map(v => {
				this.defaultMenuIndex.push(v.index);
			});
			if (this.chooseIndex.indexOf('menu') > -1) {
				let obj = newVal.find(item => {
					return item.index === this.chooseIndex;
				});
				this.waitTitle = obj.shortName;
				this.pageInfo = {
					pageNo: 1,
					pageSize: 30
				};
				let params = {};
				obj.condition.map(v => {
					params[v.key] = v.value;
					this.pageInfo[v.key] = v.value;
				});
				this.$nextTick(() => {
					this.$refs.search.businessTypes(params);
				});
				this.getHandleList(this.pageInfo);
			}
		},
		chooseIndex: {
			handler(newVal) {
				this.$refs.search.reset(false);
				if (newVal.indexOf('menu') > -1) {
					let obj = this.defaultMenu.find(item => {
						return item.index === newVal;
					});
					this.waitTitle = obj.shortName;
					this.pageInfo = {
						pageNo: 1,
						pageSize: 30
					};
					let params = {};
					obj.condition.map(v => {
						params[v.key] = v.value;
						this.pageInfo[v.key] = v.value;
					});
					this.$nextTick(() => {
						this.$refs.search.businessTypes(params);
					});
					this.getHandleList(this.pageInfo);
				} else {
					let obj = this.workList.find(item => {
						return item.index === newVal;
					});
					this.waitTitle = obj ? obj.name : '待办';
					this.pageInfo = {
						pageNo: 1,
						pageSize: 30
					};
					if (this.$route.path === '/wait-handle' && Object.keys(this.$route.query).length) {
						this.getHandleList({ ...this.pageInfo, applicationId: this.applicationId }, true);
					} else {
						this.getHandleList({ ...this.pageInfo, applicationId: this.applicationId });
					}
				}
				this.getColumnWidths();
			}
		}
	},

	created() {
		this.getColumnWidths();
	},

	mounted() {
		this.openHandle();
		this._BUS.$on(CoosEventTypes.updateWaitList, this.socketUpdateList);
	},
	destroyed() {
		this._BUS.$off(CoosEventTypes.updateWaitList, this.socketUpdateList);
	},
	methods: {
		renderOverflow({ cellValue }) {
			const h = this.$createElement;
			return h('vxe-tooltip', {
				props: { content: cellValue, transfer: true },
				slots: { default: () => cellValue }
			});
		},
		getColumnWidths() {
			// 重新渲染表格
			this.hideTable = true;
			let congfig = localStorage.getItem(this.waitTitle + '待办');
			if (congfig) {
				congfig = JSON.parse(congfig);
			} else {
				congfig = {};
			}
			this.columnWidths = congfig;
			setTimeout(() => {
				// 重新渲染表格,生成新的宽度
				this.hideTable = false;
			}, 5);
		},
		handleResizeColumn(e) {
			// 设置列宽缓存
			let waitName = this.waitTitle + '待办';
			let congf = localStorage.getItem(waitName);
			if (congf) {
				congf = JSON.parse(congf);
			} else {
				congf = {};
			}
			congf[e.column.field] = e.resizeWidth;
			localStorage.setItem(waitName, JSON.stringify(congf));
		},
		/**改变页码*/
		handleCurrentChange(i) {
			this.pageInfo.pageNo = i;
			this.getList();
		},
		/**改变分页大小*/
		handleSizeChange(i) {
			this.pageInfo.pageNo = 1;
			this.pageInfo.pageSize = i;
			this.getList();
		},
		getList() {
			if (['1', '2'].includes(this.chooseIndex)) {
				this.getHandleList(this.pageInfo, false);
			} else {
				this.getHandleList({ ...this.pageInfo, applicationId: this.applicationId }, false);
			}
		},
		socketUpdateList() {
			this.getHandleList(this.pageInfo);
		},
		/**关闭弹窗更新*/
		updateList() {
			let params = { ...this.pageInfo };
			if (this.applicationId) {
				params.applicationId = this.applicationId;
			}
			this.getHandleList(params);
		},
		/**搜索*/
		search() {
			this.pageInfo.pageNo = 1;
			let params = {
				...this.pageInfo
			};
			// 处理搜索的时候就没有带上applicationId的bug
			if (!this.defaultMenuIndex.includes(this.chooseIndex)) {
				params.applicationId = this.applicationId;
			}
			this.getHandleList(params);
		},
		// 跳转打开待办
		openHandle() {
			if (this.$route.path === '/wait-handle') {
				if (Object.keys(this.$route.query).length) {
					this.$refs.search.reset(false);
					this.getHandleList(this.pageInfo, true);
				} else {
					this.getHandleList(this.pageInfo);
				}
				this._BUS.$emit(CoosEventTypes.aiSessionParams, {
					coosType: 'waitList',
					coosTypeCode: getDictionary('AI编码/智问待办'),
					applicationId: getDictionary('应用ID/待办'),
					modeType: 'zdbk',
					canDbClick: true
				});
			}
		},
		//判断反的数据类型
		isJSONString(str) {
			try {
				JSON.parse(str);
				return true;
			} catch (e) {
				return false;
			}
		},

		/** 去处理 */
		goHandle(rows) {
			let row = '';
			if (rows) {
				row = rows;
			} else {
				this.tableData.forEach(item => {
					if (item.id === this.searchId) {
						row = item;
					}
				});
			}
			if (!row) return;
			if (this.openDetailType === '2') {
				try {
					let urlMap = JSON.parse(row.detailUrl); // 解析出url映射
					let url = urlMap.PC;
					if (GetQueryString(url, 'newPage')) {
						window.open(url, '_blank');
						return;
					}
				} catch (e) {
					console.log(e);
				}
				const route = this.$router.resolve({ name: 'WaitDetail' });
				window.open(`${route.href}?blank_id=${row.id}`, '_blank');
			} else {
				this.$refs.detailDialog.open(row);
			}
		},
		/** 获取我的待办列表  val为真说明为外部进入需打开处理流程*/
		getHandleList(params, val) {
			this.isLoading = true;
			// if (['1', '2'].includes(this.chooseIndex)) {
			handleList({ ...params, ...this.$refs.search.form, id: this.searchId }).then(res => {
				this.handleNum = res.result.total;
				let arr = res?.result?.records || [];
				arr.forEach(item => {
					item.businessType = item.businessType || item.taskTypeDictText;
					item.url = `${assetsUrl}/workspace/client/workPlatformTaskAppsIcon/${item.applicationId}.png`;
				});
				this.tableData = arr;
				this.isLoading = false;
				if (val) {
					this.goHandle(false);
				}
			});
			// }
		},
		/**关注 */
		toggleFavorite(item) {
			console.log(item);
			if (item.isFellow == null) {
				item.isFellow = true;
			} else {
				item.isFellow = !item.isFellow;
			}
			this.userInteraction({
				actionType: '1',
				objectType: 'task',
				objectTitle: item.title,
				objectId: item.transactionId
			});
			if (this.waitTitle == '关注') {
				window.vm._BUS.$emit(CoosEventTypes.updateWaitList);
			}
		},
		//记录行为
		userInteraction(par) {
			userInteraction(par).then(res => {
				if (this.waitTitle == '关注') {
					window.vm._BUS.$emit(CoosEventTypes.updateWaitList);
				}
				window.vm._BUS.$emit(CoosEventTypes.updateWaitCount);
			});
		}
	}
};
</script>

<style lang="scss" scoped>
::v-deep.el-table td,
.el-table th.is-leaf {
	border-bottom: 1px solid #f0f0f0;
}

::v-deep .has-gutter tr th {
	border: none;
}

::v-deep .el-table__header-wrapper {
	border-bottom: 1px solid #f0f0f0;
}

::v-deep .el-table__row:last-child > td {
	border: none;
}

::v-deep .el-drawer__body {
	overflow: hidden;
}

::v-deep .el-table::before {
	height: 0px;
}

.handleStatus {
	width: 50px;
	height: 22px;
	font-weight: 400;
	font-size: 12px;
	line-height: 22px;
	text-align: center;
	border-radius: 3px 3px 3px 3px;
}

.over {
	background: #e6eaf2;
	color: #515b6e;
}

.notover {
	background: rgba(24, 144, 255, 0.1);
	color: #1890ff;
}

//common: row.urgencyLevel == 3,
//second: row.urgencyLevel == 2,
//ordinary: row.urgencyLevel == 1,
//urgent: row.urgencyLevel == 0,
//extraUrgent: row.urgencyLevel == -1
.common {
	color: #3088ff;
	background-color: rgb(48, 136, 255, 0.1);
	padding: 3px 4px;
	border-radius: 4px;
	text-align: center;
}

.second {
	color: #faad14;
	background-color: rgb(48 136 255 / 10%);
	padding: 3px 4px;
	border-radius: 4px;
	text-align: center;
}
.ordinary {
	color: #ed7b2f;
	background-color: rgb(168 0 13 / 10%);
	padding: 3px 4px;
	border-radius: 4px;
	text-align: center;
}
.urgent {
	color: #e34d59;
	background-color: rgb(227 77 89 / 10%);
	padding: 3px 4px;
	border-radius: 4px;
	text-align: center;
}

.extraUrgent {
	color: #a8000d;
	background-color: rgb(168 0 13 / 10%);
	padding: 3px 4px;
	border-radius: 4px;
	text-align: center;
}
.main-content {
	background: #fff;
	position: relative;
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;

	.title {
		width: 100%;
		height: 54px;
		background: #fff;
		line-height: 54px;
		padding-left: 20px;
		color: $primaryTextColor;
		font-weight: 800;
		border-bottom: 1px solid #f0f0f0;
	}

	.list-boder {
		flex: 1;
		// border: 1px solid #f0f0f0;
		// border-radius: 9px;
		// padding: 12px 10px;
		margin: 12px 20px;
		overflow: hidden;
		//height: 100%;
		.columInfo {
			.deadTime {
				line-height: 20px;
				color: #ff4d4f;
				font-size: 12px;
			}
		}

		.nameInfo {
			display: flex;
			align-items: center;

			.nameLast {
				flex-shrink: 0;
				margin-right: 5px;
				width: 22px;
				height: 22px;
				background: var(--brand-6);
				color: #fff;
				line-height: 22px;
				text-align: center;
				border-radius: 3px;
			}

			.svg-icon {
				margin-right: 5px;
				font-size: 25px;

				color: var(--brand-6);
			}

			.workSvg {
				flex-shrink: 0;
				width: 22px;
				height: 22px;
				border-radius: 3px;
			}

			.nameDetail {
				overflow: hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
			}

			// .name {
			// }
		}

		.handle-column {
			cursor: pointer;
			font-weight: 400;
			font-size: 14px;
			color: var(--brand-6);
			line-height: 22px;
		}
	}

	::v-deep .el-drawer__header {
		margin: 0;
		// height: 56px !important;
		color: hsl(0, 0%, 15%);
		font-weight: 600;
		font-size: 16px;
		padding: 0px 16px;
	}

	::v-deep .el-drawer__header > :first-child {
		height: 56px;
		display: flex;
		align-items: center;
	}

	.dialogInfo {
		.urlInfo {
			width: 100%;
			height: 100%;
			border: none;
			border-top: 1px solid #f0f0f0;
		}
	}
}

// 提取组件发现的，不知道哪里会用
.desk-el-form {
	::v-deep .el-input__inner {
		height: 32px !important;
	}
}

.createTimeDesc {
	@include aLineEllipse;
}

.page {
	padding: 27px 24px 24px;

	::v-deep.el-pager li.active {
		color: #fff;
	}
}
::v-deep .vxe-table--render-wrapper,
::v-deep .vxe-table--layout-wrapper,
::v-deep .vxe-table--main-wrapper,
.vxe-table--viewport-wrapper {
	border: none !important;
	// display: none;
}
::v-deep .vxe-table--render-default.border--default .vxe-header--column {
	/* 修改表头 */
	background: #fff !important;
	border-bottom: 1px solid #e0e0e0 !important;
}
::v-deep .vxe-table--border .vxe-table--body-wrapper .vxe-body--column-wrapper {
	border-right: none !important; /* 去掉右侧边框 */
	background-image: none !important;
}

::v-deep .vxe-table--border .vxe-table--header-wrapper .vxe-header--column {
	border-right: none !important; /* 去掉表头右侧边框 */
	background-image: none !important;
	background-image: none !important;
}

::v-deep .vxe-table--border .vxe-table--body-wrapper .vxe-body--row {
	border-bottom: none !important; /* 去掉行与行之间的下边框 */
	background-image: none !important;
}
::v-deep .vxe-table--render-default.border--default .vxe-body--column {
	background-image: none !important; /* 去掉行与行伪装成边框的背景 */
}
::v-deep .vxe-table .vxe-body--column,
.vxe-table .vxe-header--column {
	border-right-color: transparent; /* 或者你希望的任何颜色 */
	border-right-width: 0; /* 或者你希望的宽度 */
}
.star-red {
	color: red;
}
.star-off {
	cursor: pointer;
	position: relative;
}
.list-item-top-img {
	position: absolute;
	top: calc(50% - 10px);
}
</style>
