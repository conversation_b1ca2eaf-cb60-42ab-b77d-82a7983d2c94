<template>
	<div class="search desk-el-form">
		<el-row>
			<el-col :span="12" class="search-item">
				<div class="search-label">关键字：</div>
				<el-input
					v-model="form.title"
					placeholder="请输入"
					class="search-input"
					clearable
					@keydown.enter.native="search"
				></el-input>
			</el-col>
			<el-col :span="12" class="search-item">
				<div class="search-label">类型：</div>
				<el-select
					v-model="form.businessType"
					clearable
					filterable
					placeholder="请选择"
					class="search-input"
				>
					<el-option
						v-for="item in businessTypesList"
						:key="item.value"
						:label="item.label"
						:value="item.value"
					></el-option>
				</el-select>
			</el-col>
			<el-col :span="12" class="search-item">
				<div class="search-label">紧急程度：</div>
				<el-select v-model="form.urgencyLevel" clearable placeholder="请选择" class="search-input">
					<el-option
						v-for="item in urgencyLevelOptions"
						:key="item.itemValue"
						:label="item.itemText"
						:value="item.itemValue"
					></el-option>
				</el-select>
			</el-col>
			<el-col v-if="specialSearch.indexOf('待办来源') != -1" :span="12" class="search-item">
				<div class="search-label">来源：</div>
				<el-input
					v-model="form.createByName"
					placeholder="请输入"
					class="search-input"
					clearable
					@keydown.enter.native="search"
				></el-input>
			</el-col>
			<el-col v-if="specialSearch.indexOf('主办人') != -1" :span="12" class="search-item">
				<div class="search-label">主办人：</div>
				<el-select v-model="form.mainUserId" clearable placeholder="请选择" class="search-input">
					<el-option
						v-for="item in myAgentsList"
						:key="item.userId"
						:label="item.realname"
						:value="item.userId"
					></el-option>
				</el-select>
			</el-col>
			<el-col v-if="specialSearch.indexOf('办理状态') != -1" :span="12" class="search-item">
				<div class="search-label">办理状态：</div>
				<el-select
					v-model="form.currentStatusHandle"
					clearable
					placeholder="请选择"
					class="search-input"
				>
					<el-option
						v-for="item in currentStatusHandleOptions"
						:key="item.value"
						:label="item.label"
						:value="item.value"
					></el-option>
				</el-select>
			</el-col>
		</el-row>
		<div class="search-button">
			<div class="search-button-sure" @click="search">查询</div>
			<div class="search-button-cale" @click="reset(true)">重置</div>
		</div>
	</div>
</template>

<script>
import { businessTypes, myAgents } from '@/api/modules/wait-handle';
import { getCalendarDirection } from '@/api/modules/calendar';

export default {
	name: 'Search',
	data() {
		return {
			form: {
				title: '',
				businessType: '',
				createByName: '',
				mainUserId: '',
				urgencyLevel: '',
				currentStatusHandle: null
			},
			myAgentsList: [],
			// 办理状态
			currentStatusHandleOptions: [
				{
					label: '在途',
					value: 'processing'
				},
				{
					label: '已办结',
					value: 'handled'
				},
				{
					label: '驳回',
					value: 'rejected'
				}
			],
			specialSearch: '',
			businessTypesList: [], //类型
			// 紧急程度
			urgencyLevelOptions: []
		};
	},
	created() {
		getCalendarDirection('task_urgency_level').then(res => {
			console.log(res, 'res');
			this.urgencyLevelOptions = res.result.task_urgency_level;
		});
		myAgents().then(res => {
			this.myAgentsList = res.result || [];
		});
	},
	methods: {
		businessTypes(params) {
			businessTypes(params).then(res => {
				this.businessTypesList = res.result || [];
			});
		},
		/**搜索*/
		search() {
			this.$emit('search', this.form);
		},
		setSearch(text) {
			this.specialSearch = text || '';
		},
		/**重置*/
		reset(isQuest = true) {
			Object.keys(this.form).forEach(key => {
				this.form[key] = '';
			});
			if (isQuest) {
				this.search();
			}
		}
	}
};
</script>

<style scoped lang="scss">
.search {
	padding: 11px 188px 0 20px;
	position: relative;
	background: #ffffff;
	border-bottom: 1px solid #f0f0f0;
	flex-wrap: wrap;
	@include flexBox(flex-start);
	&-item {
		@include flexBox(flex-start);
		margin-bottom: 11px;
	}
	&-label {
		width: 80px;
		font-weight: 400;
		font-size: 14px;
		color: rgba(0, 0, 0, 0.6);
		line-height: 22px;
		text-align: right;
	}
	&-input {
		flex: 1;
	}
	&-button {
		position: absolute;
		right: 20px;
		top: 11px;
		@include flexBox();
		&-sure {
			width: 68px;
			height: 32px;
			background: #ffffff;
			border-radius: 6px;
			border: 1px solid var(--brand-6);
			font-weight: 400;
			font-size: 14px;
			color: var(--brand-6);
			line-height: 22px;
			margin-right: 4px;
			cursor: pointer;
			@include flexBox();
		}
		&-cale {
			width: 68px;
			height: 32px;
			background: #ffffff;
			border-radius: 6px;
			border: 1px solid $borderColor;
			font-weight: 400;
			font-size: 14px;
			color: $textColor;
			line-height: 22px;
			cursor: pointer;
			@include flexBox();
		}
	}
}
</style>
