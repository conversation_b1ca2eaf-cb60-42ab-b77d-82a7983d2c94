<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2024-08-19 08:42:15
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-09-02 08:56:12
 * @FilePath: /coos-desktop-app/src/views/calendar/index.vue
 * @Description:
-->
<template>
	<div class="calendar">
		<el-button
			v-if="activeName === 'calendar'"
			type="primary"
			class="btn-add-calendar"
			@click="handleOpenAdd('newAdd')"
		>
			添加日程
		</el-button>
		<el-tabs v-model="activeName">
			<el-tab-pane label="日历" name="calendar">
				<calendar ref="calendar" />
			</el-tab-pane>
			<el-tab-pane label="工作备忘录" name="workMemo">
				<WorkMemo ref="workMemo" />
			</el-tab-pane>
		</el-tabs>
	</div>
</template>

<script>
import Calendar from './components/calendar/index.vue';
import WorkMemo from './components/work-memo/index.vue';
export default {
	name: 'Index',
	components: { Calendar, WorkMemo },
	data() {
		return {
			activeName: 'calendar'
		};
	},
	watch: {
		$route(newVal) {
			this.searchDetail(newVal);
		}
	},
	mounted() {
		this.searchDetail(this.$route);
	},
	methods: {
		/**全局搜索以及跳转进入详情*/
		searchDetail(newVal) {
			if (newVal.path == '/calendar') {
				setTimeout(() => {
					this.activeName = newVal.query.type === 'memorandum' ? 'workMemo' : 'calendar';
					this.$refs[this.activeName].openDetail(newVal.query);
				}, 1000);
			}
		},
		handleOpenAdd(type) {
			this.$refs.calendar.updateInit(type);
		}
	}
};
</script>

<style scoped lang="scss">
.calendar {
	height: 100%;
	overflow-y: hidden;
	display: flex;
	flex-direction: column;
	position: relative;
	.btn-add-calendar {
		position: absolute;
		top: 10px;
		right: 20px;
		z-index: 1;
	}
	::v-deep {
		.el-tabs {
			flex: 1;
			display: flex;
			flex-direction: column;
			overflow: hidden;
		}
		.el-tabs__header {
			background-color: #fff;
			height: 54px;
			margin: 0;
		}
		.el-tabs__nav-wrap {
			padding: 0 14px;
		}
		.el-tabs__item {
			height: 54px;
			line-height: 54px;
			font-size: 18px;
		}
		.el-tabs__content {
			height: 100%;
			.el-tab-pane {
				height: 100%;
			}
		}
	}
}
</style>
