<template>
	<div>
		<el-dialog
			v-loading="loading"
			top="0"
			width="960px"
			append-to-body
			class="desk-el-custom-dialog"
			title="选择项目"
			:show-close="true"
			:visible.sync="showPopup"
		>
			<div class="body">
				<el-form ref="searchForm" class="form" inline :status-icon="false" :model="searchForm">
					<el-form-item
						v-for="item in slotJson.customParams"
						:key="item.key"
						:prop="item.key"
						:label="item.label"
					>
						<template v-if="item.type === 'select'">
							<el-select v-model="searchForm[item.key]">
								<el-option
									v-for="option in item.options"
									:key="option.value"
									:label="option.label"
									:value="option.value"
								/>
							</el-select>
						</template>
						<template v-else-if="item.type === 'input'">
							<el-input
								v-model="searchForm[item.key]"
								clearable
								:placeholder="`请输入${item.label}进行搜索`"
							/>
						</template>
					</el-form-item>
					<el-form-item>
						<el-button icon="el-icon-search" type="primary" @click="handleProjectSearch">
							搜索
						</el-button>
						<el-button type="default" @click="handleProjectSearch('reset')">重置</el-button>
					</el-form-item>
				</el-form>
				<el-table
					ref="table"
					v-loading="tableLoading"
					border
					height="400px"
					highlight-current-row
					:row-style="getRowStyle"
					:header-row-style="headerRowStyle"
					:header-cell-style="headerCellStyle"
					:data="tablePagination.data"
					@row-dblclick="handleTableRowDblclick"
					@row-click="handleTableRowClick"
				>
					<el-table-column
						v-for="(header, i) of tableHeader"
						:key="'head-' + i"
						:label="header.label"
						:prop="header.key"
						:min-width="header.minWidth || 0"
						:align="header.align"
						:show-overflow-tooltip="header.isShowOverflowTooltip"
					/>
				</el-table>
				<el-pagination
					:current-page.sync="tablePagination.pageNo"
					:page-size="tablePagination.pageSize"
					layout="total, prev, pager, next"
					:total="tablePagination.total"
					style="margin-top: 12px"
					@current-change="getProjectList"
				/>
			</div>
			<span slot="footer" class="dialog-footer">
				<el-button @click="handleCancel">取 消</el-button>
				<el-button type="primary" @click="saveEditAvatar">确 定</el-button>
			</span>
		</el-dialog>
	</div>
</template>

<script>
import { isEmpty } from 'lodash';
import request from '@/utils/request';

export default {
	name: 'ProjectSelect',
	props: {
		// 项目选择插槽json配置
		slotJson: {
			type: Object,
			default: () => {
				return {};
			}
		}
	},
	data() {
		return {
			// 查询表单
			searchForm: {},
			selectedRow: { project: '', projectDictText: '' },
			tablePagination: {
				data: [],
				pageNo: 1,
				pageSize: 10,
				total: 0
			},
			// 表头
			tableHeader: [
				{
					label: '编码',
					key: 'code',
					minWidth: 160,
					align: 'left',
					isShowOverflowTooltip: false
				},
				{
					label: '名称',
					key: 'name',
					minWidth: 160,
					align: 'left',
					isShowOverflowTooltip: false
				}
			],
			loading: false, // 加载效果
			showPopup: false, // 弹窗显隐
			/**每一行的样式*/
			headerRowStyle: {
				height: '46px',
				background: '#EDF2F6'
			},
			/**每一格样式*/
			headerCellStyle: {
				background: 'transparent',
				fontSize: '16px',
				fontFamily: 'PingFang SC, PingFang SC',
				fontWeight: 500,
				color: '#2f446b',
				lineHeight: '24px'
			},
			tableLoading: true
		};
	},
	watch: {
		slotJson() {
			this.initFormJson();
			this.getProjectList();
		}
	},
	mounted() {
		if (!isEmpty(this.slotJson)) {
			this.initFormJson();
			this.getProjectList();
		}
	},
	methods: {
		/**关闭弹窗*/
		handleCancel() {
			this.showPopup = false;
		},
		/**确认保存*/
		saveEditAvatar() {
			if (this.selectedRow.project) {
				this.$emit('sure', this.selectedRow);
				this.showPopup = false;
			} else {
				this.$message.error('请选择项目');
			}
		},
		/**初始化*/
		initFormJson() {
			this.searchForm = this.slotJson.customParams.reduce((act, cur) => {
				act[cur.key] = cur.initValue;
				return act;
			}, {});
		},
		/**获取表格奇偶列的样式*/
		getRowStyle({ row, rowIndex }) {
			return {
				height: '54px',
				background: rowIndex % 2 === 0 ? '#ffffff' : ' #f5f7fa'
			};
		},
		/**打开弹窗*/
		open({ project, projectDictText }) {
			this.selectedRow.project = project || '';
			this.selectedRow.projectDictText = projectDictText || '';
			this.showPopup = true;
		},
		// 获取项目列表数据
		async getProjectList() {
			try {
				this.tableLoading = true;
				const { result } = await request({
					url: this.slotJson.api.split('&code=1')[0],
					method: 'get',
					params: {
						...this.searchForm,
						pageNo: this.tablePagination.pageNo,
						pageSize: this.tablePagination.pageSize
					}
				});
				this.tablePagination.total = result.total;
				this.tablePagination.data = result.records;
			} catch (error) {
				console.log(
					'%c [  ]-242-「project-select」',
					'font-size:13px; background:pink; color:#bf2c9f;',
					error
				);
			} finally {
				this.tableLoading = false;
			}
		},
		// 监听表格单击事件
		handleTableRowClick(row) {
			this.selectedRow.project = row.id;
			this.selectedRow.projectDictText = row.name;
		},
		// 监听表格双击事件
		handleTableRowDblclick(row) {
			this.selectedRow.project = row.id;
			this.selectedRow.projectDictText = row.name;
			this.saveEditAvatar();
		},
		// 具体项目选择搜索
		handleProjectSearch(type) {
			if (type === 'reset') {
				this.$refs.searchForm.resetFields();
			}
			this.tablePagination.pageNo = 1;
			this.getProjectList();
		}
	}
};
</script>

<style scoped lang="scss">
.table-content {
	background: #ffffff;
	border-radius: 6px;
	border: 1px solid $borderColor;
	padding: 12px;
	&-button {
		cursor: pointer;
		display: inline-block;
		background: #ffffff;
		border-radius: 6px;
		border: 1px solid var(--brand-6);
		padding: 5px 16px;
		font-weight: 400;
		font-size: 14px;
		color: var(--brand-6);
		line-height: 22px;
		&-icon {
			margin-right: 4px;
			color: var(--brand-6);
		}
	}
}
.desk-el-custom-dialog {
	::v-deep .el-dialog {
		display: flex;
		flex-direction: column;
		overflow: hidden;
		.el-dialog__body {
			flex: 1;
			overflow: hidden;
		}
	}
}
.body {
	max-height: 60vh;
	overflow-y: auto;
	@include noScrollBar;
}
::v-deep .el-table__body tr.current-row > td {
	background: #eef4fd !important;
}
</style>
