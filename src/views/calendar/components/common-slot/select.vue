<script>
import request from '@/utils/request';
export default {
	title: '普通下拉框组件',
	name: 'Select',
	components: {
		footerSlot: {
			props: {
				vNode: [Array, String, Object, Number]
			},
			render(h) {
				if (typeof this.vNode === 'object') {
					return this.vNode;
				} else {
					return h('div', this.vNode);
				}
			}
		}
	},
	props: {
		data: {
			type: [String, Number, Array],
			default: () => null
		},
		options: {
			type: Array,
			default: () => []
		},
		optionKeyMap: {
			type: Object,
			default: () => ({
				label: 'label',
				value: 'id'
			})
		},
		loadMethod: {
			type: Function,
			default: null
		},
		isMultiple: {
			type: Number,
			default: 0
		},
		isFilterable: {
			type: Number,
			default: 0
		},
		isOverflowHide: {
			type: Number,
			default: 0
		},
		// 自定义输入框宽度
		width: {
			type: [Number, String],
			default: '100%'
		},
		// 提示文字
		tips: {
			type: [Number, String],
			default: ''
		},
		// 渲染footer的插槽
		renderFooter: {
			type: Function,
			default: null
		},
		// select选项是通过字典配置
		dictionaryCode: {
			type: [String],
			default: ''
		},
		// 字典数据label和value映射对应关系
		dictionaryKeyMap: {
			type: Object,
			default: () => {
				return { label: 'dictTitle', value: 'dictValue' };
			}
		}
	},
	data() {
		return {
			asyncDataSource: null,
			footerVNode: null // 底部插槽vNode
		};
	},
	computed: {
		selectValue: {
			get() {
				if (this.isMultiple) {
					return this.data ? this.data.split(',') : [];
				} else {
					return this.data ?? '';
				}
			},
			set(value) {
				const data = this.isMultiple ? value.join(',') : value;
				this.$emit('update:data', data);
			}
		},
		optionKey() {
			const keyMap = this.optionKeyMap;
			return {
				label: keyMap ? keyMap.label || 'label' : 'label',
				value: keyMap ? keyMap.value || 'value' : 'value'
			};
		},
		clearable() {
			return this.$attrs?.clearable ?? true;
		}
	},
	async created() {
		this.dictionaryCode && this.getDictionaryByCode();
		// 绑定初始化options方法
		this.loadMethod && this.initAsyncOptions();
	},
	methods: {
		// 若数据是走异步接口
		async initAsyncOptions() {
			const data = await this.loadMethod();
			this.asyncDataSource = data || null;
		},
		// 通过数据字典获取数据
		async getDictionaryByCode() {
			try {
				const res = await request({
					url: `${this.dictionaryCode}`, // 配字典接口地址
					method: 'get'
				});
				this.asyncDataSource = res.data.map(i => {
					return {
						...i,
						label: i[this.dictionaryKeyMap.label],
						value: i[this.dictionaryKeyMap.value]
					};
				});
			} catch (e) {
				console.log(e);
			}
		}
	},
	render() {
		return (
			<div class="base-design-select-wrap">
				<el-select
					vModel={this.selectValue}
					{...{
						on: {
							...this.$listeners,
							change: value => {
								const options = this.asyncDataSource || this.options;
								const option = options.find(item => item.value === value);
								this.$emit('change', { value, option });
							}
						},
						props: this.$attrs
					}}
					multiple={this.isMultiple === 1}
					filterable={this.isFilterable === 1}
					class="base-design-select"
					clearable={this.clearable}
					style={{ width: this.width }}
					popper-append-to-body={false}
				>
					{(this.asyncDataSource || this.options).map(option => {
						return (
							<el-option
								key={option[this.optionKey.value]}
								value={option[this.optionKey.value]}
								label={option[this.optionKey.label]}
							/>
						);
					})}
				</el-select>
				{this.tips && <span class="select-tips">{this.tips}</span>}
				{this.renderFooter && this.renderFooter()}
			</div>
		);
	}
};
</script>

<style lang="scss">
.base-design-select-wrap {
	width: 100%;
	.base-design-select {
		.el-tag {
			max-width: 100%;
			.el-select__tags-text {
				display: inline-block;
				overflow: hidden;
				text-overflow: ellipsis;
				max-width: calc(100% - 17px);
			}
		}
		.select-tips {
			font-size: 12px;
			color: #666;
			line-height: 18px;
		}
	}
}
</style>
