<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2024-08-21 08:56:30
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-09-04 16:37:58
 * @FilePath: /coos-desktop-app/src/views/calendar/components/common-slot/index.vue
 * @Description:
-->
<template>
	<div class="common-slot">
		<div v-for="item in slotJson" :key="item.key" class="form-item">
			<div class="form-item-label">{{ item.label }}</div>
			<div class="form-item-component">
				<!--输入框-->
				<el-input
					v-if="item.type === 'input'"
					v-model="form[item.key]"
					clearable
					:placeholder="`请输入${item.label}`"
				/>
				<base-select
					v-else-if="item.type === 'select'"
					v-model="form[item.key]"
					:placeholder="`请选择${item.label}`"
					:options="item.options"
					:load-method="item.origin === 'api' ? () => commonSelectMethod(item) : null"
					@change="context => onSelectChange(context, item)"
				/>
				<el-date-picker
					v-else-if="item.type === 'timePicker'"
					v-model="form[item.key]"
					type="datetime"
					value-format="yyyy-MM-dd HH:mm:ss"
					:placeholder="`请选择${item.label}`"
				/>
				<template v-else-if="item.type === 'personSelect'">
					<el-button icon="el-icon-circle-plus-outline" @click="handleAddUser(item)">
						批量添加
					</el-button>
					<label v-if="form[item.key] && form[item.key].length">
						{{
							form[item.key]
								.slice(0, 2)
								.map(item => item.name)
								.join('、')
						}}等{{ form[item.key].length }}人
					</label>
				</template>
				<!--项目选择-->
				<template v-else-if="item.type === 'selectPage'">
					<template v-if="item.key === 'name'">
						<el-input v-model="form.name" readonly :placeholder="`请点击加号选择${item.label}`">
							<template slot="suffix">
								<div class="suffix" @click="handleShowBudgetSelect(item)">
									<i class="coos-iconfont suffix-icon icon-jiahao" />
								</div>
							</template>
						</el-input>
					</template>
					<template v-else>
						<el-input
							v-model="form.projectDictText"
							readonly
							:placeholder="`请点击加号选择${item.label}`"
						>
							<template slot="suffix">
								<div class="suffix" @click="handleShowProjectSelect(item)">
									<i class="coos-iconfont suffix-icon icon-jiahao" />
								</div>
							</template>
						</el-input>
					</template>
				</template>
			</div>
		</div>
		<!--协作人选择弹窗-->
		<orgPersonnelDialog
			title="选择协作人"
			need-all-data
			disable-all
			:init-values="addUser.users"
			:visible.sync="addUser.visible"
			:data-source="addUser.dataSource"
			:can-select-depart="false"
			@sure="handlePersonSure"
			@close="handlePersonClose"
		/>
		<!--项目选择弹窗-->
		<ProjectSelect
			ref="projectSelect"
			:slot-json="projectSelectDialog.slotJson"
			@sure="handleProjectSure"
		/>
		<!--费用科目选择弹窗-->
		<BudgetSelect
			ref="budgetSelect"
			:slot-json="budgetSelectDialog.slotJson"
			@sure="handleBudgetSure"
		/>
	</div>
</template>

<script>
import request from '@/utils/request';
import BaseSelect from './select';
import OrgPersonnelDialog from '@/components/org-personnel-dialog';
import ProjectSelect from './project-select';
import BudgetSelect from './budget-select';
import { isEmpty } from '@/utils';

export default {
	components: { BaseSelect, OrgPersonnelDialog, ProjectSelect, BudgetSelect },
	props: {
		slotJson: {
			type: Array,
			default: () => []
		},
		value: {
			type: Object,
			default: () => ({})
		}
	},
	data() {
		return {
			addUser: {
				visible: false,
				formKey: '',
				dataSource: ['user', 'depart']
			},
			projectSelectDialog: {
				visible: false,
				slotJson: {},
				formKey: ''
			},
			budgetSelectDialog: {
				visible: false,
				slotJson: {},
				formKey: ''
			}
		};
	},
	computed: {
		form: {
			get() {
				return this.value;
			},
			set(val) {
				this.$emit('input', val); // 之前用的$emit("update:value")是有问题的
			}
		}
	},
	watch: {
		slotJson: {
			deep: true,
			handler(newVal) {
				this.initForm();
			}
		}
	},
	created() {
		this.initForm();
	},
	methods: {
		initForm() {
			// 每次切换都要改变初始字段
			if (Object.values(this.form).filter(item => !isEmpty(item)).length === 0) {
				let form = {};
				this.slotJson.forEach(item => {
					form[item.key] = item.initValue || '';
					form[`${item.key}DictText`] = '';
				});
				this.form = form;
			}
		},
		async commonSelectMethod(item) {
			const { result } = await request({
				url: item.api,
				method: 'get'
			});
			return result;
		},
		handleAddUser(item) {
			this.addUser.visible = true;
			this.addUser.users = this.form[item.key]?.map(__ => ({ ...__, title: __.name }));
			this.addUser.formKey = item.key;
		},
		/**人员选择弹窗根据类型赋值*/
		handlePersonSure(arr) {
			this.form.meetPerson = arr
				.filter(item => item.id != 0 && item.dataType != 'org')
				.flatMap(item => {
					return {
						id: item.id,
						dataType: item.dataType,
						name: item.realname || item.title || item.name
					};
				});
			this.addUser.visible = false;
		},
		/**取消*/
		handlePersonClose() {
			this.addUser.visible = false;
		},
		// 监听下拉选项的值变更
		onSelectChange({ value, option }, item) {
			this.form[`${item.key}DictText`] = option.label;
		},
		// 项目选择弹窗
		handleShowProjectSelect(item) {
			this.projectSelectDialog.formKey = item.key;
			this.projectSelectDialog.slotJson = item;
			this.$refs.projectSelect.open({
				project: this.form.project,
				projectDictText: this.form.projectDictText
			});
			this.projectSelectDialog.visible = true;
		},
		// 项目选择控件确定事件
		handleProjectSure(obj) {
			this.form.project = obj.project;
			this.form.projectDictText = obj.projectDictText;
		},
		// 费用科目选择弹窗
		handleShowBudgetSelect(item) {
			this.budgetSelectDialog.formKey = item.key;
			this.budgetSelectDialog.slotJson = item;
			this.$refs.budgetSelect.open({
				name: this.form.name,
				nameDictText: this.form.nameDictText
			});
			this.budgetSelectDialog.visible = true;
		},
		// 费用科目选择控件确定事件
		handleBudgetSure(obj) {
			Object.keys(obj).forEach(key => {
				if (Object.prototype.hasOwnProperty.call(this.form, key)) {
					this.form[key] = obj[key];
				}
			});
		}
	}
};
</script>

<style lang="scss" scoped>
.form-item {
	width: 100%;
	display: flex;
	align-items: center;
	height: 40px;
	margin-top: 12px;
	&-label {
		width: 100px;
		height: 22px;
		font-size: 14px;
		color: #2f446b;
		line-height: 22px;
	}
	&-component {
		flex: 1 1 0;
		::v-deep {
			.el-input > .el-input__inner,
			.el-button,
			.el-range-editor.el-input__inner {
				height: 40px;
				border-radius: 6px;
				border: 1px solid #bccadb;
			}
			.el-range-separator {
				line-height: 32px;
			}
			.el-select {
				width: 360px;
				.el-input__prefix {
					left: 15px;
					display: flex;
					align-items: center;
				}
			}
			.el-input {
				.el-input__suffix {
					display: flex;
					align-items: center;
					right: 10px;
				}
			}
			.suffix {
				@include flexBox();
				width: 24px;
				height: 24px;
				border-radius: 3px;
				border: 1px solid $borderColor;
				cursor: pointer;
				&-icon {
					font-size: 18px;
				}
			}
		}
	}
}
</style>
