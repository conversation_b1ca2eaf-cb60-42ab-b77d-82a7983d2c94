<template>
	<div>
		<!--   参会人员   -->
		<!-- <el-row :gutter="12" class="body-item desk-el-form" style="margin-bottom: 8px">
			<el-col :span="1"></el-col>
			<el-col :span="1">
				<i class="coos-iconfont icon-pepole title-icon"></i>
			</el-col>
			<el-col :span="17">
				<div class="select-person-input" @click="openPersonSelect('must')">
					<div class="tabs">
						<div v-for="(item, index) of initMustValues" :key="index" class="tab">
							<div class="tab-icon">{{ item.title.slice(0, 1) }}</div>
							<div class="tab-name">{{ item.title }}</div>
							<i class="coos-iconfont icon-guanbi1 tab-close" @click.stop="delMust(index)"></i>
						</div>
						<div class="placeholder">添加必须参与人</div>
					</div>
					<div v-show="initMustValues.length" class="count">{{ initMustValues.length }}人</div>
				</div>
			</el-col>
			<el-col :span="5">
				<i class="el-icon-arrow-down next-icon"></i>
			</el-col>
		</el-row> -->
		<!--   参会人员   -->
		<!-- <el-row :gutter="12" class="body-item desk-el-form">
			<el-col :span="2"></el-col>
			<el-col :span="17">
				<div class="select-person-input" @click="openPersonSelect('nomust')">
					<div class="tabs">
						<div v-for="(item, index) of initValues" :key="index" class="tab">
							<div class="tab-icon">{{ item.title.slice(0, 1) }}</div>
							<div class="tab-name">{{ item.title }}</div>
							<i class="coos-iconfont icon-guanbi1 tab-close" @click.stop="del(index)"></i>
						</div>
						<div class="placeholder">添加可选参与人</div>
					</div>
					<div v-show="initValues.length" class="count">{{ initValues.length }}人</div>
				</div>
			</el-col>
		</el-row> -->
		<!--   参会地点   -->
		<el-row :gutter="6" class="body-item desk-el-form">
			<el-col :span="1"></el-col>
			<el-col :span="1">
				<i class="coos-iconfont icon-dingwei title-icon"></i>
			</el-col>
			<el-col v-for="(item, index) of form.room" :key="index" class="room-input" :span="8">
				<el-input v-model="item.title" placeholder="添加会议室地点"></el-input>
				<i
					class="coos-iconfont icon-cuowu-fullcopy metting-close-icon"
					@click="removeRoom(index)"
				></i>
			</el-col>
			<div class="add-button" @click="addRoom">
				<i class="el-icon-circle-plus-outline add-button-icon"></i>
				<span>添加会议室</span>
			</div>
		</el-row>
		<!--   添加视频会议   -->
		<el-row :gutter="6" class="body-item desk-el-form">
			<el-col :span="1"></el-col>
			<el-col :span="1">
				<i class="coos-iconfont icon-live title-icon"></i>
			</el-col>
			<el-col v-for="(item, index) of form.meetCode" :key="index" class="room-input" :span="8">
				<el-input v-model="item.code" placeholder="添加视频会议"></el-input>
				<i
					class="coos-iconfont icon-cuowu-fullcopy metting-close-icon"
					@click="removeMeet(index)"
				></i>
			</el-col>
			<div class="add-button" @click="addMeet">
				<i class="el-icon-circle-plus-outline add-button-icon"></i>
				<span>添加视频会议</span>
			</div>
		</el-row>
		<!--   添加会议描述   -->
		<el-row class="body-item desk-el-form" style="align-items: flex-start">
			<el-col :span="1"></el-col>
			<el-col :span="1" style="margin-top: 10px">
				<i class="coos-iconfont icon-text title-icon"></i>
			</el-col>
			<el-col :span="10">
				<el-input
					v-model="form.content"
					:rows="3"
					type="textarea"
					resize="none"
					placeholder="添加会议描述"
					:maxlength="100"
				></el-input>
				<!-- <div class="meeting-add">
					<div class="meeting-add-item">
						<i class="coos-iconfont icon-add title-icon"></i>
						添加议程
					</div>
					<div class="meeting-add-item">
						<i class="coos-iconfont icon-more1 title-icon"></i>
						添加会议模板
					</div>
				</div> -->
			</el-col>
		</el-row>
		<!--   我的日历   -->
		<!-- <el-row class="body-item desk-el-form">
			<el-col :span="1"></el-col>
			<el-col :span="1">
				<i class="coos-iconfont icon-calendar title-icon"></i>
			</el-col>
			<el-col :span="10">
				<el-select v-model="form.remindMode" placeholder="我的日历">
					<el-option
						v-for="item in tipTimeArr"
						:key="item.id"
						:label="item.itemText"
						:value="parseInt(item.itemValue)"
					></el-option>
				</el-select>
			</el-col>
		</el-row> -->
		<orgPersonnelDialog
			title="选择参会成员"
			:init-values="personSelectType === 'must' ? initMustValues : initValues"
			:disabled-ids="disabledIds"
			:visible="visible"
			need-all-data
			disable-all
			:data-source="dataSource"
			@sure="sure"
			@close="close"
		></orgPersonnelDialog>
	</div>
</template>

<script>
import orgPersonnelDialog from '@/components/org-personnel-dialog';
export default {
	name: 'Meeting',
	components: {
		orgPersonnelDialog
	},
	props: {
		dataJson: {
			type: String,
			default: () => {
				return '';
			}
		}
	},
	data() {
		return {
			// 人员选择的配置
			personSelectType: 'must', // 人员选择弹窗的类型
			dataSource: ['user', 'depart', 'label'],
			initMustValues: [], // 选择必须选择人员
			initValues: [], // 选择的人员
			visible: false,
			form: {
				meetCode: [], // 会议码
				content: '', // 会议描述
				room: [{ title: '' }] // 会议室数量
			}
		};
	},
	computed: {
		disabledIds() {
			let arr = this.personSelectType === 'must' ? this.initValues : this.initMustValues;
			return arr.map(item => item.id);
		}
	},
	watch: {
		dataJson(newVal) {
			this.init();
		}
	},
	mounted() {
		this.init();
	},
	methods: {
		/**人员选择弹窗根据类型赋值*/
		sure(arr) {
			if (this.personSelectType === 'must') {
				this.initMustValues = arr;
			} else {
				this.initValues = arr;
			}
			this.visible = false;
		},
		/**取消*/
		close() {
			this.visible = false;
		},
		/**删除可选选择人员*/
		del(index) {
			this.initValues.splice(index, 1);
		},
		/**删除必须选择人员*/
		delMust(index) {
			this.initMustValues.splice(index, 1);
		},
		/**打开弹窗*/
		openPersonSelect(type) {
			this.personSelectType = type;
			this.visible = true;
		},
		removeRoom(index) {
			this.form.room.splice(index, 1);
		},
		removeMeet(index) {
			this.form.meetCode.splice(index, 1);
		},
		init() {
			if (this.dataJson) {
				let data = JSON.parse(this.dataJson);
				Object.keys(this.form).forEach(key => {
					this.$set(this.form, key, data[key]);
				});
			}
		},
		/**添加会议*/
		addMeet() {
			if (this.form.meetCode.length > 1) {
				this.$message.warning('最多可以添加两个视频会议！');
				return;
			}
			this.form.meetCode.push({ code: '' });
		},
		/**添加会议室*/
		addRoom() {
			if (this.form.room.length > 1) {
				this.$message.warning('最多可以添加两个会议室！');
				return;
			}
			this.form.room.push({ title: '' });
		},
		/**为外部提供组件数据*/
		getData() {
			return JSON.stringify(this.form);
		}
	}
};
</script>

<style scoped lang="scss">
.body-item {
	margin-bottom: 20px;
	@include flexBox(flex-start);
	flex-wrap: wrap;
	.select-person-input {
		height: 40px;
		background: #ffffff;
		border-radius: 6px;
		border: 1px solid $borderColor;
		padding: 6px 11px;
		cursor: pointer;
		@include flexBox(space-between);
		.tabs {
			flex: 1;
			overflow: auto;
			@include flexBox(flex-start);
			scrollbar-width: none;
			&::-webkit-scrollbar {
				height: 0;
			}
			.tab {
				padding: 4px 10px;
				background: #f3f4f6;
				border-radius: 3px;
				margin-right: 8px;
				flex-shrink: 0;
				@include flexBox(flex-start);
				&-icon {
					width: 16px;
					height: 16px;
					background: var(--brand-6);
					border-radius: 3px;
					@include flexBox();
					font-size: 10px;
					font-weight: 400;
					color: #ffffff;
					line-height: 10px;
				}
				&-name {
					font-size: 12px;
					font-weight: 400;
					color: $textColor;
					line-height: 20px;
					margin: 0 4px;
				}
				&-close {
					font-size: 12px;
					line-height: 12px;
					cursor: pointer;
				}
			}
			.placeholder {
				flex-shrink: 0;
				font-size: 14px;
				font-weight: 400;
				color: $holderTextColor;
				line-height: 22px;
			}
		}
		.count {
			margin-left: 8px;
			flex-shrink: 0;
			font-size: 14px;
			font-weight: 400;
			color: $subTextColor;
			line-height: 22px;
		}
	}
	.room-input {
		position: relative;
		.metting-close-icon {
			position: absolute;
			right: -2px;
			top: -6px;
			font-size: 16px;
			cursor: pointer;
			color: $borderColor;
			z-index: 555;
		}
	}
	.title-icon {
		font-size: 20px;
		line-height: 20px;
		color: $textColor;
	}
	.add-button {
		padding: 9px 16px;
		background: #ffffff;
		border-radius: 6px;
		border: 1px solid $borderColor;
		@include flexBox();
		font-size: 14px;
		font-weight: 500;
		color: $primaryTextColor;
		line-height: 20px;
		cursor: pointer;
		margin-left: 3px;
		&-icon {
			font-size: 16px;
			line-height: 16px;
			margin-right: 4px;
		}
	}
}
.meeting-add {
	@include flexBox(space-between, center);
	height: 38px;
	width: 100%;
	border: 1px solid $borderColor;
	border-top: none;
	border-radius: 0 0 6px 6px;
	padding: 0 16px;
	&-item {
		@include flexBox(center, center);
		gap: 4px;
		cursor: pointer;
		font-family: PingFang SC, PingFang SC;
		font-weight: 500;
		font-size: 14px;
		color: $textColor;
		line-height: 22px;
	}
}
::v-deep .el-textarea__inner {
	border-radius: 6px 6px 0 0;
}
</style>
