<template>
	<div class="time-line" :style="{ top, height: `calc(100% - ${top})` }" @mousemove.prevent>
		<div class="item-left">
			<div v-for="(item, index) of 49" :key="index" class="time-line-menu">
				{{ index % 2 === 0 ? index / 2 + ':00' : (index - 1) / 2 + ':30' }}
			</div>
		</div>
		<div class="item-right">
			<div v-for="(item, index) of 49" :key="index" class="time-line-content">
				<div v-for="(block, i) of 7" :key="i" class="time-line-content-block">
					<div class="block"></div>
				</div>
			</div>
			<div
				v-for="(block, blockIndex) of eventMap"
				:key="'block' + blockIndex"
				class="event-block"
				:style="{
					zIndex: Math.ceil(block.startRow * 10),
					top: Math.min(block.startRow, block.endRow) * 50 + 'px',
					left: `calc(${block.startColumn} * (100% / 7))`,
					width:
						timeType === 'day'
							? `calc(${Math.abs(block.endColumn - block.startColumn) + 1} * (100% / 7) / 2 - 8px)`
							: `calc(${Math.abs(block.endColumn - block.startColumn) + 1} * (100% / 7) * 0.7)`,
					height: '25px'
				}"
				@click.stop.prevent="handleViewDetail(block)"
			>
				<div class="date-content" :class="getClass(block.type, 1)">
					<div class="label" :class="getClass(block.type, 2)">{{ block.type }}</div>
					<div class="content">{{ block.title || '无标题' }}</div>
				</div>
			</div>
			<div
				v-show="selectW > 0 || selectH > 0"
				class="select-block"
				:style="{
					top: startY + 'px',
					left: startX + 'px',
					width: selectW + 'px',
					height: selectH + 'px'
				}"
			></div>
		</div>

		<!--日程详情弹窗-->
		<CommonDetail
			:id="detailDialog.id"
			:show.sync="detailDialog.visible"
			:start-day="detailDialog.startDay"
			:schedule-type="1"
			@onUpdate="onUpdate"
		/>
	</div>
</template>

<script>
import { parseTime } from '@/utils';
import { delCalendarData, getCalendarDetail } from '@/api/modules/calendar';
import CommonDetail from '@/views/calendar/components/common-detail/index';

export default {
	name: 'TimeLine',
	components: {
		CommonDetail
	},
	props: {
		top: {
			type: String,
			default: () => {
				return '0';
			}
		},
		startDate: {
			type: Date,
			default: () => {
				return new Date();
			}
		},
		timeType: {
			type: String,
			default: () => {
				return 'week';
			}
		},
		// 日程数据
		calendarData: {
			type: Object,
			default: () => {
				return {};
			}
		},
		// 提醒时间集合
		tipTimeArr: {
			type: Array,
			default: () => {
				return [];
			}
		},
		// 提醒方式集合
		tipTypeArr: {
			type: Array,
			default: () => {
				return [];
			}
		},
		// 重复类型集合
		calendarArr: {
			type: Array,
			default: () => {
				return [];
			}
		}
	},
	data() {
		return {
			itemStartDay: null,
			detail: {},
			detailLoading: false,
			startRowIndex: 0, // 鼠标开始的模块索引
			startColumnIndex: 0, // 鼠标开始的模块索引
			startX: 0, // 鼠标开始前的位置
			startY: 0, // 鼠标开始前的位置
			selectW: 0, // 临时选中区域的宽高
			selectH: 0, // 临时选中区域的宽高
			event: [], // 框选结束选中的区域集合
			product: false,
			detailDialog: {
				id: '',
				visible: false,
				startDay: ''
			}
		};
	},
	computed: {
		// 日程事件
		eventMap() {
			let newArr = [];
			Object.values(this.calendarData).forEach(value => {
				value.forEach(data => {
					let { startTime, endTime, title, id, scheduleType, workMemoType, day } = data;
					let sTime = startTime.split(' ')[1].split(':');
					let startRow = parseInt(sTime[0]) / 0.5 + (sTime[1] > 30 ? 1 : 0);
					let eTime = endTime.split(' ')[1].split(':');
					let endRow = parseInt(eTime[0]) / 0.5 + (eTime[1] > 30 ? 2 : 1);
					let startColumn, endColumn;
					if (this.timeType === 'week') {
						startColumn = endColumn =
							new Date(day).getDay() - 1 < 0 ? 6 : new Date(day).getDay() - 1;
					} else {
						startColumn = 0;
						endColumn = 6;
					}
					// 是否有起点相同的叠加项
					newArr.forEach(item => {
						if (item.startRow === startRow && item.startColumn === startColumn) {
							startRow += 0.3;
							endRow += 0.4;
							startColumn += 0.05;
						}
					});
					newArr.push({
						startRow: startRow,
						endRow: endRow,
						startColumn,
						endColumn,
						startDay: startTime.split(' ')[0],
						startTime,
						endDay: endTime.split(' ')[0],
						endTime,
						title,
						type: scheduleType === 1 ? '日程' : workMemoType === 1 ? '委托' : '任务',
						id
					});
				});
			});
			return newArr;
		}
	},
	methods: {
		getClass(type, index) {
			switch (type) {
				case '日程': {
					return index === 1 ? 'date-content-richeng' : 'label-richeng';
				}
				case '委托': {
					return index === 1 ? 'date-content-weituo' : 'label-weituo';
				}
				case '任务': {
					return index === 1 ? 'date-content-renwu' : 'label-renwu';
				}
			}
		},
		openEdit() {
			this.$emit('openEdit', this.detail, this.itemStartDay);
		},
		/**获取详情*/
		getDetail(item) {
			this.itemStartDay = item.startDay;
			this.detailLoading = true;
			getCalendarDetail(item.id, { startDay: item.startDay }).then(res => {
				this.detailLoading = false;
				if (res.code === 200) {
					this.detail = res.result || {};
				} else {
					this.$message.error(res.message);
				}
			});
		},
		/**删除日程*/
		delEvent(id) {
			this.$confirm('确定删除吗?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			})
				.then(res => {
					delCalendarData(id).then(res => {
						if (res.code === 200) {
							this.$message.success('删除成功');
							this.$emit('update');
						} else {
							this.$message.error(res.message);
						}
					});
				})
				.catch(err => {});
		},
		/**框选区域新增日程*/
		mousedown(e, index, i) {
			this.startRowIndex = index;
			this.startColumnIndex = i;
			this.startX = e.layerX;
			this.startY = e.layerY;
			document.addEventListener('mousemove', this.mousemove);
		},
		mouseup(e, index, i) {
			this.startX = this.startY = this.selectW = this.selectH = 0;
			if (this.product) {
				this.event.push({
					startColumn: this.startColumnIndex,
					startRow: this.startRowIndex,
					endRow: index,
					endColumn: i,
					dateStart: parseTime(
						this.startDate.getTime() + this.startColumnIndex * 24 * 60 * 60 * 1000,
						'{y}-{m}-{d}'
					),
					timeStart:
						this.startRowIndex % 2 === 0
							? this.startRowIndex / 2 + ':00'
							: (this.startRowIndex - 1) / 2 + ':30',
					dateEnd:
						i > this.startColumnIndex
							? parseTime(this.startDate.getTime() + i * 24 * 60 * 60 * 1000, '{y}-{m}-{d}')
							: '',
					timeEnd:
						index > this.startRowIndex
							? index % 2 === 0
								? index / 2 + ':00'
								: (index - 1) / 2 + ':30'
							: ''
				});
			}
			this.product = false;
			document.removeEventListener('mousemove', this.mousemove);
		},
		mousemove(e) {
			this.selectW = e.layerX - this.startX;
			this.selectH = e.layerY - this.startY;
			this.product = true;
		},
		handleViewDetail(item) {
			this.detailDialog.id = item.id;
			this.detailDialog.visible = true;
			this.detailDialog.startDay = item.startDay;
		},
		// 监听日历详情的数据更新
		onUpdate() {
			this.$emit('onUpdate');
		}
	}
};
</script>

<style scoped lang="scss">
.time-line {
	height: 100%;
	width: 100%;
	position: absolute;
	left: 0;
	overflow-y: auto;
	padding: 10px 21px 0;
	@include flexBox(flex-start, flex-start);
	@include noScrollBar;
	.item-left {
		height: 100%;
		width: 40px;
	}
	.item-right {
		flex: 1;
		position: relative;
		.event-block {
			position: absolute;
			@include noScrollBar;
			.close {
				position: absolute;
				font-size: 16px;
				font-weight: 600;
				top: 5px;
				right: 5px;
				cursor: pointer;
				color: #ffffff;
			}
			.date-content {
				cursor: pointer;
				width: 100%;
				height: 24px;
				border-radius: 3px;
				padding: 0 4px;
				background: #ffffff;
				border: 1px solid #ffffff;
				margin-bottom: 3px;
				margin-right: 2px;
				@include flexBox();
				.label-weituo {
					color: #09bc9c;
					background: rgba(10, 186, 155, 0.1);
				}
				.label-renwu {
					color: #1890ff;
					background: rgba(24, 144, 255, 0.1);
				}
				.label-richeng {
					color: #6e5be3;
					background: rgba(110, 91, 227, 0.1);
				}
				.label {
					width: 32px;
					height: 16px;
					border-radius: 3px;
					margin-right: 4px;
					font-weight: 400;
					font-size: 12px;
					line-height: 12px;
					flex-shrink: 0;
					@include flexBox();
				}
				.content {
					font-weight: 400;
					font-size: 12px;
					color: $textColor;
					line-height: 20px;
					flex: 1;
					@include aLineEllipse;
				}
			}
			.date-content-weituo {
				border: 1px solid #9ce3d6;
			}
			.date-content-renwu {
				border: 1px solid #8ccbf8;
			}
			.date-content-richeng {
				border: 1px solid #8c90f8;
			}
			.time-title {
				font-size: 14px;
				color: rgb(28, 76, 186);
				@include aLineEllipse;
				&-text {
					color: rgb(28, 76, 186);
					margin-bottom: 8px;
				}
			}
			&-con {
				font-size: 14px;
				font-weight: 400;
				color: rgb(28, 76, 186);
				line-height: 22px;
				margin: 8px 0;
			}
		}
		.select-block {
			background: rgba(39, 120, 229, 0.3);
			position: absolute;
			border-radius: 4px;
		}
	}
	&-menu {
		flex-shrink: 0;
		width: 100%;
		height: 57px;
		@include flexBox(flex-start, flex-start);
		color: #c3c3c3;
		font-size: 12px;
		margin-top: -7px;
	}
	&-content {
		flex: 1;
		height: 50px;
		@include flexBox(flex-start);
		&-block {
			width: calc(100% / 7);
			padding-left: 8px;
			height: 100%;
			border-top: 1px solid #c3c3c3;
			.block {
				width: 100%;
				height: 100%;
			}
		}
	}
}
</style>
