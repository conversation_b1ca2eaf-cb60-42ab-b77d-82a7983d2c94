<template>
	<div>
		<el-dialog
			v-loading="submitLoading"
			width="720px"
			custom-class="calendar-update-dialog"
			append-to-body
			:close-on-click-modal="false"
			:title="title"
			:visible.sync="visible"
		>
			<div class="update-form">
				<div class="form-item">
					<div class="form-item-label">标题</div>
					<div class="form-item-component">
						<el-input v-model="form.content" placeholder="请添加标题" clearable></el-input>
					</div>
				</div>
				<div v-if="scheduleType === 1" class="form-item">
					<div class="form-item-label">时间</div>
					<div class="form-item-component">
						<el-date-picker
							v-model="form.date"
							type="datetimerange"
							range-separator="至"
							start-placeholder="开始日期"
							end-placeholder="结束日期"
							value-format="yyyy-MM-dd HH:mm:ss"
						/>
					</div>
				</div>
				<div v-if="scheduleType === 1" class="form-item">
					<div class="form-item-label">地点</div>
					<div class="form-item-component">
						<el-input v-model="form.address" placeholder="请输入地点" clearable></el-input>
					</div>
				</div>
				<div class="form-item">
					<div class="form-item-label">协作人</div>
					<div class="form-item-component">
						<el-button icon="el-icon-circle-plus-outline" @click="handleAddUser">
							批量添加
						</el-button>
						<label v-if="form.users && form.users.length">
							{{
								form.users
									.slice(0, 2)
									.map(item => item.name)
									.join('、')
							}}等{{ form.users.length }}人
						</label>
					</div>
				</div>
				<!--工作备忘提醒时间-->
				<div v-if="scheduleType === 2" class="form-item">
					<div class="form-item-label">提醒时间</div>
					<div class="form-item-component">
						<el-date-picker
							v-model="form.remindTime"
							type="datetime"
							placeholder=" 请选择提醒时间"
							value-format="yyyy-MM-dd HH:mm:ss"
						/>
					</div>
				</div>
				<!--日程提醒时间-->
				<div v-if="scheduleType === 1" class="form-item">
					<div class="form-item-label">提醒时间</div>
					<div class="form-item-component">
						<el-select v-model="form.remindMode" placeholder="请选择">
							<el-option
								v-for="item in tipTimeArr"
								:key="`remindMode_${item.itemValue}`"
								:label="item.itemText"
								:value="item.itemValue"
							/>
						</el-select>
					</div>
				</div>
				<div v-if="form.remindMode || form.remindTime" class="form-item">
					<div class="form-item-label">提醒方式</div>
					<div class="form-item-component">
						<el-select v-model="form.remindChannel" placeholder="请选择">
							<el-option
								v-for="item in tipTypeArr"
								:key="`remindChannel_${item.itemValue}`"
								:label="item.itemText"
								:value="item.itemValue"
							/>
						</el-select>
					</div>
				</div>
				<div v-if="scheduleType === 1" class="form-item">
					<div class="form-item-label">重复</div>
					<div class="form-item-component">
						<el-select v-model="form.repeatMode" :disabled="!!form.id" placeholder="请选择">
							<el-option
								v-for="item in calendarArr"
								:key="`repeatMode_${item.itemValue}`"
								:label="item.itemText"
								:value="item.itemValue"
							/>
						</el-select>
					</div>
				</div>
				<div class="form-item">
					<div class="form-item-label">优先级</div>
					<div class="form-item-component">
						<el-select v-model="form.priority" popper-class="priority-select" placeholder="请选择">
							<template #prefix>
								<span
									v-if="form.priority !== ''"
									class="select-option-circle"
									:style="{
										backgroundColor: priorityColor,
										width: '7px',
										height: '7px',
										display: 'inline-block',
										'border-radius': '100%'
									}"
								/>
							</template>
							<el-option
								v-for="item in priorityOptions"
								:key="`priority_${item.value}`"
								:label="item.label"
								:value="item.value"
							>
								<span
									class="select-option-circle"
									:style="{
										backgroundColor: item.color,
										width: '7px',
										height: '7px',
										display: 'inline-block',
										'border-radius': '100%'
									}"
								/>
								<label>{{ item.label }}</label>
							</el-option>
						</el-select>
					</div>
				</div>
				<!--工作性质选择-->
				<div v-if="scheduleType === 1 && themeArr.length > 1" class="form-item">
					<div class="form-item-label">工作性质</div>
					<div class="form-item-component">
						<el-select v-model="form.theme" placeholder="请选择">
							<el-option
								v-for="item in themeArr"
								:key="`theme_${item.accessCode}`"
								:label="item.accessName"
								:value="item.accessCode"
							/>
						</el-select>
					</div>
				</div>
				<!--公共费控管理插槽部分-->
				<common-slot
					v-if="calendarSlotJson.length > 0"
					v-model="form.extraJson"
					:slot-json="calendarSlotJson"
				/>
				<div v-if="scheduleType === 1" class="line"></div>
				<!--插槽部分-->
				<common-slot
					v-if="commonSlotJson.length > 0"
					v-model="form.slotForm"
					:slot-json="commonSlotJson"
				/>
			</div>
			<!--操作按钮-->
			<span slot="footer" class="dialog-footer">
				<el-button @click="visible = false">取消</el-button>
				<el-button type="primary" @click="beforeSave">确定</el-button>
			</span>
		</el-dialog>
		<tipPopup
			ref="delPopup"
			title="修改类型"
			cancel-text="修改"
			@handleButton="handleUpdateTipPopup"
		/>
		<orgPersonnelDialog
			title="选择协作人"
			need-all-data
			disable-all
			:init-values="addUser.users"
			:visible="addUser.visible"
			:data-source="addUser.dataSource"
			:can-select-depart="false"
			@sure="handleAddUserSure"
			@close="handleAddUserClose"
		/>
	</div>
</template>

<script>
import { saveCalendarData, upCalendarData, getCalendarSlotConfig } from '@/api/modules/calendar';
import { deepClone } from '@/utils';
import { getDictionary } from '@/utils/data-dictionary';
import TipPopup from '@/views/calendar/components/tip-popup';
import CommonSlot from '@/views/calendar/components/common-slot/index';
import OrgPersonnelDialog from '@/components/org-personnel-dialog';
export default {
	name: 'CommonUpdate',
	components: {
		OrgPersonnelDialog,
		TipPopup,
		CommonSlot
	},
	props: {
		show: {
			type: Boolean,
			default: false
		},
		// 主题类型
		topic: {
			type: Array,
			default: () => []
		},
		// 提醒时间集合
		tipTimeArr: {
			type: Array,
			default: () => []
		},
		// 提醒方式集合
		tipTypeArr: {
			type: Array,
			default: () => []
		},
		// 重复类型集合
		calendarArr: {
			type: Array,
			default: () => []
		},
		// 主题类型集合
		themeArr: {
			type: Array,
			default: () => []
		},
		detail: {
			type: Object,
			default: () => {
				return {};
			}
		},
		// 日程开始时间
		startDay: {
			type: String,
			default: () => {
				return '';
			}
		},
		// 1 日程 2工作备忘
		scheduleType: {
			type: Number,
			default: 1
		}
	},
	data() {
		return {
			submitLoading: false,
			/**备忘S*/
			memorandumForm: {
				id: '',
				priority: 0, // 优先级(0.一般、1.重要、2.紧急)
				content: '', // 备忘事项
				remindTime: '', // 提醒时间
				remindTimeDictText: '', // 提醒时间
				remindChannel: '', // 提醒方式
				users: [], // 人员选择
				priorityDictText: '一般',
				remindChannelDictText: '',
				usersDictText: [] // 基础人员选择的回显数据
			},
			/**备忘E*/
			/**日程S*/
			dayForm: {
				id: '',
				date: [], // 处理所用的时间区间
				slotForm: {}, // 处理所用的
				extraJson: {}, // 扩展数据
				content: '', // 日程事情
				priority: 0, // 优先级(0.一般、1.重要、2.紧急)
				priorityDictText: '一般',
				users: [], // 人员选择
				usersDictText: [], // 基础人员选择的回显数据
				workMemoType: '0', // 日程类型
				workMemoTypeDictText: '普通日程', // 日程类型文字
				address: '', // 日程地址
				location: '', // 地址的经纬度
				theme: '', // 主题
				themeDictText: '',
				startTime: '', // 日程开始时间
				endTime: '', // 日程结束时间
				startTimeObjDictText: {}, // 日程开始时间对象-特俗渲染
				endTimeObjDictText: {}, // 日程结束时间对象-特俗渲染
				startTimeDictText: '',
				endTimeDictText: '',
				remindMode: '', // 提醒时间
				remindModeDictText: '', // 提醒时间中文
				remindChannel: '', // 提醒方式
				remindChannelDictText: '', // 提醒方式中文
				repeatMode: 0, // 重复类型
				repeatModeDictText: '不重复' // 重复类型中文
			},
			form: {}, // 具体提交的数据
			resetForm: {}, // 重置为空的数据
			initForm: {}, // 初始数据，方便重置
			startDate: '', // 开始日期
			endDate: '', // 结束日期
			startTime: '', // 开始时间
			endTime: '', // 结束时间
			saveLoading: false,
			meetingCode: getDictionary('日程/会议'),
			addUser: {
				visible: false,
				users: [],
				userIds: [],
				dataSource: ['user', 'depart']
			},
			priorityOptions: [
				{
					label: '紧急',
					value: 2,
					color: '#FF4D4F'
				},
				{
					label: '重要',
					value: 1,
					color: '#ED7B2F'
				},
				{
					label: '一般',
					value: 0,
					color: '#618CDF'
				}
			],
			themeArray: [],
			calendarSlotJson: []
		};
	},
	computed: {
		visible: {
			get() {
				return this.show;
			},
			set(val) {
				this.$emit('update:show', val);
			}
		},
		title() {
			return `${this.form.id ? '编辑' : '新增'}${this.scheduleType === 1 ? '日程' : '工作备忘'}`;
		},
		// 渲染插槽的json数据
		commonSlotJson() {
			const obj = this.themeArr.find(item => {
				return item.accessCode === this.form.theme;
			});
			if (obj && obj.accessJson) {
				return JSON.parse(obj.accessJson).formConfig || [];
			}
			return [];
		},
		priorityColor() {
			return this.priorityOptions?.find(item => item.value === this.form.priority)?.color || '';
		}
	},
	watch: {
		// 主题类型的字典请求回来自动默认赋值
		topic: {
			deep: true,
			handler: function (newVal) {
				this.form.theme = !newVal.length ? '' : newVal[0].accessCode;
				this.initForm.theme = !newVal.length ? '' : newVal[0].accessCode;
				this.resetForm.theme = !newVal.length ? '' : newVal[0].accessCode;
			}
		},
		// 重复类型的字典请求回来自动默认赋值
		calendarArr: {
			deep: true,
			handler: function (newVal) {
				this.form.repeatMode = newVal[0].itemValue;
				this.initForm.repeatMode = newVal[0].itemValue;
				this.resetForm.repeatMode = newVal[0].itemValue;
			}
		},
		visible(newVal) {
			if (!newVal) {
				this.reset();
			} else {
				if (this.scheduleType === 1) {
					this.form = deepClone(this.dayForm);
					this.getCalendarSlotConfig();
				} else {
					this.form = deepClone(this.memorandumForm);
				}
				if (this.detail.id) {
					this.handleDetail();
				}
			}
		}
	},
	mounted() {
		this.resetForm = deepClone(this.form);
	},
	methods: {
		async getCalendarSlotConfig() {
			const { result } = await getCalendarSlotConfig();
			this.calendarSlotJson = result;
		},
		handleChangeRemind(val) {
			// 当提醒时间变更的时候，重置提醒方式为默认值
			if (val) {
				this.form.remindChannel = '';
			}
		},
		// 显示批量添加协作人弹窗
		handleAddUser() {
			this.addUser.userIds = this.form.users.map(item => item.id);
			this.addUser.users = this.form.users.map(item => ({
				...item,
				title: item.name
			}));

			this.addUser.visible = true;
		},
		/**人员选择弹窗根据类型赋值*/
		handleAddUserSure(arr) {
			this.form.users = arr
				.filter(item => item.id != 0 && item.dataType != 'org')
				.flatMap(item => {
					return {
						id: item.id,
						dataType: item.dataType,
						name: item.realname || item.title || item.name
					};
				});
			this.form.userIds = arr.map(item => item.id);
			this.addUser.visible = false;
		},
		/**取消*/
		handleAddUserClose() {
			this.addUser.visible = false;
		},
		/**重置*/
		reset() {
			this.startDate = this.endDate = this.startTime = this.endTime = '';
			this.form = deepClone(this.resetForm);
			this.form.users = [];
		},
		cancel() {
			this.visible = false;
		},
		beforeClose(done) {
			if (JSON.stringify(this.form) === JSON.stringify(this.initForm)) {
				done();
				return;
			}
			this.$confirm('内容未保存，确认关闭吗？', '提示', {
				confirmButtonText: '确认',
				showCancelButton: '取消',
				type: 'warning'
			})
				.then(() => {
					done();
				})
				.catch(() => {});
		},
		/**保存前判断是否是修改操作*/
		beforeSave() {
			// 编辑模式并且是重复性
			if (this.scheduleType === 1 && this.form.id && this.detail.repeatMode != 0) {
				this.$refs.delPopup.open();
			} else {
				this.handleUpdate();
			}
		},
		handleUpdateTipPopup(mode) {
			this.$refs.delPopup.cancel();
			this.handleUpdate(true, mode);
		},
		async handleDetail() {
			if (this.scheduleType === 2) {
				Object.keys(this.form).forEach(key => {
					this.form[key] = this.detail[key];
				});
			} else {
				Object.keys(this.form).forEach(key => {
					if (this.detail[key] !== undefined) {
						if (this.detail[key] === null) {
							this.form[key] = '';
						} else {
							this.form[key] = this.detail[key];
						}
					}
				});
				const { startTime, endTime } = this.detail;

				this.form.date = [startTime, endTime];

				// 有主题的话回显
				if (this.form.theme) {
					let obj = this.themeArr.find(item => {
						return item.accessCode === this.form.theme;
					});

					this.form.themeDictText = obj ? obj.accessName : '';
					this.form.slotForm = JSON.parse(this.detail.themeDataJson) || {};
				}
				// 有扩展数据的话回显
				this.form.extraJson = JSON.parse(this.detail.extraJson) || {};
			}

			this.initForm = deepClone(this.form);
		},
		/**保存数据*/
		handleUpdate(isSure, mode) {
			const form = {
				scheduleType: this.scheduleType,
				themeDataJson: ''
			};

			if (
				this.scheduleType === 1 &&
				this.form.remindChannel === '' &&
				![0, ''].includes(this.form.remindMode)
			) {
				return this.$message.warning('当选择了提醒时间时，必须选择提醒方式！');
			}
			if (this.form.id && this.scheduleType === 1 && this.form.repeatMode != 0 && !isSure) {
				this.$refs.delPopup.open();
				return;
			}
			this.submitLoading = true;
			// 过滤基础表单子字段
			Object.keys(this.form).forEach(key => {
				if (!/DictText$/.test(key)) {
					form[key] = this.form[key];
				}
			});
			// 仅日程有时间
			if (this.scheduleType === 1) {
				form.startDay = this.startDay;
				if (this.form.date?.length > 0) {
					const [startTime, endTime] = this.form.date;
					form.startTime = startTime;
					form.endTime = endTime;
				}
			}
			// 包含主题插槽
			if (this.form.theme) {
				form.themeDataJson = JSON.stringify(this.form.slotForm);
			}

			try {
				form.extraJson = JSON.stringify(this.form.extraJson);
			} catch (err) {
				form.extraJson = '';
				console.log(err);
			}
			const apiMap = {
				add: saveCalendarData,
				edit: upCalendarData
			};
			if (this.form.id) {
				// 编辑重复类型
				if (this.form.repeatMode != 0) {
					form.mode = mode;
				}
			}
			apiMap[this.form.id ? 'edit' : 'add'](form)
				.then(({ code, message, success }) => {
					if (code === 200 && success) {
						this.$message.success('操作成功');
						this.$emit('onUpdate');
						this.visible = false;
					} else {
						this.$message.warning(message || '操作失败');
					}
				})
				.catch(err => {
					console.log(
						'%c [  ]-553-「common-update」',
						'font-size:13px; background:pink; color:#bf2c9f;',
						err
					);
				})
				.finally(() => {
					this.submitLoading = false;
				});
		}
	}
};
</script>

<style scoped lang="scss">
.form-item {
	width: 100%;
	display: flex;
	align-items: center;
	height: 40px;
	margin-top: 12px;
	&-label {
		width: 100px;
		height: 22px;
		font-size: 14px;
		color: #2f446b;
		line-height: 22px;
	}
	&-component {
		flex: 1 1 0;
		::v-deep {
			.el-input > .el-input__inner,
			.el-button,
			.el-range-editor.el-input__inner {
				height: 40px;
				border-radius: 6px;
				border: 1px solid #bccadb;
			}
			.el-range-separator {
				line-height: 32px;
			}
			.el-select {
				width: 360px;
				.el-input__prefix {
					left: 15px;
					display: flex;
					align-items: center;
				}
			}
		}
	}
}
.line {
	border-top: 1px solid $lineColor;
	margin-top: 20px;
}
</style>

<style lang="scss">
.calendar-update-dialog {
	.el-dialog__body {
		min-height: 346px;
		max-height: calc(600px - 116px);
		padding: 20px 20px;
		overflow-y: auto;
		@include scrollBar;
	}
}
.priority-select {
	.el-select-dropdown__item {
		display: flex;
		align-items: center;
		label {
			margin-left: 10px;
		}
	}
}
</style>
