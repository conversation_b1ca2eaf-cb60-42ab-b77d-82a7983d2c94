<template>
	<el-dialog
		:title="title"
		:visible.sync="show"
		:modal-append-to-body="false"
		:append-to-body="true"
		class="desk-el-custom-dialog"
		top="0"
		width="40%"
	>
		<el-radio-group v-model="mode">
			<!--      1.所有日程,2.仅本次日程,3.本次及之后的所有日程-->
			<el-radio :label="1">所有日程</el-radio>
			<el-radio :label="2">仅本次日程</el-radio>
			<el-radio :label="3">本次及之后的所有日程</el-radio>
		</el-radio-group>
		<div class="footer">
			<div class="footer-cancel" @click="cancel">取消</div>
			<div class="footer-save" @click="handleButton">{{ cancelText }}</div>
		</div>
	</el-dialog>
</template>

<script>
export default {
	name: 'DelPopup',
	props: {
		title: {
			type: String,
			default: () => {
				return '删除类型';
			}
		},
		cancelText: {
			type: String,
			default: () => {
				return '删除';
			}
		}
	},
	data() {
		return {
			show: false,
			mode: 1
		};
	},
	methods: {
		open() {
			this.show = true;
		},
		cancel() {
			this.show = false;
		},
		handleButton() {
			this.show = false;
			this.$emit('handleButton', this.mode);
		}
	}
};
</script>

<style scoped lang="scss">
.footer {
	margin-top: 12px;
	width: 100%;
	height: 52px;
	padding-right: 6px;
	@include flexBox(flex-end);
	&-cancel {
		width: 68px;
		height: 36px;
		background: #ffffff;
		border-radius: 6px;
		border: 1px solid $borderColor;
		margin-right: 10px;
		cursor: pointer;
		font-size: 14px;
		font-weight: 400;
		color: $primaryTextColor;
		line-height: 36px;
		text-align: center;
	}
	&-save {
		width: 96px;
		height: 36px;
		background: var(--brand-6);
		border-radius: 6px;
		font-size: 14px;
		font-weight: 400;
		color: rgba(255, 255, 255, 0.9);
		line-height: 36px;
		text-align: center;
		cursor: pointer;
	}
}
</style>
