<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2024-08-22 11:34:18
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-08-27 09:49:18
 * @FilePath: /coos-desktop-app/src/views/calendar/components/common-detail/handler-process.vue
 * @Description:
-->
<template>
	<el-dialog class="workmemo-el-dialog" :visible.sync="visible" :title="title" @close="handleClose">
		<el-table
			style="width: 100%"
			max-height="502px"
			class="handler-table"
			:data="handlers"
			:cell-class-name="cellClassName"
			:row-style="{ height: '46px' }"
			:show-header="false"
		>
			<el-table-column width="180" label="姓名">
				<template slot-scope="{ row }">
					<div class="list">
						<div class="list-icon">
							{{ row.realName.slice(-1) }}
							<i v-if="row.isMark" class="status-icon">
								<svg-icon icon-class="success" class-name="completed-success-icon" />
							</i>
						</div>
						<div class="list-desc">{{ row.realName }}</div>
					</div>
				</template>
			</el-table-column>
			<el-table-column width="auto" label="状态" :formatter="statusFormatter"></el-table-column>
			<el-table-column label="时间">
				<template slot-scope="{ row }">
					<div>
						<span v-if="row.isMark">{{ row.createTime }}</span>
					</div>
				</template>
			</el-table-column>
		</el-table>
	</el-dialog>
</template>

<script>
export default {
	name: 'WorkMemo',
	props: {
		show: {
			type: Boolean,
			default: () => {
				return false;
			}
		},
		title: {
			type: String,
			default: () => {
				return '完成情况';
			}
		},
		handlers: {
			type: Array,
			default: () => {
				return [];
			}
		}
	},
	computed: {
		visible: {
			get() {
				return this.show;
			},
			set(val) {
				this.$emit('update:show', val);
			}
		}
	},
	methods: {
		handleClose() {
			// 处理关闭事件
			this.visible = false;
		},
		statusFormatter(row) {
			return row.isMark ? '已完成' : '未完成';
		},
		cellClassName({ row, column }) {
			let className = 'el-table_column';
			if (row.isMark && column.label == '状态') {
				className += ' completedStatus';
			} else if (!row.isMark && column.label == '状态') {
				className += ' uncompletedStatus';
			} else if (column.label == '时间') {
				className += ' completedTime';
			}
			return className;
		}
	}
};
</script>

<style scoped lang="scss">
.workmemo-el-dialog {
	display: flex;
	justify-content: center;
	align-items: center;
	::v-deep .el-dialog {
		width: 520px;
		max-width: 80vw;
		margin-top: 10vh !important;
		box-shadow: 0px 8px 10px rgba(0, 0, 0, 0.08), 0px 16px 24px rgba(0, 0, 0, 0.04),
			0px 6px 30px rgba(0, 0, 0, 0.05);
		border-radius: 16px;
		margin-bottom: 0;
	}
	::v-deep .el-dialog__header {
		margin: 12px 30px;
		padding: 10px 0;
		.el-dialog__headerbtn {
			top: 26px;
			right: 30px;
		}
		.el-dialog__title {
			font-size: 18px;
			font-weight: 700;
			color: #303133;
			line-height: 24px;
		}
	}
	::v-deep .el-dialog__body {
		padding: 10px 30px 20px;
		overflow: hidden;
		.el-dialog__content {
			font-size: 14px;
			color: #606266;
		}
	}
}

/**隐藏表头*/
::v-deep .el-table__header-wrapper {
	display: none;
}
::v-deep .el-table_column {
	padding: 0;
	.cell {
		padding: 0;
	}
}
.el-table__body-wrapper {
	display: flex;
	width: 100%;
	.el-table__body {
		display: flex;
		width: 100%;
	}
	.el-table__row {
		display: flex;
		width: 100%;
		.el-table__cell {
			display: flex;
			align-items: center;
			justify-content: center;
		}
	}
}
::v-deep .completedStatus {
	font-size: 13px;
	color: #40c274;
}
::v-deep .uncompletedStatus {
	font-size: 13px;
	color: #d1d3db;
}
::v-deep .completedTime {
	font-size: 12px;
	color: #737a94;
	span {
		float: right;
	}
}
.list {
	display: flex;
	@include flexBox(flex-start);
	&-icon {
		flex-shrink: 0;
		width: 30px;
		height: 30px;
		background: var(--brand-6);
		border-radius: $borderRadius;
		margin-right: 8px;
		font-size: 14px;
		font-weight: 500;
		color: #ffffff;
		line-height: 26px;
		@include flexBox();
		.status-icon {
			position: absolute;
			left: 23px;
			bottom: 8px;
			width: 10px;
			height: 10px;
			background-color: #40c274;
			border-radius: 50%;
			display: flex;
			align-items: center;
			justify-content: center;
			.completed-success-icon {
				width: 6px;
				height: 6px;
			}
		}
	}
	&-desc {
		flex: 1;
		font-size: medium;
		font-weight: 500;
		color: $primaryTextColor;
		line-height: 24px;
		@include aLineEllipse;
	}
}
</style>
