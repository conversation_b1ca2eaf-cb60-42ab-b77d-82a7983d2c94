<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2024-08-19 16:24:18
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-09-06 14:03:25
 * @FilePath: /coos-desktop-app/src/views/calendar/components/common-detail/index.vue
 * @Description:
-->
<template>
	<div>
		<el-dialog
			v-loading="dialogLoading"
			:title="title"
			:visible.sync="visible"
			custom-class="calendar-detail-dialog"
			append-to-body
		>
			<div class="base-info">
				<div class="title">
					<span class="type" :class="`type-${details.workMemoType}`">
						{{ { 0: '日程', 1: '委托', 2: '任务' }[details.workMemoType] }}
					</span>
					<label class="title-text">{{ details.content }}</label>
				</div>
				<template v-if="details.scheduleType == 1">
					<div class="cell">
						<div class="cell-label">
							<span class="coos-iconfont icon-shijian3"></span>
							<span class="cell-label-text">时间</span>
						</div>
						<div class="cell-content">{{ details.startTimeDesc }}至{{ details.endTimeDesc }}</div>
					</div>
					<div class="cell">
						<div class="cell-label">
							<span class="coos-iconfont icon-location"></span>
							<span class="cell-label-text">地点</span>
						</div>
						<div class="cell-content">{{ details.address ? details.address : '暂无' }}</div>
					</div>
				</template>
				<template v-if="!details.isOrigin">
					<div class="cell">
						<div class="cell-label">
							<span class="coos-iconfont icon-Friendsettings"></span>
							<span class="cell-label-text">
								{{ details.workMemoType == 2 && details.scheduleType == 1 ? '委托人' : '创建人' }}
							</span>
						</div>
						<div class="cell-content">{{ details.originCreateByName }}</div>
					</div>
				</template>
				<div v-if="!!Object.values(details.usersRealName || {}).length" class="cell">
					<div class="cell-label">
						<span class="coos-iconfont icon-yonghu"></span>
						<span class="cell-label-text">
							{{ details.workMemoType == 1 && details.scheduleType == 1 ? '办理人' : '协作人' }}
						</span>
					</div>
					<div class="cell-content handle-user">
						<span>{{ Object.values(details.usersRealName).join('、') || '暂无' }}</span>
						<span
							v-if="Object.values(details.usersRealName || {}).length > 0"
							class="finish-process"
						>
							<label>{{ details.handleAllNum - details.handleNotNum }}</label>
							<label class="color-1">
								/ {{ Object.values(details.usersRealName || {}).length || 0 }}
							</label>
							<label style="cursor: pointer" @click="handleViewFinishProcess">
								已完成
								<i class="el-icon-arrow-right color-1" style="margin-left: 12px"></i>
							</label>
						</span>
					</div>
				</div>
				<div class="cell">
					<div class="cell-label">
						<span class="coos-iconfont icon-calendar-copy-copy"></span>
						<span class="cell-label-text">类型</span>
					</div>
					<div class="cell-content">{{ details.scheduleType == 1 ? '日程' : '备忘' }}</div>
				</div>
				<div class="cell">
					<div class="cell-label">
						<span class="coos-iconfont icon-shijian3"></span>
						<span class="cell-label-text">创建时间</span>
					</div>
					<div class="cell-content">{{ details.createTime }}</div>
				</div>
				<div class="cell">
					<div class="cell-label">
						<span class="coos-iconfont icon-target"></span>
						<span class="cell-label-text">优先级</span>
					</div>
					<div class="cell-content priority">
						<span
							:style="{
								backgroundColor: { 2: '#FF4D4F', 1: '#ED7B2F', 0: '#618CDF' }[details.priority]
							}"
							class="circle"
						></span>
						<span
							:style="{
								color: { 2: '#FF4D4F', 1: '#ED7B2F', 0: '#618CDF' }[details.priority],
								flex: 1
							}"
							class="priority-text"
						>
							{{ { 2: '紧急', 1: '重要', 0: '一般' }[details.priority] }}
						</span>
					</div>
				</div>
				<div class="cell">
					<div class="cell-label">
						<span class="coos-iconfont icon-xiaoxizhongxin"></span>
						<span class="cell-label-text">提醒</span>
					</div>
					<div class="cell-content">
						{{
							details.scheduleType == 1
								? details.remindModeDictText || '不提醒'
								: details.remindTimeDesc || '不提醒'
						}}
					</div>
				</div>
				<!--重复性属性-->
				<template v-if="details.repeatMode && details.repeatModeDictText">
					<div class="cell">
						<div class="cell-label">
							<span class="coos-iconfont icon-loop"></span>
							<span class="cell-label-text">重复</span>
						</div>
						<div class="cell-content">{{ details.repeatModeDictText }}</div>
					</div>
					<div class="cell">
						<div class="cell-label">
							<span class="coos-iconfont icon-volume1"></span>
							<span class="cell-label-text">提醒方式</span>
						</div>
						<div class="cell-content">{{ details.remindChannelDictText || '不提醒' }}</div>
					</div>
				</template>
			</div>
			<!--更多信息-->
			<div v-if="details.extraJson" class="more">
				<div class="cell-title">扩展信息</div>
				<div v-for="item in extraJsonObj" :key="item.key" class="cell">
					<div class="cell-label">{{ item.label }}</div>
					<div class="cell-content">{{ formatSlotText(item, extraJsonData) || '暂未填写' }}</div>
				</div>
			</div>
			<!--更多信息-->
			<div v-if="details.theme" class="more">
				<div class="cell-title">更多信息</div>
				<div v-for="item in slotJsonObj" :key="item.key" class="cell">
					<div class="cell-label">{{ item.label }}</div>
					<div class="cell-content">{{ formatSlotText(item, slotJsonData) || '暂未填写' }}</div>
				</div>
			</div>
			<!--来源-->
			<div v-if="details.originMsg" class="source">
				<div class="cell-title">来源</div>
				<div class="source-content">{{ details.originMsg }}</div>
			</div>
			<!--操作按钮-->
			<div class="handle">
				<ul
					v-for="item in handleOptions"
					:key="item.value"
					class="handle-item"
					@click="
						() => {
							item.event();
						}
					"
				>
					<li class="handle-item-icon coos-iconfont" :style="`color:${item.color}`">
						{{ item.icon }}
					</li>
					<li class="handle-item-text" :style="`color:${item.color}`">{{ item.label }}</li>
				</ul>
			</div>
		</el-dialog>
		<!--重复性日程二次确认弹窗-->
		<el-dialog :title="'重复日程删除类型选择'" :visible.sync="repeatDialog.visible" append-to-body>
			<el-radio-group v-model="repeatDialog.selectType">
				<el-radio v-for="item in changeTypeArr" :key="item.value" :label="item.value">
					{{ item.label }}
				</el-radio>
			</el-radio-group>
			<span slot="footer" class="dialog-footer">
				<el-button @click="repeatDialog.visible = false">取消</el-button>
				<el-button type="primary" @click="handleDelete">确定</el-button>
			</span>
		</el-dialog>
		<CommonUpdate
			ref="commonUpdate"
			:show.sync="detailDialog.visible"
			:topic="topic"
			:tip-time-arr="tipTimeArr"
			:tip-type-arr="tipTypeArr"
			:calendar-arr="calendarArr"
			:theme-arr="themeArr"
			:start-day="detailDialog.startDay"
			:detail="detailDialog.detail"
			:schedule-type="scheduleType"
			@onUpdate="onUpdate"
		/>
		<HandlerProcess :show.sync="workMemoDialog.visible" :handlers="workMemoDialog.handlers" />
	</div>
</template>

<script>
import {
	getCalendarType,
	delCalendarData,
	markComplete,
	markCancelComplete,
	getCalendarDetail,
	getCalendarDirection,
	getCalendarSlotConfig
} from '@/api/modules/calendar';
import CommonUpdate from '@/views/calendar/components/common-update';
import HandlerProcess from './handler-process.vue';
import { mapGetters } from 'vuex';
import { getDictionary } from '@/utils/data-dictionary';

export default {
	components: { CommonUpdate, HandlerProcess },
	props: {
		show: {
			type: Boolean,
			default: false
		},
		id: {
			type: String,
			default: ''
		},
		startDay: {
			type: String,
			default: ''
		},
		scheduleType: {
			type: Number,
			default: 1
		}
	},
	data() {
		return {
			slotJsonObj: [],
			extraJsonObj: [],
			handleOptions: [],
			dialogLoading: false,
			details: {},
			dataJson: [],
			repeatDialog: {
				visible: false,
				selectType: '1'
			},
			changeTypeArr: [
				{
					label: '所有',
					value: '1'
				},
				{
					label: '本次',
					value: '2'
				},
				{
					label: '本次及之后的所有',
					value: '3'
				}
			],
			detailDialog: {
				detail: {}, // 详情内容
				visible: false,
				startDay: null // 点击数据的开始时间
			},
			topic: [], // 工作性质
			tipTimeArr: [], // 提醒时间集合
			tipTypeArr: [], // 提醒方式集合
			calendarArr: [], // 重复类型集合
			themeArr: [], // 主题集合
			workMemoDialog: {
				visible: false,
				handlers: []
			}
		};
	},
	computed: {
		...mapGetters(['rentThemValidate']),
		visible: {
			get() {
				return this.show;
			},
			set(val) {
				this.$emit('update:show', val);
			}
		},
		title() {
			return this.scheduleType === 1 ? '查看日程详情' : '查看工作备忘详情';
		},
		slotJsonData() {
			return JSON.parse(this.details?.themeDataJson || '{}');
		},
		extraJsonData() {
			return JSON.parse(this.details?.extraJson || '{}');
		}
	},
	watch: {
		async visible(val) {
			if (val) {
				await this.getDetails();
			}
		}
	},
	created() {
		if (this.scheduleType === 1) {
			this.getCalendarSlotConfig();
		}
		this.getDirection();
		this.getTheme();
	},
	methods: {
		async getCalendarSlotConfig() {
			const { result } = await getCalendarSlotConfig();
			this.extraJsonObj = result.map(item => ({
				label: item.label,
				value: item.key
			}));
		},
		/**获取下拉选择的数据字典*/
		getDirection() {
			getCalendarDirection('schedule_repeat,schedule_remind_mode,schedule_remind_channel').then(
				res => {
					if (res.code === 200) {
						let { schedule_repeat, schedule_remind_mode, schedule_remind_channel } = res.result;
						this.tipTimeArr = [{ itemText: '不提醒', itemValue: '' }].concat(
							schedule_remind_mode.map(item => ({
								...item,
								itemValue: Number(item.itemValue)
							}))
						); // 提醒时间集合
						this.tipTypeArr = [{ itemText: '不提醒', itemValue: '' }].concat(
							schedule_remind_channel.map(item => ({
								...item,
								itemValue: Number(item.itemValue)
							}))
						); // 提醒方式集合
						this.calendarArr = schedule_repeat.map(item => {
							item.itemValue = parseInt(item.itemValue);
							return item;
						}); // 重复类型集合
					} else {
						this.$message.error(res.message);
					}
				}
			);
		},
		/**获取主题数据*/
		async getTheme() {
			let params = {
				applicationId: getDictionary('应用ID/日历'),
				moduleCode: 'module:schedule',
				slotCode: 'slot:scheduleTheme'
			};
			let { code, result, message } = await getCalendarType(params);
			if (code === 200) {
				this.themeArr = [{ accessName: '请选择工作性质', accessCode: '' }].concat(result);
			} else {
				this.$message.error(message);
			}
		},
		getDetails() {
			this.dialogLoading = true;
			let params = {
				startDay: this.startDay || ''
			};
			getCalendarDetail(this.id, params)
				.then(res => {
					this.details = {
						...res.result,
						userDetails: []
					};
					setTimeout(() => {
						this.dialogLoading = false;
					}, 300);
					this.getMoreHandleOptions();
					this.calendarType();
					this.dataJson = JSON.parse(this.details.dataJson || '{}');
				})
				.catch(() => {
					this.dialogLoading = false;
				});
		},
		// 主题参数
		calendarType() {
			let params = {
				applicationId: getDictionary('应用ID/日历'),
				moduleCode: 'module:schedule',
				slotCode: 'slot:scheduleTheme'
			};
			getCalendarType(params).then(({ result }) => {
				const currentSlot = result.find(item => item.accessCode === this.details.theme);
				if (currentSlot) {
					const { accessJson } = currentSlot;
					const formConfig = JSON.parse(accessJson).formConfig;
					this.slotJsonObj = formConfig.map(item => ({
						label: item.label,
						value: item.key
					}));
				}
			});
		},
		// 获取更多操作的选项
		async getMoreHandleOptions() {
			// const params = {
			// 	applicationId: 'A1005',
			// 	moduleCode: 'module:schedule',
			// 	slotCode: 'slot:scheduleBusOpt'
			// };
			const allHandleOptions = [
				// {
				// 	label: '转派',
				// 	value: 'transfer',
				// 	icon: '\ue6ef',
				// 	event: (e) => {
				// 	}
				// },
				{
					label: '编辑',
					value: 'edit',
					icon: '\ue6d6',
					show: this.details.canUpdate,
					event: () => {
						this.updateItem();
					}
				},
				{
					label: '删除',
					value: 'delete',
					icon: '\ue6dd',
					show: this.details.canUpdate,
					event: () => {
						this.deleteItem();
					}
				},
				{
					label: this.details.isMark ? '标记为未完成' : '标记为完成',
					value: 'mark',
					icon: '\ue6c2',
					color: this.rentThemValidate.coosThemColor,
					event: e => {
						this.handleMark();
					}
				}
			];
			// 暂时取消插槽过来的操作按钮
			// const { result } = await getCalendarType(params);
			// result.forEach(item => {
			// 	const accessJson = JSON.parse(item.accessJson);
			// 	allHandleOptions.unshift({
			// 		label: item.accessName,
			// 		value: item.applicationId,
			// 		icon: accessJson.icon
			// 	});
			// });
			this.handleOptions = allHandleOptions.filter(item =>
				item.show !== undefined ? item.show : true
			);
		},
		formatSlotText(item, data) {
			const value = data[item.value];
			const dictText = data[`${item.value}DictText`];
			// 优化后的逻辑
			if (typeof value === 'string') {
				return dictText || value;
			}
			return value?.map(__ => __.name).join('、') || '-';
		},
		handleDelete() {
			delCalendarData(this.id, {
				mode: this.repeatDialog.selectType,
				startDay: this.details.startDay
			}).then(res => {
				if (res.code == 200 && res.success) {
					this.$emit('onUpdate');
					this.$message.success(res.message || '操作成功');
					this.repeatDialog.visible = false;
					this.visible = false;
				}
			});
		},
		handleMark() {
			if (!this.details.isMark) {
				markComplete(this.id, { startDay: this.startDay }).then(res => {
					if (res.code == 200 && res.success) {
						this.$emit('onUpdate');
						this.$message.success(res.message || '操作成功');
						this.getDetails();
					}
				});
			} else {
				markCancelComplete(this.id, { startDay: this.startDay }).then(res => {
					if (res.code == 200 && res.success) {
						this.$emit('onUpdate');
						this.$message.success(res.message || '操作成功');
						this.getDetails();
					}
				});
			}
		},
		deleteItem() {
			// 重复日程删除类型选择
			if (this.details.repeatMode > 0) {
				this.repeatDialog.visible = true;
			} else {
				// 二次确认
				this.$confirm(
					`确定删除该条${this.details.scheduleType == '1' ? '日程' : '工作备忘'}, 是否继续?`,
					'提示',
					{
						confirmButtonText: '确定',
						cancelButtonText: '取消',
						type: 'warning'
					}
				)
					.then(() => {
						this.handleDelete();
					})
					.catch(() => {
						this.$message({
							type: 'info',
							message: '已取消删除'
						});
					});
			}
		},
		updateItem() {
			if (this.details.canUpdate) {
				this.detailDialog.visible = true;
				this.detailDialog.detail = this.details;
				this.detailDialog.startDay = this.details.startDay || '';
			} else {
				this.$message.error('当前用户无权限编辑！');
			}
		},
		// 监听日历详情的数据更新
		onUpdate() {
			this.getDetails();
			this.$emit('onUpdate');
		},
		handleViewFinishProcess() {
			this.workMemoDialog.visible = true;
			this.workMemoDialog.handlers = this.details.handlers;
		}
	}
};
</script>

<style lang="scss" scoped>
.type {
	width: 34px;
	height: 20px;
	display: inline-block;
	border-radius: 3px 3px 3px 3px;
	text-align: center;
	&-0 {
		background: rgba(110, 91, 227, 0.1);
		color: #6e5be3;
	}
	&-1 {
		background: rgba(10, 186, 155, 0.1);
		color: #09bc9b;
	}
	&-2 {
		background: rgba(24, 144, 255, 0.1);
		color: #1890ff;
	}
}
.title {
	margin-bottom: 12px;
	&-text {
		height: 24px;
		font-weight: 500;
		font-size: 16px;
		color: $primaryTextColor;
		line-height: 24px;
		margin-left: 12px;
	}
}
.cell {
	margin-top: 10px;
	display: flex;
	align-items: flex-start;
	&-title {
		padding: 5px 0;
		height: 32px;
		font-weight: 500;
		font-size: 15px;
		color: #2f446b;
		line-height: 22px;
	}
	&-label {
		width: 84px;
		display: flex;
		align-items: center;
		.coos-iconfont {
			font-size: 16px;
		}
		&-text {
			height: 20px;
			font-size: 14px;
			color: $subTextColor;
			line-height: 22px;
			margin-left: 4px;
		}
	}
	&-content {
		flex: 1 1 0;
		font-size: 14px;
		color: #2f446b;
		line-height: 20px;
		margin-left: 8px;
		display: flex;
		align-items: flex-start;
		&.priority {
			align-items: center;
		}
		.finish-process {
			height: 22px;
			font-size: 14px;
			color: #3088ff;
			line-height: 22px;
			margin-left: 45px;
			flex-shrink: 0;
			.color-1 {
				color: #d1d3db;
			}
		}
		.circle {
			width: 5px;
			height: 5px;
			display: inline-block;
			border-radius: 100%;
			margin-right: 8px;
		}
		&.handle-user {
			display: flex;
			justify-content: space-between;
		}
	}
}
.more {
	margin-top: 20px;
}
.base-info + .more,
.base-info + .source {
	padding-top: 20px;
	margin-top: 20px;
	border-top: 1px solid $lineColor;
}
.source {
	&-content {
		margin-top: 8px;
		background: #f3f4f6;
		padding: 16px;
		border-radius: 6px;
		font-size: 14px;
		color: $primaryTextColor;
		line-height: 22px;
	}
}
.handle {
	border-top: 1px solid $lineColor;
	width: 100%;
	height: 76px;
	background-color: #fff;
	border-radius: 0 0 16px 16px;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20px 20px 12px;
	position: absolute;
	bottom: 0;
	left: 0;
	&-item {
		display: flex;
		align-items: center;
		padding: 3px 12px;
		margin: 8px 0;
		cursor: pointer;
		&-icon {
			font-size: 14px;
		}
		&-text {
			height: 22px;
			font-size: 14px;
			color: $primaryTextColor;
			line-height: 22px;
			margin-left: 5px;
		}
	}
}
</style>
<style lang="scss">
.calendar-detail-dialog {
	position: relative;
	.el-dialog__body {
		min-height: 346px;
		max-height: 546px;
		padding: 20px 20px 100px;
		overflow-y: auto;
		@include scrollBar;
	}
}
</style>
