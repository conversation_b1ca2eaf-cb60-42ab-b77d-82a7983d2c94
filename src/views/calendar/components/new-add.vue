<template>
	<el-dialog
		:title="type === 'newAdd' ? (!agendaOrMemo ? '新建日程' : '新建备忘') : '编辑日程'"
		:visible.sync="show"
		class="desk-el-custom-dialog"
		top="0"
		width="80%"
	>
		<!--    :before-close="beforeClose"-->
		<div class="body">
			<!--   日历标题   -->
			<div v-if="!hiddenSchedule" class="body-tab">
				<div
					class="body-tab-check"
					:style="{
						background: !agendaOrMemo ? '#ffffff' : '',
						color: !agendaOrMemo ? '#0F45EA' : '#737a94'
					}"
					@click="tabHandleClick(false)"
				>
					<i class="coos-iconfont icon-riqi1 title-icon"></i>
					普通日程
				</div>
				<div
					class="body-tab-check"
					:style="{
						background: agendaOrMemo ? '#ffffff' : '',
						color: agendaOrMemo ? '#0F45EA' : '#737a94'
					}"
					@click="tabHandleClick(true)"
				>
					<i class="coos-iconfont icon-tuwen title-icon"></i>
					工作备忘
				</div>
			</div>
			<div v-if="agendaOrMemo">
				<el-row :gutter="20" class="body-item desk-el-form">
					<el-col :span="1">
						<i class="coos-iconfont icon-calendar-copy-copy title-icon"></i>
					</el-col>
					<el-col :span="14">
						<el-input v-model="form.title" placeholder="添加备忘录标题"></el-input>
					</el-col>
					<el-col :span="9">
						<el-select v-model="form.workMemoType" clearable placeholder="请选择工作备忘录类型">
							<el-option
								v-for="item in workMemoTypeOpitions"
								:key="item.value"
								:label="item.label"
								:value="item.value"
							></el-option>
						</el-select>
					</el-col>
				</el-row>
				<el-row :gutter="8" class="body-item desk-el-form">
					<el-col :span="1">
						<i class="coos-iconfont icon-shijian3 title-icon"></i>
					</el-col>
					<el-col :span="5">
						<el-date-picker
							v-model="startDate"
							type="date"
							placeholder="选择日期"
							format="yyyy 年 MM 月 dd 日"
						></el-date-picker>
					</el-col>
					<el-col :span="4">
						<el-time-picker v-model="startTime" placeholder="--: --: --"></el-time-picker>
					</el-col>
					<el-col :span="1" style="text-align: center">至</el-col>
					<el-col :span="5">
						<el-date-picker
							v-model="endDate"
							type="date"
							placeholder="选择日期"
							format="yyyy 年 MM 月 dd 日"
						></el-date-picker>
					</el-col>
					<el-col :span="4">
						<el-time-picker v-model="endTime" placeholder="--: --: --"></el-time-picker>
					</el-col>
				</el-row>
				<!--   协作者   -->
				<el-row :gutter="12" class="body-item desk-el-form">
					<el-col :span="1">
						<i class="coos-iconfont icon-pepole title-icon"></i>
					</el-col>
					<el-col :span="17">
						<div class="select-person-input" @click="openPersonSelect('must')">
							<div class="tabs">
								<div v-for="(item, index) of initValues" :key="index" class="tab">
									<div class="tab-icon">{{ item.title.slice(0, 1) }}</div>
									<div class="tab-name">{{ item.title }}</div>
									<i class="coos-iconfont icon-guanbi1 tab-close" @click.stop="del(index)"></i>
								</div>
								<div class="placeholder">添加协作人</div>
							</div>
							<div v-show="initValues.length" class="count">{{ initValues.length }}人</div>
							<i class="el-icon-arrow-down next-icon"></i>
						</div>
					</el-col>
				</el-row>
				<!--   地点   -->
				<el-row :gutter="12" class="body-item desk-el-form">
					<el-col :span="1">
						<i class="coos-iconfont icon-dingwei title-icon"></i>
					</el-col>
					<el-col :span="17">
						<el-input v-model="form.address" placeholder="添加地点"></el-input>
					</el-col>
				</el-row>
			</div>
			<!-- <workMemo ref="schedule" :data-json="form.dataJson"></workMemo> -->
			<div v-else>
				<el-row :gutter="20" class="body-item desk-el-form">
					<el-col :span="1">
						<i class="coos-iconfont icon-calendar-copy-copy title-icon"></i>
					</el-col>
					<el-col :span="14">
						<el-input v-model="form.title" placeholder="添加日历标题"></el-input>
					</el-col>
					<!--					<el-col :span="9">-->
					<!--						<el-select v-model="form.theme" clearable placeholder="请选择人员日历主题">-->
					<!--							<el-option-->
					<!--								v-for="item in topic"-->
					<!--								:key="item.id"-->
					<!--								:label="item.accessName"-->
					<!--								:value="item.accessCode"-->
					<!--							></el-option>-->
					<!--						</el-select>-->
					<!--					</el-col>-->
				</el-row>
				<!--   日历时间   -->
				<el-row :gutter="8" class="body-item desk-el-form">
					<el-col :span="1">
						<i class="coos-iconfont icon-shijian3 title-icon"></i>
					</el-col>
					<el-col :span="5">
						<el-date-picker
							v-model="startDate"
							type="date"
							placeholder="选择日期"
							format="yyyy 年 MM 月 dd 日"
						></el-date-picker>
					</el-col>
					<el-col :span="4">
						<el-time-picker v-model="startTime" placeholder="--: --: --"></el-time-picker>
					</el-col>
					<el-col :span="1" style="text-align: center">至</el-col>
					<el-col :span="5">
						<el-date-picker
							v-model="endDate"
							type="date"
							placeholder="选择日期"
							format="yyyy 年 MM 月 dd 日"
						></el-date-picker>
					</el-col>
					<el-col :span="4">
						<el-time-picker v-model="endTime" placeholder="--: --: --"></el-time-picker>
					</el-col>
					<el-col :span="4">
						<el-select v-model="form.repeatMode" :disabled="type === 'edit'" placeholder="不重复">
							<el-option
								v-for="item in calendarArr"
								:key="item.id"
								:label="item.itemText"
								:value="item.itemValue"
							></el-option>
						</el-select>
					</el-col>
				</el-row>
				<!--   协作者   -->
				<el-row :gutter="12" class="body-item desk-el-form">
					<el-col :span="1">
						<i class="coos-iconfont icon-pepole title-icon"></i>
					</el-col>
					<el-col :span="17">
						<div class="select-person-input" @click="openPersonSelect('must')">
							<div class="tabs">
								<div v-for="(item, index) of initValues" :key="index" class="tab">
									<div class="tab-icon">{{ item.title.slice(0, 1) }}</div>
									<div class="tab-name">{{ item.title }}</div>
									<i class="coos-iconfont icon-guanbi1 tab-close" @click.stop="del(index)"></i>
								</div>
								<div class="placeholder">添加协作人</div>
							</div>
							<div v-show="initValues.length" class="count">{{ initValues.length }}人</div>
							<i class="el-icon-arrow-down next-icon"></i>
						</div>
					</el-col>
				</el-row>
				<!--   地点   -->
				<el-row :gutter="12" class="body-item desk-el-form">
					<el-col :span="1">
						<i class="coos-iconfont icon-dingwei title-icon"></i>
					</el-col>
					<el-col :span="17">
						<el-input v-model="form.address" placeholder="添加地点"></el-input>
					</el-col>
				</el-row>
				<!--   添加提醒   -->
				<!--				<el-row :gutter="8" class="body-item desk-el-form">-->
				<!--					<el-col :span="1">-->
				<!--						<i class="coos-iconfont icon-xiaoxizhongxin title-icon"></i>-->
				<!--					</el-col>-->
				<!--					<el-col :span="5">-->
				<!--						<el-select v-model="form.remindMode" placeholder="提醒时间">-->
				<!--							<el-option-->
				<!--								v-for="item in tipTimeArr"-->
				<!--								:key="item.id"-->
				<!--								:label="item.itemText"-->
				<!--								:value="parseInt(item.itemValue)"-->
				<!--							></el-option>-->
				<!--						</el-select>-->
				<!--					</el-col>-->
				<!--					<el-col :span="5">-->
				<!--						<el-select v-model="form.remindChannel" placeholder="提醒方式">-->
				<!--							<el-option-->
				<!--								v-for="item in tipTypeArr"-->
				<!--								:key="item.id"-->
				<!--								:label="item.itemText"-->
				<!--								:value="parseInt(item.itemValue)"-->
				<!--							></el-option>-->
				<!--						</el-select>-->
				<!--					</el-col>-->
				<!--				</el-row>-->
				<!--   动态表单   -->
				<div v-if="show">
					<!--   会议表单   -->
					<meeting
						v-if="form.theme === meetingCode"
						ref="schedule"
						:data-json="form.dataJson"
					></meeting>
				</div>
			</div>
		</div>
		<div class="footer">
			<div class="footer-cancel" @click="cancel">取消</div>
			<div v-loading="saveLoading">
				<div class="footer-save" @click="beforeSave">
					{{ !agendaOrMemo ? '提交日程' : '提交备忘' }}
				</div>
			</div>
		</div>
		<tipPopup ref="delPopup" title="修改类型" cancel-text="修改" @handleButton="save"></tipPopup>
		<orgPersonnelDialog
			title="选择协作人"
			:init-values="initValues"
			:visible="visible"
			need-all-data
			disable-all
			:data-source="dataSource"
			@sure="sure"
			@close="close"
		></orgPersonnelDialog>
	</el-dialog>
</template>

<script>
import { saveCalendarData, upCalendarData } from '@/api/modules/calendar';
import { deepClone, parseTime } from '@/utils';
import meeting from '@/views/calendar/components/meeting';
import { getDictionary } from '@/utils/data-dictionary';
import tipPopup from '@/views/calendar/components/tip-popup';
import orgPersonnelDialog from '@/components/org-personnel-dialog';
export default {
	name: 'NewAdd',
	components: {
		orgPersonnelDialog,
		meeting,
		tipPopup
	},
	props: {
		// 主题类型
		topic: {
			type: Array,
			default: () => {
				return [];
			}
		},
		// 提醒时间集合
		tipTimeArr: {
			type: Array,
			default: () => {
				return [];
			}
		},
		// 提醒方式集合
		tipTypeArr: {
			type: Array,
			default: () => {
				return [];
			}
		},
		// 重复类型集合
		calendarArr: {
			type: Array,
			default: () => {
				return [];
			}
		},
		detail: {
			type: Object,
			default: () => {
				return {};
			}
		},
		// 日程开始时间
		startDay: {
			type: String,
			default: () => {
				return '';
			}
		},
		// 隐藏日程 默认不隐藏
		hiddenSchedule: {
			type: Boolean,
			default: () => {
				return false;
			}
		}
	},
	data() {
		return {
			visible: false,
			initValues: [], // 协作者选择数据
			dataSource: ['user', 'depart', 'label'],
			type: 'newAdd', // 弹窗类型
			saveLoading: false, //动态效果
			show: false,
			// 表单
			form: {
				users: [],
				// userIds: [], // 必须人员
				// selUserIds: [], // 可选人员
				dataJson: '', // 动态表单的JSON拓展数据
				repeatMode: '', //是否重复
				title: '', // 日历标题
				theme: '', // 日历主题
				startTime: '', // 开始时间
				endTime: '', // 结束时间
				remindMode: '', // 提醒时间
				remindChannel: '', // 提醒方式
				workMemoType: '', //工作备忘录类型
				scheduleType: 1, //创建日历主题 默认为个人日程
				address: '',
				startTimeDesc: '', // 开始时间
				endTimeDesc: '' // 结束时间
			},
			resetForm: {}, // 重置为空的数据
			initForm: {}, // 初始数据，方便重置
			startDate: '', // 开始日期
			endDate: '', // 结束日期
			startTime: '', // 开始时间
			endTime: '', // 结束时间
			meetingCode: getDictionary('日程/会议'),
			agendaOrMemo: false, //日程false 备忘true
			workMemoTypeOpitions: [
				{
					value: 1,
					label: '我的委托'
				},
				{
					value: 2,
					label: '我的任务'
				}
			]
		};
	},
	watch: {
		// 主题类型的字典请求回来自动默认赋值
		topic: {
			deep: true,
			handler: function (newVal) {
				this.form.theme = !newVal.length ? '' : newVal[0].accessCode;
				this.initForm.theme = !newVal.length ? '' : newVal[0].accessCode;
				this.resetForm.theme = !newVal.length ? '' : newVal[0].accessCode;
			}
		},
		// 重复类型的字典请求回来自动默认赋值
		calendarArr: {
			deep: true,
			handler: function (newVal) {
				this.form.repeatMode = newVal[0].itemValue;
				this.initForm.repeatMode = newVal[0].itemValue;
				this.resetForm.repeatMode = newVal[0].itemValue;
			}
		},
		show(newVal) {
			if (!newVal) {
				this.reset();
			} else {
				this.initForm = deepClone(this.form);
			}
		}
	},
	mounted() {
		this.resetForm = deepClone(this.form);
	},
	methods: {
		/**删除必须选择人员*/
		del(index) {
			this.initValues.splice(index, 1);
		},
		/**人员选择弹窗根据类型赋值*/
		sure(arr) {
			this.initValues = arr;
			this.visible = false;
		},
		/**取消*/
		close() {
			this.visible = false;
		},
		/**打开弹窗*/
		openPersonSelect() {
			this.visible = true;
		},
		/**重置*/
		reset() {
			this.startDate = this.endDate = this.startTime = this.endTime = '';
			this.form = deepClone(this.resetForm);
			this.initValues = [];
		},
		cancel() {
			this.show = false;
			// this.beforeClose(() => {
			// 	this.show = false;
			// });
		},
		beforeClose(done) {
			if (JSON.stringify(this.form) === JSON.stringify(this.initForm)) {
				done();
				return;
			}
			this.$confirm('内容未保存，确认关闭吗？', '提示', {
				confirmButtonText: '确认',
				showCancelButton: '取消',
				type: 'warning'
			})
				.then(() => {
					done();
				})
				.catch(() => {});
		},
		/**保存前判断是否是修改操作*/
		beforeSave() {
			// 编辑模式并且是重复性
			if (this.type === 'edit' && this.detail.repeatMode != 0) {
				this.$refs.delPopup.open();
			} else {
				this.save();
			}
		},
		/**保存数据*/
		save(mode = '') {
			if (this.saveLoading) return;
			// 判断日期时间
			if (!this.startDate) return this.$message.error('请选择开始日期！');
			if (!this.endDate) return this.$message.error('请选择结束日期！');
			if (!this.form.title) return this.$message.error('请填写日程标题！');
			if (this.form.scheduleType == 2 && !this.form.workMemoType)
				return this.$message.error('请选择工作备忘类型！');
			this.saveLoading = true;
			// 组装日期时间
			this.form.startTime =
				parseTime(this.startDate, '{y}-{m}-{d}') +
				' ' +
				(this.startTime ? parseTime(this.startTime, '{h}:{i}:{s}') : '00:00:00');
			this.form.endTime =
				parseTime(this.endDate, '{y}-{m}-{d}') +
				' ' +
				(this.endTime ? parseTime(this.endTime, '{h}:{i}:{s}') : '23:59:59');
			// 获取动态表单的数据
			let ref = this.form.theme.split(':')[1]; // code为slot:type格式，我们只需要type
			if (this.$refs[ref]) {
				this.form.dataJson = this.$refs[ref].getData();
			}
			this.form.scheduleType = this.agendaOrMemo + 1; //隐式转化
			this.form.users = this.initValues.map(item => {
				return {
					dataType: item.dataType,
					id: item.id
				};
			});
			let params = deepClone(this.form);
			if (params.theme === 'other') {
				params.theme = '';
			}

			// 选择组织人员的id组装
			// this.form.userIds = this.initMustValues.map(item => {
			// 	return item.id;
			// });
			// this.form.selUserIds = this.initValues.map(item => {
			// 	return item.id;
			// });
			let FUNC;
			let DATA;
			if (this.type === 'newAdd') {
				FUNC = saveCalendarData;
				DATA = params;
			} else if (this.type === 'edit') {
				FUNC = upCalendarData;
				DATA = { ...params, id: this.detail.id, startDay: this.startDay || '', mode };
			}
			FUNC(DATA).then(res => {
				this.saveLoading = false;
				if (res.code === 200) {
					this.$emit('update');
					this.$message.success('操作成功！');
					this.show = false;
				} else {
					this.$message.error(res.message);
				}
			});
		},
		open(type, date, agendaOrMemoType) {
			this.type = type;
			this.startDate = date;
			this.endDate = date;
			if (type === 'edit') {
				Object.keys(this.form).forEach(key => {
					this.$set(this.form, key, this.detail[key]);
					// if (key === 'theme') {
					// 	// if (key === 'theme' && !this.detail[key]) {   暂时屏蔽 避免会议字段的影响
					// 	this.$set(this.form, 'theme', 'other');
					// } else {
					// 	this.$set(this.form, key, this.detail[key]);
					// }
				});
				this.agendaOrMemo = !(agendaOrMemoType - 2);
				this.startDate = new Date(this.detail.startTime);
				this.startTime = new Date(this.detail.startTime);
				this.endDate = new Date(this.detail.endTime);
				this.endTime = new Date(this.detail.endTime);
				this.initValues = this.detail.users
					? this.detail.users.map(item => {
							return {
								dataType: item.dataType,
								title: this.detail.usersRealName[item.id] || '未知',
								id: item.id
							};
					  })
					: [];
				// this.initValues = this.detail.selUserIds
				// 	? this.detail.selUserIds.map(item => {
				// 			return {
				// 				dataType: 'user',
				// 				title: this.detail.usersRealName[item],
				// 				id: item
				// 			};
				// 	  })
				// 	: [];
				// this.initMustValues = this.detail.userIds
				// 	? this.detail.userIds.map(item => {
				// 			return {
				// 				dataType: 'user',
				// 				title: this.detail.usersRealName[item],
				// 				id: item
				// 			};
				// 	  })
				// 	: [];
			}
			this.show = true;
		},
		tabHandleClick(bool) {
			this.agendaOrMemo = bool;
			this.reset(); // 切换时初始化form数据
		}
	}
};
</script>

<style scoped lang="scss">
.body {
	&-item {
		margin-bottom: 20px;
		@include flexBox(flex-start);
		flex-wrap: wrap;

		.room-input {
		}

		.title-icon {
			font-size: 20px;
			line-height: 20px;
			color: $textColor;
		}

		.select-person-input {
			height: 40px;
			background: #ffffff;
			border-radius: 6px;
			border: 1px solid $borderColor;
			padding: 6px 11px;
			cursor: pointer;
			@include flexBox(space-between);

			.tabs {
				flex: 1;
				overflow: auto;
				@include flexBox(flex-start);

				&::-webkit-scrollbar {
					height: 0;
				}

				.tab {
					padding: 4px 10px;
					background: #f3f4f6;
					border-radius: 3px;
					margin-right: 8px;
					flex-shrink: 0;
					@include flexBox(flex-start);

					&-icon {
						width: 16px;
						height: 16px;
						background: var(--brand-6);
						border-radius: 3px;
						@include flexBox();
						font-size: 10px;
						font-weight: 400;
						color: #ffffff;
						line-height: 10px;
					}

					&-name {
						font-size: 12px;
						font-weight: 400;
						color: $textColor;
						line-height: 20px;
						margin: 0 4px;
					}

					&-close {
						font-size: 12px;
						line-height: 12px;
						cursor: pointer;
					}
				}

				.placeholder {
					flex-shrink: 0;
					font-size: 14px;
					font-weight: 400;
					color: $holderTextColor;
					line-height: 22px;
				}
			}

			.count {
				margin-left: 8px;
				flex-shrink: 0;
				font-size: 14px;
				font-weight: 400;
				color: $subTextColor;
				line-height: 22px;
			}
		}

		.next-icon {
			font-size: 16px;
			line-height: 16px;
		}
	}

	.active-form {
	}

	&-tab {
		width: 197px;
		height: 34px;
		background: #f4f5f7;
		border-radius: 3px;
		margin: 22px 0 24px 0;
		padding: 5px 12px;
		@include flexBox(center);
		font-weight: 400;
		line-height: 20px;

		&-check {
			width: 88px;
			height: 24px;
			border-radius: 3px;
			@include flexBox(center);
			cursor: pointer;
		}
	}
}

.footer {
	width: 100%;
	height: 52px;
	padding-right: 6px;
	@include flexBox(flex-end);

	&-cancel {
		width: 68px;
		height: 36px;
		background: #ffffff;
		border-radius: 6px;
		border: 1px solid $borderColor;
		margin-right: 10px;
		cursor: pointer;
		font-size: 14px;
		font-weight: 400;
		color: $primaryTextColor;
		line-height: 36px;
		text-align: center;
	}

	&-save {
		width: 96px;
		height: 36px;
		background: var(--brand-6);
		border-radius: 6px;
		font-size: 14px;
		font-weight: 400;
		color: rgba(255, 255, 255, 0.9);
		line-height: 36px;
		text-align: center;
		cursor: pointer;
	}
}

::v-deep .el-col-1 {
	width: 24px;
	margin-right: 14px;
}
</style>
