<template>
	<div>
		<div v-loading="loading" class="detail">
			<div v-show="detail.isMark" class="detail-success">已完成</div>
			<div style="padding: 16px 16px 0 0">
				<div class="item">
					<div class="itemTitle" :style="{ color: !detail.isMark ? '#2f446b' : '#737a94' }">
						<span
							class="item-tag"
							:style="{
								color: detail.scheduleType === 1 ? '#6E5BE3' : '#09BC9B',
								backgroundColor: detail.scheduleType === 1 ? '#6E5BE326' : '#0ABA9B26'
							}"
						>
							{{ detail.scheduleType === 1 ? '日程' : detail.workMemoType === 1 ? '委托' : '任务' }}
						</span>
						{{ detail.title }}
					</div>
				</div>
				<div class="item">
					{{ detail.scheduleType === 1 ? '日程时间：' : '任务地点：' }}
					<div class="item-tagDesc">{{ tagTime }}</div>
				</div>
				<div class="item">
					{{ detail.scheduleType === 1 ? '日程地点：' : '任务地点：' }}

					<div class="item-tagDesc">{{ detail.address }}</div>
				</div>
				<div class="item" style="align-items: normal">
					<span style="margin-right: 14px; white-space: nowrap">
						{{ detail.scheduleType === 1 ? '协作人：' : '办理人：' }}
					</span>
					<div class="item-nameDesc">
						<div class="item-tagDesc">
							{{ Object.values(detail.usersRealName || {}).join('、') }}
						</div>
					</div>
				</div>
				<div class="item">
					创建时间：
					<div class="item-tagDesc">
						{{ detail.createTime || ''.split(' ')[0].replace(/-/g, '.') }}
					</div>
				</div>
				<div class="item">
					日程类型：
					<div class="item-tagDesc">{{ detail.scheduleType === 1 ? '普通日程' : '工作备忘' }}</div>
				</div>
				<div v-show="detail.scheduleType === 1" class="item">
					提醒方式：
					<div class="item-tagDesc">{{ Reminder(detail.repeatMode) }}</div>
				</div>
				<div class="item-source">
					<div class="item-source-title">
						<label>来源：</label>
						<!-- <div
							v-show="detail.scheduleType === 2"
							class="button-ground-item"
							style="margin: 0 0 0 auto"
							@click="jump()"
						>
							<i class="coos-iconfont icon-dangqianweizhi item-iconB"></i>
							<div class="button-text">定位到聊天</div>
						</div> -->
					</div>
					<div class="item-source-desc">
						{{
							detail.scheduleType === 1 ? `日程创建人：${detail.createByName}` : detail.originMsg
						}}
					</div>
				</div>
				<!-- <div v-show="false" class="item-more">
					<label>更多信息</label>
					<div class="item">
						日程主题：
						<div class="item-tagDesc">会议</div>
					</div>
					<div class="item">
						会议地点：
						<div class="item-tagDesc">成都市经信局2楼203会议室</div>
					</div>
					<div class="item">
						<span style="margin-right: 14px">参与人：</span>
						<div class="item-tagDesc">章三、李四、xxx</div>
					</div>
				</div> -->
				<div class="button-ground">
					<div class="button-ground-item" @click="markCompleteHandle()">
						<i v-if="detail.isMark" class="coos-iconfont icon-close_circle item-iconB"></i>
						<i v-else class="coos-iconfont icon-selected item-iconB"></i>
						<div class="button-text">{{ detail.isMark ? '标记为未完成' : '标记为完成' }}</div>
					</div>
					<div v-if="!detail.isMark" class="button-ground-item" @click="edit()">
						<svg-icon icon-class="edit" class="svg-icon"></svg-icon>
						<div class="button-text">编辑</div>
					</div>
					<div class="button-ground-item" @click="deleteItem()">
						<svg-icon icon-class="delete" class="svg-icon"></svg-icon>
						<div class="button-text">删除</div>
					</div>
				</div>
				<tip-popup ref="delPopup" @handleButton="del"></tip-popup>
			</div>
		</div>
	</div>
</template>

<script>
import { getKeyByValue } from '@/utils/data-dictionary';
import { delCalendarData, markComplete, markCancelComplete } from '@/api/modules/calendar';
import tipPopup from '@/views/calendar/components/tip-popup';
import { getThemColor } from './them-color';
//import { parseTime } from '@/utils';

export default {
	name: 'Detail',
	components: {
		tipPopup
	},
	props: {
		detail: {
			type: Object,
			default: () => {
				return {};
			}
		},
		loading: {
			type: Boolean,
			default: () => {
				return false;
			}
		},
		// 提醒方式集合
		tipTypeArr: {
			type: Array,
			default: () => {
				return [];
			}
		},
		// 日程开始时间
		startDay: {
			type: String,
			default: () => {
				return '';
			}
		}
	},
	data() {
		return {
			getThemColor
		};
	},

	computed: {
		tipType() {
			let obj = this.tipTypeArr.find(item => {
				return item.itemValue === this.detail.remindChannel;
			});
			return obj ? obj.itemText : '未设置提醒';
		},
		tagTime() {
			// const startDate = parseTime(this.detail.startTime, '{m}月{d}日 {h}:{i}') || '';
			// const endDate = parseTime(this.detail.endTime, '{m}月{d}日 {h}:{i}') || '';
			// return `${startDate.replace('0', '')}至${endDate.replace('0', '')}`;
			//替换为后端直接处理的日程时间段
			return this.detail.startTimeDesc + '至' + this.detail.endTimeDesc;
		}
	},
	methods: {
		Reminder(type) {
			switch (type) {
				case 0: {
					return '不重复';
				}
				case 1: {
					return '每天重复';
				}
				case 2: {
					return '每周一重复';
				}
				case 3: {
					return '每周五重复';
				}
				case 4: {
					return '每月重复';
				}
				case 5: {
					return '每个工作日重复';
				}
			}
		},
		getName(value) {
			return getKeyByValue(value);
		},
		jump() {
			this.$message.warning('功能开发中，敬请期待！');
		},
		/**删除按钮*/
		deleteItem() {
			if (this.detail.canUpdate) {
				// 重复性日程，选择删除的类型
				if (this.detail.repeatMode != 0) {
					this.$refs.delPopup.open();
				} else {
					this.$confirm('确定删除吗?', '提示', {
						confirmButtonText: '确定',
						cancelButtonText: '取消',
						type: 'warning'
					})
						.then(() => {
							this.del();
						})
						.catch(err => {});
				}
			} else {
				this.$message.error('当前用户无权限删除！');
			}
		},
		/**删除接口*/
		del(mode) {
			delCalendarData(this.detail.id, {
				startDay: this.startDay || '',
				mode: mode || ''
			}).then(res => {
				if (res.code === 200) {
					this.$message.success('删除成功');
					this.$emit('update');
				} else {
					this.$message.error(res.message);
				}
			});
		},
		edit() {
			if (this.detail.canUpdate) {
				this.$emit('openEdit', 'edit');
			} else {
				this.$message.error('当前用户无权限编辑！');
			}
		},
		markCompleteHandle() {
			let FUNC;
			if (this.detail.isMark) {
				FUNC = markCancelComplete;
			} else {
				FUNC = markComplete;
			}
			FUNC(this.detail.id, { startDay: this.detail.startDay }).then(res => {
				if (res.code === 200) {
					this.$message.success('操作成功');
					this.$emit('update');
				} else {
					this.$message.error(res.message);
				}
			});
		}
	}
};
</script>

<style scoped lang="scss">
// * {
// 	scrollbar-width: none !important;
// 	scrollbar-color: transparent !important;
// }
.detail {
	position: relative;
	overflow: hidden;

	&-success {
		position: absolute;
		height: 20px;
		background-color: #40c274;
		transform: rotate(45.09deg);
		font-family: PingFang SC, PingFang SC;
		font-weight: 400;
		font-size: 12px;
		color: #ffffff;
		line-height: 20px;
		right: -50px;
		top: 20px;
		width: 150px;
		z-index: 1;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	position: relative;
	min-width: 240px;
	max-width: 352px;

	& .itemTitle {
		width: 324px;
		font-weight: 800;
		font-size: 16px;
		color: $subTextColor;
		line-height: 24px;
		font-family: PingFang SC, PingFang SC;
	}

	&-title {
		font-size: 14px;
		font-weight: 500;
		color: $primaryTextColor;
		line-height: 22px;
	}

	&-time {
		font-size: 12px;
		font-weight: 400;
		color: $subTextColor;
		line-height: 20px;
		margin-top: 8px;
	}

	.item {
		@include flexBox(flex-start);
		font-family: PingFang SC, PingFang SC;
		font-weight: 400;
		font-size: 14px;
		color: $subTextColor;
		line-height: 22px;
		text-align: left;
		font-style: normal;
		text-transform: none;
		margin-top: 12px;

		&-source {
			margin-top: 24px;

			&-title {
				& label {
					font-weight: 800;
					font-size: 14px;
					color: $textColor;
					line-height: 22px;
					font-family: PingFang SC, PingFang SC;
				}

				display: flex;
				margin-bottom: 4px;
			}

			&-desc {
				width: 100%;
				background: #f3f4f6;
				border-radius: 6px;
				padding: 8px 0 8px 8px;
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				color: $primaryTextColor;
				line-height: 22px;
				background-color: #f3f4f6;
			}
		}

		&-more {
			margin: 19px 0 13px 0;

			& label {
				font-family: PingFang SC, PingFang SC;
				font-weight: 800;
				font-size: 14px;
				color: $textColor;
				line-height: 22px;
			}
		}

		&-icon {
			font-size: 14px;
			line-height: 14px;
			margin-right: 2px;
		}

		&-tip {
			width: 6px;
			height: 6px;
			border-radius: 50%;
			margin-right: 5px;
		}

		&-title {
			font-size: 12px;
			font-weight: 400;
			color: $subTextColor;
			line-height: 20px;
			width: 52px;
		}

		&-avatar {
			width: 16px;
			height: 16px;
			background: var(--brand-6);
			border-radius: 3px;
			margin-right: 3px;
			font-size: 10px;
			font-weight: 400;
			color: #ffffff;
			line-height: 16px;
			text-align: center;
		}

		&-avatarB {
			width: 24px;
			height: 24px;
			background: var(--brand-6);
			border-radius: 6px;
			font-size: 12px;
			font-weight: 400;
			color: #ffffff;
			line-height: 20px;
			display: flex;
			align-items: center;
			justify-content: center;
		}

		&-name {
			font-size: 12px;
			font-weight: 400;
			color: $primaryTextColor;
			line-height: 20px;
		}

		&-nameB {
			font-size: 14px;
			font-weight: 400;
			color: $textColor;
			line-height: 22px;
			margin-left: 4px;
		}

		&-tag {
			display: inline-block;
			width: 40px;
			height: 22px;
			border-radius: 3px;
			font-weight: 400;
			font-size: 12px;
			color: #09bc9b;
			line-height: 22px;
			background: rgba(10, 186, 155, 0.1);
			text-align: center;
			white-space: nowrap;
			font-family: PingFang SC, PingFang SC;
		}

		&-nameDesc {
			max-height: 150px;
			overflow: auto;
			scrollbar-width: none !important;

			&-tagDesc {
				margin-left: 8px;
			}

			&::-webkit-scrollbar {
				width: 0 !important;
				height: 0;
			}
		}

		&-iconB {
			width: 16px;
			height: 16px;
			margin-right: 4px;
			display: flex;
			align-items: center;
		}
	}

	.button-ground {
		margin-top: 17px;
		height: 37px;
		border-top: 1px solid #f0f0f0;
		@include flexBox(space-between, center);

		&-item {
			cursor: pointer;
			margin: 0 21px 0 11px;
			@include flexBox();

			.svg-icon {
				width: 16px;
				height: 16px;
				margin-right: 4px;
			}

			.button-text {
				font-size: 14px;
				font-weight: 400;
				color: $primaryTextColor;
				line-height: 22px;
			}
		}
	}
}
</style>
