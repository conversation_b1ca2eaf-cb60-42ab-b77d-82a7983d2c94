<template>
	<div class="calendar">
		<div class="calendar-content">
			<div class="calendar-content-left">
				<div class="calendar-content-left-date">{{ time }}</div>
				<el-calendar ref="leftCalendar" v-model="selectDate" class="leftCalendar">
					<template slot="dateCell" slot-scope="{ data }">
						<div class="date">
							<div class="date-con">
								<span v-if="new Date().toDateString() === new Date(data.day).toDateString()">
									今
								</span>
								<span v-else>
									{{ data.day.split('-')[data.day.split('-').length - 1].replace(/^0/gi, '') }}
								</span>
							</div>
						</div>
					</template>
				</el-calendar>
				<div class="myCon">日程类型</div>
				<div class="detail">
					<el-checkbox-group v-model="typesName" value-type="string">
						<el-checkbox
							v-for="item in types"
							:key="item.typeName"
							:label="item.typeValue"
							:class="[typesName.includes(item.typeValue) ? `checked_${item.typeValue}` : '']"
						>
							{{ item.typeName }}
						</el-checkbox>
					</el-checkbox-group>
				</div>
				<div class="myCon">日程主题</div>
				<div class="detail">
					<el-checkbox-group v-model="filterThemeCode" value-type="string">
						<el-checkbox
							v-for="item in filterThemeArray"
							:key="item.accessCode"
							:label="item.accessCode"
							class="theme-checkbox"
						>
							{{ item.accessName }}
						</el-checkbox>
					</el-checkbox-group>
				</div>
			</div>
			<div class="calendar-content-right">
				<div class="calendar-content-right-title">
					<div class="left">
						<el-button type="primary" style="cursor: pointer" @click="changeDate('current')">
							今天
						</el-button>
						<div class="left-pre" @click="changeDate('prev')">
							<svg-icon icon-class="seal-left"></svg-icon>
						</div>
						<div class="left-next" @click="changeDate('next')">
							<svg-icon icon-class="next"></svg-icon>
						</div>
						<div class="left-time">{{ time }}</div>
					</div>
					<div class="right">
						<div
							class="right-item"
							:class="{ select: timeType === 'day' }"
							@click="selectTimeType('day')"
						>
							日
						</div>
						<div
							class="right-item"
							:class="{ select: timeType === 'week' }"
							@click="selectTimeType('week')"
						>
							周
						</div>
						<div
							class="right-item"
							:class="{ select: timeType === 'mouth' }"
							@click="selectTimeType('mouth')"
						>
							月
						</div>
					</div>
				</div>
				<el-calendar
					v-show="timeType !== 'day'"
					ref="rightCalendar"
					v-model="selectDate"
					v-loading="loading"
					v-bind="options"
					class="rightCalendar"
					:style="
						timeType === 'mouth' ? 'width: 100%' : 'width: calc(100% - 30px);margin-left: 30px;'
					"
				>
					<template slot="dateCell" slot-scope="{ data }">
						<div
							class="date"
							:class="{ 'date-select': data.isSelected, 'date-disable': timeType !== 'week' }"
						>
							<div class="date-num">
								{{ data.day.split('-')[data.day.split('-').length - 1].replace(/^0/gi, '') }}
							</div>
							<div v-for="(item, index) of calendarData[data.day]" :key="index">
								<div class="date-content" @click.stop.prevent="handleViewDetail(item)">
									<div
										class="date-content-icon"
										:style="{ background: item.scheduleType == 1 ? '' : '#26C88E' }"
									/>
									<div
										class="date-content-text"
										:class="{
											deledeLine: item.isMark
										}"
									>
										{{ item.title || '无标题' }}
									</div>
								</div>
							</div>
						</div>
					</template>
				</el-calendar>
				<timeLine
					v-show="timeType !== 'mouth'"
					:key="timeType"
					v-loading="timeType === 'day' && loading"
					:calendar-data="calendarData"
					:time-type="timeType"
					:tip-time-arr="tipTimeArr"
					:tip-type-arr="tipTypeArr"
					:calendar-arr="calendarArr"
					:start-date="range[0]"
					:top="timeType === 'day' ? '80px' : '160px'"
					@onUpdate="onUpdate"
					@openEdit="updateEdit"
				></timeLine>
			</div>
		</div>
		<CommonUpdate
			ref="commonUpdate"
			:show.sync="updateDialog.visible"
			:topic="topic"
			:tip-time-arr="tipTimeArr"
			:tip-type-arr="tipTypeArr"
			:calendar-arr="calendarArr"
			:theme-arr="themeArr"
			:start-day="itemStartDay"
			:detail="detail"
			@onUpdate="onUpdate"
		/>
		<!--日程详情弹窗-->
		<CommonDetail
			:id="detailDialog.id"
			:show.sync="detailDialog.visible"
			:start-day="detailDialog.startDay"
			:schedule-type="1"
			@onUpdate="onUpdate"
		/>
	</div>
</template>

<script>
import TimeLine from '@/views/calendar/components/time-line';
import CommonUpdate from '@/views/calendar/components/common-update';
import CommonDetail from '@/views/calendar/components/common-detail/index';
import { getThemColor } from '../them-color';
import { getCalendarDirection, getCalendarList, getCalendarType } from '@/api/modules/calendar';
import { deepClone, parseTime } from '@/utils';
import { getDictionary } from '@/utils/data-dictionary';
export default {
	components: {
		TimeLine,
		CommonUpdate,
		CommonDetail
	},
	data() {
		return {
			getThemColor,
			loading: false,
			calendarData: {}, // 日程数据
			filterCode: [], // 筛选显示的日程主题
			today: new Date(), // 今天
			selectDate: new Date(), // 双向绑定选中的日期
			timeType: 'mouth', // 展示日期类型
			range: [], // 日历范围
			types: [
				{
					typeName: '我的日程',
					typeValue: 0,
					color: '#3586FF'
				},

				{
					typeName: '我的委托(我委托给别人完成)',
					typeValue: 1,
					color: '#27CDB0'
				},
				{
					typeName: '我的任务(别人委托给我完成)',
					typeValue: 2,
					color: '#8B7CEB'
				}
			], // 日程类型
			typesName: [0, 1, 2], // 筛选显示的日程类型
			topic: [], // 日程主题
			detail: {}, // 详情内容
			itemStartDay: null, // 点击数据的开始时间
			detailLoading: false,
			tipTimeArr: [], // 提醒时间集合
			tipTypeArr: [], // 提醒方式集合
			calendarArr: [], // 重复类型集合
			themeArr: [], // 主题集合
			filterThemeArray: [], // 用于筛选使用
			filterThemeCode: [], // 筛选主题
			clickDetail: false,
			time: '', // 当前选中年月的回显
			detailDialog: {
				id: '',
				visible: false,
				startDay: ''
			},
			updateDialog: {
				visible: false
			}
		};
	},
	computed: {
		options() {
			let obj = {};
			if (this.range.toString()) {
				obj.range = this.range;
			}
			return obj;
		}
	},
	watch: {
		/**选择日期*/
		selectDate(newVal) {
			let date = new Date(newVal);
			let time = date.getFullYear() + '年' + (date.getMonth() + 1) + '月' + date.getDate() + '日';
			if (this.time === time) return;
			this.time = time;
			// this.timeType === 'week' && this.computedRang();
			// let keys = date.toLocaleDateString().split('/');
			// keys = keys.map(key => {
			// 	if (key.length < 2) {
			// 		key = '0' + key;
			// 	}
			// 	return key;
			// });
			// if (this.calendarData[keys.join('-')]) return; // 如果数据存在就不请求
			!this.clickDetail && this.selectTimeType(this.timeType);
			setTimeout(() => {
				this.clickDetail = false;
			}, 0);
		},
		/**监听主题类型变化，请求不同的数据*/
		filterThemeCode: {
			deep: true,
			handler: function (newVal) {
				this.getData();
			}
		},
		typesName: {
			deep: true,
			handler: function (newVal) {
				if (newVal.length === 0) {
					this.calendarData = {};
				} else {
					this.getData();
				}
			}
		}
	},
	mounted() {
		this.time =
			this.today.getFullYear() +
			'年' +
			(this.today.getMonth() + 1) +
			'月' +
			this.today.getDate() +
			'日';
		this.getTopic();
		this.getDirection(); // 获取字典数据
		this.getTheme();
	},
	methods: {
		/**全局搜索进入详情*/
		openDetail(options) {
			if (options.startDay) {
				this.selectDate = new Date(options.startDay);
			}
			if (options.id) {
				this.handleViewDetail(options);
			}
		},
		/**获取下拉选择的数据字典*/
		getDirection() {
			getCalendarDirection('schedule_repeat,schedule_remind_mode,schedule_remind_channel').then(
				res => {
					if (res.code === 200) {
						let { schedule_repeat, schedule_remind_mode, schedule_remind_channel } = res.result;
						this.tipTimeArr = [{ itemText: '不提醒', itemValue: '' }].concat(
							schedule_remind_mode.map(item => ({
								...item,
								itemValue: Number(item.itemValue)
							}))
						); // 提醒时间集合
						this.tipTypeArr = [{ itemText: '不提醒', itemValue: '' }].concat(
							schedule_remind_channel.map(item => ({ ...item, itemValue: Number(item.itemValue) }))
						); // 提醒方式集合
						this.calendarArr = schedule_repeat.map(item => {
							item.itemValue = parseInt(item.itemValue);
							return item;
						}); // 重复类型集合
					} else {
						this.$message.error(res.message);
					}
				}
			);
		},
		/**获取数据列表*/
		getData() {
			this.calendarData = {};
			this.loading = true;
			let startDay = this.range[0] ? this.range[0] : this.selectDate;
			startDay = parseTime(startDay, '{y}-{m}-{d}');
			let endDay = this.range[1] ? parseTime(this.range[1], '{y}-{m}-{d}') : '';

			let theme =
				this.filterThemeCode.length < this.filterThemeArray.length
					? this.filterThemeCode.join(',')
					: ''; // 全部选中就传空

			getCalendarList({ startDay, endDay, theme, workMemoTypeSel: this.typesName.join(',') }).then(
				res => {
					this.loading = false;
					if (res.code === 200) {
						// if (this.filterCode.length !== 0) {
						this.calendarData = res.result;
						// }
					} else {
						this.$message.error(res.message);
					}
				}
			);
		},
		/**筛选主题*/
		filterTopic(accessCode) {
			if (this.filterCode.includes(accessCode)) {
				this.filterCode = this.filterCode.filter(item => {
					return item !== accessCode;
				});
			} else {
				this.filterCode.push(accessCode);
			}
		},
		/**获取主题*/
		getTopic() {
			let params = {
				applicationId: getDictionary('应用ID/日历'),
				moduleCode: 'module:schedule',
				slotCode: 'slot:schedule'
			};
			getCalendarType(params).then(res => {
				if (res.code === 200) {
					this.topic = deepClone(res.result);
					// 默认全部选中
					this.filterCode = this.topic.map(item => {
						return item.accessCode;
					});
				} else {
					this.$message.error(res.message);
				}
			});
		},
		/**获取主题数据*/
		async getTheme() {
			let params = {
				applicationId: getDictionary('应用ID/日历'),
				moduleCode: 'module:schedule',
				slotCode: 'slot:scheduleTheme'
			};
			let { code, result, message } = await getCalendarType(params);
			if (code === 200) {
				this.themeArr = [{ accessName: '请选择工作性质', accessCode: '' }].concat(result);
				this.filterThemeArray = [{ accessName: '普通', accessCode: 'other' }].concat(result);
				// 默认全部选中
				this.filterThemeCode = this.filterThemeArray.map(item => {
					return item.accessCode;
				});
			} else {
				this.$message.error(message);
			}
		},
		updateEdit(detail, itemStartDay) {
			console.log(
				'%c [ 监听更新 ]-531-「index」',
				'font-size:13px; background:pink; color:#bf2c9f;'
			);
			this.detail = detail;
			this.itemStartDay = itemStartDay;
			this.$nextTick(() => {
				this.updateInit('edit');
			});
		},
		updateInit() {
			this.updateDialog.visible = true;
		},
		/**切换展示类型*/
		selectTimeType(type) {
			this.timeType = type;
			if (type === 'week') {
				this.computedRang();
			} else if (type === 'mouth') {
				this.range = [];
			} else {
				this.range = [this.selectDate, this.selectDate];
			}
			this.getData();
		},
		/**
		 * @method 计算日期范围-主要用于周
		 * @param type {String of ['pre''next']} 计算类型
		 * */
		computedRang(type) {
			if (type === 'prev') {
				this.selectDate = new Date(this.selectDate.getTime() - 7 * 24 * 60 * 60 * 1000);
			} else if (type === 'next') {
				this.selectDate = new Date(this.selectDate.getTime() + 7 * 24 * 60 * 60 * 1000);
			} else if (type === 'current') {
				this.selectDate = this.today;
			}
			let week = this.selectDate.getDay() === 0 ? 7 : this.selectDate.getDay();
			let min = new Date(this.selectDate.getTime() - (week - 1) * 24 * 60 * 60 * 1000);
			let max = new Date(this.selectDate.getTime() + (7 - week) * 24 * 60 * 60 * 1000);
			this.range = [min, max];
		},
		/**日期范围切换*/
		changeDate(type) {
			if (this.timeType === 'mouth') {
				let changeType = type === 'current' ? 'today' : `${type}-month`;
				this.$refs.rightCalendar.selectDate(changeType);
			} else if (this.timeType === 'week') {
				this.computedRang(type);
			} else if (this.timeType === 'day') {
				if (type === 'prev') {
					this.selectDate = new Date(new Date(this.selectDate).getTime() - 24 * 60 * 60 * 1000);
				} else if (type === 'next') {
					this.selectDate = new Date(new Date(this.selectDate).getTime() + 24 * 60 * 60 * 1000);
				} else {
					this.selectDate = new Date();
				}
			}
		},
		handleViewDetail(item) {
			this.detailDialog.id = item.id;
			this.detailDialog.visible = true;
			this.detailDialog.startDay = item.startDay;
		},
		// 监听日历详情的数据更新
		onUpdate() {
			this.getData();
		}
	}
};
</script>

<style lang="scss" scoped>
.calendar {
	&-content {
		width: 100%;
		height: 100%;
		background: linear-gradient(180deg, var(--brand-1) 0%, rgb(250, 252, 255) 100%);

		@include flexBox();

		&-left {
			width: 284px;
			height: 100%;

			&-date {
				height: 36px;
				padding: 14px 12px 2px;
				font-size: 14px;
				font-weight: 800;
				color: $primaryTextColor;
				line-height: 22px;
			}

			.leftCalendar {
				height: 234px;
				background: transparent;

				::v-deep .el-calendar__header {
					display: none;
				}

				::v-deep .el-calendar__body {
					padding: 12px 16px 15px;

					th {
						font-size: 14px;
						font-weight: 400;
						color: $primaryTextColor;
						line-height: 22px;
						height: 24px;
						padding: 4px 0;
					}

					.current,
					.next,
					.prev {
						border: none !important;

						.el-calendar-day {
							height: auto;
							padding: 4px 0;
							width: 100%;

							&:hover {
								background: transparent;
							}
						}
					}

					.next,
					.prev {
						.date {
							color: $disabledTextColor;
						}
					}

					.current.is-today .date-con {
						border: 1px solid var(--brand-6);
						color: var(--brand-6);
					}

					.is-selected {
						background: transparent;

						.date-con {
							background: var(--brand-6);
							color: #ffffff !important;
						}
					}
				}

				.date {
					font-size: 14px;
					font-weight: 400;
					color: $primaryTextColor;
					line-height: 22px;
					height: 24px;
					@include flexBox();

					&-con {
						border-radius: 3px;
						height: 24px;
						width: 24px;
						@include flexBox();

						&:hover {
							background: var(--brand-1);
							color: #ffffff !important;
						}
					}
				}
			}

			.myCon {
				padding: 14px 12px 2px;
				font-size: 14px;
				font-weight: 800;
				color: $primaryTextColor;
				line-height: 22px;
			}

			.detail {
				padding: 12px;
				::v-deep {
					.el-checkbox {
						display: block;
						.el-checkbox__inner {
							width: 16px;
							height: 16px;
						}
						.el-checkbox__label {
							font-weight: 400;
						}
						&.checked_0 {
							.el-checkbox__inner {
								background-color: #3586ff;
								border-color: #3586ff;
							}
						}
						&.checked_1 {
							.el-checkbox__inner {
								background-color: #27cdb0;
								border-color: #27cdb0;
							}
						}
						&.checked_2 {
							.el-checkbox__inner {
								background-color: #8b7ceb;
								border-color: #8b7ceb;
							}
						}
					}
					.theme-checkbox.is-checked {
						.el-checkbox__inner {
							background: var(--brand-1);
							border-color: var(--brand-6);
							&::after {
								border: 1px solid var(--brand-6);
								border-left: 0;
								border-top: 0;
							}
						}
					}
				}
				&-item {
					min-height: 28px;
					font-size: 14px;
					font-weight: 400;
					color: $textColor;
					line-height: 14px;
					cursor: pointer;
					@include flexBox(flex-start);
					flex-direction: column;
					align-items: normal;

					&-icon {
						width: 16px;
						height: 16px;
						background: #ffffff;
						border-radius: 2px;
						@include flexBox();
						margin-right: 10px;
					}
				}
			}
		}

		&-right {
			position: relative;
			flex: 1;
			height: 100%;
			padding: 0 21px;
			background: #ffffff;
			display: flex;
			flex-direction: column;

			&-title {
				flex-shrink: 0;
				height: 70px;
				@include flexBox(space-between);

				.left {
					@include flexBox();

					&-pre,
					&-next {
						width: 36px;
						height: 32px;
						border: 1px solid $borderColor;
						margin-left: 8px;
						border-radius: 6px;
						cursor: pointer;
						@include flexBox();
					}

					&-time {
						font-size: 16px;
						font-weight: 500;
						color: $primaryTextColor;
						line-height: 24px;
						margin-left: 8px;
					}
				}

				.right {
					width: 120px;
					height: 32px;
					background: #f0f0f0;
					border-radius: 6px;
					padding: 2px;
					@include flexBox();

					.select {
						background: #ffffff;
					}

					&-item {
						width: 33.33%;
						height: 100%;
						border-radius: 2px;
						font-size: 14px;
						font-weight: 400;
						color: $textColor;
						line-height: 28px;
						text-align: center;
						cursor: pointer;
					}
				}
			}

			.rightCalendar {
				.deledeLine {
					text-decoration: line-through;
				}

				flex: 1;

				::v-deep .el-calendar__header {
					display: none;
				}

				::v-deep .el-calendar__body {
					height: 100%;
					padding: 0;

					tbody {
						height: calc(100% - 30px);
					}

					.el-calendar-table {
						height: 100%;
					}

					.el-calendar-table__row {
						height: 16.7%;
					}

					th {
						font-size: 14px;
						font-weight: 400;
						color: $textColor;
						line-height: 22px;
						text-align: right;
						padding: 0 0 8px;
					}

					.current {
						overflow: hidden;
					}

					.current,
					.next,
					.prev {
						padding-left: 8px;
						border: none;

						.el-calendar-day {
							height: 100%;
							overflow: auto;
							width: 100%;
							padding: 0;
							border-top: 1px solid #f0f0f0 !important;
							@include noScrollBar;

							&:hover {
								background: var(--brand-1);
							}
						}
					}

					.next,
					.prev {
						.date-disable {
							color: $disabledTextColor;
						}
					}

					.is-selected {
						background: transparent;
					}
				}

				.date {
					color: $primaryTextColor;
					overflow: hidden;
					position: relative;

					&-num {
						font-size: 14px;
						line-height: 22px;
						font-weight: 600;
						text-align: right;
					}

					&-content {
						width: calc(100% - 12px);
						padding: 0 4px;
						border-radius: 3px;
						margin: 2px 6px;
						cursor: pointer;
						@include flexBox(flex-start);

						&-icon {
							background: var(--brand-6);
							width: 6px;
							height: 6px;
							border-radius: 50%;
							margin-right: 4px;
						}

						&-text {
							flex: 1;
							font-size: 14px;
							font-weight: 400;
							color: $subTextColor;
							line-height: 22px;
							@include aLineEllipse;
						}
					}

					&-mask {
						width: 100%;
						height: 22px;
						background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, #ffffff 100%);
						position: absolute;
						bottom: 0;
						left: 0;
					}
				}

				.date-select {
					min-height: 100%;
					border-top: 1px solid var(--brand-6) !important;
					background: var(--brand-2);
				}
			}
		}
	}
}
</style>

<style>
.custom-popover.el-popover {
	border-radius: 12px !important;
	padding: 0 0 16px 16px !important;
	width: auto !important;
}
</style>
