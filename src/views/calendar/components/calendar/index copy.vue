<template>
	<div class="calendar">
		<div class="calendar-content">
			<div class="calendar-content-left">
				<div class="calendar-content-left-date">{{ time }}</div>
				<el-calendar ref="leftCalendar" v-model="selectDate" class="leftCalendar">
					<template slot="dateCell" slot-scope="{ data }">
						<div class="date">
							<div class="date-con">
								<span v-if="new Date().toDateString() === new Date(data.day).toDateString()">
									今
								</span>
								<span v-else>
									{{ data.day.split('-')[data.day.split('-').length - 1].replace(/^0/gi, '') }}
								</span>
							</div>
						</div>
					</template>
				</el-calendar>
				<div class="myCon">日程类型</div>
				<div class="detail">
					<div
						v-for="(item, index) of types"
						:key="index"
						class="detail-item"
						@click="filterType(item.typeName)"
					>
						<div style="display: flex">
							<div
								class="detail-item-icon"
								:style="{
									background: !typesName.includes(item.typeName)
										? ''
										: item.typeName == '工作备忘'
										? '#26C88E'
										: '#1890FF'
								}"
							>
								<svg-icon icon-class="check"></svg-icon>
							</div>
							<span>{{ item.typeName }}</span>
						</div>
						<div v-if="!!item.childNode" class="detail" style="margin-left: 14px">
							<div
								v-for="(info, Index) of item.childNode"
								:key="Index"
								class="detail-item"
								@click.stop="filterType(info.typeName)"
							>
								<div style="display: flex">
									<div
										class="detail-item-icon"
										:style="{
											background: !typesName.includes(info.typeName)
												? ''
												: info.typeName == '工作备忘'
												? '#26C88E'
												: '#26C88E'
										}"
									>
										<svg-icon icon-class="check"></svg-icon>
									</div>
									<span>{{ info.typeName }}</span>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div v-if="filterCode.length != 0" class="myCon">日程主题</div>
				<div v-if="filterCode.length != 0" class="detail">
					<div
						v-for="(item, index) of topic"
						:key="index"
						class="detail-item"
						@click="filterTopic(item.accessCode)"
					>
						<div style="display: flex">
							<div
								class="detail-item-icon"
								:style="{
									background: filterCode.includes(item.accessCode) ? '#1890FF' : ''
								}"
							>
								<svg-icon icon-class="check"></svg-icon>
							</div>
							<span>{{ item.accessName }}</span>
						</div>
					</div>
				</div>
			</div>
			<div class="calendar-content-right">
				<div class="calendar-content-right-title">
					<div class="left">
						<el-button type="primary" style="cursor: pointer" @click="changeDate('current')">
							今天
						</el-button>
						<div class="left-pre" @click="changeDate('prev')">
							<svg-icon icon-class="seal-left"></svg-icon>
						</div>
						<div class="left-next" @click="changeDate('next')">
							<svg-icon icon-class="next"></svg-icon>
						</div>
						<div class="left-time">{{ time }}</div>
					</div>
					<div class="right">
						<div
							class="right-item"
							:class="{ select: timeType === 'day' }"
							@click="selectTimeType('day')"
						>
							日
						</div>
						<div
							class="right-item"
							:class="{ select: timeType === 'week' }"
							@click="selectTimeType('week')"
						>
							周
						</div>
						<div
							class="right-item"
							:class="{ select: timeType === 'mouth' }"
							@click="selectTimeType('mouth')"
						>
							月
						</div>
					</div>
				</div>
				<el-calendar
					v-show="timeType !== 'day'"
					ref="rightCalendar"
					v-model="selectDate"
					v-loading="loading"
					v-bind="options"
					class="rightCalendar"
					:style="
						timeType === 'mouth' ? 'width: 100%' : 'width: calc(100% - 30px);margin-left: 30px;'
					"
				>
					<template slot="dateCell" slot-scope="{ date, data }">
						<div
							class="date"
							:class="{ 'date-select': data.isSelected, 'date-disable': timeType !== 'week' }"
						>
							<div class="date-num">
								{{ data.day.split('-')[data.day.split('-').length - 1].replace(/^0/gi, '') }}
							</div>
							<!--  动态ref 加上其id 用于通过路由进入后查找到对应popover内容refs -->
							<el-popover
								v-for="(item, index) of calendarData[data.day]"
								v-show="timeType === 'mouth'"
								:ref="`popover_${item.id}`"
								:key="index"
								popper-class="custom-popover"
								placement="right"
								width="400"
								trigger="click"
								@click.native="getDetail(item)"
							>
								<detail
									:detail="detail"
									:tip-type-arr="tipTypeArr"
									:loading="detailLoading"
									:start-day="itemStartDay"
									@update="update"
									@openEdit="openAdd"
								></detail>
								<div slot="reference" class="date-content">
									<div
										class="date-content-icon"
										:style="{ background: item.scheduleType == 1 ? '#1890FF' : '#26C88E' }"
									></div>
									<div
										class="date-content-text"
										:class="{
											deledeLine: item.isMark
										}"
									>
										{{ item.title || '无标题' }}
									</div>
								</div>
							</el-popover>
							<!--							<div class="date-mask"></div>-->
						</div>
					</template>
				</el-calendar>
				<timeLine
					v-show="timeType !== 'mouth'"
					:key="timeType"
					v-loading="timeType === 'day' && loading"
					:calendar-data="calendarData"
					:time-type="timeType"
					:tip-time-arr="tipTimeArr"
					:tip-type-arr="tipTypeArr"
					:calendar-arr="calendarArr"
					:start-date="range[0]"
					:top="timeType === 'day' ? '80px' : '160px'"
					@update="update"
					@openEdit="openEdit"
				></timeLine>
			</div>
		</div>
		<newAdd
			ref="newAdd"
			:topic="topic"
			:tip-time-arr="tipTimeArr"
			:tip-type-arr="tipTypeArr"
			:calendar-arr="calendarArr"
			:start-day="itemStartDay"
			:detail="detail"
			:hidden-schedule="hiddenSchedule"
			@update="update"
		/>
	</div>
</template>

<script>
import timeLine from '@/views/calendar/components/time-line';
import newAdd from '@/views/calendar/components/new-add';
import { getThemColor } from '../them-color';
import {
	getCalendarDetail,
	getCalendarDirection,
	getCalendarList,
	getCalendarType
} from '@/api/modules/calendar';
import { deepClone, parseTime } from '@/utils';
import detail from '@/views/calendar/components/detail';
import { getDictionary } from '@/utils/data-dictionary';
export default {
	components: {
		timeLine,
		newAdd,
		detail
	},
	data() {
		return {
			getThemColor,
			loading: false,
			calendarData: {}, // 日程数据
			filterCode: [], // 筛选显示的日程主题
			today: new Date(), // 今天
			selectDate: new Date(), // 双向绑定选中的日期
			timeType: 'mouth', // 展示日期类型
			range: [], // 日历范围
			types: [
				{
					typeName: '我的日程',
					typeValue: 1,
					childNode: null
				},
				{
					typeName: '工作备忘',
					typeValue: 2,
					childNode: [
						{
							typeName: '我的委托',
							typeValue: 1,
							childNode: null
						},
						{
							typeName: '我的任务',
							typeValue: 2
						}
					]
				}
			], // 日程类型
			typesName: ['我的日程', '工作备忘', '我的委托', '我的任务'], // 筛选显示的日程类型
			topic: [], // 日程主题
			detail: {}, // 详情内容
			itemStartDay: null, // 点击数据的开始时间
			detailLoading: false,
			tipTimeArr: [], // 提醒时间集合
			tipTypeArr: [], // 提醒方式集合
			calendarArr: [], // 重复类型集合
			clickDetail: false,
			time: '', // 当前选中年月的回显
			hiddenSchedule: false //是否隐藏日程
		};
	},
	computed: {
		options() {
			let obj = {};
			if (this.range.toString()) {
				obj.range = this.range;
			}
			return obj;
		}
	},
	watch: {
		/**选择日期*/
		selectDate(newVal) {
			let date = new Date(newVal);
			let time = date.getFullYear() + '年' + (date.getMonth() + 1) + '月' + date.getDate() + '日';
			if (this.time === time) return;
			this.time = time;
			// this.timeType === 'week' && this.computedRang();
			// let keys = date.toLocaleDateString().split('/');
			// keys = keys.map(key => {
			// 	if (key.length < 2) {
			// 		key = '0' + key;
			// 	}
			// 	return key;
			// });
			// if (this.calendarData[keys.join('-')]) return; // 如果数据存在就不请求
			!this.clickDetail && this.selectTimeType(this.timeType);
			setTimeout(() => {
				this.clickDetail = false;
			}, 0);
		},
		/**监听主题类型变化，请求不同的数据*/
		filterCode: {
			deep: true,
			handler: function (newVal) {
				this.getData();
			}
		},
		typesName: {
			deep: true,
			handler: function (newVal) {
				if (newVal.length === 0) {
					this.calendarData = {};
				} else {
					this.getData();
				}
			}
		},
		$route(newVal) {
			this.searchDetail(newVal);
		}
	},
	mounted() {
		this.time =
			this.today.getFullYear() +
			'年' +
			(this.today.getMonth() + 1) +
			'月' +
			this.today.getDate() +
			'日';
		this.getTopic();
		this.getDirection(); // 获取字典数据
		this.searchDetail(this.$route);
	},
	methods: {
		/**全局搜索进入详情*/
		searchDetail(newVal) {
			this.itemStartDay = newVal.query.startDay;
			if (newVal.path == '/calendar' && newVal.query.id && newVal.query.startDay) {
				this.selectDate = new Date(newVal.query.startDay);
				getCalendarDetail(newVal.query.id, { startDay: newVal.query.startDay }).then(res => {
					if (res.code === 200) {
						this.detailLoading = false;
						this.detail = res.result || {};
						//保证一定执行一次doShow()
						let timer = () => {
							setTimeout(() => {
								if (!this.$refs[`popover_${newVal.query.id}`]) {
									timer();
								} else {
									this.$refs[`popover_${newVal.query.id}`][0].doShow();
								}
							}, 1000);
						};
						timer();
					} else {
						this.$message.error(res.message);
					}
				});
			}
		},
		/**获取下拉选择的数据字典*/
		getDirection() {
			getCalendarDirection('schedule_repeat,schedule_remind_mode,schedule_remind_channel').then(
				res => {
					if (res.code === 200) {
						let { schedule_repeat, schedule_remind_mode, schedule_remind_channel } = res.result;
						this.tipTimeArr = schedule_remind_mode; // 提醒时间集合
						this.tipTypeArr = schedule_remind_channel; // 提醒方式集合
						this.calendarArr = schedule_repeat.map(item => {
							item.itemValue = parseInt(item.itemValue);
							return item;
						}); // 重复类型集合
					} else {
						this.$message.error(res.message);
					}
				}
			);
		},
		/**获取详情*/
		getDetail(item) {
			this.clickDetail = true; // 点击的查看详情
			this.itemStartDay = item.startDay;
			this.detailLoading = true;
			getCalendarDetail(item.id, { startDay: item.startDay }).then(res => {
				this.detailLoading = false;
				if (res.code === 200) {
					this.detail = res.result || {};
				} else {
					this.$message.error(res.message);
				}
			});
		},
		/**更新列表*/
		update() {
			this.getData();
		},
		/**获取数据列表*/
		getData() {
			this.calendarData = {};
			this.loading = true;
			let startDay = this.range[0] ? this.range[0] : this.selectDate;
			startDay = parseTime(startDay, '{y}-{m}-{d}');
			let endDay = this.range[1] ? parseTime(this.range[1], '{y}-{m}-{d}') : '';
			const typeMap = {
				我的日程: '1',
				工作备忘: '2',
				我的委托: '1',
				我的任务: '2'
			};

			let scheduleTypeSelArr = [];
			let workMemoTypeSelArr = [];
			if (this.typesName && this.typesName.length) {
				this.typesName.forEach(item => {
					const value = typeMap[item];
					if (value !== undefined) {
						if (item.startsWith('我的日程') || item.startsWith('工作备忘')) {
							scheduleTypeSelArr.push(value);
						}
						if (item.startsWith('我的委托') || item.startsWith('我的任务')) {
							workMemoTypeSelArr.push(value);
						}
					}
				});
			}
			let scheduleTypeSel = scheduleTypeSelArr.join(',');
			let workMemoTypeSel = workMemoTypeSelArr.join(',');
			let theme = this.filterCode.length < this.topic.length ? this.filterCode.join(',') : ''; // 全部选中就传空
			getCalendarList({ startDay, endDay, theme, scheduleTypeSel, workMemoTypeSel }).then(res => {
				this.loading = false;
				if (res.code === 200) {
					// if (this.filterCode.length !== 0) {
					this.calendarData = res.result;
					// }
				} else {
					this.$message.error(res.message);
				}
			});
		},
		/**筛选主题*/
		filterTopic(accessCode) {
			if (this.filterCode.includes(accessCode)) {
				this.filterCode = this.filterCode.filter(item => {
					return item !== accessCode;
				});
			} else {
				this.filterCode.push(accessCode);
			}
		},
		/**筛选类型*/
		filterType(typeName) {
			// 特殊处理'工作备忘'
			if (typeName === '工作备忘') {
				if (this.typesName.includes('工作备忘')) {
					// 如果'工作备忘'已存在，则移除'工作备忘'、'我的委托'和'我的任务'
					this.typesName = this.typesName.filter(
						item => !['工作备忘', '我的委托', '我的任务'].includes(item)
					);
				} else {
					// 如果'工作备忘'不存在，则添加'工作备忘'、'我的委托'和'我的任务'
					this.typesName.push('工作备忘', '我的委托', '我的任务');
				}
			} else {
				// 对于其他typeName值，进行简单的添加或删除操作
				// 如果typeName已存在，则删除它；否则，添加它
				const index = this.typesName.indexOf(typeName);
				if (index > -1) {
					this.typesName.splice(index, 1);
				} else {
					this.typesName.push(typeName);
				}

				// 确保'我的委托'和'我的任务'总在'工作备忘'后面
				if (
					(typeName === '我的委托' || typeName === '我的任务') &&
					!this.typesName.includes('工作备忘')
				) {
					this.typesName.push('工作备忘');
				}
				// 确保'我的委托'和'我的任务'删除后，'工作备忘'也要删除
				if (
					!this.typesName.includes('我的委托') &&
					!this.typesName.includes('我的任务') &&
					this.typesName.includes('工作备忘')
				) {
					this.typesName = this.typesName.filter(item => !['工作备忘'].includes(item));
				}
			}
			console.log(this.typesName);
		},
		/**获取主题*/
		getTopic() {
			let params = {
				applicationId: getDictionary('应用ID/日历'),
				moduleCode: 'module:schedule',
				slotCode: 'slot:schedule'
			};
			getCalendarType(params).then(res => {
				if (res.code === 200) {
					this.topic = deepClone(res.result);
					// 默认全部选中
					this.filterCode = this.topic.map(item => {
						return item.accessCode;
					});
				} else {
					this.$message.error(res.message);
				}
			});
		},
		openEdit(detail, itemStartDay) {
			this.detail = detail;
			this.itemStartDay = itemStartDay;
			this.$nextTick(() => {
				this.openAdd('edit');
			});
		},
		openAdd(type = 'newAdd') {
			if (this.detail.scheduleType == 2 && type == 'edit') {
				this.hiddenSchedule = true;
			} else {
				this.hiddenSchedule = false;
			}
			this.$refs.newAdd.open(type, this.selectDate, this.detail.scheduleType);
		},
		/**切换展示类型*/
		selectTimeType(type) {
			this.timeType = type;
			if (type === 'week') {
				this.computedRang();
			} else if (type === 'mouth') {
				this.range = [];
			} else {
				this.range = [this.selectDate, this.selectDate];
			}
			this.getData();
		},
		/**
		 * @method 计算日期范围-主要用于周
		 * @param type {String of ['pre''next']} 计算类型
		 * */
		computedRang(type) {
			if (type === 'prev') {
				this.selectDate = new Date(this.selectDate.getTime() - 7 * 24 * 60 * 60 * 1000);
			} else if (type === 'next') {
				this.selectDate = new Date(this.selectDate.getTime() + 7 * 24 * 60 * 60 * 1000);
			} else if (type === 'current') {
				this.selectDate = this.today;
			}
			let week = this.selectDate.getDay() === 0 ? 7 : this.selectDate.getDay();
			let min = new Date(this.selectDate.getTime() - (week - 1) * 24 * 60 * 60 * 1000);
			let max = new Date(this.selectDate.getTime() + (7 - week) * 24 * 60 * 60 * 1000);
			this.range = [min, max];
		},
		/**日期范围切换*/
		changeDate(type) {
			if (this.timeType === 'mouth') {
				let changeType = type === 'current' ? 'today' : `${type}-month`;
				this.$refs.rightCalendar.selectDate(changeType);
			} else if (this.timeType === 'week') {
				this.computedRang(type);
			} else if (this.timeType === 'day') {
				if (type === 'prev') {
					this.selectDate = new Date(new Date(this.selectDate).getTime() - 24 * 60 * 60 * 1000);
				} else if (type === 'next') {
					this.selectDate = new Date(new Date(this.selectDate).getTime() + 24 * 60 * 60 * 1000);
				} else {
					this.selectDate = new Date();
				}
			}
		}
	}
};
</script>

<style lang="scss" scoped>
.calendar {
	&-content {
		width: 100%;
		height: calc(100% - 54px);
		background: #f3f4f6;
		@include flexBox();

		&-left {
			width: 284px;
			height: 100%;

			&-date {
				height: 36px;
				padding: 14px 12px 2px;
				font-size: 14px;
				font-weight: 800;
				color: $primaryTextColor;
				line-height: 22px;
			}

			.leftCalendar {
				height: 234px;
				background: transparent;

				::v-deep .el-calendar__header {
					display: none;
				}

				::v-deep .el-calendar__body {
					padding: 12px 16px 15px;

					th {
						font-size: 14px;
						font-weight: 400;
						color: $primaryTextColor;
						line-height: 22px;
						height: 24px;
						padding: 4px 0;
					}

					.current,
					.next,
					.prev {
						border: none !important;

						.el-calendar-day {
							height: auto;
							padding: 4px 0;
							width: 100%;

							&:hover {
								background: transparent;
							}
						}
					}

					.next,
					.prev {
						.date {
							color: $disabledTextColor;
						}
					}

					.current.is-today .date-con {
						border: 1px solid var(--brand-6);
						color: var(--brand-6);
					}

					.is-selected {
						background: transparent;

						.date-con {
							background: var(--brand-6);
							color: #ffffff !important;
						}
					}
				}

				.date {
					font-size: 14px;
					font-weight: 400;
					color: $primaryTextColor;
					line-height: 22px;
					height: 24px;
					@include flexBox();

					&-con {
						border-radius: 3px;
						height: 24px;
						width: 24px;
						@include flexBox();

						&:hover {
							background: var(--brand-6);
							color: #ffffff !important;
						}
					}
				}
			}

			.myCon {
				padding: 14px 12px 2px;
				font-size: 14px;
				font-weight: 800;
				color: $primaryTextColor;
				line-height: 22px;
			}

			.detail {
				padding: 12px;

				&-item {
					min-height: 28px;
					font-size: 14px;
					font-weight: 400;
					color: $textColor;
					line-height: 14px;
					cursor: pointer;
					@include flexBox(flex-start);
					flex-direction: column;
					align-items: normal;

					&-icon {
						width: 16px;
						height: 16px;
						background: #ffffff;
						border-radius: 2px;
						@include flexBox();
						margin-right: 10px;
					}
				}
			}
		}

		&-right {
			position: relative;
			flex: 1;
			height: 100%;
			padding: 0 21px;
			background: #ffffff;
			display: flex;
			flex-direction: column;

			&-title {
				flex-shrink: 0;
				height: 70px;
				@include flexBox(space-between);

				.left {
					@include flexBox();

					&-pre,
					&-next {
						width: 36px;
						height: 32px;
						border: 1px solid $borderColor;
						margin-left: 8px;
						border-radius: 6px;
						cursor: pointer;
						@include flexBox();
					}

					&-time {
						font-size: 16px;
						font-weight: 500;
						color: $primaryTextColor;
						line-height: 24px;
						margin-left: 8px;
					}
				}

				.right {
					width: 120px;
					height: 32px;
					background: #f0f0f0;
					border-radius: 6px;
					padding: 2px;
					@include flexBox();

					.select {
						background: #ffffff;
					}

					&-item {
						width: 33.33%;
						height: 100%;
						border-radius: 2px;
						font-size: 14px;
						font-weight: 400;
						color: $textColor;
						line-height: 28px;
						text-align: center;
						cursor: pointer;
					}
				}
			}

			.rightCalendar {
				.deledeLine {
					text-decoration: line-through;
				}

				flex: 1;

				::v-deep .el-calendar__header {
					display: none;
				}

				::v-deep .el-calendar__body {
					height: 100%;
					padding: 0;

					tbody {
						height: calc(100% - 30px);
					}

					.el-calendar-table {
						height: 100%;
					}

					.el-calendar-table__row {
						height: 16.7%;
					}

					th {
						font-size: 14px;
						font-weight: 400;
						color: $textColor;
						line-height: 22px;
						text-align: right;
						padding: 0 0 8px;
					}

					.current {
						overflow: hidden;
					}

					.current,
					.next,
					.prev {
						padding-left: 8px;
						border: none;

						.el-calendar-day {
							height: 100%;
							overflow: auto;
							width: 100%;
							padding: 0;
							border-top: 1px solid #f0f0f0 !important;
							@include noScrollBar;

							&:hover {
								background: #e6f7ff;
							}
						}
					}

					.next,
					.prev {
						.date-disable {
							color: $disabledTextColor;
						}
					}

					.is-selected {
						background: transparent;
					}
				}

				.date {
					color: $primaryTextColor;
					overflow: hidden;
					position: relative;

					&-num {
						font-size: 14px;
						line-height: 22px;
						font-weight: 600;
						text-align: right;
					}

					&-content {
						width: calc(100% - 12px);
						padding: 0 4px;
						border-radius: 3px;
						margin: 2px 6px;
						cursor: pointer;
						@include flexBox(flex-start);

						&-icon {
							width: 6px;
							height: 6px;
							border-radius: 50%;
							margin-right: 4px;
						}

						&-text {
							flex: 1;
							font-size: 14px;
							font-weight: 400;
							color: $subTextColor;
							line-height: 22px;
							@include aLineEllipse;
						}
					}

					&-mask {
						width: 100%;
						height: 22px;
						background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, #ffffff 100%);
						position: absolute;
						bottom: 0;
						left: 0;
					}
				}

				.date-select {
					min-height: 100%;
					border-top: 1px solid var(--brand-6) !important;
					background: #e6f7ff;
				}
			}
		}
	}
}
</style>

<style>
.custom-popover.el-popover {
	border-radius: 12px !important;
	padding: 0 0 16px 16px !important;
	width: auto !important;
}
</style>
