<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2024-08-19 09:23:54
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-08-27 16:57:26
 * @FilePath: /coos-desktop-app/src/views/calendar/components/work-memo/index.vue
 * @Description:
-->
<template>
	<div class="memo">
		<div class="filter">
			<div class="filter-mark">
				<span
					v-for="(item, index) in filterType"
					:key="'item-' + index"
					:class="['filter-item', activeFilterType === item.value ? 'filter-item-active' : '']"
					@click="handleFilter(item.value)"
				>
					{{ item.label }}
				</span>
			</div>
			<div class="filter-priority">
				<el-dropdown @command="handleFilterPriority">
					<span class="text">
						<i class="coos-iconfont icon-shaixuan1 filter-icon"></i>
						{{ filterPriorityText }}
						<i class="el-icon-arrow-down el-icon--right"></i>
					</span>
					<el-dropdown-menu slot="dropdown">
						<el-dropdown-item command="">全部</el-dropdown-item>
						<el-dropdown-item
							v-for="item in priorityOptions"
							:key="item.value"
							:command="item.value"
						>
							<span
								class="select-option-circle"
								:style="{
									backgroundColor: item.color,
									width: '7px',
									height: '7px',
									display: 'inline-block',
									'border-radius': '100%',
									'margin-right': '6px'
								}"
							/>
							{{ item.label }}
						</el-dropdown-item>
					</el-dropdown-menu>
				</el-dropdown>
			</div>
		</div>
		<div v-loading="submitLoading" class="memo-add">
			<el-input
				v-model="addForm.content"
				placeholder="输入标题，回车确认"
				@keydown.enter.native="handleAdd"
			/>
			<el-button icon="el-icon-circle-plus-outline" @click="handleAddUser">添加人员</el-button>
			<label v-if="addForm.users.length > 0" class="select-user">
				{{ addForm.users.map(item => item.name).join('、') }}
			</label>
			<el-select
				v-model="addForm.priority"
				popper-class="priority-select"
				style="width: 100px; flex-shrink: 0"
				placeholder="请选择"
			>
				<template #prefix>
					<span
						class="select-option-circle"
						:style="{
							backgroundColor: priorityOptions.find(item => item.value === addForm.priority).color,
							width: '7px',
							height: '7px',
							display: 'inline-block',
							'border-radius': '100%'
						}"
					/>
				</template>
				<el-option
					v-for="item in priorityOptions"
					:key="item.value"
					:label="item.label"
					:value="item.value"
				>
					<span
						class="select-option-circle"
						:style="{
							backgroundColor: item.color,
							width: '7px',
							height: '7px',
							display: 'inline-block',
							'border-radius': '100%'
						}"
					/>
					<label>{{ item.label }}</label>
				</el-option>
			</el-select>
			<el-date-picker
				v-model="addForm.remindTime"
				class="mgl-20"
				style="width: 300px"
				prefix-icon="coos-iconfont icon-xiaoxizhongxin"
				type="datetime"
				placeholder="设置提醒时间"
				value-format="yyyy-MM-dd HH:mm:ss"
			/>
		</div>
		<div v-loading="list.loading" class="memo-list">
			<div v-if="activeFilterType === 0" class="memo-on">
				<div class="memo-title">工作备忘({{ list.data.length }})</div>
				<MemoItems ref="MemoItems" :items="list.data" @onUpdate="onUpdate" />
			</div>
			<div v-else class="memo-off">
				<div class="memo-title">已完成({{ list.total }})</div>
				<MemoItems ref="MemoItems" :items="list.data" @onUpdate="onUpdate" />
				<el-pagination
					v-if="list.total > 0"
					:current-page.sync="list.pagination.pageNo"
					:page-size="list.pagination.pageSize"
					layout="total, prev, pager, next"
					:total="list.total"
					@current-change="getList"
				/>
			</div>
			<BasicEmpty :loading="list.loading" :data="list.data" name="no-data" />
		</div>
		<orgPersonnelDialog
			title="选择协作人"
			need-all-data
			disable-all
			:init-values="addUser.users"
			:visible="addUser.visible"
			:data-source="addUser.dataSource"
			:can-select-depart="false"
			@sure="handleUserSure"
			@close="handleUserClose"
		/>
	</div>
</template>

<script>
import { getWorkMemoList, saveCalendarData } from '@/api/modules/calendar';
import MemoItems from './items.vue';
import orgPersonnelDialog from '@/components/org-personnel-dialog';
import { parseTime } from '@/utils/index';

export default {
	components: { MemoItems, orgPersonnelDialog },
	data() {
		return {
			addInitForm: {
				content: '',
				scheduleType: 2,
				priority: 0,
				users: [],
				remindTime: '',
				remindChannel: 1,
				mode: ''
			},
			submitLoading: false,
			addForm: {},
			priorityOptions: [
				{
					label: '紧急',
					value: 2,
					color: '#FF4D4F'
				},
				{
					label: '重要',
					value: 1,
					color: '#ED7B2F'
				},
				{
					label: '一般',
					value: 0,
					color: '#618CDF'
				}
			],
			list: {
				loading: true,
				total: 0,
				priority: '',
				pagination: {
					pageNo: 1,
					pageSize: 10
				},
				data: []
			},
			addUser: {
				users: [],
				userIds: [],
				visible: false,
				dataSource: ['user', 'depart']
			},
			activeFilterType: 0,
			filterType: [
				{
					label: '未完成',
					value: 0
				},
				{ label: '已完成', value: 1 }
			]
		};
	},
	computed: {
		filterPriorityText() {
			const currentPriority = this.priorityOptions.find(item => item.value === this.list.priority);
			return currentPriority ? currentPriority.label : '全部';
		}
	},
	created() {
		this.addForm = {
			...this.addInitForm
		};
		this.getList();
	},
	methods: {
		/**打开详情*/
		openDetail(options) {
			if (options.id) {
				this.$refs.MemoItems.handleView(options);
			}
		},
		async handleAdd() {
			this.submitLoading = true;
			const { success, code, message } = await saveCalendarData(this.addForm);
			if (code === 200 && success) {
				this.$message.success('添加成功');
				this.addForm = {
					...this.addInitForm
				};
				this.onUpdate();
			} else {
				this.$message.error(message || '添加失败');
			}
			this.submitLoading = false;
		},
		// 获取备忘列表
		async getList() {
			this.list.data = [];
			this.list.loading = true;
			const { result } = await getWorkMemoList({
				pageNo: this.activeFilterType === 0 ? 1 : this.list.pagination.pageNo,
				pageSize: this.activeFilterType === 0 ? -1 : this.list.pagination.pageSize,
				isMark: !!this.activeFilterType,
				priority: this.list.priority
			});
			this.list.loading = false;
			this.list.data = result.records;
			this.list.total = result.total;
		},
		// 监听详情更新 处理列表数据
		onUpdate() {
			this.list.pagination.pageNo = 1;
			this.getList();
		},
		// 重要程度筛选
		handleFilterPriority(priority) {
			this.list.priority = priority;
			this.list.pagination.pageNo = 1;
			this.getList();
		},
		// 完成情况筛选
		handleFilter(val) {
			this.activeFilterType = val;
			this.list.pagination.pageNo = 1;
			this.getList();
		},
		// 显示批量添加协作人弹窗
		handleAddUser() {
			this.addUser.userIds = this.addForm.users.map(item => item.id);
			this.addUser.users = this.addForm.users.map(item => ({
				...item,
				title: item.name
			}));

			this.addUser.visible = true;
		},
		// 选择人员确认事件
		handleUserSure(arr) {
			this.addForm.users = arr
				.filter(item => item.id != 0 && item.dataType != 'org')
				.flatMap(item => {
					return {
						id: item.id,
						dataType: item.dataType,
						name: item.realname || item.title || item.name
					};
				});
			this.addUser.visible = false;
		},
		handleUserClose() {
			this.addUser.visible = false;
		},
		getNextThirtyOrZeroMinutes() {
			const now = new Date();
			const currentMinutes = now.getMinutes();
			let nextMinutes = 30;

			// 如果当前分钟数已经过了30分钟，则取下一小时的00分钟
			if (currentMinutes >= 30) {
				nextMinutes = 0;
				now.setHours(now.getHours() + 1);
			}

			// 计算还需要增加多少分钟才能到达下一个30分钟或00分钟
			const minutesToAdd = nextMinutes - currentMinutes;

			// 增加相应的分钟数
			now.setMinutes(now.getMinutes() + minutesToAdd);
			now.setSeconds(0);

			return parseTime(now, '{y}-{m}-{d} {h}:{i}:{s}');
		}
	}
};
</script>

<style lang="scss" scoped>
.memo {
	height: 100%;
	padding-top: 20px;
	background-color: #fff;
	display: flex;
	flex-direction: column;
	.filter {
		padding: 0px 13px;
		flex-direction: row;
		display: flex;
		align-items: center;
		justify-content: space-between;
		&-item {
			width: 74px;
			height: 30px;
			cursor: pointer;
			display: inline-block;
			text-align: center;
			background: #f3f4f6;
			border-radius: 6px;
			font-size: 14px;
			color: #2f446b;
			line-height: 30px;
			&-active {
				background: var(--brand-1);
				color: var(--brand-6);
			}
			&:not(:first-child) {
				margin-left: 8px;
			}
		}
		&-priority {
			padding: 8px 16px;
			.text {
				font-size: 12px;
				color: #737a94;
				line-height: 22px;
				display: flex;
				align-items: center;
			}
			.filter-icon {
				font-size: 16px;
				margin-right: 8px;
			}
			.el-icon-arrow-down {
				font-size: 16px;
				margin-left: 4px;
			}
		}
	}

	&-add {
		margin: 16px 13px 20px 13px;
		height: 58px;
		background: #f3f4f6;
		border-radius: 6px;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 12px;
		.el-button {
			margin: 0 20px;
		}
		.el-date-editor {
			margin-left: 20px;
			flex-shrink: 0;
		}
		.select-user {
			width: 250px;
			display: inline-block;
			overflow: hidden;
			white-space: nowrap;
			text-overflow: ellipsis;
			color: #2f446b;
		}
	}
	&-title {
		height: 32px;
		padding: 4px 8px;
		font-weight: 600;
		font-size: 14px;
		color: #2f446b;
		line-height: 24px;
		margin: 0 8px 8px;
	}
	&-list {
		flex: 1;
		overflow-y: auto;
		padding-bottom: 10px;
		.el-pagination {
			text-align: right;
		}
	}
	.show-marked {
		text-align: center;
		font-size: 12px;
		color: var(--brand-6);
		line-height: 20px;
		padding: 12px 0;
		&-text {
			cursor: pointer;
		}
	}
	::v-deep {
		.el-select {
			.el-input__prefix {
				left: 15px;
				display: flex;
				align-items: center;
			}
		}
	}
}
</style>
<style lang="scss">
.priority-select {
	.el-select-dropdown__item {
		display: flex;
		align-items: center;
		label {
			margin-left: 10px;
		}
	}
}
</style>
