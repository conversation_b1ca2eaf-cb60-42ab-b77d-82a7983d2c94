<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2024-08-19 10:49:43
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-08-27 17:11:40
 * @FilePath: /coos-desktop-app/src/views/calendar/components/work-memo/items.vue
 * @Description:
-->
<template>
	<div class="memo-items">
		<div
			v-for="item in items"
			:key="item.id"
			class="memo-item"
			:class="{ 'memo-item-mark': item.isMark }"
			@contextmenu.prevent="event => handleRightClick(event, item)"
		>
			<div class="left" @click="handleMark(item)">
				<i
					class="coos-iconfont left-icon"
					:class="{
						'icon-chenggong': item.isMark,
						marked: item.isMark
					}"
				/>
			</div>
			<div class="right">
				<div class="title" @click="handleView(item)">{{ item.title }}</div>
				<div v-if="!item.canUpdate" class="cell">
					<div class="cell-title">
						<span class="coos-iconfont icon-Friendsettings"></span>
						<span class="cell-title-text">创建人</span>
					</div>
					<div class="cell-content">{{ item.createByName }}</div>
				</div>
				<div class="cell">
					<div class="cell-title">
						<span class="coos-iconfont icon-me"></span>
						<span class="cell-title-text">协作人</span>
					</div>
					<div class="cell-content">
						<span class="user-name">{{ setName(item.usersRealName) }}</span>
						<span v-if="Object.values(item.usersRealName || {}).length > 0" class="finish-process">
							<label>{{ item.handleAllNum - item.handleNotNum }}</label>
							<label class="color-1">
								/ {{ Object.values(item.usersRealName || {}).length || 0 }}
							</label>
							<label style="cursor: pointer" @click="handleViewFinishProcess(item)">
								已完成
								<i class="el-icon-arrow-right color-1" style="margin-left: 12px"></i>
							</label>
						</span>
					</div>
				</div>
				<div class="cell">
					<div class="cell-title">
						<span class="coos-iconfont icon-xiaoxizhongxin"></span>
						<span class="cell-title-text">提醒我</span>
					</div>
					<div class="cell-content">{{ item.remindTimeDesc }}</div>
				</div>
			</div>
			<div class="tag" :class="`tag-${item.priority}`">{{ item.priorityDictText }}</div>
		</div>
		<!---详情-->
		<CommonDetail
			:id="detailDialog.id"
			:show.sync="detailDialog.visible"
			:schedule-type="2"
			@onUpdate="onUpdate"
		/>

		<HandlerProcess :show.sync="workMemoDialog.visible" :handlers="workMemoDialog.handlers" />
		<!--右键操作-->
		<vue-context ref="menu" @close="onContextMenuClose">
			<li
				v-for="item in filterHandleOptions(rightClickItem)"
				:key="item.value"
				class="context-item"
				@click.prevent="handleRightClickSelect(item)"
			>
				<i class="coos-iconfont context-item-icon">{{ item.icon }}</i>
				<span>{{ item.label }}</span>
			</li>
		</vue-context>
		<!--重复性日程二次确认弹窗-->
		<el-dialog :title="'重复日程删除类型选择'" :visible.sync="repeatDialog.visible" append-to-body>
			<el-radio-group v-model="repeatDialog.selectType">
				<el-radio v-for="item in changeTypeArr" :key="item.value" :label="item.value">
					{{ item.label }}
				</el-radio>
			</el-radio-group>
			<span slot="footer" class="dialog-footer">
				<el-button @click="repeatDialog.visible = false">取消</el-button>
				<el-button type="primary" @click="handleDelete">确定</el-button>
			</span>
		</el-dialog>
	</div>
</template>

<script>
import CommonDetail from '../common-detail/index';
import {
	getCalendarType,
	markComplete,
	markCancelComplete,
	delCalendarData
} from '@/api/modules/calendar';
import HandlerProcess from '../common-detail/handler-process';
import { getDictionary } from '@/utils/data-dictionary';

export default {
	components: { CommonDetail, HandlerProcess },
	props: {
		items: {
			type: Array,
			default: () => []
		}
	},
	data() {
		return {
			detailDialog: {
				visible: false,
				id: ''
			},
			workMemoDialog: {
				visible: false,
				handlers: []
			},
			rightClickItem: {}, // 右键选择的数据
			changeTypeArr: [
				{
					label: '所有',
					value: '1'
				},
				{
					label: '本次',
					value: '2'
				},
				{
					label: '本次及之后的所有',
					value: '3'
				}
			],
			repeatDialog: {
				visible: false,
				selectType: '1'
			},
			slotButtons: []
		};
	},
	created() {
		this.getSlotButtons();
	},
	methods: {
		async getSlotButtons() {
			const params = {
				applicationId: getDictionary('应用ID/日历'),
				moduleCode: 'module:schedule',
				slotCode: 'slot:scheduleBusOpt'
			};
			// 暂时取消插槽过来的操作按钮;
			const { result } = await getCalendarType(params);
			this.slotButtons = result.map(item => {
				const accessJson = JSON.parse(item.accessJson);
				return {
					label: item.accessName,
					value: item.applicationId,
					icon: accessJson.icon
				};
			});
		},
		// 获取操作的选项
		filterHandleOptions(item) {
			const allHandleOptions = [
				{
					label: '打开',
					value: 'open',
					icon: '\ue6dc',
					event: e => {
						this.handleView(item);
					}
				},
				// {
				// 	label: '转派',
				// 	value: 'transfer',
				// 	icon: '\ue6ef',
				// 	event: (e) => {
				// 	}
				// },
				...this.slotButtons,
				{
					label: '删除',
					value: 'delete',
					icon: '\ue6dd',
					show: item.canUpdate,
					event: () => {
						// 二次确认
						this.$confirm(
							`确定删除该条${
								this.rightClickItem.scheduleType == '1' ? '日程' : '工作备忘'
							}, 是否继续?`,
							'提示',
							{
								confirmButtonText: '确定',
								cancelButtonText: '取消',
								type: 'warning'
							}
						)
							.then(() => {
								this.handleDelete(this.rightClickItem);
							})
							.catch(() => {
								this.$message({
									type: 'info',
									message: '已取消删除'
								});
							});
					}
				},
				{
					label: item.isMark ? '标记为未完成' : '标记为完成',
					value: 'mark',
					icon: '\ue6c2',
					color: '#0F45EA',
					event: e => {
						this.handleMark(item);
					}
				}
			];
			return allHandleOptions.filter(item => (item.show !== undefined ? item.show : true));
		},
		setName(item) {
			try {
				let arr = [...Object.values(item)];
				if (arr.length) {
					return arr.join('、');
				} else {
					return '暂无';
				}
			} catch (e) {
				return '暂无';
			}
		},
		handleView(item) {
			this.detailDialog.visible = true;
			this.detailDialog.id = item.id;
		},
		onUpdate() {
			this.detailDialog.visible = false;
			this.$emit('onUpdate');
		},
		handleMark(item) {
			if (!item.isMark) {
				markComplete(item.id, { startDay: item.startDay }).then(res => {
					if (res.code == 200 && res.success) {
						this.$emit('onUpdate');
						this.$message.success(res.message || '操作成功');
					}
				});
			} else {
				markCancelComplete(item.id, { startDay: item.startDay }).then(res => {
					if (res.code == 200 && res.success) {
						this.$emit('onUpdate');
						this.$message.success(res.message || '操作成功');
					}
				});
			}
		},
		handleViewFinishProcess(item) {
			this.workMemoDialog.visible = true;
			this.workMemoDialog.handlers = item.handlers || [];
		},
		// 监听右键菜单关闭事件
		onContextMenuClose() {},
		// 右键菜单开启事件
		handleRightClick(event, item) {
			if (!this.$refs.menu) {
				return;
			}
			this.$refs.menu.open(event, item);
			this.rightClickItem = item;
		},
		// 右键菜单选择事件
		handleRightClickSelect(item) {
			item.event ? item.event() : this.$message.warning('未配置方法，需根据实际情况来处理');
		},
		handleDelete(item) {
			delCalendarData(item.id, {
				mode: this.repeatDialog.selectType,
				startDay: item.startDay
			}).then(res => {
				if (res.code == 200 && res.success) {
					this.$emit('onUpdate');
					this.$message.success(res.message || '操作成功');
					this.repeatDialog.visible = false;
				}
			});
		}
	}
};
</script>

<style lang="scss" scoped>
.memo-item {
	display: flex;
	justify-content: space-between;
	margin: 0 13px 13px;
	background: #ffffff;
	border: solid 1px transparent;
	border-radius: 9px;
	background-image: linear-gradient(#fff, #fff),
		linear-gradient(180deg, rgba(188, 227, 255, 1), rgba(188, 211, 255, 0.3));
	background-origin: border-box;
	background-clip: content-box, border-box;
	position: relative;
	.left {
		width: 18px;
		height: 18px;
		margin: 12px 8px 12px;
		flex-shrink: 0;
		border: 1px solid #bccadb;
		border-radius: 100%;
		position: relative;
		cursor: pointer;
		.left-icon {
			display: inline-block;
			font-size: 20px;
			cursor: pointer;
			position: absolute;
			top: -3px;
			left: -2px;
		}
	}
	.right {
		flex: 1;
		overflow: hidden;
		padding: 12px 13px 12px 0;
		.title {
			height: 24px;
			font-weight: 500;
			font-size: 15px;
			color: $primaryTextColor;
			line-height: 24px;
			overflow: hidden;
			white-space: nowrap;
			text-overflow: ellipsis;
			cursor: pointer;
			&:hover {
				color: #3088ff;
			}
		}
	}
	.cell {
		margin-top: 8px;
		display: flex;
		align-items: center;
		.cell-title {
			display: flex;
			align-items: center;
			flex-shrink: 0;
			.coos-iconfont {
				font-size: 16px;
			}
			&-text {
				height: 20px;
				font-size: 12px;
				color: $subTextColor;
				line-height: 20px;
				margin-left: 4px;
			}
		}
		.cell-content {
			height: 20px;
			font-size: 12px;
			color: #2f446b;
			line-height: 20px;
			margin-left: 8px;
			display: flex;
			justify-content: space-between;
			flex: 1;
			overflow: hidden;
			.user-name {
				flex: 1;
				display: inline-block;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}
			.finish-process {
				height: 22px;
				display: inline-block;
				font-size: 14px;
				color: var(--brand-6);
				line-height: 22px;
				margin-left: 55px;
				flex-shrink: 0;
				padding-right: 15px;
				.color-1 {
					color: #d1d3db;
				}
			}
		}
	}
	.tag {
		position: absolute;
		right: 0;
		top: 0;
		width: 40px;
		height: 22px;
		text-align: center;
		background-color: rgba(255, 77, 79, 0.1);
		border-radius: 0px 9px 0px 9px;
		font-size: 12px;
		color: #ff4d4f;
		line-height: 22px;

		&-2 {
			color: #ff4d4f;
			background: rgba(255, 77, 79, 0.1);
		}

		&-1 {
			color: #ed7b2f;
			background: rgba(237, 123, 47, 0.1);
		}

		&-0 {
			color: #618cdf;
			background: rgba(94, 124, 182, 0.1);
		}
	}
	&-mark {
		color: #b9bdc9;
		background-image: linear-gradient(#fff, #fff),
			linear-gradient(180deg, rgba(222, 227, 230, 1), rgba(188, 211, 255, 0.3));
		.title {
			text-decoration: line-through;
			color: inherit !important;
		}
		.cell {
			.cell-title {
				.coos-iconfont {
					color: inherit !important;
				}
				&-text {
					color: inherit !important;
				}
			}
			.cell-content {
				color: inherit !important;
				.finish-process {
					color: inherit !important;
					.color-1 {
						color: inherit !important;
					}
				}
			}
		}
	}
}
.v-context {
	min-width: 9em;
	padding: 8px 0;
	.context-item {
		display: flex;
		align-items: center;
		padding: 5px 8px;
		color: #2f446b;
		cursor: pointer;
		&:hover {
			background: #e0ebfc;
		}
		&-icon {
			margin-right: 3px;
			font-size: 16px;
		}
	}
	::v-deep {
		li > a {
			padding: 0;
			height: 22px;
			font-size: 14px;
			color: #2f446b;
			line-height: 22px;
		}
	}
}
</style>
