<template>
	<div v-loading="loading" class="application-market">
		<div
			class="mian"
			:style="{
				backgroundImage: `url(${require(`@/assets/${rentThem}/application-market/bg.png`)})`
			}"
		>
			<div class="mian-header">
				<div class="header-box">
					<img class="header-box-img" :src="require(`@/assets/${rentThem}/home/<USER>" />
					<div class="header-box-text">川投数科业务操作系统应用市场</div>
				</div>
			</div>
			<el-row :gutter="20">
				<el-col :span="12" :offset="6">
					<div class="mian-search">
						<div class="title">探索解决方案，激发团队灵感</div>
						<div class="input">
							<el-input
								v-model="name"
								placeholder="请输入内容"
								clearable
								@clear="searchClear"
								@input="search"
								@keydown.enter.native="search"
							>
								<i slot="prefix" class="el-input__icon el-icon-search" @click="search"></i>
							</el-input>
						</div>
					</div>
				</el-col>
			</el-row>

			<div ref="content" class="mian-content">
				<div ref="left" class="left">
					<div class="left-title">应用分类</div>
					<el-menu :default-active="categoryId" @select="selectCategory">
						<el-menu-item v-for="item of categoryList" :key="item.id" :index="item.id">
							<span slot="title">{{ item.name }}</span>
						</el-menu-item>
					</el-menu>
				</div>
				<div ref="right" class="right">
					<!-- <div class="right-title">文旅行业应用推荐</div>
					<div class="right-dec">一体化的行业时间，解决业务核心挑战，促进企业增长</div> -->
					<div v-infinite-scroll="load" class="list" :style="styleObject">
						<div v-for="(item, index) of list" :key="index" class="box" @click="handleClick(item)">
							<img v-if="item.coverImgUrl" class="box-img" :src="item.coverImgUrl" alt="" />
							<img
								v-else
								class="box-img"
								:src="require(`@/assets/${rentThem}/application-market/default.png`)"
								alt=""
							/>
							<div class="box-content">
								<div class="box-content-title">
									{{ item.name }}
								</div>
								<div class="box-content-dec">
									{{ item.digest }}
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<el-dialog
			title=""
			:visible.sync="dialogTableVisible"
			width="60%"
			:custom-class="'details-dialog'"
			:modal="true"
			:modal-append-to-body="false"
		>
			<div class="details">
				<div class="details-header">
					<div class="details-header-left">
						<img v-if="details.logoUrlPath" :src="details.logoUrlPath" alt="" />
						<img v-else :src="require(`@/assets/${rentThem}/application-market/logo.png`)" alt="" />
					</div>
					<div class="details-header-right">
						<div class="details-header-box">
							<div class="details-title">{{ details.name }}</div>
							<div class="details-tags">{{ details.categoryName }}</div>
						</div>

						<div class="details-dec">{{ details.digest }}</div>
						<div class="details-btn" @click="getApp">
							<el-button type="primary">获取应用</el-button>
						</div>
					</div>
				</div>
				<div class="details-content">
					<div class="details-content-left" v-html="details.description"></div>
					<div class="details-content-right">
						<div class="details-box">
							<div class="details-box-lable">开发者</div>
							<div class="details-box-img">
								<img :src="details.developerLogoUrlPath" alt="" />
								<div class="details-box-img-name">{{ details.companyName }}</div>
							</div>
						</div>
						<div v-if="details.labelList && details.labelList.length" class="details-box">
							<div class="details-box-lable">标签</div>
							<div class="details-box-tags">
								<div v-for="(item, i) of details.labelList" :key="i" class="tags-item">
									{{ item }}
								</div>
							</div>
						</div>
						<div v-if="details.helpInfo" class="details-box">
							<div class="details-box-lable">帮助信息</div>
							<div class="details-box-btn" @click="help">
								<i class="coos-iconfont icon-content icon"></i>
								<el-button type="text">帮助文档</el-button>
							</div>
						</div>
					</div>
				</div>
			</div>
			<el-dialog
				width="30%"
				title="获取应用"
				:visible.sync="innerVisible"
				append-to-body
				:custom-class="'form-dialog'"
			>
				<div class="form-title">当前应用需向管理申请使用</div>
				<el-form ref="formName" v-loading="loading" :model="form" :rules="rules">
					<el-form-item label="" prop="userName">
						<el-input v-model="form.userName" placeholder="请输入申请人名字"></el-input>
					</el-form-item>
					<el-form-item label="" prop="phone">
						<el-input v-model="form.phone" placeholder="请输入您的联系电话"></el-input>
					</el-form-item>
					<el-form-item label="">
						<el-input
							v-model="form.contactRemark"
							type="textarea"
							:rows="2"
							placeholder="联系备注(例如：邮箱等信息)"
						></el-input>
					</el-form-item>
					<el-form-item label="" prop="applyRemark">
						<el-input
							v-model="form.applyRemark"
							type="textarea"
							:rows="4"
							placeholder="输入说明，让其他管理员了解此角色的用途和注意事项"
						></el-input>
					</el-form-item>
				</el-form>
				<div slot="footer" class="dialog-footer">
					<el-button @click="close">取 消</el-button>
					<el-button
						:loading="loading"
						:disabled="loading"
						type="primary"
						@click.native.prevent="submitForm"
					>
						确 定
					</el-button>
				</div>
			</el-dialog>
		</el-dialog>
	</div>
</template>

<script>
import {
	getMarketCategory,
	getMarketSearch,
	getMarketDetails,
	useApply
} from '@/api/modules/application-market';
export default {
	name: 'Index',
	data() {
		return {
			name: '',
			dialogTableVisible: false,
			innerVisible: false,
			categoryList: [],
			categoryId: '',
			details: {
				labelList: []
			},
			list: [],
			loading: false,
			searchName: '',
			form: {},
			pageNo: 1,
			styleObject: {
				padding: '0px',
				width: '100%'
			},
			rules: {
				userName: [{ required: true, message: '请输入申请人姓名	', trigger: 'blur' }],
				phone: [
					{ required: true, message: '请输入联系电话	', trigger: 'blur' },
					{ pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确', trigger: 'blur' }
				],
				applyRemark: [{ required: true, message: '请输入申请说明', trigger: 'blur' }]
			}
		};
	},
	created() {
		this.getMarketCategorys();
	},
	mounted() {
		this.$nextTick(() => {
			console.log(this.$refs.right.offsetWidth);
			let offsetWidth = this.$refs.right.offsetWidth;
			let padding = offsetWidth % 316;
			let width = offsetWidth - padding;
			console.log(padding);
			// let pad = padding / 4;
			this.styleObject.width = `${width}px `;
			this.styleObject.maring = '0 aotu';
		});
	},

	methods: {
		getMarketCategorys() {
			getMarketCategory().then(res => {
				this.categoryList = res.result || [];
				this.categoryList.unshift({ name: '全部', id: 'all' });
				this.categoryId = this.categoryList[0]?.id || '';
				this.getList();
			});
		},
		selectCategory(index) {
			this.categoryId = index;
			this.pageNo = 1;
			this.getList();
		},
		search() {
			this.searchName = this.name;
			this.pageNo = 1;
			this.getList();
		},
		searchClear() {
			this.searchName = '';
			this.pageNo = 1;
			this.getList();
		},
		load() {
			if (this.total > this.list.length) {
				this.pageNo += 1;
				this.getList();
			}
		},
		getList() {
			this.loading = true;
			getMarketSearch({
				categoryId: this.categoryId == 'all' ? '' : this.categoryId,
				name: this.searchName,
				pageNo: this.pageNo,
				pageSize: 20
			}).then(res => {
				this.loading = false;

				if (res.code !== 200) return;
				if (this.pageNo == 1) {
					this.list = res.result.records || {};
				} else {
					this.list = this.list.concat(res.result.records);
				}
				this.total = res.result.total;
			});
		},
		help() {
			window.open(this.details.helpDocUrl);
		},
		handleClick(item) {
			getMarketDetails(item.id).then(res => {
				this.details = res.result;
				this.dialogTableVisible = true;
			});
		},
		getApp() {
			this.innerVisible = true;
		},
		close() {
			this.innerVisible = false;
			this.form = {};
		},
		submitForm() {
			this.loading = true;
			this.$refs.formName.validate(valid => {
				if (valid) {
					let params = { ...this.form, applicationId: this.details.id };
					useApply(params).then(res => {
						if (res.code == 200 && res.success) {
							this.$message({
								message: '申请成功',
								type: 'success'
							});
							this.innerVisible = false;
							this.loading = false;
						} else {
							this.loading = false;
							this.$message({
								message: res.message,
								type: 'error'
							});
						}
					});
				} else {
					this.loading = false;
				}
			});
		}
	}
};
</script>

<style scoped lang="scss">
.application-market {
	background-color: #ffffff;
	//	margin: 24px;
	height: 100vh;
	// border-radius: 9px;
}
.mian {
	background-repeat: no-repeat;
	background-size: 100% 45%;
	// border-radius: 9px;
	padding: 21px 16px;
	min-width: 1080px;
	&-header {
		.header-box {
			display: flex;
			align-items: center;
			padding-bottom: 42px;
			&-img {
				width: 40px;
				height: 40px;
				border-radius: 9px;
			}
			&-text {
				margin-left: 16px;
				font-family: PingFang SC, PingFang SC;
				font-weight: 600;
				font-size: 20px;
				color: $primaryTextColor;
				line-height: 28px;
			}
		}
	}
	&-search {
		margin-bottom: 86px;
		min-width: 624px;
		.title {
			width: 100%;
			font-family: PingFang SC, PingFang SC;
			font-weight: 800;
			font-size: 36px;
			line-height: 38px;
			text-align: center;
			background: linear-gradient(8.365639121815596deg, #1991ff 0%, #553edf 100%);
			-webkit-background-clip: text;
			-webkit-text-fill-color: transparent;
		}
		.input {
			display: flex;
			justify-content: center;
			margin-top: 24px;
			padding: 0 32px;
			.el-input {
				width: 100%;
				height: 56px;
				box-shadow: 0px 3px 12px 0px rgba(131, 115, 238, 0.25);
				border-radius: 53px;
				border-color: transparent !important;
				::v-deep .el-input__inner::placeholder {
					font-size: 16px;
				}
				::v-deep .el-input__prefix {
					left: 15px;
					top: 2px;
					i {
						font-size: 20px;
					}
				}
				::v-deep .el-input__suffix {
					right: 15px;
					top: 4px;
					i {
						font-size: 20px;
					}
				}
				::v-deep .el-input__inner {
					border-radius: 53px !important;
					height: 56px;
					font-size: 16px;
					padding: 0 32px 0 40px;
					border-color: transparent !important;
					line-height: 56px;
					&:hover {
						border-color: transparent !important;
					}
				}
			}
		}
	}
	&-content {
		display: flex;
		padding: 20px 20px 0;
		.left {
			&-title {
				font-family: PingFang SC, PingFang SC;
				font-weight: 800;
				font-size: 18px;
				color: $primaryTextColor;
				line-height: 24px;
				padding: 11px;
			}
			.el-menu {
				background-color: transparent;
				border: none;
				padding-left: 9px !important;
				.el-menu-item {
					padding-left: 9px !important;
					width: 171px;
					height: 32px;
					border-radius: 6px;
					font-family: PingFang SC, PingFang SC;
					font-weight: 400;
					font-size: 16px;
					color: #2f446b;
					line-height: 24px;
					margin-bottom: 16px;
				}
				.el-menu-item.is-active {
					width: 171px;
					height: 32px;
					border-radius: 6px;
					background: linear-gradient(95deg, #badeff 0%, rgba(175, 216, 255, 0) 100%);
					font-family: PingFang SC, PingFang SC;
					font-weight: 400;
					font-size: 16px;
					color: var(--brand-6);
					line-height: 24px;
					text-align: left;
				}
			}
		}
		.right {
			flex: 1;
			// padding-left: 50px;
			display: flex;
			justify-content: center;
			&-title {
				font-family: PingFang SC, PingFang SC;
				font-weight: 600;
				font-size: 24px;
				color: $primaryTextColor;
				line-height: 32px;
				margin-bottom: 8px;
			}
			&-dec {
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 14px;
				color: #737a94;
				line-height: 22px;
				margin-bottom: 24px;
			}
			.list {
				width: 100%;
				height: calc(100vh - 380px);
				overflow-y: auto;
				display: flex;
				padding-bottom: 60px;
				flex-wrap: wrap;
				.box {
					margin: 0 10px 15px;
					cursor: pointer;
					border: 1px solid #e3ebf2;
					height: 250px;
					border-radius: 9px;
					&-img {
						border-bottom: 1px solid #e3ebf2;
						width: 290px;
						height: 163px;
						border-radius: 9px 9px 0px 0px;
					}
					&-content {
						width: 290px;
						background: #ffffff;
						border-radius: 0px 0px 9px 9px;
						padding: 5px 10px;
						&-title {
							width: 271px;
							height: 24px;
							font-family: PingFang SC, PingFang SC;
							font-weight: 500;
							font-size: 16px;
							color: $primaryTextColor;
							line-height: 24px;
							overflow: hidden;
							text-overflow: ellipsis;
							white-space: nowrap;
							margin-bottom: 2px;
						}
						&-dec {
							width: 271px;
							height: 44px;
							font-family: PingFang SC, PingFang SC;
							font-weight: 400;
							font-size: 14px;
							color: #737a94;
							line-height: 22px;
							display: -webkit-box;
							-webkit-line-clamp: 2;
							-webkit-box-orient: vertical;
							overflow: hidden;
							text-overflow: ellipsis;
						}
					}
				}
			}
		}
	}
}
::v-deep .details-dialog {
	max-height: calc(100vh - 223px);
	min-height: 688px;
	min-width: 1000px;
	border-radius: 12px !important;
	margin-bottom: 0px !important;
	::v-deep .el-dialog__body {
		padding: 12px 20px;
	}
}
.details {
	&-header {
		display: flex;
		padding: 0 24px 24px;
		border-bottom: 1px solid #ebebeb;
		&-left {
			img {
				width: 110px;
				height: 110px;
				border-radius: 12px;
			}
			margin-right: 16px;
		}
		&-right {
			.details-header-box {
				display: flex;
				align-items: center;
				.details-tags {
					margin-left: 5px;
					padding: 0 5px;
					background: #fff3d0;
					border-radius: 6px 6px 6px 6px;
					font-family: PingFang SC, PingFang SC;
					font-weight: 400;
					font-size: 14px;
					color: #795c31;
					line-height: 22px;
				}
			}
			.details-title {
				font-family: PingFang SC, PingFang SC;
				font-weight: 600;
				font-size: 24px;
				color: $primaryTextColor;
				line-height: 32px;
			}
			.details-dec {
				margin-top: 8px;
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 14px;
				color: #737a94;
				margin-bottom: 8px;
				line-height: 22px;
			}
		}
	}
	&-content {
		padding: 30px 12px;
		display: flex;
		&-left {
			flex: 1;
			height: 380px;
			padding: 0 0 20px;
			overflow-y: auto;
		}
		&-right {
			margin-left: 44px;
			width: 310px;
			.details-box {
				margin-bottom: 38px;
				&-lable {
					font-family: PingFang SC, PingFang SC;
					font-weight: 800;
					font-size: 16px;
					color: $primaryTextColor;
					margin-bottom: 14px;
					line-height: 22px;
				}
				&-btn {
					.icon {
						color: var(--brand-6);
						margin-right: 5px;
					}
				}
				&-tags {
					display: flex;
					flex-wrap: wrap;
					.tags-item {
						padding: 2px 10px;
						background: #ffffff;
						border-radius: 6px 6px 6px 6px;
						border: 1px solid #d9d9d9;
						margin-right: 4px;
						margin-bottom: 8px;
						font-family: PingFang SC, PingFang SC;
						font-weight: 400;
						font-size: 12px;
						color: $primaryTextColor;
						line-height: 20px;
					}
				}
				&-img {
					display: flex;
					align-items: center;
					img {
						width: 36px;
						height: 36px;
						background: #ffffff;
						// border-radius: 6px 6px 6px 6px;
						// border: 1px solid #f0f0f0;
					}
					&-name {
						font-family: PingFang SC, PingFang SC;
						font-weight: 400;
						font-size: 16px;
						margin-left: 12px;
						color: #000000;
						line-height: 24px;
					}
				}
			}
		}
	}
}
.form-title {
	font-family: PingFang SC, PingFang SC;
	font-weight: 400;
	font-size: 14px;
	color: #2f446b;
	line-height: 22px;
	margin-bottom: 8px;
}
::v-deep .form-dialog {
	border-radius: 12px;
	.el-dialog__body {
		padding: 0 20px;
	}
}
</style>
