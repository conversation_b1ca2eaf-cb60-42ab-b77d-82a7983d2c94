<template>
	<div class="dev">
		<!--  配置弹窗  -->
		<div>
			<div class="dev-title">1.组织选择弹窗</div>
			<div>
				<el-button type="primary" @click="visible = true">弹窗人员选择组件</el-button>
				<!--:init-values="initValues" -->
				<orgPersonnelDialog
					:can-select-depart="true"
					:visible="visible"
					:data-source="dataSource"
					:disable-all="false"
					:is-all="false"
					:disable-org="false"
					:init-value="[]"
					:need-all-data="true"
					@change="changeSelect"
					@changeRadio="changeRadio"
					@sure="sure"
					@close="close"
				></orgPersonnelDialog>
			</div>
		</div>
		<!--  上传组件  -->
		<div>
			<div class="dev-title">2.文件上传（标签模式）</div>
			<uploadFile
				v-model="ids"
				:other-params="otherParams"
				:can-download="true"
				:multiple="true"
				mode="file"
				:limit="3"
			/>
		</div>
		<div>
			<div class="dev-title">3.文件上传（图片模式）</div>
			<uploadFile
				v-model="ids1"
				:other-params="otherParams"
				:can-download="true"
				:multiple="true"
				mode="image"
				:limit="3"
			/>
		</div>
		<div>
			<div class="dev-title">4.上传（自定义样式）</div>
			<uploadFile v-model="ids2" :other-params="otherParams" :multiple="true" :custom-file="true">
				<div slot="custom-file" slot-scope="upLoadFile">
					<div v-for="(item, index) of upLoadFile" :key="index">{{ item }}</div>
				</div>
				<el-button slot="custom-button" type="primary">自定义上传</el-button>
			</uploadFile>
		</div>
		<!--  回显组件  -->
		<div>
			<div class="dev-title">5.回显组件</div>
			<FileById :can-down-load="true" :value="ids3" :size="120" :can-pre-view="true" />
		</div>
		<div>
			<div class="dev-title">6.浏览器消息通知</div>
			<el-button class="btn" @click="test">通知</el-button>
		</div>
		<div>
			<div class="dev-title">8.分页下拉搜索</div>
			<div class="dev-table-select" @click="openSelectTable">
				<span v-if="tableSelectValue.length > 0">{{ tableSelectValue.join(',') }}</span>
				<span v-else class="placeholder">请选择数据</span>
			</div>
			<tableSelect
				ref="tableSelect"
				:table-header="tableColumn"
				:table-data="tableData"
			></tableSelect>
		</div>
		<div style="width: 100%">
			<div class="dev-title">9.编辑表格</div>
			<div style="height: 300px; width: 100%">
				<editTable
					:can-edit="true"
					:show-checkbox="true"
					:show-delete="true"
					:show-add="true"
					:sort="true"
					:options="{
						subId: subId
					}"
					:table-data="tableData"
					:table-column="tableColumn"
				></editTable>
			</div>
		</div>
		<div>
			<div class="dev-title">10.节点下一步选择</div>
			<button type="primary" @click="next">下一步</button>
			<flowNext ref="flowNext" @nextSelected="nextSelected"></flowNext>
		</div>
		<div>
			<div class="dev-title">11.测试待办公用弹窗</div>
			<button type="primary" @click="openDialog2">打开</button>
			<waitDetail ref="waitDetail"></waitDetail>
		</div>
		<div style="width: 100%">
			<div class="dev-title">12.富文本</div>
			<tinymce
				ref="editor"
				v-model="htmlText"
				style="width: 100%; height: auto; border-radius: 22px; padding: 0 20px 0 0"
				:height="750"
			></tinymce>
		</div>
	</div>
</template>

<script>
import orgPersonnelDialog from '@/components/org-personnel-dialog';
import uploadFile from '@/components/upload-file';
import editTable from '@/components/edit-table';
import tableSelect from '@/components/table-select';
import flowNext from '@/components/flow-next';
import { showNotification } from '@/utils';
import waitDetail from '@/components/wait-detail/index.vue';
import { mapState } from 'vuex';
export default {
	name: 'DevIndex',
	components: {
		orgPersonnelDialog,
		uploadFile,
		editTable,
		tableSelect,
		flowNext,
		waitDetail
	},
	data() {
		return {
			htmlText: '',
			tableSelectValue: [],
			showWuJie: false,
			initValues: [{ dataType: 'user', title: '王家梅', id: '1731598352319598593' }],
			dataSource: ['user', 'depart'], // 'label''depart',
			visible: false,
			/**上传组件*/
			otherParams: {
				applicationId: '1729387429589024769',
				dataId: '1729387429589024769',
				fieldCode: 'logoUrl',
				moduleCode: 'sysApplication'
			},
			ids: '', //,1730107568341004290
			ids1: '', //,1730107568341004290
			ids2: '', //,1730107568341004290
			ids3: '05467b86-e2f4-4fd3-9663-8ec653a3221c', //,1730107568341004290
			subId: [
				{
					label: '测试1',
					value: 'test1'
				},
				{
					label: '测试2',
					value: 'test2'
				}
			],
			tableData: [
				{
					checkDate: '2024-10-09',
					controlPoints: '测试1',
					checkOrg: '智胜集成01',
					subId: 'test1'
				},
				{
					checkDate: '2024-10-08',
					controlPoints: '测试2',
					checkOrg: '智胜集成02',
					subId: 'test2'
				}
			],
			tableColumn: [
				{
					prop: 'checkDate',
					label: '检查日期',
					type: 'date',
					minWidth: '260',
					formInputConfig: {
						format: 'yyyy-MM-dd',
						valueFormat: 'yyyy-MM-dd'
					}
				},
				{
					prop: 'subId',
					label: '检查子项',
					minWidth: '180',
					type: 'select'
				},
				{
					prop: 'controlPoints',
					label: '检查内容',
					isReadOnly: true,
					type: 'text',
					minWidth: '240',
					formInputConfig: {
						autosize: {
							minRows: 10
						},
						maxLength: 2000,
						type: 'textarea'
					}
				},
				{
					prop: 'checkOrg',
					label: '组织单位',
					type: 'input',
					minWidth: '180',
					formInputConfig: {
						maxLength: 50
					}
				},
				{
					prop: 'file',
					label: '附件',
					type: 'file',
					minWidth: '180'
				}
			]
		};
	},
	computed: {
		...mapState('user', ['waitConfig']),
		// 打开待办的方式
		openDetailType() {
			return this.waitConfig.customerTaskMenu
				? this.waitConfig.customerTaskMenu.detailOpenType
				: '1';
		}
	},
	methods: {
		test() {
			showNotification('Jartto的消息通知', {
				dir: 'rtl',
				body: '这是一个消息,通过 Web Notification 发送,作为测试!',
				icon: 'https://raw.githubusercontent.com/chenfengyanyu/my-web-accumulation/master/images/logo.jpeg'
			});
		},
		openDialog2() {
			// let data = localStorage.getItem('temp');
			// this.$refs.waitDetail.open(JSON.parse(data));
		},
		/**确认节点选择*/
		nextSelected(form) {
			console.log('节点选中--------', form);
		},
		/**打开下一步节点*/
		async next() {
			let boolean = await this.$refs.flowNext.check('AFenergy-om-safetyins-template', {});
			if (boolean) {
				this.$message.success('提交成功');
			}
		},
		/**打开弹窗*/
		openSelectTable() {
			this.$refs.tableSelect.open();
		},
		/**确定*/
		sure() {
			this.visible = false;
		},
		/**取消*/
		close() {
			this.visible = false;
		},
		/**人员选择发生变化*/
		changeSelect(val) {
			console.log('新值', val);
		},
		changeRadio(val) {
			console.log(val);
		}
	}
};
</script>

<style scoped lang="scss">
.dev {
	height: 100%;
	margin: 0 20px;
	padding: 20px;
	background: #ffffff;
	@include flexBox(flex-start, flex-start);
	flex-wrap: wrap;
	overflow-y: auto;
	& > div {
		width: 50%;
		@include flexBox();
		flex-direction: column;
	}
	&-title {
		margin: 20px 0 10px;
		text-align: left;
		width: 100%;
	}
	.btn {
		margin-top: 30px;
	}
}
.dev-table-select {
	border: 1px solid #f2f2f2;
	border-radius: 6px;
	height: 40px;
	width: 300px;
	padding: 5px 8px;
	font-size: 14px;
	cursor: pointer;
	display: flex;
	align-items: center;
}
.placeholder {
	color: $subTextColor;
}
</style>
