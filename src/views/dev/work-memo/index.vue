<template>
	<el-dialog class="workmemo-el-dialog" :visible.sync="visible" :title="title" @close="handleClose">
		<situationList :completions-data="completionsData"></situationList>
	</el-dialog>
</template>

<script>
import situationList from '@/views/dev/work-memo/situation-list/situation-list.vue';
export default {
	name: 'WorkMemo',
	components: {
		situationList
	},
	props: {
		visible: {
			type: Boolean,
			default: () => {
				return false;
			}
		},
		title: {
			type: String,
			default: () => {
				return '完成情况';
			}
		},
		completionsData: {
			type: Array,
			default: () => {
				return [];
			}
		}
	},
	methods: {
		handleClose() {
			// 处理关闭事件
			this.$emit('update:visible', false);
		}
	}
};
</script>

<style scoped lang="scss">
.workmemo-el-dialog {
	display: flex;
	justify-content: center;
	align-items: center;
	height: 100vh;
	::v-deep .el-dialog {
		width: 520px;
		height: 322px;
		max-width: 80vw;
		margin-top: 10vh !important;
		box-shadow: 0px 8px 10px rgba(0, 0, 0, 0.08), 0px 16px 24px rgba(0, 0, 0, 0.04),
			0px 6px 30px rgba(0, 0, 0, 0.05);
		border-radius: 16px;
		margin-bottom: 0;
	}
	::v-deep .el-dialog__header {
		margin: 12px 30px;
		padding: 10px 0;
		.el-dialog__headerbtn {
			top: 26px;
			right: 30px;
		}
		.el-dialog__title {
			font-size: 18px;
			font-weight: 700;
			color: #303133;
			line-height: 24px;
		}
	}
	::v-deep .el-dialog__body {
		padding: 0 30px;
		height: 230px;
		overflow: hidden;
		.el-dialog__content {
			font-size: 14px;
			color: #606266;
		}
	}
}
</style>
