<template>
	<div ref="scrollContainer" class="scroll-list">
		<el-table
			:data="completionsData"
			:cell-class-name="cellClassName"
			:row-style="{ height: '46px' }"
			style="width: 100%"
			show-header="false"
		>
			<el-table-column width="180" label="姓名">
				<template slot-scope="scope">
					<div class="list">
						<div class="list-icon">
							{{ scope.row.user.slice(-1) }}
							<i v-if="scope.row.status === '1'" class="status-icon">
								<svg-icon icon-class="success" class-name="completed-success-icon"></svg-icon>
							</i>
						</div>
						<div class="list-desc">{{ scope.row.user }}</div>
					</div>
				</template>
			</el-table-column>
			<el-table-column width="auto" label="状态" :formatter="statusFormatter"></el-table-column>
			<el-table-column label="时间">
				<template slot-scope="scope">
					<div>
						<span v-if="scope.row.status === '1'">
							{{ scope.row.completionsDate }} {{ scope.row.completionstime }}
						</span>
					</div>
				</template>
			</el-table-column>
		</el-table>
	</div>
</template>
<script>
export default {
	name: 'SituationList',
	props: {
		completionsData: {
			type: Array,
			default: () => []
		}
	},
	data() {
		return {
			scrollTimeout: null
		};
	},
	mounted() {
		this.$refs.scrollContainer.addEventListener('scroll', this.handleScroll);
	},
	beforeDestroy() {
		this.$refs.scrollContainer.removeEventListener('scroll', this.handleScroll);
	},
	methods: {
		statusFormatter(row) {
			return row.status === '1' ? '已完成' : '未完成';
		},
		cellClassName({ row, column }) {
			let className = 'el-table_column';
			if (row.status === '1' && column.label == '状态') {
				className += ' completedStatus';
			} else if (row.status === '0' && column.label == '状态') {
				className += ' uncompletedStatus';
			} else if (column.label == '时间') {
				className += ' completedTime';
			}
			return className;
		},
		handleScroll() {
			const scrollContainer = this.$refs.scrollContainer;
			if (scrollContainer) {
				scrollContainer.classList.add('scrolling');
				clearTimeout(this.scrollTimeout);
				this.scrollTimeout = setTimeout(() => {
					scrollContainer.classList.remove('scrolling');
				}, 2000);
			}
		}
	}
};
</script>

<style scoped lang="scss">
/**滚动列表*/
.scroll-list {
	max-height: 100%;
	overflow-y: overlay;
	width: 100%;
	scrollbar-width: none;
	-ms-overflow-style: none;
	::-webkit-scrollbar {
		width: 0;
		height: 0;
	}
	&.scrolling {
		scrollbar-width: thin;
		-ms-overflow-style: auto;
		overflow-y: overlay;
	}
}

/**隐藏表头*/
::v-deep .el-table__header-wrapper {
	display: none;
}
::v-deep .el-table_column {
	padding: 0;
	.cell {
		padding: 0;
	}
}
.el-table__body-wrapper {
	display: flex;
	width: 100%;
	.el-table__body {
		display: flex;
		width: 100%;
	}
	.el-table__row {
		display: flex;
		width: 100%;
		.el-table__cell {
			display: flex;
			align-items: center;
			justify-content: center;
		}
	}
}
::v-deep .completedStatus {
	font-size: 13px;
	color: #40c274;
}
::v-deep .uncompletedStatus {
	font-size: 13px;
	color: #d1d3db;
}
::v-deep .completedTime {
	font-size: 12px;
	color: #737a94;
	span {
		float: right;
	}
}
.list {
	display: flex;
	@include flexBox(flex-start);
	&-icon {
		flex-shrink: 0;
		width: 30px;
		height: 30px;
		background: var(--brand-6);
		border-radius: $borderRadius;
		margin-right: 8px;
		font-size: 14px;
		font-weight: 500;
		color: #ffffff;
		line-height: 26px;
		@include flexBox();
		.status-icon {
			position: absolute;
			left: 23px;
			bottom: 8px;
			width: 10px;
			height: 10px;
			background-color: #40c274;
			border-radius: 50%;
			display: flex;
			align-items: center;
			justify-content: center;
			.completed-success-icon {
				width: 6px;
				height: 6px;
			}
		}
	}
	&-desc {
		flex: 1;
		font-size: medium;
		font-weight: 500;
		color: $primaryTextColor;
		line-height: 24px;
		@include aLineEllipse;
	}
}
</style>
