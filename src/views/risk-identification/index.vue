<template>
	<div class="risk-identification">
		<div ref="md" class="md">
			<v-md-preview v-for="(t, i) in processedAnswer" :key="i" :text="t"></v-md-preview>
		</div>
		<div v-loading="loading" class="head">
			<el-select v-model="select" multiple placeholder="请选择选择" @change="handleSelect">
				<el-option
					v-for="item in options"
					:key="item.id"
					:label="item.sceneName"
					:value="item.id"
				></el-option>
			</el-select>
			<div class="upd">
				<el-upload
					ref="upd"
					class="upload-demo"
					accept=".pdf,.doc,.docx,.txt"
					multiple
					action=""
					:limit="999"
					:on-change="handleChange"
					:data="params"
					:show-file-list="false"
					:auto-upload="false"
				>
					<el-button size="small" type="primary">选择文件</el-button>
				</el-upload>
			</div>
			<div class="file">
				<span v-for="(t, i) in fileList" :key="i">{{ t.name }}</span>
			</div>
			<el-button class="btn" @click="handleSave">提 交</el-button>
		</div>
		<div class="step">
			<el-steps :active="active" finish-status="success">
				<el-step title="文件上传"></el-step>
				<el-step title="文件解析"></el-step>
				<el-step title="风险推理"></el-step>
				<el-step title="整理答案"></el-step>
				<el-step title="完成"></el-step>
			</el-steps>
		</div>
	</div>
</template>

<script>
import { sceneSelect, uploadDocs, riskIdentify } from '@/api/modules/risk-identification.js';
import { CoosEventTypes } from '@/utils/bus';

export default {
	name: 'RiskIdentification',
	data() {
		return {
			loading: false,
			options: [],
			select: [],
			params: {
				sceneIds: ''
			},
			active: 0,
			fileList: [],
			metadata: {},
			socket: null,
			processedAnswer: []
		};
	},
	created() {
		this.sceneSelect();
	},
	methods: {
		riskIdentify() {
			const { sceneIds, metadataId } = this.metadata;
			riskIdentify({ sceneIds, metadataId }).then(res => {
				if (res.code == 200) {
					this.active = 3;
					this.processedAnswer.push(res.result.answerInfo.answer);
					this.active = 5;
					this.fileList = [];
					this.$refs.upd.clearFiles();
					this.loading = false;
					this.$nextTick(() => {
						this.$refs.md.scrollTop = this.$refs.md.scrollHeight;
					});
				}
			});
		},
		msg(m) {
			this.active = 2;
			if (m.dataVal.metadataId == this.metadata.metadataId) {
				if (m.dataVal.docsDealStatus == '1') {
					this.riskIdentify();
				} else {
					this.loading = false;
					this.active = 0;
					this.$message.error('数据向量化失败！');
				}
			}
		},
		handleChange(file, fileList) {
			this.active = 0;
			this.fileList = fileList;
		},
		handleSave() {
			if (!this.fileList || this.fileList.length == 0) {
				this.$message({
					showClose: true,
					message: '请先选择文件',
					type: 'error'
				});
				return;
			}
			if (!this.select || this.select.length == 0) {
				this.$message({
					showClose: true,
					message: '请先选择场景',
					type: 'error'
				});
				return;
			}
			let formData = new FormData();
			this.fileList.forEach(file => formData.append('fileList', file.raw));
			formData.append('sceneIds', this.params.sceneIds);
			this.loading = true;
			uploadDocs(formData).then(res => {
				if (res.code == 200) {
					this.metadata = res.result;
					this.active = 1;
					this._BUS.$on(CoosEventTypes.onSocketMessage, this.msg);
				} else {
					this.loading = false;
					this.active = 0;
					this.$message.error(res.message || '操作失败，请稍后在试！');
				}
			});
		},
		sceneSelect() {
			sceneSelect().then(res => {
				if (res.code == 200) {
					this.options = res.result;
				}
			});
		},
		handleSelect(d) {
			this.active = 0;
			this.params.sceneIds = d.join(',');
		}
	}
};
</script>

<style lang="scss" scoped>
.risk-identification {
	height: calc(100% - 20px);
	padding: 10px;
	background: #f2f6fc;
	width: 100%;
	.head {
		margin-top: 10px;
		display: flex;
		height: 80px;
		background-color: #fff;
		border-radius: 8px;
		align-items: center;
		box-sizing: border-box;
		padding: 0 20px;
		.upd {
			margin: 0 20px;
		}
		.file {
			display: -webkit-box; /* 将对象作为弹性伸缩盒子模型显示 */
			-webkit-box-orient: vertical; /* 设置或检索伸缩盒对象的子元素的排列方式 */
			-webkit-line-clamp: 2; /* 限制在一个块元素显示的文本的行数 */
			overflow: hidden; /* 隐藏溢出的内容 */
			font-size: 16px;
			color: #606266;
			width: calc(100% - 410px);
			.span {
				margin: 5px 5px 0 0;
			}
		}
		.btn {
			margin-left: auto;
		}
	}
	.step {
		margin-top: 10px;
		padding: 10px 20px;
		background-color: #fff;
		border-radius: 8px;
	}
	.md {
		background-color: #fff;
		height: calc(100% - 200px);
		padding: 10px 20px;
		background-color: #fff;
		border-radius: 8px;
		overflow: auto;
	}
}
</style>
