<template>
	<div class="svg">
		<div v-for="(svg, index) of svgArr" :key="index" class="svg-item">
			<svg-icon :icon-class="svg" class="svg-item-icon" @click="copy(svg)"></svg-icon>
			<div class="svg-item-title">{{ svg }}</div>
		</div>
	</div>
</template>

<script>
export default {
	name: 'Index',
	data() {
		return {
			svgArr: []
		};
	},
	mounted() {
		const req = require.context('@/icons/svg', true, /\.svg$/);
		let svgArr = req
			.keys()
			.map(req)
			.map(svg => {
				return svg.default.id.replace('icon-', '');
			});
		this.svgArr = svgArr;
	},
	methods: {
		copy(svg) {
			let text = `<svg-icon icon-class="${svg}"></svg-icon>`;
			navigator.clipboard.writeText(text).then(() => {
				this.$message.success('复制成功！');
			});
		}
	}
};
</script>

<style scoped lang="scss">
.svg {
	border-radius: 16px;
	background: #ffffff;
	height: 100%;
	margin: 0 32px;
	padding: 20px;
	@include flexBox(flex-start, flex-start);
	flex-wrap: wrap;
	&-item {
		cursor: pointer;
		margin: 20px;
		width: 40px;
		@include flexBox();
		flex-direction: column;
		&-icon {
			width: 24px;
			height: 24px;
		}
		&-title {
			margin-top: 8px;
			font-size: 12px;
			color: #c3c3c3;
		}
	}
}
</style>
