<template>
	<div class="active-form">
		<div class="title">
			<div class="title-left">
				<FileById class="title-left-img" :size="56" :value="detail.logo"></FileById>
				<div class="title-left-text">
					<div class="top">表单名称：{{ detail.name }}</div>
					<div class="bottom">表单编号：{{ detail.code }}</div>
				</div>
			</div>
			<div class="title-right" style="flex: 1" :style="{ height: isShowOpen ? '' : '56px' }">
				<!-- <span>关键词搜索：</span>
				<el-input
					v-model="form.param"
					class="title-right-input"
					placeholder="请输入"
					@input="search"
				></el-input> -->
				<template v-if="showSearch">
					<SearchFormRender
						ref="vFormRefSearch"
						class="searchBox"
						is-search-views
						:is-show-open="isShowOpen"
						:min-height="34"
						:max-height="0"
						:form-json="searchFormJson"
						@searchClick="handleSearch"
						@resetClick="handleReset"
					></SearchFormRender>
				</template>
			</div>
		</div>
		<div class="content">
			<div class="content-title">
				<div class="add" @click="openPopup('newAdd')">
					<svg-icon icon-class="white-add" class="svg"></svg-icon>
					<span>添加数据</span>
				</div>
			</div>
			<template v-if="false">
				<div class="content-title">
					<div class="print" @click="openPopup('printView')">
						<span>预览</span>
					</div>
				</div>
			</template>
		</div>
		<div class="table">
			<dynamicList
				ref="dynamicList"
				:loading="tableLoading"
				:table-head="tableHead"
				:table-data="tableData"
				@openPopup="openPopup"
			></dynamicList>
		</div>
		<div v-if="total > 0" class="footer">
			<div class="footer-total">已选{{ selectArr.length }}条数据</div>
			<el-pagination
				class="desk-el-pagination"
				background
				:current-page="form.pageNo"
				:page-sizes="[10, 20, 50, 100]"
				:page-size="form.pageSize"
				layout="total, sizes, ->, prev, pager, next, jumper"
				:total="total"
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
			></el-pagination>
		</div>
		<addData
			ref="addData"
			:is-process-enabled="isProcessEnabled"
			:form-json="formJson"
			:detail-id="detailId"
			:process-instance-id="processInstanceId"
			:ocr-auto-form="ocrAutoForm"
			@update="update"
		>
			<!--			<div slot="title">标题</div>-->
			<!--			<div slot="customForm">内容</div>-->
		</addData>
		<template v-if="false">
			<PrintView
				ref="printView"
				:form-json="formJson"
				:detail-id="detailId"
				:process-instance-id="processInstanceId"
			></PrintView>
		</template>
	</div>
</template>

<script>
import dynamicList from '@/components/dynamic-list';
import {
	getFormDetail,
	getFormTable,
	getFormHead,
	formRevoke,
	formDelete
} from '@/api/modules/form-page';
import { debounce, processList } from '@/utils';
import addData from '@/views/form-page/components/add-data';
import { getItem } from '@/wile-fire/ui/util/storageHelper';
import { mapGetters } from 'vuex';
import md5 from 'crypto-js/md5';
import SearchFormRender from '@/components/search-form-render/index.vue';
import PrintView from './components/print-view.vue';
import { CoosEventTypes } from '@/utils/bus';

export default {
	name: 'FormPage',
	components: {
		dynamicList,
		addData,
		SearchFormRender,
		PrintView
	},
	data() {
		return {
			clickState: false,
			imDraw: false,
			type: 'newAdd', // 弹窗类型
			selectArr: [], // 选中的数据
			form: {
				pageNo: 1, //当当前页码
				param: [], // 搜索关键词
				pageSize: 10 // 分页大小
			},
			total: 0, //总条数
			detail: {}, //表单详情
			tableLoading: false,
			isProcessEnabled: false, //是否有流程设计
			formJson: {}, // 动态表单渲染器参数
			tableData: [], // 表格数据
			detailId: '', // 详情ID
			tableHead: [], // 表格头部
			ocrAutoForm: false, // 是否有orc识别按钮
			processInstanceId: '',
			statusObj: {
				0: '草稿',
				1: '已提交',
				2: '审核通过',
				3: '审核驳回',
				4: '审核中',
				5: '撤回',
				6: '审核终止'
			},
			allFieldWidgets: {},
			selectedQueryColumns: [],
			searchFormJson: {},
			isShowOpen: false,
			showSearch: false
		};
	},
	computed: {
		...mapGetters(['userInfo'])
	},
	watch: {
		$route(newVal) {
			if (this.$route.path == '/form-page' && newVal.query.id) {
				this.detail = {};
				// this.formJson = {};
				this.showSearch = false;
				this.init();
			}
		}
	},
	mounted() {
		if (this.$route.query.id) {
			this.init();
		}
	},
	methods: {
		init() {
			this.getDetail(); // 获取表单详情
			this.getHead(1); // 获取表单动态设计的表单配置
			this.getHead(2); // 获取表单动态设计的列表配置
			this.getTableData(); // 获取表格数据
		},
		// 打开弹窗
		openPopup(type, row = {}) {
			console.log(row.id, '111111');
			this.detailId = row?.id || '';
			this.processInstanceId = row.processInstanceId;
			if (type === 'revoke') {
				// 撤回
				formRevoke(this.$route.query.id, row.id || row).then(res => {
					if (res.code === 200) {
						this.getTableData();
						this.$message.success('已撤回！');
					} else {
						this.$message.error(res.message);
					}
				});
			} else if (type === 'delete') {
				// 删除
				this.tableLoading = true;
				formDelete(row)
					.then(res => {
						this.tableLoading = false;
						if (res.code === 200) {
							this.getTableData();
							this.$message.success('已删除！');
						} else {
							this.$message.error('删除失败！');
						}
					})
					.catch(() => {
						this.tableLoading = false;
					});
			} else if (type === 'printView') {
				this.$refs.printView.open('detail');
			} else {
				// 新增和编辑
				if (type === 'detail') {
					this.ocrAutoForm = false;
				} else {
					this.ocrAutoForm = true;
				}
				// $nextTick 确保props数据更新
				this.$nextTick(() => {
					this.$refs.addData.open(type);
				});
			}
		},
		// 获取表单详情
		getDetail() {
			getFormDetail(this.$route.query.id).then(res => {
				if (res.code === 200) {
					this.detail = res.result || {};
					this.ocrAutoForm = JSON.parse(res.result.setting)
						? JSON.parse(res.result.setting).ocrAutoForm
						: false;
					this.isProcessEnabled = res.result.isProcessEnabled || false;
				} else {
					this.$message.error(res.message);
				}
			});
		},
		// 获取列表的设计数据
		getHead(designType) {
			getFormHead({ formId: this.$route.query.id, designType }).then(res => {
				if (res.code === 200) {
					if (res.result[0] && res.result[0].configJson) {
						let data = res.result;
						const formJson = JSON.parse(data[0].configJson);
						if (designType === 2) {
							let optionalColumns = formJson.optionalColumns || [];
							let resColums = processList(optionalColumns, 'pcChecked', 'pcSort');
							this.tableHead = resColums;
							this.initCoos(this.tableHead);
							this.isShowOpen = false;
							// 设置搜索字段
							let widgetList = processList(optionalColumns, 'pcQueryChecked', 'pcQuerySort');
							widgetList = widgetList.map(item => {
								// 当搜索时关闭所有表单验证
								item.fields.options.required = false;
								// 将隐藏的字段展示到搜索配置上
								item.fields.options.hidden = false;
								return {
									...item.fields,
									formItemFlag: true
								};
							});
							// 如果 搜索字段大于0个,就需要搜索
							if (widgetList.length > 0) {
								this.searchFormJson = {
									widgetList
								};
								this.showSearch = true;
								// 由于加载表单详情表单名称,编号 是异步的,会导致加载搜索框时会闪动 延迟一会渲染搜索
								this.$nextTick(() => {
									setTimeout(() => {
										this.$refs.vFormRefSearch.setFormJson(this.searchFormJson);
									}, 200);
								});
							}
						} else {
							// let data = res.result.find(item => {
							// 	return item.isH5 === 0;
							// });
							const config = JSON.parse(data[0].configJson);
							const { widgetList } = config;
							if (widgetList) {
								const wdilist = this.traverseArray(widgetList);
								config.widgetList = wdilist;
							}
							config.formConfig.labelWidth = 120;
							this.formJson = { ...config };
						}
					}
				} else {
					this.$message.error(res.message);
				}
			});
		},
		traverseArray(widgetList) {
			for (let item of widgetList) {
				if (Array.isArray(item.widgetList)) {
					this.traverseArray(item.widgetList); // 递归遍历子数组
				} else if (Array.isArray(item.cols)) {
					this.traverseArray(item.cols);
				} else {
					if (item.type == 'checkbox' || item.type == 'radio' || item.type == 'select') {
						if (item.options?.optionItems.length > 0) {
							const optionItems = item.options?.optionItems || [];
							item.options.optionItems = optionItems.map(v => {
								let it = { ...v };
								// 确保值为字符串
								if (v.value) {
									it.value = String(v.value);
								}
								return it;
							});
						}
					}
				}
			}
			return widgetList;
		},
		/**初始化coos助手的数据*/
		initCoos(tableHead) {
			let fields = [];
			tableHead.forEach(item => {
				// 只查询文字字段
				if (item.showMode === 1) {
					fields.push({
						column: `a.data_json ->> '$.${item.field}'`,
						comments: item.desc
					});
				}
			});
			let arr = [
				{
					tableName: 'form_custom_data',
					alias: 'a',
					fields
				}
			];
			let url = decodeURIComponent(this.$route.fullPath);
			let moduleId = md5(url).toString();
			let chatObjectId = moduleId;
			let listParams = {
				tableConfig: JSON.stringify(arr), // 表结构
				// relation, // 表关系
				sessionId: moduleId,
				moduleId, // 保存会话记录的依据
				createBy: this.userInfo.id, // 创建人
				tenantId: getItem('tenantId') // 租户
			};
			let otherParams = {
				type: 2, //动态表单固定是2
				formId: this.$route.query.id || '',
				params: {
					userId: this.userInfo.id,
					userName: this.userInfo.realname,
					tenantId: getItem('tenantId')
				}
			};
			this._BUS.$emit(CoosEventTypes.aiSessionParams, {
				chatObjectId,
				listParams,
				otherParams,
				modeType: 'zklh',
				componentType: 'other-system',
				prologue: '你好，我是COOS助手，可以为你检索事务细节，助你快速决策。',
				clickEvent: 'dbClick',
				showCoos: true
			});
		},
		// 新增或者修改数据后重新刷新列表
		update() {
			// this.form.pageNo = 1
			this.getTableData();
		},
		// 获取表格数据
		getTableData() {
			this.tableLoading = true;
			getFormTable({ formId: this.$route.query.id, ...this.form }).then(res => {
				this.tableLoading = false;
				// 延迟处理操作错位问题
				this.$nextTick(() => {
					setTimeout(() => {
						this.$refs.dynamicList.layout();
					}, 20);
				});
				if (res.code === 200) {
					const records = res.result.records || [];
					this.tableData = records.map(v => {
						let data = {
							...v.data,
							id: v.id,
							status: v.status,
							statusName: this.statusObj[v.status] || '',
							processInstanceId: v.processInstanceId
						};
						return data;
					});
					this.total = res.result.total || 0;
				} else {
					this.$message.error(res.message);
				}
			});
		},
		/**页码大小发生变化*/
		handleSizeChange(size) {
			this.form.pageSize = size;
			this.getTableData();
		},
		/**页码发生变化*/
		handleCurrentChange(page) {
			this.form.pageNo = page;
			this.getTableData();
		},
		/**搜索数据*/
		search: debounce(
			function () {
				this.$message.warning('开发中！');
				// this.pageNo = 1;
				// this.getTable();
			},
			500,
			false
		),
		handleSearch(data) {
			const parseJson = JSON.parse(data);
			const searchStr = [];
			parseJson.map(v => {
				if (v.value !== null) {
					searchStr.push(v);
				}
			});
			this.form.param = JSON.stringify(searchStr);
			this.pageNo = 1;
			this.getTableData();
			// this.getUsers(this.searchType, this.searchId);
		},
		handleReset() {
			this.pageNo = 1;
			this.form.param = [];
			this.getTableData();
			// this.getUsers(this.searchType, this.searchId);
		}
	}
};
</script>

<style scoped lang="scss">
::v-deep {
	.desk-el-custom-dialog .el-dialog {
		max-height: max-content;
	}
}
.active-form {
	width: 100%;
	height: 100%;
	padding: 0 24px 23px;
	background: #ffffff;
	@include flexBox(flex-start, flex-start);
	flex-direction: column;
	overflow: hidden;
	.title {
		width: 100%;
		padding: 32px 0 24px;
		border-bottom: 1px solid $borderColor;
		@include flexBox(space-between);
		&-left {
			@include flexBox(flex-start);
			&-text {
				@include flexBox(space-between, flex-start);
				flex-direction: column;
				.top {
					font-size: 18px;
					font-weight: 500;
					color: $primaryTextColor;
					line-height: 26px;
					margin-bottom: 8px;
				}
				.bottom {
					font-size: 14px;
					font-weight: 500;
					color: $subTextColor;
					line-height: 22px;
				}
			}
		}
		&-right {
			font-size: 14px;
			font-weight: 400;
			color: rgba(0, 0, 0, 0.6);
			line-height: 22px;
			@include flexBox();
			justify-content: flex-end;
			&-input {
				width: 180px;
				height: 32px;
				::v-deep .el-input__inner {
					border-radius: 6px;
					&::placeholder {
						font-size: 14px;
						font-weight: 400;
						color: $holderTextColor;
						line-height: 22px;
					}
				}
			}
		}
	}
	.content {
		width: 100%;
		display: flex;
		justify-content: space-between;
		&-title {
			padding: 12px 0;
			@include flexBox(flex-start);
			.add {
				width: 108px;
				height: 36px;
				border-radius: 6px;
				font-size: 14px;
				font-weight: 400;
				color: #ffffff;
				line-height: 22px;
				cursor: pointer;
				background: var(--brand-6);
				@include flexBox();
				.svg {
					width: 16px;
					height: 16px;
					margin-right: 4px;
				}
			}
			.print {
				@include flexBox();
				padding: 0 12px;
				height: 36px;
				border-radius: 6px;
				font-size: 14px;
				font-weight: 400;
				color: #ffffff;
				line-height: 22px;
				cursor: pointer;
				background: var(--brand-6);
			}
		}
	}
	.table {
		width: 100%;
		flex: 1;
		overflow: hidden;
	}
	.footer {
		width: 100%;
		&-total {
			font-size: 12px;
			font-weight: 400;
			color: $textColor;
			line-height: 20px;
			margin: 14px 0 16px;
		}
	}
}

.searchBox {
	width: 100%;
}
</style>
