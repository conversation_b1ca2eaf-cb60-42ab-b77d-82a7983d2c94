<script src="../../../utils/request.js"></script>
<template>
	<el-dialog
		v-loading="loading"
		:visible.sync="show"
		class="desk-el-custom-dialog"
		top="0"
		:append-to-body="true"
		width="90%"
		title="预览"
		@closed="closed"
	>
		<div id="printBox" class="print-box">
			<div class="form-title">{{ formTitle }}</div>
			<v-form-render
				v-if="formJson.widgetList"
				:key="formKey"
				ref="vFormRef"
				v-loading="loading"
				:class="type === 'detail' ? 'desk-el-active-form displayUpdata' : 'desk-el-active-form'"
				:option-data="optionData"
				:form-json="formJson"
				:form-data="formData"
				:base-url="config.baseUrl"
				:disabled="true"
				v-bind="$attrs"
				@viewUploadFile="viewUploadFile"
			></v-form-render>
		</div>
		<div class="footer">
			<el-button @click="cancel">取消</el-button>
			<el-button type="primary" @click="doPrint">打印</el-button>
		</div>
	</el-dialog>
</template>

<script>
import printJs from 'print-js';
import { getFormData } from '@/api/modules/form-page';
import { get_token, getToken } from '@/utils/auth';
import { baseUrl } from '@/config';
import { previewFile } from '@/utils';
import config from '@/config';
export default {
	name: 'PrintView',
	props: {
		processInstanceId: {
			type: String,
			default: ''
		},
		// 动态表单配置
		formJson: {
			type: Object,
			default: () => {
				return {};
			}
		},
		// 动态表单数据ID
		detailId: {
			type: [String, Number],
			default: () => {
				return '';
			}
		},
		// 打印标题
		formTitle: {
			type: String,
			default: () => {
				return '';
			}
		}
	},
	computed: {
		formId() {
			return this.formID || this.$route.query.id;
		}
	},
	data() {
		return {
			config,
			formKey: 0,
			show: false,
			optionData: {},
			loading: false,
			type: 'newAdd', // 弹窗类型
			dialogName: { newAdd: '添加数据', edit: '编辑', detail: '查看详情' },
			formData: {}, // 动态表单渲染器参数
			uploadBaseUrl: baseUrl + '/api/robot/ocr/upload_images',
			formDisiabled: false,
			headers: {
				'X-Coos-Client-Access-Token': getToken(),
				'X-Coos-Client-Tenant-Id': get_token('X-Coos-Client-Tenant-Id')
			},
			processEnabled: false,
			formState: false,
			formTemplate: {},
			disableWidgets: [],
			enableWidgets: [],
			hideWidgets: [],
			showWidgets: [],
			proInstanceId: null,
			formType: '',
			submiting: false
		};
	},
	watch: {
		detailId(newVal) {
			if (newVal) {
				this.getDetail();
			}
		},
		isProcessEnabled(val) {
			this.processEnabled = val;
		},
		processInstanceId: function (val) {
			this.proInstanceId = val;
		},

		show(val) {
			if (val) {
				this.getDetail();
			}
		}
	},
	mounted() {},
	methods: {
		doPrint() {
			const that = this;
			// 由于高度采用滚动, 打印展示不全,所以需要在打印时重置样式, 结束后恢复样式
			const printBox = document.getElementById('printBox');
			// 记录原始样式
			const originalPrintBoxStyle = printBox.style.cssText;
			// 临时调整样式 - 内容不会被剪裁，内容会溢出到元素的外部
			printBox.style.overflow = 'visible';

			setTimeout(() => {
				printJs({
					printable: 'printBox',
					type: 'html',
					targetStyles: '*',
					style: '.el-divider{background:red}',
					scanStyle: true,
					maxWidth: '1200',
					onLoadingStart: function () {
						console.log('开始加载PDF');
					},
					onLoadingEnd: function () {
						console.log('结束加载PDF');
						that.$emit('loding-success');
						// 恢复原始样式
						printBox.style.cssText = originalPrintBoxStyle;
					}
				});
			}, 500);
		},
		viewUploadFile() {},
		// 上传代码前进行的操作---限制文件大小，限制文件格式
		open(type) {
			this.formKey += 1;
			this.formType = type;
			this.type = type;
			this.getDetail();
			this.show = true;
		},

		cancel() {
			this.show = false;
			setTimeout(() => {
				this.formData = {};
				this.proInstanceId = '';
			}, 200);
		},
		closed() {
			this.formState = false;
			this.proInstanceId = '';
			this.formData = {};
		},
		/**获取详情*/
		getDetail() {
			console.log(this.detailId);
			if (!this.detailId || !this.show) return;
			this.loading = true;
			getFormData(this.detailId).then(res => {
				this.loading = false;
				if (res.code === 200) {
					this.formData = res.result.data;
					this.formKey += 1;
				} else {
					this.$message.error(res.message);
				}
			});
		}
	}
};
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__body {
	display: flex;
	flex-direction: column;
	height: calc(80vh - 60px); // 减去footer的高度
}
.form-title {
	text-align: center;
	font-weight: bold;
	font-size: 20px;
	color: #333;
	margin-bottom: 40px;
}
.print-box {
	flex: 1;
	overflow-y: auto;
	padding-right: 6px;
}

.footer {
	height: 60px; // 确保有足够的高度
	background: #fff;
	padding: 10px 24px;
	display: flex;
	justify-content: flex-end;
	align-items: center;
	gap: 5px;
	border-top: 1px solid #dcdfe6;

	/* 打印样式 */
}
</style>

<style>
.print-box .field-wrapper {
	margin-bottom: 18px;
}
.print-box .el-radio-group {
	width: 100%;
}
.print-box .el-divider__text {
	display: block;
	min-width: 110px;
	text-align: center;
}
.field-wrapper .el-alert--info.is-light,
.el-divider,
.el-slider__runway {
	-webkit-print-color-adjust: exact;
	print-color-adjust: exact;
	color-adjust: exact;
}
.print-box .el-slider__runway {
	margin: 0;
}
.print-box .el-slider {
	height: 38px;
	padding-top: 16px;
}
.print-box .el-slider__button-wrapper {
	padding: 5px;
}
</style>
