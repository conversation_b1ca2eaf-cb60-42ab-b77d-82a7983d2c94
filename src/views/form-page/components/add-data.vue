<script src="../../../utils/request.js"></script>
<template>
	<el-dialog
		v-loading="loading"
		:visible.sync="show"
		class="desk-el-custom-dialog"
		top="0"
		:append-to-body="appendToBody"
		width="90%"
		@closed="closed"
	>
		<div slot="title">
			<slot name="title">{{ dialogName[type] }}</slot>
		</div>
		<div class="tabs-upload">
			<el-tabs
				v-if="!formRender"
				v-model="activeName"
				class="desk-el-tabs"
				@tab-click="handleClick"
			>
				<el-tab-pane label="表单填写" name="write-form"></el-tab-pane>
				<template v-if="processEnabled">
					<el-tab-pane label="表单流程" name="form-flow">
						<div class="form-container">
							<show-flow
								v-show="processEnabled && activeName == 'form-flow'"
								ref="formFlow"
								:form-id="formId"
								:pro-instance-id="proInstanceId"
							></show-flow>
						</div>
					</el-tab-pane>
				</template>

				<template v-if="isProcessEnabled && flowRecord.length > 0">
					<el-tab-pane label="流转记录" name="form-records">
						<div class="form-container">
							<flow-records
								:flow-record="flowRecord"
								:form-id="formId"
								:process-instance-id="processInstanceId"
							/>
						</div>
					</el-tab-pane>
				</template>
			</el-tabs>
			<slot name="customForm"></slot>
		</div>
		<div v-if="activeName === 'write-form' || formRender" class="form-container">
			<div :class="formRender ? 'ocr-box' : 'tab-ocr-box'">
				<el-upload
					v-if="ocrAutoForm"
					ref="orcUpload"
					class="upload-demo"
					:multiple="false"
					:action="uploadBaseUrl"
					:on-preview="handlePreview"
					:on-remove="handleRemove"
					:before-remove="beforeRemove"
					accept=".png,.jpg"
					:on-success="onSuccess"
					:headers="headers"
					:before-upload="beforeUpload"
					:limit="1"
					:on-exceed="handleExceed"
					:show-file-list="false"
				>
					<el-tooltip class="item" effect="dark" content="OCR识别" placement="top">
						<div class="upload-button" size="small" type="primary">
							<div class="ocr-content">
								<img
									:src="require(`@/assets/${rentThem}/form/ocr.png`)"
									alt=""
									class="ocr-content-icon"
								/>
							</div>
						</div>
					</el-tooltip>
				</el-upload>
			</div>
			<v-form-render
				v-if="formJson.widgetList"
				:key="formKey"
				ref="vFormRef"
				v-loading="loading"
				:class="type === 'detail' ? 'desk-el-active-form displayUpdata' : 'desk-el-active-form'"
				:option-data="optionData"
				:form-json="formJson"
				:form-data="formData"
				:base-url="config.baseUrl"
				:disabled="formState"
				v-bind="$attrs"
				@viewUploadFile="viewUploadFile"
			></v-form-render>
		</div>
		<div v-if="type !== 'detail'" class="footer">
			<el-button @click="cancel">取消</el-button>
			<el-button type="primary" @click="saveDraft">保存草稿</el-button>
			<el-button type="primary" :loading="submiting" @click="submitDraft">提交</el-button>

			<!-- <div class="footer-cancel" @click="cancel">取消</div>
			<div class="footer-save" @click="saveDraft">保存草稿</div>
			<div class="footer-save" @click="submitDraft">提交</div> -->
		</div>
		<formSetting ref="formSetting" @nextSelected="nextSelected" />
	</el-dialog>
</template>

<script>
import {
	getFormData,
	getImageLlmContent,
	ocrToJson,
	saveFormData,
	updateFormData,
	saveDraft,
	submitDraft,
	getFormHead
} from '@/api/modules/form-page';
import { findByFormId, selectListHis } from '@/api/modules/flow-design';
import { get_token, getToken } from '@/utils/auth';
import { baseUrl } from '@/config';
import ShowFlow from '@/components/logic-flow/show-flow.vue';
import FlowRecords from '@/components/logic-flow/flow-records.vue';
import FormSetting from '@/components/form-setting';
import { previewFile } from '@/utils';
import config from '@/config';
export default {
	name: 'Index',
	components: { ShowFlow, FlowRecords, FormSetting },
	props: {
		isProcessEnabled: {
			type: Boolean,
			default: () => {
				return false;
			}
		},
		// 是否纯系统表单
		formRender: {
			type: Boolean,
			default: false
		},
		processInstanceId: {
			type: String,
			default: ''
		},
		// 是否有ocr识别
		ocrAutoForm: {
			type: Boolean,
			default: () => {
				return false;
			}
		},
		// 动态表单数据ID
		detailId: {
			type: [String, Number],
			default: () => {
				return '';
			}
		},
		formID: {
			type: [String, Number],
			default: () => {
				return '';
			}
		},
		appendToBody: {
			type: Boolean,
			default: () => {
				return false;
			}
		}
	},
	computed: {
		formId() {
			return this.formID || this.$route.query.id;
		}
	},
	data() {
		return {
			config,
			formKey: 0,
			activeName: 'write-form',
			show: false,
			optionData: {},
			loading: false,
			type: 'newAdd', // 弹窗类型
			dialogName: { newAdd: '添加数据', edit: '编辑', detail: '查看详情' },
			formData: {}, // 动态表单渲染器参数
			uploadBaseUrl: baseUrl + '/api/robot/ocr/upload_images',
			formDisiabled: false,
			headers: {
				'X-Coos-Client-Access-Token': getToken(),
				'X-Coos-Client-Tenant-Id': get_token('X-Coos-Client-Tenant-Id')
			},
			processEnabled: false,
			formState: false,
			formTemplate: {},
			disableWidgets: [],
			enableWidgets: [],
			hideWidgets: [],
			showWidgets: [],
			proInstanceId: null,
			flowRecord: [],
			formType: '',
			formJson: {
				widgetList: []
			},
			status: '',
			submiting: false,
			submitFormData: {}
		};
	},
	watch: {
		isProcessEnabled(val) {
			this.processEnabled = val;
		},
		processInstanceId: function (val) {
			this.proInstanceId = val;
		},

		show(val) {
			if (val) {
				if (this.type !== 'newAdd') {
					this.getDetail();
				} else {
					this.status = '';
				}
				// 如果有流程 那么就要根据流程去加载表单
				if (this.isProcessEnabled) {
					this.proInstanceId = this.processInstanceId;
					findByFormId(this.formId, { processInstanceId: this.processInstanceId }).then(res => {
						if (!res.result) return;
						const jsonContent = JSON.parse(res.result.config.jsonContent);
						const config = [...res.result.config.config];
						const flowNodes = {};
						config.map(v => {
							flowNodes[v.nodeId] = { nodeConfig: { ...JSON.parse(v.configData) } };
						});
						const { edges = [] } = jsonContent || {};
						let startNode = {};
						edges.map(v => {
							if (v.sourceNodeId.indexOf('startEvent_') > -1) {
								startNode = { ...flowNodes[v.targetNodeId] };
							}
						});
						const formProperties = startNode.nodeConfig?.node.properties.formProperties;
						const privileges = formProperties?.privileges || [];
						if (privileges.length == 0) return;
						const disableWidgets = [],
							enableWidgets = [],
							hideWidgets = [],
							showWidgets = [];
						privileges.map(v => {
							if (v.displayable) {
								//显示 默认false 默认显示
								hideWidgets.push(v.id);
							} else {
								// 显示
								showWidgets.push(v.id);
							}
							if (v.readable) {
								// 只读
								disableWidgets.push(v.id);
							}
							if (v.writable) {
								// 可编辑
								enableWidgets.push(v.id);
							}
						});
						this.$nextTick(() => {
							setTimeout(() => {
								this.$refs.vFormRef.disableWidgets(disableWidgets);
								this.$refs.vFormRef.enableWidgets(enableWidgets);
								this.$refs.vFormRef.hideWidgets(hideWidgets);
								this.$refs.vFormRef.showWidgets(showWidgets);
							}, 0);
						});
						// console.log(formProperties);
						// getDesignById(formProperties.templateId).then(res => {
						// 	console.log('template:', JSON.parse(res.result.configJson));
						// });
					});
				} else {
					this.proInstanceId = '';
					this.flowRecord = [];
					this.formData = {};
					this.submiting = false;
				}
			}
		}
	},
	mounted() {},
	methods: {
		saveDraft() {
			this.$refs.vFormRef.getFormData().then(formData => {
				// formid 表单的id
				// id 数据的id
				// formData 表单的数据
				saveDraft(this.formId, this.detailId, formData).then(res => {
					if (res.code === 200) {
						this.show = false;
						const msg = this.type == 'newAdd' ? '新增成功！' : '更新成功';
						this.$message.success(msg);
						this.$emit('update');
					} else {
						this.$message.error(res.message);
					}
				});
			});
		},
		submitDraft() {
			if (this.submiting) return;
			this.$refs.vFormRef.getFormData().then(formData => {
				this.submitFormData = formData;
				// 如果有流程,就需要设置紧急程度,以及选择下一步办理人
				if (this.isProcessEnabled) {
					this.$refs.formSetting.open(
						this.formId,
						formData,
						this.detailId,
						this.processInstanceId,
						this.status
					);
				} else {
					this.nextSelected();
				}
			});
		},
		nextSelected(e = {}) {
			const submitData = { formVars: this.submitFormData, ...e };
			console.log('submitData:', submitData);
			this.submiting = true;
			submitDraft(this.formId, this.detailId, submitData).then(res => {
				this.submiting = false;
				if (res.code === 200) {
					this.show = false;
					this.$message.success('提交成功！');
					this.$emit('update');
				} else {
					this.$message.error(res.message);
				}
			});
		},
		// 上传代码前进行的操作---限制文件大小，限制文件格式
		beforeUpload(file) {
			const fileSize = file.size / 1024 / 1024; // 将文件大小转换为MB
			if (fileSize > 10) {
				this.$message.warning('文件大小不能超过10M');
				return false; // 阻止文件上传
			}
			const allowedExtensions = ['.jpg', '.png'];
			const fileExtension = file.name.substring(file.name.lastIndexOf('.')).toLowerCase();
			if (!allowedExtensions.includes(fileExtension)) {
				this.$message.warning('不支持的文件格式');
				return false; // 阻止文件上传
			}
			this.fileState = false;
			return true; // 允许文件上传
		},
		traverseArray(widgetList) {
			for (let item of widgetList) {
				if (Array.isArray(item.widgetList)) {
					this.traverseArray(item.widgetList); // 递归遍历子数组
				} else if (Array.isArray(item.cols)) {
					this.traverseArray(item.cols);
				} else {
					if (item.type == 'checkbox' || item.type == 'radio' || item.type == 'select') {
						if (item.options?.optionItems.length > 0) {
							const optionItems = item.options?.optionItems || [];
							item.options.optionItems = optionItems.map(v => {
								let it = { ...v };
								// 确保值为字符串
								if (v.value) {
									it.value = String(v.value);
								}
								return it;
							});
						}
					}
				}
			}
			return widgetList;
		},
		open(type) {
			this.formType = type;
			this.type = type;

			if (type == 'detail') {
				this.formState = true;
			} else if (type == 'edit') {
				this.formState = false;
			}
			if (type === 'newAdd') {
				this.formData = {};
				this.formKey += 1;
			}
			if (this.type !== 'newAdd') {
				if (this.detailId) {
					this.getDetail();
				} else {
					this.formData = {};
					this.formKey += 1;
				}
			}
			this.loading = true;
			getFormHead({ formId: this.formId, designType: 1 }).then(res => {
				if (res.code === 200) {
					if (res.result[0] && res.result[0].configJson) {
						let data = res.result;
						const formJson = JSON.parse(data[0].configJson);
						const config = JSON.parse(data[0].configJson);
						const { widgetList } = config;
						if (widgetList) {
							const wdilist = this.traverseArray(widgetList);
							config.widgetList = wdilist;
						}
						config.formConfig.labelWidth = 120;
						this.formJson = { ...config };
						console.log(this.formJson, 'this.formJson ');
						this.formKey += 1;
					}
					this.loading = false;
				} else {
					this.loading = false;
					this.$message.error(res.message);
				}
			});
			this.show = true;
		},
		selectListHis(processInstanceId) {
			const flowParams = {
				desc: false,
				pageNo: 1,
				pageSize: 1000,
				processInstanceId
			};
			selectListHis({ ...flowParams }).then(res => {
				this.flowRecord = res.result?.records;
			});
		},
		handleRemove(file, fileList) {
			console.log(file, fileList);
		},
		handlePreview(file) {
			console.log(file);
		},
		handleExceed(files, fileList) {
			this.$message.warning(
				`当前限制选择 1 个文件，本次选择了 ${files.length} 个文件，共选择了 ${
					files.length + fileList.length
				} 个文件`
			);
		},
		beforeRemove(file, fileList) {
			// return this.$confirm(`确定移除 ${file.name}？`);
		},
		onSuccess(response) {
			this.loading = true;
			let obj = response.result.filenames || {};
			const values = Object.values(obj);
			if (values.length > 0) {
				// 如果存在文件名，获取图像的内容
				getImageLlmContent({
					uploadImageName: values[0]
				})
					.then(res => {
						// 请求成功且结果有效时，处理OCR数据
						if (res.code == 200 && res.success) {
							try {
								const ocrData = JSON.parse(res.result.answer);
								let data = {
									ocrData: ocrData,
									formId: this.$route.query.id,
									tenantId: get_token('X-Coos-Client-Tenant-Id')
								};
								// 将OCR数据转换为JSON格式
								ocrToJson(data).then(jsonRes => {
									if (jsonRes.code == 200 && jsonRes.success) {
										// 更新表单数据和键值
										this.formData = jsonRes.result;
										this.formKey += 1;
										this.ocrMessage(jsonRes);
									} else {
										this.ocrMessage(res, 1);
									}
								});
							} catch (e) {
								this.ocrMessage({ message: '解析OCR失败' }, 1);
							}
						} else {
							this.ocrMessage(res, 1);
						}
					})
					.catch(error => {
						this.$message.warning(error.message || '解析OCR失败');
					});
			}
		},
		ocrMessage(res, type) {
			this.loading = false;
			this.$refs.orcUpload.clearFiles();
			if (type) {
				this.$message.warning(res.message);
			}
		},
		/**点击切换tab*/
		handleClick() {
			if (this.activeName == 'form-flow') {
				this.$nextTick(() => {
					this.$refs.formFlow.init();
				});
			}
		},
		cancel() {
			this.show = false;
			setTimeout(() => {
				this.activeName = 'write-form';
				this.formData = {};
				this.proInstanceId = '';
			}, 200);
		},
		closed() {
			this.formState = false;
			this.activeName = 'write-form';
			this.proInstanceId = '';
			this.formData = {};
			this.flowRecord = [];
		},
		/**保存数据*/
		save() {
			this.$refs.vFormRef.getFormData().then(formData => {
				if (this.type === 'newAdd') {
					saveFormData(this.$route.query.id, formData).then(res => {
						if (res.code === 200) {
							this.show = false;
							this.$message.success('新增成功！');
							this.$emit('update');
						} else {
							this.$message.error(res.message);
						}
					});
				} else {
					updateFormData(this.detailId, formData).then(res => {
						if (res.code === 200) {
							this.show = false;
							this.$emit('update');
							this.$message.success('更新成功！');
						} else {
							this.$message.error(res.message);
						}
					});
				}
			});
		},
		/**获取详情*/
		getDetail() {
			if (!this.detailId) return;
			this.loading = true;
			getFormData(this.detailId).then(res => {
				this.loading = false;
				if (res.code === 200) {
					this.formData = res.result.data;
					this.status = res.result.status;
					this.formKey += 1;
					if (res.result.processInstanceId) {
						this.selectListHis(res.result.processInstanceId);
					}
				} else {
					this.$message.error(res.message);
				}
			});
		},
		viewUploadFile(link) {
			if (link?.fileUrl) {
				previewFile(link.fileUrl);
			} else {
				this.$message.info('文件有误,无法预览');
			}
		}
	}
};
</script>

<style scoped lang="scss">
.form-container,
.flow-records-box {
	height: calc(100vh - 290px);
	overflow: auto;
	overflow-x: hidden;
}
.footer {
	width: 100%;
	height: 52px;
	padding-right: 6px;
	@include flexBox(flex-end);
	grid-gap: 5px;
	&-cancel {
		width: 68px;
		height: 36px;
		background: #ffffff;
		border-radius: 6px;
		border: 1px solid $borderColor;
		cursor: pointer;
		font-size: 14px;
		font-weight: 400;
		color: $primaryTextColor;
		line-height: 36px;
		text-align: center;
	}
	&-save {
		width: 96px;
		height: 36px;
		background: var(--brand-6);
		border-radius: 6px;
		font-size: 14px;
		font-weight: 400;
		color: rgba(255, 255, 255, 0.9);
		line-height: 36px;
		text-align: center;
		cursor: pointer;
	}
}
.tabs-upload {
	position: relative;
}
.upload-demo {
}
::v-deep .el-upload-list .el-progress {
	//display: none !important;
}
.upload-button {
	width: 48px;
	height: 38px;
	background: linear-gradient(117deg, #d5e3ff 0%, #e8f4ff -126%);
	border-radius: 6px;
	display: flex;
	justify-content: center;
	align-items: center;
}
::v-deep {
	.el-upload-list__item:first-child {
		margin-top: 0;
	}
	.el-upload-list__item {
		line-height: 36px;
	}
}
.displayUpdata {
	::v-deep {
		.el-upload--text {
			display: none;
		}
		.el-upload--picture-card {
			display: none;
		}
		.upload-file-list .el-icon-delete {
			display: none;
		}
	}
}
.ocr-content {
	display: flex;
	align-items: center;
	justify-content: center;
	&-icon {
		height: 30px;
		width: 30px;
	}
}
.ocr-box {
	display: flex;
	flex-direction: row-reverse;
	margin-bottom: 8px;
}
.tab-ocr-box {
	position: absolute;
	right: 28px;
	top: 60px;
}
</style>
