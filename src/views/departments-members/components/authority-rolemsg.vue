<template>
	<div class="authority-RoleMsg">
		<h2 class="title">角色信息</h2>
		<el-form
			ref="ruleForm"
			:model="roleMsg"
			label-width="100px"
			label-position="top"
			class="demo-ruleForm"
			:hide-required-asterisk="hideasterisk"
		>
			<el-form-item
				label="角色名称"
				prop="roleName"
				:rules="[{ required: true, message: '请输入角色名称', trigger: 'change' }]"
			>
				<el-input v-model="roleMsg.roleName" placeholder="请输入角色名称"></el-input>
			</el-form-item>
			<el-form-item
				label="选择角色关联组织"
				prop="orgId"
				:rules="[{ required: true, message: '请选择角色关联组织', trigger: 'change' }]"
			>
				<el-cascader
					ref="handleCascader"
					v-model="roleMsg.orgId"
					:options="options"
					style="width: 100%"
					:props="{ checkStrictly: true, expandTrigger: 'hover' }"
					clearable
					@change="selectChange"
				></el-cascader>
			</el-form-item>
			<el-form-item label="是否角色模板">
				<el-switch v-model="roleMsg.isTemplate" :active-value="1" :inactive-value="0"></el-switch>
			</el-form-item>
			<el-form-item label="角色简介" prop="description">
				<el-input
					v-model="roleMsg.description"
					placeholder="输入说明，让其他管理了解此角色的用途和注意事项"
					type="textarea"
					:autosize="{ minRows: 4 }"
				></el-input>
			</el-form-item>
		</el-form>
	</div>
</template>
<script>
import { getOrgInfo } from '@/api/modules/authority';
export default {
	props: {
		roleMsg: {
			type: Object,
			default: () => {
				return {
					roleName: '',
					description: '',
					isTemplate: 0
				};
			}
		}
	},
	data() {
		return {
			options: [],
			hideasterisk: true
		};
	},
	mounted() {
		this.getOrgTree();
	},
	methods: {
		selectChange(val) {
			if (this.$refs.handleCascader) {
				this.$refs.handleCascader.dropDownVisible = false;
			}
		},
		updateProperties(obj) {
			const { id, orgName, children } = obj;
			const updatedObj = {
				value: id,
				label: orgName
			};
			if (children && children.length > 0) {
				updatedObj.children = children.map(this.updateProperties);
			}
			return updatedObj;
		},
		getOrgTree() {
			getOrgInfo({ limitOrg: true }).then(res => {
				this.options = res.result.map(this.updateProperties);
			});
		},
		async validate() {
			return await new Promise((resolve, reject) => {
				this.$refs.ruleForm.validate(valid => {
					if (valid) {
						resolve(this.roleMsg);
					} else {
						reject('校验失败');
						return false;
					}
				});
			});
		}
	}
};
</script>
<style lang="scss" scoped>
::v-deep .is-required .el-form-item__label::after {
	content: '*';
	color: #ff0000;
	margin-left: 4px;
}
.title {
	font-size: 21px;
	color: #000;
	font-weight: 700;
	font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, Segoe UI, Arial,
		Roboto, 'PingFang SC', miui, 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;
}
</style>
