<template>
	<el-tree
		:key="data.length"
		:data="data"
		node-key="id"
		:current-node-key="data && data.length ? data[0].id : ''"
		highlight-current
		:props="defaultProps"
		@node-click="handleNodeClick"
	>
		<!-- eslint-disable-next-line vue/no-template-shadow -->
		<template #default="{ data }">
			<div class="treeItem">
				<div><i class="coos-iconfont icon-tubiao-liucheng"></i></div>
				<el-tooltip
					v-if="data.orgName.length > 11"
					:open-delay="1000"
					effect="dark"
					:content="data.orgName"
					placement="top-start"
				>
					<div class="title">{{ data.orgName }}</div>
				</el-tooltip>
				<div v-else class="title">{{ data.orgName }}</div>
			</div>
		</template>
	</el-tree>
</template>

<script>
export default {
	name: 'MenuTree',
	props: {
		data: {
			type: Array,
			default: () => []
		}
	},
	data() {
		return {
			defaultProps: {
				children: 'children',
				label: 'title'
			}
		};
	},
	methods: {
		handleNodeClick(data) {
			const { id } = data;
			this.$emit('searchRoleList', id);
		}
	}
};
</script>

<style lang="scss" scoped>
.treeItem {
	display: flex;
	margin-left: 9px;
	font-size: 14px;
	color: $primaryTextColor;
	line-height: 22px;
	.title {
		margin-left: 4px;
		@include aLineEllipse;
	}
}
::v-deep .el-tree-node__content {
	height: 42px;

	.el-tree-node__expand-icon {
		font-size: 18px;
	}
	.treeItem {
		overflow: hidden;
	}
}
</style>
