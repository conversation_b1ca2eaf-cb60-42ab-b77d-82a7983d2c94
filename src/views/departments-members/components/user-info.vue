<template>
	<div>
		<div v-for="(item, index) in drawerJson" :key="index">
			<p class="title">{{ item.options.label }}:</p>
			<div
				v-if="
					typeof formData[item.options.name] === 'object' &&
					Array.isArray(formData[item.options.name])
				"
				class="infos"
			>
				{{ setText(item) }}
			</div>
			<div v-else class="infos">{{ formData[item.options.name] }}</div>
		</div>
		<div>
			<p class="title">身份信息</p>
			<div v-for="(item, index) in formData.userIdentities" :key="index" class="user-box">
				<div class="label" :class="!item.isMain ? 'label2' : ''">
					{{ item.isMain ? '主身份' : '子身份' }}
				</div>
				<div
					class="info"
					:style="
						index != formData.userIdentities.length - 1
							? 'border-bottom: 1px dashed #DCE3E7;'
							: 'padding:0'
					"
				>
					<div>
						<span>所属部门：</span>
						{{ item.departName }}
					</div>
					<div v-if="userVersion === 'v2'">
						<span>岗位：</span>
						{{ item.postName }}
					</div>
					<div v-if="userVersion === 'v2'">
						<span>职务：</span>
						{{ item.jobTitle }}
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import { mapGetters } from 'vuex';

export default {
	props: {
		drawerJson: {
			type: Array,
			default: () => []
		},
		formData: {
			type: Object,
			default: () => {}
		}
	},
	data() {
		return {};
	},
	computed: {
		...mapGetters(['rentInfo', 'userInfo', 'userVersion'])
	},
	created() {
		this.setJSON();
	},
	methods: {
		setJSON() {
			let version = this.userVersion;
			if (version == 'v2') {
				console.log(this.drawerJson);
				this.drawerJson.forEach((item, index) => {
					if (item.options.label == '所属部门') {
						this.drawerJson.splice(index, 1);
					}
				});
			}
			this.drawerJson.forEach(item => {
				if (item.type != 'input') {
					item.options.name = item.options.name + 'DictText';
				}
			});
		},
		setText(item) {
			return this.formData[item.options.name].length < 1
				? '--'
				: this.formData[item.options.name].join(',');
		}
	}
};
</script>

<style lang="scss" scoped>
p {
	margin: 0;
}
.title {
	font-size: 14px;
	color: #737a94;
}

.infos {
	font-size: 16px;
	color: #15224c;
	margin-top: 8px;
	margin-bottom: 20px;
}
.user-box {
	display: flex;
	justify-content: space-between;
	margin-top: 12px;
	.label {
		width: 59px;
		height: 24px;
		text-align: center;
		background: linear-gradient(270deg, #ebf4ff 0%, #b5d6f4 100%);
		border-radius: 3px;
		line-height: 24px;
		font-size: 14px;
		color: var(--brand-6);
	}
	.label2 {
		background: linear-gradient(270deg, #dde3eb 0%, #96aec4 100%);
		color: #737a94;
	}
	.info {
		font-size: 14px;
		color: #2f446b;
		width: calc(100% - 80px);
		padding-bottom: 10px;
		div {
			margin-bottom: 8px;
			span {
				color: #737a94;
			}
		}
	}
}
</style>
