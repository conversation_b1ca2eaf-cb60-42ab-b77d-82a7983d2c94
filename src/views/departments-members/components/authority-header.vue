<template>
	<div class="authority-Management">
		<div class="authority-Management-all">
			<span class="authority-Management-title">角色与权限管理</span>
			<div class="authority-Management-right">
				<div class="authority-Management-search">
					<el-input
						v-model="keyword"
						placeholder="搜索姓名、角色名称"
						class="custom-el-search"
						@input="changeKeyword"
					>
						<svg-icon
							slot="suffix"
							class="authority-Management-search-icon"
							icon-class="search"
						></svg-icon>
					</el-input>
				</div>
				<div class="authority-Management-btn" @click="openAddRola">
					<svg-icon slot="suffix" class="authority-Management-btn-icon" icon-class="add"></svg-icon>
					添加角色
				</div>
			</div>
		</div>
		<authorityAddRole ref="authorityAddRole" :pramas="pramas" v-on="$listeners" />
	</div>
</template>
<script>
import authorityAddRole from './authority-add-role';
import { debounce } from '@/utils';
export default {
	components: { authorityAddRole },
	data() {
		return {
			keyword: '', //搜索关键字
			pramas: {}
		};
	},
	methods: {
		/**改变搜索内容*/
		changeKeyword: debounce(
			function () {
				this.$emit('search', this.keyword);
			},
			500,
			false
		),
		/**添加角色按钮控制authority-add-role组件 打开弹窗 */
		openAddRola() {
			this.pramas.flow = true;
			this.pramas.title = '添加角色';
			this.$refs.authorityAddRole.open();
		}
	}
};
</script>
<style lang="scss" scoped>
.authority-Management {
	height: 100%;
	padding: 0 24px;
	&-all {
		width: 100%;
		height: 100%;
		display: flex;
		justify-content: space-between;
		background: #ffffff;
		padding: 0px 17px;
		border-radius: 12px 12px 0px 0px;
		font-size: 14px;
		font-weight: 400;
		line-height: 22px;
	}
	&-right {
		display: flex;
		justify-content: right;
		align-items: center;
	}
	&-title {
		display: flex;
		align-items: center;
		flex-shrink: 0;
		font-size: 18px;
		font-weight: 800;
		color: $textColor;
		line-height: 24px;
	}
	&-search {
		cursor: pointer;
		display: flex;
		align-items: center;
		width: 196px;
		height: 40px;
		opacity: 1;
		&-icon {
			width: 16px;
			height: 16px;
			margin-right: 4px;
		}
		& ::v-deep .el-input__inner {
			height: 36px;
		}
	}
	&-btn {
		cursor: pointer;
		margin-left: 16px;
		width: 108px;
		height: 36px;
		background: var(--brand-6);
		border-radius: $borderRadius;
		color: #ffffff;
		display: flex;
		align-items: center;
		justify-content: center;
		&-icon {
			width: 16px;
			height: 16px;
			padding: 3px;
			margin-right: 4px;
		}
	}
}
::v-deep .el-input__suffix {
	display: flex;
	align-items: center;
}

::v-deep .el-input__suffix-inner {
	display: flex;
	align-items: center;
}
</style>
