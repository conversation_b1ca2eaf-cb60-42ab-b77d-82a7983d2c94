<template>
	<div v-loading="loading" class="all">
		<div class="content">
			<div class="content-tree">
				<!--				<div class="tree-title" @click="resetData">-->
				<!--					<img :src="rentInfo.logoUrl" alt="" class="logo" />-->
				<!--					<div class="title-info">{{ rentInfo.name }}</div>-->
				<!--				</div>-->
				<menuTree :data="treeList" @searchRoleList="searchRoleList"></menuTree>
			</div>
			<div class="authority-body">
				<el-table
					:data="tableData"
					height="calc(100% - 70px)"
					style="width: 100%"
					:row-style="getRowStyle"
					:header-row-style="headerRowStyle"
					:header-cell-style="headerCellStyle"
				>
					<el-table-column label="角色管理" min-width="210">
						<template slot-scope="{ row }">
							<div class="authority-body-tableRole">
								<el-tooltip :open-delay="1000" :content="row.roleName" placement="top">
									<span class="authority-body-tableRole-title">{{ row.roleName }}</span>
								</el-tooltip>
								<div v-if="row.isSysRole" class="authority-body-tableRole-type">系统角色</div>
							</div>
						</template>
					</el-table-column>
					<el-table-column
						label="所属组织"
						prop="orgName"
						min-width="190"
						:show-overflow-tooltip="true"
					></el-table-column>
					<el-table-column label="绑定成员" min-width="330">
						<template slot-scope="{ row }">
							<div v-if="row.users" class="authority-body-tableUser">
								<div v-for="(item, index) in row.users" :key="index">
									<div class="authority-body-tableUser-type">
										<FileById
											:size="18"
											:value="item.avatarUrl"
											:more-style="{ borderRadius: '3px', marginLeft: '1px' }"
											:default-font-icon="item.userName.slice(-1)"
											class="authority-AddRoleMember-img"
										></FileById>

										<div :style="{ width: item.userName.length * 14 + 'px' }">
											{{ item.userName }}
										</div>
									</div>
								</div>
								<div
									:style="{ width: 28 + 8 + row.userNum.toString().length * 8.5 + 'px' }"
									class="authority-body-tableUser-Type"
								>
									共{{ row.userNum }}人
								</div>
							</div>
							<div v-else>
								<div>暂无</div>
							</div>
						</template>
					</el-table-column>
					<el-table-column label="权限范围" show-overflow-tooltip min-width="300">
						<template slot-scope="{ row }">
							<span v-if="row.perms.length">
								{{ row.perms.join(',') }}

								<span
									v-if="row.permNum - row.perms.length > 0"
									class="authority-body-tableperms-type"
								>
									+{{ row.permNum - row.perms.length }}
								</span>
							</span>
							<span v-else>
								<div>暂无</div>
							</span>
						</template>
					</el-table-column>
					<el-table-column label="操作" width="250" fixed="right">
						<template slot-scope="{ row }">
							<div class="authority-body-tableHandle">
								<button class="authority-body-tableHandle-detailBtn" @click="detailclick(row)">
									详情
								</button>
								<button
									v-if="!row.isSysRole"
									class="authority-body-tableHandle-detailBtn"
									@click="editclick(row)"
								>
									编辑
								</button>
								<button
									v-if="!row.isSysRole"
									class="authority-body-tableHandle-detailBtn"
									@click="roleCopy(row)"
								>
									复制
								</button>
								<button
									v-if="!row.isSysRole"
									class="authority-body-tableHandle-deleteBtn"
									@click="deleteclick(row)"
								>
									删除
								</button>
							</div>
						</template>
					</el-table-column>
					<template slot="empty">
						<BasicEmpty />
					</template>
				</el-table>

				<div class="authority-body-pagination">
					<el-pagination
						:page-sizes="pageSizes"
						:page-size="pageSize"
						layout="total, sizes, ->, prev, pager, next, jumper"
						:total="total"
						background
						@size-change="handleSizeChange"
						@current-change="handleCurrentChange"
					></el-pagination>
				</div>
			</div>
		</div>

		<!-- save为编辑角色和新增角色时接受信息进行表单更新 saveTable为关联人员时表单进行更新-->
		<authorityDrawer
			ref="authorityDrawer"
			@save="save"
			@saveToTable="save"
			@closeDrawer="closeDrawer"
		/>
		<authorityAddRole ref="authorityAddRole" @save="save"></authorityAddRole>
	</div>
</template>
<script>
import authorityAddRole from './authority-add-role';
import authorityDrawer from './authority-drawer';
import menuTree from './menu-tree.vue';
import { isEmpty } from '@/utils';
import {
	deleteRole,
	getRoleList,
	getAllTree,
	getRoleMemberlist,
	getTreeList,
	roleCopy
} from '@/api/modules/authority';
import FileById from '@/components/file-by-id';
import { mapGetters } from 'vuex';
export default {
	components: { authorityDrawer, FileById, authorityAddRole, menuTree },
	data() {
		return {
			treeList: [], //组织tree
			/**表头每一行的样式*/
			headerRowStyle: {
				height: '48px',
				background: '#EDF2F6'
			},
			/**表头每一格样式*/
			headerCellStyle: {
				background: 'transparent',
				fontSize: '14px',
				fontFamily: 'PingFang SC, PingFang SC',
				fontWeight: 500,
				color: '#2f446b',
				lineHeight: '24px',
				height: '48px'
			},
			tableData: [],
			pageNo: 1, // 当前页
			total: 0, // 总数
			pageSizes: [5, 10, 20, 40], //分页数组
			pageSize: 10, // 当前分页数
			orgId: '',
			keywords: null,
			loading: false
		};
	},
	computed: {
		...mapGetters(['rentInfo', 'userInfo', 'userVersion'])
	},
	mounted() {
		this.getData();
		this.getOrgTree();
	},

	methods: {
		// 关闭抽屉
		closeDrawer() {
			this.getData();
		},
		/**根据组织id获取table数据 */
		searchRoleList(id) {
			this.orgId = id;
			this.pageNo = 1;
			this.getData();
		},
		/** 获取树组织数据 */
		getOrgTree() {
			getTreeList({ limitOrg: true }).then(res => {
				this.treeList = res.result;
				this.orgId = this.treeList[0]?.id || '';
			});
		},
		resetData() {
			this.orgId = null;
			this.pageNo = 1;
			this.getData();
		},
		/**用于接受到addRole 组件的保存信息后更新表单数据 */
		save() {
			this.getData();
		},
		/**详情操作 */
		detailclick(row) {
			this.$refs.authorityDrawer.open(row);
		},
		/**编辑操作 */
		async editclick(row) {
			let res1 = await getAllTree(row.id);
			row.permIds = [];

			if (res1.result.existTenant) {
				row.permIds = res1.result.rolePerms ? res1.result.rolePerms : [];
			}
			if (res1.result.existDesk) {
				row.permIds = res1.result.desk.rolePerms
					? row.permIds.concat(res1.result.desk.rolePerms)
					: row.permIds;
			}
			if (res1.result.existApplication) {
				row.permIds = res1.result.application.rolePerms
					? row.permIds.concat(res1.result.application.rolePerms)
					: row.permIds;
			}

			let res2 = await getRoleMemberlist(row.id);
			row.tableData = res2.result;
			this.$refs.authorityAddRole.open(row);
		},
		roleCopy(row) {
			this.$confirm('是否复制该条数据?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {
				roleCopy(row.id).then(res => {
					this.getData();
					this.$message({
						message: '操作成功',
						type: 'success'
					});
				});
			});
		},
		/**移除操作 */
		deleteclick(row) {
			this.$confirm('此操作将永久删除该角色, 是否继续?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {
				deleteRole(row.id).then(res => {
					this.getData();
					this.$message({
						message: res.message,
						type: 'success'
					});
				});
			});
		},
		/**搜索时调用*/
		doSearch(keywords) {
			this.keywords = keywords;
			this.pageNo = 1;
			this.getData();
		},
		/**请求数据*/
		getData(params) {
			if (!isEmpty(params)) {
				this.pageNo = 1; // 重置
			}
			this.loading = true;
			let obj = {
				pageNo: this.pageNo,
				pageSize: this.pageSize,
				keywords: this.keywords,
				limitOrg: true,
				orgId: this.orgId
			};
			getRoleList(obj).then(res => {
				if (!res) return; // 因为关闭了请求
				if (res.code === 200) {
					this.tableData = res.result.records || [];
					this.total = res.result.total;
					this.loading = false;
				} else {
					this.$message.error(res.message);
				}
			});
		},
		/**获取表格奇偶列的样式*/
		getRowStyle({ row, rowIndex }) {
			return {
				height: '56px',
				borderRadius: '6px',
				fontSize: '14px',
				fontFamily: 'PingFang SC, PingFang SC',
				fontWeight: '400',
				color: '#2f446b',
				lineHeight: '22px',
				background: rowIndex % 2 === 0 ? '#ffffff' : ' #f5f7fa'
			};
		},

		/**页码大小发生变化*/
		handleSizeChange(pageSize) {
			this.pageSize = pageSize;
			this.pageNo = 1;
			this.getData();
		},

		/**当前页数发生变化*/
		handleCurrentChange(page) {
			this.pageNo = page;
			this.getData();
		}
	}
};
</script>
<style lang="scss" scoped>
.all {
	height: 100%;
	padding: 0 24px;
	::-webkit-scrollbar {
		width: 0px;
	}
}
.content {
	display: flex;
	height: 100%;
	.content-tree {
		width: 300px;
		padding: 15px 17px 15px 0;
		background: #fff;
		border-radius: 0px 0px 0px 12px;
		border-top: 1px solid #f0f0f0;
		border-right: 1px solid #f0f0f0;
		overflow-y: auto;
		@include scrollBar;
		.tree-title {
			padding-left: 8px;
			cursor: pointer;
			display: flex;
			align-items: center;
			//justify-content: center;
			width: 100%;
			border-radius: 6px;
			margin-bottom: 8px;
			background: #f7f7f7;
			height: 42px;
			.logo {
				width: 24px;
				border-radius: 4px 4px 4px 4px;
			}
			.title-info {
				margin-left: 8px;
				font-size: 14px;
				font-weight: 400;
				line-height: 22px;
			}
		}
		.el-menu {
			border: none;
		}
	}
}
.authority-body {
	display: flex;
	flex-direction: column;
	flex-shrink: 0;
	padding: 15px 17px;
	height: 100%;
	width: calc(100% - 290px);
	// width: 100%;
	//min-width: 1350px;
	background: #ffffff;
	border-radius: 0px 0px 12px 0px;
	border-top: 1px solid #f0f0f0;
	font-size: 14px;
	font-weight: 400;
	color: $primaryTextColor;
	line-height: 12px;
	opacity: 1;
	&-pagination {
		height: 70px;
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	&-tableRole {
		width: 100%;
		display: flex;
		align-items: center;
		white-space: nowrap;
		&-title {
			display: inline-block;
			white-space: nowrap;
			overflow: hidden;
			text-overflow: ellipsis;
		}
		&-type {
			display: flex;
			align-items: center;
			justify-content: center;
			margin-left: 4px;
			padding: 6px 10px;
			font-size: 14px;
			line-height: 12px;
			height: 24px;
			white-space: nowrap;
			color: #1890ff;
			border: 1px solid #bae7ff;
			background-color: #e6f7ff;
			border-radius: $borderRadius;
		}
	}
	&-tableUser {
		display: flex;
		align-items: center;

		&-type {
			display: flex;
			align-items: center;
			margin-right: 4px;
			padding: 3px 4px;
			border-radius: 3px;
			background-color: #f2f3f5;
		}
		&-Type {
			flex-shrink: 0;
			padding: 2px 4px;
			margin-left: 1px;
			border-radius: 3px;
			text-align: center;
			background-color: #f2f3f5;
			color: #1890ff;
		}
	}
	&-tableperms {
		&-type {
			color: var(--brand-6);
		}
	}
	&-tableHandle {
		&-detailBtn {
			cursor: pointer;
			width: 56px;
			text-align: left;
			padding: 0;
			color: var(--brand-6);
			border: none;
			background-color: transparent;
		}
		&-deleteBtn {
			cursor: pointer;
			border: none;
			width: 56px;
			padding: 0;
			text-align: left;
			color: #ff7a7b;
			background-color: transparent;
		}
	}
	& ::v-deep.el-table td,
	.el-table th.is-leaf {
		border: none;
	}
}
</style>
