<template>
	<div class="Drawer">
		<el-drawer
			title="角色详情"
			:visible.sync="drawer"
			direction="rtl"
			custom-class="drawer"
			@close="close"
		>
			<div class="Drawer-Msg">
				<div class="Drawer-Msg-Header">
					<div class="Drawer-Msg-Header-Name">
						<div class="Drawer-Msg-Header-name">{{ roleMsg.roleName }}</div>
						<div v-if="roleMsg.isSysRole" class="Drawer-Msg-Header-type">系统角色</div>
					</div>
					<div class="Drawer-Msg-Header-description">{{ roleMsg.description }}</div>
				</div>
			</div>
			<el-menu
				class="Drawer-Msg-Header-menu"
				:default-active="Cindex"
				mode="horizontal"
				@select="handleSelect"
			>
				<el-menu-item index="0">成员</el-menu-item>
				<el-menu-item index="1">权限</el-menu-item>
			</el-menu>
			<authorityTable
				v-if="Cindex == '0'"
				ref="components"
				v-loading="loading"
				:role-msg="roleMsg"
				v-on="$listeners"
				@interfaceFn="interfaceFn"
			/>
			<authorityTree
				v-if="Cindex == '1'"
				ref="components"
				v-loading="loading"
				:role-msg="roleMsg"
				v-on="$listeners"
				@interfaceFn="interfaceFn"
			/>
		</el-drawer>
	</div>
</template>
<script>
import authorityTable from './authority-table';
import authorityTree from './authority-tree';
import { getRoleMsg } from '@/api/modules/authority';
export default {
	components: { authorityTable, authorityTree },
	data() {
		return {
			components: [authorityTable, authorityTree], //组件通过components  index 进行切换
			drawer: false, //抽屉显隐
			roleMsg: {}, //角色信息
			Cindex: '0', // components 对应index 且通过Cindex 来进行组件切换时的初始化
			loading: false, //loading
			roleId: ''
		};
	},
	methods: {
		close() {
			this.$emit('closeDrawer');
		},
		/** 初次打开抽屉的初始化*/
		async open(row) {
			this.drawer = true;
			this.Cindex = '0';
			this.loading = true;
			this.roleId = row.id;
			await getRoleMsg(this.roleId).then(res => {
				this.roleMsg = res.result;
				this.loading = false;
			});

			this.$nextTick(function () {
				//
				this.$refs.components.table(this.roleMsg.id); //输出：修改后的值
			});
		},
		/** 菜单切换 */
		handleSelect(key) {
			this.Cindex = key;
			this.loading = true;
			/** 切换至树形控件时 将人员表单数据带给树形控件角色信息 用于在进入编辑时可回显表单数据 */

			this.$nextTick(async function () {
				if (this.Cindex == '0') {
					this.$refs.components.getDrawertable(this.roleMsg.id);
					this.loading = false;
				} else if (this.Cindex == '1') {
					this.$refs.components.getTree(this.roleMsg.id);
					this.loading = false;
				}
			});
		},
		/**接受子组件传过来的值 arg 为多个参数的数组 [remove,{}] 第二个参数必须为对象  第一个参数默认为空时则进行添加或修改操作*/
		interfaceFn(...arg) {
			if (arg.includes('remove')) {
				Object.keys(arg[arg.indexOf('remove') + 1]).map(item => {
					this.roleMsg[item].splice(
						this.roleMsg[item].indexOf(arg[arg.indexOf('remove') + 1][item]),
						1
					); //arg[][] 第一个为[] index 第二个[] 则对应对象的键值
				});
			} else {
				Object.keys(arg[0]).map(item => {
					this.roleMsg[item] = arg[0][item];
				});
			}
		}
	}
};
</script>
<style lang="scss" scoped>
.Drawer {
	& ::v-deep .el-drawer__body {
		overflow: hidden;
	}
	& ::v-deep .el-drawer__header {
		font-size: 16px;
		font-weight: 600;
		color: $primaryTextColor;
		line-height: 24px;
		padding: 14px 0 18px 16px;
		margin: 0;
	}
	& ::v-deep .drawer {
		min-width: 500px;
	}

	&-Msg {
		padding: 2px 20px 0 20px;
		&-Header {
			display: flex;
			justify-content: center;
			flex-direction: column;

			&-description {
				font-size: 12px;
				font-weight: 400;
				color: $subTextColor;
				line-height: 20px;
				padding-bottom: 16px;
			}
			&-name {
				font-size: 16px;
				font-weight: 800;
				color: $primaryTextColor;
				line-height: 22px;
				padding: 16px 7px 10px 0px;
			}
			&-Name {
				display: flex;
				align-items: center;
			}
			&-type {
				width: 68px;
				height: 22px;
				border-radius: 3px;
				text-align: center;
				line-height: 22px;
				font-size: 12px;
				font-weight: 400;
				background: #e6f7ff;
				color: #1890ff;
				margin-top: 7px;
			}
			&-menu {
				& ::v-deep.el-menu-item.is-active {
					font-size: 16px;
					font-weight: 500;
					width: 32px;
					height: 38px;
					color: var(--brand-6);
					line-height: 22px;
					margin: 0px 16px;
					padding: 0;
				}
				& ::v-deep.el-menu-item {
					font-size: 16px;
					height: 38px;
					font-weight: 400;
					color: $textColor;
					line-height: 22px;
					padding: 0px 16px 12px;
				}
			}
		}
	}
}
</style>
