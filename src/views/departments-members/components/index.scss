.box {
	display: flex;
	height: 100%;
	.lf {
		height: 90%;
		width: 280px;
		border-right: 1px solid #f0f0f0;
		overflow: auto;
		@include noScrollBar;
	}
	.rf {
		height: 100%;
		width: calc(100% - 280px);
		display: flex;
		flex-direction: column;

		.head {
			padding-bottom: 16px;
			border-bottom: 1px solid #f0f0f0;

			&-title {
				display: flex;
				padding: 19px 20px 17px 16px;
				align-items: center;
			}

			.title {
				font-size: 18px;
				font-weight: 800;
				color: $primaryTextColor;
				line-height: 24px;
			}

			.num {
				margin-left: 25px;
				font-size: 14px;
				font-weight: 400;
				color: $primaryTextColor;
				line-height: 22px;

				b {
					margin-left: 5px;
					font-size: 16px;
					font-weight: 800;
					color: var(--brand-6);
					line-height: 22px;
				}
			}

			.btn {
				margin-left: auto;
				display: flex;
				align-items: center;
				cursor: pointer;

				img {
					width: 16px;
					height: 16px;
					margin-right: 5px;
				}

				span {
					font-size: 13px;
					font-weight: 400;
					color: $textColor;
					line-height: 22px;
				}
			}
		}

		.search {
			padding: 0 16px;

			.btm {
				width: 100%;
				height: 100%;
				padding: 20px 0;
				box-sizing: border-box;
				display: flex;
				flex-direction: row-reverse;
				.btn {
					margin-left: 10px;
					height: 36px;
				}
			}
		}

		.table {
			flex: 1;
			// height: calc(100% - 180px);
			padding: 0 16px;
			overflow: hidden;
		}

		.page {
			margin-top: 15px;
			display: flex;
			justify-content: center;
			margin-bottom: 20px;
		}
	}
}
::v-deep .el-dialog {
	min-width: 750px;
	min-height: 600px;
	height: 80%;
	max-height: 80%;
	width: 70%;
	margin-top: 10vh !important;
	-webkit-box-shadow: 0px 8px 10px -5px rgba(0, 0, 0, 0.08), 0px 16px 24px 2px rgba(0, 0, 0, 0.04),
		0px 6px 30px 5px rgba(0, 0, 0, 0.05);
	box-shadow: 0px 8px 10px -5px rgba(0, 0, 0, 0.08), 0px 16px 24px 2px rgba(0, 0, 0, 0.04),
		0px 6px 30px 5px rgba(0, 0, 0, 0.05);
	border-radius: 16px;
	margin-bottom: 0;
	.el-dialog__body {
		height: calc(100% - 120px);
	}
	.dialog-box {
		display: block;
		// min-height: 300px;
		padding: 12px 38px;
		width: 100%;
		overflow-y: auto;
		height: 100%;
		box-sizing: border-box;

		.head {
			padding: 0;
			height: 49px;
			display: flex;
			border-bottom: none;
			align-items: center;
			width: 100%;

			.title {
				font-size: 18px;
				font-weight: 800;
				color: #303133;
				line-height: 24px;
			}

			.close {
				margin-left: auto;
				font-size: 18px;
				color: $textColor;
				cursor: pointer;
			}
		}

		.btn-title {
			margin: 29px 0 24px 0;
			display: flex;
			width: 100%;

			.tit {
				height: 22px;
				font-size: 16px;
				font-weight: 800;
				color: $primaryTextColor;
				line-height: 22px;
				border-left: 4px solid var(--brand-6);
				padding: 0 19px 0 3px;
				span {
					color: #e34d59;
					font-size: 14px;
					margin-left: 10px;
				}
			}

			.btn {
				cursor: pointer;
				margin-left: auto;
				display: flex;
				align-items: center;

				img {
					width: 16px;
					height: 16px;
					margin-right: 3px;
				}

				span {
					font-size: 14px;
					font-weight: 400;
					color: $textColor;
					line-height: 22px;
				}
			}
		}

		.tips {
			margin-top: 33px;

			.tit {
				display: flex;
				align-items: center;
				margin-bottom: 11px;
				font-size: 14px;
				font-weight: 800;
				color: $textColor;
				line-height: 22px;

				img {
					width: 16px;
					height: 16px;
					margin-left: 5px;
				}
			}

			.list {
				margin-top: 13px;
				display: flex;
				align-items: center;

				img {
					display: block;
					width: 10px;
					height: 9px;
				}

				.name {
					margin-left: 7px;
					font-size: 14px;
					font-weight: 400;
					color: $primaryTextColor;
					line-height: 22px;

					b {
						margin-left: 11px;
						font-size: 14px;
						font-weight: 400;
						color: var(--brand-6);
						line-height: 22px;
					}

					i {
						font-size: 14px;
						color: var(--brand-6);
						margin-left: 4px;
					}
				}
			}

			.open {
				cursor: pointer;
			}
		}
	}

	.dialog-footer {
		// margin-top: 24px;
		text-align: right;
	}

	.el-header {
		padding-top: 4px;
	}

	.el-aside {
		width: 260px !important;
	}

	.right-panel {
		width: 305px !important;
	}

	.main-container {
		min-width: 400px !important;
	}

	.head {
		height: 53px;
		display: flex;
		padding: 0 15px;
		border-bottom: 1px solid #f0f0f0;

		.lf {
			width: 280px;
			display: flex;
			align-items: center;

			.btn {
				padding: 0;
				align-items: center;
				width: 60px;
				height: 32px;

				span {
					margin-left: 0;
				}
			}

			.inp {
				margin: -3px 0 0 11px;
				width: 157px;
				height: 40px;
				background: #f2f5f8;
				border-radius: 21px;
				display: flex;
				align-items: center;
				padding: 0 11px;

				.iconfont {
					display: block;
					font-size: 23px;
					color: rgba(58, 156, 237, 1);
				}

				.search {
					width: 116px;

					.el-input__inner {
						border: none;
						background: transparent;
						padding: 0 0 0 5px;
					}
				}
			}
		}

		.mid {
			width: 600px;
			display: flex;
			justify-content: space-between;

			.list {
				cursor: pointer;
				display: flex;
				align-items: center;

				b {
					display: block;
					width: 22px;
					height: 22px;
					background: #abb8ce;
					opacity: 1;
					text-align: center;
					line-height: 22px;
					border-radius: 50%;
					font-size: 14px;
					font-weight: 800;
					color: #ffffff;
					line-height: 22px;
				}

				span {
					font-size: 16px;
					font-weight: 400;
					color: $subTextColor;
					line-height: 24px;
					display: block;
					margin-left: 2px;
				}
			}

			.act {
				b {
					background: rgba(39, 120, 229, 1);
					color: #fff;
				}

				span {
					color: rgba(39, 120, 229, 1);
				}
			}
		}

		.rf {
			margin-left: auto;
			display: flex;
			align-items: center;
			justify-content: right;

			.btn {
				font-size: 14px;
				font-weight: 400;
				color: $textColor;
				line-height: 22px;
				width: 88px;
				height: 40px;
				text-align: center;
				line-height: 40px;
				cursor: pointer;
			}

			.btn:hover {
				color: var(--brand-6);
			}
		}
	}
}
.custom-dialog {
	& > ::v-deep .el-dialog {
		height: calc(100% - 40px);
		margin-bottom: 0 !important;
		border-radius: 16px 16px 0 0;
		display: flex;
		flex-direction: column;
		& > .el-dialog__body {
			flex: 1;
			overflow: hidden;
			padding: 0 0 0 30px !important;
		}
	}
}
.drawer {
	padding: 0 16px;
	height: calc(100vh - 130px);
	overflow-y: auto;

	.user {
		display: flex;
		align-items: center;
		padding: 9px 0;

		img {
			width: 50px;
			height: 50px;
			border-radius: 6px;
		}

		.name {
			margin-left: 8px;

			div {
				height: 22px;
				font-size: 16px;
				font-family: PingFang SC, PingFang SC;
				font-weight: 800;
				color: $primaryTextColor;
				line-height: 22px;
				margin-bottom: 8px;
			}

			p {
				margin: 0;
				font-size: 12px;
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				color: $holderTextColor;
				line-height: 20px;
			}
		}

		.btn {
			margin-left: auto;
			display: flex;
			align-items: center;

			img {
				width: 15px;
				height: 14px;
				margin-left: 5px;
			}
		}
	}

	&-footer {
		height: 53px;
		border-top: 1px solid #f0f0f0;
		display: flex;
		align-items: center;
		padding-right: 15px;
		justify-content: flex-end;
	}
}
