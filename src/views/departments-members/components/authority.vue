<template>
	<div class="all">
		<div class="authority-Management">
			<!--搜索-->
			<authorityheader class="authorityheader" @search="search" @save="save" />
			<!--表格-->
			<authoritybody ref="table" class="authoritybody" />
		</div>
	</div>
</template>
<script>
import authorityheader from './authority-header';
import authoritybody from './authority-body';
export default {
	name: 'Authority',
	components: { authorityheader, authoritybody },
	methods: {
		search(params) {
			this.$refs.table.doSearch(params); //组件authority-header搜索 通知body表更新数据
		},
		save() {
			this.$refs.table.getData(); //组件authority-addrole新增角色后 通知body表更新数据
		}
	}
};
</script>
<style lang="scss" scoped>
.all {
	height: calc(100% - 54px);
	display: flex;
	flex-direction: column;

	.authority-Management {
		height: calc(100% - 54px);
	}
	.authorityheader {
		height: 54px;
	}
	.authoritybody {
		flex: 1;
	}
}
</style>
