<template>
	<div v-loading="loading" class="authority-AssignPermissionItem">
		<div class="titleInfo">
			<h2 class="title">分配角色权限/菜单</h2>
			<div v-if="titleInfo.length >= 2" class="titleChoose">
				<div v-for="item in titleInfo" :key="item.id">
					<div
						v-if="item.show"
						class="titleOne"
						:class="{ clicked: isChoose == item.id }"
						@click="handleChoose(item.id)"
					>
						{{ item.title }}
					</div>
				</div>
				<!-- <div class="titleOne" :class="{ clicked: !isChoose }" @click="handleChoose(0)">
					管理权限
				</div>
				<div class="titleTwo" :class="{ clicked: isChoose }" @click="handleChoose(1)">
					自定义菜单
				</div>
				<div class="titleTwo" :class="{ clicked: isChoose }" @click="handleChoose(2)">
					自定义菜单
				</div> -->
			</div>
		</div>
		<div :key="isChoose" class="authority-AssignPermissionItem-tree">
			<el-tree
				ref="tree"
				:data="treeList"
				show-checkbox
				check-on-click-node
				default-expand-all
				node-key="key"
				:check-strictly="true"
				:default-checked-keys="initData"
				@check="handleclick"
				@check-change="handleCheckChange"
			></el-tree>
		</div>
	</div>
</template>
<script>
import { getAllTree } from '@/api/modules/authority';
import { deepClone } from '@/utils';

export default {
	props: {
		roleMsg: {
			type: Object,
			default: () => {
				return {
					treedata: [],
					permIds: []
				};
			}
		},
		checkedData: {
			type: Array,
			default: () => []
		},
		treeData: {
			type: Array,
			default: () => []
		}
	},
	data() {
		return {
			check: this.roleMsg.permIds,
			treeList: this.treeData,
			checkStrictly: true,
			loading: false,
			initData: [],
			//权限类型选择判断
			isChoose: 0,
			/**桌面端权限 */
			existDesk: false,
			/**管理端权限 */
			existTenant: false,
			existApplication: false,
			deskList: [],
			tenantList: [],
			applicationList: [],
			combinedArray: [],
			limitPerm: false,
			limitPermIds: [],
			titleInfo: [
				{
					id: 0,
					title: '管理权限',
					rolePerms: [],
					show: false
				},
				{
					id: 1,
					rolePerms: [],
					title: '自定义权限',
					show: false
				},
				{
					id: 2,
					rolePerms: [],
					title: '应用功能',
					show: false
				}
			]
		};
	},
	mounted() {
		this.setChecked();
		// this.initData = deepClone(this.roleMsg.permIds);
	},
	methods: {
		handleChoose(index) {
			this.isChoose = index;
			this.checkStrictly = true;

			// if (this.existDesk && this.existTenant) {
			// let combinedArray = this.tenantList.concat(this.deskList);
			// this.$emit('changeList', combinedArray);
			this.$emit('changeList', this.combinedArray);
			if (index == 0) {
				this.treeList = this.tenantList;
			} else if (index == 1) {
				this.treeList = this.deskList;
			} else {
				this.treeList = this.applicationList.map(item => {
					item.disabled = !item.canCheck;
					return item;
				});
			}
			this.setChecked();
			// }
		},

		setChecked() {
			let arr = deepClone(this.roleMsg.permIds);
			if (!arr.length) {
				return;
			}
			this.loading = true;
			setTimeout(() => {
				const that = this;
				arr.forEach(element => {
					let node = that.$refs.tree.getNode(element);
					if (node) {
						that.$refs.tree.setChecked(node, true);
					}
				});
				this.loading = false;
			}, 100);
		},
		//获取树形控件初始化树
		async getTree() {
			return await new Promise((resolve, reject) => {
				getAllTree(this.roleMsg.id).then(res => {
					console.log(res, 'res');
					this.existDesk = res.result.existDesk;
					this.limitPerm = res.result.limitPerm || false;
					this.limitPermIds = res.result.limitPermIds || [];
					this.titleInfo[1].show = res.result.existDesk;
					this.existTenant = res.result.existTenant;
					this.titleInfo[0].show = res.result.existTenant;
					this.existApplication = res.result.existApplication;
					this.titleInfo[2].show = res.result.existApplication;
					this.titleInfo[0].rolePerms = res.result?.tenant?.rolePerms || [];
					this.titleInfo[1].rolePerms = res.result?.desk?.rolePerms || [];
					this.titleInfo[2].rolePerms = res.result?.application?.rolePerms || [];
					const firstShownPermission = this.titleInfo.find(permission => permission.show === true);
					this.isChoose = firstShownPermission.id;
					this.setChecked();
					this.deskList = res.result.desk ? res.result.desk.treeList : [];
					this.tenantList = res.result.treeList;
					this.applicationList = res.result.application ? res.result.application.treeList : [];

					if (this.existTenant) {
						this.treeList = this.tenantList;
						// this.treeList = this.tenantList.map(item => {
						// 	delete item.children;
						// 	return item;
						// });
						if (this.existDesk) {
							this.combinedArray = this.tenantList.concat(this.deskList);
							if (this.existApplication) {
								this.combinedArray = this.combinedArray.concat(this.applicationList);
							}
						} else {
							this.combinedArray = this.tenantList;
							if (this.existApplication) {
								this.combinedArray = this.tenantList.concat(this.applicationList);
							}
						}
					} else {
						// 如果不存在租户，则按照之前的逻辑处理办公桌和应用列表
						if (this.existDesk) {
							this.treeList = this.deskList;
							if (this.existApplication) {
								this.combinedArray = this.deskList.concat(this.applicationList);
							} else {
								this.combinedArray = this.deskList;
							}
						} else {
							this.treeList = this.applicationList;
							this.combinedArray = this.applicationList;
						}
					}
					if (this.limitPerm) {
						this.setDisabled(this.treeList);
						console.log(this.treeList);
					}
					resolve(this.combinedArray);
				});
			});
		},
		setDisabled(treeList) {
			treeList.forEach(node => {
				node.disabled = !this.limitPermIds.includes(node.key);
				if (node.children && node.children.length > 0) {
					this.setDisabled(node.children);
				}
			});
		},
		//当点击权限时对当前点击权限进行处理
		handleCheckChange(Data, Node) {
			let getHalfCheckedNodes = this.$refs.tree.getHalfCheckedNodes();
			let getCheckedNodes = this.$refs.tree.getCheckedNodes();
			// if (!Node) {
			// 	this.check = this.check.filter(item => item != Data.key);
			// }
			let arr = [];
			getHalfCheckedNodes.forEach(item => {
				arr.push(item.key);
			});
			getCheckedNodes.forEach(item => {
				arr.push(item.key);
			});
			this.titleInfo[this.isChoose].rolePerms = arr
				.sort()
				.filter((item, index, sortedArr) => item !== sortedArr[index - 1]);
			let newArr = [];
			this.titleInfo.forEach(item => {
				newArr = newArr.concat(item.rolePerms);
			});
			this.check = newArr;
			this.$emit('interfaceFn', { permIds: this.check });
		},
		handlenodeclick(Data, Node) {
			Node.checked = !Node.checked;
			// this.parentProccess(Node);
			this.childrenProccess(Node);
		},
		parentProccess(Node) {
			if (Node.parent) {
				Node.parent.checked = true;
				this.parentProccess(Node.parent);
			}
		},
		childrenProccess(Node, checked) {
			Node.childNodes.forEach(item => {
				/**因为设置了check-strictly不强制关联，所有方法setChecked参数deep无效*/
				this.$refs.tree.setChecked(item.data.id, checked, true);
				if (item.childNodes.length > 0) {
					this.childrenProccess(item, checked);
				}
			});
		},
		// 实现权限向下级联
		handleclick(currentObj, treeStatus) {
			// 用于：父子节点严格互不关联时，父节点勾选变化时通知子节点同步变化，实现单向关联。
			let selected = treeStatus.checkedKeys.indexOf(currentObj.id); // -1未选中,>=0为选中
			// 选中
			if (selected !== -1) {
				// 子节点只要被选中父节点就被选中(需要选中父节点时候调用此方法)
				this.selectedParent(currentObj);
				// 统一处理子节点为相同的勾选状态
				this.uniteChildSame(currentObj, true);
			} else {
				// 未选中 处理子节点全部未选中
				if (currentObj.children && currentObj.children.length !== 0) {
					this.uniteChildSame(currentObj, false);
				}
			}
		},
		// 统一处理子节点为相同的勾选状态
		uniteChildSame(treeList, isSelected) {
			this.$refs.tree.setChecked(treeList.id, isSelected);
			if (treeList.children) {
				for (let i = 0; i < treeList.children.length; i++) {
					this.uniteChildSame(treeList.children[i], isSelected);
				}
			}
		},
		// 统一处理父节点为选中
		selectedParent(currentObj) {
			let currentNode = this.$refs.tree.getNode(currentObj);
			if (currentNode.parent.key !== undefined) {
				this.$refs.tree.setChecked(currentNode.parent, true);
				this.selectedParent(currentNode.parent);
			}
		},
		parentChecked(Node, checked) {
			if (Node.parent.parent) {
				this.$refs.tree.setChecked(Node.parent.data.id, checked, true);
				this.parentChecked(Node.parent, Node.parent.checked);
			} else {
				this.$refs.tree.setChecked(Node.data.id, checked, true);
			}
		}
	}
};
</script>
<style lang="scss" scoped>
.authority-AssignPermissionItem {
	height: 100%;
	.titleInfo {
		margin-bottom: 15px;
		.titleChoose {
			display: flex;

			border-bottom: 1px solid #f0f0f0;
			font-size: 16px;
			color: $textColor;
			line-height: 22px;
			font-weight: 400;
			.titleOne {
				cursor: pointer;
				margin-right: 32px;
				padding-bottom: 12px;
			}
			.titleTwo {
				cursor: pointer;
				padding-bottom: 12px;
				margin-right: 32px;
			}
			.clicked {
				font-weight: 500;
				font-size: 16px;
				color: var(--brand-6);
				border-bottom: 2px solid var(--brand-6);
			}
		}
	}
	&-tree {
		height: 80%;
	}
}
.title {
	font-size: 21px;
	color: #000;
	font-weight: 700;
	font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, Segoe UI, Arial,
		Roboto, 'PingFang SC', miui, 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;
}
</style>
