<template>
	<div>
		<PersonnelDialog v-if="isDev" v-bind="$attrs" v-on="$listeners"></PersonnelDialog>
		<async-component
			v-else
			v-bind="$attrs"
			:umd-url="assetsUrlPre + PersonnelDialog"
			v-on="$listeners"
		></async-component>
	</div>
</template>

<script>
import asyncComponent from '@/components/async-component';
import { assetsUrl } from '@/config';

export default {
	name: 'OrgPersonnelDialog',
	components: { asyncComponent },
	data() {
		return {
			isDev: process.env.VUE_APP_ENV === 'development',
			assetsUrlPre: assetsUrl + '/common/umd',
			PersonnelDialog: '/PersonnelDialog.umd.js'
		};
	}
};
</script>

<style scoped lang="scss"></style>
