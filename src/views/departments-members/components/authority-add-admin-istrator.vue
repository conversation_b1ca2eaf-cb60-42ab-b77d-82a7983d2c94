<template>
	<div class="authority-AddAdministrator">
		<orgPersonnelDialog
			:key="key"
			append-to-body
			:title="dialogTitle"
			:can-select-depart="dataSource.includes('depart') && !dataSource.includes('user')"
			:include-users="dataSource.includes('user')"
			:visible="visible"
			:data-source="dataSource"
			:init-values="initValue"
			:disable-all="true"
			:need-all-data="true"
			@change="changeSelect"
			@sure="sure"
			@close="close"
		></orgPersonnelDialog>
	</div>
</template>
<script>
import orgPersonnelDialog from './umd-view.vue';
import {
	saveRoleMemberlist,
	savePostlist,
	saveIdentityslist,
	saveDeptsList,
	saveLabelList
} from '@/api/modules/authority';
export default {
	components: {
		orgPersonnelDialog
	},
	props: {
		dialogTitle: {
			type: String,
			default: () => {
				return '';
			}
		},
		roleMsg: {
			type: Object,
			default: () => {}
		},
		dataSource: {
			type: Array,
			default: () => []
		},
		types: {
			default: 'userIds',
			type: String
		}
	},
	data() {
		return {
			key: 1,
			visible: false,
			// dataSource: ['user', 'depart'], // 'label''depart',
			rolelist: [],
			tableData: this.roleMsg.tableData //接受父组件roleMsg中的tableData数据
		};
	},
	computed: {
		initValue() {
			return this.tableData;
		}
	},
	methods: {
		//初始加载时对父组件的table数据进行格式化
		roleMsgTable() {
			this.key += 1;
			this.$nextTick(() => {
				this.visible = true;
				if (this.roleMsg.tableData) {
					this.tableData = this.roleMsg.tableData.map(item => {
						if (this.types === 'userIds' || !this.types) {
							return {
								dataType: 'user',
								title: item.title ? item.title : item.realname,
								id: item.userId ? item.userId : item.id,
								avatarUrl: item.avatarUrl,
								logo: item.logo
							};
						} else if (this.types === 'postIds') {
							return {
								dataType: 'post',
								title: item.postName,
								id: item.id,
								postName: item.postName,
								postTypeDictText: item.postTypeDictText
							};
						} else if (this.types === 'departIds') {
							return {
								...item,
								dataType: 'depart',
								title: item.departName
							};
						} else if (this.types === 'labelIds') {
							return {
								...item,
								dataType: 'label',
								title: item.labelTitle
							};
						} else {
							return {
								dataType: 'identity',
								title: item.userDepartJobTitle,
								id: item.id,
								departName: item.departName,
								orgIdDictText: item.userDepartJobTitle,
								realname: item.realname,
								jobTitle: item.jobTitle
							};
						}
					});
				}
			});
		},
		/**人员选择完成后，确定操作*/
		sure() {
			// 通过判断controller 来确定是新增角色时的操作 还是在编辑角色时的操作
			if (this.roleMsg.controller) {
				const reqFnMap = {
					postIds: savePostlist,
					identityIds: saveIdentityslist,
					userIds: saveRoleMemberlist,
					departIds: saveDeptsList,
					labelIds: saveLabelList
				};

				const reqFn =
					reqFnMap[this.types] ||
					(() => {
						console.error('Unknown type:', this.types);
						throw new Error(`Unsupported type: ${this.types}`);
					});

				reqFn(this.roleMsg.id, this.rolelist).then(() => {
					this.$emit('saveToTable', this.roleMsg.id); //通知最外层进行数据更新 (编辑角色时)
					this.visible = false;
				});
			} else {
				this.$emit('listenToTable', this.rolelist); //通知最外层进行数据更新 (新增角色时)
				this.visible = false;
			}
		},
		/**取消操作*/
		close() {
			this.visible = false;
		},
		/**人员选择发生变化*/
		changeSelect(val) {
			// 通过判断controller 来确定是新增角色时的操作 还是在编辑角色时的操作
			if (this.roleMsg.controller) {
				this.rolelist = val.map(item => item.id); //为通知最外层进行数据更新，格式化通知携带参数 (编辑角色时)
			} else {
				this.rolelist = val.map(item => item); //为通知最外层进行数据更新，格式化通知携带参数  (新增角色时)
			}
		}
	}
};
</script>
<style></style>
