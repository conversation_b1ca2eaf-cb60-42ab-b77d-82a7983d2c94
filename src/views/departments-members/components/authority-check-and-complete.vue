<template>
	<div class="authority-CheckAndComplete">
		<div class="authority-CheckAndComplete-title">检查并完成</div>
		<div class="authority-CheckAndComplete-container">
			<div class="authority-CheckAndComplete-container-label">角色名称</div>
			<div class="authority-CheckAndComplete-container-inp">{{ roleMsg.roleName }}</div>
			<div class="authority-CheckAndComplete-container-label">角色说明</div>
			<div v-if="roleMsg.description" class="authority-CheckAndComplete-container-inp">
				{{ roleMsg.description }}
			</div>
			<div v-else class="authority-CheckAndComplete-container-inp">暂无</div>
			<div class="authority-CheckAndComplete-container-label">
				成员包含
				<!-- <div class="authority-CheckAndComplete-tableUser-Type">
					共 {{ roleMsg.tableData ? roleMsg.tableData.length : 0 }}人
				</div> -->
			</div>
			<div class="authority-CheckAndComplete-container-tableUser" style="margin-bottom: 20px">
				<div
					v-if="roleMsg.tableData || roleMsg.userIds"
					class="authority-CheckAndComplete-tableUser"
				>
					<div v-for="(item, index) in roleMsg.userIds || roleMsg.tableData" :key="index">
						<div class="authority-CheckAndComplete-tableUser-name">
							<FileById
								:size="18"
								:value="item.avatarUrl"
								:more-style="{ borderRadius: '3px', marginRight: '3px' }"
								:default-font-icon="item.realname ? item.realname.slice(-1) : item.title.slice(-1)"
								class="authority-AddRoleMember-img"
							></FileById>
							<div style="white-space: nowrap">
								{{ item.realname ? item.realname : item.title }}
							</div>
						</div>
					</div>
				</div>
				<div v-else>
					<div>暂无</div>
				</div>
			</div>
			<div v-if="roleMsg.postIds" class="authority-CheckAndComplete-container-label">岗位包含</div>
			<div class="authority-CheckAndComplete-container-post">
				<div v-if="roleMsg.postIds" class="items">
					<div v-for="(item, index) in roleMsg.postIds" :key="index">
						{{ item.postName ? item.postName : '暂无' }}
					</div>
				</div>
			</div>
			<div v-if="roleMsg.identityIds" class="authority-CheckAndComplete-container-label">
				身份包含
			</div>
			<div class="authority-CheckAndComplete-container-post">
				<div class="items">
					<div v-for="(item, index) in roleMsg.identityIds" :key="index">
						{{ item.realname ? item.realname : '暂无' }}
						<span v-if="item.departName">({{ item.departName }})</span>
					</div>
				</div>
			</div>
			<div v-if="roleMsg.deptIds" class="authority-CheckAndComplete-container-label">部门包含</div>
			<div class="authority-CheckAndComplete-container-post">
				<div class="items">
					<div v-for="(item, index) in roleMsg.deptIds" :key="index">
						{{ item.departName ? item.departName : '暂无' }}
					</div>
				</div>
			</div>
			<div v-if="roleMsg.labelIds" class="authority-CheckAndComplete-container-label">标签包含</div>
			<div class="authority-CheckAndComplete-container-post">
				<div class="items" style="margin-bottom: 0">
					<div v-for="(item, index) in roleMsg.labelIds" :key="index">
						{{ item.title ? item.title : item.labelTitle ? item.labelTitle : '暂无' }}
					</div>
				</div>
			</div>

			<div class="authority-CheckAndComplete-tree">
				<div class="authority-CheckAndComplete-tree-title">分配权限内容</div>
				<el-tree
					ref="tree"
					:data="newArr"
					:check-strictly="true"
					node-key="key"
					:default-checked-keys="treePermsid"
					default-expand-all
				>
					<span
						slot-scope="{ node }"
						:style="{
							display: 'flex',
							alignItems: 'center',
							margin: node.data.parentId ? '8px 0' : '12px 0'
						}"
					>
						<template>
							<div>
								<!-- <svg-icon
									v-if="node.checked"
									icon-class="checked"
									class="authority-CheckAndComplete-tree-icon"
								></svg-icon> -->
								<span class="authority-CheckAndComplete-tree-label">{{ node.label }}</span>
							</div>
						</template>
					</span>
				</el-tree>
			</div>
		</div>
	</div>
</template>
<script>
import { deepClone } from '@/utils';
import { getPostPage } from '@/api/modules/dynamic';
import { getDeptList, getIdentityList, getLabelList } from '@/api/modules/component';
export default {
	props: {
		roleMsg: {
			type: Object,
			default: () => {
				return {};
			}
		}
	},
	data() {
		return {
			treePermsid: [],
			newArr: [],
			arr: []
		};
	},
	created() {
		let userData = JSON.parse(localStorage.getItem('coos_rent_rentInfo'));
		this.version = JSON.parse(userData.extend).moreSetting?.version || 'v1';
		if (this.roleMsg.id && this.version == 'v2') {
			// this.getList(this.roleMsg.id);
		}
		if (this.roleMsg.id) {
			// this.getAllList(this.roleMsg.id);
		}
	},
	methods: {
		async treeDateProcessor() {
			return await new Promise((resolve, reject) => {
				let arr = deepClone(this.roleMsg.treedata);
				/** 构建树结构 */
				this.treedateProcessor(arr);
				/** 针对有父节点 无子节点的勾选问题 */
				this.newArr.forEach(item => {
					this.treePermsid.push(item.id);
				});
				/** 树结构二次处理及其勾选问题 */
				this.newArr = this.treeSecondDateProcessor(this.newArr);
				resolve();
			});
		},
		/** 构建树结构 */
		treedateProcessor(arr) {
			arr.forEach((item, index, element) => {
				if (this.roleMsg.permIds.includes(item.id)) {
					this.newArr.push(item);
					return;
				}
				if (item.children) {
					this.treedateProcessor(item.children);
				}
			});
		},
		/** 树结构二次处理及其勾选问题 */
		treeSecondDateProcessor(arr) {
			return arr.filter(item => {
				if (this.roleMsg.permIds.includes(item.id) && item.children) {
					if (!this.treePermsid.includes(item.id)) {
						this.treePermsid.push(item.id);
					}
					item.children = this.treeSecondDateProcessor(item.children);
				}
				return this.roleMsg.permIds.includes(item.id);
			});
		},
		// 获取部门、标签
		getAllList(id) {
			getDeptList({
				roleId: id,
				status: 1,
				pageNo: 1,
				pageSize: 999,
				keywords: this.keywords
			}).then(res => {
				this.roleMsg.deptIds = res.result.records;
				this.$forceUpdate();
			});
			getLabelList({
				roleId: id,
				status: 1,
				pageNo: 1,
				pageSize: 999,
				keywords: this.keywords
			}).then(res => {
				this.roleMsg.labelIds = res.result.records;
				this.$forceUpdate();
			});
		},
		// 获取岗位、身份数据
		getList(id) {
			getPostPage({
				roleId: id,
				status: 1,
				pageNo: 1,
				pageSize: 999,
				keywords: this.keywords
			}).then(res => {
				this.roleMsg.postIds = res.result.records;
				this.$forceUpdate();
			});
			getIdentityList({
				roleId: id,
				status: 1,
				pageNo: 1,
				pageSize: 999,
				keywords: this.keywords
			}).then(res => {
				this.roleMsg.identityIds = res.result.records;
				this.$forceUpdate();
			});
		}
	}
};
</script>
<style lang="scss" scoped>
.authority-CheckAndComplete {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	overflow-y: hidden;
	&-title {
		flex-shrink: 0;
		margin-bottom: 26px;
		font-weight: 800;
		font-size: 18px;
		color: $primaryTextColor;
		line-height: 24px;
		text-align: left;
		font-style: normal;
		text-transform: none;
	}
	&-container {
		flex: 1;
		display: flex;
		flex-direction: column;
		@include scrollBar();
		overflow-y: auto;
		&-label {
			font-weight: 500;
			font-size: 14px;
			color: $subTextColor;
			line-height: 22px;
			text-align: left;
			font-style: normal;
			text-transform: none;
			margin-bottom: 9px;
		}
		&-inp {
			margin-bottom: 21px;
			font-weight: 400;
			font-size: 16px;
			color: $textColor;
			line-height: 24px;
			text-align: left;
			font-style: normal;
			text-transform: none;
		}
	}
	&-tableUser {
		display: flex;
		align-items: center;
		width: 100%;
		flex-wrap: wrap;
		&-name {
			display: flex;
			align-items: center;
			margin: 0 16px 9px 0;
			background-color: #f2f3f5;
			border-radius: 3px;
			padding: 2px 4px;
			font-weight: 400;
			font-size: 14px;
			color: $primaryTextColor;
			line-height: 20px;
			text-align: left;
			font-style: normal;
			text-transform: none;
		}
		//&-type {
		//	width: 1.125rem;
		//	height: 1.125rem;
		//	margin: 0.25rem;
		//	display: flex;
		//	align-items: center;
		//	justify-content: center;
		//	border-radius: 0.125rem;
		//	font-size: 0.625rem;
		//	color: #ffffff;
		//	background-color: var(--brand-6,#0f45ea);
		//}
		&-Type {
			display: inline-block;
			margin-left: 12px;
			color: #1890ff;
		}
	}
	&-post {
		// display: flex;
		// align-items: center;
		width: 100%;
		// flex-wrap: wrap;
	}

	&-tree {
		flex: 1;
		&-icon {
			margin-left: 3px;
			width: 14px;
			height: 14px;
		}
		&-label {
			margin-left: 9px;
			//   font-size: 14px;
			// font-weight: 400;
			// color: $subTextColor;
			// line-height: 22px;
		}
		&-title {
			font-weight: 500;
			font-size: 14px;
			color: $subTextColor;
			line-height: 22px;
			text-align: left;
			font-style: normal;
			text-transform: none;
			margin: 28px 0 12px;
		}
		& ::v-deep .el-tree {
			border-radius: 9px;
			background: #f5f7fa;
			padding-left: 28px;
		}
		& ::v-deep .expanded.el-tree-node__expand-icon.el-icon-caret-right {
			display: none;
		}
		& ::v-deep .is-leaf.el-tree-node__expand-icon.el-icon-caret-right {
			display: none;
		}
		& ::v-deep .el-tree-node__content {
			height: auto;
		}
	}
}

.items {
	display: flex;
	flex-wrap: wrap;
	margin-bottom: 20px;
	div {
		margin-right: 10px;
		font-size: 16px;
		color: $textColor;
		margin-bottom: 8px;
	}
}
</style>
