<template>
	<div v-loading="loading" class="authority-Tree">
		<div v-if="!roleMsg.isSysRole" class="authority-Tree-button">
			<div class="authority-Tree-btn" @click="openAddRola">
				<svg-icon slot="suffix" class="authority-Tree-search-icon" icon-class="key"></svg-icon>
				编辑权限项
			</div>
		</div>
		<div class="authority-Tree-tree">
			<el-tree
				:key="key"
				ref="tree"
				:check-strictly="true"
				:data="treedata"
				node-key="key"
				:props="props"
				:default-checked-keys="permIdsProcessor"
				default-expand-all
			>
				<span
					slot-scope="{ node }"
					:style="{
						display: 'flex',
						alignItems: 'center',
						margin: node.data.parentId ? '8px 0' : '12px 0'
					}"
				>
					<template>
						<div>
							<!-- 	<svg-icon
								v-if="node.checked"
								icon-class="checked"
								class="authority-Tree-icon"
							></svg-icon> -->

							<div style="margin-left: 17px"></div>
							<span class="authority-Tree-label">
								{{ node.label }}
							</span>
						</div>
					</template>
				</span>
			</el-tree>
		</div>
		<authorityAddRole ref="authorityAddRole" :pramas="pramas" v-on="$listeners" @save="save" />
	</div>
</template>
<script>
import { getAuthorityTree } from '@/api/modules/authority';
import authorityAddRole from './authority-add-role';

export default {
	components: { authorityAddRole },
	props: {
		roleMsg: {
			type: Object,
			default: () => {}
		}
	},
	data() {
		return {
			loading: false,
			id: '',
			/**为树形控件添加只读属性*/
			props: {
				disabled: this.disabled,
				title: 'label'
			},
			btn: false,
			key: 0,
			permIdsProcessor: [], //tree 默认选中处理后的数据
			pramas: {},
			treedata: [],
			permIds: []
		};
	},
	methods: {
		/**给详情添加只读 */
		disabled(data, node) {
			return true;
		},
		/**获取角色对应权限树 */
		getTree(id) {
			this.id = id;
			this.loading = true;
			getAuthorityTree(id).then(res => {
				this.permIds = [];
				this.permIdsProcessor = [];
				this.treedata = res.result;
				this.treeDateProcessor(this.treedata); // 处理后会对第一层级节点 及有子节点的进行id收集 permIdsProcessor
				this.$emit('interfaceFn', { treedata: res.result, permIds: this.permIds });
				this.key += 1;
				this.loading = false;
			});
		},
		/**编辑权限项 按钮打开addRole 组件 弹窗 */
		openAddRola() {
			this.pramas.flow = false;
			this.pramas.title = '编辑角色';
			this.$refs.authorityAddRole.open(this.roleMsg);
		},
		/**对tree 数据进行处理 */
		treeDateProcessor(arr) {
			arr.forEach(item => {
				this.permIds.push(item.id);
				this.permIdsProcessor.push(item.key);
				if (item.children.length > 0) {
					this.treeDateProcessor(item.children);
				}
				if (item.parentId.length > 0 && item.children.length == 0) {
					this.permIdsProcessor.splice(this.permIdsProcessor.indexOf(item.key), 1);
				}
			});
		},
		save() {
			this.getTree(this.id);
		}
	}
};
</script>
<style lang="scss" scoped>
.authority-Tree {
	height: calc(100% - 86px - 39px);
	display: flex;
	flex-direction: column;
	padding: 0px 22px 0 16px;
	&-icon {
		margin-left: 3px;
		width: 14px;
		height: 14px;
	}
	&-label {
		margin-left: 8px;
		padding: 0px 0;
		// font-size: 14px;
		// font-weight: 400;
		// color: $subTextColor;
		// line-height: 22px;
	}
	&-button {
		display: flex;
		justify-content: right;
		height: 61px;
		width: 100%;
		padding: 13px 6px;
	}
	&-btn {
		cursor: pointer;
		width: 112px;
		height: 36px;
		background: var(--brand-1);
		border-radius: $borderRadius;
		font-size: 16px;
		font-weight: 400;
		color: var(--brand-6);
		border: 1px solid var(--brand-6);
		line-height: 36px;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: 20px;
	}
	&-tree {
		border-radius: 9px;
		padding: 14px 18px 0 16px;
		background-color: #f5f7fa;
		overflow: auto;
		@include scrollBar;
		margin-top: 12px;
		flex: 1;
	}
	& ::v-deep .expanded.el-tree-node__expand-icon.el-icon-caret-right {
		display: none;
	}
	& ::v-deep .is-leaf.el-tree-node__expand-icon.el-icon-caret-right {
		display: none;
	}
	& ::v-deep .el-tree {
		background: transparent;
	}
	& ::v-deep .el-tree-node__content {
		height: auto;
	}
}
</style>
