<template>
	<div class="authority-AddRoleMember">
		<h2 class="title">添加角色成员</h2>
		<!--		<div v-if="version === 'v1'" class="authority-AddRoleMember-btn" @click="AddAdminIstratorClick">-->
		<!--			<svg-icon slot="suffix" class="authority-AddRoleMember-icon" icon-class="link"></svg-icon>-->
		<!--			关联人员-->
		<!--		</div>-->
		<div class="titleChoose">
			<div v-for="item in titleInfo" :key="item.id">
				<div
					v-if="item.type === 'all' || version === item.type"
					class="titleOne"
					:class="{ clicked: isChoose === item.id }"
					@click="handleChoose(item)"
				>
					{{ item.title }}
				</div>
			</div>
			<div class="add" @click="AddAdminIstratorClick">+ 添加</div>
		</div>
		<el-table
			style="width: 100%"
			height="100%"
			:data="tableData"
			class="custom-el-table"
			:row-style="getRowStyle"
			:header-row-style="headerRowStyle"
			:header-cell-style="headerCellStyle"
			align-center
		>
			<el-table-column
				v-if="rolePerms[0] === 'user' || rolePerms[0] === 'identity'"
				key="1"
				label="成员姓名"
			>
				<template slot-scope="{ row }">
					<div class="authority-AddRoleMember-tableRole-tableUser">
						<div class="authority-AddRoleMember-tableRole-tableUser-type">
							<FileById
								:size="24"
								:value="row.avatarUrl || row.logo"
								:more-style="{ borderRadius: '6px', margin: '4px' }"
								:default-font-icon="row.title ? row.title.slice(-1) : row.realname.slice(-1)"
								class="authority-AddRoleMember-img"
							></FileById>
							<div>{{ row.title || row.realname }}</div>
						</div>
					</div>
				</template>
			</el-table-column>
			<el-table-column v-if="rolePerms[0] === 'post'" key="3" label="岗位" show-overflow-tooltip>
				<template slot-scope="{ row }">
					<div class="authority-Table-departName">
						{{ row.postName }}
					</div>
				</template>
			</el-table-column>
			<el-table-column
				v-if="rolePerms[0] === 'post'"
				key="4"
				label="岗位类型"
				show-overflow-tooltip
			>
				<template slot-scope="{ row }">
					<div v-if="!row.orgIdDictText" class="authority-Table-departName">暂无</div>
					<div v-if="row.orgIdDictText" class="authority-Table-departName">
						{{ row.postTypeDictText }}
					</div>
				</template>
			</el-table-column>
			<el-table-column
				v-if="rolePerms[0] === 'identity'"
				key="5"
				label="所属身份"
				show-overflow-tooltip
			>
				<template slot-scope="{ row }">
					<div class="authority-Table-departName">
						{{ row.departName }}
						<span v-if="row.jobTitle">-{{ row.jobTitle }}</span>
					</div>
				</template>
			</el-table-column>
			<el-table-column
				v-if="rolePerms[0] === 'label'"
				key="5"
				label="所属标签"
				show-overflow-tooltip
			>
				<template slot-scope="{ row }">
					<div class="authority-Table-departName">
						{{ row.title }}
					</div>
				</template>
			</el-table-column>
			<el-table-column prop="handle" label="操作" width="80px">
				<template slot-scope="{ row }">
					<button class="authority-AddRoleMember-tableRole-deleteBtn" @click="deleteclick(row)">
						移除
					</button>
				</template>
			</el-table-column>
			<template slot="empty">
				<BasicEmpty />
			</template>
		</el-table>
		<authorityAddAdminIstrator
			ref="AddAdminIstrator"
			:role-msg="roleMsg"
			:data-source="rolePerms"
			:types="rolePerms[0] + 'Ids'"
			@listenToTable="getTable"
		/>
	</div>
</template>
<script>
import authorityAddAdminIstrator from './authority-add-admin-istrator';
import FileById from '@/components/file-by-id';
export default {
	components: {
		authorityAddAdminIstrator,
		FileById
	},
	props: {
		roleMsg: {
			type: Object,
			default: () => {
				return { tableData: Array };
			}
		}
	},
	data() {
		return {
			isChoose: 0,
			rolePerms: ['user', 'depart'],
			// 选择项目
			titleInfo: [
				{
					id: 0,
					title: '绑定成员',
					type: 'all',
					rolePerms: ['user']
				}
				// {
				// 	id: 1,
				// 	rolePerms: ['post'],
				// 	type: 'v2',
				// 	title: '绑定岗位'
				// },
				// {
				// 	id: 2,
				// 	rolePerms: ['identity'],
				// 	type: 'v2',
				// 	title: '绑定身份'
				// },
				// {
				// 	id: 3,
				// 	rolePerms: ['depart'],
				// 	type: 'all',
				// 	title: '绑定部门'
				// },
				// {
				// 	id: 4,
				// 	rolePerms: ['label'],
				// 	type: 'all',
				// 	title: '绑定标签'
				// }
			],
			/**表头每一行的样式*/
			headerRowStyle: {
				height: '48px',
				background: '#EDF2F6',
				borderRadius: '6px'
			},
			/**表头每一格样式*/
			headerCellStyle: {
				paddingLeft: '10px',
				background: 'transparent',
				fontSize: '16px',
				fontFamily: 'PingFang SC, PingFang SC',
				fontWeight: 500,
				color: '#2f446b',
				lineHeight: '24px'
			},
			userList: this.roleMsg.userIds,
			postList: this.roleMsg.postIds,
			idenList: this.roleMsg.identityIds,
			deptList: this.roleMsg.deptIds,
			labelIds: this.roleMsg.labelIds,
			tableData: this.roleMsg.userIds,
			version: 'v1'
		};
	},
	created() {
		let userData = JSON.parse(localStorage.getItem('coos_rent_rentInfo'));
		this.version = JSON.parse(userData.extend).moreSetting?.version || 'v1';
	},
	methods: {
		//点击tab页
		handleChoose(e) {
			this.rolePerms = e.rolePerms;
			this.isChoose = e.id;
			if (e.id === 0) {
				this.tableData = this.userList;
			} else if (e.id === 1) {
				this.tableData = this.postList;
			} else if (e.id === 2) {
				this.tableData = this.idenList;
			} else if (e.id === 3) {
				this.tableData = this.deptList;
			} else if (e.id === 4) {
				this.tableData = this.labelIds;
			}
		},

		/**打开关联人员弹窗 */
		AddAdminIstratorClick() {
			this.roleMsg.tableData = this.tableData;
			this.$emit('interfaceFn', { controller: false });
			this.$refs.AddAdminIstrator.roleMsgTable();
		},
		/**接受关联人员弹窗的保存按钮进行数据更新 */
		getTable(tableData) {
			if (this.isChoose === 0) {
				this.tableData = tableData;
				this.userList = tableData;
				this.$emit('interfaceFn', { userIds: tableData });
			} else if (this.isChoose === 1) {
				this.tableData = tableData;
				this.postList = tableData;
				this.$emit('interfaceFn', { postIds: tableData });
			} else if (this.isChoose === 2) {
				this.tableData = tableData;
				this.idenList = tableData;
				this.$emit('interfaceFn', { identityIds: tableData });
			} else if (this.isChoose === 3) {
				// 部门
				this.tableData = tableData.map(item => {
					return {
						...item,
						depName: item.departName
					};
				});
				this.deptList = this.tableData;
				this.$emit('interfaceFn', { deptIds: this.tableData });
			} else if (this.isChoose === 4) {
				// 标签
				this.tableData = tableData;
				this.labelIds = this.tableData;
				this.$emit('interfaceFn', { labelIds: this.tableData });
			}
		},
		/**移除 */
		deleteclick(row) {
			this.$confirm(
				`此操作将移除该${
					this.rolePerms[0] === 'user' ? '人员' : this.rolePerms[0] === 'post' ? '岗位' : '身份'
				}, 是否继续?`,
				'提示',
				{
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					type: 'warning'
				}
			).then(() => {
				if (this.rolePerms[0] === 'user') {
					this.$emit('interfaceFn', 'remove', { userIds: row });
					this.tableData = this.roleMsg.userIds;
					this.userList = this.roleMsg.userIds;
				} else if (this.rolePerms[0] === 'post') {
					this.$emit('interfaceFn', 'remove', { postIds: row });
					this.tableData = this.roleMsg.postIds;
					this.postList = this.roleMsg.postIds;
				} else {
					this.$emit('interfaceFn', 'remove', { identityIds: row });
					this.tableData = this.roleMsg.identityIds;
					this.idenList = this.roleMsg.identityIds;
				}
				this.$message({
					message: `移除${
						this.rolePerms[0] === 'user' ? '人员' : this.rolePerms[0] === 'post' ? '岗位' : '身份'
					}成功`,
					type: 'success'
				});
			});
		},
		/**表格奇偶样式 */
		getRowStyle({ row, rowIndex }) {
			return {
				height: '80px',
				borderRadius: '6px',
				background: rowIndex % 2 === 0 ? '#ffffff' : ' #f5f7fa'
			};
		}
	}
};
</script>
<style lang="scss" scoped>
.authority-AddRoleMember {
	height: 100%;
	display: flex;
	flex-direction: column;
	::-webkit-scrollbar {
		width: 0px;
	}
	&-tableRole {
		padding-left: 10px;
		&-deleteBtn {
			cursor: pointer;
			width: 60px;
			height: 32px;
			border: none;
			color: #ff7a7b;
			text-align: left;
			padding: 1px 10px;
			background-color: transparent;
		}
		&-tableUser {
			display: flex;
			margin-left: 10px;
			&-type {
				padding: 3px 4px 3px 0;
				line-height: 12px;
				display: flex;
				align-items: center;
				background-color: #f2f3f5;
				border-radius: 3px;
			}
		}
	}
	&-btn {
		cursor: pointer;
		width: 128px;
		height: 36px;
		background: var(--brand-1);
		border-radius: $borderRadius;
		text-align: center;
		font-size: 16px;
		font-weight: 400;
		color: #5092ea;
		border: 1px solid #5092ea;
		line-height: 36px;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: 20px;
	}
	& ::v-deep.el-table td,
	.el-table th.is-leaf {
		border: none;
	}
}

.titleChoose {
	display: flex;
	border-bottom: 1px solid #f0f0f0;
	font-size: 16px;
	color: $textColor;
	line-height: 22px;
	font-weight: 400;
	position: relative;
	margin-bottom: 20px;
	.add {
		position: absolute;
		font-size: 14px;
		color: var(--brand-6);
		right: 7px;
		top: 0;
		cursor: pointer;
	}
	.titleOne {
		cursor: pointer;
		margin-right: 32px;
		padding-bottom: 12px;
	}
	.titleTwo {
		cursor: pointer;
		padding-bottom: 12px;
		margin-right: 32px;
	}
	.clicked {
		font-weight: 500;
		font-size: 16px;
		color: var(--brand-6);
		border-bottom: 2px solid var(--brand-6);
	}
}
.title {
	font-size: 21px;
	color: #000;
	font-weight: 700;
	font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, Segoe UI, Arial,
		Roboto, 'PingFang SC', miui, 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;
}
</style>
