<template>
	<!-- 通讯录【成员】 -->
	<div class="dynamic-list">
		<el-table
			ref="table"
			:data="tableData"
			class="desk-el-table"
			:row-style="headerRowStyle"
			:header-cell-style="headerCellStyle"
			style="width: 100%"
			height="100%"
		>
			<el-table-column v-if="isIndex" type="index" label="序号" width="50"></el-table-column>
			<el-table-column
				v-for="(t, i) in tableHead"
				:key="getRandom(i)"
				:label="t.desc"
				:fixed="t.btns && t.btns.length > 0 ? 'right' : null"
				:render-header="renderHeader"
				:width="t.btns && t.btns.length > 0 ? '200px' : null"
				show-overflow-tooltip
			>
				<template slot-scope="scope">
					<form-table-render :row="scope.row" :ele-data="t">
						<el-dropdown
							v-if="t.btns && t.btns.length > 1"
							@command="
								type => {
									return t.btnsOnEvent(type, scope.row);
								}
							"
						>
							<div class="dynamic-editor">
								<span class="dynamic-editor-text">更多</span>
								<i class="el-icon-arrow-down el-icon--right"></i>
							</div>
							<el-dropdown-menu slot="dropdown" class="dynamic-editor-list">
								<el-dropdown-item
									v-for="(btn, k) in t.btns"
									:key="k"
									class="dynamic-editor-list-btn"
									:command="btn.type"
								>
									{{ btn.name }}
								</el-dropdown-item>
							</el-dropdown-menu>
						</el-dropdown>
						<div v-if="t.btns && t.btns.length > 0 && !hideBtn" class="buttons">
							<div
								v-for="(item, index) of t.btns"
								:key="index"
								class="buttons-item"
								@click="handleCommand(item.type, scope.row.id)"
							>
								{{ item.name }}
							</div>
						</div>
					</form-table-render>
				</template>
			</el-table-column>
			<template slot="empty">
				<BasicEmpty />
			</template>
		</el-table>
	</div>
</template>

<script>
import formTableRender from '@/components/form-table-render';
import { TableMixin } from '@/mixins/table-fix';
import { renderHeader } from '@/utils/index';
// import AfTableColumn from 'af-table-column';

export default {
	name: 'DynamicList',
	components: { formTableRender },
	mixins: [TableMixin],
	props: {
		isIndex: {
			type: Boolean,
			default: false
		},
		tableData: {
			type: Array,
			default: () => {
				return [];
			}
		},
		tableHead: {
			type: Array,
			default: () => {
				return [];
			}
		},
		// 成员与部门,暂时不需要展示出来,屏蔽按钮,与后面模块保持一致
		hideBtn: {
			type: Boolean,
			default: false
		}
	},
	data() {
		return {
			headerCellStyle: {
				backgroundColor: '#EDF2F6',
				color: '#2f446b',
				fontSize: '14px',
				lineHeight: '22px'
			},
			renderHeader
		};
	},
	created() {},
	methods: {
		headerRowStyle({ row, rowIndex }) {
			return {
				fontSize: '14px',
				color: '#2f446b',
				lineHeight: '22px',
				background: rowIndex % 2 === 0 ? '#ffffff' : '#f5f7fa'
			};
		},
		handleCommand(type, id) {
			this.$emit('handleCommand', type, id);
		}
	}
};
</script>

<style lang="scss" scoped>
.dynamic-list {
	width: 100%;
	height: 100%;
}

.dynamic-editor {
	display: flex;
	align-items: center;
	font-family: PingFang SC, PingFang SC;
	font-weight: 400;
	color: $primaryTextColor;
	cursor: pointer;

	&-list {
		padding: 12px 11px;

		&-btn {
			border-radius: 4px;
		}
	}
}
.buttons {
	display: flex;
	align-items: center;
	&-item {
		cursor: pointer;
		font-weight: 400;
		font-size: 14px;
		color: var(--brand-6);
		line-height: 22px;
		margin-right: 20px;
	}
	.delete {
		color: #ff4d4f;
	}
}
</style>
