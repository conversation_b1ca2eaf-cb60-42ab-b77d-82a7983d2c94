<template>
	<div v-loading="loading" class="authority-Table">
		<div class="authority-Table-top">
			<el-input
				v-model="keywords"
				:placeholder="'搜索' + dialogTitle + '名称'"
				class="authority-Table-search"
				@input="changeKeyword"
			>
				<svg-icon slot="suffix" class="authority-Table-search-icon" icon-class="search"></svg-icon>
			</el-input>
			<div class="authority-Table-btn" @click="AddAdminIstratorClick">
				<svg-icon slot="suffix" icon-class="link"></svg-icon>
				关联{{ dialogTitle }}
			</div>
		</div>
		<div class="tab-box">
			<div
				v-for="(item, index) in tabList"
				v-show="item.type === 'all' || version === item.type"
				:key="index"
				:class="item.value === current ? 'currentTab' : ''"
				@click="onTab(item)"
			>
				{{ item.label }}
			</div>
		</div>
		<div style="height: calc(100% - 228px)">
			<el-table
				:data="tableData"
				style="width: 100%"
				class="desk-el-table"
				height="100%"
				:row-style="getRowStyle"
				:header-row-style="headerRowStyle"
				:header-cell-style="headerCellStyle"
			>
				<el-table-column
					v-if="current === 'userIds' || current === 'identityIds'"
					:key="1"
					label="成员姓名"
				>
					<template slot-scope="{ row }">
						<div class="authority-Table-tableUser">
							<div class="authority-Table-tableUser-type">
								<FileById
									:size="24"
									:value="row.avatarUrl || row.logo"
									:more-style="{ borderRadius: '6px', margin: '4px' }"
									:default-font-icon="row.realname ? row.realname.slice(-1) : ''"
									class="authority-AddRoleMember-img"
								></FileById>
								<el-tooltip
									:open-delay="1000"
									class="item"
									effect="dark"
									:content="row.realname"
									placement="top-start"
								>
									<div class="realName">{{ row.realname }}</div>
								</el-tooltip>
							</div>
						</div>
					</template>
				</el-table-column>
				<el-table-column
					v-if="current === 'userIds' || current === 'departIds'"
					:key="2"
					label="部门"
					show-overflow-tooltip
				>
					<template slot-scope="{ row }">
						<div class="authority-Table-departName">
							{{ row.departNames ? row.departNames : row.departName ? row.departName : '暂无' }}
						</div>
					</template>
				</el-table-column>
				<el-table-column v-if="current === 'postIds'" :key="3" label="岗位" show-overflow-tooltip>
					<template slot-scope="{ row }">
						<div class="authority-Table-departName">
							{{ row.postName }}
						</div>
					</template>
				</el-table-column>
				<el-table-column
					v-if="current === 'postIds'"
					:key="4"
					label="所属机构"
					show-overflow-tooltip
				>
					<template slot-scope="{ row }">
						<div v-if="!row.orgIdDictText" class="authority-Table-departName">暂无</div>
						<div v-if="row.orgIdDictText" class="authority-Table-departName">
							{{ row.orgIdDictText }}
						</div>
					</template>
				</el-table-column>
				<el-table-column
					v-if="current === 'identityIds'"
					:key="5"
					label="所属身份"
					show-overflow-tooltip
				>
					<template slot-scope="{ row }">
						<div class="authority-Table-departName">
							{{ row.departName }}
							<span v-if="row.jobTitle">-{{ row.jobTitle }}</span>
						</div>
					</template>
				</el-table-column>
				<el-table-column
					v-if="current === 'labelIds'"
					:key="5"
					label="所属标签"
					show-overflow-tooltip
				>
					<template slot-scope="{ row }">
						<div class="authority-Table-departName">
							{{ row.labelTitle }}
						</div>
					</template>
				</el-table-column>
				<el-table-column :key="6" label="操作" width="80">
					<template slot-scope="scope">
						<button class="authority-Table-deleteBtn" @click="deleteclick(scope.row, scope.$index)">
							移除
						</button>
					</template>
				</el-table-column>
				<template slot="empty">
					<BasicEmpty />
				</template>
			</el-table>
		</div>
		<authorityAddAdminIstrator
			ref="AddAdminIstrator"
			:dialog-title="'关联' + dialogTitle"
			:role-msg="roleMsg"
			:data-source="rolePerms"
			:types="current"
			@saveToTable="table"
			v-on="$listeners"
		/>
	</div>
</template>
<script>
import authorityAddAdminIstrator from './authority-add-admin-istrator';
import { debounce } from '@/utils';
import {
	deleteRoleMember,
	getRoleMemberlist,
	savePostlist,
	saveIdentityslist,
	saveDeptsList,
	saveLabelList
} from '@/api/modules/authority';
import FileById from '@/components/file-by-id';
import { getPostPage } from '@/api/modules/dynamic';
import { getDeptList, getIdentityList, getLabelList } from '@/api/modules/component';
export default {
	components: {
		authorityAddAdminIstrator,
		FileById
	},
	props: {
		roleMsg: {
			type: Object,
			default: () => {}
		}
	},
	data() {
		return {
			rolePerms: ['depart', 'user'],
			current: 'userIds',
			keywords: '',
			/**表头每一行的样式*/
			headerRowStyle: {
				height: '48px',
				background: '#EDF2F6',
				borderRadius: '6px'
			},
			/**表头每一格样式*/
			headerCellStyle: {
				background: 'transparent',
				fontSize: '16px',
				fontFamily: 'PingFang SC, PingFang SC',
				fontWeight: 500,
				color: '#2f446b',
				lineHeight: '24px'
			},
			loading: false,
			tableData: this.roleMsg.tableData,
			tabList: [
				{
					label: '成员',
					rolePerms: ['depart', 'user'],
					type: 'all',
					value: 'userIds'
				}
			],
			version: ''
		};
	},
	computed: {
		dialogTitle() {
			const tab = this.tabList.find(tabItem => tabItem.value === this.current);
			return tab ? tab.label : '未知';
		}
	},
	created() {
		let userData = JSON.parse(localStorage.getItem('coos_rent_rentInfo'));
		this.version = JSON.parse(userData.extend).moreSetting?.version || 'v1';
	},

	methods: {
		/**获取表单数据 */
		getDrawertable(id) {
			this.$emit('interfaceFn', { id });
			this.loading = true;
			if (this.current === 'userIds') {
				getRoleMemberlist(id, this.keywords).then(res => {
					this.setTable(res.result);
				});
			} else if (this.current === 'postIds') {
				getPostPage({
					roleId: id,
					status: 1,
					pageNo: 1,
					pageSize: 999,
					keywords: this.keywords
				}).then(res => {
					this.setTable(res.result.records);
				});
			} else if (this.current === 'departIds') {
				getDeptList({
					roleId: id,
					status: 1,
					pageNo: 1,
					pageSize: 999,
					keywords: this.keywords
				}).then(res => {
					this.setTable(res.result.records);
				});
			} else if (this.current === 'labelIds') {
				getLabelList({
					roleId: id,
					status: 1,
					pageNo: 1,
					pageSize: 999,
					keywords: this.keywords
				}).then(res => {
					this.setTable(res.result.records);
				});
			} else {
				getIdentityList({
					roleId: id,
					status: 1,
					pageNo: 1,
					pageSize: 999,
					keywords: this.keywords
				}).then(res => {
					this.setTable(res.result.records);
				});
			}
		},
		setTable(result) {
			this.tableData = [];
			this.tableData.push(...result);
			this.$emit('interfaceFn', { tableData: this.tableData });
			this.loading = false;
		},
		/**搜索 */
		changeKeyword: debounce(
			function () {
				this.getDrawertable(this.roleMsg.id, this.keywords);
			},
			500,
			false
		),
		/**表单操作里的移除 */
		deleteclick(row, index) {
			this.loading = true;
			this.$confirm(`是否移除该${this.dialogTitle}?`, '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			})
				.then(() => {
					if (this.current === 'userIds') {
						deleteRoleMember(row.userRoleId).then(res => {
							this.loading = false;
							if (res.code === 200) {
								this.$emit('interfaceFn', 'remove', { tableData: row });
								this.$message({
									message: `移除${this.dialogTitle}成功`,
									type: 'success'
								});
							} else {
								this.$message.error(res.message);
							}
						});
					} else {
						let reqFn;
						switch (this.current) {
							case 'postIds':
								reqFn = savePostlist;
								break;
							case 'departIds':
								reqFn = saveDeptsList;
								break;
							case 'labelIds':
								reqFn = saveLabelList;
								break;
							default:
								reqFn = saveIdentityslist; // 或者其他合理的默认值
						}
						let list = JSON.parse(JSON.stringify(this.roleMsg.tableData));
						list = list.map(item => item.id);
						list.splice(index, 1);
						reqFn(this.roleMsg.id, list).then(res => {
							this.loading = false;
							if (res.code === 200) {
								this.$emit('interfaceFn', 'remove', { tableData: row });
								this.$message({
									message: `移除${this.dialogTitle}成功`,
									type: 'success'
								});
							} else {
								this.$message.error(res.message);
							}
						});
					}
				})
				.catch(err => {
					this.loading = false;
				});
		},
		/**关联人员弹窗打开方法 */
		AddAdminIstratorClick() {
			this.$emit('interfaceFn', { controller: true });
			this.$refs.AddAdminIstrator.roleMsgTable();
		},
		/**接收关联人员弹窗 保存信息 并进行表单更新*/
		table(id) {
			/**数据初始化 */
			this.keywords = '';
			this.getDrawertable(id);
		},
		/**获取表格奇偶列的样式*/
		getRowStyle({ row, rowIndex }) {
			return {
				height: '80px',
				borderRadius: '6px',
				background: rowIndex % 2 === 0 ? '#ffffff' : ' #f5f7fa'
			};
		},
		//切换
		onTab(data) {
			let _this = this;
			setTimeout(function () {
				_this.current = data.value;
				_this.rolePerms = data.rolePerms;
				_this.getDrawertable(_this.roleMsg.id);
			}, 200);
		}
	}
};
</script>
<style lang="scss" scoped>
.realName {
	max-width: 120px;
	@include aLineEllipse;
}
.authority-Table {
	width: 100%;
	padding: 16px 27px 4px 15px;
	height: 100%;
	display: flex;
	flex-direction: column;
	&-img {
		width: 28px;
		height: 28px;
		border-radius: $borderRadius;
		margin: 4px;
	}
	&-top {
		display: flex;
		justify-content: space-between;
		padding: 16px 0px;
	}
	& ::v-deep .el-input__inner {
		height: 36px;
	}
	&-search {
		width: 196px;
		height: 36px;
		&-icon {
			margin: 10px 5px;
		}
	}
	&-btn {
		cursor: pointer;
		width: 128px;
		height: 32px;
		background: var(--brand-1);
		border-radius: $borderRadius;
		font-size: 16px;
		font-weight: 400;
		color: #5092ea;
		border: 1px solid #5092ea;
		line-height: 36px;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	&-departName {
		padding-left: 4px;
		overflow: hidden;
		white-space: nowrap;
		text-overflow: ellipsis;
	}
	&-tableUser {
		display: flex;
		align-items: center;
		&-type {
			margin-right: 4px;
			padding: 3px 4px;
			line-height: 16px;
			display: flex;
			align-items: center;
			background-color: #f2f3f5;
			@include aLineEllipse;
			border-radius: 3px;
		}
	}
	&-deleteBtn {
		cursor: pointer;
		text-align: left;
		padding-left: 10px;
		width: 60px;
		height: 32px;
		border: none;
		color: #ff7a7b;
		background-color: transparent;
	}
	& ::v-deep.el-table td,
	.el-table th.is-leaf {
		border: none;
	}
}

.tab-box {
	display: flex;
	margin-bottom: 12px;
	div {
		width: 68px;
		cursor: pointer;
		height: 30px;
		background: #f3f4f6;
		border-radius: 6px;
		text-align: center;
		font-size: 14px;
		color: #2f446b;
		line-height: 30px;
		margin-right: 12px;
	}
	.currentTab {
		background: rgba(39, 120, 229, 0.1);
		color: var(--brand-6);
	}
}
</style>
