<template>
	<div class="addRole-el-dialog">
		<el-dialog
			class="custom-el-dialog-footer custom-dialog"
			:title="pramas.title"
			:visible.sync="dialogVisible"
			append-to-body
			width="960px"
			top="8vh"
		>
			<div v-loading="loading" style="height: 100%">
				<div class="addRole-el-dialog-body">
					<div class="addRole-el-dialog-left">
						<el-steps direction="vertical" :active="active" :space="80" :align-center="true">
							<el-step title="角色信息" description="设置角色名称等信息">
								<template v-slot:icon>
									<div v-if="active == 0" class="addRole-el-dialog-body-icon-process"></div>
									<svg-icon
										v-if="active > 0"
										class="addRole-el-dialog-body-icon-finish"
										icon-class="checked"
									></svg-icon>
								</template>
							</el-step>
							<el-step title="分配权限项" description="设置角色可以管理的权限范围">
								<template v-slot:icon>
									<div v-if="active == 1" class="addRole-el-dialog-body-icon-process"></div>
									<div v-if="active < 1" class="addRole-el-dialog-body-icon-wait"></div>
									<svg-icon
										v-if="active > 1"
										class="addRole-el-dialog-body-icon-finish"
										icon-class="checked"
									></svg-icon>
								</template>
							</el-step>
							<el-step title="添加角色成员" description="将成员添加到此角色中">
								<template v-slot:icon>
									<div v-if="active == 2" class="addRole-el-dialog-body-icon-process"></div>
									<div v-if="active < 2" class="addRole-el-dialog-body-icon-wait"></div>
									<svg-icon
										v-if="active > 2"
										class="addRole-el-dialog-body-icon-finish"
										icon-class="checked"
									></svg-icon>
								</template>
							</el-step>
							<el-step title="检查并完成" description="预览角色与权限并确认">
								<template v-slot:icon>
									<div v-if="active == 3" class="addRole-el-dialog-body-icon-process"></div>
									<div v-if="active < 3" class="addRole-el-dialog-body-icon-wait"></div>
								</template>
							</el-step>
						</el-steps>
					</div>
					<div class="addRole-el-dialog-right">
						<component
							:is="currentTabComponent"
							ref="ruleForm"
							:role-msg="roleMsg"
							:checked-data="checkedData"
							:table-data="tableData"
							@interfaceFn="interfaceFn"
							@changeList="changeList"
						/>
					</div>
				</div>
			</div>

			<div slot="footer" v-loading="loading" class="addRole-el-dialog-footer">
				<button
					v-if="active < 4 && active > 0"
					class="addRole-el-dialog-footer-btnlast"
					@click="last"
				>
					上一步
				</button>
				<button v-if="active == 0" class="addRole-el-dialog-footer-btnlast" @click="cancel">
					取消
				</button>
				<button v-if="active < 3" class="addRole-el-dialog-footer-btnnext" @click="next">
					下一步
				</button>
				<button v-if="active == 3" class="addRole-el-dialog-footer-btnsave" @click="save">
					{{ pramas.title == '添加角色' ? pramas.title : '保存编辑' }}
				</button>
			</div>
		</el-dialog>
	</div>
</template>
<script>
import authorityAddRoleMember from './authority-add-role-member';
import authorityAssignPermissionLtem from './authority-assign-permission-ltem';
import authorityCheckAndComplete from './authority-check-and-complete.vue';
import authorityRoleMsg from './authority-rolemsg';
import { addRole, editRole } from '@/api/modules/authority';
import { deepClone } from '@/utils'; //新增和编辑保存接口
export default {
	components: {
		authorityAddRoleMember,
		authorityAssignPermissionLtem,
		authorityCheckAndComplete,
		authorityRoleMsg
	},
	props: {
		pramas: {
			/**title 弹窗title */
			/**flow  header组件新增角色和tree组件编辑权限项 不同组件打开时进行不同的处理的判断条件*/
			type: Object,
			default: () => {
				return { title: '添加角色', flow: false };
			}
		}
	},
	data() {
		return {
			components: [
				authorityRoleMsg,
				authorityAssignPermissionLtem,
				authorityAddRoleMember,
				authorityCheckAndComplete
			], //流程所对应页面组件
			active: 0, //流程的绑定的key
			dialogVisible: false, //弹窗开关
			roleMsg: {
				rolename: '',
				description: '',
				permIds: [],
				treedata: []
			}, // 角色信息
			tableData: [],
			checkedData: [],
			loading: false
		};
	},

	computed: {
		/**通过active的改变来绑定组件和流程步骤 */
		currentTabComponent() {
			return this.components[this.active];
		}
	},
	methods: {
		changeList(list) {
			this.roleMsg.treedata = list;
		},
		/**上一步按钮的逻辑处理 */
		last() {
			switch (this.active) {
				/**通过flow 来区分header和tree组件 */
				case 1: {
					this.loading = true;
					this.active--;

					this.$nextTick(() => {
						this.loading = false;
					});
					break;
				}
				case 2: {
					this.loading = true;
					this.active--;
					this.$nextTick(() => {
						this.$refs.ruleForm.getTree().then(() => {
							this.loading = false;
						});
					});
					break;
				}
				case 3: {
					this.loading = true;
					if (this.pramas.flow) {
						this.active--;
						this.$nextTick(() => {
							this.loading = false;
						});
					} else if (!this.pramas.flow) {
						this.active -= 2;
						this.$nextTick(() => {
							this.$refs.ruleForm.getTree().then(() => {
								this.loading = false;
							});
						});
					}
					break;
				}
			}
		},
		/**下一步按钮的逻辑处理 */
		next() {
			switch (this.active) {
				case 0: {
					this.loading = true;

					/**validate Form表单的表单验证 */
					this.$refs.ruleForm
						.validate()
						.then(res => {
							// console.log(this.roleMsg, 6);

							// this.roleMsg = res;
							this.active++;
							this.$nextTick(() => {
								this.$refs.ruleForm.getTree().then(res => {
									this.roleMsg.treedata = res;
									this.loading = false;
								});
							});
						})
						.catch(() => {
							this.loading = false;
						});
					break;
				}
				case 1: {
					console.log(this.roleMsg);
					this.loading = true;
					if (this.pramas.flow) {
						this.active++;
						this.$nextTick(() => {
							this.loading = false;
						});
					} else if (!this.pramas.flow) {
						this.active += 2;
						this.$nextTick(() => {
							this.$refs.ruleForm.treeDateProcessor().then(() => {
								this.loading = false;
							});
						});
					}
					break;
				}
				case 2: {
					this.loading = true;
					this.active++;
					this.$nextTick(() => {
						this.$refs.ruleForm.treeDateProcessor().then(() => {
							this.loading = false;
						});
					});
					break;
				}
			}
		},
		/**新增角色和编辑角色的接口调用 */
		save() {
			if (this.roleMsg.userIds) {
				this.roleMsg.userIds = this.roleMsg.userIds.map(item => item.id);
			}
			if (this.roleMsg.postIds) {
				this.roleMsg.postIds = this.roleMsg.postIds.map(item => item.id);
			}
			if (this.roleMsg.identityIds) {
				this.roleMsg.identityIds = this.roleMsg.identityIds.map(item => item.id);
			}
			if (this.roleMsg.labelIds) {
				this.roleMsg.labelIds = this.roleMsg.labelIds.map(item => item.id);
			}
			if (this.roleMsg.deptIds) {
				this.roleMsg.deptIds = this.roleMsg.deptIds.map(item => item.id);
			}
			let orgId = Array.isArray(this.roleMsg.orgId)
				? this.roleMsg.orgId.length > 0
					? this.roleMsg.orgId[this.roleMsg.orgId.length - 1]
					: 0
				: this.roleMsg.orgId;
			let params = {
				roleName: this.roleMsg.roleName,
				description: this.roleMsg.description,
				permIds: this.roleMsg.permIds,
				userIds:
					this.pramas.title == '添加角色'
						? this.roleMsg.userIds
						: this.roleMsg.tableData.map(item => item.id),
				identityIds: this.roleMsg.identityIds,
				postIds: this.roleMsg.postIds,
				deptIds: this.roleMsg.deptIds,
				labelIds: this.roleMsg.labelIds,
				isTemplate: this.roleMsg.isTemplate,
				orgId
			};
			console.log(params);
			if (this.pramas.title == '添加角色') {
				addRole(params).then(res => {
					this.dialogVisible = false;
					if (res.code === 200) {
						this.$emit('save'); //通知最外层表单更新数据
						this.$message({
							message: '新增角色成功',
							type: 'success'
						});
						this.roleMsg = {
							rolename: '',
							description: '',
							permIds: [],
							treedata: []
						}; // 角色信息
					} else {
						this.$message.error(res.message);
					}
				});
			} else if (this.pramas.title == '编辑角色') {
				console.log(params);
				editRole(this.roleMsg.id, params).then(res => {
					this.dialogVisible = false;
					if (res.code === 200) {
						this.$emit('save'); //通知最外层表单更新数据
						this.$message({
							message: '编辑角色成功',
							type: 'success'
						});
					} else {
						this.$message.error(res.message);
					}
				});
			}
		},
		cancel() {
			this.dialogVisible = false;
		},
		/**接受子组件传过来的值 arg 为多个参数的数组 [remove,{}] 第二个参数必须为对象  第一个参数默认为空时则进行添加或修改操作*/
		interfaceFn(...arg) {
			if (arg.includes('remove')) {
				Object.keys(arg[arg.indexOf('remove') + 1]).map(item => {
					this.roleMsg[item].splice(
						this.roleMsg[item].indexOf(arg[arg.indexOf('remove') + 1][item]),
						1
					); //arg[][] 第一个为[] index 第二个[] 则对应对象的键值
				});
			} else {
				Object.keys(arg[0]).map(item => {
					this.roleMsg[item] = arg[0][item];
				});
			}
		},
		/**父组件打开弹窗 */
		open(roleMsg) {
			this.active = 0;
			this.dialogVisible = true;
			if (roleMsg) {
				this.roleMsg = deepClone(roleMsg);
			}
			if (!this.pramas.flow) {
				this.pramas.title = '编辑角色';
			}
		}
	}
};
</script>
<style lang="scss" scoped>
.addRole-el-dialog-body-icon-process {
	width: 16px;
	height: 16px;
	border-radius: 50px;
	opacity: 1;
	border: 4px solid #1890ff;
}
.addRole-el-dialog-body-icon-wait {
	width: 16px;
	height: 16px;
	border-radius: 50px;
	opacity: 1;
	border: 4px solid $holderTextColor;
}
.addRole-el-dialog {
	&-body {
		display: flex;
		border: 1px solid #f0f0f0;
		border-radius: 14px;
		height: 100%;
	}
	&-left {
		height: 100%;
		width: 25%;
		border-right: 1px solid #f0f0f0;
		padding: 40px 20px;
		& ::v-deep .el-step__title.is-finish,
		& ::v-deep .el-step__title.is-wait {
			font-size: 16px;
			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			color: $textColor;
			line-height: 24px;
			padding: 0 0 4px;
		}

		& ::v-deep.el-step__title.is-process {
			font-size: 16px;
			font-weight: 500;
			color: $primaryTextColor;
			line-height: 24px;
			padding-bottom: 4px;
		}
		& ::v-deep .el-step__description.is-finish,
		& ::v-deep .el-step__description.is-process,
		& ::v-deep .el-step__description.is-wait {
			font-size: 12px;
			font-family: PingFang SC, PingFang SC;
			font-weight: 400;
			color: $subTextColor;
			line-height: 20px;
		}
		&::v-deep .el-step__head.is-wait .el-step__icon.is-text {
			border: none;
		}
		& ::v-deep .el-step__head.is-process .el-step__icon.is-text {
			border: none;
		}
		& ::v-deep .el-step__head.is-finish .el-step__icon.is-text {
			border: none;
		}

		// ::v-deep .el-step__head.is-finish .el-step__line {
		// 	width: 2px;
		// 	background: #f0f0f0 !important;
		// }
		// ::v-deep .el-step__line {
		// 	width: 2px;
		// 	background: #f0f0f0 !important;
		// }
		::v-deep .el-step__line-inner {
			border: none;
			background: #f0f0f0;
		}
		::v-deep .el-step__line {
			border: none;
			background: #f0f0f0;
		}
	}
	&-right {
		height: 100%;
		width: 75%;
		padding: 10px 20px;
	}
	&-footer {
		& button {
			cursor: pointer;
		}
		&-btnlast {
			width: 82px;
			height: 36px;
			background: #ffffff;
			border-radius: $borderRadius;
			opacity: 1;
			margin-right: 10px;
			border: 1px solid $borderColor;
		}
		&-btnnext {
			width: 82px;
			height: 36px;
			background: var(--brand-6);
			color: rgba(255, 255, 255, 0.9);
			border-radius: $borderRadius;
			opacity: 1;
			border: none;
		}
		&-btnsave {
			width: 96px;
			height: 36px;
			background: var(--brand-6);
			border-radius: $borderRadius;
			opacity: 1;
			border: none;
			color: #000;
			font-size: 14px;
			font-weight: 400;
			color: rgba(255, 255, 255, 0.9);
			line-height: 22px;
		}
		& ::v-deep .circular {
			display: none;
		}
	}
}
::v-deep .el-dialog {
	border-radius: 16px;
	.el-dialog__body {
		overflow-y: auto;
		max-height: 625px;
	}
}
</style>
