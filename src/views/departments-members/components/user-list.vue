<template>
	<el-table
		:key="timeStamp"
		:data="tableData"
		stripe
		style="width: 100%"
		:row-class-name="rowClassName"
	>
		<el-table-column prop="deptId" label="部门" width="220px" align="center">
			<template slot="header">
				<span>
					部门
					<span style="color: red">*</span>
				</span>
			</template>
			<template slot-scope="scope">
				<div class="flex">
					<div class="icon-box">
						<!-- <i class="coos-iconfont icon-Friendsettings"></i> -->
						<img
							v-if="scope.$index > 0"
							width="16"
							height="18"
							src="../../../assets/images/common/tuozhuai.png"
							alt=""
						/>
					</div>
					<el-cascader
						v-model="scope.row.deptId"
						:disabled="scope.row.disabled"
						:props="{ checkStrictly: true, emitPath: false }"
						:options="scope.row.disabled ? scope.row.deptArr : depData"
						:show-all-levels="false"
						@change="e => handleChange(e, 1, scope.row, scope)"
					></el-cascader>
				</div>
			</template>
		</el-table-column>

		<el-table-column
			v-if="userVersion === 'v2'"
			prop="postId"
			label="岗位"
			width="200px"
			align="center"
		>
			<template slot="header">
				<span>
					岗位
					<span style="color: red">*</span>
				</span>
			</template>
			<template slot-scope="scope">
				<el-cascader
					:key="postDataKey"
					v-model="scope.row.postId"
					:disabled="scope.row.disabled"
					:options="postData[scope.$index]"
					value-key="id"
					:show-all-levels="false"
					@change="e => handleChange(e, 2, scope.row)"
				></el-cascader>
			</template>
		</el-table-column>
		<el-table-column
			v-if="userVersion === 'v2'"
			prop="jobTitle"
			label="职务"
			width="200px"
			align="center"
		>
			<template slot="header">
				<span>
					职务
					<span style="color: red">*</span>
				</span>
			</template>
			<template slot-scope="scope">
				<el-input
					v-model="scope.row.jobTitle"
					:disabled="scope.row.disabled"
					placeholder="请输入内容"
				></el-input>
			</template>
		</el-table-column>
		<el-table-column prop="isMain" label="级别" align="center">
			<template slot-scope="scope">
				<span :style="scope.row.isMain ? 'color:#0f45ea' : ''">
					{{ scope.row.isMain ? '主身份' : '子身份' }}
				</span>
			</template>
		</el-table-column>
		<el-table-column prop="isDirector" label="是否部门负责人" align="center">
			<template slot-scope="scope">
				<el-switch v-model="scope.row.isDirector" :disabled="scope.row.disabled"></el-switch>
			</template>
		</el-table-column>
		<el-table-column prop="status" label="是否启用" align="center">
			<template slot-scope="scope">
				<el-switch v-model="scope.row.status" :disabled="scope.row.disabled"></el-switch>
			</template>
		</el-table-column>
		<el-table-column fixed="right" label="操作" width="140" align="center">
			<template slot-scope="scope">
				<el-button
					v-if="!scope.row.isMain"
					type="text"
					:disabled="scope.row.disabled"
					size="small"
					style="color: #0f45ea"
					@click="master(scope.$index, scope.row)"
				>
					设为主身份
				</el-button>
				<el-button
					type="text"
					size="small"
					style="color: red"
					:disabled="scope.row.disabled"
					@click="deletes(scope)"
				>
					删除
				</el-button>
			</template>
		</el-table-column>
	</el-table>
</template>

<script>
import Sortable from 'sortablejs';
import { treeByDept, getorgAndDepartTree } from '@/api/modules/dynamic';
import { mapGetters } from 'vuex';
export default {
	props: {
		depList: {
			type: Array,
			default: () => []
		}
	},
	data() {
		return {
			timeStamp: '',
			depData: [],
			postData: {},
			postDataKey: 1,
			tableData: [
				{
					deptId: '',
					postId: '',
					jobTitle: '',
					postData: [],
					isMain: true,
					isDirector: false,
					status: true
				}
			]
		};
	},
	computed: {
		...mapGetters(['userVersion'])
	},
	created() {
		// this.getTree();
		this.depData = this.depList;
		this.ifDepShow(this.depData);
		// console.log(this.depData.length<1){
		// }
	},
	mounted() {
		this.$nextTick(() => {
			const el = this.$el.querySelector('.el-table__body-wrapper tbody');
			Sortable.create(el, {
				draggable: '.el-table__row__drag',
				onEnd: event => {
					const { oldIndex, newIndex } = event;
					this.updateRowOrder(oldIndex, newIndex);
				}
			});
		});
	},
	methods: {
		rowClassName({ row, rowIndex }) {
			// 为除了第一行外的所有行添加额外样式
			return rowIndex != 0 ? 'el-table__row__drag' : '';
		},
		addUser() {
			let current = this.tableData[this.tableData.length - 1];
			if (this.tableData.length < 1) {
				this.tableData.push({
					deptId: '',
					postId: '',
					jobTitle: '',
					isMain: true,
					isDirector: false,
					status: true
				});
			} else {
				if (this.userVersion == 'v1') {
					if (current.deptId) {
						this.tableData.push({
							deptId: '',
							postId: '',
							jobTitle: '',
							postData: [],
							isMain: false,
							isDirector: false,
							status: true
						});
					} else {
						this.$message({
							message: '请完善信息后添加！',
							type: 'error'
						});
					}
					return;
				}
				if (current.deptId && current.postId && current.jobTitle) {
					this.tableData.push({
						deptId: '',
						postId: '',
						jobTitle: '',
						postData: [],
						isMain: false,
						isDirector: false,
						status: true
					});
				} else {
					this.$message({
						message: '请完善信息后添加！',
						type: 'error'
					});
				}
			}
		},
		// 删除
		deletes(e) {
			if (this.tableData[e.$index].isMain) {
				this.tableData.splice(e.$index, 1);
				this.tableData[0].isMain = 1;
			} else {
				this.tableData.splice(e.$index, 1);
			}
		},
		//设为主身份
		master(index, data) {
			this.tableData[index].isMain = 1;
			this.tableData.forEach((item, i) => {
				if (index != i) {
					item.isMain = 0;
				}
			});
			this.tableData.splice(index, 1); // 删除元素
			this.tableData.unshift(data); // 将元素插入到数组开始位置
		},
		//判断部门选择
		ifDepShow(data) {
			// data.forEach(item => {
			// 	if (item.children) {
			// 		item.children.forEach(ltem => {
			// 			if (ltem.disabled == false) {
			// 				item.disabled = false;
			// 			}
			// 			if (ltem.children) {
			// 				this.ifDepShow(ltem.children);
			// 			}
			// 		});
			// 	}
			// });
			getorgAndDepartTree({ limitOrg: true, orgNodeDisabled: true }).then(res => {
				this.depData = this.DepartTreeProcess(res.result);
				this.tableData.forEach((item, index) => {
					if (item.deptId) {
						const dept = this.findDeptById(this.depData, item.deptId);
						if (!dept) {
							console.log('找不到部门');
							// value: item.id,
							//   label: item.title,
							//   children: item.children,
							//   disabled: item.disabled
							item.deptArr = [
								{
									value: item.deptId,
									label: item.departName,
									children: [],
									disabled: false
								}
							];
							item.disabled = true;
							console.log(item);
						}
					}
				});
			});
		},
		/** 部门树 数据处理 */
		DepartTreeProcess(arr) {
			return arr.map(item => {
				if (item.children) {
					item.children = this.DepartTreeProcess(item.children);
				}
				return {
					value: item.id,
					label: item.title,
					children: item.children,
					disabled: item.disabled
				};
			});
		},
		//行拖拽
		updateRowOrder(oldIndex, newIndex) {
			const movedItem = this.tableData.splice(oldIndex, 1)[0];
			this.tableData.splice(newIndex, 0, movedItem);
			const newArray = this.tableData;
			this.tableData = [];
			this.$nextTick(() => {
				this.tableData = newArray;
			});
			// this.timeStamp = new Date() + '';
			this.updateOrderOnServer();
		},
		updateOrderOnServer() {},

		handleChange(e, type, data, scope) {
			if (type === 1) {
				data.deptId = e;
				this.getTree(data.deptId, scope.$index);
			} else {
				data.postId = e[e.length - 1];
			}
		},
		setData(data) {
			this.tableData = data;
			this.tableData.forEach((item, index) => {
				if (item.deptId) {
					this.getTree(item.deptId, index);
				}
			});
		},
		findDeptById(deptData, deptId) {
			for (const item of deptData) {
				if (item.value === deptId) {
					return item;
				}
				if (item.children && item.children.length > 0) {
					const result = this.findDeptById(item.children, deptId);
					if (result) {
						return result;
					}
				}
			}
			return null;
		},
		getTree(id, index) {
			treeByDept({ deptId: id, limitOrg: true }).then(res => {
				this.postData[index] = res.result;
				this.postDataKey += 1;
				// const defaultNode = this.findDefaultNode(this.postData);
				// this.tableData.forEach(item => {
				// 	item.postId = defaultNode?.id || '';
				// });
			});
		},
		findDefaultNode(tree) {
			if (!tree) return null;
			for (const node of tree) {
				if (node.isDefault === true) {
					return node;
				}
				if (node.children && Array.isArray(node.children) && node.children.length > 0) {
					const result = this.findDefaultNode(node.children);
					if (result) {
						return result;
					}
				}
			}
			return null;
		}
	}
};
</script>

<style lang="scss" scoped>
::v-deep .is-leaf {
	background: #edf2f6;
}
::v-deep .el-table--striped .el-table__body tr.el-table__row--striped td {
	background: #edf2f6 !important;
	border: none !important;
}
::v-deep .el-radio__input {
	color: red;
}
::v-deep .el-table__body-wrapper {
	&::-webkit-scrollbar {
		height: 8px;
		width: 0px;
	}
}

.flex {
	display: flex;
	.icon-box {
		width: 20px;
		cursor: pointer;
		img {
			position: relative;
			top: 6px;
			display: none;
		}
	}
}
::v-deep .hover-row .icon-box img {
	display: block !important;
}
</style>
