<template>
	<div class="box">
		<!-- 左侧内容 -->
		<div v-loading="lfLoading" class="lf">
			<!-- 组织结构数 -->
			<organizationTree
				:data="treeData"
				:default-checked-keys="defaultCheckedKeys"
				@handleNode="handleOrg"
			></organizationTree>
		</div>
		<!-- 右侧内容 -->
		<div class="rf">
			<div class="head">
				<div class="head-title">
					<div class="title">{{ title }}</div>
					<div class="num">
						<span>总人数</span>
						<b>{{ total }}</b>
					</div>
				</div>
				<!-- 搜索框 -->
				<SearchFormRender
					ref="vFormRefSearch"
					:min-height="34"
					:max-height="0"
					class="searchBox"
					is-search-views
					:form-json="searchFormJson"
					@searchClick="handleSearch"
					@resetClick="handleReset"
				></SearchFormRender>
			</div>
			<div class="search">
				<div class="top"></div>
				<div class="btm">
					<el-button class="btn" type="primary" icon="el-icon-plus" @click="openAdd">
						添加成员
					</el-button>
				</div>
			</div>
			<!-- 成员展示列表 -->
			<div v-loading="rfLoading" class="table">
				<DynamicList
					:table-head="tableHead"
					:table-data="tableData"
					:table-head-status="false"
					:table-head-btns="false"
					:hide-btn="true"
					@handleRow="handleRow"
				></DynamicList>
			</div>
			<div class="page">
				<el-pagination
					background
					:current-page="page"
					:page-sizes="[10, 20, 50, 100]"
					:page-size="size"
					layout="total, sizes, ->, prev, pager, next, jumper"
					:total="total"
					@size-change="handleSizeChange"
					@current-change="handleCurrentChange"
				></el-pagination>
			</div>
		</div>
		<!-- 添加成员弹窗 -->
		<el-dialog
			v-loading="addLoading"
			:title="type === 'edite' ? '编辑成员' : '添加成员'"
			class="custom-el-dialog-footer"
			:visible.sync="visible"
			:before-close="closeVisible"
		>
			<div v-if="visible" class="dialog-box">
				<!-- <div slot="header" class="head">
          <div class="title">添加成员</div>
          <div class="el-icon-close close" @click=""></div>
        </div> -->
				<div class="btn-title">
					<div class="tit">基本信息</div>
				</div>
				<v-form-render
					v-if="formJson.widgetList"
					ref="vFormRender"
					:form-json="formJson"
					:form-data="formData"
					:option-data="optionData"
					:base-url="config.baseUrl"
				></v-form-render>
				<div>
					<div class="btn-title jc-bt">
						<div v-if="userVersion === 'v2'" class="tit">
							身份信息
							<span>*每个成员有且仅可设置一个主身份</span>
						</div>
						<div v-else class="tit">
							部门信息
							<!--              <span>*每个成员有且仅可设置一个主部门</span>-->
						</div>
						<div style="cursor: pointer" @click="addUser">
							+ {{ userVersion == 2 ? '添加身份' : '添加部门' }}
						</div>
					</div>

					<userList ref="userList" :dep-list="depList"></userList>
				</div>
			</div>
			<div slot="footer" class="dialog-footer">
				<el-button @click="closeVisible">取消</el-button>
				<el-button v-if="formData.id" type="primary" @click="handlerUserInfoSave(false)">
					保存
				</el-button>
				<el-button v-else type="primary" @click="sunmit">确认</el-button>
			</div>
		</el-dialog>
		<!-- 成员信息弹窗（编辑、详情） -->
		<el-drawer title="成员详情" :visible.sync="drawer" size="474px" destroy-on-close>
			<div class="drawer">
				<div class="user">
					<FileById v-if="formData.avatar" :value="formData.avatar" :size="50" />
					<FileById
						v-else
						:default-font-icon="formData.realname ? formData.realname.slice(-1) : ''"
						:size="50"
					/>
					<div class="name">
						<div>{{ formData.realname }}</div>
						<p>
							<b>用户名:</b>
							<span>{{ formData.username }}</span>
						</p>
					</div>
					<!--					<div class="btn">-->
					<!--						<el-link :underline="false">更多操作</el-link>-->
					<!--						<img src="@/assets/images/dynamic/setting.png" alt="" />-->
					<!--					</div>-->
				</div>
				<el-tabs v-model="activeName">
					<el-tab-pane label="基本信息" name="first">
						<!-- <v-form-render
              v-if="drawerJson.widgetList"
              ref="vFormRef"
              v-loading="userInfoLoading"
              :form-json="drawerJson"
              :form-data="formData"
              :disabled="userFormDisabled"
              :option-data="optionData"
              :base-url="config.baseUrl"
            ></v-form-render> -->
						<userInfo :drawer-json="drawerJson.widgetList" :form-data="formData"></userInfo>
					</el-tab-pane>
				</el-tabs>
			</div>
			<div v-if="activeName == 'first'" class="drawer-footer">
				<el-button type="primary" ghost @click="handlerUserInfoSave(1)">
					{{ userFormDisabled ? '编辑资料' : '保存信息' }}
				</el-button>
			</div>
		</el-drawer>
	</div>
</template>
<script>
import OrganizationTree from '@/components/organization-tree/index.vue';
import SearchFormRender from '@/components/search-form-render/index.vue';
import DynamicList from '../components/dynamic-list/index.vue';
import { getItem } from '@/utils/localstorage';
import UserList from '@/views/departments-members/components/user-list.vue';
import {
	deleteTenantUser,
	getDesigns,
	getorgAndDepartTree,
	getUserRole,
	getUsers,
	tenantUser,
	updateTenantUser
} from '@/api/modules/dynamic';
import { processList } from '@/utils';
import { getCalendarType } from '@/api/modules/calendar';
import { config } from '@vue/test-utils';
import { mapGetters } from 'vuex';
import UserInfo from '@/views/departments-members/components/user-info.vue';
import { getDictionary } from '@/utils/data-dictionary';

export default {
	name: 'MembersDepartmentsUser',
	components: { UserInfo, UserList, DynamicList, SearchFormRender, OrganizationTree },
	data() {
		return {
			config,
			drawerJson: {},
			userData: {},
			activeName: 'first',
			drawer: false,
			addLoading: false,
			lfLoading: false,
			rfLoading: false,
			diaLoading: false,
			// 成员信息详情抽屉loading
			userInfoLoading: false,
			formJson: {},
			formData: {
				realname: ''
			},
			optionData: {},
			tabIndex: 0,
			treeData: [],
			departmentData: [],
			postData: [],
			defaultCheckedKeys: [],
			title: '',
			search: '',
			visible: false,
			dialogVisible: false,
			midList: [
				{ label: '表单设计' },
				{ label: '流程设计' },
				{ label: '列表设计' },
				{ label: '高级设置' }
			],
			act: 1,
			isEdit: false,
			tableHead: [],
			tableData: [],
			page: 1,
			size: 10,
			total: 0,
			userFormDisabled: true,
			selectedTableHead: [],
			addSysCode: {
				sysCode: 'sys_user',
				designCode: 'formSys:sysUserList',
				designType: '2',
				// isH5: 0,
				moduleId: '1737019234036236293',
				applicationId: getDictionary('应用ID/通讯录'),
				isDefault: '0'
			},
			searchSelectedQueryColumns: [],
			optionalQueryColumns: [],
			// 列表条件查询
			param: null,
			searchType: null,
			optionalColumns: [],
			searchId: null,
			thingData: [],
			popoverShow: false,
			thingTableHead: [
				{ desc: '应用(族)名称', field: 'appName' },
				{ desc: '订阅目的', field: 'purpose' }
			],
			btnPower: getItem('permission_identification')
				? JSON.parse(getItem('permission_identification'))
				: [],
			thingTitle: {},
			dynamicTab: [],
			haveClearDevice: false,
			postDicts: [], //岗位类型
			currentIndex: 2,

			depList: [],
			searchFormJson: {},
			jsonRes: {},
			type: ''
		};
	},
	computed: {
		...mapGetters(['rentInfo', 'userInfo', 'userVersion'])
	},
	created() {
		// 成员树

		this.getorgAndDepartTree();
		this.getUsersTable();
		this.getDesigns(1);
		this.getDesigns(2);
	},
	methods: {
		// 条件搜索
		handleSearch(data) {
			this.param = data;
			this.page = 1;
			this.getUsersTable();
		},
		// 重置条件搜索
		handleReset() {
			this.param = null;
			this.page = 1;
			this.getUsersTable();
		},
		openAdd() {
			this.type = 'add';
			this.formData = {};
			this.openModal();
		},
		async handleRow(type, row) {
			this.type = type;
			if (type === 'delete') {
				this.$confirm('此操作将永久删除该成员, 是否继续?', '提示', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					type: 'warning'
				}).then(() => {
					deleteTenantUser(row.id).then(res => {
						if (res.code === 200) {
							this.getUsersTable();
							this.$message({
								message: '删除成功',
								type: 'success'
							});
						} else {
							this.$message({
								message: res.message,
								type: 'error'
							});
						}
					});
				});
			} else if (type === 'edite') {
				const loading = this.$loading({
					lock: true,
					text: '',
					spinner: 'el-icon-loading',
					background: 'rgba(0, 0, 0, 0.1)'
				});
				// 获取用户详情展示数据
				this.formData = {};
				const res2 = await getUserRole({ id: row.id });
				// 数据获取完成
				if (this.jsonRes.code === 200) {
					const result = this.jsonRes.result;
					let drawerJson = [];
					result.forEach(element => {
						// if (element.isH5 == 0) {
						drawerJson = element;
						// }
					});
					let formJson = drawerJson?.configJson ? JSON.parse(drawerJson?.configJson) : {};
					// if (this.userVersion === 'v2') {
					this.depList = formJson.widgetList[2].options.optionItems;
					formJson.widgetList.forEach((item, index) => {
						if (item.options.label === '所属部门') {
							formJson.widgetList.splice(index, 1);
						}
					});
					// }
					let widgetList = formJson.widgetList;
					widgetList = widgetList.map(item => {
						if (item && item.options && item.options.url !== undefined) {
							let url = item.options.url;
							if (url && typeof url === 'string' && !url.includes('/api')) {
								url = '/api' + url + '?limitOrg=true';
							} else {
								url = url + '?limitOrg=true';
							}
							item.options.url = url;
						}
						return { ...item };
					});
					formJson.widgetList = widgetList;
					this.formJson = formJson;
				}
				if (res2.code === 200) {
					// 组件加载完成，使用异步加载数据
					if (this.$refs.vFormRef) {
						this.$refs.vFormRef.setFormData(res2.result);
					}
					this.formData = res2.result;
				}
				// this.visible = true;
				this.openModal(res2.result?.userIdentities || []);
				loading.close();
				// let _this = this;

				// if (_this.$refs.userList) {
				// 	this.$nextTick(() => {
				// 		_this.$refs.userList.tableData = res2.result?.userIdentities || [];
				//
				// 	});
				// }
			} else {
				const loading = this.$loading({
					lock: true,
					text: '',
					spinner: 'el-icon-loading',
					background: 'rgba(0, 0, 0, 0.1)'
				});
				// 根据触发按钮类型启用或关闭信息的编辑或查看
				this.userFormDisabled = !(type === 'edite');
				// 获取用户详情展示数据
				this.formData = {};
				// 获取表单设计数据
				const res1 = await getDesigns({
					sysCode: 'sys_user',
					designType: 1
				});
				// 获取用户信息数据
				const res2 = await getUserRole({ id: row.id });
				const list = await Promise.all([res2, res1]);
				// 数据获取完成
				if (list[1].code === 200) {
					const result = list[1].result;
					let drawerJson = [];
					result.forEach(element => {
						// if (element.isH5 == 0) {
						drawerJson = element;
						// }
					});
					this.drawerJson = drawerJson?.configJson ? JSON.parse(drawerJson?.configJson) : {};
				}
				if (list[0].code === 200) {
					// 组件加载完成，使用异步加载数据
					if (this.$refs.vFormRef) {
						this.$refs.vFormRef.setFormData(list[0].result);
					}
					this.formData = list[0].result;
				}
				// 每次默认第一个
				this.activeName = 'first';
				this.drawer = true;
				loading.close();
			}
		},
		handleCurrentChange(i) {
			this.page = i;
			this.getUsersTable();
		},
		handleSizeChange(i) {
			this.page = 1;
			this.size = i;
			this.getUsersTable();
		},
		handleOrg(i) {
			this.page = 1;
			this.searchType = i ? (i.dataType === 'org' ? 'org' : 'depart') : '';
			this.searchId = i ? i.id : '';
			this.title = i ? i.title : '';
			this.getUsersTable();
		},
		closeVisible() {
			this.visible = false;
		},
		// 添加身份
		addUser() {
			this.$refs.userList.addUser();
		},
		// 获取成员树组件数据
		getorgAndDepartTree(includeUsers) {
			this.lfLoading = true;
			getorgAndDepartTree({ includeUsers, limitOrg: true }).then(res => {
				if (res.code === 200) {
					this.treeData = res.result;
				}
				this.lfLoading = false;
			});
		},
		// 处理表单列表数据
		setUserIdentities() {
			let userIdentities = this.$refs.userList?.tableData || [];
			let isreq = false;
			if (userIdentities.length > 0) {
				userIdentities.forEach((item, index) => {
					item.sortOrder = index + 1;
					if (this.userVersion === 'v1') {
						isreq = !item.deptId;
					} else {
						isreq = !item.deptId || !item.postId || !item.jobTitle;
					}
				});
				if (isreq) {
					this.$message({
						message: '请完善身份信息！',
						type: 'error'
					});
				}
			}
			return {
				isreq,
				userIdentities
			};
		},
		// 处理表单数据
		setFormData(userIdentities, formData) {
			this.addLoading = true;
			let inpList = this.$refs.vFormRender.getFieldWidgets();
			let data = [];
			inpList = inpList.reduce((accumulator, currentValue) => {
				accumulator[currentValue.name] = currentValue;
				return accumulator;
			}, {});
			for (let key in inpList) {
				let item = {};
				let source = inpList[key]?.field?.options?.source;
				if (!source) {
					source = this.getDataSourceType(inpList[key]?.type) ? 2 : '';
				}
				item.field = key;
				item.value = formData[key];
				item.source = source;
				data.push(item);
			}
			if (this.userVersion === 'v2') {
				data.push({
					field: 'userIdentities',
					value: userIdentities,
					source: 1
				});
			} else {
				data.push({
					field: 'departs',
					value: userIdentities.map(item => {
						return item.deptId;
					}),
					source: 1
				});
			}
			return data;
		},
		sunmit() {
			let { isreq = true, userIdentities } = this.setUserIdentities();
			if (isreq) return;
			this.$refs.vFormRender
				.getFormData()
				.then(formData => {
					let data = this.setFormData(userIdentities, formData);
					tenantUser(data).then(res => {
						if (res.code == 200) {
							let { username, password } = res.result;
							this.$message({
								message: '新增成功',
								type: 'success'
							});
							this.visible = false;
							this.$alert(
								`<div>
							            <span style='font-weight:600;margin-left:40px'>初始账号：</span>
							            <span>${username}</span>
							          </div>
							          <div>
							            <span style='font-weight:600;margin-left:40px'>初始密码：</span>
							            <span>${password}</span>
							          </div>`,
								'提示',
								{
									dangerouslyUseHTMLString: true,
									confirmButtonText: '确定',
									callback: action => {}
								}
							);
							this.getUsersTable();
						} else {
							this.$message.error({
								message: res.message
							});
						}
						this.addLoading = false;
					});
				})
				.catch(error => {});
		},
		// 编辑用户信息
		handlerUserInfoSave(type) {
			if (type) {
				this.drawer = false;
				this.handleRow('edite', this.formData);
			} else {
				let { isreq = true, userIdentities } = this.setUserIdentities();
				if (isreq) return;
				// 保存编辑信息
				this.$refs.vFormRender.getFormData().then(formData => {
					this.addLoading = true;
					let data = this.setFormData(userIdentities, formData);
					// 数据提交
					updateTenantUser(data, this.formData.id).then(res => {
						if (res.code === 200) {
							this.visible = false;
							this.$message({ message: '成员信息已修改', type: 'success' });
							this.getUsersTable();
						} else {
							this.$message.error('成员信息修改失败，请检查输入内容后重试');
						}
						this.addLoading = false;
					});
				});
			}
		},
		// 获取设计
		getDesigns(designType) {
			if (designType == 1) {
				this.addLoading = true;
				this.diaLoading = true;
			}
			getDesigns({
				sysCode: 'sys_user',
				designType
			}).then(res => {
				if (designType == 1) {
					this.jsonRes = res;
				}
				if (res.code == 200) {
					let data;
					if (designType == 1) {
						if (res.result) {
							let drawerJson = [];
							for (let i = 0; i < res.result.length; i++) {
								// PC端的配置
								// if (res.result[i].isH5 != 1) {
								data = JSON.parse(res.result[i].configJson);
								// if (this.userVersion == 'v2') {
								this.depList = data.widgetList[2].options.optionItems;
								data.widgetList.forEach((item, index) => {
									if (item.options.label == '所属部门') {
										data.widgetList.splice(index, 1);
									}
								});
								// }
								let widgetList = data.widgetList;
								widgetList = widgetList.map(item => {
									if (item && item.options && item.options.url !== undefined) {
										let url = item.options.url;
										if (url && typeof url === 'string' && !url.includes('/api')) {
											url = '/api' + url + '?limitOrg=true';
										} else {
											url = url + '?limitOrg=true';
										}
										item.options.url = url;
									}
									return { ...item };
								});
								data.widgetList = widgetList;
								this.formJson = data;
								// 更新新增和编辑的表单渲染filde数据
								if (this.$refs.vFormRender) {
									this.$refs.vFormRender.setFormJson(this.formJson);
								}
								this.isEdit = true;
								drawerJson = res.result[i];
								// }
							}
							// 使用设计配置信息初始化drawerJson（查看、编辑等）
							this.drawerJson = drawerJson?.configJson ? JSON.parse(drawerJson?.configJson) : {};
						} else {
							this.isEdit = false;
						}

						this.addLoading = false;
						this.diaLoading = false;
					} else {
						if (res.result[0]?.configJson) {
							this.isEdit = true;
							// 设置table的head数据
							const configJson = JSON.parse(res.result[0].configJson);
							let optionalColumns = JSON.parse(JSON.stringify(configJson.optionalColumns));
							let resColums = [];
							resColums = processList(optionalColumns, 'pcChecked', 'pcSort');
							resColums.push({
								desc: '操作',
								btns: [
									{
										name: '查看信息',
										type: 'query'
									},
									{
										name: '编辑成员',
										type: 'edite'
									},
									{
										name: '删除成员',
										type: 'delete'
									}
								],
								btnsOnEvent: this.handleRow
							});
							let widgetList = processList(optionalColumns, 'pcQueryChecked', 'pcQuerySort');
							widgetList = widgetList.map(item => {
								if (
									item &&
									item.fields &&
									item.fields.options &&
									item.fields.options.url !== undefined
								) {
									let url = item.fields.options.url;
									if (url && typeof url === 'string' && !url.includes('/api')) {
										url = '/api' + url + '?limitOrg=true';
									} else {
										url = url + '?limitOrg=true';
									}
									item.fields.options.url = url;
								}
								// 当搜索时关闭所有表单验证
								item.fields.options.required = false;
								// 将隐藏的字段展示到搜索配置上
								item.fields.options.hidden = false;
								return {
									...item.fields,
									formItemFlag: true
								};
							});
							this.tableHead = resColums;
							this.searchFormJson = {
								widgetList: widgetList
							};
							this.searchSelectedQueryColumns = configJson.selectedQueryColumns;
							this.optionalQueryColumns = configJson.optionalQueryColumns;
							this.optionalColumns = configJson.optionalColumns;
							if (this.$refs.vFormRefSearch) {
								this.$refs.vFormRefSearch.setFormJson(this.searchFormJson);
							}
						} else {
							this.isEdit = false;
						}
					}
				}
			});
		},
		// 获取列表数据
		getUsersTable() {
			this.rfLoading = true;
			getUsers({
				pageNo: this.page,
				pageSize: this.size,
				param: this.param,
				limitOrg: true,
				departId: this.searchType === 'depart' ? this.searchId : '',
				orgId: this.searchType === 'org' ? this.searchId : ''
			}).then(res => {
				if (res.code == 200) {
					this.total = res.result.total;
					// 处理所属部门与成员标签展示形式
					const userDataRes = res.result.records.map(record => {
						record.departs = record.departs ? record.departs.join(',') : null;
						record.labels = record.labels ? record.labels.join(',') : null;
						return record;
					});
					this.tableData = userDataRes;
				}
				this.rfLoading = false;
			});
		},
		openModal(tabledata) {
			getCalendarType({
				applicationId: getDictionary('应用ID/通讯录'),
				moduleCode: 'module:user',
				slotCode: 'slot:userAddOperate'
			}).then(res => {
				if (res.code === 200) {
					let url = res?.result[0]?.accessJson || 0;
					this.thingTitle = res?.result[0] || {};
					if (url) {
						url = JSON.parse(url);
						url = url?.url;
						// dynamicUrl(url, {
						// 	tenantId: get_token('X-Tenant-Id'),
						// 	applicationId: 'A1002'
						// }).then(res1 => {
						// 	if (res1.code === 200) {
						// 		this.thingData = res1.result;
						// 	}
						// });
					}
				}
			});
			if (this.tabIndex == 1 && this.searchType === 'depart') {
				this.formData.departs = [this.searchId];
			}
			this.getDesigns(1);
			this.visible = true;
			if (tabledata && tabledata.length > 0) {
				this.$nextTick(() => {
					if (this.$refs.userList) {
						this.$refs.userList.setData(tabledata);
					}
				});
			}
		}
	}
};
</script>
<style scoped lang="scss">
@import '~@/views/departments-members/components/index.scss';
::v-deep .el-table__body-wrapper {
	&::-webkit-scrollbar {
		height: 6px !important;
	}
}
.jc-bt {
	display: flex;
	justify-content: space-between;
	align-items: center;
	color: var(--brand-6);
}
</style>
