<template>
	<div class="members-departments">
		<div v-if="pathType == 1" class="title">
			<div class="title-left" @click="goBack">
				<svg-icon class="title-icon" icon-class="seal-left"></svg-icon>
				<span class="title-text">返回</span>
			</div>
		</div>
		<div class="tabs">
			<div class="tab" :class="tabIndex === 0 ? 'active' : ''" @click="handleTab(0)">成员</div>
			<div class="tab" :class="tabIndex === 1 ? 'active' : ''" @click="handleTab(1)">部门</div>
			<div class="tab" :class="tabIndex === 2 ? 'active' : ''" @click="handleTab(2)">
				角色与权限
			</div>
			<div class="line" :style="{ left: getLeft() }"></div>
		</div>
		<!-- 成员 -->
		<div v-if="tabIndex === 0" class="box">
			<MembersDepartmentsUser></MembersDepartmentsUser>
		</div>
		<div v-if="tabIndex === 1" class="box">
			<dept></dept>
		</div>
		<div v-if="tabIndex === 2" class="box">
			<authority></authority>
		</div>
	</div>
</template>
<script>
import MembersDepartmentsUser from '@/views/departments-members/components/user.vue';
import { mapGetters } from 'vuex';
import Dept from '@/views/departments-members/components/dept.vue';
import Authority from '@/views/departments-members/components/authority.vue';

export default {
	name: 'MembersDepartments',
	components: { Authority, Dept, MembersDepartmentsUser },
	data() {
		return {
			tabIndex: 0,
			lfLoading: false,
			pathType: '',
			treeData: []
		};
	},
	computed: {
		...mapGetters(['rentInfo', 'userInfo', 'userVersion'])
	},
	created() {
		this.pathType = this.$route.query.pathType || '';
	},
	methods: {
		handleTab(index) {
			this.tabIndex = index;
		},
		goBack() {
			this.$router.go(-1);
		},
		handleOrg() {},
		getLeft() {
			if (this.tabIndex == 0) {
				return '20px';
			} else if (this.tabIndex == 1) {
				return '84px';
			} else if (this.tabIndex == 2) {
				return '174px';
			}
			return '212px';
		}
	}
};
</script>
<style scoped lang="scss">
.members-departments {
	margin: 0 23px;
	height: 100%;
	background-color: #fff;
	border-radius: 12px;
	.title {
		padding: 20px;
		display: flex;
		align-items: center;
		justify-content: space-between;
		border-bottom: 1px solid #f0f0f0;

		&-left {
			cursor: pointer;
			display: flex;
			align-items: center;
		}
	}
	.tabs {
		height: 52px;
		border-bottom: 1px solid #f0f0f0;
		display: flex;
		align-items: center;
		padding: 0 20px;
		position: relative;

		.tab {
			font-size: 16px;
			font-weight: 400;
			color: $textColor;
			line-height: 22px;
			margin-right: 32px;
			cursor: pointer;
		}

		.line {
			width: 32px;
			height: 3px;
			background: var(--brand-6);
			border-radius: 9px;
			position: absolute;
			bottom: 0;
			left: 12px;
			transition: all 0.3s;
		}
	}
}
.box {
	height: calc(100% - 54px);
}
</style>
