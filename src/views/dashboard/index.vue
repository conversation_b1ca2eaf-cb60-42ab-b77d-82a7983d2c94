<!--
 * @Description:
 * @Version: 1.0
 * @Autor: zhao<PERSON>ming
 * @Date: 2022-06-09 17:08:30
 * @LastEditors: zhaodongming
 * @LastEditTime: 2022-12-05 15:49:24
-->
<template>
	<div class="dashboard-container">
		<div class="dashboard-text">仪表盘</div>
		<el-row :gutter="10" style="margin: 10px">
			<el-col :span="6" :xs="24" :sm="24" :md="12" :lg="6">
				<el-stat-card title="总销售量" :actions="actions">
					<el-number slot="total" value="13345" prefix="￥"></el-number>
					<div style="height: 60px; padding-top: 35px">
						<span>
							周同比
							<el-number :value="0.12" percentage type="primary" trend="up"></el-number>
						</span>
						<span style="margin-left: 10px">
							日同比
							<el-number :value="0.1" percentage type="primary" trend="down"></el-number>
						</span>
					</div>
					<div slot="footer">
						日销售额
						<el-number value="3345" prefix="￥"></el-number>
					</div>
				</el-stat-card>
			</el-col>
			<el-col :span="6" :xs="24" :sm="24" :md="12" :lg="6">
				<el-stat-card title="访问量" :actions="actions">
					<el-number slot="total" value="3345"></el-number>
					<div slot="footer">
						日访问量
						<el-number value="3345" prefix="￥"></el-number>
					</div>
				</el-stat-card>
			</el-col>
			<el-col :span="6" :xs="24" :sm="24" :md="12" :lg="6">
				<el-stat-card title="总销售量">
					<el-corner-mark>HOT</el-corner-mark>
					<el-number slot="total" value="3345"></el-number>
					<div slot="footer">
						日访问量
						<el-number value="3345" prefix="￥"></el-number>
					</div>
				</el-stat-card>
			</el-col>
			<el-col :span="6" :xs="24" :sm="24" :md="12" :lg="6">
				<el-stat-card title="总销售量">
					<el-corner-mark type="success">TOP</el-corner-mark>
					<el-number slot="total" value="0.5" percentage></el-number>
					<div style="height: 60px; padding-top: 35px">
						<el-progress :stroke-width="14" :percentage="50" :show-text="false"></el-progress>
					</div>
					<div slot="footer">
						日访问量
						<el-number value="3345" prefix="￥"></el-number>
					</div>
				</el-stat-card>
			</el-col>
		</el-row>
		<div class="dashboard-text">测试Name: {{ name }}</div>
		<el-button
			v-md:cs_event
			type="primary"
			icon="el-icon-share"
			data-md-name="测试名称"
			data-md-id="测试ID"
			data-md-desc="desc"
			@click="handlerChangeColor"
		>
			换肤
		</el-button>
		<theme-set :drawer="drawer" @change="isShow"></theme-set>
		<div class="color-test">色彩混合演示</div>
		<AsyncComponent
			url="https://minio.wisesoft.net.cn/frontend/resources/eoss_component.1.0.0.umd.js"
		/>
	</div>
</template>

<script>
import { mapGetters } from 'vuex';
import ThemeSet from '@/components/theme-set';
import AsyncComponent from '@/components/async-component';

export default {
	name: 'Dashboard',
	components: {
		ThemeSet,
		AsyncComponent
	},
	data() {
		return {
			radio: '',
			drawer: false,
			actions: [
				{
					icon: 'el-icon-info',
					tooltip: '指标说明'
				}
			],
			chartOption: {
				grid: {
					left: -1,
					top: 0,
					bottom: 0,
					right: 0
				},
				'legend.show': false,
				'yAxis.0.splitLine.show': false,
				color: ['#91D5FF'],
				series: {
					symbol: 'none'
				}
			}
		};
	},
	computed: {
		...mapGetters(['name'])
	},
	methods: {
		handlerChangeColor() {
			this.drawer = !this.drawer;
		},
		isShow(val) {
			this.drawer = val;
		},
		getChartData() {
			const rows = [];
			for (let i = 0; i < 30; i++) {
				rows.push([i, Math.floor(Math.random() * 1000)]);
			}
			return {
				columns: ['日期', '数量'],
				rows
			};
		}
	}
};
</script>

<style lang="scss" scoped>
.dashboard {
	&-container {
		margin: 30px;
	}
	&-text {
		font-size: 30px;
		line-height: 46px;
		color: var(--brand-6);
	}
}

.color-test {
	font-size: 25px;
	color: mix(#000000, #1890ff, 38%);
}
.el-col {
	margin-bottom: 10px;
}
</style>
