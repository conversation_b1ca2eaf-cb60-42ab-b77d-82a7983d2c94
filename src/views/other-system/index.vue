<template>
	<div v-loading="loading" class="content">
		<iframe id="iframe" ref="iframe" class="iframe" src="" frameborder="0"></iframe>
		<div v-show="showMask" class="mask"></div>
		<el-dialog
			title="流程"
			:visible.sync="showFlow"
			class="desk-el-custom-dialog"
			top="0"
			width="60%"
			@close="closeFlow"
		>
			<FlowPopup v-if="showFlow" :flow-query="flowQuery"></FlowPopup>
		</el-dialog>
		<!--  待办详情Start  -->
		<waitHandle ref="detailDialog" @updateList="updateList"></waitHandle>
		<!--  待办详情End  -->
	</div>
</template>

<script>
import { otherSystem, preUrl } from '@/config';
import { mapGetters, mapMutations, mapState } from 'vuex';
import { get_token, getToken } from '@/utils/auth';
import { checkLogin } from '@/api/modules/common';
import FlowPopup from '@/views/process/flow-view/index.vue';
import waitHandle from '@/components/wait-detail/index.vue';
import { CoosEventTypes } from '@/utils/bus';

export default {
	name: 'IframeIndex',
	components: { FlowPopup, waitHandle },
	data() {
		return {
			otherSystem,
			flowQuery: {},
			showFlow: false,
			showMask: false, // 是否显示遮罩层
			needUpdate: true, // 需要更新
			iframeHost: '', // 主机域名
			loading: false, // 加载效果
			eventMap: [
				{
					code: 3,
					name: '发送Token',
					change: this.getToken
				},
				{
					code: 5,
					name: '登录失效',
					change: this.goLogin
				},
				{
					code: 9,
					name: '跳转页面',
					change: this.jumpPage
				},
				{
					code: 1118,
					name: '打开流程窗口',
					change: this.openFlow
				},
				{
					code: 2118,
					name: '打开待办窗口',
					change: this.openWait
				}
			]
		};
	},
	computed: {
		...mapState('app', ['iframes', 'wujies', 'workApp']),
		...mapGetters(['userInfo']),
		...mapState('user', ['waitConfig']),
		// 打开待办的方式
		openDetailType() {
			return this.waitConfig.customerTaskMenu
				? this.waitConfig.customerTaskMenu.detailOpenType
				: '1';
		}
	},
	watch: {
		// 路由变化，iframe地址发生变化，但是一定要在dom加载后才执行
		$route(newVal) {
			if (this.needUpdate) {
				this.init();
			}
		},
		// keep-alive激活后才有dom，才能执行，弥补$route监听的漏洞
		needUpdate(newVal) {
			if (newVal) {
				this.init();
			}
		}
	},
	mounted() {
		this.init();
		window.addEventListener('message', this.handleMessage);
		this._BUS.$on(CoosEventTypes.changeMask, showMask => {
			this.showMask = showMask;
		});
	},
	beforeDestroy() {
		window.removeEventListener('message', this.handleMessage);
		this._BUS.$off(CoosEventTypes.changeMask);
	},
	activated() {
		this.needUpdate = true;
	},
	deactivated() {
		this.needUpdate = false;
	},
	methods: {
		...mapMutations('app', ['SET_IFRAMES']),
		jumpPage(data) {
			let { openType, url, isExternal } = data;
			if (isExternal) {
				let mainUrl = /http/gi.test(url) ? url : (preUrl || window.location.origin) + url;
				this.$router.push(`/other-system?url=${encodeURIComponent(mainUrl)}`);
				return;
			}
			if (/^http/.test(url)) {
				if (openType === 2) {
					window.open(url);
				} else {
					this.$router.push(`/other-system?url=${encodeURIComponent(url)}`);
				}
			} else {
				this.$router.push(url);
			}
		},
		openFlow(data) {
			this.flowQuery = data;
			this.showFlow = true;
		},
		openWait(data) {
			if (this.openDetailType === '2') {
				const route = this.$router.resolve({ name: 'WaitDetail' });
				window.open(`${route.href}?blank_id=${data.id}`, '_blank');
			} else {
				this.$refs.detailDialog.open(data);
			}
		},
		closeFlow() {
			this.showFlow = false;
		},
		/**
		 * @method 处理主子应用通信事件
		 * */
		handleMessage(message) {
			let data = message.data;
			// 因为postMessage都挂载在window监听，所以通过code判断是否本实例进行的通知
			// && data.arg.message.webKey === this.socketVM.code
			let eventObj = this.eventMap.find(item => {
				return item.code === data.code;
			});
			if (eventObj) {
				eventObj.change(data.data);
			}
		},
		/**返回登录页*/
		goLogin() {
			checkLogin().then(res => {
				if (res.code === 200) {
					this.$message.error('主应用登录未过期！');
				}
			});
		},
		/**获取token*/
		getToken() {
			if (this.$refs.iframe && this.$refs.iframe.contentWindow) {
				this.$refs.iframe.contentWindow.postMessage(
					{
						code: 3,
						accessToken: getToken(), // token
						tenantId: get_token('X-Coos-Client-Tenant-Id') // 租户id
					},
					'*'
				);
			}
		},
		/**关闭弹窗更新*/
		updateList() {
			if (this.$refs.iframe && this.$refs.iframe.contentWindow) {
				this.$refs.iframe.contentWindow.postMessage(
					{
						code: 2118
					},
					'*'
				);
			}
		},
		init() {
			// 拿到iframe的url以及参数
			// let url = this.$route.query.url ? decodeURIComponent(this.$route.query.url) : '';
			let url = this.$route.query.url;
			if (this.$route.name === 'OtherSystem' && url) {
				this.loading = true;
				this.iframeHost = url.split('#')[0]; // 获取主机域名
				// 不包括主机域名，说明第一次进行加载，缓存起来，后续直接匹配，避免每次重复加载资源
				if (!this.iframes.includes(this.iframeHost)) {
					this.SET_IFRAMES([this.iframeHost]);
				} else {
					this.loading = false;
				}
				// js改动路由
				this.$nextTick(() => {
					let iframe = document.getElementById('iframe'); // this.iframeHost
					iframe.src = url;
					// 不要重复添加监听
					// IE下
					if (iframe.attachEvent) {
						iframe.attachEvent('onload', () => {
							//    后续操作
							this.loading = false;
						});
					} else {
						//    后续操作
						iframe.onload = () => {
							this.loading = false;
						};
					}
				});
			}
		}
	}
};
</script>

<style scoped lang="scss">
.content {
	width: 100%;
	height: 100%;
	position: relative;
	display: flex;
	align-items: center;
}
.iframe {
	//width: 100%;
	height: 100%;
	flex: 1;
}
.mask {
	width: 100%;
	height: 100%;
	position: absolute;
	top: 0;
	left: 0;
	z-index: 1;
}
.aiFont {
	width: 60px;
	height: 60px;
	border-radius: 50%;
	box-shadow: 0px 1px 3px 0px var(--brand-1), 0px 2px 6px 0px var(--brand-1),
		0px 5px 9px 0px var(--brand-1), 0px 17px 24px 0px var(--brand-1);
}

.draggContent {
	height: calc(100% - 60px);
	display: flex;
	width: 40%;
	min-width: 500px;
	align-items: center;
	padding: 20px 0px;
}

.coosContent {
	border-left: 1px solid #dce3e7;
	flex-shrink: 0;
	width: 30%;
	border-radius: 12px;
	height: 100%;
	max-width: 500px;
	min-width: 400px;
	box-shadow: 0px 1px 3px 0px rgba(40, 76, 185, 0.06), 0px 2px 6px 0px rgba(40, 76, 185, 0.09),
		0px 5px 9px 0px rgba(40, 76, 185, 0.12), 0px 17px 24px 0px rgba(40, 76, 185, 0.18);
	transition: all 0.3s;
}
</style>
