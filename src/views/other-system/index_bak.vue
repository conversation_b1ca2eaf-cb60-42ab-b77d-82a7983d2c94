<template>
	<div v-loading="loading" class="content">
		<!--和子项目做了无界对接的才可以使用-->
		<!--子路由跳转未调试通，暂时不用-->
		<!--<WujieVue
     v-for="(item, index) of wujies"
     v-show="iframeHost === item.url"
     :key="index"
     width="100%"
     height="100%"
     :props="{ real_route: realRoute }"
     :name="item.name"
     :url="item.url"
   />-->
		<!--否则使用iframe，未对接，fetch请求不到资源，也不能通知子组件路有跳转-->
		<!--  iframe缓存版本S  -->
		<!--		<iframe-->
		<!--			v-for="(item, index) of iframes"-->
		<!--			v-show="iframeHost === item"-->
		<!--			:id="item"-->
		<!--			:key="index"-->
		<!--			class="iframe"-->
		<!--			:src="item"-->
		<!--			frameborder="0"-->
		<!--		></iframe>-->
		<!--  iframe缓存版本E  -->
		<iframe id="iframe" class="iframe" src="" frameborder="0"></iframe>
		<div v-show="showMask" class="mask"></div>
	</div>
</template>

<script>
import { otherSystem } from '@/config';
import { mapMutations, mapState } from 'vuex';
import { CoosEventTypes } from '@/utils/bus';

export default {
	name: 'IframeIndex',
	data() {
		return {
			otherSystem,
			showMask: false, // 是否显示遮罩层
			realRoute: '', // 无界路由
			needUpdate: true, // 需要更新
			iframeHost: '', // 主机域名
			loading: false, // 加载效果
			workAppId: 6 //测试用
		};
	},
	computed: {
		...mapState('app', ['iframes', 'wujies', 'workApp'])
	},
	watch: {
		// 路由变化，iframe地址发生变化，但是一定要在dom加载后才执行
		$route(newVal) {
			if (this.needUpdate) {
				this.init();
			}
		},
		// keep-alive激活后才有dom，才能执行，弥补$route监听的漏洞
		needUpdate(newVal) {
			if (newVal) {
				this.init();
			}
		}
	},
	mounted() {
		this._BUS.$on(CoosEventTypes.changeMask, showMask => {
			this.showMask = showMask;
		});
	},
	activated() {
		this.needUpdate = true;
	},
	deactivated() {
		this.needUpdate = false;
	},
	methods: {
		...mapMutations('app', ['SET_IFRAMES', 'SET_WUJIES', 'SET_WORKAPP']),
		init() {
			if (this.$route.name === 'OtherSystem') {
				this.SET_WORKAPP({
					appId: 'OtherSystem',
					id: this.workAppId,
					title: this.$route.query.name || this.$route.meta.title,
					url: this.$route.query.url,
					icon: this.$route.query.logoUrlPath || '/minio/coos/1783053566486736897.png'
				});
				this.workAppId++;
				this.loading = true;
				let url = this.$route.query.url ? decodeURIComponent(this.$route.query.url) : '';
				this.iframeHost = url.split('#')[0]; // 获取主机域名
				/**无界子应用路由跳转未调试成功，暂时不用*/
				/**
				// 判断主机域名属不属于无界
				let obj = this.otherSystem.find(item => {
					return this.iframeHost === item.url;
				});
				// 无界只需按照名字匹配，额外的外链，iframe显示
				if (obj) {
					this.realRoute = url.split('#')[1];
					// 不包括主机域名，说明第一次进行加载，缓存起来，后续直接匹配，避免每次重复加载资源
					let isSave = this.wujies.findIndex(item => {
						return item.url === this.iframeHost;
					});
					if (isSave === -1) {
						this.SET_WUJIES(obj);
					}
					this.loading = false;
				}
        else {
         */
				// 不包括主机域名，说明第一次进行加载，缓存起来，后续直接匹配，避免每次重复加载资源
				if (!this.iframes.includes(this.iframeHost)) {
					this.SET_IFRAMES([this.iframeHost]);
				} else {
					this.loading = false;
				}
				// js改动路由
				this.$nextTick(() => {
					let iframe = document.getElementById('iframe'); // this.iframeHost
					iframe.src = url;
					// 不要重复添加监听
					// IE下
					if (iframe.attachEvent) {
						iframe.attachEvent('onload', () => {
							//    后续操作
							this.loading = false;
						});
					} else {
						//    后续操作
						iframe.onload = () => {
							this.loading = false;
						};
					}
				});
				// }
			}
		}
	}
};
</script>

<style scoped lang="scss">
.content {
	width: 100%;
	height: 100%;
	position: relative;
}
.iframe {
	width: 100%;
	height: 100%;
}
.mask {
	width: 100%;
	height: 100%;
	position: absolute;
	top: 0;
	left: 0;
	z-index: 1;
}
</style>
