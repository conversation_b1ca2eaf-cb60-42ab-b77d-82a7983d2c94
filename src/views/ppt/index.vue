<template>
	<div v-loading="loading" class="ppt">
		<div class="head">
			<img :src="require(`@/assets/${rentThem}/ppt/robat.png`)" alt="" />
			AI生成PPT
		</div>
		<div class="content">
			<div class="msg-box">
				<div class="ai-msg">
					<img class="avatar" :src="require(`@/assets/${rentThem}/coos/coos-avatar.png`)" alt="" />
					<div class="mod-msg">
						<div class="tit">Hi，今天你想创作什么主题的PPT呢？我都可以帮到你哦～</div>
						<div class="select">
							<div class="list" :class="select == 0 ? 'act' : ''" @click="handleSelect(0)">
								<div class="top">
									<i class="icon coos-iconfont icon-more1"></i>
									<span>输入主题</span>
								</div>
								<div class="btm">输入主题直接生成PPT</div>
							</div>
							<!-- <div class="list" :class="select == 1 ? 'act' : ''" @click="handleSelect(1)">
								<div class="top">
									<i class="icon coos-iconfont icon-wendang1"></i>
									<span>上传文档</span>
								</div>
								<div class="btm">上传文档生成PPT</div>
							</div> -->
						</div>
					</div>
				</div>
				<div ref="list" class="msg-list">
					<div v-for="(t, i) in msgList" :key="i" class="list">
						<div v-if="t.isMy" class="my">
							<div class="tit">
								{{ t.content }}
							</div>
							<img
								class="avatar"
								:src="require(`@/assets/${rentThem}/coos/coos-avatar.png`)"
								alt=""
							/>
						</div>
						<div v-else class="your">
							<img
								class="avatar"
								:src="require(`@/assets/${rentThem}/coos/coos-avatar.png`)"
								alt=""
							/>
							<div v-if="t.type == 'text'" class="tit">
								{{ t.content }}
							</div>
							<div v-else-if="t.type == 'file'" class="file">
								<img :src="t.img" />
								<div class="right">
									<div class="name">{{ t.name }}</div>
									<div class="size">11</div>
								</div>
							</div>
							<div v-else-if="t.type == 'select'" class="select">
								<div class="msg-content">
									<div class="tit">{{ t.content }}</div>
									<div class="btn-box">
										<div v-for="(item, index) in t.btn" :key="index" class="btn">
											{{ item }}
										</div>
									</div>
								</div>
								<div class="tips">跳过问答，直接生成</div>
							</div>
							<div v-else-if="t.type == 'edit'" class="edit">
								<div class="text">您可以编辑此大纲，或按原样继续</div>
								<div class="title-inp">
									<el-input v-model="t.title" placeholder="请输入内容"></el-input>
								</div>
								<div
									v-for="(item, index) in t.list"
									:key="index"
									class="list-title"
									:class="item.type == 'val' ? 'list-val' : ''"
								>
									<div class="inp-box">
										<span class="icon">·</span>
										<el-input v-model="item.title" placeholder="请输入内容"></el-input>
										<div class="btn-list">
											<i class="btn el-icon-plus" @click="addTitle(i, index, item.type)"></i>
											<i class="btn el-icon-minus" @click="handleMin(i, index)"></i>
										</div>
									</div>
								</div>
								<div class="edit-foot">
									<div class="tips">
										<i class="el-icon-circle-check"></i>
										创作完成
									</div>
									<el-button type="primary" class="btn" @click="handleAdd()">生成PPT</el-button>
								</div>
							</div>
							<div v-else-if="t.type == 'mod'" class="mod">
								<div v-if="modList && modList[modIndex]" class="msg-content">
									<div class="tips">已选择的PPT模板：</div>
									<img
										v-for="(item, index) in modList[modIndex].imgList"
										:key="index"
										:src="`${preUrl}${item}`"
										alt=""
									/>
								</div>
								<div class="foot-tips" @click="templateGetList">重新选择</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="foot">
			<div class="top">
				<div v-if="fileQuery">
					<div class="file-tip">
						<div class="file-tip-left">
							<svg-icon class="file-tip-image" :icon-class="getBackground(fileQuery)"></svg-icon>
							<span class="file-tip-text">正在基于文件“</span>
							<span class="file-tip-name">{{ fileQuery.name }}</span>
							<span class="file-tip-text">”为你生成ppt</span>
						</div>
						<span class="file-tip-btn" @click="clearFile">清空</span>
					</div>
				</div>
				<div v-else>
					<i
						class="coos-iconfont icon-folder folder-file-icon more-icon"
						@click="pickFile('file')"
					/>
					<input
						ref="fileInput"
						type="file"
						:accept="accept"
						style="display: none"
						@change="onPickFile($event)"
					/>
				</div>
				<!--				<div class="list-box">-->
				<!--					&lt;!&ndash; <div v-for="(t, i) in footList" :key="i" class="list">制作精美PPT的方法</div> &ndash;&gt;-->
				<!--				</div>-->
				<img
					class="rf"
					:src="require(`@/assets/${rentThem}/ppt/ql.png`)"
					alt=""
					@click="resetMessageList"
				/>
			</div>
			<div class="input-box">
				<el-input
					v-model="msgInput"
					type="textarea"
					:placeholder="currentMode.prologue || '请输入问题'"
					resize="none"
					rows="2"
					:autosize="{ minRows: 1, maxRows: 10 }"
					@keyup.enter.exact.native="handleSend"
					@keyup.ctrl.enter.native="addNewLine"
				></el-input>
				<el-button type="primary" @click="handleSend()">发送</el-button>
			</div>
		</div>
		<el-dialog
			title=""
			:visible.sync="dialogVisible"
			width="1020px"
			modal-append-to-body
			append-to-body
			:close-on-click-modal="false"
			custom-class="dialog"
		>
			<div v-loading="loading" class="dia-box">
				<div v-if="dialogVisible" class="left">
					<img
						v-if="
							modList &&
							modList.length &&
							modList[modIndex].imgList &&
							modList[modIndex].imgList.length
						"
						:src="`${preUrl}${modList[modIndex].imgList[0]}`"
						alt=""
					/>
					<div class="img-list">
						<img
							v-for="(t, i) in modList[modIndex].imgList"
							:key="i"
							:src="`${preUrl}${t}`"
							alt=""
						/>
					</div>
				</div>
				<div class="right">
					<div class="title">选择PPT主题风格</div>
					<div class="tit">PPT大纲生成中，可以先选择一个喜欢的模板</div>
					<div class="mid-content">
						<div v-for="(t, i) in modList" :key="i" class="list" @click="handleMod(i)">
							<div class="tag">推荐</div>
							<div v-if="modIndex == i" class="mask"></div>
							<img
								v-if="modIndex == i"
								class="icon"
								src="../../assets/images/common/selected.png"
								alt=""
							/>
							<img :src="`${preUrl}${t.imgList[0]}`" alt="" />
						</div>
					</div>
					<div class="right-foot">
						<el-button class="btn" type="primary" @click="createPPTLargeSteel()">选好啦</el-button>
					</div>
				</div>
			</div>
		</el-dialog>
	</div>
</template>

<script>
import {
	templateGetList,
	createPPTLargeSteel,
	createFile,
	uploadPPTLargeSteel
} from '@/api/modules/ppt.js';
import { preUrl, assetsUrl, serveUrl } from '@/config';
import getBackground from '@/utils/get-file-icon';

export default {
	name: 'Ppt',
	comments: {},
	props: {
		chatType: {
			type: [Number, String],
			default: 0
		},
		currentMode: {
			type: Object,
			default: () => {
				return {};
			}
		}
	},
	data() {
		return {
			dialogVisible: false,
			accept: '*',
			assetsUrl,
			preUrl: preUrl,
			select: 0,
			footList: [1],
			msgInput: '',
			msgList: [],
			modList: [],
			isHandleAdd: false,
			modIndex: 0,
			fileQuery: null, // 当前上传的文件
			loading: false,
			query: '',
			config: {
				document: {
					fileType: 'pptx',
					key: 'core_secret',
					title: 'Example Document Title.docx',
					url: assetsUrl + '/1.pptx'
				},
				documentType: 'pptx',
				editorConfig: {
					callbackUrl: 'https://example.com/url-to-callback.ashx'
				}
			},
			list: []
		};
	},
	mounted() {},
	methods: {
		addNewLine() {
			this.msgInput += '\r';
		},
		/**获取文件背景图*/
		getBackground,
		/**清空文件上下文*/
		clearFile() {
			this.fileQuery = null;
		},
		pickFile() {
			this.$nextTick(() => {
				this.$refs['fileInput'].click();
			});
		},
		/**选择文件完成*/
		onPickFile(event) {
			let file = event.target.files[0];
			let { name } = file;
			let typeArr = name.split('.');
			let type = typeArr[typeArr.length - 1];
			event.target.value = '';
			this.processStatus = '#19be6b';
			this.fileQuery = {
				name,
				type,
				file,
				process: 0
			};
		},
		handleAdd() {
			if (!this.modList.length) {
				this.isHandleAdd = true;
				this.templateGetList();
				return;
			}
			this.isHandleAdd = false;
			this.loading = true;
			let title = this.msgList[this.msgList.length - 1].title;
			let content = [];
			let params = {
				title
			};
			let a = 0;
			for (let i = 0; i < this.list.pages.length; i++) {
				if (i == 0) {
					content[a] = {};
					content[a].title = this.list.pages[i].title;
					content[a].content = [];
				} else if (this.list.pages[i].type == 'val') {
					content[a].content.push({
						description: this.list.pages[i].description,
						title: this.list.pages[i].title
					});
				} else {
					a += 1;
					content[a] = {};
					content[a].title = this.list.pages[i].title;
					content[a].content = [];
				}
			}
			params.pages = content;
			createFile({
				templateId: this.modList[this.modIndex].id,
				content: JSON.stringify(params)
			}).then(res => {
				if (res.code == 200) {
					let fileUrl = res.result.fileUrl;
					if (fileUrl.indexOf('http') == -1) {
						fileUrl = `${serveUrl || window.location.origin}${fileUrl}`;
					}
					this.loading = false;
					let url = `${
						serveUrl || window.location.origin
					}/coos_office/?url=${fileUrl}&title=${title}&fileType=pptx&documentType=slide`;
					// let url = `http://localhost:5173/office/?url=${res.result.fileUrl}&title=${title}&fileType=pptx&documentType=slide`;
					window.open(url);
				} else {
					this.loading = false;
					this.$message.error(res.message);
				}
			});
		},
		resetMessageList(type) {
			if (type) {
				this.sessionId = '';
			}
			this.msgList = [];
		},
		setAnswer(data, title) {
			this.loading = true;
			this.msgList = [];
			let answer = JSON.parse(data);
			this.msgList.push({
				isMy: true,
				type: 'text',
				content: title
			});
			const pages = answer.pages;
			let list = [];
			for (let i = 0; i < pages.length; i++) {
				list.push({ title: pages[i].title });
				for (let a = 0; a < pages[i].content.length; a++) {
					list.push({
						title: pages[i].content[a].title,
						description: pages[i].content[a].description,
						type: 'val'
					});
				}
			}
			let item = {
				isMy: false,
				type: 'edit',
				title: answer.title,
				list: list
			};
			this.list = {
				pages: list,
				title: title
			};
			this.msgList.push(item);
			this.dialogVisible = false;
			this.goBtm();
			this.loading = false;
		},
		createPPTLargeSteel() {
			this.msgList.push({ isMy: false, type: 'mod' });
			if (this.isHandleAdd) {
				this.dialogVisible = false;
				this.handleAdd();
				return;
			}
			let params = {
				chatObjectId: '',
				chatType: this.chatType || 0,
				clientType: '',
				createBy: '',
				dialogChatId: '',
				fileDialogId: '',
				functionId: '',
				knowledgeBaseName: '',
				metadataIds: [],
				query: this.query,
				robotDataContent: '',
				sessionId: '',
				system: '',
				tenantId: 1114
			};
			if (this.fileQuery != null) {
				this.fileSend();
				return;
			}
			this.send(params);
		},
		fileSend() {
			this.loading = true;
			uploadPPTLargeSteel({ file: this.fileQuery.file, query: this.query }, process => {
				// 根据定位的文件所在索引位置，赋值进度条
				this.fileQuery.process =
					((process.loaded / process.total) * 100).toFixed(2) > 99
						? 99
						: ((process.loaded / process.total) * 100).toFixed(2);
			}).then(res => {
				if (res.code == 200) {
					if (!this.sessionId) {
						this.$emit('getChatRecords');
					}
					this.loading = false;
					let list = [];
					const pages = res.result.pages;
					for (let i = 0; i < pages.length; i++) {
						list.push({ title: pages[i].title });
						for (let a = 0; a < pages[i].content.length; a++) {
							list.push({
								title: pages[i].content[a].title,
								description: pages[i].content[a].description,
								type: 'val'
							});
						}
					}
					let item = {
						isMy: false,
						type: 'edit',
						title: res.result.title,
						list: list
					};
					this.list = {
						pages: list,
						title: res.result.title
					};
					this.msgList.push(item);
					this.dialogVisible = false;
					this.goBtm();
				} else {
					this.processStatus = '#ff0000';
					this.$message.error(res.message);
				}
			});
		},
		send(params) {
			this.loading = true;
			createPPTLargeSteel(params).then(res => {
				if (res.code == 200) {
					if (!this.sessionId) {
						this.$emit('getChatRecords');
					}
					this.loading = false;
					let list = [];
					const pages = res.result.pages;
					for (let i = 0; i < pages.length; i++) {
						list.push({ title: pages[i].title });
						for (let a = 0; a < pages[i].content.length; a++) {
							list.push({
								title: pages[i].content[a].title,
								description: pages[i].content[a].description,
								type: 'val'
							});
						}
					}
					let item = {
						isMy: false,
						type: 'edit',
						title: res.result.title,
						list: list
					};
					this.list = {
						pages: list,
						title: res.result.title
					};
					this.msgList.push(item);
					this.dialogVisible = false;
					this.goBtm();
				} else {
					this.$message.error(res.message);
				}
			});
		},
		templateGetList() {
			templateGetList().then(res => {
				if (res.code === 200) {
					if (res.result && res.result.length) {
						console.log(this.modList, 'this.modListthis.modList');
						this.modList = res.result.map(t => {
							t.imgList = t.exampleImagePath.split(',') || [];
							return t;
						});
						this.dialogVisible = true;
						this.loading = false;
						return;
					}
					this.msgList.push({
						isMy: false,
						type: 'text',
						content: '暂无配置模板、请联系管理员配置模板后再试'
					});
					this.loading = false;
				} else {
					this.loading = false;
					this.$message.error(res.message);
				}
			});
		},
		goBtm() {
			this.$nextTick(() => {
				var container = document.querySelector('.msg-list');
				container.scrollTop = container.scrollHeight;
			});
		},
		handleMod(i) {
			if (this.modIndex != i) {
				this.modIndex = i;
			}
		},
		handleMin(i, index) {
			this.msgList[i].list.splice(index, 1);
		},
		addTitle(i, index, t) {
			let item = { title: '', type: t == 'val' ? 'val' : 'title' };
			this.msgList[i].list.splice(index + 1, 0, item);
			this.$forceUpdate();
		},
		handleSend() {
			if (this.msgInput) {
				this.msgList.push({
					isMy: true,
					type: 'text',
					content: JSON.parse(JSON.stringify(this.msgInput))
				});
				this.query = JSON.parse(JSON.stringify(this.msgInput));
				this.$forceUpdate();
				this.goBtm();
				this.msgInput = '';
				this.templateGetList();
				// this.dialogVisible = true
			} else {
				this.$message({
					message: '不能发送空白消息哦~',
					type: 'warning'
				});
			}
		},
		handleSelect(i) {
			if (this.select != i) {
				this.select = i;
			}
		}
	}
};
</script>

<style lang="scss" scoped>
.ppt {
	width: 100%;
	height: 100%;
	background: #f1f4f8;
	.head {
		height: 54px;
		padding: 0 20px 0 18px;
		display: flex;
		align-items: center;
		border-top: 1px solid #f0f0f0;
		border-bottom: 1px solid #dce3e7;
		font-size: 18px;
		font-weight: 800;
		color: $primaryTextColor;
		line-height: 24px;
		align-items: center;
		img {
			width: 41px;
			height: 30px;
			border-radius: 6px;
			margin-right: 7px;
		}
	}
	.content {
		height: calc(100% - 240px);
		overflow: hideen;
		.msg-box {
			height: 100%;
		}
		.ai-msg {
			display: flex;
			width: 700px;
			margin: 24px 0;
			padding: 12px 20px;
			box-sizing: border-box;
			img {
				width: 40px;
				height: 40px;
				margin-right: 10px;
			}
			.mod-msg {
				width: 654px;
				margin-left: auto;
				background: #ffffff;
				border-radius: 0px 6px 6px 6px;
				padding: 12px;
				box-sizing: border-box;
				.tit {
					font-weight: 400;
					font-size: 14px;
					color: #2f446b;
					line-height: 22px;
					text-align: left;
					font-style: normal;
					text-transform: none;
				}
				.select {
					margin-top: 12px;
					display: flex;
					.list {
						width: 163px;
						height: 66px;
						background: #eef3f8;
						border-radius: 6px 6px 6px 6px;
						padding: 9px;
						box-sizing: border-box;
						.top {
							i {
								font-size: 16px;
							}
							span {
								font-weight: 600;
								font-size: 16px;
								color: #515b6e;
								line-height: 22px;
								text-align: center;
								font-style: normal;
								text-transform: none;
								margin-left: 6px;
							}
						}
						.btm {
							font-weight: 400;
							font-size: 14px;
							color: #96a0b4;
							line-height: 22px;
							text-align: center;
							font-style: normal;
							text-transform: none;
							margin-top: 4px;
						}
					}
					.list:last-child {
						margin-left: 12px;
					}
					.act {
						border: 1px solid var(--brand-6);
						background: var(--brand-1);
					}
				}
				.edit {
					width: 518px;
					padding: 12px;
				}
			}
		}
		.msg-list {
			height: calc(100% - 172px);
			overflow: auto;
			padding: 12px 20px;
			box-sizing: border-box;
			.list {
				width: 100%;
				margin-bottom: 24px;
				.my {
					margin-left: auto;
					width: 700px;
					display: flex;
					justify-content: flex-end;
					img {
						width: 40px;
						height: 40px;
						margin-left: 10px;
					}
					.tit {
						margin-left: auto;
						max-width: calc(100% - 46px);
						padding: 12px;
						background: #fff;
						font-family: PingFang SC, PingFang SC;
						font-weight: 400;
						font-size: 14px;
						color: #000000;
						line-height: 22px;
						text-align: left;
						font-style: normal;
						text-transform: none;
						border-radius: 6px 0px 6px 6px;
					}
				}
				.your {
					width: 700px;
					display: flex;
					margin-bottom: 24px;
					img {
						width: 40px;
						height: 40px;
						margin-right: 10px;
					}
					.tit {
						max-width: calc(100% - 46px);
						padding: 12px;
						background: #fff;
						font-family: PingFang SC, PingFang SC;
						font-weight: 400;
						font-size: 14px;
						color: #000000;
						line-height: 22px;
						text-align: right;
						font-style: normal;
						text-transform: none;
						border-radius: 6px 0px 6px 6px;
					}
					.file {
						display: flex;
						padding: 12px;
						max-width: calc(100% - 46px);
						background: #fff;
						align-items: center;
						border-radius: 6px 0px 6px 6px;
						.img {
							font-size: 20px;
							width: 31px;
						}
						.right {
							.name {
								font-weight: 500;
								font-size: 14px;
								color: #2f446b;
								line-height: 22px;
								text-align: left;
								font-style: normal;
								text-transform: none;
							}
							.size {
								font-family: PingFang SC, PingFang SC;
								font-weight: 400;
								font-size: 14px;
								color: #737a94;
								line-height: 22px;
								text-align: left;
								font-style: normal;
								text-transform: none;
							}
						}
					}
					.select {
						max-width: calc(100% - 46px);
						.msg-content {
							border-radius: 6px 0px 6px 6px;
							padding: 12px;
							background: #fff;
							max-width: 100%;
							.tit {
								font-family: PingFang SC, PingFang SC;
								font-weight: 400;
								font-size: 14px;
								color: #2f446b;
								line-height: 22px;
								text-align: left;
								font-style: normal;
								text-transform: none;
								white-space: nowrap;
							}
							.btn-box {
								display: flex;
								.btn {
									width: 163px;
									height: 32px;
									background: #eef3f8;
									border-radius: 6px 6px 6px 6px;
									font-weight: 400;
									font-size: 14px;
									color: #515b6e;
									line-height: 32px;
									text-align: center;
									font-style: normal;
									text-transform: none;
								}
								.btn:last-child {
									margin-left: 12px;
								}
							}
						}
						.tips {
							margin-top: 19px;
							font-family: PingFang SC, PingFang SC;
							font-weight: 400;
							font-size: 14px;
							color: var(--brand-6);
							line-height: 22px;
							text-align: left;
							font-style: normal;
							text-transform: none;
						}
					}
					.edit {
						width: 518px;
						padding: 12px;
						background: #fff;
						.text {
							font-family: PingFang SC, PingFang SC;
							font-weight: 400;
							font-size: 14px;
							color: #2f446b;
							line-height: 22px;
							text-align: left;
							font-style: normal;
							text-transform: none;
							margin-bottom: 12px;
						}
						.title-inp {
							height: 32px;
							border-radius: 6px 6px 6px 6px;
							margin-bottom: 8px;
							::v-deep .el-input__inner {
								border: none;
								font-weight: 600;
								font-size: 16px;
								color: $primaryTextColor;
								border-radius: 6px 6px 6px 6px;
							}
							::v-deep .el-input__inner:hover {
								box-shadow: none;
								border: 1px solid;
								border-radius: 6px 6px 6px 6px;
								border-image: linear-gradient(
										90deg,
										rgba(213, 227, 255, 1),
										rgba(232, 244, 255, 1),
										rgba(229, 243, 255, 1)
									)
									1 1;
							}
						}
						.list-title {
							.inp-box {
								height: 32px;
								border-radius: 6px 6px 6px 6px;
								margin-bottom: 8px;
								position: relative;
								.btn-list {
									position: absolute;
									top: 6px;
									right: 7px;
									.btn {
										font-size: 16px;
										width: 19px;
										height: 19px;
										text-align: center;
										line-height: 19px;
									}
									.btn:hover {
										width: 19px;
										height: 19px;
										background: #e9f4ff;
										border-radius: 3px 3px 3px 3px;
									}
								}
								.icon {
									z-index: 10;
									position: absolute;
									left: 12px;
									top: 5px;
									font-weight: 400;
									font-size: 14px;
									color: #737a94;
									line-height: 22px;
									text-align: left;
									font-style: normal;
									text-transform: none;
								}
								::v-deep .el-input__inner {
									padding-left: 19px;
									padding-right: 60px;
									border: none;
									font-family: PingFang SC, PingFang SC;
									font-weight: 400;
									font-size: 14px;
									color: #2f446b;
									line-height: 22px;
									text-align: left;
									font-style: normal;
									text-transform: none;
									border-radius: 6px 6px 6px 6px;
								}
								::v-deep .el-input__inner:hover {
									box-shadow: none;
									border: 1px solid;
									border-radius: 6px 6px 6px 6px;
									border-image: linear-gradient(
											90deg,
											rgba(213, 227, 255, 1),
											rgba(232, 244, 255, 1),
											rgba(229, 243, 255, 1)
										)
										1 1;
								}
							}
						}
						.list-val {
							.inp-box {
								height: 32px;
								border-radius: 6px 6px 6px 6px;
								margin-bottom: 8px;
								.icon {
									left: 24px;
								}
								::v-deep .el-input__inner {
									padding-left: 31px;
									border: none;
									font-family: PingFang SC, PingFang SC;
									font-weight: 400;
									font-size: 14px;
									color: #2f446b;
									line-height: 22px;
									text-align: left;
									font-style: normal;
									text-transform: none;
									border-radius: 6px 6px 6px 6px;
								}
								::v-deep .el-input__inner:hover {
									box-shadow: none;
									border: 1px solid;
									border-radius: 6px 6px 6px 6px;
									border-image: linear-gradient(
											90deg,
											rgba(213, 227, 255, 1),
											rgba(232, 244, 255, 1),
											rgba(229, 243, 255, 1)
										)
										1 1;
								}
							}
						}
						.edit-foot {
							margin: 0 15px;
							border-top: 1px solid #e3ebf2;
							.tips {
								font-weight: 400;
								font-size: 13px;
								color: #2f446b;
								line-height: 22px;
								text-align: left;
								font-style: normal;
								margin: 12px 0;
								i {
									font-size: 13px;
									color: #40c274;
									margin-left: 8px;
								}
							}
							.btn {
								width: 100%;
							}
						}
					}
					.mod {
						max-width: calc(100% - 46px);
						.msg-content {
							border-radius: 6px 0px 6px 6px;
							padding: 12px;
							background: #fff;
							max-width: 100%;
							.tips {
								font-family: PingFang SC, PingFang SC;
								font-weight: 400;
								font-size: 14px;
								color: #2f446b;
								line-height: 22px;
								text-align: left;
								font-style: normal;
								text-transform: none;
							}
							img {
								width: 160px;
								height: 90px;
								border-radius: 6px 6px 6px 6px;
								border: 1px solid #e3ebf2;
							}
						}
						.foot-tips {
							margin-top: 19px;
							font-family: PingFang SC, PingFang SC;
							font-weight: 400;
							font-size: 14px;
							color: var(--brand-6);
							line-height: 22px;
							text-align: left;
							font-style: normal;
							text-transform: none;
						}
					}
				}
			}
		}
	}
	.foot {
		height: 130px;
		margin: 0 5px;
		margin-top: 25px;
		background: linear-gradient(var(--brand-1) 0%, #f3f7ff 100%);
		box-shadow: inset 0px 4px 4px 0px rgba(255, 255, 255, 0.25);
		border-radius: 16px 16px 16px 16px;
		border: 2px solid #ffffff;
		padding: 0 13px 16px;
		box-sizing: border-box;
		.top {
			display: flex;
			align-items: center;
			padding-left: 8px;
			height: 36px;
			.list-box {
				width: 800px;
				display: flex;
				.list {
					padding: 6px 19px 6px 12px;
					background: #ffffff;
					border-radius: 9px 9px 9px 9px;
					font-family: PingFang SC, PingFang SC;
					font-weight: 400;
					font-size: 14px;
					color: $primaryTextColor;
					line-height: 24px;
					text-align: left;
					font-style: normal;
					text-transform: none;
				}
			}
			.rf {
				margin-left: auto;
				width: 18px;
			}
		}
		.input-box {
			height: 64px;
			background: #ffffff;
			border-radius: 12px 12px 12px 12px;
			border: 1px solid var(--brand-6);
			padding: 18px 16px 14px 27px;
			box-sizing: border-box;
			display: flex;
			::v-deep .el-textarea {
				overflow: auto;
				textarea {
					border: none;
					padding: 0;
					white-space: pre-wrap;
				}
				.el-textarea__inner:focus {
					box-shadow: none;
				}
			}
		}
	}
}
.dialog {
	box-shadow: 0px 8px 10px -5px rgba(0, 0, 0, 0.08), 0px 16px 24px 2px rgba(0, 0, 0, 0.04),
		0px 6px 30px 5px rgba(0, 0, 0, 0.05);
	border-radius: 16px 16px 16px 16px;
	.dia-box {
		display: flex;
		padding: 0 14px;
		.left {
			width: 552px;
			img {
				width: 552px;
				height: 310px;
				border-radius: 12px 12px 12px 12px;
				border: 1px solid #e3ebf2;
			}
			.img-list {
				margin-top: 12px;
				img {
					width: 176px;
					height: 96px;
					border-radius: 9px 9px 9px 9px;
					border: 1px solid #e3ebf2;
				}
				img:nth-child(2) {
					margin: 0 12px;
				}
			}
		}
		.right {
			width: calc(100% - 572px);
			margin-left: auto;
			.title {
				font-family: PingFang SC, PingFang SC;
				font-weight: 600;
				font-size: 20px;
				color: $primaryTextColor;
				line-height: 28px;
				text-align: left;
				font-style: normal;
				text-transform: none;
			}
			.tit {
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 14px;
				color: $subTextColor;
				line-height: 22px;
				text-align: left;
				font-style: normal;
				text-transform: none;
			}
			.mid-content {
				height: 302px;
				margin-top: 20px;
				display: flex;
				flex-wrap: wrap;
				justify-content: space-between;
				overflow: auto;
				.list {
					width: 180px;
					height: 106px;
					position: relative;
					margin-bottom: 16px;
					img {
						width: 100%;
						height: 100%;
						border-radius: 6px 6px 6px 6px;
						border: 1px solid #e3ebf2;
					}
					.tag {
						width: 28px;
						height: 17px;
						background: #40c274;
						border-radius: 0px 6px 0px 6px;
						position: absolute;
						top: 0;
						right: 0;
						font-family: PingFang SC, PingFang SC;
						font-weight: 400;
						font-size: 10px;
						color: #ffffff;
						line-height: 17px;
						text-align: center;
						font-style: normal;
						text-transform: none;
						z-index: 3;
					}
					.mask {
						width: 100%;
						height: 100%;
						position: absolute;
						top: 0;
						left: 0;
						z-index: 1;
						background: rgba(0, 0, 0, 0.35);
						border-radius: 6px 6px 6px 6px;
					}
					.icon {
						width: 29px;
						height: 29px;
						position: absolute;
						left: calc(50% - 14.5px);
						top: calc(50% - 14.5px);
						z-index: 2;
						border: none;
					}
				}
			}
			.right-foot {
				padding-top: 14px;
				.btn {
					width: 100%;
				}
			}
		}
	}
}
.avatar {
	border-radius: 6px;
}
.file-tip {
	width: 100%;
	@include flexBox(space-between);

	.file-tip-left {
		flex: 1;
		overflow: hidden;
		@include flexBox(flex-start);

		.file-tip-image {
			width: 18px;
			height: 18px;
			flex-shrink: 0;
			margin-right: 8px;
		}

		.file-tip-text {
			font-size: 14px;
			flex-shrink: 0;
		}

		.file-tip-name {
			max-width: 400px;
			font-size: 14px;
			@include aLineEllipse;
			color: var(--brand-6);
		}
	}

	.file-tip-btn {
		cursor: pointer;
		margin-left: 16px;
		color: var(--brand-6);
	}

	.file-tip-process {
		position: absolute;
		bottom: 0;
		width: 100%;
	}
}
</style>
