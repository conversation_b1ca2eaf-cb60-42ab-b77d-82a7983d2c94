/*
 * @Description: man.js
 * @Version: 1.0
 * @Autor: zhaodongming
 * @Date: 2022-06-09 17:08:26
 * @LastEditors: zhaodongming
 * @LastEditTime: 2023-07-06 16:33:18
 */
import Vue from 'vue';
import 'normalize.css/normalize.css'; // A modern alternative to CSS resets
import ElementUI from 'element-eoss';
import 'element-eoss/lib/theme-chalk/index.css';
import locale from 'element-eoss/lib/locale/lang/zh-CN'; // lang i18n
import App from './App';
import store from './store';
import router from './router';
import '@/icons'; // icon
import '@/permission'; // permission control
// import mdDirect from '@/directive/md';
import directive from '@/utils/directive'; // 自定义指令
import components from '@/utils/components';
import UmdComponents from '@/utils/reject-umd-components.js'; // 注册umd组件
import COOS_SDK from '@/utils/coos-sdk'; // 供子项目调用的coos_sdk以及项目通信载体_BUS也在里面
import '@/assets/font/font.css';
import vform from '../lib/vform/VFormDesigner.umd.min.js';
import '../lib/vform/VFormDesigner.css';
import * as L from 'leaflet';
import 'leaflet/dist/leaflet.css';
window.L = L;
import 'leaflet.chinatmsproviders';
import { serveUrl } from '@/config';

/**代码块高亮展示*/
import VueHighlightJS from 'vue-highlightjs';
import 'highlight.js/styles/atom-one-dark.css';
import 'vue-slider-component/theme/default.css';
Vue.use(VueHighlightJS);

// 注册业务组件库
import ElementComponent from '../lib/element-component/scic-element-components';
import '../lib/element-component/style.css';
Vue.use(ElementComponent, {
	serveUrl
});

// 富文本全局注册
import tinymce from '@/components/tinymce/index.vue';
Vue.component('tinymce', tinymce); /**markdown预览功能*/
import mardownPreview from '@/utils/mardown-preview';
Vue.use(mardownPreview);
/**引入无界微前端*/
// import Wujie from '@/utils/wujie';
// Vue.use(Wujie);
// 全局修改默认配置，按下ESC不能关闭弹窗
ElementUI.Dialog.props.closeOnPressEscape.default = false;
// 全局修改默认配置，点击空白处不能关闭弹窗
ElementUI.Dialog.props.closeOnClickModal.default = false;
Vue.use(ElementUI, { locale });
/**自定义指令*/
Vue.use(directive);
Vue.use(vform);
/**埋点指令*/
// Vue.use(mdDirect);
/**注册供子项目使用的SDK以及项目里面的通信载体_BUS*/
Vue.use(COOS_SDK);
/**注册全局自定义组件*/
Vue.use(components);
/**注册umd组件*/
Vue.use(UmdComponents);
// 如果想要中文版 element-eoss，按如下方式声明
// Vue.use(ElementUI)
Vue.config.productionTip = false;
import vue2WaterMarker from 'vue2-water-marker';
Vue.use(vue2WaterMarker);
import VueSignaturePad from 'vue-signature-pad';
Vue.use(VueSignaturePad);
// 不用babel插件去除打印，是因为IM的proto.min.js里面的打印不会被去除
if (process.env.VUE_APP_BUILD_CONFIG?.includes('clearConsole')) {
	window.console.log = () => {};
	window.console.warn = () => {};
	window.console.warning = () => {};
	console.clear();
}

// 野火im中main.js的配置
import { InitWileFire } from '@wile/main.js';

let { i18n, wileFireStore } = InitWileFire(Vue);

Vue.prototype.$allPathImg = function allPathImg(imgUrl = '') {
	if (imgUrl) {
		if (String(imgUrl).indexOf('http') == 0) {
			return imgUrl;
		} else {
			return serveUrl + imgUrl;
		}
	} else {
		return '';
	}
};

import globalMixins from '@/mixins/global-mixins';
Vue.mixin(globalMixins);
let vm = new Vue({
	el: '#app',
	i18n,
	router,
	store,
	render: h => h(App)
});
vm.store = wileFireStore.state;
window.vm = vm;
