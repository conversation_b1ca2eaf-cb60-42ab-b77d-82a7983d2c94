<template>
	<el-dialog
		append-to-body
		class="custom-el-dialog-footer"
		:class="{ noContent: radio === 2 }"
		top="10vh"
		:title="title"
		:close-on-click-modal="true"
		:close-on-press-escape="true"
		:show-close="true"
		:visible.sync="dialogShow"
		:before-close="beforeClose"
	>
		<div v-if="!disableAll" class="tabs">
			<el-radio-group v-model="radio" @change="changeIsAll">
				<el-radio :label="1">部分成员</el-radio>
				<el-radio :disabled="disableAll" :label="2">全部成员</el-radio>
			</el-radio-group>
		</div>
		<PersonnelSelect
			v-show="radio === 1"
			ref="orgPersonnelSelect"
			v-bind="$attrs"
			style="flex: 1; overflow: hidden"
			@changeValue="changeValue"
			@change="change"
		></PersonnelSelect>
		<span slot="footer" class="dialog-footer custom-el-button">
			<el-button @click="close">{{ caleButton }}</el-button>
			<el-button v-loading="sureLoading" type="primary" @click="sure">{{ sureButton }}</el-button>
		</span>
	</el-dialog>
</template>

<script>
import PersonnelSelect from '../personnel-select/index.vue';
export default {
	name: 'PersonnelDialog',
	components: {
		PersonnelSelect
	},
	props: {
		// 弹窗关闭是否重置表单数据
		isReset: {
			type: Boolean,
			default: () => {
				return true;
			}
		},
		// 确定按钮的文字
		sureButton: {
			type: String,
			default: () => {
				return '保 存';
			}
		},
		// 取消按钮的文字
		caleButton: {
			type: String,
			default: () => {
				return '取 消';
			}
		},
		// 可否禁用全部标签
		disableAll: {
			type: Boolean,
			default: () => {
				return false;
			}
		},
		// 显示隐藏
		visible: {
			type: Boolean,
			default: () => {
				return false;
			}
		},
		// 是否全部
		isAll: {
			type: Boolean,
			default: () => {
				return false;
			}
		},
		// 标题
		title: {
			type: String,
			default: '选择人员' // '应用可用范围管理'
		},
		// 确认按钮的状态
		sureLoading: {
			type: Boolean,
			default: () => {
				return false;
			}
		}
	},
	data() {
		return {
			radio: 1,
			dialogShow: false,
			selectArr: [], // 当前选择的数据
			valChange: false // value是否发生变化
		};
	},
	watch: {
		// 监听关闭弹窗就重置弹窗
		visible(newVal) {
			this.dialogShow = newVal;
		},
		dialogShow(newVal) {
			if (!newVal) {
				this.$emit('close');
				this.isReset && this.reset();
			} else {
				this.radio = this.isAll ? 2 : 1; //每次打开初始化
			}
		},
		isAll(newVal) {
			this.radio = newVal ? 2 : 1;
		}
	},
	mounted() {
		this.radio = this.isAll ? 2 : 1;
	},
	methods: {
		/**通知外层数据是否编辑过*/
		changeValue(bool) {
			this.valChange = bool;
		},
		changeIsAll() {
			this.$emit('changeRadio', this.radio);
		},
		/**关闭前*/
		beforeClose(done) {
			if (!this.valChange) {
				done();
				return;
			}
			this.$confirm('内容未保存，确定关闭吗？', '关闭提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			})
				.then(res => {
					this.valChange = false;
					done();
				})
				.catch(err => {
					// console.log('取消');
				});
		},
		/**确定*/
		sure() {
			if (this.sureLoading) return;
			this.valChange = false;
			this.$emit('sure', this.selectArr, this.radio === 2);
		},
		/**取消*/
		close() {
			this.beforeClose(() => {
				this.$emit('close');
			});
		},
		/**关闭弹窗重置数据*/
		reset() {
			this.$refs.orgPersonnelSelect.reset();
		},
		change(val) {
			this.valChange = true;
			this.selectArr = val; // 当前选择的数据
			this.$emit('change', val, this.radio === 2); // 2为全部
		}
	}
};
</script>

<style scoped lang="scss">
.tabs {
	height: 32px;
	padding: 0 4px;
	display: flex;
	justify-content: flex-start;
	align-items: center;
	margin-bottom: 16px;
	flex-shrink: 0;
}
.custom-el-dialog-footer.noContent {
	::v-deep .el-dialog {
		height: auto;
		min-height: auto;
	}
	::v-deep .el-dialog__body {
		height: 58px;
	}
}
// 按钮
.custom-el-button {
	::v-deep .el-button {
		width: 68px;
		height: 36px;
		border-radius: 6px;
		font-size: 14px;
		font-weight: 400;
		line-height: 22px;
	}
	::v-deep .el-button.is-disabled {
		background: #bae7ff;
	}
	::v-deep .el-button--default {
		color: #2f446b;
	}
	::v-deep .el-button--primary {
		background: var(--brand-6, #0f45ea);
		color: rgba(255, 255, 255, 0.9);
	}
}
.custom-el-dialog-footer {
	::v-deep .el-dialog {
		min-width: 750px;
		min-height: 600px;
		height: 80%;
		max-height: 80%;
		width: 70%;
		margin-top: 10vh !important;
		box-shadow: 0px 8px 10px -5px rgba(0, 0, 0, 0.08), 0px 16px 24px 2px rgba(0, 0, 0, 0.04),
			0px 6px 30px 5px rgba(0, 0, 0, 0.05);
		border-radius: 16px;
		margin-bottom: 0;
	}
	::v-deep .el-dialog__header {
		padding: 27px 30px 26px;
		.el-dialog__headerbtn {
			top: 30px;
			right: 31px;
		}
		.el-dialog__title {
			font-size: 18px;
			font-weight: 800;
			color: #303133;
			line-height: 24px;
		}
	}
	::v-deep .el-dialog__body {
		height: calc(100% - 77px - 80px);
		padding: 0 32px 16px;
		display: flex;
		flex-direction: column;
		overflow-y: auto;
		&::-webkit-scrollbar-track-piece {
			background: #f0f7fc;
		}

		&::-webkit-scrollbar {
			width: 6px;
		}

		&::-webkit-scrollbar-thumb {
			background: #dde9f4;
			border-radius: 7px;
		}
	}
	::v-deep .el-dialog__footer {
		padding: 24px 30px 20px;
	}
}
</style>
