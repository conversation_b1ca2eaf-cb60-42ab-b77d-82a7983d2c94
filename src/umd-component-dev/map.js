const UMD_JSON = require('./index.json');
const UMD_DESC = {
	PersonnelSelect: '人员选择',
	PersonnelDialog: '人员选择弹窗'
};
const CHOICES_LIST = Object.keys(UMD_JSON).map((key, index) => {
	return {
		name: `${UMD_DESC[key]}(${key})`,
		value: `{ "${key}": "${UMD_JSON[key]}" }`
	};
});
CHOICES_LIST.unshift({
	name: '全部',
	value: JSON.stringify(UMD_JSON)
}); // 默认全部
module.exports = {
	type: 'list',
	name: 'type',
	message: '请选择打包的组件',
	choices: CHOICES_LIST
};
