<template>
	<div
		v-infinite-scroll="scrollBottom"
		class="post"
		infinite-scroll-distance="20"
		:infinite-scroll-disabled="listLoading || lock"
	>
		<el-tree
			ref="ElTree"
			v-loading="loading"
			class="custom-el-three"
			:data="list"
			show-checkbox
			node-key="id"
			:props="defaultProps"
			:filter-node-method="filterNode"
			@check="check"
		></el-tree>
		<div class="line"></div>
		<el-checkbox-group v-model="checkedArr" class="custom-el-checkbox" @change="changeSelect">
			<el-checkbox v-for="item of pageList" :key="item.id" :label="item.id">
				<div class="title">{{ item.postName }}</div>
			</el-checkbox>
		</el-checkbox-group>
		<div v-show="listLoading" class="loading-text">加载中...</div>
		<div v-show="lock" class="loading-text">没有更多了</div>
	</div>
</template>

<script>
import { getPostList, getPostTree } from '@/api/modules/component';
import { debounce } from '@/utils';

export default {
	name: 'PostSelect',
	data() {
		return {
			checkedArr: [], // 岗位分页数据选中集合
			list: [], // 岗位树
			defaultProps: {
				children: 'children',
				label: 'label'
			},
			loading: false, // 树结构的加载效果
			lock: false, // 分页列表的锁
			pageListParams: {
				pageNo: 1,
				pageSize: 10,
				status: 1,
				postName: '' // 搜索词，岗位名称
			}, // 岗位列表请求参数
			pageList: [], // 岗位列表数据
			filterIds: [], // 已经选择的数据id，便于清除
			lazyInit: false, // 延迟初始化
			initData: [],
			listLoading: false // 加载效果
		};
	},
	watch: {
		// 树数据加载的时候执行初始化
		list: {
			deep: true,
			handler: function (newVal) {
				if (this.lazyInit) {
					this.$nextTick(() => {
						this.init(this.initData);
						this.lazyInit = false;
					});
				}
			}
		}
	},
	mounted() {
		this.getData();
		this.getList();
	},
	methods: {
		/**初始化回显*/
		init(data) {
			// 如果数据还未加载，延迟初始化
			if (this.list.length === 0) {
				this.initData = data;
				this.lazyInit = true;
				return;
			}
			this.checkedArr = this.filterIds = data;
			this.filterIds.forEach(eId => {
				this.$refs.ElTree.setChecked(eId, true, true);
			});
		},
		/**外部搜索*/
		search(val) {
			// 树形搜索
			this.loading = true;
			this.$refs.ElTree.filter(val);
			setTimeout(() => {
				this.loading = false;
			}, 500);

			// 列表搜索
			this.lock = false;
			this.pageListParams.pageNo = 1;
			this.pageListParams.postName = val;
			this.pageList = [];
			this.getList();
		},
		/**选择集合发生变化*/
		changeSelect(val) {
			let obj = {
				checkedNodes: []
			};
			this.pageList.forEach(item => {
				if (val.includes(item.id)) {
					obj.checkedNodes.push(item);
					this.$refs.ElTree.setChecked(item.id, true, true);
				} else {
					this.$refs.ElTree.setChecked(item.id, false, true);
				}
			});
			this.check(val, obj);
		},
		/**触底*/
		scrollBottom: debounce(
			function () {
				this.pageListParams.pageNo += 1;
				this.getList();
			},
			500,
			true
		),
		/**获取岗位列表数据*/
		getList() {
			if (this.lock) return;
			this.listLoading = true;
			getPostList(this.pageListParams).then(res => {
				this.listLoading = false;
				if (res.code === 200) {
					this.pageList = this.pageList.concat(res.result.records);
					if (res.result.pages <= this.pageListParams.pageNo) {
						this.lock = true;
					}
				} else {
					this.$message.error(res.message);
				}
			});
		},
		/**获取岗位树形数据*/
		getData() {
			getPostTree().then(res => {
				if (res.code === 200) {
					this.list = res.result || [];
				} else {
					this.$message.error(res.message);
				}
			});
		},
		/**搜索筛选的方法*/
		filterNode(val, data) {
			if (!val) return true;
			return data.label.indexOf(val) > -1;
		},
		/**节点选中状态发生了变化*/
		check(arr, obj) {
			// 数据重复可被去重
			let filterArr = new Map();
			this.filterIds = [];
			// 有父节点被选中的，不展示子节点
			obj.checkedNodes.forEach(item => {
				if (item.dataType === 'post') {
					filterArr.set(item.id, item);
					this.filterIds.push(item.id);
				}
			});
			this.checkedArr = this.filterIds;
			this.$emit('changeSelect', this.filterIds, filterArr, 'post');
		},
		/**移除，是否全部清除，默认false*/
		remove(isClear, id) {
			if (isClear) {
				this.filterIds.forEach(eId => {
					this.$refs.ElTree.setChecked(eId, false, true);
				});
				this.checkedArr = [];
			} else {
				this.$refs.ElTree.setChecked(id, false, true);
				let index = this.checkedArr.indexOf(id);
				if (index > -1) {
					this.checkedArr.splice(index, 1);
				}
			}
		}
	}
};
</script>

<style scoped lang="scss">
@mixin noScrollBar {
	&::-webkit-scrollbar {
		width: 0;
	}
}
.post {
	height: 100%;
	overflow: auto;
	@include noScrollBar;
}
.custom-el-three {
	&::-webkit-scrollbar-track-piece {
		background: #d3dce6;
	}

	@include noScrollBar;

	&::-webkit-scrollbar-thumb {
		background: #99a9bf;
		border-radius: 20px;
	}
}
.custom-el-three {
	::v-deep .el-tree-node__expand-icon {
		color: #737a94;
	}
	::v-deep .el-tree-node__expand-icon.is-leaf {
		color: transparent !important;
	}
	::v-deep .el-tree-node__label {
		font-size: 14px;
		font-weight: 400;
		color: #2f446b;
		line-height: 22px;
	}
}
.custom-el-checkbox {
	::v-deep .el-checkbox {
		height: 28px !important;
	}
	.title {
		flex: 1;
		overflow: hidden;
		white-space: nowrap;
		text-overflow: ellipsis;
	}
}
.loading-text {
	height: 30px;
	line-height: 30px;
	color: #c0c0c0;
	font-size: 14px;
	text-align: center;
}
.line {
	height: 1px;
	background: #f2f2f2;
	margin: 10px 0;
}
</style>
