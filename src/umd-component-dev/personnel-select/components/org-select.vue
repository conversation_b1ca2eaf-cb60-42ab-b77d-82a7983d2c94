<template>
	<div class="orgSelect">
		<div class="orgSelect-title">
			<el-checkbox
				v-if="disableOrg"
				v-model="checkAllStatus"
				:indeterminate="indeterminate"
				class="custom-el-checkbox-item"
				:disabled="isRadio"
				@change="checkAll"
			>
				全选
			</el-checkbox>
			<el-tooltip
				v-if="orgArr.length"
				:open-delay="1000"
				effect="dark"
				:content="orgArr[currentOrgName]"
				placement="top"
			>
				<el-select
					v-if="hasDepart"
					v-model="currentOrgName"
					class="custom-el-select"
					:class="{ 'disable-org-input': !disableOrg }"
					placeholder="请选择组织"
					@change="changeOrg"
				>
					<el-option
						v-for="(item, index) in orgArr"
						:key="index"
						:label="item"
						:value="index"
					></el-option>
				</el-select>
			</el-tooltip>
		</div>
		<div
			v-if="navbar.length"
			class="navbar"
			:style="{
				padding: showOrgCheck ? 'padding: 10px 12px;' : '10px 0'
			}"
		>
			<el-checkbox
				v-if="showOrgCheck"
				v-model="checkCurrentOrg"
				class="custom-el-checkbox-item"
				@change="checkOrg"
			></el-checkbox>
			<div
				v-for="(item, index) of navbar"
				:key="index"
				class="navbar-item"
				:style="{ color: index === navbar.length - 1 ? '#2f446b' : '#737a94' }"
			>
				<img v-if="orgIcon && index !== 0" class="navbar-item-icon" :src="orgIcon" alt="" />
				<div v-else-if="item !== '搜索结果'" class="empty-icon">{{ item.slice(0, 1) }}</div>
				<div
					class="navbar-item-text"
					:class="{ over: item !== '搜索结果' && !showOrgCheck }"
					@click="changeTreeData(item)"
				>
					{{ item === '搜索结果' ? `包含“${orgKeyword}”的搜索结果` : item }}
				</div>
				<div v-if="index !== navbar.length - 1" style="margin: 0 6px">></div>
			</div>
		</div>
		<el-checkbox-group
			v-model="checkedArr"
			v-infinite-scroll="scrollBottom"
			infinite-scroll-distance="20"
			:infinite-scroll-disabled="disabled"
			class="custom-el-checkbox"
			@change="changeSelect"
		>
			<el-checkbox
				v-for="item of list.values()"
				:key="item.id"
				:label="item.id"
				:disabled="
					(disableOrg && item.dataType === 'org') ||
					checkedArr.includes(item.pid) ||
					(!canSelectDepart && item.dataType === 'depart') ||
					disabledIds.includes(item.id)
				"
			>
				<!--<div v-if="item.dataType === 'depart'" class="depart-icon">
          <i class="coos-iconfont icon-zuzhijiagou"></i>
        </div>
        <svg-icon v-else class="depart-icon" icon-class="default-avatar"></svg-icon>
        <div v-else class="icon">{{ item.title.slice(-1) }}</div>-->

				<img
					v-if="!item.error && (item.logo || item.avatarUrl)"
					class="depart-icon depart-icon-img"
					:src="item.logo || item.avatarUrl"
					alt=""
					@error="avatarError(item)"
				/>
				<div v-else-if="item.dataType === 'depart'" class="depart-icon">
					<i class="coos-iconfont icon-zuzhijiagou"></i>
				</div>
				<svg-icon
					v-else-if="item.dataType === 'org'"
					icon-class="org-logo"
					class="icon"
					style="background: #ffffff"
				></svg-icon>
				<div v-else class="icon">{{ item.title.slice(-1) }}</div>
				<div class="title title-box">
					<div
						class="title-box-title"
						:style="item.dataType !== 'org' && item.dataType !== 'depart' ? 'width:130px' : ''"
					>
						{{ item.title }}
					</div>
					<el-tooltip class="item" effect="dark" :content="setDec(item)" placement="top-start">
						<div v-if="item.dataType !== 'org' && item.dataType !== 'depart'" class="dec">
							{{ setDec(item) }}
						</div>
					</el-tooltip>
				</div>
				<div
					v-if="item.children && item.children.length > 0"
					class="more"
					:class="noShowMore(item) ? 'disabled' : ''"
					@click.stop.prevent="!noShowMore(item) && openChildren(item)"
				>
					展开
					<i
						:class="noShowMore(item) ? 'disabled' : ''"
						class="coos-iconfont icon-nav-bottom more-icon"
					></i>
				</div>
			</el-checkbox>
			<div v-show="listLoading && navbar.length === 1" class="loading-text">加载中...</div>
			<div v-show="noMore && navbar.length === 1" class="loading-text">没有更多了</div>
		</el-checkbox-group>
	</div>
</template>

<script>
export default {
	name: 'OrgSelect',
	props: {
		/**默认选中的组织名称*/
		defaultOegName: {
			type: String,
			default: () => {
				return '';
			}
		},
		// 全部数据
		listMap: {
			type: Map,
			default: () => {
				return new Map();
			}
		},
		// 当前树展示的数据
		list: {
			type: Map,
			default: () => {
				return new Map();
			}
		},
		// 面包屑
		navbar: {
			type: Array,
			default: () => {
				return [];
			}
		},
		// 组织的图标
		orgIcon: {
			type: String,
			default: () => {
				return '';
			}
		},
		// 组织选项
		orgArr: {
			type: Array,
			default: () => {
				return [];
			}
		},
		listLoading: {
			type: Boolean,
			default: () => {
				return false;
			}
		},
		noMore: {
			type: Boolean,
			default: () => {
				return false;
			}
		},
		orgKeyword: {
			type: String,
			default: () => {
				return '';
			}
		},
		// 是否含有部门
		hasDepart: {
			type: Boolean,
			default: () => {
				return true;
			}
		},
		// 可以选择部门，false的话只能选择部门下面的人员
		canSelectDepart: {
			type: Boolean,
			default: () => {
				return true;
			}
		},
		/**组织人员是否单选*/
		isRadio: {
			type: Boolean,
			default: () => {
				return false;
			}
		},
		/**禁止选中的id*/
		disabledIds: {
			type: Array,
			default: () => {
				return [];
			}
		},
		/**是否禁止选择组织*/
		disableOrg: {
			type: Boolean,
			default: () => {
				return true;
			}
		},
		/**面包屑第一个组织*/
		firstNavOrg: {
			type: Object,
			default: () => {
				return {};
			}
		}
	},
	data() {
		return {
			checkCurrentOrg: false, // 是否选中当前组织
			checkAllStatus: false,
			indeterminate: false,
			currentOrgName: 0, // 当前选中的组织
			checkedArr: [] // 选中的部门人员
		};
	},
	computed: {
		// 是否可以继续出发滚动触底
		disabled() {
			return this.listLoading || this.noMore;
		},
		showOrgCheck() {
			return !this.disableOrg && this.navbar.length === 1 && this.navbar[0] !== '搜索结果';
		}
	},
	watch: {
		// 值发生变化要重新计算
		checkedArr: {
			handler: function (newVal) {
				if (!this.disableOrg) {
					this.checkCurrentOrg = newVal.includes(this.firstNavOrg.id);
				}
				this.computedCheckAll();
			},
			deep: true
		},
		// 面包屑要重新计算
		navbar: {
			handler: function (newVal) {
				this.computedCheckAll();
			},
			deep: true
		},
		orgArr: {
			deep: true,
			handler: function (newVal) {
				let index = newVal.findIndex(item => {
					return item === this.defaultOegName;
				});
				this.currentOrgName = index > -1 ? index : 0;
			}
		},
		firstNavOrg: {
			deep: true,
			handler: function (newVal) {
				if (!this.disableOrg) {
					this.checkCurrentOrg = this.checkedArr.includes(newVal.id);
				}
			}
		}
	},
	methods: {
		setDec(event) {
			if (!event) return '';
			const getPrefixedValue = (value, prefix = '-') => (value ? `${prefix}${value}` : '');
			const org = event.orgShort || event.orgName || '';
			const depart = getPrefixedValue(event.departShort || event.departName);
			const postName = getPrefixedValue(event.postName);
			const jobTitle =
				getPrefixedValue(event.jobTitle, '(').replace(/^-/, '') + (event.jobTitle ? ')' : '');
			return `${org}${depart}${postName}${jobTitle}`;
		},
		avatarError(item) {
			item.error = true;
		},
		/**选中最外层组织*/
		checkOrg(val) {
			if (val) {
				this.checkedArr.push(this.firstNavOrg.id);
			} else {
				this.checkedArr = this.checkedArr.filter(item => {
					return item !== this.firstNavOrg.id;
				});
			}
			this.changeSelect(this.checkedArr);
		},
		/**改变组织选择*/
		changeOrg(index) {
			let title = this.orgArr[index];
			this.$emit('changeCurrentOrg', title);
		},
		/**左边滚动条触发事件*/
		scrollBottom() {
			this.$emit('scrollBottom');
		},
		/**计算是否全选*/
		computedCheckAll() {
			let listArr = Array.from(this.list.keys());
			// 面包屑或者选中值发生变化，计算当前树的数据是否全选中
			let newList = listArr.filter(item => {
				return this.checkedArr.includes(item);
			});
			// 筛选后的数据和树数据一致说明全选中
			if (newList.length === listArr.length) {
				this.checkAllStatus = true;
				this.indeterminate = false;
			} else if (newList.length === 0) {
				this.checkAllStatus = false;
				this.indeterminate = false;
			} else {
				this.checkAllStatus = false;
				this.indeterminate = true;
			}
		},
		/**是否显示更多*/
		noShowMore(value) {
			return this.checkedArr.includes(value.id);
		},
		/**选择集合发生变化*/
		changeSelect(val) {
			let filterArr = []; // 逻辑中父子关系不能同时存在，下面做筛选
			val.forEach(item => {
				// 根据id获取到对应的对象，取出父ID判断集合中是不是存在父亲，存在的话，自己就不加入到筛选后的集合
				let pid = this.listMap.get(item) ? this.listMap.get(item).pid : '';
				// 没有父元素就不存在筛选的问题
				if (!pid) {
					filterArr.push(item);
				}
				// 该元素的父id包括在集合中，就说明父集存在,父集合不存在才通过筛选
				else if (!val.includes(pid)) {
					filterArr.push(item);
				}
			});
			this.checkedArr = filterArr;
			if (this.isRadio && this.checkedArr.length > 1) {
				this.checkedArr = this.checkedArr.slice(-1);
			}
			this.$emit('changeSelect', this.checkedArr);
		},
		/**全选*/
		checkAll(val) {
			let listArr = Array.from(this.list.keys());
			// 筛选不是disabled的选项进行全选|全不选的操作
			let ids = listArr.filter(id => {
				let item = this.list.get(id);
				// 不能是组织
				// 父级选中，子集不可改变状态
				// 不能选择部门的话，部门也不可以改变
				// 外部指定的id不可操作项
				return (
					(!this.disableOrg || item.dataType !== 'org') &&
					(this.canSelectDepart || item.dataType !== 'depart') &&
					!this.disabledIds.includes(item.id)
				);
			});
			ids = ids.filter(id => {
				let item = this.list.get(id);
				return !ids.includes(item.pid);
			});
			if (val) {
				// 直接把全部选中的数据加入，用new Set去重，再解构为数组
				this.checkedArr = [...new Set(this.checkedArr.concat(ids))];
			} else {
				// 取消全选就是筛选出全部数据中包含的数据，保留其余数据
				this.checkedArr = this.checkedArr.filter(item => {
					return !ids.includes(item);
				});
			}
			this.$emit('changeSelect', this.checkedArr);
		},
		/**打开子集*/
		openChildren(value) {
			this.$emit('openChildren', value);
		},
		/**切换树数据*/
		changeTreeData(item) {
			this.$emit('changeTreeData', item);
		},
		/**移除，是否全部清除，默认false*/
		remove(isClear, index) {
			if (isClear) {
				this.checkedArr = [];
			} else {
				this.checkedArr.splice(index, 1);
			}
		},
		init(data) {
			this.checkedArr = data;
			if (!this.disableOrg && this.checkedArr.includes(this.firstNavOrg.id)) {
				this.checkCurrentOrg = true;
			}
		}
	}
};
</script>

<style scoped lang="scss">
$borderRadius: 6px; // 全局统一圆角
@mixin noScrollBar {
	&::-webkit-scrollbar {
		width: 0;
	}
}
.orgSelect {
	height: 100%;
	display: flex;
	flex-direction: column;
	&-title {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}
}
.custom-el-tabs {
	margin-top: 10px;
	padding-left: 8px;
	::v-deep .el-tabs__header.is-top {
		margin-bottom: 10px;
	}
}
.custom-el-checkbox {
	flex: 1;
	overflow-y: auto;
	@include noScrollBar;
	.icon {
		width: 32px;
		height: 32px;
		background: var(--brand-6, #0f45ea);
		border-radius: $borderRadius;
		font-size: 14px;
		font-weight: 500;
		color: #ffffff;
		line-height: 22px;
		margin-right: 10px;
		flex-shrink: 0;
		display: flex;
		justify-content: center;
		align-items: center;
	}
	.title {
		min-width: 160px;
		flex: 1;
		overflow: hidden;
		white-space: nowrap;
		text-overflow: ellipsis;
	}
	.more {
		font-size: 14px;
		font-family: PingFang SC, PingFang SC;
		font-weight: 500;
		color: var(--brand-6, #0f45ea);
		line-height: 32px;
		position: absolute;
		top: 0;
		right: 0;
		cursor: pointer;
		display: flex;
		justify-content: center;
		align-items: center;
		&-icon {
			font-size: 16px;
		}
	}
	.disabled {
		cursor: not-allowed;
		color: #c0c0c0;
	}
	.loading-text {
		height: 30px;
		line-height: 30px;
		color: #c0c0c0;
		font-size: 14px;
		text-align: center;
	}
	.depart-icon {
		width: 32px;
		height: 32px;
		background: #47d07e;
		border-radius: $borderRadius;
		font-size: 16px;
		color: #ffffff;
		margin-right: 8px;
		display: flex;
		justify-content: center;
		align-items: center;
	}
	.depart-icon-img {
		background: #ffffff;
	}
	::v-deep .el-checkbox__input.is-disabled {
		position: relative;
		&:hover {
			&:before {
				content: '跟随上级设置';
				padding: 12px 8px 6px;
				background: #000000;
				color: #ffffff;
				font-size: 9px;
				position: absolute;
				bottom: -34px;
				left: -10px;
				z-index: 666;
				-webkit-clip-path: polygon(20% 0, 25% 20%, 100% 20%, 100% 100%, 0 100%, 0% 20%, 15% 20%);
				clip-path: polygon(20% 0, 25% 20%, 100% 20%, 100% 100%, 0 100%, 0% 20%, 15% 20%);
			}
		}
	}
}
.navbar {
	font-size: 14px;
	font-weight: 400;
	color: #737a94;
	line-height: 16px;
	display: flex;
	justify-content: flex-start;
	align-items: center;
	position: relative;
	z-index: 9999;
	flex-wrap: wrap;
	&-item {
		display: flex;
		justify-content: flex-start;
		align-items: center;
		margin: 4px 0;
		&-icon {
			width: 24px;
			height: 24px;
			margin-right: 12px;
			border-radius: 4px;
			border: 1px solid #f0f0f0;
		}
		.empty-icon {
			width: 24px;
			height: 24px;
			background: var(--brand-6, #0f45ea);
			border-radius: 4px;
			font-size: 14px;
			font-weight: 500;
			color: #ffffff;
			margin-right: 12px;
			flex-shrink: 0;
			display: flex;
			justify-content: center;
			align-items: center;
		}
		&-text {
			cursor: pointer;
			flex-shrink: 0;
		}
		.over {
			max-width: 100px;
			overflow: hidden;
			white-space: nowrap;
			text-overflow: ellipsis;
		}
	}
}
.custom-el-checkbox-item {
	height: 38px;
	margin-bottom: 2px;
	font-size: 14px;
	font-weight: 500;
	color: #2f446b;
	line-height: 22px;
	padding: 0 8px;
	display: flex;
	align-items: center;
	justify-content: flex-start;
	::v-deep .el-checkbox__inner {
		height: 16px;
		width: 16px;
	}
	::v-deep .el-checkbox__label {
		padding-left: 12px;
	}
}
.custom-el-checkbox {
	::v-deep .el-checkbox {
		height: 48px;
		margin-bottom: 2px;
		margin-right: 0;
		font-size: 14px;
		font-weight: 500;
		color: #2f446b;
		line-height: 22px;
		padding: 0 8px;
		display: flex;
		align-items: center;
		justify-content: flex-start;
		//overflow: hidden;
	}
	::v-deep .el-checkbox__inner {
		height: 16px;
		width: 16px;
	}
	::v-deep .el-checkbox__label {
		font-size: 14px;
		font-weight: 400;
		color: #2f446b;
		line-height: 14px;
		padding-left: 8px;
		position: relative;
		width: 100%;
		padding-right: 60px;
		display: flex;
		align-items: center;
		justify-content: flex-start;
		flex: 1;
		overflow: hidden;
	}
}
.custom-el-select {
	margin-right: 6px;
	::v-deep .el-input__inner {
		width: 140px;
		height: 36px;
		background: #ffffff;
		border-radius: 6px;
		border: 1px solid #bccadb;
	}
	::v-deep .el-input__icon {
		display: flex;
		flex-direction: column;
		justify-content: center;
	}
}
.disable-org-input {
	width: 100%;
	::v-deep .el-input__inner {
		width: 100% !important;
	}
}
.dec {
	flex: 1;
	font-family: PingFang SC, PingFang SC;
	font-weight: 400;
	font-size: 12px;
	color: #737a94;
	line-height: 20px;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}
.title-box {
	display: flex;
	align-items: center;
}
</style>
