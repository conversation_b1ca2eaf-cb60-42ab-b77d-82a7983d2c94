<template>
	<div class="content-right">
		<div v-show="selectData.length" class="title">
			<div class="title-name">已选：</div>
			<div class="title-detail">
				<span v-show="orgCount">{{ orgCount }}个组织，</span>
				<span v-show="personCount">{{ personCount }}名成员，</span>
				<span v-show="labelCount">{{ labelCount }}个人员标签，</span>
				<span v-show="departCount">{{ departCount }}个部门，</span>
				<span v-show="postCount">{{ postCount }}个岗位，</span>
				<span v-show="identityCount">{{ identityCount }}个身份</span>
			</div>
			<div class="title-clear" @click="remove(true)">
				<svg-icon icon-class="delete" class="icon"></svg-icon>
				清空
			</div>
		</div>
		<div class="list">
			<div v-for="(item, index) of selectData" :key="index" class="list-item">
				<div v-if="item.dataType === 'label'" class="list-item-left">
					<span class="list-item-left-label">用户标签：{{ item.title }}</span>
				</div>
				<div v-else-if="item.dataType === 'post'" class="list-item-left">
					<span class="list-item-left-label">
						岗位：{{ item.label || item.postName || item.title }}
					</span>
				</div>
				<div v-else-if="item.dataType === 'identity'" class="list-item-left">
					<span class="list-item-left-label">
						身份：{{ item.label || item.userDepartJobTitle || item.title }}
					</span>
				</div>
				<div v-else class="list-item-left">
					<!--<div v-if="item.dataType === 'depart'" class="list-item-left-dev">
						<i class="coos-iconfont icon-zuzhijiagou"></i>
					</div>
					<svg-icon v-else class="list-item-left-dev" icon-class="default-avatar"></svg-icon>
					<div v-else class="list-item-left-icon">{{ item.title.slice(-1) }}</div>-->

					<img
						v-if="!item.error && (item.avatarUrl || item.logo)"
						class="list-item-left-dev list-item-left-dev-img"
						:src="item.avatarUrl || item.logo"
						alt=""
						@error="avatarError"
					/>
					<div v-else-if="item.dataType === 'depart'" class="list-item-left-dev">
						<i class="coos-iconfont icon-zuzhijiagou"></i>
					</div>
					<svg-icon
						v-else-if="item.dataType === 'org'"
						icon-class="org-logo"
						class="list-item-left-icon"
						style="background: #ffffff"
					></svg-icon>
					<div v-else class="list-item-left-icon">{{ item.title.slice(-1) }}</div>
					<div class="list-item-left-desc">{{ item.title }}</div>
				</div>
				<div class="list-item-right" @click="remove(false, index)">
					移除
					<svg-icon icon-class="close" class="close-icon"></svg-icon>
				</div>
			</div>
			<div v-show="selectData.length === 0" class="empty">没有选中的数据</div>
		</div>
	</div>
</template>

<script>
export default {
	name: 'ShowSelect',
	props: {
		/**已经选中的数据*/
		selectData: {
			type: Array,
			default: () => {
				return [];
			}
		}
	},
	data() {
		return {
			orgCount: 0, // 组织数量
			identityCount: 0, //身份数量
			postCount: 0, //岗位数量
			labelCount: 0, // 标签数量
			departCount: 0, // 部门数量
			personCount: 0 // 人员数量
		};
	},
	watch: {
		selectData: {
			deep: true,
			handler: function (newVal) {
				let labelCount = 0;
				let identityCount = 0;
				let postCount = 0;
				let departCount = 0;
				let personCount = 0;
				let orgCount = 0;
				newVal.forEach(item => {
					switch (item.dataType) {
						case 'depart':
							departCount += 1;
							break;
						case 'label':
							labelCount += 1;
							break;
						case 'post':
							postCount += 1;
							break;
						case 'identity':
							identityCount += 1;
							break;
						case 'org':
							orgCount += 1;
							break;
						default:
							personCount += 1;
					}
				});
				this.orgCount = orgCount;
				this.identityCount = identityCount;
				this.postCount = postCount;
				this.labelCount = labelCount;
				this.departCount = departCount;
				this.personCount = personCount;
			}
		}
	},
	methods: {
		avatarError(item) {
			item.error = true;
		},
		/**移除，是否全部清除，默认false*/
		remove(isClear, index) {
			this.$emit('remove', isClear, index);
		}
	}
};
</script>

<style scoped lang="scss">
$borderRadius: 6px; // 全局统一圆角
.content-right {
	flex: 1;
	height: 100%;
	border-radius: 0px 12px 12px 0px;
	border: 1px solid #f0f0f0;
	padding: 26px 13px;
	display: flex;
	flex-direction: column;
	.title {
		display: flex;
		justify-content: flex-start;
		align-items: center;
		padding-right: 70px;
		position: relative;
		&-name {
			font-size: 14px;
			font-weight: 800;
			color: #2f446b;
			line-height: 22px;
		}
		&-detail {
			font-size: 14px;
			font-weight: 400;
			color: #2f446b;
			line-height: 22px;
		}
		&-clear {
			position: absolute;
			right: 0;
			top: 0;
			font-size: 14px;
			font-weight: 400;
			color: #2f446b;
			line-height: 22px;
			cursor: pointer;
			display: flex;
			justify-content: center;
			align-items: center;
			.icon {
				margin-right: 4px;
				height: 16px;
				width: 16px;
			}
		}
	}
	.list {
		flex: 1;
		margin-top: 17px;
		overflow-y: auto;
		&::-webkit-scrollbar-track-piece {
			background: #d3dce6;
		}

		&::-webkit-scrollbar {
			width: 6px;
		}

		&::-webkit-scrollbar-thumb {
			background: #99a9bf;
			border-radius: 20px;
		}
		&-item {
			padding: 12px 6px 12px 12px;
			display: flex;
			justify-content: space-between;
			align-items: center;
			&-left {
				flex: 1;
				display: flex;
				justify-content: flex-start;
				align-items: center;
				&-label {
					font-size: 16px;
					font-weight: 400;
					color: #2f446b;
					line-height: 24px;
				}
				&-icon {
					flex-shrink: 0;
					width: 32px;
					height: 32px;
					background: var(--brand-6, #0f45ea);
					border-radius: $borderRadius;
					margin-right: 8px;
					font-size: 14px;
					font-weight: 500;
					color: #ffffff;
					line-height: 26px;
					display: flex;
					justify-content: center;
					align-items: center;
				}
				&-desc {
					flex: 1;
					font-size: 16px;
					font-weight: 400;
					color: #2f446b;
					line-height: 24px;
					overflow: hidden;
					white-space: nowrap;
					text-overflow: ellipsis;
				}
				&-dev {
					width: 32px;
					height: 32px;
					background: #47d07e;
					border-radius: $borderRadius;
					font-size: 16px;
					color: #ffffff;
					margin-right: 8px;
					display: flex;
					justify-content: center;
					align-items: center;
				}
				&-dev-img {
					background: #ffffff;
				}
			}
			&-right {
				flex-shrink: 0;
				font-size: 14px;
				font-weight: 400;
				color: #737a94;
				line-height: 22px;
				cursor: pointer;
				display: flex;
				justify-content: center;
				align-items: center;
				.close-icon {
					width: 16px;
					height: 16px;
					margin-left: 2px;
				}
			}
		}
		.empty {
			height: 200px;
			text-align: center;
			line-height: 200px;
		}
	}
}
</style>
