<template>
	<!--  :default-expanded-keys="[2, 3]"
		:default-checked-keys="[5]"-->
	<el-tree
		ref="ElTree"
		v-loading="loading"
		class="custom-el-three"
		:data="userLabel"
		show-checkbox
		node-key="id"
		:props="defaultProps"
		:filter-node-method="filterNode"
		@check="check"
	></el-tree>
</template>

<script>
export default {
	name: 'PersonSelect',
	props: {
		userLabel: {
			type: Array,
			default: () => {
				return [];
			}
		}
	},
	data() {
		return {
			loading: false,
			defaultProps: {
				children: 'children',
				label: 'title'
			},
			lazyInit: false, // 延迟初始化
			initData: [], // 初始数据
			filterIds: [] // 保存父节点key便于清除
		};
	},
	watch: {
		// 树数据加载的时候执行初始化
		userLabel: {
			deep: true,
			handler: function (newVal) {
				if (this.lazyInit) {
					this.$nextTick(() => {
						this.init(this.initData);
						this.lazyInit = false;
					});
				}
			}
		}
	},
	methods: {
		/**初始化回显*/
		init(data) {
			// 如果数据还未加载，延迟初始化
			if (this.userLabel.length === 0) {
				this.initData = data;
				this.lazyInit = true;
				return;
			}
			this.filterIds = data;
			this.filterIds.forEach(eId => {
				this.$refs.ElTree.setChecked(eId, true, true);
			});
		},
		/**搜索*/
		search(val) {
			this.loading = true;
			this.$refs.ElTree.filter(val);
			setTimeout(() => {
				this.loading = false;
			}, 500);
		},
		/**搜索筛选的方法*/
		filterNode(val, data) {
			if (!val) return true;
			return data.title.indexOf(val) > -1;
		},
		/**节点选中状态发生了变化*/
		check(arr, obj) {
			// 数据重复可被去重
			let filterArr = new Map();
			this.filterIds = [];
			// 有父节点被选中的，不展示子节点
			obj.checkedNodes.forEach(item => {
				if (!obj.checkedKeys.includes(item.pid)) {
					filterArr.set(item.id, item);
					this.filterIds.push(item.id);
				}
			});
			this.$emit('changeSelect', this.filterIds, filterArr, 'label');
		},
		/**移除，是否全部清除，默认false*/
		remove(isClear, id) {
			if (isClear) {
				this.filterIds.forEach(eId => {
					this.$refs.ElTree.setChecked(eId, false, true);
				});
			} else {
				this.$refs.ElTree.setChecked(id, false, true);
			}
		}
	}
};
</script>

<style scoped lang="scss">
@mixin noScrollBar {
	&::-webkit-scrollbar {
		width: 0;
	}
}
.custom-el-three {
	flex: 1;
	overflow-y: auto;
	&::-webkit-scrollbar-track-piece {
		background: #d3dce6;
	}

	@include noScrollBar;

	&::-webkit-scrollbar-thumb {
		background: #99a9bf;
		border-radius: 20px;
	}
}
.custom-el-three {
	::v-deep .el-tree-node__expand-icon {
		color: #737a94;
	}
	::v-deep .el-tree-node__expand-icon.is-leaf {
		color: transparent !important;
	}
	::v-deep .el-tree-node__label {
		font-size: 14px;
		font-weight: 400;
		color: #2f446b;
		line-height: 22px;
	}
}
</style>
