<template>
	<div v-loading="pageLoading" class="content" :style="{ height }">
		<div class="content-left">
			<el-input
				v-show="activeName === 'org'"
				v-model="orgKeyword"
				class="custom-el-input"
				placeholder="请输入姓名"
				@input="search"
			>
				<div slot="prefix" class="prefix">
					<svg-icon class="prefix-icon" icon-class="search"></svg-icon>
				</div>
			</el-input>
			<el-input
				v-show="activeName === 'label'"
				v-model="userKeyword"
				class="custom-el-input"
				placeholder="请输入标签名称"
				@input="search"
			>
				<div slot="prefix" class="prefix">
					<svg-icon class="prefix-icon" icon-class="search"></svg-icon>
				</div>
			</el-input>
			<el-input
				v-show="activeName === 'post'"
				v-model="postKeyword"
				class="custom-el-input"
				placeholder="请输入岗位关键词"
				@input="search"
			>
				<div slot="prefix" class="prefix">
					<svg-icon class="prefix-icon" icon-class="search"></svg-icon>
				</div>
			</el-input>
			<el-input
				v-show="activeName === 'identity'"
				v-model="identityKeyword"
				class="custom-el-input"
				placeholder="请输入身份关键词"
				@input="search"
			>
				<div slot="prefix" class="prefix">
					<svg-icon class="prefix-icon" icon-class="search"></svg-icon>
				</div>
			</el-input>
			<el-tabs
				v-model="activeName"
				class="custom-el-tabs"
				:class="{ fixTransform: dataSource.length === 1 }"
				@tab-click="handleClick"
			>
				<el-tab-pane
					v-if="dataSource.includes('depart') || dataSource.includes('user')"
					label="组织架构"
					name="org"
				>
					<!--组织选择-->
					<orgSelect
						ref="orgTree"
						v-loading="orgLoading"
						:default-oeg-name="defaultOegName"
						:list="list"
						:has-depart="hasDepart"
						:org-arr="orgArr"
						:navbar="navbar"
						:org-icon="orgIcon"
						:list-map="listMap"
						:list-loading="listLoading"
						:no-more="noMore"
						:org-keyword="orgKeyword"
						:disable-org="disableOrg"
						:first-nav-org="firstNavOrg"
						v-bind="$attrs"
						@scrollBottom="scrollBottom"
						@openChildren="openChildren"
						@changeTreeData="changeTreeData"
						@changeSelect="changeSelect"
						@changeCurrentOrg="changeCurrentOrg"
					></orgSelect>
				</el-tab-pane>
				<el-tab-pane v-if="dataSource.includes('label')" label="用户标签" name="label">
					<div class="label-content">
						<div v-show="userKeyword" class="search-result">以下是全部搜索结果：</div>
						<!--   人员标签选择   -->
						<personSelect
							:key="labelTreeKey"
							ref="labelTree"
							:user-label="userLabel"
							@changeSelect="changeSelect"
						></personSelect>
					</div>
				</el-tab-pane>
				<el-tab-pane v-if="dataSource.includes('post')" label="岗位" name="post">
					<!--岗位选择-->
					<postSelect ref="postDom" @changeSelect="changeSelect"></postSelect>
				</el-tab-pane>
				<el-tab-pane v-if="dataSource.includes('identity')" label="身份" name="identity">
					<!--身份选择-->
					<identitySelect ref="identityDom" @changeSelect="changeSelect"></identitySelect>
				</el-tab-pane>
			</el-tabs>
		</div>
		<!--展示已选-->
		<showSelect :select-data="selectData" @remove="remove"></showSelect>
	</div>
</template>

<script>
import orgSelect from './components/org-select.vue';
import showSelect from './components/show-select.vue';
import personSelect from './components/person-select.vue';
import postSelect from './components/post-select.vue';
import identitySelect from './components/identity-select.vue';
import { debounce } from '@/utils';
import { getOrigTree, getPersonTree, getUserLabelList } from '@/api/modules/component';
export default {
	name: 'PersonnelSelect',
	components: {
		orgSelect,
		showSelect,
		personSelect,
		postSelect,
		identitySelect
	},
	props: {
		/**默认打开部门*/
		defaultDepId: {
			type: String,
			default: () => {
				return '';
			}
		},
		/**是否限制数据为当前组织*/
		limitOrg: {
			type: Boolean,
			default: () => {
				return false;
			}
		},
		/**默认选中的组织名称*/
		defaultOegName: {
			type: String,
			default: () => {
				return '';
			}
		},
		/**是否返回全部数据*/
		needAllData: {
			type: Boolean,
			default: () => {
				return false;
			}
		},
		/**数据来源*/
		dataSource: {
			type: Array,
			default: () => {
				return ['depart', 'user', 'label']; // 默认组织、人员、标签
			}
		},
		/**组件高度*/
		height: {
			type: String,
			default: () => {
				return '580px';
			}
		},
		/**初始化数据的函数*/
		initFunc: {
			type: [Object, Function],
			default: () => {
				return null;
			}
		},
		/**初始化数据的参数*/
		initParams: {
			type: Object,
			default: () => {
				return null;
			}
		},
		/**是否包含部门下面的人员*/
		includeUsers: {
			type: Boolean,
			default: () => {
				return true;
			}
		},
		/**初始化数据，静态的数组*/
		initValues: {
			type: Array,
			default: () => {
				return [];
			}
		},
		/**是否禁止选择组织*/
		disableOrg: {
			type: Boolean,
			default: () => {
				return true;
			}
		}
	},
	data() {
		return {
			identityKeyword: '', // 身份搜索关键词
			postKeyword: '', // 单独保存post岗位的搜索关键词
			orgLoading: false, // 组织树加载数据效果
			hasDepart: true, // 是否含有部门的数据
			orgArr: [], // 组织集合
			orgArrMap: new Map(), // 组织集合map映射
			navbar: [], // 选择层级面包屑
			orgIcon: '', // 当前组织的icon
			listMap: new Map(), // 所有数据的map键值对，便于查询取舍
			listLoading: false,
			noMore: false,
			orgKeyword: '', // 搜索数据

			pageLoading: false, // 整个页面的效果，因为mounted的时候初始数据显示，列表可能未请求完成
			pageNo: 1,
			pageSize: 10,
			labelTreeKey: 0, // 刷新树组件的方式
			activeName: 'org', // 当前选中的tab
			userKeyword: '', // 搜索数据
			orgSearchData: new Map(), // 组织数据搜索结果的map集合
			personData: new Map(), // 人员列表map集合
			allTreeMap: new Map(), // navbar切换每个tab对应的树数据map键值对
			selectDataMap: new Map(), // 选中数据的对象集合map映射关系，避免多次循环对比
			selectData: [], // 选中数据对象集合
			orgIds: [], // 组织选择类型的id集合
			userLabel: [], // 人员标签树数据
			initData: [], // 初始数据
			currentOrg: '', // 当前组织
			orgIcons: new Map(), // 组织集合对应的icon，循环一次存储，不用每次都循环对比
			computedOrgTitle: '组织人员计算后的数据',
			initDataMap: new Map() // 初始数据的键值对
		};
	},
	computed: {
		// 组织树选择数据
		list() {
			let key = this.navbar[this.navbar.length - 1]; // 最后一项为当前需要展示的数据
			if (this.orgArr.includes(key)) {
				let key = this.orgKeyword ? '搜索结果' : this.computedOrgTitle;
				return this.allTreeMap.get(key);
			} else if (this.orgArr.length === 0) {
				return this.personData;
			} else {
				return this.allTreeMap.get(key) || new Map();
			}
		},
		// 面包屑第一个组织数据
		firstNavOrg() {
			return this.orgKeyword ? {} : this.orgArrMap.get(this.navbar[0]);
		}
	},
	watch: {
		selectData: {
			deep: true,
			handler: function (newVal) {
				let data;
				// 处理好的数据
				if (this.needAllData) {
					data = newVal;
				} else {
					data = newVal.map(item => {
						// 没有dataType默认是user
						return {
							objectId: item.id,
							dataType: item.dataType || 'user',
							objectType: item.dataType === 'depart' ? 1 : item.dataType === 'label' ? 3 : 2
						}; //objectType对象类型;1部门,2成员,3标签
					});
				}
				this.$emit('change', data);
			}
		},
		/**请求数据函数发生变化*/
		initFunc: {
			deep: true,
			handler: function (newVal) {
				// 重置初始化交互数据，再渲染，避免干扰
				this.reset();
			}
		},
		/**请求数据参数发生变化*/
		initParams: {
			deep: true,
			handler: function (newVal) {
				// 重置初始化交互数据，再渲染，避免干扰
				this.reset();
			}
		},
		/**初始回显数据发生变化*/
		initValues: {
			deep: true,
			handler: function (newVal) {
				// 重置初始化交互数据，再渲染，避免干扰
				this.reset();
			}
		}
	},
	async mounted() {
		this.pageLoading = true;
		// 需要展示人员数据的时候才请求
		if (this.dataSource.includes('user')) {
			await this.getPersonList();
		}
		// 需要展示部门数据的时候才请求
		if (this.dataSource.includes('depart')) {
			await this.getOrigList();
			// 如果有部门，才需要计算合并部门和人员的数据
			if (this.currentOrg) {
				let newMap = new Map([...this.allTreeMap.get(this.currentOrg), ...this.personData]); // 组织的数据+人员列表的数据
				this.allTreeMap.set(this.computedOrgTitle, newMap);
			} else {
				let newMap = new Map([...this.personData]); // 组织的数据+人员列表的数据
				this.allTreeMap.set('全部', newMap);
			}
		} else {
			// 否则只有人员的数据
			this.currentOrg = '全部人员';
			this.hasDepart = false;
			this.allTreeMap.set(this.currentOrg, new Map([...this.personData]));
		}
		await this.init(); // 初始化数据
		this.proListMap(); // 更新所有数据的id键值对
		if (this.currentOrg) {
			this.navbar.push(this.currentOrg); // 全部数据请求完成再渲染
		}
		this.getUserLabel(); // 获取用户标签的数据
		this.pageLoading = false;
		this.activeName = ['depart', 'user'].includes(this.dataSource[0]) ? 'org' : this.dataSource[0];
		this.$emit('changeValue', false);
		if (this.defaultDepId) {
			let map = this.allTreeMap.get(this.currentOrg);
			if (map.has(this.defaultDepId)) {
				this.openChildren(map.get(this.defaultDepId));
			}
		}
	},
	methods: {
		/**初始化数据*/
		async init() {
			// 需要初始化数据就请求初始数据
			if (this.initFunc) {
				await this.getList();
			}
			// 有静态数据需要回显也要处理
			else if (this.initValues.length > 0) {
				this.initData = this.initValues;
				this.handleInitData();
			}
		},
		/**改变当前数据-只是组织发生改变不用重新计算proListMap*/
		changeCurrentOrg(name) {
			let newMap = new Map([...this.allTreeMap.get(name), ...this.personData]); //把组织数据和人员数据拼接起来
			this.allTreeMap.set(this.computedOrgTitle, newMap);
			this.currentOrg = name;
			this.navbar = this.orgKeyword ? ['搜索结果'] : [name];
			if (this.navbar.length === 1 && this.navbar[0] !== '搜索结果') {
				this.orgIcon = this.orgIcons.get(name);
			} else {
				this.orgIcon = '';
			}
			// 切换组织之后有搜索重新执行搜索
			if (this.orgKeyword) {
				this.search();
			}
		},
		/**处理初始化回显数据*/
		handleInitData() {
			let depperson = []; // 组织人员
			let label = []; // 标签
			let postArr = []; // 岗位
			let identityArr = []; // 身份
			this.initData.forEach(item => {
				// 对象类型;1部门,2成员,3标签
				if (item.dataType === 'label') {
					label.push(item.id);
				} else if (item.dataType === 'post') {
					postArr.push(item.id);
				} else if (item.dataType === 'identity') {
					identityArr.push(item.id);
				} else {
					// 组织和人员放在一起
					depperson.push(item.id);
				}
				// 存储初始数据的键值对
				this.initDataMap.set(item.id, item);
			});
			this.orgIds = depperson;
			this.selectDataMap = new Map([...this.initDataMap]); // ...this.selectDataMap, 初始数据存为已选中数据
			this.selectData = Array.from(this.selectDataMap.values()); // 初始数据存为已选中展示数据
			this.$refs.orgTree && this.$refs.orgTree.init(depperson); // 组件初始化数据进行回选操作
			if (this.dataSource.includes('label')) {
				this.$nextTick(() => {
					this.$refs.labelTree.init(label); // 标签组件初始化数据进行回选操作
				});
			}
			if (this.dataSource.includes('post')) {
				this.$nextTick(() => {
					this.$refs.postDom.init(postArr); // 标签组件初始化数据进行回选操作
				});
			}
			if (this.dataSource.includes('identity')) {
				this.$nextTick(() => {
					this.$refs.identityDom.init(identityArr); // 标签组件初始化数据进行回选操作
				});
			}
			this.pageLoading = false;
		},
		/**获取应用范围回显数据*/
		async getList() {
			this.pageLoading = true;
			let res = await this.initFunc(this.initParams);
			if (res.code === 200) {
				this.initData = res.result || [];
				this.handleInitData();
			} else {
				this.$message.error(res.message);
			}
		},
		/**重置数据*/
		async reset() {
			// this.activeName = 'org';
			this.activeName = ['depart', 'user'].includes(this.dataSource[0])
				? 'org'
				: this.dataSource[0];
			this.orgKeyword = '';
			this.userKeyword = '';
			this.postKeyword = '';
			this.identityKeyword = '';
			this.initDataMap = new Map(); // 初始值刷新清空
			this.selectDataMap.clear();
			this.selectData = [];
			this.labelTreeKey += 1; // 刷新组件的状态
			this.navbar = this.orgArr[0] ? [this.orgArr[0]] : ['全部人员'];
			this.orgIds = [];
			this.search();
			this.$refs.orgTree && this.$refs.orgTree.init([]); // 组件初始化数据进行回选操作
			await this.init();
			this.$emit('changeValue', false);
		},
		/**搜索*/
		search: debounce(
			async function () {
				if (this.activeName === 'org') {
					this.orgLoading = true;
					this.orgSearchData.clear(); // 每次搜索重置数据
					// 搜索函数，以便递归
					const filterOrgData = arr => {
						arr.forEach(item => {
							// 有子元素递归循环筛选
							if (item.children && item.children.length) {
								filterOrgData(item.children);
							}
							// 搜索到数据加入搜索结果
							if (item.title.indexOf(this.orgKeyword) > -1) {
								if (item.dataType === 'user') {
									if (item.isMain) {
										this.orgSearchData.set(item.id, item);
									}
								} else {
									this.orgSearchData.set(item.id, item);
								}
							}
						});
					};
					// 未建立组织的时候
					if (this.currentOrg) {
						let filterData = Array.from(this.allTreeMap.get(this.currentOrg).values());
						filterOrgData(filterData); // 前端搜索组织结构中数据
					}
					console.log(this.currentOrg, 'currentOrg', this.filterData);

					// 需要展示人员选择数据的时候才去搜索筛选
					if (this.dataSource.includes('user')) {
						await this.searchPersonList(); // 后端搜索人员列表数据
					}
					let text, navKey, orgData; // 存储树列表数据的字段，面包屑字段，搜索结果还是全部
					if (this.orgKeyword) {
						text = '搜索结果';
						navKey = '搜索结果';
						orgData = this.orgSearchData;
						this.orgIcon = '';
					} else {
						text = this.computedOrgTitle;
						navKey = this.currentOrg;
						orgData = this.currentOrg ? this.allTreeMap.get(this.currentOrg) : new Map();
						this.orgIcon = this.orgIcons.get(this.currentOrg);
					}

					let newMap = new Map([...orgData, ...this.personData]); // 搜索结果 = 组织的搜索结果 + 人员列表的搜索结果

					this.allTreeMap.set(text, newMap);
					this.proListMap();
					this.orgLoading = false;
					this.navbar = navKey ? [navKey] : [];
				} else if (this.activeName === 'label') {
					this.$refs.labelTree.search(this.userKeyword);
				} else if (this.activeName === 'post') {
					this.$refs.postDom.search(this.postKeyword);
				} else if (this.activeName === 'identity') {
					this.$refs.identityDom.search(this.identityKeyword);
				}
			},
			500,
			false
		),
		/**人员列表后端搜索*/
		async searchPersonList() {
			// 重置滚动加载的配置
			this.noMore = false;
			this.pageNo = 1;
			this.personData.clear(); // 清空人员列表的数据，搜索不累加
			await this.getPersonList(this.orgKeyword);
		},
		/**获取用户标签的数据*/
		getUserLabel() {
			getUserLabelList().then(res => {
				if (res.code === 200) {
					this.userLabel = res.result.map(item => {
						item.children = item.children || [];
						return item;
					});
				} else {
					this.$message.error(res.message);
				}
			});
		},
		/**左侧右边滚动条触发事件*/
		async scrollBottom() {
			let key = this.navbar[this.navbar.length - 1]; // 最后一项为当前需要展示的数据
			// 只有人员数据允许加载才有滚动触发
			if (
				this.dataSource.includes('user') &&
				(this.orgArr.includes(key) || ['搜索结果', '全部人员'].includes(key))
			) {
				this.pageNo += 1;
				await this.getPersonList(this.orgKeyword);
				let text = this.orgKeyword ? '搜索结果' : this.computedOrgTitle;
				let navArr = this.allTreeMap.get(text) || []; // 每一个navbar切换的数据进行拼接，只有人员的时候是undefined
				this.allTreeMap.set(text, new Map([...navArr, ...this.personData]));
				this.navbar = [this.currentOrg];
				this.proListMap();
			}
		},
		/**移除，是否全部清除，默认false*/
		remove(isClear, index) {
			if (isClear) {
				this.$refs.orgTree && this.$refs.orgTree.remove(isClear, index);
				this.dataSource.includes('post') && this.$refs.postDom.remove(isClear, index);
				this.dataSource.includes('identity') && this.$refs.identityDom.remove(isClear, index);
				this.dataSource.includes('label') && this.$refs.labelTree.remove(isClear, index);
				this.selectDataMap.clear();
				this.selectData = [];
			} else {
				let delObj = this.selectData[index]; // 拿到删除对象
				// 判断删除对象的类型
				let isLabel = delObj.dataType === 'label';
				let isPost = delObj.dataType === 'post';
				let isIdentity = delObj.dataType === 'identity';
				// 根据类型通知子组件删除对应的数据
				if (isLabel) {
					this.$refs.labelTree.remove(isClear, delObj.id);
				} else if (isPost) {
					this.$refs.postDom.remove(isClear, delObj.id);
				} else if (isIdentity) {
					this.$refs.identityDom.remove(isClear, delObj.id);
				} else {
					// 获得对象在对应的类型集合中的索引
					let i = this.orgIds.indexOf(delObj.id);
					this.$refs.orgTree && this.$refs.orgTree.remove(isClear, i);
				}
				this.selectDataMap.delete(delObj.id); // 删除键值对
				this.selectData.splice(index, 1);
			}
			this.$emit('changeValue', true);
		},
		/**计算所有数据map映射关系，便于根据ID取舍数据*/
		proListMap() {
			let allDataMap = Array.from(this.allTreeMap.values());
			allDataMap = allDataMap.reduce((org, item) => {
				return org.concat([...item]);
			}, []);
			this.listMap = new Map(allDataMap);
		},
		/**
		 * @Method 选中集合改变
		 * arr 选中ID集合，分组织选中 和 标签选中
		 * filterArr 只有标签选中的时候才传，为选中的对象
		 * */
		changeSelect(arr, filterArr, dataType) {
			// 把新增的数据加入到集合
			arr.forEach(id => {
				// 如果是历史数据就不用重复存储
				if (!this.selectDataMap.get(id)) {
					// 拿到本id对应的对象
					this.selectDataMap.set(id, filterArr ? filterArr.get(id) : this.listMap.get(id));
				}
			});
			let keyArr = Array.from(this.selectDataMap.values());
			// 把删除的数据删除掉
			if (filterArr) {
				keyArr = keyArr
					.filter(item => {
						return item.dataType === dataType;
					})
					.map(item => item.id);
			} else {
				this.orgIds = arr; // 组织类型的id集合，方便删除时候用
				keyArr = keyArr
					.filter(item => {
						return !['label', 'post', 'identity'].includes(item.dataType);
					})
					.map(item => item.id);
			}
			keyArr.forEach(id => {
				// 如果id不包含在最新的id集合，则删除
				if (!arr.includes(id)) {
					this.selectDataMap.delete(id);
				}
			});
			// 因为map在vue中监听不到改变，所以显示的时候取数据
			this.selectData = Array.from(this.selectDataMap.values());
			this.$emit('changeValue', true);
		},
		/**改变树数据*/
		changeTreeData(item) {
			let index = this.navbar.indexOf(item);
			this.navbar.splice(index + 1); // 删除之后的数据
		},
		/**组织树选择子集*/
		openChildren(item) {
			// 如果map中没有该键值对，把子集存进map（之前用名字作为key，有不唯一的弊端，现在加一个id判断，解决问题）
			if (
				!this.allTreeMap.has(item.title) ||
				this.allTreeMap.get(item.title).keys()[0] !== item.children[0].id
			) {
				// 缓存每次方便取拿树数据
				let map = new Map();
				item.children.forEach(item => {
					map.set(item.id, item);
				});
				this.allTreeMap.set(item.title, map);
				this.proListMap(); // 更新所有数据的id键值对
			}
			this.navbar.push(item.title);
		},
		/**获取组织树形结构*/
		async getOrigList() {
			let params = { includeUsers: this.includeUsers, limitOrg: this.limitOrg };
			let res = await getOrigTree(params);
			if (res.code === 200) {
				res.result.forEach(item => {
					if (item.dataType === 'org') {
						this.orgArr.push(item.title);
						this.orgArrMap.set(item.title, item);
						this.orgIcons.set(item.title, item.logo || '');
					}
					let map = new Map();
					if (item.children && item.children.length > 0) {
						item.children.forEach(child => {
							map.set(child.id, child);
						});
					}
					// 把第二级部门的数据存入进去，因为不展示第一级组织数据
					this.allTreeMap.set(item.title, map);
				});
				// 如果不禁止组织选择，将数据加入
				if (!this.disableOrg) {
					let navIdMap = new Map();
					Array.from(this.orgArrMap.values()).forEach(item => {
						navIdMap.set(item.id, item);
					});
					this.allTreeMap.set('allNav', navIdMap);
				}
				// 自定义的默认
				if (this.defaultOegName) {
					let org = res.result.find(item => {
						return this.defaultOegName === item.title;
					});
					// 有匹配项目才赋值
					if (org) {
						this.currentOrg = this.defaultOegName;
						this.orgIcon = org.logo || '';
					}
				}
				// 如果之前没有赋值，才取默认
				if (!this.currentOrg) {
					this.currentOrg =
						res.result[0] && res.result[0].dataType === 'org' ? res.result[0].title : ''; // 默认选中第一个数据为当前组织
					this.orgIcon = res.result[0] ? res.result[0].logo : ''; // 默认选中第一个数据为当前组织
				}
			} else {
				this.$message.error(res.message);
			}
		},
		/**获取人员列表*/
		async getPersonList(keyword = '') {
			this.listLoading = true;
			let res = await getPersonTree({
				pageSize: this.pageSize,
				pageNo: this.pageNo,
				realName: keyword,
				limitOrg: this.limitOrg
			});
			this.listLoading = false;
			if (!res) return; // 频繁请求会被cancel
			if (res.code === 200) {
				// 数量已经加载完毕,不可继续加载
				if (res.result.pages <= this.pageNo) {
					this.noMore = true;
				}
				res.result.records.forEach(item => {
					// 保持数据一致
					item.title = item.realname;
					item.pid = '';
					this.personData.set(item.id, item);
				});
			} else {
				this.$message.error(res.message);
			}
		},
		/**切换组织和人员的tab*/
		handleClick() {}
	}
};
</script>

<style scoped lang="scss">
.content {
	display: flex;
	justify-content: flex-start;
	align-items: flex-start;
	background: #ffffff;
	height: 100%;
	&-left {
		width: 55%;
		height: 100%;
		border-radius: 12px 0px 0px 12px;
		border: 1px solid #f0f0f0;
		padding: 17px 13px;
		overflow: hidden;
		display: flex;
		flex-direction: column;
		.prefix {
			height: 100%;
			width: 100%;
			display: flex;
			justify-content: center;
			align-items: center;
			&-icon {
				width: 16px;
				height: 16px;
			}
		}
		// tab标签
		.custom-el-tabs {
			flex: 1;
			overflow: hidden;
			display: flex;
			flex-direction: column;
			::v-deep .el-tabs__content {
				flex: 1;
				overflow: hidden;
				.el-tab-pane {
					height: 100%;
				}
			}
			::v-deep .el-tabs__item {
				font-size: 16px;
				padding: 0 16px;
			}
			::v-deep .el-tabs__item.is-active {
				color: var(--brand-6, #0f45ea);
			}
			::v-deep .el-tabs__active-bar {
				background: var(--brand-6, #0f45ea);
			}
			::v-deep .el-tabs__nav-wrap::after {
				background: #f0f0f0;
			}
			.search-result {
				flex-shrink: 0;
				font-size: 14px;
				height: 40px;
				line-height: 40px;
			}
		}
		// 输入框
		.custom-el-input {
			height: 40px;
			border-radius: 6px;
			::v-deep .el-input__prefix {
				left: 15px;
			}
			::v-deep .el-input__inner {
				padding-left: 36px;
				height: 40px;
				border-radius: 6px;
			}
		}
	}
}
.label-content {
	height: 100%;
	display: flex;
	flex-direction: column;
}
.fixTransform {
	::v-deep .el-tabs__active-bar {
		transform: translateX(0) !important;
	}
}
</style>
