<!--
 * @Description: APP.Vue
 * @Version: 1.0
 * @Autor: zhaodongming
 * @Date: 2021-12-14 09:47:43
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-03-18 14:54:10
-->
<template>
	<div id="app">
		<router-view />
	</div>
</template>

<script>
import { handleSetThemeVar, handleColorLoad } from '@/utils/color.js';
import { getItem, setItem } from '@/utils/localstorage';
import { cdnLoad } from '@/utils/cdn';
import { mapActions, mapGetters, mapMutations } from 'vuex';
import { updateCount } from '@/utils/update-help';
import { getToken } from '@/utils/auth';
import WebviewSocket from '@/utils/socket';
import { updateWater } from '@/utils/get-water';
import { loginWileFire } from '@/utils/wile-fire-login';
import { getDefaultMenuList } from '@/router';
import wfc from '@/wile-fire/wfc/client/wfc';
import config from '@/config/index.js';
import { checkLogin } from '@/api/modules/common';
import { getTenantConfig } from '@/api/modules/wait-handle';
import { CoosEventTypes } from '@/utils/bus';
import { getDictionary } from '@/utils/data-dictionary';
import { preUrl } from '@/config';
import axios from 'axios';
const defaultColor = '#1890ff';
const themeColor = '#0F45EA';
export default {
	name: 'App',
	data() {
		return {
			socketVM: null
		};
	},
	computed: {
		...mapGetters(['systemInfo', 'openIm', 'userInfo', 'rentInfo'])
	},
	watch: {
		'systemInfo.logoIcon': {
			deep: true,
			handler: function (newVal) {
				// 从服务端获取到favicon
				if (newVal) {
					this.changeFavicon(newVal);
				}
			}
		}
	},
	async created() {
		// 第一次打开项目一定是最新的版本，直接存储新版本号
		try {
			let versionRes = await axios.get(
				`${window.location.origin}${window.location.pathname}version.json`,
				{}
			);
			setItem('VERSION', versionRes.data.version);
		} catch (err) {
			// console.log(err);
		}
		// 免密字段
		localStorage.setItem('SecretFree', !!this.$route.query.SecretFree);
		// 租户端的SDK
		window['coosRentDesk'] = {
			windowNodeEnvPath: 'desk',
			coosVFormBaseUrl: /http/gi.test(config.baseUrl)
				? config.baseUrl
				: window.location.origin + config.baseUrl
		};
		// 如果是主应用，加监听
		if (window.top === window.self) {
			window.addEventListener('message', this.handleWatch);
		}
		// 关闭长连接
		this._BUS.$on(CoosEventTypes.closeSocket, this.closeSocket);
		// 模拟项目重新加载
		this._BUS.$on(CoosEventTypes.reloadProject, this.reload);
		// 回显主题设置 放到SET_THEM里面统一处理
		// this.handlerReShow();
	},
	mounted() {
		// 设置全局未捕获的Promise错误处理
		window.addEventListener('unhandledrejection', event => {
			if (event.reason.name === 'AbortError') {
				event.preventDefault(); // 阻止默认错误处理
				console.log('捕获到未处理的AbortError，已静默处理');
			}
		});
		this.reload();
	},
	destroyed() {
		this._BUS.$off(CoosEventTypes.closeSocket, this.closeSocket);
		this._BUS.$off(CoosEventTypes.reloadProject, this.reload);
		window.removeEventListener('message', this.handleWatch);
	},
	methods: {
		...mapMutations('user', [
			'CLEAR_PRE_USER',
			'CLEAR_CACHE',
			'SET_WAIT_CONFIG',
			'SET_DOCUMENT_CONFIG'
		]),
		...mapActions('them', ['SET_THEM']),
		...mapActions('settings', ['GET_MAIN_MENU']),
		...mapActions('rent', ['GET_AI_ENTER', 'GET_COOS_CONFIG']),
		/**返回登录页*/
		goLogin() {
			checkLogin().then(res => {
				if (res.code === 200) {
					this.$message.error('主应用登录未过期！');
				}
			});
		},
		/**关闭socket*/
		closeSocket() {
			this.socketVM && this.socketVM.close(); // 如果有连接都要先断开，防止多个socket存在
		},
		/**
		 * 加载项目以及重新加载（切换租户、重新登陆、切换身份等业务场景使用）
		 * 以前靠window.location.reload重载项目清除、更新数据，导致页面交互等待空白
		 * 需要更新数据的时候，全局派发事件解决
		 * @param {Boolean} isCheckRoute 是否检查路由权限
		 * */
		reload(isCheckRoute) {
			if (getToken()) {
				// 加载ai入口列表
				this.GET_AI_ENTER();
				// 加载coos配置
				this.GET_COOS_CONFIG();
				// 如果校验权限,目前除了重新登陆
				this.checkRoute(isCheckRoute);
				// 获取主菜单配置
				this.GET_MAIN_MENU();
				// 获取待办的配置
				this.getWaitConfig();
				// 获取文档配置
				this.getDocumentConfig();
				// 更新待办数量
				updateCount();
				// 更新水印
				updateWater();
				// 清除之前的用户数据
				this.CLEAR_PRE_USER();
				// 如果有连接都要先断开，防止多个socket存在
				this.socketVM && this.socketVM.close();
				// 退出登录，防止消息通知等出问题
				wfc.disconnect();
				// 创建长连接，用于待办通知
				this.socketVM = new WebviewSocket(this.rentInfo.id + '_' + this.userInfo.id);
				// 登录im
				if (this.openIm === 'true') {
					loginWileFire();
				}
				// cdn加载
				this.loadCdn();
			}
			// 设置主题
			this.SET_THEM();
			// 改变浏览器图标
			if (this.systemInfo.logoIcon) this.changeFavicon(this.systemInfo.logoIcon);
		},
		/**加载cdn*/
		loadCdn() {
			// 刷新页面，有些脚本在登录失效检测还未完成之前就加载了，会导致执行有问题
			checkLogin()
				.then(res => {
					if (res.code === 200) {
						cdnLoad();
					}
				})
				.catch(e => {
					console.log(e);
				});
		},
		/**判断租户是否在主菜单，不在的话要重定向到首页*/
		checkRoute(isCheckRoute) {
			if (isCheckRoute) {
				let list = getDefaultMenuList();
				let index = list.findIndex(item => item.name === this.$route.name);
				// 没有权限的话就跳转到首页
				if (index === -1) {
					this.$router.replace('/');
				}
				// 清除租户控制的一些数据
				this.CLEAR_CACHE();
				// 更新当前页面
				this._BUS.$emit(CoosEventTypes.updatePage);
			}
		},
		/**获取待办的配置*/
		getWaitConfig() {
			getTenantConfig(getDictionary('应用ID/待办')).then(res => {
				if (res.code === 200) {
					this.SET_WAIT_CONFIG(res.result);
				} else {
					this.$message.error(res.message);
				}
			});
		},
		/**获取文档的配置*/
		getDocumentConfig() {
			getTenantConfig(getDictionary('应用ID/文档')).then(res => {
				if (res.code === 200) {
					this.SET_DOCUMENT_CONFIG(res.result);
				} else {
					this.$message.error(res.message);
				}
			});
		},
		/**处理监听(最开始的设计，后面将弃用，新设计模式参考webview组件和other-system页面)*/
		handleWatch(e) {
			if (e.data === 'toLogin') {
				this.goLogin();
			} else if (e.data?.type)
				switch (e.data.type) {
					case 'openOther':
						this.openOther(e);
						break;
				}
		},
		openOther(e) {
			let url = e.data.url || '';
			let mainUrl = /http/gi.test(url) ? url : (preUrl || window.location.origin) + url;
			this.$router.push(
				`/other-system?url=${encodeURIComponent(mainUrl || '')}&name=${e.data.title || ''}`
			);
		},
		handlerReShow() {
			setItem('admin-theme', themeColor);
			const reTheme = getItem('admin-theme');
			// 获取到主题配置时,设置回显
			if (reTheme) {
				// 全局颜色变量同步
				handleSetThemeVar(reTheme);
				// 全局组件换肤色彩同步
				handleColorLoad(reTheme, defaultColor);
				// 应用对应的色系
				document.documentElement.setAttribute('theme-color', reTheme);
			} else {
				// 使用默认CSS全局色彩变量
				handleSetThemeVar(defaultColor);
			}
		},
		/**根据配置文件显示favicon文件*/
		changeFavicon(logoIcon) {
			let favicon = document.querySelector('link[rel="icon"]');
			if (favicon !== null) {
				favicon.href = logoIcon;
			} else {
				favicon = document.createElement('link');
				favicon.rel = 'icon';
				favicon.href = logoIcon;
				document.head.appendChild(favicon);
			}
		}
	}
};
</script>
<style lang="scss">
#app {
	font-family: PingFang SC, PingFang SC;
}
</style>
