import request from '@/utils/request';

/**获取应用的配置*/
export function getMemberList(applicationId) {
	return request({
		url: `/api/sys/application/tenantConfig/${applicationId}`,
		method: 'get'
	});
}

/**判断流程下一步是否需要选择节点*/
export function getFlowNext(data) {
	return request({
		url: `/api/flow/runtime/getFirstNodeSelectType`,
		method: 'post',
		data
	});
}

/**标记任务已完成*/
export function handleTask(id, userId) {
	return request({
		url: `/api/task/markWaitHandle/${id}?userId=${userId}`,
		method: 'put'
	});
}

// 矩阵的字典
export function geMatrixtDicts(dictCodes) {
	return request({
		url: `/api/sys/dicts/tenant/${dictCodes}`,
		method: 'get'
	});
}

/**获取进入后台的权限*/
export function getRoleAdmin(params) {
	return request({
		url: `/api/sys/permission/applicationPermission`,
		method: 'get',
		params
	});
}

/**获取AI快捷入口菜单*/
export function getAiEnter() {
	return request({
		url: `/api/sys/application/ai/link/functions/search`,
		method: 'get',
		params: { clientType: 1 }
	});
}

/**检测登录*/
export function checkLogin() {
	return request({
		url: `/api/sys/checkLoginStatus`,
		method: 'get'
	});
}
