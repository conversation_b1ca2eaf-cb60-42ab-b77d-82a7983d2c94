import request from '@/utils/request';

/**应用分类列表*/
export function getMarketCategory() {
	return request({
		url: `/api/sys/application/market/category`,
		method: 'get'
	});
}
/**应用分页列表*/
export function getMarketSearch(params) {
	return request({
		url: `/api/sys/application/market/search`,
		method: 'get',
		params
	});
}

/**应用详情*/
export function getMarketDetails(id) {
	return request({
		url: `/api/sys/application/market/${id}`,
		method: 'get'
	});
}

/**获取应用*/
export function useApply(data) {
	return request({
		url: `/api/sys/application/useApply`,
		method: 'post',
		data
	});
}

/**获取目录*/
export function getLeftMenus() {
	return request({
		url: `/api/sys/web/fixed/applications/search`,
		method: 'get'
	});
}
