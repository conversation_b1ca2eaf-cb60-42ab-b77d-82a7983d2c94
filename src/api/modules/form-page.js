import request from '@/utils/request';

/** 获取表单设计详情 */
export function getFormDetail(id) {
	return request({
		url: `/api/form/custom/define/${id}`,
		method: 'GET'
	});
}

/** 获取表单列表设计的数据 */
export function getFormHead(params) {
	return request({
		url: `/api/form/custom/designs/search`,
		method: 'GET',
		params
	});
}

/** 获取动态表单的列表数据 */
export function getFormTable(params) {
	return request({
		url: `/api/form/custom/datas/search`,
		method: 'GET',
		params
	});
}

/** 保存动态表单的数据 */
export function saveFormData(formId, data) {
	return request({
		url: `/api/form/custom/data/${formId}`,
		method: 'post',
		data
	});
}

/** 获取动态表单的数据 */
export function getFormData(id, params) {
	return request({
		url: `/api/form/custom/data/${id}`,
		method: 'get',
		params
	});
}

/** 更新编辑动态表单的数据 */
export function updateFormData(id, data) {
	return request({
		url: `/api/form/custom/data/${id}`,
		method: 'put',
		data
	});
}
/** 获取图片文本内容经过大模型整理*/
export function getImageLlmContent(data) {
	return request({
		url: `/api/robot/ocr/getImageLlmContent`,
		method: 'post',
		isFormData: true,
		data
	});
}
/** OCR识别数据返回绑定json*/
export function ocrToJson(data) {
	return request({
		url: `/api/form/data/ocrToJson`,
		method: 'post',
		data
	});
}

/**表单数据-保存草稿
 * @param
 * formId:表单id
 * id:数据id
 * 如果有数据id 那么就是修改草稿
 */
export function saveDraft(formId, id, data) {
	let param = `${formId}`;

	if (id && id !== 'null' && id !== 'undefined') {
		param += `/${id}`;
	}
	return request({
		url: `/api/form/custom/data/saveDraft/${param}`,
		method: 'post',
		data
	});
}
/**提交草稿 */
export function submitDraft(formId, id, data) {
	let param = `${formId}`;
	if (id) {
		param += `/${id}`;
	}
	return request({
		url: `/api/form/custom/data/submit/${param}`,
		method: 'post',
		data
	});
}
/**提交草稿 */
export function formRevoke(formId, id, data) {
	return request({
		url: `/api/form/custom/data/revoke/${formId}/${id}`,
		method: 'post',
		data
	});
}

/**提交草稿 */
export function formDelete(id) {
	return request({
		url: `/api/form/custom/data/${id}`,
		method: 'delete'
	});
}

//表单设计-根据表单设计id(视图id)获取设计
export function getDesignById(id) {
	return request({
		url: `/api/form/custom/design/${id}`,
		method: 'get'
	});
}

/**通过taskid获取流程变量 */
export function getVariables(data) {
	return request({
		url: `/api/flow/task/getVariables`,
		method: 'post',
		isFormData: true,
		data
	});
}
/**通过流程实例获取流程变量*/
export function getHisVariables(data) {
	return request({
		url: `/api/flow/task/getHisVariables`,
		method: 'post',
		isFormData: true,
		data
	});
}
