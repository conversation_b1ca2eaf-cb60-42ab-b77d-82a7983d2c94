import request from '@/utils/request';

/**获取组件分类*/
export function getComponentType(params) {
	return request({
		url: '/sys/slot/access/categorys/search',
		method: 'get',
		params
	});
}

/**获取组件分类下面的列表*/
export function getListByType(params) {
	return request({
		url: '/sys/slot/access/applications/search',
		method: 'get',
		cancelKey: 'getListByType',
		params
	});
}

/**获取分类下面具体的组件列表*/
export function getComponentsList(params) {
	return request({
		url: '/sys/slot/accesss/search',
		method: 'get',
		cancelKey: 'getComponentsList',
		params
	});
}

/**保存设置好的布局*/
export function saveComponentList(data) {
	return request({
		url: '/sys/slot/instances',
		method: 'put',
		cancelKey: 'saveComponentList',
		data
	});
}

/**获取设置好的布局*/
export function getComponentList(params) {
	return request({
		url: '/api/sys/slot/instances/search',
		method: 'get',
		cancelKey: 'getComponentList',
		params
	});
}

/**获取工作台视图列表（新）*/
export function getModeList(params) {
	return request({
		url: '/api/sys/workbenchs/search',
		method: 'get',
		cancelKey: 'getModeList',
		params
	});
}

/**根据id获取视图配置（新）*/
export function getConfigByModeId(objectType, objectId, params) {
	return request({
		url: `/api/sys/slot/${objectType}/${objectId}/instances/search`,
		method: 'get',
		cancelKey: 'getConfigByModeId',
		params
	});
}
