import request from '@/utils/request';

/**待办列表 */
export function handleList(params) {
	return request({
		url: '/api/task/tasks/search',
		method: 'GET',
		params,
		cancelKey: 'handleList'
	});
}
/** 工作台待办 */
export function workHandleList(params) {
	return request({
		url: '/api/task/apps',
		method: 'GET',
		params
	});
}
/** 工作台待办统计 */
export function statsForCondition(data) {
	return request({
		url: '/api/task/statsForCondition',
		method: 'POST',
		data: data
	});
}

/** 获取待办未办理总数 */
export function getTaskStats(params) {
	return request({
		url: '/api/task/stats',
		method: 'GET',
		params
	});
}
export function getMenuConfig() {
	return request({
		url: '/api/task/menuConfig',
		method: 'GET'
	});
}
export function getTenantConfig(applicationId) {
	return request({
		url: `/api/sys/application/tenantConfig/${applicationId}`,
		method: 'GET'
	});
}
//数据字典
export function getDicts(code) {
	return request({
		url: `/sys/dicts/${code}`,
		method: 'get'
	});
}

//数据字典
export function myAgents() {
	return request({
		url: `/api/sys/userSecretary/myAgents`,
		method: 'get'
	});
}
export function businessTypes(params) {
	return request({
		url: `/api/task/businessTypes`,
		method: 'get',
		params
	});
}
//记录行为
export function userInteraction(data) {
	return request({
		url: 'api/sys/userInteraction',
		method: 'put',
		data
	});
}
