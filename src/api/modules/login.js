import request from '@/utils/request';

/**获取公钥*/
export function getEncryptKey(params) {
	return request({
		url: '/api/sys/encryptKey',
		method: 'get',
		params
	});
}
/**登录*/
export function login(data) {
	return request({
		url: '/api/sys/login',
		method: 'post',
		data
	});
}

/**自动登录*/
export function autoLogin(code) {
	return request({
		url: `/api/sys/thirdAuth/login/${code}`,
		method: 'get'
	});
}

/**退出登录*/
export function loginOut(params) {
	return request({
		url: '/api/sys/logout',
		method: 'get',
		params
	});
}

/**
 * 应用系统退出登录地址集合，opt-获取方式(code:用户端主动退出时根据临时获取三方退出地址，authInvalid:用户端认证过期时根据用户端的用户身份信息获取第三方退出地址),params-用户退出参数
 * opt=code时参数为{code:临时码}
 * opt=authInvalid时参数为{userId:用户编号,token:用户令牌,tenantId:租户环境}
 */
export function appsLogout(opt, params) {
	return request({
		url: `/api/sys/sso/appsLogout/${opt}`,
		method: 'get',
		params: params
	});
}

/**切换租户*/
export function changeRent(newTenantId) {
	return request({
		url: `/api/sys/switch/tenant/${newTenantId}`,
		method: 'get'
	});
}
/**切换身份*/
export function switchIdentity(newIdentityId) {
	return request({
		url: `/api/sys/switch/identity/${newIdentityId}`,
		method: 'get'
	});
}

/**更新信息*/
export function updateInfo(data) {
	return request({
		url: `/api/sys/user/self`,
		method: 'put',
		data
	});
}

/**校验密码*/
export function checkPassword(params) {
	return request({
		url: `/api/sys/pwd/check`,
		method: 'get',
		params
	});
}

/**发送手机验证码*/
export function sendCode(channel) {
	return request({
		url: `/api/sys/verifyCode/send/${channel}`,
		method: 'get'
	});
}

/**验证码校验*/
export function checkCode(params) {
	return request({
		url: `/api/sys/verifyCode/check`,
		method: 'get',
		params
	});
}

/**更新密码*/
export function savePassword(data) {
	return request({
		url: `/api/sys/user/self/pwd`,
		method: 'put',
		data
	});
}

/**获取菜单*/
export function getMenuList(params) {
	return request({
		url: `/api/sys/permission/clientMenu`,
		method: 'get',
		params
	});
}

/**获取租户配置信息*/
export function getTenantConfig({ tenantId, key }) {
	return request({
		url: `/api/sys/common/tenantConfig/${tenantId}/${key}`,
		method: 'get'
	});
}

/** 短信登录 */
export function loginCode(params, tenantId) {
	return request({
		url: '/api/sys/shortMessage/sendVerificationCode',
		method: 'get',
		headers: {
			'X-Coos-Client-Tenant-Id': `${tenantId}`
		},
		params
	});
}
/** 验证码登录 */
export function codeToLogin(params) {
	return request({
		url: '/api/sys/verificationCodeLogin',
		method: 'post',
		headers: {
			'content-type': 'application/json'
		},
		data: params
	});
}

/** 扫码登录 */
export function qrCodeLogin(params) {
	return request({
		url: '/api/sys/qrLoginInit',
		method: 'get',
		params
	});
}

/** 扫码登录确认 */
export function isQrCodeLogin(code) {
	return request({
		url: `/api/sys/qrLogin/${code}`,
		method: 'get'
	});
}

/** 获取文件大小限制 */
export function getFileMaxSize(code = 'diskFileSetting') {
	return request({
		url: `/api/sys/globalSettings/${code}`,
		method: 'get'
	});
}

/**忘记密码-发送验证码*/
export function verifyCodeSend(data) {
	return request({
		url: '/api/sys/forgetPwd/verifyCode/send',
		headers: {
			'content-type': 'application/json'
		},
		method: 'post',
		data
	});
}

/**忘记密码-提交重置密码*/
export function forgetPwd(data) {
	return request({
		url: `/api/sys/forgetPwd`,
		method: 'put',
		data
	});
}

/**客户端-获取应用依赖接口调用的token*/
export function relyToken(tenantId) {
	return request({
		url: `/api/sys/application/rely/token`,
		method: 'get',
		params: { tenantId }
	});
}
//获取用户行为验证码
export function getUserCode(params) {
	return request({
		url: '/api/sys/userActionVerifyCode',
		method: 'get',
		params
	});
}

// 判断是否需要用户行为验证码
export function isNeedUserActionVerifyCode() {
	return request({
		url: '/api/sys/userActionVerifyCode/need',
		method: 'get'
	});
}
// 检查是否有公众号关注功能
export function wxMpSubscribeCheck() {
	return request({
		url: '/api/sys/common/wxMp/subscribe/check',
		method: 'get'
	});
}
// 获取公众号关注二维码图片(base64)
export function wxMpSubscribeQrCode() {
	return request({
		url: '/api/sys/common/wxMp/subscribe/qrCode',
		method: 'get'
	});
}

// 应用列表
export function applications() {
	return request({
		url: `/api/sys/applications/search`,
		method: 'get'
	});
}

//  默认租户信息
export function defaultTenantId() {
	return request({
		url: `/api/sys/globalSettings/defaultTenantId`,
		method: 'get'
	});
}
