import request from '@/utils/request';
/** 获取文本校对质量类型接口 */
export function getProofreadQualityType(params) {
	return request({
		url: '/api/robot/writeProofread/getProofreadQualityType',
		method: 'GET',
		params
	});
}

/**
 * 文本校对接口
 * @param {Object} data - 请求数据
 * @param {string} data.text - 需要校对的文本
 */
export function proofreadText(data) {
	return request({
		url: '/api/robot/writeProofread/proofread',
		method: 'POST',
		timeout: 60 * 1000,
		data
	});
}

/**
 * 删除校对信息接口
 * @param {Array} data - 校对信息ID列表
 */
export function deleteProofreadInfo(data) {
	return request({
		url: '/api/robot/writeProofread',
		method: 'DELETE',
		data
	});
}

/**
 * 校对详细内容接口
 * @param {string|number} id - 校对内容的ID
 */
export function getProofreadDetail(id) {
	return request({
		url: `/api/robot/writeProofread/${id}`,
		method: 'GET'
	});
}

/**
 * 校对分页列表信息接口
 * @param {Object} params - 查询参数
 * @param {number} params.pageNo - 页码
 * @param {number} params.pageSize - 每页大小
 * @param {Object} data - 请求数据
 */
export function getProofreadList(params, data) {
	return request({
		url: '/api/robot/writeProofread/search',
		method: 'GET',
		params,
		data
	});
}

/**
 * 更新校对信息接口
 * @param {Object} data - 请求数据
 */
export function updateDealResult(data) {
	return request({
		url: '/api/robot/writeProofread/updateDealResult',
		method: 'post',
		data
	});
}

/**
 * 保存黑白名单数据（新增或更新）
 * @param {Object} data - 请求数据
 * @param {string} data.type - 类型（black=黑名单，white=白名单）
 * @param {string} data.targetWord - 目标字词
 * @param {string} data.remark - 备注（黑名单：正确字词；白名单：原因）
 * @param {string[]} [data.tags] - 词库标签（可选）
 * @param {string} [data.proofreadId] - 校对记录ID（可选）
 */
export function saveWhiteBlackList(data) {
	return request({
		url: '/api/robot/writeProofreadWb/save',
		method: 'POST',
		headers: {
			'Content-Type': 'application/json'
		},
		data
	});
}
/**
 * 删除黑白名单数据
 * @param {Object} data - 请求数据
 * @param {string[]} data.ids - 需要删除的黑白名单ID列表
 */
export function deleteWhiteBlackList(data) {
	return request({
		url: '/api/robot/writeProofreadWb',
		method: 'DELETE',
		data
	});
}
/**
 * 获取黑白名单详细信息
 * @param {string|number} id - 黑白名单记录ID
 */
export function getWhiteBlackListDetail(id) {
	return request({
		url: `/api/robot/writeProofreadWb/${id}`,
		method: 'GET'
	});
}
/**
 * 分页查询黑白名单记录（支持条件筛选）
 * @param {Object} params - 查询参数
 * @param {number} [params.pageNo=1] - 页码
 * @param {number} [params.pageSize=10] - 每页数量
 * @param {string} [params.targetWord] - 目标字词（模糊匹配）
 */
export function getWhiteBlackListPage(params) {
	return request({
		url: '/api/robot/writeProofreadWb/search',
		method: 'GET',
		params
	});
}
/**
 * 更新校对记录标题接口
 * @param {Object} data - 请求数据
 * @param {string} data.id - 校对记录ID
 * @param {string} data.title - 新标题
 */
export function updateProofreadTitle(data) {
	return request({
		url: '/api/robot/writeProofread/updateTitle',
		method: 'POST',
		data
	});
}
