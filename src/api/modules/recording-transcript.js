import request from '@/utils/request';

/**会议列表*/
export function meetingRecordList(params) {
	return request({
		url: `/api/robot/meetingRecord/search`,
		method: 'get',
		params
	});
}
/**会议详情*/
export function meetingRecordDetails(id) {
	return request({
		url: `/api/robot/meetingRecord/${id}`,
		method: 'get'
	});
}
/**保存会议记录/纪要*/
export function meetingRecordEdit(data) {
	return request({
		url: `/api/robot/meetingRecord/content`,
		method: 'PUT',
		data
	});
}
/**会议删除*/
export function meetingRecordDel(params) {
	return request({
		url: `/api/robot/meetingRecord`,
		method: 'delete',
		data: params
	});
}
/**会议新增*/
export function meetingRecordAdd(data) {
	return request({
		url: `/api/robot/meetingRecord`,
		method: 'POST',
		data
	});
}
/**重新解析*/
export function meetingRecordReParser(id, optType) {
	return request({
		url: `/api/robot/meetingRecord/reParser/${id}?optType=${optType}`,
		method: 'get'
	});
}
/**保存会议纪要/内容-下载*/
export function exportContent(params) {
	return request({
		url: `/api/robot/meetingRecord/exportContent`,
		method: 'get',
		params
	});
}
