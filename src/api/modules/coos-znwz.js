import request from '@/utils/request';

/**智能问政点赞*/
export function addDgg(data) {
	return request({
		url: '/api/robot/v2/addQACache',
		method: 'post',
		data,
		isFormData: true
	});
}

/**智能问政取消点赞*/
export function delDgg(data) {
	return request({
		url: '/api/robot/v2/delQACache',
		method: 'post',
		data,
		isFormData: true
	});
}

/**是否存在缓存*/
export function existsCache(data) {
	return request({
		url: '/api/robot/v2/getQACache',
		method: 'post',
		data,
		isFormData: true
	});
}

/**是否点赞*/
export function existsDgg(data) {
	return request({
		url: '/api/robot/v2/existsQACache',
		method: 'post',
		data
	});
}

/**获取写作的标题推荐*/
export function getWriteHistory(data) {
	return request({
		url: '/aihub/aihubDocRecord/listJson',
		method: 'get',
		data
	});
}
/**未知功能*/
export function questionsSearch(data) {
	return request({
		url: '/api/robot/agent/questions/search',
		method: 'get',
		params: data
	});
}

/**获取写作的历史记录*/
export function getHistory(data) {
	return request({
		url: '/api/robot/writeManuscript/search',
		method: 'get',
		params: data
	});
}
