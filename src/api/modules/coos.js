import { baseUrl } from '@/config';
import request from '@/utils/request';
import { get_token, getToken } from '@/utils/auth';
import fetchRequest from '@/utils/fetch-request';

/**AI大模式知识库对话*/
export function aiBigMode(body) {
	let pre = process.env['VUE_APP_ENV'] === 'development' ? '/api' : baseUrl;
	return fetch(pre + '/chat/knowledge_base_chat', {
		method: 'post',
		body: JSON.stringify(body),
		headers: {
			'Content-Type': 'application/json'
		}
	}).catch(reason => {
		if (reason.name === 'AbortError') {
			console.log('Request aborted');
		} else {
			console.log('Fetch error:', reason);
		}
	});
}

/**AI大模式直接对话*/
export function aiQueryAnswer(body) {
	let pre = process.env['VUE_APP_ENV'] === 'development' ? '/api' : baseUrl;
	return fetch(pre + '/chat/chat', {
		method: 'post',
		body: JSON.stringify(body),
		headers: {
			'Content-Type': 'application/json',
			'X-Coos-Client-Access-Token': getToken(),
			'X-Coos-Client-Tenant-Id': get_token('X-Coos-Client-Tenant-Id')
		}
	}).catch(reason => {
		if (reason.name === 'AbortError') {
			console.log('Request aborted');
		} else {
			console.log('Fetch error:', reason);
		}
	});
}

export function newAiQueryStreamToString(body, signal) {
	let url = baseUrl + '/api/robot/v2/dialog/knowledge/base/writing/streamToString';
	return fetch(url, {
		method: 'post',
		signal,
		body: JSON.stringify(body),
		headers: {
			'Content-Type': 'application/json',
			'X-Coos-Client-Access-Token': getToken(),
			'X-Coos-Client-Tenant-Id': get_token('X-Coos-Client-Tenant-Id')
		}
	}).catch(reason => {
		if (reason.name === 'AbortError') {
			console.log('Request aborted');
		} else {
			console.log('Fetch error:', reason);
		}
	});
}
/**AI大模式直接对话*/
export function newAiQuery(body, signal) {
	let url = baseUrl + '/api/robot/dialog/chat/answer/streamToString';
	return fetch(url, {
		method: 'post',
		signal,
		body: JSON.stringify(body),
		headers: {
			'Content-Type': 'application/json',
			'X-Coos-Client-Access-Token': getToken(),
			'X-Coos-Client-Tenant-Id': get_token('X-Coos-Client-Tenant-Id')
		}
	}).catch(reason => {
		if (reason.name === 'AbortError') {
			console.log('Request aborted');
		} else {
			console.log('Fetch error:', reason);
		}
	});
}
/**AI大模式直接对话*/
export function newV2AiQuery(body, signal) {
	let url = baseUrl + '/api/robot/v2/dialog/chat/answer/streamToString';
	return fetch(url, {
		method: 'post',
		signal,
		body: JSON.stringify(body),
		headers: {
			'Content-Type': 'application/json',
			'X-Coos-Client-Access-Token': getToken(),
			'X-Coos-Client-Tenant-Id': get_token('X-Coos-Client-Tenant-Id')
		}
	}).catch(reason => {
		if (reason.name === 'AbortError') {
			console.log('Request aborted');
		} else {
			console.log('Fetch error:', reason);
		}
	});
}

/**AI大模式直接对话（封装版本）*/
export function newFetchAiQuery(body, signal) {
	let url = baseUrl + '/api/robot/v2/dialog/chat/answer/streamToString';
	return fetchRequest(url, {
		signal,
		body: JSON.stringify(body)
	});
}

/**AI待办对话*/
export function getWorkAiAnswerBySocket(body) {
	return fetch(baseUrl + '/api/robot/dialog/chat/business/answer/streamToString', {
		method: 'post',
		body: JSON.stringify(body),
		headers: {
			'Content-Type': 'application/json',
			'X-Coos-Client-Access-Token': getToken(),
			'X-Coos-Client-Tenant-Id': get_token('X-Coos-Client-Tenant-Id')
		}
	}).catch(reason => {
		if (reason.name === 'AbortError') {
			console.log('Request aborted');
		} else {
			console.log('Fetch error:', reason);
		}
	});
}

/**AI我的空间对话*/
export function getSpaceAiAnswerBySocket(body) {
	return fetch(baseUrl + '/api/robot/dialog/chat/person/knowledge/answer/streamToString ', {
		method: 'post',
		body: JSON.stringify(body),
		headers: {
			'Content-Type': 'application/json',
			'X-Coos-Client-Access-Token': getToken(),
			'X-Coos-Client-Tenant-Id': get_token('X-Coos-Client-Tenant-Id')
		}
	}).catch(reason => {
		if (reason.name === 'AbortError') {
			console.log('Request aborted');
		} else {
			console.log('Fetch error:', reason);
		}
	});
}
/**直接大模型对话，保存对话接口*/
export function saveAiQueryAnswer(data) {
	return request({
		url: '/api/robot/dialog/chat',
		method: 'post',
		data
	});
}

/**选择完插件的请求初始数据*/
export function getChangePluginInit(data) {
	return request({
		url: `/api/robot/dialog/chat/centerControl/getEmbedForm`,
		method: 'post',
		data
	});
}

/**获取文档来源*/
export function getAnswerFrom(id) {
	return request({
		url: `/api/robot/dialog/chat/${id}`,
		method: 'get'
	});
}

/**智控模式对话*/
export function pluginModelAi(data) {
	return request({
		url: `/api/robot/dialog/chat/centerControl/answer/`,
		method: 'post',
		timeout: 30 * 1000,
		data
	});
}

/**获取对话的UUID*/
export function getUUID() {
	return request({
		url: `/api/sys/common/generateId`,
		method: 'get',
		params: { num: 1 }
	});
}
/**coos聊天记录*/
export function getCoosHistory(params) {
	return request({
		url: '/api/robot/dialog/chats/search',
		method: 'get',
		params
	});
}

/**提问，获取知识库*/
export function getQueryQueen(params) {
	return request({
		url: '/api/robot/dialog/chat/disk/spaces/search',
		method: 'get',
		timeout: 30 * 1000,
		params
	});
}

/**通过知识库获取答案*/
export function getQueryByQueen(data) {
	return request({
		url: '/api/robot/dialog/chat/answer/from',
		method: 'post',
		data
	});
}

/**办公助手*/
export function getQueryByAdministrativeAssistant(data, signal) {
	return fetch(baseUrl + '/api/robot/chat/stream', {
		method: 'post',
		signal,
		body: JSON.stringify(data),
		headers: {
			'Content-Type': 'application/json',
			'X-Coos-Client-Access-Token': getToken(),
			'X-Coos-Client-Tenant-Id': get_token('X-Coos-Client-Tenant-Id')
		}
	}).catch(reason => {
		if (reason.name === 'AbortError') {
			console.log('Request aborted');
		} else {
			console.log('Fetch error:', reason);
		}
	});
}

/**保存AI对话记录*/
export function saveCoosHistory(id, data) {
	return request({
		url: `/api/robot/dialog/chat/${id}`,
		method: 'put',
		data
	});
}

/**删除AI对话记录*/
export function delCoosHistory(ids) {
	return request({
		url: `/api/robot/dialog/chats/${ids}`,
		method: 'delete'
	});
}

/**获取知识库*/
export function getKnowledge(params) {
	return request({
		url: `/api/disk/spaces/myShare`,
		method: 'get',
		params
	});
}

/**创建对话*/
export function createMessage(data) {
	return request({
		url: `/api/robot/session`,
		method: 'post',
		data
	});
}

/**获取对话记录列表*/
export function getChatList(params) {
	return request({
		url: `/api/robot/sessions/search`,
		method: 'get',
		params
	});
}

/**删除对话记录列表*/
export function delChatList(ids) {
	return request({
		url: `/api/robot/sessions/${ids}`,
		method: 'delete'
	});
}

/**修改对话标题*/
export function updateTittle(id, data) {
	return request({
		url: `/api/robot/session/${id}`,
		method: 'put',
		data
	});
}

/**获取最新的系统消息*/
export function getNewSystemMessage(params) {
	return request({
		url: `/api/message/msg/latest`,
		method: 'get',
		params
	});
}

/**获取系统消息的列表*/
export function getSystemMessageList(params) {
	return request({
		url: `/api/message/msgs/search`,
		method: 'get',
		params
	});
}

/**
 * 推荐插件*/
export function somePlugins(params) {
	return request({
		url: `/api/sys/application/functions/search`,
		method: 'get',
		params
	});
}

/**
 * 全部插件*/
export function allPlugins(params) {
	return request({
		url: `/api/sys/application/category/functions/search`,
		method: 'get',
		params
	});
}

/** 获取表单数据 */
export function getFormData(id) {
	return request({
		url: `/api/robot/dialog/chat/data/instance/${id}`,
		method: 'get'
	});
}

/**更新表单数据*/
export function updateFormData(id, data) {
	return request({
		url: `/api/robot/dialog/chat/data/instance/${id}`,
		method: 'put',
		data
	});
}

/**列表智能对话*/
export function getListAiQuery(data) {
	return request({
		url: `/api/robot/tableChat/aiChat`,
		method: 'post',
		data,
		timeout: 30 * 1000
	});
}

/**查询表数据*/
export function getTableDataBySql(data) {
	return request({
		url: `/api/robot/tableChat/findByChatSql`,
		method: 'post',
		data,
		timeout: 30 * 1000
	});
}

/**动态列表会话获取表结构*/
export function getListHeader(data) {
	return request({
		url: `/api/robot/tableChatCfg/findByIds`,
		method: 'post',
		data
	});
}

/**COOS上传文件*/
export function uploadCoosFile() {
	return {
		customUploadFileHandler: (data, onUploadProgress, cancelKey) => {
			return request({
				url: '/api/robot/uploadFileChat',
				method: 'post',
				isFormData: true,
				data,
				onUploadProgress,
				cancelKey,
				timeout: 30 * 1000
			});
		},
		customUploadFileCancel: cancelKey => {
			request.customCancel(cancelKey);
		}
	};
}

/**获取常用问题清单*/
export function getQuestionsList(params) {
	return request({
		url: `/api/robot/dialog/presetQuestions/search`,
		method: 'GET',
		params
	});
}

/**获取图表配置*/
export function getChartConfig(data) {
	return request({
		url: `/api/robot/tableChat/dataToChats`,
		method: 'post',
		data,
		timeout: 30 * 1000
	});
}
/**聊天历史消息*/
export function messagesHistory(params) {
	return request({
		url: `/api/message/im_app_server/messages/history`,
		method: 'GET',
		params
	});
}

/**数据智析模式*/
export function geDataDrillingAiAnswer(body) {
	return fetch(baseUrl + '/api/robot/dialog/chat/businessData/answer/streamToString', {
		method: 'post',
		body: JSON.stringify(body),
		headers: {
			'Content-Type': 'application/json',
			'X-Coos-Client-Access-Token': getToken(),
			'X-Coos-Client-Tenant-Id': get_token('X-Coos-Client-Tenant-Id')
		}
	}).catch(reason => {
		if (reason.name === 'AbortError') {
			console.log('Request aborted');
		} else {
			console.log('Fetch error:', reason);
		}
	});
}

/**联网搜索*/
export function getNetworkAnswer(body, signal) {
	return fetch(baseUrl + '/api/robot/dialog/chat/webSearch/answer/streamToString', {
		method: 'post',
		signal,
		body: JSON.stringify(body),
		headers: {
			'Content-Type': 'application/json',
			'X-Coos-Client-Access-Token': getToken(),
			'X-Coos-Client-Tenant-Id': get_token('X-Coos-Client-Tenant-Id')
		}
	}).catch(reason => {
		if (reason.name === 'AbortError') {
			console.log('Request aborted');
		} else {
			console.log('Fetch error:', reason);
		}
	});
}
/**获取知识钻取的列表*/
export function getDataDrilling(data) {
	return request({
		url: `/api/robot/dialog/business/config/page`,
		method: 'post',
		data
	});
}

/**获取欢迎语*/
export function getWelcomeText(applicationId, permsCode) {
	return request({
		url: `/api/sys/application/function/${applicationId}/${permsCode}`,
		method: 'get'
	});
}

/**根据答案寻找模型*/
export function submitSuggestions(data) {
	return request({
		url: `/api/robot/v2/feedback`,
		method: 'post',
		data
	});
}

/**指标查询接口*/
export function dataInterpretationV2(data) {
	return request({ url: `/api/robot/v2/dataInterpretation`, method: 'post', data });
}

/**根据答案寻找模型-外部页面*/
export function getModelV2(data) {
	return request({ url: '/api/robot/v2/mode', data, method: 'post' });
}

/**智能问政*/
export function getPoliticsBySocket(body, signal) {
	return fetch(baseUrl + '/api/robot/v2/dialog/knowledge/base/policy/chat/answer/streamToString', {
		method: 'post',
		signal,
		body: JSON.stringify(body),
		headers: {
			'Content-Type': 'application/json',
			'X-Coos-Client-Access-Token': getToken(),
			'X-Coos-Client-Tenant-Id': get_token('X-Coos-Client-Tenant-Id')
		}
	}).catch(reason => {
		if (reason.name === 'AbortError') {
			console.log('Request aborted');
		} else {
			console.log('Fetch error:', reason);
		}
	});
}
/**智能问数*/
export function getQueryNumber(body) {
	return fetch(baseUrl + '/api/robot/v2/ask/data/streamToString', {
		method: 'post',
		body: JSON.stringify(body),
		headers: {
			'Content-Type': 'application/json',
			'X-Coos-Client-Access-Token': getToken(),
			'X-Coos-Client-Tenant-Id': get_token('X-Coos-Client-Tenant-Id')
		}
	}).catch(reason => {
		if (reason.name === 'AbortError') {
			console.log('Request aborted');
		} else {
			console.log('Fetch error:', reason);
		}
	});
}
/**tosql*/
export function getDataInterpretationBySocket(body, signal) {
	return fetch(baseUrl + '/api/robot/v2/dataInterpretation/tosql/streamToString', {
		method: 'post',
		signal,
		body: JSON.stringify(body),
		headers: {
			'Content-Type': 'application/json',
			'X-Coos-Client-Access-Token': getToken(),
			'X-Coos-Client-Tenant-Id': get_token('X-Coos-Client-Tenant-Id')
		}
	}).catch(reason => {
		if (reason.name === 'AbortError') {
			console.log('Request aborted');
		} else {
			console.log('Fetch error:', reason);
		}
	});
}

// 获取对话类型数据
export function getAgents(params) {
	return request({ url: `/api/robot/agentInfos/search`, params, method: 'get' });
}
// 判断对话类型
export function getAgentsId(data) {
	return request({ url: `/api/robot/chat/recommendAgent`, data, method: 'post' });
}

/**获取扩展阅读信源*/
export function getAnswerExtend(data) {
	return request({
		url: `/api/robot/v2/dialog/knowledge/base/policy/chat/answer/extend`,
		data,
		method: 'post'
	});
}
