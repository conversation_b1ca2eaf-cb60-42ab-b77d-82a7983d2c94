import request from '@/utils/request';
import { baseUrl } from '@/config';
import fetchRequest from '@/utils/fetch-request';
/**快速生成大纲*/
export function quicklyProOutline() {
	return {
		quicklyProOutlineRequest: data => {
			return request({
				url: `/api/robot/writeManuscript/outlineFast`,
				data,
				method: 'post',
				timeout: 3000 * 1000,
				cancelKey: 'quicklyProOutline'
			});
		},
		quicklyProOutlineCancel: cancelKey => {
			request.customCancel(cancelKey);
		}
	};
}

/**删除文稿*/
export function deleteDoc(data) {
	return request({ url: `/api/robot/writeManuscript`, data, method: 'delete' });
}

/**一键匹配大纲*/
export function getQuicklyOutline(id) {
	return request({
		url: `/api/robot/writeManuscript/outlineMatchingMeta/${id}`,
		method: 'get',
		timeout: 3000 * 1000,
		cancelKey: 'quicklyProOutline'
	});
}

/**获取信源*/
export function getSourceDetail(id, params) {
	return request({
		url: `/api/robot/writeManuscript/${id}`,
		params,
		method: 'get',
		cancelKey: 'getSourceDetail'
	});
}

/**保存大纲*/
export function saveOutline(data, id) {
	return request({
		url: `/api/robot/writeManuscript/${id}`,
		method: 'post',
		data
	});
}

/**生成内容*/
export function generateFullText(body, signal) {
	let url = baseUrl + '/api/robot/writeManuscript/content';
	return fetchRequest(url, {
		signal,
		body: JSON.stringify(body)
	});
}

/**生成大纲*/
export function getOutlineTitle(body, signal) {
	let url = baseUrl + '/api/robot/writeManuscript/outline';
	return fetchRequest(url, {
		signal,
		body: JSON.stringify(body)
	});
}

/**大纲智能对话(SSE版本)*/
export function outlineAi(body, signal) {
	let url = baseUrl + '/api/robot/writeManuscript/outlineAdjust';
	return fetchRequest(url, {
		signal,
		body: JSON.stringify(body)
	});
}

/**获取设置*/
export function getSettings(id, params) {
	return request({
		url: `/api/robot/writeManuscript/userCustomSetting`,
		params,
		method: 'get',
		cancelKey: 'getSettings'
	});
}

/**获取设置*/
export function saveSettings(data) {
	return request({
		url: `/api/robot/writeManuscript/userCustomSetting`,
		data,
		method: 'put',
		cancelKey: 'saveSettings'
	});
}

/**获取配置信息*/
export function getConfigJson(code, params) {
	return request({
		url: `/api/sys/globalSettings/configJson/${code}`,
		params,
		method: 'get',
		cancelKey: 'getConfigJson'
	});
}
