import request from '@/utils/request';
import qs from 'qs';

// 获取模版
export function templateGetList(data) {
	return request({
		url: '/api/robot/ppt/template/getList',
		method: 'post',
		data
	});
}

// AI创建PPT大钢接口
export function createPPTLargeSteel(data) {
	return request({
		url: '/api/robot/createPPTLargeSteel',
		method: 'post',
		data,
		timeout: 5000000
	});
}

// AI创建PPT大钢接口
export function createFile(data) {
	return request({
		url: '/api/robot/ppt/createFile',
		method: 'post',
		data: qs.stringify(data),
		timeout: 5000000
	});
}
/** 上传文件生成ppt */
export function uploadPPTLargeSteel(params) {
	return request({
		url: `/api/robot/uploadPPTLargeSteel`,
		method: 'post',
		isFormData: true,
		headers: {
			'content-type': 'application/json'
		},
		timeout: 5000000,
		data: params
	});
}
//
