import request from '@/utils/request';

/**部门统计用户访问量*/
export function deptPage(data) {
	return request({
		url: `/api/agent/track/deptPage`,
		method: 'post',
		data
	});
}
/**部门统计用户访问量汇总*/
export function deptCount(data) {
	return request({
		url: `/api/agent/track/deptCount`,
		method: 'post',
		data
	});
}
/**用户统计访问量汇总*/
export function userCount(data) {
	return request({
		url: `/api/agent/track/userCount`,
		method: 'post',
		data
	});
}
/**用户统计访问量*/
export function userPage(data) {
	return request({
		url: `/api/agent/track/userPage`,
		method: 'post',
		data
	});
}
