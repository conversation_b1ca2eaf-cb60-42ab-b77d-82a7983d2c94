import request from '@/utils/request';
// 项目列表
const projectApi = {
	add: data => {
		return request({
			url: '/api/project/base',
			method: 'post',
			data
		});
	},
	// 批量删除
	batchDlt: data => {
		return request({
			url: '/api/project/base/deleteBatchIds',
			method: 'post',
			data
		});
	},
	// 删除
	dlt: id => {
		return request({
			url: `/api/project/base/${id}`,
			method: 'DELETE'
		});
	},
	// 编辑
	edit: data => {
		return request({
			url: '/api/project/base/edit',
			method: 'put',
			data
		});
	},
	// 分页列表
	page: data => {
		return request({
			url: '/api/project/base/page',
			method: 'post',
			data
		});
	},
	// 详情
	detail: id => {
		return request({
			url: `/api/project/base/${id}`,
			method: 'get'
		});
	}
};

// 任务管理
const taskApi = {
	add: data => {
		return request({
			url: '/api/task/info',
			method: 'post',
			data
		});
	},
	// 批量创建
	batchAdd: data => {
		return request({
			url: '/api/task/info/batchAdd',
			method: 'post',
			data
		});
	},
	// 批量删除
	batchDlt: data => {
		return request({
			url: '/api/task/info/deleteBatchIds',
			method: 'post',
			data
		});
	},
	// 删除
	dlt: id => {
		return request({
			url: `/api/task/info/${id}`,
			method: 'DELETE'
		});
	},
	// 编辑
	edit: data => {
		return request({
			url: '/api/task/info/edit',
			method: 'put',
			data
		});
	},
	// 修改任务完成状态
	changeStatus: data => {
		return request({
			isFormData: 1,
			url: '/api/task/info/setStatus',
			method: 'post',
			data
		});
	},
	// 分页列表
	page: data => {
		return request({
			url: '/api/task/info/page',
			method: 'post',
			data
		});
	},
	// 详情
	detail: id => {
		return request({
			url: `/api/task/info/${id}`,
			method: 'get'
		});
	}
};

// 任务进度管理
const taskScheduleApi = {
	add: data => {
		return request({
			url: '/api/task/progress',
			method: 'post',
			data
		});
	},
	// 批量删除
	batchDlt: data => {
		return request({
			url: '/api/task/progress/deleteBatchIds',
			method: 'post',
			data
		});
	},
	// 删除
	dlt: id => {
		return request({
			url: `/api/task/progress/${id}`,
			method: 'DELETE'
		});
	},
	// 编辑
	edit: data => {
		return request({
			url: '/api/task/progress/edit',
			method: 'put',
			data
		});
	},
	// 分页列表
	page: data => {
		return request({
			url: '/api/task/progress/page',
			method: 'post',
			data
		});
	},
	// 详情
	detail: id => {
		return request({
			url: `/api/task/progress/${id}`,
			method: 'get'
		});
	}
};

// 监控管理
const monitorApi = {
	add: data => {
		return request({
			url: '/api/monitor/resource',
			method: 'post',
			data
		});
	},
	// 批量删除
	batchDlt: data => {
		return request({
			url: '/api/monitor/resource/deleteBatchIds',
			method: 'post',
			data
		});
	},
	// 删除
	dlt: id => {
		return request({
			url: `/api/monitor/resource/${id}`,
			method: 'DELETE'
		});
	},
	// 编辑
	edit: data => {
		return request({
			url: '/api/monitor/resource/edit',
			method: 'put',
			data
		});
	},
	// 分页列表
	page: data => {
		return request({
			url: '/api/monitor/resource/page',
			method: 'post',
			data
		});
	},
	// 详情
	detail: id => {
		return request({
			url: `/api/monitor/resource/${id}`,
			method: 'get'
		});
	}
};

export { projectApi, taskApi, taskScheduleApi, monitorApi };
