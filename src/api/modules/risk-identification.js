import request from '@/utils/request';
import qs from 'qs';

export function sceneSelect(params) {
	return request({
		url: '/api/robot/ri/sceneSelect',
		method: 'get',
		params
	});
}

export function uploadDocs(data) {
	return request({
		url: '/api/robot/ri/uploadDocs',
		method: 'post',
		data,
		timeout: 3600 * 1000
	});
}

export function riskIdentify(data) {
	return request({
		url: '/api/robot/ri/riskIdentify',
		method: 'post',
		data: qs.stringify(data),
		timeout: 3600 * 1000
	});
}
