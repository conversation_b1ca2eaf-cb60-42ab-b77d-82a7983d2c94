import request from '@/utils/request';

/**获取应用管理列表*/
export function getAppAdminList(params) {
	return request({
		url: '/sys/tenant/applications/self/search',
		method: 'get',
		isUmd: true,
		params
	});
}

/**获取更多应用类型*/
export function getMoreAppType(params) {
	return request({
		url: '/api/sys/application/categorys/search',
		method: 'get',
		isUmd: true,
		params
	});
}

/**获取更多应用分类下面具体的数据列表*/
export function getListByType(params) {
	return request({
		url: '/api/sys/applications/search',
		method: 'get',
		isUmd: true,
		cancelKey: 'getListByType',
		params
	});
}

/**用户是否具有该应用的权限*/
export function hasAppRole(params) {
	return request({
		url: '/api/sys/application/checkAuthority',
		method: 'get',
		isUmd: true,
		params
	});
}

/**其他系统的待办事项，动态请求接口*/
export function getTodoList(url, params) {
	return request({
		url,
		method: 'get',
		isUmd: true,
		params
	});
}

/**获取表单小组件的表单列表*/
export function getFormItemList(params) {
	return request({
		url: '/form/custom/defines/published',
		method: 'get',
		params
	});
}

/**获取已经关联的表单*/
export function getFormList(params) {
	return request({
		url: '/api/form/custom/defines/selected',
		method: 'get',
		isUmd: true,
		params
	});
}

/**判断有误权限打开动态表单*/
export function isRoleOpen(params) {
	return request({
		url: '/api/form/custom/define/checkAuthority',
		method: 'get',
		params
	});
}
