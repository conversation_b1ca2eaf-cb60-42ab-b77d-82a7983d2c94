/**
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2024-08-19 08:42:15
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-09-04 10:17:51
 * @FilePath: /coos-desktop-app/src/api/modules/calendar.js
 * @Description:
 */
import request from '@/utils/request';

/** 获取日历主题 */
export function getCalendarType(params) {
	return request({
		url: `/api/sys/slot/accesss/search`,
		method: 'GET',
		params
	});
}

/** 获取字典数据 */
export function getCalendarDirection(dictCodes) {
	return request({
		url: `/sys/dicts/${dictCodes}`,
		method: 'GET'
	});
}

/** 存储日程数据 */
export function saveCalendarData(data) {
	return request({
		url: `/api/schedule`,
		method: 'post',
		data
	});
}

/** 获取日历数据 */
export function getCalendarList(params) {
	return request({
		url: `/api/schedule/schedules/search`,
		method: 'get',
		params
	});
}

/** 删除日历数据 */
export function delCalendarData(id, params) {
	return request({
		url: `/api/schedule/${id}`,
		method: 'delete',
		params
	});
}

/** 获取日程详情 */
export function getCalendarDetail(id, params) {
	return request({
		url: `/api/schedule/${id}`,
		method: 'get',
		params
	});
}

/** 更新日程数据 */
export function upCalendarData(data) {
	return request({
		url: `/api/schedule/${data.id}`,
		method: 'put',
		data
	});
}
/** 获取日程扩展字段配置信息 */
export function getCalendarSlotConfig(data) {
	return request({
		url: `/api/schedule/slotAccess/extraColsConfig`,
		method: 'get',
		data
	});
}

/** 日程标记已完成 */
export function markComplete(id, params) {
	return request({
		url: `/api/schedule/mark/${id}`,
		method: 'put',
		params
	});
}
/** 取消日程标记已完成 */
export function markCancelComplete(id, params) {
	return request({
		url: `/api/schedule/cancelMark/${id}`,
		method: 'put',
		params
	});
}

/** 获取备忘列表 */
export function getWorkMemoList(params) {
	return request({
		url: `/api/schedule/schedules/workMemo/search`,
		method: 'get',
		params
	});
}
