import request from '@/utils/request';

/**组织人员选择组件-获取组织列表*/
export function getOrigTree(params = { includeUsers: false }) {
	return request({
		url: '/api/sys/depart/orgAndDepartTree',
		method: 'get',
		params
	});
}

/**组织人员选择组件-获取人员列表*/
export function getPersonTree(params) {
	return request({
		url: '/api/sys/users/basic/search',
		method: 'get',
		cancelKey: 'getPersonTree',
		params
	});
}

/**组织人员选择组件-获取公司列表*/
export function organizationTree(params) {
	return request({
		url: '/api/sys/organization/tree',
		method: 'get',
		cancelKey: 'organizationTree',
		params
	});
}
/**组织人员选择组件-获取人员标签列表*/
export function getUserLabelList(params) {
	return request({
		url: '/api/sys/label/tree',
		method: 'get',
		params: {
			includeUsers: false
		}
	});
}

/**获取应用可用范围-用于数据回显*/
export function getAvailableRange(params) {
	return request({
		url: '/api/sys/application/authoritys/search',
		method: 'get',
		params
	});
}

/**保存应用可用范围*/
export function saveAvailableRange(applicationId, data) {
	return request({
		url: `/api/sys/application/authoritys/${applicationId}`,
		method: 'put',
		data
	});
}

/**查询文件详情*/
export function getFileDetail(ids) {
	return request({
		url: `/api/sys/files/${ids}`,
		method: 'get'
	});
}

/**自定义的文件上传*/
export function customUploadFile() {
	return {
		customUploadFileHandler: (data, onUploadProgress, cancelKey) => {
			return request({
				url: '/api/sys/file/upload',
				method: 'post',
				isFormData: true,
				onUploadProgress,
				data,
				cancelKey,
				timeout: 3600 * 1000
			});
		},
		customUploadFileCancel: cancelKey => {
			request.customCancel(cancelKey);
		}
	};
}
// 获取水印
export function getWatermark(applicationId) {
	return request({
		url: `/api/sys/application/watermark/${applicationId}`,
		method: 'get'
	});
}

/**获取岗位树结构*/
export function getPostTree(params) {
	return request({
		url: '/sys/post/tree',
		method: 'get',
		params
	});
}

/**岗位分页列表*/
export function getPostList(params) {
	return request({
		url: '/sys/post/page',
		method: 'get',
		params
	});
}

/**获取身份树结构*/
export function getIdentityTree(params) {
	return request({
		url: '/sys/tenant/user/identity/tree',
		method: 'get',
		params
	});
}

/**身份分页列表*/
export function getIdentityList(params) {
	return request({
		url: '/sys/tenant/user/identity/page',
		method: 'get',
		params
	});
}

/**多文件上传*/
export function multipleUpload(data, onUploadProgress) {
	return request({
		url: '/sys/file/uploads',
		method: 'post',
		isFormData: true,
		onUploadProgress,
		data
	});
}
/**部门分页列表*/
export function getDeptList(params) {
	return request({
		url: '/api/sys/userRoles/dept/page',
		method: 'get',
		params
	});
}
/**标签分页列表*/
export function getLabelList(params) {
	return request({
		url: '/api/sys/userRoles/label/page',
		method: 'get',
		params
	});
}
