import request from '@/utils/request';
/** 备忘录统计 */
export function getStateApi(params) {
	return request({
		url: '/api/schedule/chatWorkMemos/stats',
		method: 'get',
		params: params
	});
}

/** 备忘录列表 */
export function getListApi(params) {
	return request({
		url: '/api/schedule/chatWorkMemos/search',
		method: 'get',
		params: params
	});
}

/** 备忘录取消已完成标记 */
export function cancelMarkApi(id) {
	return request({
		url: '/api/schedule/cancelMark/' + id,
		method: 'put'
	});
}

/** 备忘录完成标记 */
export function completeMarkApi(id) {
	return request({
		url: '/api/schedule/mark/' + id,
		method: 'put'
	});
}

/** 备忘录修改 */
export function updateScheduleApi(id, data) {
	return request({
		url: '/api/schedule/' + id,
		method: 'put',
		data: data
	});
}

/** 备忘录列表删除 */
export function deleteScheduleApi(id) {
	return request({
		url: '/api/schedule/' + id,
		method: 'delete'
	});
}

/** 添加为备忘录 */
export function addScheduleApi(data) {
	return request({
		url: '/api/schedule/chatWorkMemos/msgs',
		method: 'post',
		data: data
	});
}

/** 取消为备忘录 */
export function cancelScheduleApi(data) {
	return request({
		url: '/api/schedule/chatWorkMemos/msgs',
		method: 'put',
		data: data
	});
}
