import request from '@/utils/request';
/** 查询分类 */
export function queryCategory() {
	return request({
		url: '/api/sys/client/global/search/accesss',
		method: 'GET'
	});
}
/** 综合搜索 */
export function comprehensiveSearch(params) {
	return request({
		url: '/api/sys/client/global/search',
		method: 'GET',
		params
	});
}
/** 指定分类搜索 */
export function categorySearch(accessApplicationId, params) {
	return request({
		url: `/api/sys/client/search/${accessApplicationId}`,
		method: 'GET',
		params
	});
}
