import request from '@/utils/request';

// 角色分页列表
export function getRoleList(params) {
	return request({
		url: '/api/sys/tenant/roles/search',
		method: 'GET',
		params
	});
}

// 角色详情
export function getRoleMsg(id) {
	return request({
		url: `/api/sys/role/${id}`,
		method: 'GET'
	});
}
// 新增角色
export function addRole(data) {
	return request({
		url: '/api/sys/tenant/role',
		method: 'POST',
		data
	});
}

// 编辑角色
export function editRole(id, data) {
	return request({
		url: `/api/sys/tenant/role/${id}`,
		method: 'PUT',
		data
	});
}

// 角色删除(支持批量)
export function deleteRole(ids) {
	return request({
		url: `/api/sys/roles/${ids}`,
		method: 'DELETE'
	});
}

// 角色成员列表
export function getRoleMemberlist(roleId, params) {
	return request({
		url: `/api/sys/role/${roleId}/users`,
		method: 'GET',
		params: { username: params }
	});
}
//角色成员更新
export function saveRoleMemberlist(roleId, data) {
	return request({
		url: `/api/sys/tenant/role/users/${roleId}`,
		method: 'PUT',
		data: { userIds: data }
	});
}
//角色岗位更新
export function savePostlist(roleId, data) {
	return request({
		url: `/api/sys/tenant/role/posts/${roleId}`,
		method: 'PUT',
		data: { postIds: data }
	});
}
//角色身份更新
export function saveIdentityslist(roleId, data) {
	return request({
		url: `/api/sys/tenant/role/identitys/${roleId}`,
		method: 'PUT',
		data: { identityIds: data }
	});
}
//标签身份更新
export function saveLabelList(roleId, data) {
	return request({
		url: `/api/sys/tenant/role/labels/${roleId}`,
		method: 'PUT',
		data: { labelIds: data }
	});
}
//部门身份更新
export function saveDeptsList(roleId, data) {
	return request({
		url: `/api/sys/tenant/role/depts/${roleId}`,
		method: 'PUT',
		data: { deptIds: data }
	});
}

//复制角色
export function roleCopy(roleId) {
	return request({
		url: `/api/sys/tenant/role/copy/${roleId}`,
		method: 'PUT'
	});
}

// 权限树(指定角色)
const Id = '';

export function getAuthorityTree(id = Id) {
	return request({
		url: `/api/sys/permission/tree/${id}`,
		method: 'GET'
	});
}

// 权限树(完整树)
export function getAllTree(id) {
	return request({
		url: '/api/sys/permission/tree',
		method: 'GET',
		params: { roleId: id, optType: 'openToTenant', limitPerm: true }
	});
}
// 角色成员移除操作
export function deleteRoleMember(ids, params) {
	return request({
		url: `/api/sys/userRoles/${ids}`,
		method: 'DELETE',
		params
	});
}

// 用户所属部门名称查询
export function departNames(userIds) {
	return request({
		url: `/api/sys/users/departNames/${userIds}`,
		method: 'GET'
	});
}

// 组织结构基本信息
export function getOrgInfo(params) {
	return request({
		url: '/api/sys/organization/tree',
		method: 'GET',
		params
	});
}

//组织树基本信息
export function getTreeList(params) {
	return request({
		url: '/api/sys/organization/tree',
		method: 'GET',
		params
	});
}
