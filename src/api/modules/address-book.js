import request from '@/utils/request';

/* 获取设计组成员列表**/
export function getMemberList(params) {
	return request({
		url: '/api/sys/userDepart/users/search',
		method: 'get',
		cancelKey: 'getMemberList',
		params
	});
}
// 菜单数据
export function getTreeList(params) {
	return request({
		url: '/api/sys/depart/tree',
		method: 'get',
		params
	});
}
export function getDetailInfo(id, params) {
	return request({
		url: `/api/sys/userDepart/${id}`,
		method: 'get',
		params
	});
}

/**我的群聊*/
export function getMyGroup(params) {
	return request({
		url: `/api/message/im_app_server/groups/search`,
		method: 'get',
		params,
		cancelKey: 'getMyGroup'
	});
}

/**创建群聊*/
export function createMyGroup(data) {
	return request({
		url: `/api/message/im_app_server/group`,
		method: 'post',
		data
	});
}

/**IM消息免打扰同步记录*/
export function msgReminder(params) {
	return request({
		url: `/api/message/im_app_server/convReminder`,
		method: 'put',
		params: params
	});
}
