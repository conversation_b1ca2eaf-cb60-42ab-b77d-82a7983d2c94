/*
 * @Description: 流程设计器
 * @Version: 1.0
 * @Autor: zhaodongming
 * @Date: 2024-01-22 15:44:38
 * @LastEditors: zhaodongming
 * @LastEditTime: 2024-03-07 10:15:28
 */
import request from '@/utils/request';

export function flowJsonToBpmn(data) {
	return request({
		url: '/form/flow/define',
		method: 'post',
		data: data
	});
}

export function findFlowBpmn(formId) {
	return request({
		url: `/form/flow/define/${formId}`,
		method: 'get'
	});
}

// 流程变量分组
export function groupList() {
	return request({
		url: `/flow/variables/groupList`,
		method: 'get'
	});
}

// 分组编码获取数据类型
export function findByGroup(groupId) {
	return request({
		url: `/flow/variables/findByGroup?groupId=${groupId}`,
		method: 'get'
	});
}

// 分组编码获取数据类型
export function getNotifyType(groupId) {
	return request({
		url: `/flow/data/getNotifyType`,
		method: 'get'
	});
}

export function saveUpdate(data) {
	return request({
		url: `/flow/deploy/saveUpdate`,
		method: 'post',
		data
	});
}
export function postFlowList(data) {
	return request({
		url: `/ext/flow/list`,
		method: 'post',
		isFormData: true,
		data
	});
}

export function getParallelHandler(data) {
	return request({
		url: `/api/flow/task/getParallelHandler`,
		method: 'post',
		isFormData: true,
		data
	});
}

export function getNextHandler(data) {
	return request({
		url: `/api/flow/task/getNextHandler`,
		method: 'post',
		isFormData: true,
		data
	});
}

export function selectById(id) {
	return request({
		url: `/flow/deploy/selectById/${id}`,
		method: 'get'
	});
}

// 获取待办任务列表
export function selectList(data) {
	return request({
		url: `/api/flow/task/selectList`,
		method: 'post',
		data
	});
}
// 获取流程图
export function diagramView(id) {
	return request({
		url: `/api/flow/task/diagramView?processInstanceId=${id}`,
		method: 'get'
	});
}

//获取流程的历史节点 不包含开始结束等
export function selectListHis(data) {
	return request({
		url: `/api/flow/task/selectListHis`,
		method: 'post',
		data,
		timeout: 60000
	});
}
//获取流程 包含开始 线等
export function selectListActivity(data) {
	return request({
		url: `/api/flow/task/selectListActivity`,
		method: 'post',
		data
	});
}

// 获取流程节点配置
export function getNodeSettings(params) {
	return request({
		url: `/api/flow/task/getNodeSettings`,
		method: 'get',
		params
	});
}

// 通过
export function execute(data) {
	return request({
		url: `/api/flow/task/execute`,
		method: 'post',
		data,
		timeout: 60000
	});
}
// 委派归还
export function resolveTask(data) {
	return request({
		url: `/api/flow/task/resolveTask`,
		method: 'post',
		isFormData: true,
		data,
		timeout: 60000
	});
}
// 驳回至开始节点
export function backStart(data) {
	return request({
		url: `/api/flow/runtime/backStart`,
		method: 'post',
		data,
		timeout: 60000
	});
}

// 终止
export function interrupt(data) {
	return request({
		url: `/api/flow/task/interrupt`,
		method: 'post',
		data,
		timeout: 60000
	});
}

// 转办
export function reassign(data) {
	return request({
		url: `/api/flow/task/reassign`,
		method: 'post',
		isFormData: true,
		data,
		timeout: 60000
	});
}
// 委派
export function delegateTask(data) {
	return request({
		url: `/api/flow/task/delegateTask`,
		method: 'post',
		isFormData: true,
		data,
		timeout: 60000
	});
}
// 加签
export function counterSign(data) {
	return request({
		url: `/api/flow/task/counterSign`,
		method: 'post',
		isFormData: true,
		data,
		timeout: 60000
	});
}

export function revoke(data) {
	return request({
		url: `/api/flow/runtime/revoke`,
		method: 'post',
		data,
		isFormData: true,
		timeout: 60000
	});
}
// 获取流程图

export function getFlow(params) {
	return request({
		url: `/api/flow/task/getFlow`,
		method: 'get',
		params
	});
}
export function getProgressStatus(processInstanceId) {
	return request({
		url: `/api/flow/task/getProgressStatus?processInstanceId=${processInstanceId}`,
		method: 'get'
	});
}
export function findByFormId(id, params) {
	return request({
		url: `/api/flow/runtime/findByFromId?formId=${id}`,
		method: 'get',
		params
	});
}
export function getNodeConfig(params) {
	return request({
		url: `/api/flow/runtime/getNodeConfig`,
		method: 'get',
		params
	});
}
export function getUserTaskList(data) {
	return request({
		url: `/api/flow/task/getUserTaskList`,
		isFormData: true,
		method: 'post',
		data
	});
}
export function getNextAssignee(data) {
	return request({
		url: `/api/flow/task/getNextAssignee`,
		isFormData: true,
		method: 'post',
		data
	});
}

export function getTaskIdByBusinessKey(businessKey) {
	return request({
		url: `/api/flow/workbench/business/${businessKey}`,
		method: 'get'
	});
}

export function getCcTask(businessKey) {
	return request({
		url: `/api/flow/ccTask/${businessKey}`,
		method: 'get'
	});
}

//已阅提交
export function setHaveRead(data) {
	return request({
		url: `/api/flow/ccTask/setHaveRead`,
		method: 'post',
		isFormData: true,
		data
	});
}

// 已阅查询
export function selectByTaskInfo(data) {
	return request({
		url: `/api/flow/ccTask/selectByTaskInfo`,
		method: 'post',
		isFormData: true,
		data
	});
}
export function noticeExecute(data) {
	return request({
		url: `/api/flow/task/noticeExecute`,
		method: 'post',
		isFormData: true,
		data
	});
}
export function getBeforeNode(taskId) {
	return request({
		url: `/api/flow/task/getBeforeNode?taskId=${taskId}`,
		method: 'get'
	});
}
export function skipNode(data) {
	return request({
		url: `/api/flow/runtime/skipNode`,
		method: 'post',
		data
	});
}
export function getNextNode(taskId) {
	return request({
		url: `/api/flow/task/getNextNode?taskId=${taskId}`,
		method: 'get'
	});
}
export function findByProcessInstance(processInstanceId, tenantId) {
	return request({
		url: `/api/flow/ccTask/findByProcessInstance?processInstanceId=${processInstanceId}&tenantId=${tenantId}`,
		method: 'get'
	});
}

/** 流程撤回 */
export function undoneTask(data) {
	return request({
		url: '/api/flow/runtime/undoneTask',
		method: 'POST',
		isFormData: true,
		data: data
	});
}
/** 流程撤回 */
export function getAllNodeUserByInstanceId(params) {
	return request({
		url: '/api/flow/runtime/getAllNodeUserByInstanceId',
		method: 'get',
		isFormData: true,
		params: params
	});
}
