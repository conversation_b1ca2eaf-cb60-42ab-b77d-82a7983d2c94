import request from '@/utils/request';

/** 知识库列表 */
export function knowledgeList(params) {
	return request({
		url: '/api/disk/spaces/search',
		method: 'GET',
		params
	});
}
/** 判断是否有进入知识库的权限 */
export function knowledgeAuthority(params) {
	return request({
		url: '/api/disk/space/checkShareAuthority',
		method: 'GET',
		params
	});
}
/** 判断是否有管理权限 */
export function adminAuthority(params) {
	return request({
		url: '/api/disk/space/checkAdminAuthority',
		method: 'GET',
		params
	});
}
/** 文档列表查询 */
export function documentList(params) {
	return request({
		url: '/api/disk/files/search',
		method: 'GET',
		params
	});
}
/** 查询父级名称 */
export function getParentName(id) {
	return request({
		url: `/api/disk/folder/parents/${id}`,
		method: 'GET'
	});
}

/** 文件夹 是否置顶/重命名/移动 */
export function isTopping(id, params) {
	return request({
		url: `/api/disk/folder/${id}`,
		method: 'PUT',
		headers: {
			'content-type': 'application/json' //解决json字符串
		},
		data: params
	});
}
/** 文件 是否置顶/重命名/移动 */
export function isFileTopping(id, params) {
	return request({
		url: `/api/disk/file/${id}`,
		method: 'PUT',
		headers: {
			'content-type': 'application/json' //解决json字符串
		},
		data: params
	});
}

/** 文件夹 删除操作 */
export function deleteFolder(id) {
	return request({
		url: `/api/disk/folder/${id}`,
		method: 'DELETE'
	});
}
/** 文件 删除操作 */
export function deleteFile(id) {
	return request({
		url: `/api/disk/file/${id}`,
		method: 'DELETE'
	});
}
/** 收藏 */
export function handleFavorite(isFolder, objectId) {
	return request({
		url: `/api/disk/favorite/${isFolder}/${objectId}`,
		method: 'POST'
	});
}
/** 取消收藏 */
export function cancleFavorite(isFolder, objectId) {
	return request({
		url: `/api/disk/favorite/${isFolder}/${objectId}`,
		method: 'DELETE'
	});
}
/** 新建文件夹 */
export function createFolder(params) {
	return request({
		url: '/api/disk/folder',
		method: 'POST',
		headers: {
			'content-type': 'application/json' //解决json字符串
		},
		data: params
	});
}

/** 回收站文件夹彻底删除 */
export function folderDeleteCom(id) {
	return request({
		url: `/api/disk/folder/deletePhysic/${id}`,
		method: 'DELETE'
	});
}

/** 回收站 文件彻底删除 */
export function fileDeleteCom(id) {
	return request({
		url: `/api/disk/file/deletePhysic/${id}`,
		method: 'DELETE'
	});
}
/** 文件夹取回 */

export function folderRetrieve(id) {
	return request({
		url: `/api/disk/folder/back/${id}`,
		method: 'PUT'
	});
}
/** 文件取回 */
export function fileRetrieve(id) {
	return request({
		url: `/api/disk/file/back/${id}`,
		method: 'PUT'
	});
}
// /** 附件管理上传接口*/
// export function uploadFile(params){
//     return request({
//       url:'/api/sys/file/upload',
//       method:'POST',
//       params
//     })
// }
/**  获取文件详情  */
export function fileInfo(id) {
	return request({
		url: `/api/disk/file/info/${id}`,
		method: 'GET'
	});
}
export function setFileInfo(id, data) {
	return request({
		url: `/api/disk/file/info/${id}`,
		method: 'put',
		data
	});
}

/** 上传文件接口 */
export function uploadFiles(params) {
	return request({
		url: `/api/disk/file`,
		method: 'post',
		headers: {
			'content-type': 'application/json'
		},
		data: params
	});
}

/**  获取文件摘要 */
export function fileSummary(id) {
	return request({
		url: `/api/disk/file/summary/${id}`,
		method: 'GET'
	});
}

/** 文件父级信息查询 */
export function getFileParent(id) {
	return request({
		url: `/api/disk/file/parents/${id}`,
		method: 'GET'
	});
}
// import axios from 'axios';
// /**文档下载*/
// export function download(url) {
// 	return axios({
// 		url,
// 		method: 'GET',
// 		responseType: 'blob'
// 	});
// }

/**文档下载*/
export function download(url, params, onDownloadProgress, cancelKey) {
	let config = {
		url,
		params,
		method: 'get',
		responseType: 'blob',
		onDownloadProgress,
		timeout: 3600 * 1000
	};
	if (cancelKey) {
		config.cancelKey = cancelKey;
	}
	return request(config);
}

export function download2(url, params, onDownloadProgress, cancelKey) {
	let config = {
		url,
		data: params,
		method: 'post',
		responseType: 'blob',
		onDownloadProgress,
		timeout: 3600 * 1000
	};
	if (cancelKey) {
		config.cancelKey = cancelKey;
	}
	return request(config);
}

/** 清空回收站 */
export function clearAll() {
	return request({
		url: '/api/disk/files/clear',
		method: 'DELETE'
	});
}

/** 获取文件夹列表 */
export function floderList(params) {
	return request({
		url: '/api/disk/folders/search',
		method: 'get',
		params
	});
}
/** 补充文件上传 */
export function suppFile(params) {
	return request({
		url: '/api/disk/file/uploadSupplementalFile',
		method: 'post',
		headers: {
			'content-type': 'application/json'
		},
		data: params
	});
}
/** 删除补充文件 */
export function delSuppFile(id) {
	return request({
		url: `/api/disk/file/deleteSupplementalFile/${id}`,
		method: 'POST'
	});
}

/** 查询补充文件 */
export function searchSuppFile(id) {
	return request({
		url: `/api/sys/files/${id}`,
		method: 'GET'
	});
}
/** 判断文件是否有下载权限 */
export function checkDownAuthority(params) {
	return request({
		url: `/api/disk/space/checkDownAuthority`,
		method: 'GET',
		params
	});
}
/** 文档权限列表 */
export function authoritySearch(params) {
	return request({
		url: `/api/disk/authority/sets/search`,
		method: 'GET',
		isFormData: true,
		params
	});
}

/** 文档权限 添加 */
export function authoritySet(data) {
	return request({
		url: `/api/disk/authority/set`,
		method: 'POST',
		data
	});
}
/** 文档权限 删除 */
export function authorityDelete(id) {
	return request({
		url: `/api/disk/authority/set/${id}`,
		method: 'DELETE'
	});
}

/** 文档权限 编辑 */
export function authorityEdit(id, params) {
	return request({
		url: `/api/disk/authority/set/${id}`,
		method: 'PUT',
		headers: {
			'content-type': 'application/json' //解决json字符串
		},
		data: params
	});
}
/** 文档权限 权限对象查询 */
export function authorityObjectsSearch(params) {
	return request({
		url: `/api/disk/authority/objects/search`,
		method: 'GET',
		isFormData: true,
		params
	});
}

/** 文档-配置链接 */
export function editLinks(data) {
	return request({
		url: `/api/disk/file/editLinks`,
		method: 'POST',
		data
	});
}
/**知识空间权限配置查询 */
export function getConfigList(params) {
	return request({
		url: '/api/disk/space/authoritys/search',
		method: 'get',
		params: { authorityType: params.type, spaceId: params.id }
	});
}

/**知识空间-编辑*/
export function putEditUsersList(data) {
	return request({
		url: `/api/disk/space/${data.id}`,
		method: 'put',
		data
	});
}
/** 文档-配置链接 */
export function setLabel(data) {
	return request({
		url: `/api/disk/file/setLabel`,
		method: 'POST',
		data
	});
}
