@font-face {
	font-family: 'icomoon';
	src: url('fonts/icomoon.eot?lc1wol');
	src: url('fonts/icomoon.eot?lc1wol#iefix') format('embedded-opentype'),
		url('fonts/icomoon.ttf?lc1wol') format('truetype'),
		url('fonts/icomoon.woff?lc1wol') format('woff'),
		url('fonts/icomoon.svg?lc1wol#icomoon') format('svg');
	font-weight: normal;
	font-style: normal;
}

[class^='yehuo-'],
[class*=' yehuo-'] {
	/* use !important to prevent issues with browser extensions that change fonts */
	font-family: 'icomoon' !important;
	speak: none;
	font-style: normal;
	font-weight: normal;
	font-variant: normal;
	text-transform: none;
	line-height: 1;

	/* Better Font Rendering =========== */
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

.yehuo-ion-alert-circled:before {
	content: '\f100';
}

.yehuo-ion-alert:before {
	content: '\f101';
}

.yehuo-ion-android-add-circle:before {
	content: '\f359';
}

.yehuo-ion-android-add:before {
	content: '\f2c7';
}

.yehuo-ion-android-alarm-clock:before {
	content: '\f35a';
}

.yehuo-ion-android-alert:before {
	content: '\f35b';
}

.yehuo-ion-android-apps:before {
	content: '\f35c';
}

.yehuo-ion-android-archive:before {
	content: '\f2c9';
}

.yehuo-ion-android-arrow-back:before {
	content: '\f2ca';
}

.yehuo-ion-android-arrow-down:before {
	content: '\f35d';
}

.yehuo-ion-android-arrow-dropdown-circle:before {
	content: '\f35e';
}

.yehuo-ion-android-arrow-dropdown:before {
	content: '\f35f';
}

.yehuo-ion-android-arrow-dropleft-circle:before {
	content: '\f360';
}

.yehuo-ion-android-arrow-dropleft:before {
	content: '\f361';
}

.yehuo-ion-android-arrow-dropright-circle:before {
	content: '\f362';
}

.yehuo-ion-android-arrow-dropright:before {
	content: '\f363';
}

.yehuo-ion-android-arrow-dropup-circle:before {
	content: '\f364';
}

.yehuo-ion-android-arrow-dropup:before {
	content: '\f365';
}

.yehuo-ion-android-arrow-forward:before {
	content: '\f30f';
}

.yehuo-ion-android-arrow-up:before {
	content: '\f366';
}

.yehuo-ion-android-attach:before {
	content: '\f367';
}

.yehuo-ion-android-bar:before {
	content: '\f368';
}

.yehuo-ion-android-bicycle:before {
	content: '\f369';
}

.yehuo-ion-android-boat:before {
	content: '\f36a';
}

.yehuo-ion-android-bookmark:before {
	content: '\f36b';
}

.yehuo-ion-android-bulb:before {
	content: '\f36c';
}

.yehuo-ion-android-bus:before {
	content: '\f36d';
}

.yehuo-ion-android-calendar:before {
	content: '\f2d1';
}

.yehuo-ion-android-call:before {
	content: '\f2d2';
}

.yehuo-ion-android-camera:before {
	content: '\f2d3';
}

.yehuo-ion-android-cancel:before {
	content: '\f36e';
}

.yehuo-ion-android-car:before {
	content: '\f36f';
}

.yehuo-ion-android-cart:before {
	content: '\f370';
}

.yehuo-ion-android-chat:before {
	content: '\f2d4';
}

.yehuo-ion-android-checkbox-blank:before {
	content: '\f371';
}

.yehuo-ion-android-checkbox-outline-blank:before {
	content: '\f372';
}

.yehuo-ion-android-checkbox-outline:before {
	content: '\f373';
}

.yehuo-ion-android-checkbox:before {
	content: '\f374';
}

.yehuo-ion-android-checkmark-circle:before {
	content: '\f375';
}

.yehuo-ion-android-clipboard:before {
	content: '\f376';
}

.yehuo-ion-android-close:before {
	content: '\f2d7';
}

.yehuo-ion-android-cloud-circle:before {
	content: '\f377';
}

.yehuo-ion-android-cloud-done:before {
	content: '\f378';
}

.yehuo-ion-android-cloud-outline:before {
	content: '\f379';
}

.yehuo-ion-android-cloud:before {
	content: '\f37a';
}

.yehuo-ion-android-color-palette:before {
	content: '\f37b';
}

.yehuo-ion-android-compass:before {
	content: '\f37c';
}

.yehuo-ion-android-contact:before {
	content: '\f2d8';
}

.yehuo-ion-android-contacts:before {
	content: '\f2d9';
}

.yehuo-ion-android-contract:before {
	content: '\f37d';
}

.yehuo-ion-android-create:before {
	content: '\f37e';
}

.yehuo-ion-android-delete:before {
	content: '\f37f';
}

.yehuo-ion-android-desktop:before {
	content: '\f380';
}

.yehuo-ion-android-document:before {
	content: '\f381';
}

.yehuo-ion-android-done-all:before {
	content: '\f382';
}

.yehuo-ion-android-done:before {
	content: '\f383';
}

.yehuo-ion-android-download:before {
	content: '\f2dd';
}

.yehuo-ion-android-drafts:before {
	content: '\f384';
}

.yehuo-ion-android-exit:before {
	content: '\f385';
}

.yehuo-ion-android-expand:before {
	content: '\f386';
}

.yehuo-ion-android-favorite-outline:before {
	content: '\f387';
}

.yehuo-ion-android-favorite:before {
	content: '\f388';
}

.yehuo-ion-android-film:before {
	content: '\f389';
}

.yehuo-ion-android-folder-open:before {
	content: '\f38a';
}

.yehuo-ion-android-folder:before {
	content: '\f2e0';
}

.yehuo-ion-android-funnel:before {
	content: '\f38b';
}

.yehuo-ion-android-globe:before {
	content: '\f38c';
}

.yehuo-ion-android-hand:before {
	content: '\f2e3';
}

.yehuo-ion-android-hangout:before {
	content: '\f38d';
}

.yehuo-ion-android-happy:before {
	content: '\f38e';
}

.yehuo-ion-android-home:before {
	content: '\f38f';
}

.yehuo-ion-android-image:before {
	content: '\f2e4';
}

.yehuo-ion-android-laptop:before {
	content: '\f390';
}

.yehuo-ion-android-list:before {
	content: '\f391';
}

.yehuo-ion-android-locate:before {
	content: '\f2e9';
}

.yehuo-ion-android-lock:before {
	content: '\f392';
}

.yehuo-ion-android-mail:before {
	content: '\f2eb';
}

.yehuo-ion-android-map:before {
	content: '\f393';
}

.yehuo-ion-android-menu:before {
	content: '\f394';
}

.yehuo-ion-android-microphone-off:before {
	content: '\f395';
}

.yehuo-ion-android-microphone:before {
	content: '\f2ec';
}

.yehuo-ion-android-more-horizontal:before {
	content: '\f396';
}

.yehuo-ion-android-more-vertical:before {
	content: '\f397';
}

.yehuo-ion-android-navigate:before {
	content: '\f398';
}

.yehuo-ion-android-notifications-none:before {
	content: '\f399';
}

.yehuo-ion-android-notifications-off:before {
	content: '\f39a';
}

.yehuo-ion-android-notifications:before {
	content: '\f39b';
}

.yehuo-ion-android-open:before {
	content: '\f39c';
}

.yehuo-ion-android-options:before {
	content: '\f39d';
}

.yehuo-ion-android-people:before {
	content: '\f39e';
}

.yehuo-ion-android-person-add:before {
	content: '\f39f';
}

.yehuo-ion-android-person:before {
	content: '\f3a0';
}

.yehuo-ion-android-phone-landscape:before {
	content: '\f3a1';
}

.yehuo-ion-android-phone-portrait:before {
	content: '\f3a2';
}

.yehuo-ion-android-pin:before {
	content: '\f3a3';
}

.yehuo-ion-android-plane:before {
	content: '\f3a4';
}

.yehuo-ion-android-playstore:before {
	content: '\f2f0';
}

.yehuo-ion-android-print:before {
	content: '\f3a5';
}

.yehuo-ion-android-radio-button-off:before {
	content: '\f3a6';
}

.yehuo-ion-android-radio-button-on:before {
	content: '\f3a7';
}

.yehuo-ion-android-refresh:before {
	content: '\f3a8';
}

.yehuo-ion-android-remove-circle:before {
	content: '\f3a9';
}

.yehuo-ion-android-remove:before {
	content: '\f2f4';
}

.yehuo-ion-android-restaurant:before {
	content: '\f3aa';
}

.yehuo-ion-android-sad:before {
	content: '\f3ab';
}

.yehuo-ion-android-search:before {
	content: '\f2f5';
}

.yehuo-ion-android-send:before {
	content: '\f2f6';
}

.yehuo-ion-android-settings:before {
	content: '\f2f7';
}

.yehuo-ion-android-share-alt:before {
	content: '\f3ac';
}

.yehuo-ion-android-share:before {
	content: '\f2f8';
}

.yehuo-ion-android-star-half:before {
	content: '\f3ad';
}

.yehuo-ion-android-star-outline:before {
	content: '\f3ae';
}

.yehuo-ion-android-star:before {
	content: '\f2fc';
}

.yehuo-ion-android-stopwatch:before {
	content: '\f2fd';
}

.yehuo-ion-android-subway:before {
	content: '\f3af';
}

.yehuo-ion-android-sunny:before {
	content: '\f3b0';
}

.yehuo-ion-android-sync:before {
	content: '\f3b1';
}

.yehuo-ion-android-textsms:before {
	content: '\f3b2';
}

.yehuo-ion-android-time:before {
	content: '\f3b3';
}

.yehuo-ion-android-train:before {
	content: '\f3b4';
}

.yehuo-ion-android-unlock:before {
	content: '\f3b5';
}

.yehuo-ion-android-upload:before {
	content: '\f3b6';
}

.yehuo-ion-android-volume-down:before {
	content: '\f3b7';
}

.yehuo-ion-android-volume-mute:before {
	content: '\f3b8';
}

.yehuo-ion-android-volume-off:before {
	content: '\f3b9';
}

.yehuo-ion-android-volume-up:before {
	content: '\f3ba';
}

.yehuo-ion-android-walk:before {
	content: '\f3bb';
}

.yehuo-ion-android-warning:before {
	content: '\f3bc';
}

.yehuo-ion-android-watch:before {
	content: '\f3bd';
}

.yehuo-ion-android-wifi:before {
	content: '\f305';
}

.yehuo-ion-aperture:before {
	content: '\f313';
}

.yehuo-ion-archive:before {
	content: '\f102';
}

.yehuo-ion-arrow-down-a:before {
	content: '\f103';
}

.yehuo-ion-arrow-down-b:before {
	content: '\f104';
}

.yehuo-ion-arrow-down-c:before {
	content: '\f105';
}

.yehuo-ion-arrow-expand:before {
	content: '\f25e';
}

.yehuo-ion-arrow-graph-down-left:before {
	content: '\f25f';
}

.yehuo-ion-arrow-graph-down-right:before {
	content: '\f260';
}

.yehuo-ion-arrow-graph-up-left:before {
	content: '\f261';
}

.yehuo-ion-arrow-graph-up-right:before {
	content: '\f262';
}

.yehuo-ion-arrow-left-a:before {
	content: '\f106';
}

.yehuo-ion-arrow-left-b:before {
	content: '\f107';
}

.yehuo-ion-arrow-left-c:before {
	content: '\f108';
}

.yehuo-ion-arrow-move:before {
	content: '\f263';
}

.yehuo-ion-arrow-resize:before {
	content: '\f264';
}

.yehuo-ion-arrow-return-left:before {
	content: '\f265';
}

.yehuo-ion-arrow-return-right:before {
	content: '\f266';
}

.yehuo-ion-arrow-right-a:before {
	content: '\f109';
}

.yehuo-ion-arrow-right-b:before {
	content: '\f10a';
}

.yehuo-ion-arrow-right-c:before {
	content: '\f10b';
}

.yehuo-ion-arrow-shrink:before {
	content: '\f267';
}

.yehuo-ion-arrow-swap:before {
	content: '\f268';
}

.yehuo-ion-arrow-up-a:before {
	content: '\f10c';
}

.yehuo-ion-arrow-up-b:before {
	content: '\f10d';
}

.yehuo-ion-arrow-up-c:before {
	content: '\f10e';
}

.yehuo-ion-asterisk:before {
	content: '\f314';
}

.yehuo-ion-at:before {
	content: '\f10f';
}

.yehuo-ion-backspace-outline:before {
	content: '\f3be';
}

.yehuo-ion-backspace:before {
	content: '\f3bf';
}

.yehuo-ion-bag:before {
	content: '\f110';
}

.yehuo-ion-battery-charging:before {
	content: '\f111';
}

.yehuo-ion-battery-empty:before {
	content: '\f112';
}

.yehuo-ion-battery-full:before {
	content: '\f113';
}

.yehuo-ion-battery-half:before {
	content: '\f114';
}

.yehuo-ion-battery-low:before {
	content: '\f115';
}

.yehuo-ion-beaker:before {
	content: '\f269';
}

.yehuo-ion-beer:before {
	content: '\f26a';
}

.yehuo-ion-bluetooth:before {
	content: '\f116';
}

.yehuo-ion-bonfire:before {
	content: '\f315';
}

.yehuo-ion-bookmark:before {
	content: '\f26b';
}

.yehuo-ion-bowtie:before {
	content: '\f3c0';
}

.yehuo-ion-briefcase:before {
	content: '\f26c';
}

.yehuo-ion-bug:before {
	content: '\f2be';
}

.yehuo-ion-calculator:before {
	content: '\f26d';
}

.yehuo-ion-calendar:before {
	content: '\f117';
}

.yehuo-ion-camera:before {
	content: '\f118';
}

.yehuo-ion-card:before {
	content: '\f119';
}

.yehuo-ion-cash:before {
	content: '\f316';
}

.yehuo-ion-chatbox-working:before {
	content: '\f11a';
}

.yehuo-ion-chatbox:before {
	content: '\f11b';
}

.yehuo-ion-chatboxes:before {
	content: '\f11c';
}

.yehuo-ion-chatbubble-working:before {
	content: '\f11d';
}

.yehuo-ion-chatbubble:before {
	content: '\f11e';
}

.yehuo-ion-chatbubbles:before {
	content: '\f11f';
}

.yehuo-ion-checkmark-circled:before {
	content: '\f120';
}

.yehuo-ion-checkmark-round:before {
	content: '\f121';
}

.yehuo-ion-checkmark:before {
	content: '\f122';
}

.yehuo-ion-chevron-down:before {
	content: '\f123';
}

.yehuo-ion-chevron-left:before {
	content: '\f124';
}

.yehuo-ion-chevron-right:before {
	content: '\f125';
}

.yehuo-ion-chevron-up:before {
	content: '\f126';
}

.yehuo-ion-clipboard:before {
	content: '\f127';
}

.yehuo-ion-clock:before {
	content: '\f26e';
}

.yehuo-ion-close-circled:before {
	content: '\f128';
}

.yehuo-ion-close-round:before {
	content: '\f129';
}

.yehuo-ion-close:before {
	content: '\f12a';
}

.yehuo-ion-closed-captioning:before {
	content: '\f317';
}

.yehuo-ion-cloud:before {
	content: '\f12b';
}

.yehuo-ion-code-download:before {
	content: '\f26f';
}

.yehuo-ion-code-working:before {
	content: '\f270';
}

.yehuo-ion-code:before {
	content: '\f271';
}

.yehuo-ion-coffee:before {
	content: '\f272';
}

.yehuo-ion-compass:before {
	content: '\f273';
}

.yehuo-ion-compose:before {
	content: '\f12c';
}

.yehuo-ion-connection-bars:before {
	content: '\f274';
}

.yehuo-ion-contrast:before {
	content: '\f275';
}

.yehuo-ion-crop:before {
	content: '\f3c1';
}

.yehuo-ion-cube:before {
	content: '\f318';
}

.yehuo-ion-disc:before {
	content: '\f12d';
}

.yehuo-ion-document-text:before {
	content: '\f12e';
}

.yehuo-ion-document:before {
	content: '\f12f';
}

.yehuo-ion-drag:before {
	content: '\f130';
}

.yehuo-ion-earth:before {
	content: '\f276';
}

.yehuo-ion-easel:before {
	content: '\f3c2';
}

.yehuo-ion-edit:before {
	content: '\f2bf';
}

.yehuo-ion-egg:before {
	content: '\f277';
}

.yehuo-ion-eject:before {
	content: '\f131';
}

.yehuo-ion-email-unread:before {
	content: '\f3c3';
}

.yehuo-ion-email:before {
	content: '\f132';
}

.yehuo-ion-erlenmeyer-flask-bubbles:before {
	content: '\f3c4';
}

.yehuo-ion-erlenmeyer-flask:before {
	content: '\f3c5';
}

.yehuo-ion-eye-disabled:before {
	content: '\f306';
}

.yehuo-ion-eye:before {
	content: '\f133';
}

.yehuo-ion-female:before {
	content: '\f278';
}

.yehuo-ion-filing:before {
	content: '\f134';
}

.yehuo-ion-film-marker:before {
	content: '\f135';
}

.yehuo-ion-fireball:before {
	content: '\f319';
}

.yehuo-ion-flag:before {
	content: '\f279';
}

.yehuo-ion-flame:before {
	content: '\f31a';
}

.yehuo-ion-flash-off:before {
	content: '\f136';
}

.yehuo-ion-flash:before {
	content: '\f137';
}

.yehuo-ion-folder:before {
	content: '\f139';
}

.yehuo-ion-fork-repo:before {
	content: '\f2c0';
}

.yehuo-ion-fork:before {
	content: '\f27a';
}

.yehuo-ion-forward:before {
	content: '\f13a';
}

.yehuo-ion-funnel:before {
	content: '\f31b';
}

.yehuo-ion-gear-a:before {
	content: '\f13d';
}

.yehuo-ion-gear-b:before {
	content: '\f13e';
}

.yehuo-ion-grid:before {
	content: '\f13f';
}

.yehuo-ion-hammer:before {
	content: '\f27b';
}

.yehuo-ion-happy-outline:before {
	content: '\f3c6';
}

.yehuo-ion-happy:before {
	content: '\f31c';
}

.yehuo-ion-headphone:before {
	content: '\f140';
}

.yehuo-ion-heart-broken:before {
	content: '\f31d';
}

.yehuo-ion-heart:before {
	content: '\f141';
}

.yehuo-ion-help-buoy:before {
	content: '\f27c';
}

.yehuo-ion-help-circled:before {
	content: '\f142';
}

.yehuo-ion-help:before {
	content: '\f143';
}

.yehuo-ion-home:before {
	content: '\f144';
}

.yehuo-ion-icecream:before {
	content: '\f27d';
}

.yehuo-ion-image:before {
	content: '\f147';
}

.yehuo-ion-images:before {
	content: '\f148';
}

.yehuo-ion-information-circled:before {
	content: '\f149';
}

.yehuo-ion-information:before {
	content: '\f14a';
}

.yehuo-ion-ionic:before {
	content: '\f14b';
}

.yehuo-ion-ios-alarm-outline:before {
	content: '\f3c7';
}

.yehuo-ion-ios-alarm:before {
	content: '\f3c8';
}

.yehuo-ion-ios-albums-outline:before {
	content: '\f3c9';
}

.yehuo-ion-ios-albums:before {
	content: '\f3ca';
}

.yehuo-ion-ios-americanfootball-outline:before {
	content: '\f3cb';
}

.yehuo-ion-ios-americanfootball:before {
	content: '\f3cc';
}

.yehuo-ion-ios-analytics-outline:before {
	content: '\f3cd';
}

.yehuo-ion-ios-analytics:before {
	content: '\f3ce';
}

.yehuo-ion-ios-arrow-back:before {
	content: '\f3cf';
}

.yehuo-ion-ios-arrow-down:before {
	content: '\f3d0';
}

.yehuo-ion-ios-arrow-forward:before {
	content: '\f3d1';
}

.yehuo-ion-ios-arrow-left:before {
	content: '\f3d2';
}

.yehuo-ion-ios-arrow-right:before {
	content: '\f3d3';
}

.yehuo-ion-ios-arrow-thin-down:before {
	content: '\f3d4';
}

.yehuo-ion-ios-arrow-thin-left:before {
	content: '\f3d5';
}

.yehuo-ion-ios-arrow-thin-right:before {
	content: '\f3d6';
}

.yehuo-ion-ios-arrow-thin-up:before {
	content: '\f3d7';
}

.yehuo-ion-ios-arrow-up:before {
	content: '\f3d8';
}

.yehuo-ion-ios-at-outline:before {
	content: '\f3d9';
}

.yehuo-ion-ios-at:before {
	content: '\f3da';
}

.yehuo-ion-ios-barcode-outline:before {
	content: '\f3db';
}

.yehuo-ion-ios-barcode:before {
	content: '\f3dc';
}

.yehuo-ion-ios-baseball-outline:before {
	content: '\f3dd';
}

.yehuo-ion-ios-baseball:before {
	content: '\f3de';
}

.yehuo-ion-ios-basketball-outline:before {
	content: '\f3df';
}

.yehuo-ion-ios-basketball:before {
	content: '\f3e0';
}

.yehuo-ion-ios-bell-outline:before {
	content: '\f3e1';
}

.yehuo-ion-ios-bell:before {
	content: '\f3e2';
}

.yehuo-ion-ios-body-outline:before {
	content: '\f3e3';
}

.yehuo-ion-ios-body:before {
	content: '\f3e4';
}

.yehuo-ion-ios-bolt-outline:before {
	content: '\f3e5';
}

.yehuo-ion-ios-bolt:before {
	content: '\f3e6';
}

.yehuo-ion-ios-book-outline:before {
	content: '\f3e7';
}

.yehuo-ion-ios-book:before {
	content: '\f3e8';
}

.yehuo-ion-ios-bookmarks-outline:before {
	content: '\f3e9';
}

.yehuo-ion-ios-bookmarks:before {
	content: '\f3ea';
}

.yehuo-ion-ios-box-outline:before {
	content: '\f3eb';
}

.yehuo-ion-ios-box:before {
	content: '\f3ec';
}

.yehuo-ion-ios-briefcase-outline:before {
	content: '\f3ed';
}

.yehuo-ion-ios-briefcase:before {
	content: '\f3ee';
}

.yehuo-ion-ios-browsers-outline:before {
	content: '\f3ef';
}

.yehuo-ion-ios-browsers:before {
	content: '\f3f0';
}

.yehuo-ion-ios-calculator-outline:before {
	content: '\f3f1';
}

.yehuo-ion-ios-calculator:before {
	content: '\f3f2';
}

.yehuo-ion-ios-calendar-outline:before {
	content: '\f3f3';
}

.yehuo-ion-ios-calendar:before {
	content: '\f3f4';
}

.yehuo-ion-ios-camera-outline:before {
	content: '\f3f5';
}

.yehuo-ion-ios-camera:before {
	content: '\f3f6';
}

.yehuo-ion-ios-cart-outline:before {
	content: '\f3f7';
}

.yehuo-ion-ios-cart:before {
	content: '\f3f8';
}

.yehuo-ion-ios-chatboxes-outline:before {
	content: '\f3f9';
}

.yehuo-ion-ios-chatboxes:before {
	content: '\f3fa';
}

.yehuo-ion-ios-chatbubble-outline:before {
	content: '\f3fb';
}

.yehuo-ion-ios-chatbubble:before {
	content: '\f3fc';
}

.yehuo-ion-ios-checkmark-empty:before {
	content: '\f3fd';
}

.yehuo-ion-ios-checkmark-outline:before {
	content: '\f3fe';
}

.yehuo-ion-ios-checkmark:before {
	content: '\f3ff';
}

.yehuo-ion-ios-circle-filled:before {
	content: '\f400';
}

.yehuo-ion-ios-circle-outline:before {
	content: '\f401';
}

.yehuo-ion-ios-clock-outline:before {
	content: '\f402';
}

.yehuo-ion-ios-clock:before {
	content: '\f403';
}

.yehuo-ion-ios-close-empty:before {
	content: '\f404';
}

.yehuo-ion-ios-close-outline:before {
	content: '\f405';
}

.yehuo-ion-ios-close:before {
	content: '\f406';
}

.yehuo-ion-ios-cloud-download-outline:before {
	content: '\f407';
}

.yehuo-ion-ios-cloud-download:before {
	content: '\f408';
}

.yehuo-ion-ios-cloud-outline:before {
	content: '\f409';
}

.yehuo-ion-ios-cloud-upload-outline:before {
	content: '\f40a';
}

.yehuo-ion-ios-cloud-upload:before {
	content: '\f40b';
}

.yehuo-ion-ios-cloud:before {
	content: '\f40c';
}

.yehuo-ion-ios-cloudy-night-outline:before {
	content: '\f40d';
}

.yehuo-ion-ios-cloudy-night:before {
	content: '\f40e';
}

.yehuo-ion-ios-cloudy-outline:before {
	content: '\f40f';
}

.yehuo-ion-ios-cloudy:before {
	content: '\f410';
}

.yehuo-ion-ios-cog-outline:before {
	content: '\f411';
}

.yehuo-ion-ios-cog:before {
	content: '\f412';
}

.yehuo-ion-ios-color-filter-outline:before {
	content: '\f413';
}

.yehuo-ion-ios-color-filter:before {
	content: '\f414';
}

.yehuo-ion-ios-color-wand-outline:before {
	content: '\f415';
}

.yehuo-ion-ios-color-wand:before {
	content: '\f416';
}

.yehuo-ion-ios-compose-outline:before {
	content: '\f417';
}

.yehuo-ion-ios-compose:before {
	content: '\f418';
}

.yehuo-ion-ios-contact-outline:before {
	content: '\f419';
}

.yehuo-ion-ios-contact:before {
	content: '\f41a';
}

.yehuo-ion-ios-copy-outline:before {
	content: '\f41b';
}

.yehuo-ion-ios-copy:before {
	content: '\f41c';
}

.yehuo-ion-ios-crop-strong:before {
	content: '\f41d';
}

.yehuo-ion-ios-crop:before {
	content: '\f41e';
}

.yehuo-ion-ios-download-outline:before {
	content: '\f41f';
}

.yehuo-ion-ios-download:before {
	content: '\f420';
}

.yehuo-ion-ios-drag:before {
	content: '\f421';
}

.yehuo-ion-ios-email-outline:before {
	content: '\f422';
}

.yehuo-ion-ios-email:before {
	content: '\f423';
}

.yehuo-ion-ios-eye-outline:before {
	content: '\f424';
}

.yehuo-ion-ios-eye:before {
	content: '\f425';
}

.yehuo-ion-ios-fastforward-outline:before {
	content: '\f426';
}

.yehuo-ion-ios-fastforward:before {
	content: '\f427';
}

.yehuo-ion-ios-filing-outline:before {
	content: '\f428';
}

.yehuo-ion-ios-filing:before {
	content: '\f429';
}

.yehuo-ion-ios-film-outline:before {
	content: '\f42a';
}

.yehuo-ion-ios-film:before {
	content: '\f42b';
}

.yehuo-ion-ios-flag-outline:before {
	content: '\f42c';
}

.yehuo-ion-ios-flag:before {
	content: '\f42d';
}

.yehuo-ion-ios-flame-outline:before {
	content: '\f42e';
}

.yehuo-ion-ios-flame:before {
	content: '\f42f';
}

.yehuo-ion-ios-flask-outline:before {
	content: '\f430';
}

.yehuo-ion-ios-flask:before {
	content: '\f431';
}

.yehuo-ion-ios-flower-outline:before {
	content: '\f432';
}

.yehuo-ion-ios-flower:before {
	content: '\f433';
}

.yehuo-ion-ios-folder-outline:before {
	content: '\f434';
}

.yehuo-ion-ios-folder:before {
	content: '\f435';
}

.yehuo-ion-ios-football-outline:before {
	content: '\f436';
}

.yehuo-ion-ios-football:before {
	content: '\f437';
}

.yehuo-ion-ios-game-controller-a-outline:before {
	content: '\f438';
}

.yehuo-ion-ios-game-controller-a:before {
	content: '\f439';
}

.yehuo-ion-ios-game-controller-b-outline:before {
	content: '\f43a';
}

.yehuo-ion-ios-game-controller-b:before {
	content: '\f43b';
}

.yehuo-ion-ios-gear-outline:before {
	content: '\f43c';
}

.yehuo-ion-ios-gear:before {
	content: '\f43d';
}

.yehuo-ion-ios-glasses-outline:before {
	content: '\f43e';
}

.yehuo-ion-ios-glasses:before {
	content: '\f43f';
}

.yehuo-ion-ios-grid-view-outline:before {
	content: '\f440';
}

.yehuo-ion-ios-grid-view:before {
	content: '\f441';
}

.yehuo-ion-ios-heart-outline:before {
	content: '\f442';
}

.yehuo-ion-ios-heart:before {
	content: '\f443';
}

.yehuo-ion-ios-help-empty:before {
	content: '\f444';
}

.yehuo-ion-ios-help-outline:before {
	content: '\f445';
}

.yehuo-ion-ios-help:before {
	content: '\f446';
}

.yehuo-ion-ios-home-outline:before {
	content: '\f447';
}

.yehuo-ion-ios-home:before {
	content: '\f448';
}

.yehuo-ion-ios-infinite-outline:before {
	content: '\f449';
}

.yehuo-ion-ios-infinite:before {
	content: '\f44a';
}

.yehuo-ion-ios-information-empty:before {
	content: '\f44b';
}

.yehuo-ion-ios-information-outline:before {
	content: '\f44c';
}

.yehuo-ion-ios-information:before {
	content: '\f44d';
}

.yehuo-ion-ios-ionic-outline:before {
	content: '\f44e';
}

.yehuo-ion-ios-keypad-outline:before {
	content: '\f44f';
}

.yehuo-ion-ios-keypad:before {
	content: '\f450';
}

.yehuo-ion-ios-lightbulb-outline:before {
	content: '\f451';
}

.yehuo-ion-ios-lightbulb:before {
	content: '\f452';
}

.yehuo-ion-ios-list-outline:before {
	content: '\f453';
}

.yehuo-ion-ios-list:before {
	content: '\f454';
}

.yehuo-ion-ios-location-outline:before {
	content: '\f455';
}

.yehuo-ion-ios-location:before {
	content: '\f456';
}

.yehuo-ion-ios-locked-outline:before {
	content: '\f457';
}

.yehuo-ion-ios-locked:before {
	content: '\f458';
}

.yehuo-ion-ios-loop-strong:before {
	content: '\f459';
}

.yehuo-ion-ios-loop:before {
	content: '\f45a';
}

.yehuo-ion-ios-medical-outline:before {
	content: '\f45b';
}

.yehuo-ion-ios-medical:before {
	content: '\f45c';
}

.yehuo-ion-ios-medkit-outline:before {
	content: '\f45d';
}

.yehuo-ion-ios-medkit:before {
	content: '\f45e';
}

.yehuo-ion-ios-mic-off:before {
	content: '\f45f';
}

.yehuo-ion-ios-mic-outline:before {
	content: '\f460';
}

.yehuo-ion-ios-mic:before {
	content: '\f461';
}

.yehuo-ion-ios-minus-empty:before {
	content: '\f462';
}

.yehuo-ion-ios-minus-outline:before {
	content: '\f463';
}

.yehuo-ion-ios-minus:before {
	content: '\f464';
}

.yehuo-ion-ios-monitor-outline:before {
	content: '\f465';
}

.yehuo-ion-ios-monitor:before {
	content: '\f466';
}

.yehuo-ion-ios-moon-outline:before {
	content: '\f467';
}

.yehuo-ion-ios-moon:before {
	content: '\f468';
}

.yehuo-ion-ios-more-outline:before {
	content: '\f469';
}

.yehuo-ion-ios-more:before {
	content: '\f46a';
}

.yehuo-ion-ios-musical-note:before {
	content: '\f46b';
}

.yehuo-ion-ios-musical-notes:before {
	content: '\f46c';
}

.yehuo-ion-ios-navigate-outline:before {
	content: '\f46d';
}

.yehuo-ion-ios-navigate:before {
	content: '\f46e';
}

.yehuo-ion-ios-nutrition-outline:before {
	content: '\f46f';
}

.yehuo-ion-ios-nutrition:before {
	content: '\f470';
}

.yehuo-ion-ios-paper-outline:before {
	content: '\f471';
}

.yehuo-ion-ios-paper:before {
	content: '\f472';
}

.yehuo-ion-ios-paperplane-outline:before {
	content: '\f473';
}

.yehuo-ion-ios-paperplane:before {
	content: '\f474';
}

.yehuo-ion-ios-partlysunny-outline:before {
	content: '\f475';
}

.yehuo-ion-ios-partlysunny:before {
	content: '\f476';
}

.yehuo-ion-ios-pause-outline:before {
	content: '\f477';
}

.yehuo-ion-ios-pause:before {
	content: '\f478';
}

.yehuo-ion-ios-paw-outline:before {
	content: '\f479';
}

.yehuo-ion-ios-paw:before {
	content: '\f47a';
}

.yehuo-ion-ios-people-outline:before {
	content: '\f47b';
}

.yehuo-ion-ios-people:before {
	content: '\f47c';
}

.yehuo-ion-ios-person-outline:before {
	content: '\f47d';
}

.yehuo-ion-ios-person:before {
	content: '\f47e';
}

.yehuo-ion-ios-personadd-outline:before {
	content: '\f47f';
}

.yehuo-ion-ios-personadd:before {
	content: '\f480';
}

.yehuo-ion-ios-photos-outline:before {
	content: '\f481';
}

.yehuo-ion-ios-photos:before {
	content: '\f482';
}

.yehuo-ion-ios-pie-outline:before {
	content: '\f483';
}

.yehuo-ion-ios-pie:before {
	content: '\f484';
}

.yehuo-ion-ios-pint-outline:before {
	content: '\f485';
}

.yehuo-ion-ios-pint:before {
	content: '\f486';
}

.yehuo-ion-ios-play-outline:before {
	content: '\f487';
}

.yehuo-ion-ios-play:before {
	content: '\f488';
}

.yehuo-ion-ios-plus-empty:before {
	content: '\f489';
}

.yehuo-ion-ios-plus-outline:before {
	content: '\f48a';
}

.yehuo-ion-ios-plus:before {
	content: '\f48b';
}

.yehuo-ion-ios-pricetag-outline:before {
	content: '\f48c';
}

.yehuo-ion-ios-pricetag:before {
	content: '\f48d';
}

.yehuo-ion-ios-pricetags-outline:before {
	content: '\f48e';
}

.yehuo-ion-ios-pricetags:before {
	content: '\f48f';
}

.yehuo-ion-ios-printer-outline:before {
	content: '\f490';
}

.yehuo-ion-ios-printer:before {
	content: '\f491';
}

.yehuo-ion-ios-pulse-strong:before {
	content: '\f492';
}

.yehuo-ion-ios-pulse:before {
	content: '\f493';
}

.yehuo-ion-ios-rainy-outline:before {
	content: '\f494';
}

.yehuo-ion-ios-rainy:before {
	content: '\f495';
}

.yehuo-ion-ios-recording-outline:before {
	content: '\f496';
}

.yehuo-ion-ios-recording:before {
	content: '\f497';
}

.yehuo-ion-ios-redo-outline:before {
	content: '\f498';
}

.yehuo-ion-ios-redo:before {
	content: '\f499';
}

.yehuo-ion-ios-refresh-empty:before {
	content: '\f49a';
}

.yehuo-ion-ios-refresh-outline:before {
	content: '\f49b';
}

.yehuo-ion-ios-refresh:before {
	content: '\f49c';
}

.yehuo-ion-ios-reload:before {
	content: '\f49d';
}

.yehuo-ion-ios-reverse-camera-outline:before {
	content: '\f49e';
}

.yehuo-ion-ios-reverse-camera:before {
	content: '\f49f';
}

.yehuo-ion-ios-rewind-outline:before {
	content: '\f4a0';
}

.yehuo-ion-ios-rewind:before {
	content: '\f4a1';
}

.yehuo-ion-ios-rose-outline:before {
	content: '\f4a2';
}

.yehuo-ion-ios-rose:before {
	content: '\f4a3';
}

.yehuo-ion-ios-search-strong:before {
	content: '\f4a4';
}

.yehuo-ion-ios-search:before {
	content: '\f4a5';
}

.yehuo-ion-ios-settings-strong:before {
	content: '\f4a6';
}

.yehuo-ion-ios-settings:before {
	content: '\f4a7';
}

.yehuo-ion-ios-shuffle-strong:before {
	content: '\f4a8';
}

.yehuo-ion-ios-shuffle:before {
	content: '\f4a9';
}

.yehuo-ion-ios-skipbackward-outline:before {
	content: '\f4aa';
}

.yehuo-ion-ios-skipbackward:before {
	content: '\f4ab';
}

.yehuo-ion-ios-skipforward-outline:before {
	content: '\f4ac';
}

.yehuo-ion-ios-skipforward:before {
	content: '\f4ad';
}

.yehuo-ion-ios-snowy:before {
	content: '\f4ae';
}

.yehuo-ion-ios-speedometer-outline:before {
	content: '\f4af';
}

.yehuo-ion-ios-speedometer:before {
	content: '\f4b0';
}

.yehuo-ion-ios-star-half:before {
	content: '\f4b1';
}

.yehuo-ion-ios-star-outline:before {
	content: '\f4b2';
}

.yehuo-ion-ios-star:before {
	content: '\f4b3';
}

.yehuo-ion-ios-stopwatch-outline:before {
	content: '\f4b4';
}

.yehuo-ion-ios-stopwatch:before {
	content: '\f4b5';
}

.yehuo-ion-ios-sunny-outline:before {
	content: '\f4b6';
}

.yehuo-ion-ios-sunny:before {
	content: '\f4b7';
}

.yehuo-ion-ios-telephone-outline:before {
	content: '\f4b8';
}

.yehuo-ion-ios-telephone:before {
	content: '\f4b9';
}

.yehuo-ion-ios-tennisball-outline:before {
	content: '\f4ba';
}

.yehuo-ion-ios-tennisball:before {
	content: '\f4bb';
}

.yehuo-ion-ios-thunderstorm-outline:before {
	content: '\f4bc';
}

.yehuo-ion-ios-thunderstorm:before {
	content: '\f4bd';
}

.yehuo-ion-ios-time-outline:before {
	content: '\f4be';
}

.yehuo-ion-ios-time:before {
	content: '\f4bf';
}

.yehuo-ion-ios-timer-outline:before {
	content: '\f4c0';
}

.yehuo-ion-ios-timer:before {
	content: '\f4c1';
}

.yehuo-ion-ios-toggle-outline:before {
	content: '\f4c2';
}

.yehuo-ion-ios-toggle:before {
	content: '\f4c3';
}

.yehuo-ion-ios-trash-outline:before {
	content: '\f4c4';
}

.yehuo-ion-ios-trash:before {
	content: '\f4c5';
}

.yehuo-ion-ios-undo-outline:before {
	content: '\f4c6';
}

.yehuo-ion-ios-undo:before {
	content: '\f4c7';
}

.yehuo-ion-ios-unlocked-outline:before {
	content: '\f4c8';
}

.yehuo-ion-ios-unlocked:before {
	content: '\f4c9';
}

.yehuo-ion-ios-upload-outline:before {
	content: '\f4ca';
}

.yehuo-ion-ios-upload:before {
	content: '\f4cb';
}

.yehuo-ion-ios-videocam-outline:before {
	content: '\f4cc';
}

.yehuo-ion-ios-videocam:before {
	content: '\f4cd';
}

.yehuo-ion-ios-volume-high:before {
	content: '\f4ce';
}

.yehuo-ion-ios-volume-low:before {
	content: '\f4cf';
}

.yehuo-ion-ios-wineglass-outline:before {
	content: '\f4d0';
}

.yehuo-ion-ios-wineglass:before {
	content: '\f4d1';
}

.yehuo-ion-ios-world-outline:before {
	content: '\f4d2';
}

.yehuo-ion-ios-world:before {
	content: '\f4d3';
}

.yehuo-ion-ipad:before {
	content: '\f1f9';
}

.yehuo-ion-iphone:before {
	content: '\f1fa';
}

.yehuo-ion-ipod:before {
	content: '\f1fb';
}

.yehuo-ion-jet:before {
	content: '\f295';
}

.yehuo-ion-key:before {
	content: '\f296';
}

.yehuo-ion-knife:before {
	content: '\f297';
}

.yehuo-ion-laptop:before {
	content: '\f1fc';
}

.yehuo-ion-leaf:before {
	content: '\f1fd';
}

.yehuo-ion-levels:before {
	content: '\f298';
}

.yehuo-ion-lightbulb:before {
	content: '\f299';
}

.yehuo-ion-link:before {
	content: '\f1fe';
}

.yehuo-ion-load-a:before {
	content: '\f29a';
}

.yehuo-ion-load-b:before {
	content: '\f29b';
}

.yehuo-ion-load-c:before {
	content: '\f29c';
}

.yehuo-ion-load-d:before {
	content: '\f29d';
}

.yehuo-ion-location:before {
	content: '\f1ff';
}

.yehuo-ion-lock-combination:before {
	content: '\f4d4';
}

.yehuo-ion-locked:before {
	content: '\f200';
}

.yehuo-ion-log-in:before {
	content: '\f29e';
}

.yehuo-ion-log-out:before {
	content: '\f29f';
}

.yehuo-ion-loop:before {
	content: '\f201';
}

.yehuo-ion-magnet:before {
	content: '\f2a0';
}

.yehuo-ion-male:before {
	content: '\f2a1';
}

.yehuo-ion-man:before {
	content: '\f202';
}

.yehuo-ion-map:before {
	content: '\f203';
}

.yehuo-ion-medkit:before {
	content: '\f2a2';
}

.yehuo-ion-merge:before {
	content: '\f33f';
}

.yehuo-ion-mic-a:before {
	content: '\f204';
}

.yehuo-ion-mic-b:before {
	content: '\f205';
}

.yehuo-ion-mic-c:before {
	content: '\f206';
}

.yehuo-ion-minus-circled:before {
	content: '\f207';
}

.yehuo-ion-minus-round:before {
	content: '\f208';
}

.yehuo-ion-minus:before {
	content: '\f209';
}

.yehuo-ion-model-s:before {
	content: '\f2c1';
}

.yehuo-ion-monitor:before {
	content: '\f20a';
}

.yehuo-ion-more:before {
	content: '\f20b';
}

.yehuo-ion-mouse:before {
	content: '\f340';
}

.yehuo-ion-music-note:before {
	content: '\f20c';
}

.yehuo-ion-navyehuo-round:before {
	content: '\f20d';
}

.yehuo-ion-navicon:before {
	content: '\f20e';
}

.yehuo-ion-navigate:before {
	content: '\f2a3';
}

.yehuo-ion-network:before {
	content: '\f341';
}

.yehuo-ion-no-smoking:before {
	content: '\f2c2';
}

.yehuo-ion-nuclear:before {
	content: '\f2a4';
}

.yehuo-ion-outlet:before {
	content: '\f342';
}

.yehuo-ion-paintbrush:before {
	content: '\f4d5';
}

.yehuo-ion-paintbucket:before {
	content: '\f4d6';
}

.yehuo-ion-paper-airplane:before {
	content: '\f2c3';
}

.yehuo-ion-paperclip:before {
	content: '\f20f';
}

.yehuo-ion-pause:before {
	content: '\f210';
}

.yehuo-ion-person-add:before {
	content: '\f211';
}

.yehuo-ion-person-stalker:before {
	content: '\f212';
}

.yehuo-ion-person:before {
	content: '\f213';
}

.yehuo-ion-pie-graph:before {
	content: '\f2a5';
}

.yehuo-ion-pin:before {
	content: '\f2a6';
}

.yehuo-ion-pinpoint:before {
	content: '\f2a7';
}

.yehuo-ion-pizza:before {
	content: '\f2a8';
}

.yehuo-ion-plane:before {
	content: '\f214';
}

.yehuo-ion-planet:before {
	content: '\f343';
}

.yehuo-ion-play:before {
	content: '\f215';
}

.yehuo-ion-playstation:before {
	content: '\f30a';
}

.yehuo-ion-plus-circled:before {
	content: '\f216';
}

.yehuo-ion-plus-round:before {
	content: '\f217';
}

.yehuo-ion-plus:before {
	content: '\f218';
}

.yehuo-ion-podium:before {
	content: '\f344';
}

.yehuo-ion-pound:before {
	content: '\f219';
}

.yehuo-ion-power:before {
	content: '\f2a9';
}

.yehuo-ion-pricetag:before {
	content: '\f2aa';
}

.yehuo-ion-pricetags:before {
	content: '\f2ab';
}

.yehuo-ion-printer:before {
	content: '\f21a';
}

.yehuo-ion-pull-request:before {
	content: '\f345';
}

.yehuo-ion-qr-scanner:before {
	content: '\f346';
}

.yehuo-ion-quote:before {
	content: '\f347';
}

.yehuo-ion-radio-waves:before {
	content: '\f2ac';
}

.yehuo-ion-record:before {
	content: '\f21b';
}

.yehuo-ion-refresh:before {
	content: '\f21c';
}

.yehuo-ion-reply-all:before {
	content: '\f21d';
}

.yehuo-ion-reply:before {
	content: '\f21e';
}

.yehuo-ion-ribbon-a:before {
	content: '\f348';
}

.yehuo-ion-ribbon-b:before {
	content: '\f349';
}

.yehuo-ion-sad-outline:before {
	content: '\f4d7';
}

.yehuo-ion-sad:before {
	content: '\f34a';
}

.yehuo-ion-scissors:before {
	content: '\f34b';
}

.yehuo-ion-search:before {
	content: '\f21f';
}

.yehuo-ion-settings:before {
	content: '\f2ad';
}

.yehuo-ion-share:before {
	content: '\f220';
}

.yehuo-ion-shuffle:before {
	content: '\f221';
}

.yehuo-ion-skip-backward:before {
	content: '\f222';
}

.yehuo-ion-skip-forward:before {
	content: '\f223';
}

.yehuo-ion-social-android-outline:before {
	content: '\f224';
}

.yehuo-ion-social-android:before {
	content: '\f225';
}

.yehuo-ion-social-angular-outline:before {
	content: '\f4d8';
}

.yehuo-ion-social-angular:before {
	content: '\f4d9';
}

.yehuo-ion-social-apple-outline:before {
	content: '\f226';
}

.yehuo-ion-social-apple:before {
	content: '\f227';
}

.yehuo-ion-social-bitcoin-outline:before {
	content: '\f2ae';
}

.yehuo-ion-social-bitcoin:before {
	content: '\f2af';
}

.yehuo-ion-social-buffer-outline:before {
	content: '\f228';
}

.yehuo-ion-social-buffer:before {
	content: '\f229';
}

.yehuo-ion-social-chrome-outline:before {
	content: '\f4da';
}

.yehuo-ion-social-chrome:before {
	content: '\f4db';
}

.yehuo-ion-social-codepen-outline:before {
	content: '\f4dc';
}

.yehuo-ion-social-codepen:before {
	content: '\f4dd';
}

.yehuo-ion-social-css3-outline:before {
	content: '\f4de';
}

.yehuo-ion-social-css3:before {
	content: '\f4df';
}

.yehuo-ion-social-designernews-outline:before {
	content: '\f22a';
}

.yehuo-ion-social-designernews:before {
	content: '\f22b';
}

.yehuo-ion-social-dribbble-outline:before {
	content: '\f22c';
}

.yehuo-ion-social-dribbble:before {
	content: '\f22d';
}

.yehuo-ion-social-dropbox-outline:before {
	content: '\f22e';
}

.yehuo-ion-social-dropbox:before {
	content: '\f22f';
}

.yehuo-ion-social-euro-outline:before {
	content: '\f4e0';
}

.yehuo-ion-social-euro:before {
	content: '\f4e1';
}

.yehuo-ion-social-facebook-outline:before {
	content: '\f230';
}

.yehuo-ion-social-facebook:before {
	content: '\f231';
}

.yehuo-ion-social-foursquare-outline:before {
	content: '\f34c';
}

.yehuo-ion-social-foursquare:before {
	content: '\f34d';
}

.yehuo-ion-social-freebsd-devil:before {
	content: '\f2c4';
}

.yehuo-ion-social-github-outline:before {
	content: '\f232';
}

.yehuo-ion-social-github:before {
	content: '\f233';
}

.yehuo-ion-social-google-outline:before {
	content: '\f34e';
}

.yehuo-ion-social-google:before {
	content: '\f34f';
}

.yehuo-ion-social-googleplus-outline:before {
	content: '\f234';
}

.yehuo-ion-social-googleplus:before {
	content: '\f235';
}

.yehuo-ion-social-hackernews-outline:before {
	content: '\f236';
}

.yehuo-ion-social-hackernews:before {
	content: '\f237';
}

.yehuo-ion-social-html5-outline:before {
	content: '\f4e2';
}

.yehuo-ion-social-html5:before {
	content: '\f4e3';
}

.yehuo-ion-social-instagram-outline:before {
	content: '\f350';
}

.yehuo-ion-social-instagram:before {
	content: '\f351';
}

.yehuo-ion-social-javascript-outline:before {
	content: '\f4e4';
}

.yehuo-ion-social-javascript:before {
	content: '\f4e5';
}

.yehuo-ion-social-linkedin-outline:before {
	content: '\f238';
}

.yehuo-ion-social-linkedin:before {
	content: '\f239';
}

.yehuo-ion-social-markdown:before {
	content: '\f4e6';
}

.yehuo-ion-social-nodejs:before {
	content: '\f4e7';
}

.yehuo-ion-social-octocat:before {
	content: '\f4e8';
}

.yehuo-ion-social-pinterest-outline:before {
	content: '\f2b0';
}

.yehuo-ion-social-pinterest:before {
	content: '\f2b1';
}

.yehuo-ion-social-python:before {
	content: '\f4e9';
}

.yehuo-ion-social-reddit-outline:before {
	content: '\f23a';
}

.yehuo-ion-social-reddit:before {
	content: '\f23b';
}

.yehuo-ion-social-rss-outline:before {
	content: '\f23c';
}

.yehuo-ion-social-rss:before {
	content: '\f23d';
}

.yehuo-ion-social-sass:before {
	content: '\f4ea';
}

.yehuo-ion-social-skype-outline:before {
	content: '\f23e';
}

.yehuo-ion-social-skype:before {
	content: '\f23f';
}

.yehuo-ion-social-snapchat-outline:before {
	content: '\f4eb';
}

.yehuo-ion-social-snapchat:before {
	content: '\f4ec';
}

.yehuo-ion-social-tumblr-outline:before {
	content: '\f240';
}

.yehuo-ion-social-tumblr:before {
	content: '\f241';
}

.yehuo-ion-social-tux:before {
	content: '\f2c5';
}

.yehuo-ion-social-twitch-outline:before {
	content: '\f4ed';
}

.yehuo-ion-social-twitch:before {
	content: '\f4ee';
}

.yehuo-ion-social-twitter-outline:before {
	content: '\f242';
}

.yehuo-ion-social-twitter:before {
	content: '\f243';
}

.yehuo-ion-social-usd-outline:before {
	content: '\f352';
}

.yehuo-ion-social-usd:before {
	content: '\f353';
}

.yehuo-ion-social-vimeo-outline:before {
	content: '\f244';
}

.yehuo-ion-social-vimeo:before {
	content: '\f245';
}

.yehuo-ion-social-whatsapp-outline:before {
	content: '\f4ef';
}

.yehuo-ion-social-whatsapp:before {
	content: '\f4f0';
}

.yehuo-ion-social-windows-outline:before {
	content: '\f246';
}

.yehuo-ion-social-windows:before {
	content: '\f247';
}

.yehuo-ion-social-wordpress-outline:before {
	content: '\f248';
}

.yehuo-ion-social-wordpress:before {
	content: '\f249';
}

.yehuo-ion-social-yahoo-outline:before {
	content: '\f24a';
}

.yehuo-ion-social-yahoo:before {
	content: '\f24b';
}

.yehuo-ion-social-yen-outline:before {
	content: '\f4f1';
}

.yehuo-ion-social-yen:before {
	content: '\f4f2';
}

.yehuo-ion-social-youtube-outline:before {
	content: '\f24c';
}

.yehuo-ion-social-youtube:before {
	content: '\f24d';
}

.yehuo-ion-soup-can-outline:before {
	content: '\f4f3';
}

.yehuo-ion-soup-can:before {
	content: '\f4f4';
}

.yehuo-ion-speakerphone:before {
	content: '\f2b2';
}

.yehuo-ion-speedometer:before {
	content: '\f2b3';
}

.yehuo-ion-spoon:before {
	content: '\f2b4';
}

.yehuo-ion-star:before {
	content: '\f24e';
}

.yehuo-ion-stats-bars:before {
	content: '\f2b5';
}

.yehuo-ion-steam:before {
	content: '\f30b';
}

.yehuo-ion-stop:before {
	content: '\f24f';
}

.yehuo-ion-thermometer:before {
	content: '\f2b6';
}

.yehuo-ion-thumbsdown:before {
	content: '\f250';
}

.yehuo-ion-thumbsup:before {
	content: '\f251';
}

.yehuo-ion-toggle-filled:before {
	content: '\f354';
}

.yehuo-ion-toggle:before {
	content: '\f355';
}

.yehuo-ion-transgender:before {
	content: '\f4f5';
}

.yehuo-ion-trash-a:before {
	content: '\f252';
}

.yehuo-ion-trash-b:before {
	content: '\f253';
}

.yehuo-ion-trophy:before {
	content: '\f356';
}

.yehuo-ion-tshirt-outline:before {
	content: '\f4f6';
}

.yehuo-ion-tshirt:before {
	content: '\f4f7';
}

.yehuo-ion-umbrella:before {
	content: '\f2b7';
}

.yehuo-ion-university:before {
	content: '\f357';
}

.yehuo-ion-unlocked:before {
	content: '\f254';
}

.yehuo-ion-upload:before {
	content: '\f255';
}

.yehuo-ion-usb:before {
	content: '\f2b8';
}

.yehuo-ion-videocamera:before {
	content: '\f256';
}

.yehuo-ion-volume-high:before {
	content: '\f257';
}

.yehuo-ion-volume-low:before {
	content: '\f258';
}

.yehuo-ion-volume-medium:before {
	content: '\f259';
}

.yehuo-ion-volume-mute:before {
	content: '\f25a';
}

.yehuo-ion-wand:before {
	content: '\f358';
}

.yehuo-ion-waterdrop:before {
	content: '\f25b';
}

.yehuo-ion-wifi:before {
	content: '\f25c';
}

.yehuo-ion-wineglass:before {
	content: '\f2b9';
}

.yehuo-ion-woman:before {
	content: '\f25d';
}

.yehuo-ion-wrench:before {
	content: '\f2ba';
}

.yehuo-ion-xbox:before {
	content: '\f30c';
}
