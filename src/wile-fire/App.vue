<template>
	<!--v-visibility-change="visibilityChange"-->
	<div
		id="wile-fire"
		style="width: 100%; height: 100%"
		@contextmenu.prevent=""
		@dragenter="$event.preventDefault()"
		@dragover="$event.preventDefault()"
		@drop="$event.preventDefault()"
	>
		<vue2-water-marker
			v-if="isWaterMarker"
			width="420"
			font-size="14"
			:text="waterMarker"
			:font="'SimSun'"
			opacity="0.25"
		></vue2-water-marker>
		<!--		<div v-if="!sharedMiscState.isElectron" id="blur-container" class="blur-container">-->
		<!--			<svg-->
		<!--				xmlns="http://www.w3.org/2000/svg"-->
		<!--				version="1.1"-->
		<!--				width="100%"-->
		<!--				height="100%"-->
		<!--				id="blurred_mkvvpnf50"-->
		<!--				class="blured-img"-->
		<!--				viewBox="0 0 1920 875"-->
		<!--				preserveAspectRatio="none"-->
		<!--			>-->
		<!--				<filter id="blur_mkvvpnf">-->
		<!--					<feGaussianBlur in="SourceGraphic" stdDeviation="50"></feGaussianBlur>-->
		<!--				</filter>-->
		<!--				<image-->
		<!--					x="0"-->
		<!--					y="0"-->
		<!--					width="100%"-->
		<!--					height="100%"-->
		<!--					externalResourcesRequired="true"-->
		<!--					xmlns:xlink="http://www.w3.org/1999/xlink"-->
		<!--					xlink:href="https://static.wildfirechat.net/web_wfc_bg2.jpeg"-->
		<!--					style="filter: url(#blur_mkvvpnf)"-->
		<!--					preserveAspectRatio="none"-->
		<!--				></image>-->
		<!--			</svg>-->
		<!--			<div class="blur-mask"></div>-->
		<!--		</div>-->
		<!--用来实现视频缩略图-->
		<div id="styled_video_container" class="styled_video_container">
			<video id="bgvid" playsinline autoplay muted loop crossorigin="anonymous">
				<!-- <source src="http://thenewcode.com/assets/videos/polina.webm" type="video/webm">
                <source src="http://thenewcode.com/assets/videos/polina.mp4" type="video/mp4"> -->
			</video>
		</div>

		<CoolLightBox
			v-if="!sharedMiscState.isElectron"
			:items="sharedConversationState.previewMediaItems"
			:index="sharedConversationState.previewMediaIndex"
			:slideshow="false"
			@close="sharedConversationState.previewMediaIndex = null"
		></CoolLightBox>
		<!--		<notifications v-if="sharedMiscState.isMainWindow" />-->
		<IpcMain v-if="sharedMiscState.isMainWindow && sharedMiscState.isElectron" />
		<router-view id="main-content-container" class="main-content-container"></router-view>
	</div>
</template>

<script>
import store from './store';
import { isElectron } from './platform';
import CoolLightBox from 'vue-cool-lightbox';
import 'vue-cool-lightbox/dist/vue-cool-lightbox.min.css';
import IpcMain from './ipc/ipcMain';
import { currentWindow } from './platform';
import wfc from './wfc/client/wfc';
import { getWatermark } from '@/api/modules/component';
import { mapGetters } from 'vuex';
import { getImWater } from '@/utils/get-water';

export default {
	name: 'WileFire',
	components: {
		IpcMain,
		CoolLightBox
	},
	data() {
		return {
			url: '',
			sharedMiscState: store.state.misc,
			sharedConversationState: store.state.conversation
		};
	},
	computed: {
		...mapGetters(['waterConfig']),
		isWaterMarker() {
			return this.waterConfig.im && this.waterConfig.im.isWaterMarker;
		},
		waterMarker() {
			return this.waterConfig.im && this.waterConfig.im.waterMarker;
		}
	},
	activated() {
		this.visibilityChange(false);
		getImWater();
	},
	deactivated() {
		this.visibilityChange(true);
	},
	created() {
		getImWater();
		let root = document.documentElement;
		if (isElectron() || window.location.href.indexOf('voip') >= 0) {
			root.style.setProperty('--main-margin-left', '0px');
			root.style.setProperty('--main-margin-right', '0px');
			root.style.setProperty('--main-margin-top', '0px');
			root.style.setProperty('--main-margin-bottom', '0px');
			root.style.setProperty('--composite-message-page-width', '100vw');
			root.style.setProperty('--composite-message-page-height', '100vh');
		}

		if (this.sharedMiscState.isElectronWindowsOrLinux) {
			root.style.setProperty('--main-border-radius', '0px');
			root.style.setProperty('--home-menu-padding-top', '0px');
		}
		window.addEventListener('blur', this.onblur); // 浏览器显示
		window.addEventListener('focus', this.onfocus); // 浏览器隐藏
		if (isElectron()) {
			currentWindow.minimizable = this.sharedMiscState.enableMinimize;
		}
	},
	mounted() {
		let href = window.location.href;
		if (href.indexOf('voip') >= 0 || href.indexOf('files') >= 0) {
			let app = document.getElementById('app');
			let el = document.getElementById('blur-container');
			el && app.removeChild(el);
			el = document.getElementById('styled_video_container');
			el && app.removeChild(el);
			el = document.getElementById('main-content-container');
			el.style.backgroundColor = '#292929';
		}
		this.$eventBus.$on('uploadFile', file => {
			if (!file) {
				return;
			}
			if (file.size > 100 * 1024 * 1024 && !wfc.isSupportBigFilesUpload()) {
				this.$notify({
					title: '大文件提示',
					text: ' 不支持大文件上传',
					type: 'warn'
				});
			}
		});
	},
	beforeDestroy() {
		this.$eventBus.$off('uploadFile');
		window.removeEventListener('blur', this.onblur);
		window.removeEventListener('focus', this.onfocus);
	},
	methods: {
		//event,
		visibilityChange(hidden) {
			store.setPageVisibility(!hidden);
			if (!hidden && !isElectron()) {
				wfc.onForeground();
			}
		},
		onblur() {
			store.setPageVisibility(false);
		},
		onfocus() {
			store.setPageVisibility(true);
		}
	}
};
</script>

<!--should not scoped-->
<style lang="css">
:root {
	--main-border-radius: 10px;
	--main-margin-left: 0;
	--main-margin-right: 0;
	--main-margin-top: 0;
	--main-margin-bottom: 0;
	--tippy-right: 0px;
	--home-menu-padding-top: 60px;
	--composite-message-page-width: 100%;
	--composite-message-page-height: 100%;
}

.tippy-tooltip {
	right: var(--tippy-right) !important;
	border: 1px solid #f5f5f5 !important;
	background-color: #fcfcfc !important;
	box-shadow: 0 0 25px rgba(0, 0, 0, 0.125);
}

#app {
	position: relative;
}

.blur-container {
	overflow: hidden;
	height: 100vh;
	width: 100vw;
	z-index: -10;
	position: fixed;
	margin: 0;
}

.blur-container .blur-mask {
	position: absolute;
	left: 0;
	top: 0;
	height: 100vh;
	width: 100vw;
	background: rgba(0, 0, 0, 0.2);
	overflow: hidden;
}

.styled_video_container {
	position: fixed;
	top: 0;
	left: 0;
	width: auto;
	height: auto;
	z-index: -999;
	background-size: cover;
	transition: 1s opacity;
	opacity: 0;
}

.main-content-container {
	/*z-index: 999;*/
	/*position: absolute;*/
	width: 100%;
	height: 100%;
	/*top: 0;*/
	/*left: 0;*/
	margin: var(--main-margin-top) var(--main-margin-right) var(--main-margin-bottom)
		var(--main-margin-left);
	display: flex;
	justify-content: center;
	align-items: center;
}

.container-emoji {
	height: 300px;
}
</style>
