/*
 * Copyright (c) 2020 WildFireChat. All rights reserved.
 */

export default class ErrorCode {
	//server error code
	static kEcServerSecrectKeyMismatch = 1;
	static kEcServerInvalidData = 2;
	static kEcServerServerError = 4;
	static kEcServerNotModified = 5;
	static kEcServerTokenIncorrect = 6;
	static kConnectionStatusKickedOff = 7;
	static kEcServerUserForbidden = 8;
	static kEcServerNotInGroup = 9;
	static kEcServerInvalidMessage = 10;
	static kEcServerGroupAlreadyExist = 11;
	static kEcServerPasswordIncorrect = 15;
	static kEcServerFriendAlreadyRequested = 16;
	static kEcServerFriendRequestOverFrequency = 17;
	static kEcServerFriendRquestBlocked = 18;
	static kEcServerFriendRequestOvertime = 19;
	static kEcServerNotInChatroom = 20;
	static kEcServerNotLicensed = 22;
	static kEcServerTimeInconsistent = 30;
	static kEcServerUserIsBlocked = 245;
	static kEcServerInBlacklist = 246;
	static kEcServerForbidden_send_msg = 247;
	static kEcServerNotRight = 248;
	static kEcServerTimeout = 249;
	static kEcServerOverFrequence = 250;
	static kEcServerInvalidParameter = 251;
	static kEcServerNotExist = 253;
	static kEcServerNotImplement = 254;
}
