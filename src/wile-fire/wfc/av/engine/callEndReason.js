/*
 * Copyright (c) 2020 WildFireChat. All rights reserved.
 */

export default class CallEndReason {
	static REASON_Unknown = 0;
	static REASON_Busy = 1;
	static REASON_SignalError = 2;
	static REASON_Hangup = 3;
	static REASON_MediaError = 4;
	static REASON_RemoteHangup = 5;
	static REASON_OpenCameraFailure = 6;
	static REASON_Timeout = 7;
	static REASON_AcceptByOtherClient = 8;
	static REASON_AllLeft = 9;
	static RemoteBusy = 10;
	static RemoteTimeout = 11;
	static RemoteNetworkError = 12;
	static RoomDestroyed = 13;
	static RoomNotExist = 14;
	static RoomParticipantsFull = 15;
	static Interrupted = 16;
	static RemoteInterrupted = 17;
}
