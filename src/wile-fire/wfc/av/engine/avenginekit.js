/*
 * Copyright (c) 2020 WildFireChat. All rights reserved.
 */
import VideoProfile from './videoProfile';

export default class WfcAVEngineKit {
	/**
	 * CallSession 相关回调
	 * @deprecated 可能被移除，请勿直接设置，可通过{@link setup}参数直接传入
	 */
	sessionCallback;

	/**
	 * 音视频window显示的时候调用
	 */
	setup(sessionCallback) {}
	/**
	 * 返回当前音视频通话session
	 * 仅在音视频窗口调用有效
	 */
	getCurrentSession() {}
	static MAX_AUDIO_PARTICIPANT_COUNT = 16;
	static MAX_VIDEO_PARTICIPANT_COUNT = 9;

	// 是否禁用双流模式
	static DISABLE_DUAL_STREAM = false;

	/**
     屏幕分享替换模式。为ture时，屏幕分享会替换摄像头的数据流。为false时，屏幕分享会再单独发起一路，不会影响摄像头的输入。
     */
	static SCREEN_SHARING_REPLACE_MODE = true;

	/**
     禁止双流模式下，小流低帧率。默认为false，小流的帧率为8fps。当为true时使用 {@link SMALL_STREAM_FPS}， 或者使用同大流一样的帧率
     */
	static DISABLE_SMALL_STREAM_LOW_FPS = false;

	/**
	 * 小流分辨率，宽度
	 */
	static SMALL_STREAM_WIDTH = 200;

	/**
	 * 小流分辨率， 高度
	 */
	static SMALL_STREAM_HEIGHT = 200;

	/**
	 * 小流 fps
	 */
	static SMALL_STREAM_FPS = 8;

	static VIDEO_PROFILE = VideoProfile.VPDEFAULT;

	// 可参考 {@link videoProfile} 里面的bitrate，仅非替换模式生效。
	// 屏幕共享的分辨率等，需通过{@link startScreenShare} 设置
	static SCREEN_SHARE_MAX_BITRATE = 3600;
	/**
	 * 高级版音视频 SDK 有效
	 * 是否强制使用 TCP 传输媒体数据
	 * 启用时，janus 的配置里面，需要将 ice_tcp 置为 true
	 * 注意，强制使用 TCP 传输媒体数据，在网络丢包比较严重时，效果可能不太好
	 * @type {boolean}
	 */

	static FORCE_MEDIA_OVER_TCP = false;

	/**
	 * 高级版音视频 SDK 有效
	 * 是否强制采用 relay 模式
	 * 具体请参考 {@link https://docs.wildfirechat.cn/blogs/%E9%9F%B3%E8%A7%86%E9%A2%91%E9%AB%98%E7%BA%A7%E7%89%88%E7%9A%84%E5%8D%95%E7%AB%AF%E5%8F%A3%E5%8C%96%E5%92%8C%E5%BC%BA%E5%88%B6TCP%E5%8C%96.html}
	 * @type {boolean}
	 */
	static FORCE_RELAY = false;

	// 没有摄像头时的占位视频
	// 200 x 200，1s，无声，黑屏
	static DUMMY_VIDEO_URI =
		'data:video/mp4;base64,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';
}
