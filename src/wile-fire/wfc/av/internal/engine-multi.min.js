!(function (e, s) {
	if ('object' == typeof exports && 'object' == typeof module)
		module.exports = s(
			require('../../../config.js'),
			require('../../../platform.js'),
			require('../../messages/messageContentType'),
			require('../../model/conversationType'),
			require('../../model/nullUserInfo'),
			require('../../util/longUtil.js'),
			require('../engine/avenginekit'),
			require('../engine/avenginekitproxy'),
			require('../engine/callEndReason'),
			require('../engine/callSession'),
			require('../engine/callState'),
			require('../engine/participantProfile'),
			require('../engine/subscriber'),
			require('../messages/addParticipantsMessageContent'),
			require('../messages/callAnswerMessageContent'),
			require('../messages/callByeMessageContent'),
			require('../messages/callModifyMessageContent'),
			require('../messages/callSignalMessageContent'),
			require('../messages/callStartMessageContent'),
			require('../messages/muteVideoMessageContent')
		);
	else if ('function' == typeof define && define.amd)
		define([
			'../../../config.js',
			'../../../platform.js',
			'../../messages/messageContentType',
			'../../model/conversationType',
			'../../model/nullUserInfo',
			'../../util/longUtil.js',
			'../engine/avenginekit',
			'../engine/avenginekitproxy',
			'../engine/callEndReason',
			'../engine/callSession',
			'../engine/callState',
			'../engine/participantProfile',
			'../engine/subscriber',
			'../messages/addParticipantsMessageContent',
			'../messages/callAnswerMessageContent',
			'../messages/callByeMessageContent',
			'../messages/callModifyMessageContent',
			'../messages/callSignalMessageContent',
			'../messages/callStartMessageContent',
			'../messages/muteVideoMessageContent'
		], s);
	else {
		var o =
			'object' == typeof exports
				? s(
						require('../../../config.js'),
						require('../../../platform.js'),
						require('../../messages/messageContentType'),
						require('../../model/conversationType'),
						require('../../model/nullUserInfo'),
						require('../../util/longUtil.js'),
						require('../engine/avenginekit'),
						require('../engine/avenginekitproxy'),
						require('../engine/callEndReason'),
						require('../engine/callSession'),
						require('../engine/callState'),
						require('../engine/participantProfile'),
						require('../engine/subscriber'),
						require('../messages/addParticipantsMessageContent'),
						require('../messages/callAnswerMessageContent'),
						require('../messages/callByeMessageContent'),
						require('../messages/callModifyMessageContent'),
						require('../messages/callSignalMessageContent'),
						require('../messages/callStartMessageContent'),
						require('../messages/muteVideoMessageContent')
				  )
				: s(
						e['../../../config.js'],
						e['../../../platform.js'],
						e['../../messages/messageContentType'],
						e['../../model/conversationType'],
						e['../../model/nullUserInfo'],
						e['../../util/longUtil.js'],
						e['../engine/avenginekit'],
						e['../engine/avenginekitproxy'],
						e['../engine/callEndReason'],
						e['../engine/callSession'],
						e['../engine/callState'],
						e['../engine/participantProfile'],
						e['../engine/subscriber'],
						e['../messages/addParticipantsMessageContent'],
						e['../messages/callAnswerMessageContent'],
						e['../messages/callByeMessageContent'],
						e['../messages/callModifyMessageContent'],
						e['../messages/callSignalMessageContent'],
						e['../messages/callStartMessageContent'],
						e['../messages/muteVideoMessageContent']
				  );
		for (var t in o) ('object' == typeof exports ? exports : e)[t] = o[t];
	}
})(window, function (e, s, o, t, r, n, i, a, l, c, d, u, m, f, _, j, p, g, y, h) {
	return (function (e) {
		var s = {};
		function o(t) {
			if (s[t]) return s[t].exports;
			var r = (s[t] = { i: t, l: !1, exports: {} });
			return e[t].call(r.exports, r, r.exports, o), (r.l = !0), r.exports;
		}
		return (
			(o.m = e),
			(o.c = s),
			(o.d = function (e, s, t) {
				o.o(e, s) || Object.defineProperty(e, s, { enumerable: !0, get: t });
			}),
			(o.r = function (e) {
				'undefined' != typeof Symbol &&
					Symbol.toStringTag &&
					Object.defineProperty(e, Symbol.toStringTag, { value: 'Module' }),
					Object.defineProperty(e, '__esModule', { value: !0 });
			}),
			(o.t = function (e, s) {
				if ((1 & s && (e = o(e)), 8 & s)) return e;
				if (4 & s && 'object' == typeof e && e && e.__esModule) return e;
				var t = Object.create(null);
				if (
					(o.r(t),
					Object.defineProperty(t, 'default', { enumerable: !0, value: e }),
					2 & s && 'string' != typeof e)
				)
					for (var r in e)
						o.d(
							t,
							r,
							function (s) {
								return e[s];
							}.bind(null, r)
						);
				return t;
			}),
			(o.n = function (e) {
				var s =
					e && e.__esModule
						? function () {
								return e.default;
						  }
						: function () {
								return e;
						  };
				return o.d(s, 'a', s), s;
			}),
			(o.o = function (e, s) {
				return Object.prototype.hasOwnProperty.call(e, s);
			}),
			(o.p = ''),
			o((o.s = './av/internal/avenginekitImpl.js'))
		);
	})({
		'../../../config.js': function (s, o) {
			s.exports = e;
		},
		'../../../platform.js': function (e, o) {
			e.exports = s;
		},
		'../../messages/messageContentType': function (e, s) {
			e.exports = o;
		},
		'../../model/conversationType': function (e, s) {
			e.exports = t;
		},
		'../../model/nullUserInfo': function (e, s) {
			e.exports = r;
		},
		'../../util/longUtil.js': function (e, s) {
			e.exports = n;
		},
		'../engine/avenginekit': function (e, s) {
			e.exports = i;
		},
		'../engine/avenginekitproxy': function (e, s) {
			e.exports = a;
		},
		'../engine/callEndReason': function (e, s) {
			e.exports = l;
		},
		'../engine/callSession': function (e, s) {
			e.exports = c;
		},
		'../engine/callState': function (e, s) {
			e.exports = d;
		},
		'../engine/participantProfile': function (e, s) {
			e.exports = u;
		},
		'../engine/subscriber': function (e, s) {
			e.exports = m;
		},
		'../messages/addParticipantsMessageContent': function (e, s) {
			e.exports = f;
		},
		'../messages/callAnswerMessageContent': function (e, s) {
			e.exports = _;
		},
		'../messages/callByeMessageContent': function (e, s) {
			e.exports = j;
		},
		'../messages/callModifyMessageContent': function (e, s) {
			e.exports = p;
		},
		'../messages/callSignalMessageContent': function (e, s) {
			e.exports = g;
		},
		'../messages/callStartMessageContent': function (e, s) {
			e.exports = y;
		},
		'../messages/muteVideoMessageContent': function (e, s) {
			e.exports = h;
		},
		'./av/internal/avenginekitImpl.js': function (e, s, o) {
			'use strict';
			o.r(s),
				o.d(s, 'WfcAVEngineKitImpl', function () {
					return N;
				});
			var t = o('./node_modules/babel-runtime/core-js/json/stringify.js'),
				r = o.n(t),
				n = o('./node_modules/babel-runtime/core-js/map.js'),
				i = o.n(n),
				a = o('../../messages/messageContentType'),
				l = o.n(a),
				c = o('../messages/callSignalMessageContent'),
				d = o.n(c),
				u = o('../messages/callByeMessageContent'),
				m = o.n(u),
				f = o('../messages/callAnswerMessageContent'),
				_ = o.n(f),
				j = o('../messages/callStartMessageContent'),
				p = o.n(j),
				g = o('../messages/callModifyMessageContent'),
				y = o.n(g),
				h = o('../../model/conversationType'),
				b = o.n(h),
				S = o('../engine/callEndReason'),
				v = o.n(S),
				C = o('../engine/avenginekitproxy'),
				T = o.n(C),
				M = o('../engine/callState'),
				O = o.n(M),
				E = o('./av/internal/callSessionImpl.js'),
				I = o('../messages/addParticipantsMessageContent'),
				x = o.n(I),
				w = o('../engine/avenginekit'),
				k = o.n(w),
				A = o('../../util/longUtil.js');
			class N extends k.a {
				constructor() {
					super(),
						(this.sendMessageId = 0),
						(this._fixLongSerializedIssue = e => (
							'string' != typeof e ||
								('string' == typeof (e = JSON.parse(e)) && (e = JSON.parse(e))),
							e
						)),
						(this.onSendMessage = (e, s) => {
							s = this._fixLongSerializedIssue(s);
							let o = this.sendMessageCallbackMap.get(s.sendMessageId);
							o && o(s.error, s.messageUid, s.timestamp),
								this.sendMessageCallbackMap.delete(s.sendMessageId);
						}),
						(this.onReceiveMessage = (e, s) => {
							if (
								((s = this._fixLongSerializedIssue(s)),
								console.log(
									'receive voip message ',
									s.messageContent.type,
									s.messageContent.callId,
									s
								),
								!(
									(s.conversation.type !== b.a.Single && s.conversation.type !== b.a.Group) ||
									(1 !== s.direction &&
										s.messageContent.type !== l.a.VOIP_CONTENT_TYPE_ACCEPT &&
										s.messageContent.type !== l.a.VOIP_CONTENT_TYPE_END)
								))
							) {
								let e = s.messageContent;
								if (s.messageContent.type === l.a.VOIP_CONTENT_TYPE_SIGNAL) {
									if (!U.currentSession || U.currentSession.status === O.a.STATUS_IDLE) return;
									let o = s.messageContent;
									o.callId !== U.currentSession.callId
										? U.rejectOtherCall(s.conversation, e.callId, null, s.messageUid)
										: !U.currentSession ||
										  (U.currentSession.status !== O.a.STATUS_CONNECTING &&
												U.currentSession.status !== O.a.STATUS_CONNECTED &&
												U.currentSession.status !== O.a.STATUS_OUTGOING) ||
										  U.onReceiveData(s.from, o.payload);
								} else if (s.messageContent.type === l.a.VOIP_CONTENT_TYPE_START) {
									console.log('callstart', s);
									let o = s.messageContent.targetIds;
									if (o.findIndex(e => e === s.selfUserInfo.uid) < 0) return;
									(o = o.filter(e => e !== s.selfUserInfo.uid)),
										(o = o.push(s.from)),
										U.currentSession && U.currentSession.status !== O.a.STATUS_IDLE
											? U.rejectOtherCall(s.conversation, e.callId, o, s.messageUid)
											: ((U.currentSession = E.default.newSession(
													s.conversation,
													s.from,
													e.callId,
													e.audioOnly,
													U.sessionCallback
											  )),
											  (U.currentSession.startMsgUid = s.messageUid),
											  U.currentSession.initSession(
													!1,
													s.selfUserInfo,
													s.participantUserInfos,
													s.groupMemberUserInfos
											  ),
											  U.currentSession.setState(O.a.STATUS_INCOMING),
											  (U.currentSession.joinTime = s.timestamp),
											  U.currentSession.setUserJoinTime(s.from, s.timestamp),
											  s.participantUserInfos.forEach(e => {
													U.currentSession.setUserJoinTime(e.uid, s.timestamp);
											  }),
											  U.currentSession.setUserAcceptTime(s.from, s.timestamp));
								} else if (
									s.messageContent.type === l.a.VOIP_CONTENT_TYPE_ACCEPT ||
									s.messageContent.type === l.a.VOIP_CONTENT_TYPE_ACCEPT_T
								) {
									if (U.currentSession && U.currentSession.status !== O.a.STATUS_IDLE) {
										if (e.callId !== U.currentSession.callId)
											return void (
												1 === s.direction &&
												U.rejectOtherCall(s.conversation, e.callId, [s.selfUserInfo.uid])
											);
										if (0 === s.direction && U.currentSession.status === O.a.STATUS_INCOMING)
											return void U.currentSession.endCall(v.a.REASON_AcceptByOtherClient);
										U.currentSession.status === O.a.STATUS_OUTGOING &&
											U.currentSession.setState(O.a.STATUS_CONNECTING),
											!U.currentSession.audioOnly &&
												e.audioOnly &&
												U.currentSession.setAudioOnly(!0),
											U.currentSession.setUserAcceptTime(s.from, s.timestamp);
										let o = U.queueOfferMessageMap.get(s.from);
										o &&
											o.forEach(e => {
												U.processSignalingMessage(s.from, e);
											}),
											U.queueOfferMessageMap.delete(s.from);
									}
								} else if (s.messageContent.type === l.a.VOIP_CONTENT_TYPE_END)
									if (
										U.currentSession &&
										U.currentSession.status !== O.a.STATUS_IDLE &&
										U.currentSession.callId === e.callId
									) {
										let o = e.reason;
										if (1 === s.direction) {
											switch (e.reason) {
												case v.a.REASON_Unknown:
													o = v.a.REASON_Unknown;
													break;
												case v.a.REASON_Busy:
													o = v.a.RemoteBusy;
													break;
												case v.a.REASON_SignalError:
													o = v.a.RemoteNetworkError;
													break;
												case v.a.REASON_Hangup:
													o = v.a.REASON_RemoteHangup;
													break;
												case v.a.REASON_MediaError:
													o = v.a.RemoteNetworkError;
													break;
												case v.a.REASON_RemoteHangup:
													o = v.a.REASON_Hangup;
													break;
												case v.a.REASON_OpenCameraFailure:
													o = v.a.RemoteNetworkError;
													break;
												case v.a.REASON_Timeout:
													o = v.a.RemoteTimeout;
													break;
												case v.a.REASON_AcceptByOtherClient:
													o = v.a.REASON_AcceptByOtherClient;
													break;
												case v.a.REASON_AllLeft:
													o = v.a.REASON_AllLeft;
													break;
												case v.a.RemoteBusy:
													o = v.a.REASON_Busy;
													break;
												case v.a.RemoteTimeout:
													o = v.a.REASON_Timeout;
													break;
												case v.a.RemoteNetworkError:
													o = v.a.REASON_SignalError;
													break;
												case v.a.RoomDestroyed:
													o = v.a.RoomDestroyed;
													break;
												case v.a.RoomNotExist:
													o = v.a.RoomNotExist;
													break;
												case v.a.RoomParticipantsFull:
													o = v.a.RoomParticipantsFull;
													break;
												default:
													o = v.a.RemoteNetworkError;
											}
											U.currentSession.endUserCall(s.from, o);
										} else U.currentSession.endCall(o);
									} else console.log('invalid bye message, ignore it');
								else if (s.messageContent.type === l.a.VOIP_CONTENT_TYPE_MODIFY)
									U.currentSession &&
										U.currentSession.status === O.a.STATUS_CONNECTED &&
										U.currentSession.callId === e.callId &&
										(e.audioOnly
											? ((U.currentSession.audioOnly = !0), U.currentSession.downgrade2Voice(!0))
											: console.log('cannot modify voice call to video call'));
								else if (s.messageContent.type === l.a.VOIP_CONTENT_TYPE_ADD_PARTICIPANT)
									if (e.participants.indexOf(s.selfUserInfo.uid) > -1) {
										if (U.currentSession && U.currentSession.status !== O.a.STATUS_IDLE) {
											let o = [];
											return (
												o.push(...s.messageContent.participants),
												s.messageContent.existParticipants &&
													o.push(...s.messageContent.existParticipants),
												o.push(s.from),
												(o = o.filter(e => e !== s.selfUserInfo.uid)),
												void U.rejectOtherCall(s.conversation, e.callId, o)
											);
										}
										(U.currentSession = E.default.newSession(
											s.conversation,
											s.from,
											e.callId,
											e.audioOnly,
											U.sessionCallback
										)),
											(U.currentSession.startMsgUid = s.messageUid);
										let o = s.participantUserInfos.filter(e => e.uid !== s.selfUserInfo.uid);
										U.currentSession.initSession(!1, s.selfUserInfo, o, s.groupMemberUserInfos),
											(U.currentSession.joinTime = s.timestamp),
											o.forEach(e => {
												U.currentSession.setUserJoinTime(e.uid, s.timestamp);
											}),
											U.currentSession.updateExistParticipant(e.existParticipants, s.timestamp);
									} else if (
										U.currentSession &&
										U.currentSession.status !== O.a.STATUS_IDLE &&
										U.currentSession.callId === e.callId
									) {
										let o = s.participantUserInfos.filter(s => e.participants.indexOf(s.uid) > -1);
										U.currentSession.didAddNewParticipants(e.participants, o),
											o.forEach(e => {
												U.currentSession.setUserJoinTime(e.uid, s.timestamp);
											});
									} else {
										let o = [];
										o.push(...s.messageContent.participants),
											s.messageContent.existParticipants &&
												o.push(...s.messageContent.existParticipants),
											o.push(s.from),
											(o = o.filter(e => e !== s.selfUserInfo.uid)),
											U.rejectOtherCall(s.conversation, e.callId, o);
									}
								else if (s.messageContent.type === l.a.VOIP_CONTENT_TYPE_MUTE_VIDEO) {
									let e = s.messageContent;
									U.currentSession &&
										U.currentSession.callId === e.callId &&
										U.currentSession.status !== O.a.STATUS_IDLE &&
										U.currentSession.updateVideoMute(s.from, e.videoMuted);
								}
							}
						}),
						(this.startCall = (e, s) => {
							let o = (s = this._fixLongSerializedIssue(s)).conversation,
								t = s.audioOnly;
							if (this.currentSession) return;
							(this.currentSession = E.default.newSession(
								o,
								s.selfUserInfo.uid,
								s.callId,
								t,
								U.sessionCallback
							)),
								this.currentSession.initSession(
									!0,
									s.selfUserInfo,
									s.participantUserInfos,
									s.groupMemberUserInfos
								),
								this.currentSession.setState(O.a.STATUS_OUTGOING);
							let r = new p.a();
							(r.audioOnly = t),
								(r.callId = s.callId),
								(r.targetIds = this.currentSession.getParticipantIds()),
								(r.sdkType = 1),
								this.sendSignalMessage(
									r,
									this.currentSession.getParticipantIds(),
									!0,
									(e, s, o) => {
										U.currentSession &&
											(0 !== e
												? this.currentSession.endCall(v.a.REASON_SignalError)
												: ((this.currentSession.startMsgUid = s),
												  console.log(
														'start call startMsgUid',
														this.currentSession.startMsgUid,
														s,
														new Date().valueOf(),
														Object(A.numberValue)(o)
												  ),
												  (this.currentSession.joinTime = o),
												  this.currentSession.setAcceptTime(o),
												  r.targetIds.forEach(e => {
														this.currentSession.setUserJoinTime(e, o);
												  }, this)));
									}
								);
						});
				}
				setup() {
					console.log('wfc avengine-multi'),
						T.a.listenVoipEvent('message', this.onReceiveMessage),
						T.a.listenVoipEvent('sendMessageResult', this.onSendMessage),
						T.a.listenVoipEvent('startCall', this.startCall),
						(this.sendMessageCallbackMap = new i.a()),
						(this.queueOfferMessageMap = new i.a());
				}
				getCurrentSession() {
					return U.currentSession;
				}
				onCreateAnswerOffer(e, s) {
					console.log('send engine answer/offer'), U.sendSignalingMessage(s, [e], !0);
				}
				onIceCandidate(e, s) {
					console.log('send engine candidate', s), U.sendSignalingMessage(s, [e], !0);
				}
				inviteNewParticipants(e) {
					let s = U.currentSession;
					if (!s) return;
					let o = new x.a();
					(o.callId = s.callId),
						(o.initiator = s.selfUserInfo.uid),
						console.log('inviteNewParticipants', 'initiator ' + s.selfUserInfo),
						(o.audioOnly = s.audioOnly),
						(o.participants = e),
						(o.existParticipants = s.getExistParticipantsStatus());
					let t = [...s.getParticipantIds()];
					t.push(...e),
						this.sendSignalMessage(o, t, !0, (o, t, r) => {
							if ((console.log('send invite message success'), 0 !== o))
								return void console.log('send invite message error', o);
							let n = s.groupMemberUserInfos.filter(s => e.findIndex(e => e === s.uid) > -1);
							console.log('didAddNewParticipants av', e, n),
								s.didAddNewParticipants(e, n),
								e.forEach(e => {
									s.setUserJoinTime(e, r);
								});
						});
				}
				updateCallStartMessageContent(e, s) {
					let o = { messageUid: e, content: s };
					T.a.emitToMain('update-call-start-message', o);
				}
				clearInviteUnreadStatus(e) {}
				sendSignalMessageEx(e, s, o, t, r) {
					console.log('send signal message', s.type, s.callId, s);
					let n = { conversation: e, content: s.encode(), toUsers: o };
					r &&
						(this.sendMessageId++,
						(n.sendMessageId = this.sendMessageId),
						this.sendMessageCallbackMap.set(this.sendMessageId, r)),
						T.a.emitToMain('voip-message', n);
				}
				sendSignalMessage(e, s, o, t) {
					this.sendSignalMessageEx(U.currentSession.conversation, e, s, o, t);
				}
				sendSignalingMessage(e, s, o) {
					let t = new d.a();
					(t.callId = this.currentSession.callId),
						(t.payload = r()(e)),
						this.sendSignalMessage(t, s, o);
				}
				rejectOtherCall(e, s, o, t) {
					let r = new m.a();
					(r.callId = s),
						(r.inviteMsgUid = t),
						(r.reason = v.a.REASON_Busy),
						console.log('reject other call');
					let n = { conversation: e, content: r.encode(), toUsers: o };
					T.a.emitToMain('voip-message', n);
				}
				onReceiveData(e, s) {
					let o = JSON.parse(s);
					if (U.currentSession.getPeerConnection(e)) U.processSignalingMessage(e, o);
					else {
						console.log('queue signal', e, o);
						let s = this.queueOfferMessageMap.get(e);
						s || ((s = []), this.queueOfferMessageMap.set(e, s)),
							'answer' === o.type || 'offer' === o.type ? s.unshift(o) : s.push(o);
					}
				}
				processSignalingMessage(e, s) {
					console.log('process remote signal:' + e + ' ' + s.type),
						'offer' === s.type
							? (console.log('set remote offer'), U.currentSession.onReceiveRemoteCreateOffer(e, s))
							: 'answer' === s.type
							? (console.log('set remote answer'),
							  U.currentSession.onReceiveRemoteAnswerOffer(e, s))
							: 'candidate' === s.type
							? (console.log('set remote candidate'),
							  (s.sdpMLineIndex = s.label),
							  (s.sdpMid = s.id),
							  U.currentSession.setRemoteIceCandidate(e, s))
							: 'remove-candidates' === s.type || console.log('unknown type:' + s.type);
				}
				answerCurrentCall() {
					let e = new _.a();
					(e.audioOnly = U.currentSession.audioOnly),
						(e.callId = U.currentSession.callId),
						(e.inviteMessageUid = U.currentSession.startMsgUid),
						this.sendSignalMessage(e, this.currentSession.getParticipantIds(), !0, (e, s, o) => {
							0 === e
								? this.currentSession.setAcceptTime(o)
								: this.currentSession.endCall(v.a.REASON_SignalError);
						});
				}
				downgrade2VoiceCall() {
					let e = new y.a();
					(U.currentSession.audioOnly = !0),
						(e.audioOnly = U.currentSession.audioOnly),
						(e.callId = U.currentSession.callId),
						this.sendSignalMessage(e, this.currentSession.getParticipantIds(), !0);
				}
			}
			const U = new N();
			s.default = U;
		},
		'./av/internal/callSessionImpl.js': function (e, s, o) {
			'use strict';
			o.r(s),
				o.d(s, 'default', function () {
					return P;
				});
			var t = o('./node_modules/babel-runtime/core-js/json/stringify.js'),
				r = o.n(t),
				n = o('./node_modules/babel-runtime/helpers/extends.js'),
				i = o.n(n),
				a = o('./node_modules/babel-runtime/core-js/map.js'),
				l = o.n(a),
				c = o('./node_modules/babel-runtime/helpers/asyncToGenerator.js'),
				d = o.n(c),
				u = o('../../../config.js'),
				m = o.n(u),
				f = o('../engine/callState'),
				_ = o.n(f),
				j = o('./av/internal/avenginekitImpl.js'),
				p = o('../engine/callEndReason'),
				g = o.n(p),
				y = o('../messages/callByeMessageContent'),
				h = o.n(y),
				b = o('../engine/subscriber'),
				S = o.n(b),
				v = o('../../../platform.js'),
				C = o('../engine/callSession'),
				T = o.n(C),
				M = o('../../util/longUtil.js'),
				O = o('../engine/avenginekitproxy'),
				E = o.n(O),
				I = o('../messages/muteVideoMessageContent'),
				x = o.n(I),
				w = o('../../model/nullUserInfo'),
				k = o.n(w),
				A = o('./av/internal/soundMeter.js'),
				N = o('../engine/participantProfile'),
				U = o.n(N);
			class P extends T.a {
				constructor(...e) {
					var s;
					return (
						(s = super(...e)),
						(this.joinTime = 0),
						(this.acceptTime = 0),
						(this.status = 0),
						(this.getOnlyAudioStream = !1),
						(this.onVoipWindowClose = e => {
							this.endCall(g.a.RemoteNetworkError),
								window.removeEventListener('beforeunload', this.onVoipWindowClose);
						}),
						(this.callTimeout = () => {
							let e = new Date().valueOf();
							this.callTimer || (this.callTimer = setInterval(this.callTimeout, 1e3));
							let s = Object(M.numberValue)(this.joinTime);
							if (0 !== s) {
								if (this.status === _.a.STATUS_INCOMING) {
									if (e - s > 6e4) return void this.endCall(g.a.REASON_Timeout);
								} else if (this.status !== _.a.STATUS_CONNECTED && e - s > 6e4)
									return void this.endCall(g.a.RemoteTimeout);
								this.peerConnectionClientMap.forEach((s, o) => {
									let t = Object(M.gt)(s.acceptTime, s.acceptTime) ? s.acceptTime : s.joinTime;
									s.status !== _.a.STATUS_CONNECTED &&
										Object(M.gt)(t, 0) &&
										e - Object(M.numberValue)(t) > 6e4 &&
										this.endUserCall(o, g.a.RemoteTimeout);
								});
							}
						}),
						(this.gotRemoteStream = (e, s) => {
							if (this.remoteStream !== s.streams[0]) {
								this.sessionCallback &&
									this.sessionCallback.didReceiveRemoteVideoTrack(e, s.streams[0]);
								let o = s.streams[0],
									t = this.getClient(e);
								if (o.getAudioTracks().length > 0) {
									let e = new A.SoundMeter(window.audioContext);
									e.connectToSource(o, e => {
										console.log('connect to soundMeter', e);
									}),
										(t.soundMeter = e);
								}
								(t.stream = s.streams[0]), console.log('pc received remote stream', s.streams[0]);
							}
						}),
						(this.onIceCandidate = (e, s, o) => {
							if (o.candidate) {
								try {
									let t = {
										type: 'candidate',
										label: o.candidate.sdpMLineIndex,
										id: o.candidate.sdpMid,
										candidate: o.candidate.candidate
									};
									j.default.onIceCandidate(e, t), this.onAddIceCandidateSuccess(e, s);
								} catch (o) {
									this.onAddIceCandidateError(e, s, o);
								}
								console.log(`ICE candidate:\n${o.candidate ? o.candidate.candidate : '(null)'}`);
							}
						}),
						(this.onIceStateChange = (e, s, o) => {
							if (
								(console.log(`ICE state: ${s.iceConnectionState}`, e, s),
								console.log('ICE state change event: ', e, o),
								s)
							) {
								let o = this.getClient(e);
								'disconnected' === s.iceConnectionState ||
									('connected' === s.iceConnectionState
										? ((o.status = _.a.STATUS_CONNECTED),
										  e !== this.selfUserInfo.uid &&
												this.sessionCallback &&
												this.sessionCallback.didParticipantConnected(e),
										  this.setState(_.a.STATUS_CONNECTED))
										: 'failed' === s.iceConnectionState &&
										  this.endUserCall(e, g.a.REASON_MediaError));
							}
						}),
						s
					);
				}
				static newSession(e, s, o, t, r) {
					console.log('newSession, multi');
					let n = new P();
					return (
						(n.conversation = e),
						(n.initiatorId = s),
						(n.callId = o),
						(n.audioOnly = t),
						(n.sessionCallback = r),
						n
					);
				}
				getClient(e) {
					return this.peerConnectionClientMap.get(e);
				}
				getParticipantIds() {
					let e = [];
					return (
						this.participantUserInfos.forEach(s => {
							e.push(s.uid);
						}),
						e
					);
				}
				setAcceptTime(e) {
					(this.acceptTime = e), this.tryStartMedia();
				}
				setUserAcceptTime(e, s) {
					if (e === this.selfUserInfo.uid) return;
					console.log('setUserAcceptTime', e, s),
						(this.getClient(e).acceptTime = s),
						this.tryStartMedia();
				}
				setUserJoinTime(e, s) {
					this.getClient(e).joinTime = s;
				}
				tryStartMedia() {
					var e = this;
					return d()(function* () {
						if (Object(M.gt)(e.acceptTime, 0))
							for (const [s, o] of e.peerConnectionClientMap)
								!Object(M.gt)(o.acceptTime, 0) ||
									(o.status !== _.a.STATUS_INCOMING && o.status !== _.a.STATUS_OUTGOING) ||
									(Object(M.gt)(e.acceptTime, o.acceptTime)
										? yield e.startMedia(s, !0)
										: yield e.startMedia(s, !1));
					})();
				}
				getPeerConnection(e) {
					return this.peerConnectionClientMap.get(e).peerConnection;
				}
				getParticipantProfiles() {
					let e = [];
					for (const s of this.peerConnectionClientMap.values()) {
						let o = new U.a();
						(o.userId = s.userId),
							(o.status = s.status),
							(o.joinTime = s.joinTime),
							(o.acceptTime = s.acceptTime),
							(o.audioMuted = s.audioMuted),
							(o.videoMuted = s.videoMuted),
							e.push(o);
					}
					return e;
				}
				getSelfProfile() {
					let e = new U.a();
					return (
						(e.userId = this.selfUserInfo.uid),
						(e.status = this.status),
						(e.joinTime = this.joinTime),
						(e.acceptTime = this.acceptTime),
						(e.audioMuted = this.audioMuted),
						(e.videoMuted = this.videoMuted),
						e
					);
				}
				answerCall(e) {
					this.status === _.a.STATUS_INCOMING &&
						(this.setState(_.a.STATUS_CONNECTING),
						this.audioOnly && !e && (e = !0),
						(this.audioOnly = e),
						j.default.answerCurrentCall());
				}
				setState(e) {
					if (this.status !== e) {
						if (this.status !== _.a.STATUS_CONNECTED || e !== _.a.STATUS_CONNECTING) {
							if (
								((this.status = e),
								console.log('set status', e, this.startMsgUid),
								e === _.a.STATUS_IDLE || e === _.a.STATUS_CONNECTED)
							) {
								if (this.startMsgUid) {
									let s = { audioOnly: this.audioOnly };
									(s.status = this.endReason ? this.endReason : 0),
										e === _.a.STATUS_CONNECTED
											? ((s.connectTime = new Date().getTime()), (this.startTime = s.connectTime))
											: (s.endTime = new Date().getTime()),
										j.default.updateCallStartMessageContent(this.startMsgUid, s);
								}
								this.getOnlyAudioStream &&
									e === _.a.STATUS_CONNECTED &&
									(this.muteVideo(!0),
									this.sessionCallback &&
										this.sessionCallback.didVideoMuted(this.selfUserInfo.uid, !0));
							}
							this.sessionCallback && this.sessionCallback.didChangeState(e);
						}
					} else
						e === _.a.STATUS_CONNECTED &&
							this.sessionCallback &&
							this.sessionCallback.didChangeState(e);
				}
				setAudioOnly(e) {
					(this.audioOnly = e), this.sessionCallback && this.sessionCallback.didChangeMode(e);
				}
				initSession(e, s, o = [], t = []) {
					(this.moCall = e),
						(this.selfUserInfo = s),
						(this.participantUserInfos = o),
						(this.singleCall = 1 === o.length),
						(this.groupMemberUserInfos = t);
					let r = s;
					e || (r = o.filter(e => e.uid === this.initiatorId)[0]),
						this.sessionCallback && this.sessionCallback.onInitial(this, s, r, o, t);
					let n = [];
					o.forEach(e => {
						n.push(e.uid);
					}),
						this.initParticipantClientMap(n),
						e
							? (this.setState(_.a.STATUS_OUTGOING), this.startPreview(this.audioOnly))
							: (this.setState(_.a.STATUS_INCOMING), this.playIncomingRing()),
						this.callTimeout(),
						setTimeout(() => {
							window.addEventListener('beforeunload', this.onVoipWindowClose);
						}, 500),
						(window.AudioContext = window.AudioContext || window.webkitAudioContext),
						(window.audioContext = new AudioContext()),
						(this.soundMeterTimer = setInterval(() => {
							this.peerConnectionClientMap &&
								this.sessionCallback &&
								this.peerConnectionClientMap.forEach((e, s) => {
									if (!e.soundMeter) return;
									let o = e.soundMeter.instant.toFixed(2);
									e.soundMeter.slow.toFixed(2), e.soundMeter.clip;
									this.sessionCallback.didReportAudioVolume(s, Number(o));
								});
						}, 100));
				}
				initParticipantClientMap(e) {
					console.log('initParticipantClientMap', e),
						this.peerConnectionClientMap || (this.peerConnectionClientMap = new l.a()),
						e.forEach(e => {
							let s = new S.a(e);
							e === this.selfUserInfo.uid
								? (s.status = _.a.STATUS_OUTGOING)
								: (s.status = _.a.STATUS_INCOMING),
								this.peerConnectionClientMap.set(e, s);
						}, this);
				}
				inviteNewParticipants(e) {
					e.length &&
						(e = e.filter(
							e =>
								e !== this.selfUserInfo.uid &&
								this.participantUserInfos.findIndex(s => s.uid === e) < 0
						)).length &&
						((this.singleCall = !1), j.default.inviteNewParticipants(e));
				}
				getExistParticipantsStatus() {
					let e = [];
					return (
						e.push({
							userId: this.selfUserInfo.uid,
							acceptTime: this.acceptTime ? Object(M.numberValue)(this.acceptTime) : 0,
							joinTime: this.joinTime ? Object(M.numberValue)(this.joinTime) : 0,
							videoMuted: this.videoMuted
						}),
						this.participantUserInfos.forEach(s => {
							let o = this.getClient(s.uid);
							e.push({
								userId: o.userId,
								acceptTime: o.acceptTime ? Object(M.numberValue)(o.acceptTime) : 0,
								joinTime: o.joinTime ? Object(M.numberValue)(o.joinTime) : 0,
								videoMuted: o.videoMuted
							});
						}, this),
						e
					);
				}
				didAddNewParticipants(e, s) {
					e.forEach(e => {
						-1 === s.findIndex(s => s.uid === e) && s.push(new k.a(e));
					}),
						console.log('didAddNewParticipants', e, s),
						e.forEach(e => {
							let s = new S.a(e, this);
							(s.status = _.a.STATUS_INCOMING), this.peerConnectionClientMap.set(e, s);
						}, this),
						s.forEach(e => {
							this.participantUserInfos.push(e),
								this.sessionCallback && this.sessionCallback.didParticipantJoined(e.uid, e);
						}, this);
				}
				updateExistParticipant(e, s) {
					e.forEach(e => {
						let o = this.getClient(e.userId);
						(o.status = _.a.STATUS_INCOMING),
							(o.joinTime = s),
							(o.videoMuted = e.videoMuted),
							(o.acceptTime = e.acceptTime);
					});
				}
				updateVideoMute(e, s) {
					let o = this.getClient(e);
					o &&
						o.videoMuted !== s &&
						((o.videoMuted = s), this.sessionCallback && this.sessionCallback.didVideoMuted(e, s));
				}
				defaultVideoConstraints(e) {
					let s;
					return (
						(s = e
							? {
									audio: { echoCancellation: !0, noiseSuppression: !0, autoGainControl: !0 },
									video: !1
							  }
							: {
									audio: { echoCancellation: !0, noiseSuppression: !0, autoGainControl: !0 },
									video: { width: { max: 1280 }, height: { max: 720 } }
							  }),
						s
					);
				}
				startPreview(e) {
					var s = this;
					return d()(function* () {
						console.log('start preview');
						try {
							(s.cameraVideoStream = yield navigator.mediaDevices.getUserMedia(
								s.defaultVideoConstraints(e)
							)),
								console.log('Received local stream', s.cameraVideoStream);
						} catch (e) {
							console.error('getUserMedia error', e);
						}
						if (!s.cameraVideoStream)
							try {
								const e = {
									audio: { echoCancellation: !0, noiseSuppression: !0, autoGainControl: !0 },
									video: !1
								};
								(s.cameraVideoStream = yield navigator.mediaDevices.getUserMedia(e)),
									console.log('Received local stream audioOnly', s.cameraVideoStream),
									(s.getOnlyAudioStream = !0);
							} catch (e) {
								console.error('getUserMedia error', e),
									alert(`getUserMedia() error: ${e.name}`),
									s.endCall(g.a.REASON_MediaError);
							}
						s.sessionCallback &&
							s.sessionCallback.didCreateLocalVideoTrack(s.cameraVideoStream, s.getOnlyAudioStream);
						const o = s.cameraVideoStream.getVideoTracks();
						e
							? o &&
							  o.length > 0 &&
							  o.forEach(function (e) {
									return e.stop();
							  })
							: o && o.length > 0 && console.log(`Using video device: ${o[0].label}`);
						const t = s.cameraVideoStream.getAudioTracks();
						t.length > 0 && console.log(`Using audio device: ${t[0].label}`);
					})();
				}
				startMedia(e, s) {
					var o = this;
					return d()(function* () {
						console.log('start media', s),
							o.setState(_.a.STATUS_CONNECTING),
							(o.startTime = window.performance.now()),
							(o.getClient(e).status = _.a.STATUS_CONNECTING),
							o.cameraVideoStream
								? (console.log('start pc 1'), yield o.createPeerConnection(e, s))
								: (yield o.startPreview(o.audioOnly),
								  console.log('start pc 0'),
								  yield o.createPeerConnection(e, s));
					})();
				}
				getDesktopSources(e) {
					return v.desktopCapturer ? v.desktopCapturer.getSources({ types: e }) : null;
				}
				startScreenShare(e) {
					var s = this;
					return d()(function* () {
						console.log('start screen share'),
							s.screenShareStream ||
								(console.log('desktopCapturer ', v.desktopCapturer),
								v.desktopCapturer
									? ((s.screenShareStream = yield navigator.mediaDevices.getUserMedia({
											audio: !1,
											video: { mandatory: i()({ chromeMediaSource: 'desktop' }, e) }
									  })),
									  console.log('desktopCapturer screen share stream', s.screenShareStream))
									: (s.screenShareStream = yield navigator.mediaDevices.getDisplayMedia({
											audio: !1,
											video: !0
									  }))),
							s.sessionCallback && s.sessionCallback.didCreateLocalVideoTrack(s.screenShareStream),
							Object(M.gt)(s.acceptTime, 0) &&
								s.peerConnectionClientMap.forEach(function (e, o) {
									e.videoSender &&
										e.videoSender &&
										e.videoSender.replaceTrack(s.screenShareStream.getVideoTracks()[0]);
								}, s);
					})();
				}
				isScreenSharing() {
					return null !== this.screenShareStream;
				}
				stopScreenShare() {
					(this.screenShareStream = null),
						this.sessionCallback &&
							this.cameraVideoStream &&
							this.sessionCallback.didCreateLocalVideoTrack(this.cameraVideoStream),
						Object(M.gt)(this.acceptTime, 0) &&
							this.peerConnectionClientMap.forEach((e, s) => {
								e.videoSender &&
									e.videoSender &&
									e.videoSender.replaceTrack(this.cameraVideoStream.getVideoTracks()[0]);
							}, this);
				}
				createPeerConnection(e, s) {
					var o = this;
					return d()(function* () {
						let t = o.getClient(e),
							n = o.getSelectedSdpSemantics();
						n.iceServers = [];
						let i = m.a.ICE_SERVERS;
						i &&
							i.forEach(function (e) {
								n.iceServers.push({ urls: [e[0]], username: e[1], credential: e[2] });
							}),
							console.log('RTCPeerConnection configuration:', n);
						let a = new RTCPeerConnection(n);
						(t.peerConnection = a),
							(t.isInitiator = s),
							console.log('Created local peer connection object pc', e),
							a.addEventListener('icecandidate', function (s) {
								return o.onIceCandidate(e, a, s);
							}),
							a.addEventListener('iceconnectionstatechange', function (s) {
								return o.onIceStateChange(e, a, s);
							}),
							a.addEventListener('track', function (s) {
								return o.gotRemoteStream(e, s);
							}),
							a.addEventListener('connectionstatechange', function (s) {
								return o.onConnectionStateChange(e, s);
							});
						let l = o.screenShareStream ? o.screenShareStream : o.cameraVideoStream;
						if (
							(o.audioOnly
								? l.getAudioTracks().forEach(function (e) {
										return a.addTrack(e, l);
								  }, o)
								: (a.addTrack(l.getAudioTracks()[0], l),
								  o.getOnlyAudioStream || (t.videoSender = a.addTrack(l.getVideoTracks()[0], l))),
							console.log('Added local stream to pc'),
							s)
						)
							try {
								console.log('pc createOffer start');
								let s = { offerToReceiveAudio: !0, offerToReceiveVideo: !o.audioOnly };
								const t = yield a.createOffer(s);
								(JSON.parse(r()(t)).type = 'offer'), yield o.onCreateOfferSuccess(e, t);
							} catch (s) {
								o.onCreateSessionDescriptionError(e, s);
							}
						console.log('createPeerConnection', e, o.getClient(e));
					})();
				}
				getSelectedSdpSemantics() {
					return {};
				}
				call() {
					console.log('voip on call button click'),
						this.stopIncomingRing(),
						console.log('on call button call'),
						this.answerCall(this.audioOnly);
				}
				onCreateSessionDescriptionError(e, s) {
					console.log('Failed to create session description'),
						this.endUserCall(e, g.a.REASON_MediaError);
				}
				onReceiveRemoteCreateOffer(e, s) {
					var o = this;
					return d()(function* () {
						let t = o.peerConnectionClientMap.get(e);
						if (t.isInitiator) return;
						console.log('onReceiveRemoteCreateOffer', e, s);
						let n = o.getPeerConnection(e);
						try {
							yield n.setRemoteDescription(s), o.onSetRemoteSuccess(n);
						} catch (s) {
							o.onSetSessionDescriptionError(e, s);
						}
						t.quequedCandidates &&
							t.quequedCandidates.length > 0 &&
							(console.log('process pending ice candidates'),
							t.quequedCandidates.forEach(function (s) {
								o.setRemoteIceCandidate(e, s);
							}),
							(t.quequedCandidates.length = 0)),
							console.log('pc createAnswer start');
						try {
							const s = yield n.createAnswer();
							(JSON.parse(r()(s)).type = 'answer'), yield o.onCreateAnswerSuccess(e, s);
						} catch (e) {
							o.onCreateSessionDescriptionError(e);
						}
					})();
				}
				onCreateOfferSuccess(e, s) {
					var o = this;
					return d()(function* () {
						if (
							(console.log('pc setLocalDescription start'),
							!o.peerConnectionClientMap.get(e).isInitiator)
						)
							return;
						let t = o.getPeerConnection(e);
						try {
							yield t.setLocalDescription(s), o.onSetLocalSuccess(t);
						} catch (s) {
							o.onSetSessionDescriptionError(e, s);
						}
						console.log(s), j.default.onCreateAnswerOffer(e, s);
					})();
				}
				onSetLocalSuccess(e) {
					console.log('setLocalDescription complete');
				}
				onSetRemoteSuccess(e) {
					console.log('setRemoteDescription complete');
				}
				onSetSessionDescriptionError(e, s) {}
				onReceiveRemoteAnswerOffer(e, s) {
					var o = this;
					return d()(function* () {
						console.log('onReceiveRemoteAnswerOffer', e, s);
						try {
							let t = o.peerConnectionClientMap.get(e),
								r = t.peerConnection;
							yield r.setRemoteDescription(s),
								o.onSetRemoteSuccess(r),
								t.quequedCandidates &&
									t.quequedCandidates.length > 0 &&
									(console.log('process pending ice candidates'),
									t.quequedCandidates.forEach(function (s) {
										o.setRemoteIceCandidate(e, s);
									}),
									(t.quequedCandidates.length = 0));
						} catch (s) {
							o.onSetSessionDescriptionError(e, s);
						}
					})();
				}
				setRemoteIceCandidate(e, s) {
					var o = this;
					return d()(function* () {
						console.log('handle the candidate'), o.onReceiveRemoteIceCandidate(e, s);
					})();
				}
				onCreateAnswerSuccess(e, s) {
					var o = this;
					return d()(function* () {
						console.log('pc setLocalDescription start');
						try {
							let t = o.getPeerConnection(e);
							yield t.setLocalDescription(s), o.onSetLocalSuccess(t);
						} catch (s) {
							o.onSetSessionDescriptionError(e, s);
						}
						console.log(s), j.default.onCreateAnswerOffer(e, s);
					})();
				}
				onReceiveRemoteIceCandidate(e, s) {
					var o = this;
					return d()(function* () {
						console.log('on receive remote ice candidate');
						let t = o.peerConnectionClientMap.get(e);
						if (t.peerConnection.remoteDescription) {
							console.log('pc rdp is set');
							let e = t.peerConnection;
							yield e.addIceCandidate(s);
						} else console.log('pc rdp is null'), t.quequedCandidates || (t.quequedCandidates = []), t.quequedCandidates.push(s);
					})();
				}
				onAddIceCandidateSuccess(e, s) {
					console.log('send Ice Candidate success');
				}
				onAddIceCandidateError(e, s, o) {
					console.log(`failed to add ICE Candidate: ${e} ${o.toString()}`),
						this.endUserCall(e, g.a.REASON_MediaError);
				}
				onConnectionStateChange(e, s, o) {
					console.log('onConnectionStateChange', e, s, o);
				}
				hangup() {
					console.log('Ending call'), this.endCall(g.a.REASON_Hangup);
				}
				downgrade2Voice(e = !1) {
					if (this.status !== _.a.STATUS_CONNECTED) return;
					const s = this.cameraVideoStream.getVideoTracks();
					s && s.length > 0 && s.forEach(e => e.stop()), e && this.downToVoice();
				}
				downToVoice() {
					if (
						(console.log('down to voice'),
						this.stopIncomingRing(),
						this.status === _.a.STATUS_INCOMING)
					)
						return this.setAudioOnly(!0), void this.answerCall(!0);
					this.status === _.a.STATUS_CONNECTED &&
						(this.audioOnly || (this.setAudioOnly(!0), j.default.downgrade2VoiceCall()));
				}
				muteVideo(e) {
					this.setVideoEnabled(!e);
				}
				setVideoEnabled(e) {
					if (this.audioOnly) return;
					this.videoMuted = !e;
					const s = this.cameraVideoStream.getVideoTracks();
					s && s.length > 0 && s.forEach(s => (s.enabled = e));
					let o = new x.a();
					(o.callId = this.callId),
						(o.videoMuted = this.videoMuted),
						(o.existParticipants = this.getExistParticipantsStatus()),
						j.default.sendSignalMessage(o, this.getParticipantIds(), !0);
				}
				muteAudio(e) {
					this.setAudioEnabled(!e);
				}
				setAudioEnabled(e) {
					if (this.cameraVideoStream) {
						const s = this.cameraVideoStream.getAudioTracks();
						s && s.length > 0 && ((s[0].enabled = !s[0].enabled), (this.audioMuted = !e));
					}
				}
				endMedia() {
					console.log('Ending media'),
						this.setState(_.a.STATUS_IDLE),
						this.stopIncomingRing(),
						this.cameraVideoStream &&
							(console.log('to stop stream', this.cameraVideoStream),
							void 0 === this.cameraVideoStream.getTracks
								? this.cameraVideoStream.stop()
								: this.cameraVideoStream.getTracks().forEach(function (e) {
										e.stop(), console.log('stop track', e);
								  }),
							(this.cameraVideoStream = null));
					for (const e of this.peerConnectionClientMap.values())
						console.log('close pc', e.peerConnection),
							e.soundMeter && e.soundMeter.stop(),
							e.peerConnection && e.peerConnection.close();
					this.peerConnectionClientMap = null;
				}
				endUserCall(e, s) {
					if ((console.log('endUserCall', e, s), e === this.selfUserInfo.uid))
						return void this.endCall(s);
					let o = this.getClient(e);
					if (
						(this.peerConnectionClientMap.delete(e),
						(this.participantUserInfos = this.participantUserInfos.filter(s => s.uid !== e)),
						o)
					) {
						if (o.peerConnection) {
							o.peerConnection.getSenders().forEach(e => o.peerConnection.removeTrack(e)),
								o.peerConnection.close();
						}
						this.sessionCallback &&
							this.peerConnectionClientMap.size > 0 &&
							this.sessionCallback.didParticipantLeft(e, s);
					}
					0 === this.peerConnectionClientMap.size &&
						(0 === this.conversation.type || this.singleCall
							? this.endCall(s)
							: this.endCall(g.a.REASON_AllLeft));
				}
				endCall(e) {
					if (((this.endReason = e), this.status !== _.a.STATUS_IDLE)) {
						if (
							(this.setState(_.a.STATUS_IDLE),
							e !== g.a.REASON_AcceptByOtherClient && e !== g.a.REASON_AllLeft)
						) {
							let s = new h.a();
							(s.callId = this.callId),
								(s.inviteMsgUid = this.startMsgUid),
								(s.reason = e),
								j.default.sendSignalMessage(s, this.getParticipantIds(), !1);
						}
						(this.endTime = new Date().valueOf()),
							(j.default.currentSession = null),
							clearInterval(this.callTimer),
							clearInterval(this.soundMeterTimer),
							this.endMedia(),
							this.sessionCallback && this.sessionCallback.didCallEndWithReason(e);
					}
				}
				closeVoipWindow() {
					(this.status = _.a.STATUS_IDLE),
						(j.default.currentSession = null),
						localStorage.getItem('enable_voip_debug') ||
							E.a.useIframe ||
							(v.currentWindow ? v.currentWindow.close() : E.a.emitToMain('close-voip-div'));
				}
			}
		},
		'./av/internal/soundMeter.js': function (e, s, o) {
			'use strict';
			function t(e) {
				(this.context = e),
					(this.instant = 0),
					(this.slow = 0),
					(this.clip = 0),
					(this.script = e.createScriptProcessor(2048, 1, 1));
				const s = this;
				this.script.onaudioprocess = function (e) {
					const o = e.inputBuffer.getChannelData(0);
					let t,
						r = 0,
						n = 0;
					for (t = 0; t < o.length; ++t) (r += o[t] * o[t]), Math.abs(o[t]) > 0.99 && (n += 1);
					(s.instant = Math.sqrt(r / o.length)),
						(s.slow = 0.95 * s.slow + 0.05 * s.instant),
						(s.clip = n / o.length);
				};
			}
			o.r(s),
				o.d(s, 'SoundMeter', function () {
					return t;
				}),
				(t.prototype.connectToSource = function (e, s) {
					console.log('SoundMeter connecting');
					try {
						(this.mic = this.context.createMediaStreamSource(e)),
							this.mic.connect(this.script),
							this.script.connect(this.context.destination),
							void 0 !== s && s(null);
					} catch (e) {
						console.error(e), void 0 !== s && s(e);
					}
				}),
				(t.prototype.stop = function () {
					console.log('SoundMeter stopping'), this.mic.disconnect(), this.script.disconnect();
				});
		},
		'./node_modules/babel-runtime/core-js/json/stringify.js': function (e, s, o) {
			e.exports = {
				default: o('./node_modules/core-js/library/fn/json/stringify.js'),
				__esModule: !0
			};
		},
		'./node_modules/babel-runtime/core-js/map.js': function (e, s, o) {
			e.exports = { default: o('./node_modules/core-js/library/fn/map.js'), __esModule: !0 };
		},
		'./node_modules/babel-runtime/core-js/object/assign.js': function (e, s, o) {
			e.exports = {
				default: o('./node_modules/core-js/library/fn/object/assign.js'),
				__esModule: !0
			};
		},
		'./node_modules/babel-runtime/core-js/promise.js': function (e, s, o) {
			e.exports = { default: o('./node_modules/core-js/library/fn/promise.js'), __esModule: !0 };
		},
		'./node_modules/babel-runtime/helpers/asyncToGenerator.js': function (e, s, o) {
			'use strict';
			s.__esModule = !0;
			var t,
				r = o('./node_modules/babel-runtime/core-js/promise.js'),
				n = (t = r) && t.__esModule ? t : { default: t };
			s.default = function (e) {
				return function () {
					var s = e.apply(this, arguments);
					return new n.default(function (e, o) {
						return (function t(r, i) {
							try {
								var a = s[r](i),
									l = a.value;
							} catch (e) {
								return void o(e);
							}
							if (!a.done)
								return n.default.resolve(l).then(
									function (e) {
										t('next', e);
									},
									function (e) {
										t('throw', e);
									}
								);
							e(l);
						})('next');
					});
				};
			};
		},
		'./node_modules/babel-runtime/helpers/extends.js': function (e, s, o) {
			'use strict';
			s.__esModule = !0;
			var t,
				r = o('./node_modules/babel-runtime/core-js/object/assign.js'),
				n = (t = r) && t.__esModule ? t : { default: t };
			s.default =
				n.default ||
				function (e) {
					for (var s = 1; s < arguments.length; s++) {
						var o = arguments[s];
						for (var t in o) Object.prototype.hasOwnProperty.call(o, t) && (e[t] = o[t]);
					}
					return e;
				};
		},
		'./node_modules/core-js/library/fn/json/stringify.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_core.js'),
				r = t.JSON || (t.JSON = { stringify: JSON.stringify });
			e.exports = function (e) {
				return r.stringify.apply(r, arguments);
			};
		},
		'./node_modules/core-js/library/fn/map.js': function (e, s, o) {
			o('./node_modules/core-js/library/modules/es6.object.to-string.js'),
				o('./node_modules/core-js/library/modules/es6.string.iterator.js'),
				o('./node_modules/core-js/library/modules/web.dom.iterable.js'),
				o('./node_modules/core-js/library/modules/es6.map.js'),
				o('./node_modules/core-js/library/modules/es7.map.to-json.js'),
				o('./node_modules/core-js/library/modules/es7.map.of.js'),
				o('./node_modules/core-js/library/modules/es7.map.from.js'),
				(e.exports = o('./node_modules/core-js/library/modules/_core.js').Map);
		},
		'./node_modules/core-js/library/fn/object/assign.js': function (e, s, o) {
			o('./node_modules/core-js/library/modules/es6.object.assign.js'),
				(e.exports = o('./node_modules/core-js/library/modules/_core.js').Object.assign);
		},
		'./node_modules/core-js/library/fn/promise.js': function (e, s, o) {
			o('./node_modules/core-js/library/modules/es6.object.to-string.js'),
				o('./node_modules/core-js/library/modules/es6.string.iterator.js'),
				o('./node_modules/core-js/library/modules/web.dom.iterable.js'),
				o('./node_modules/core-js/library/modules/es6.promise.js'),
				o('./node_modules/core-js/library/modules/es7.promise.finally.js'),
				o('./node_modules/core-js/library/modules/es7.promise.try.js'),
				(e.exports = o('./node_modules/core-js/library/modules/_core.js').Promise);
		},
		'./node_modules/core-js/library/modules/_a-function.js': function (e, s) {
			e.exports = function (e) {
				if ('function' != typeof e) throw TypeError(e + ' is not a function!');
				return e;
			};
		},
		'./node_modules/core-js/library/modules/_add-to-unscopables.js': function (e, s) {
			e.exports = function () {};
		},
		'./node_modules/core-js/library/modules/_an-instance.js': function (e, s) {
			e.exports = function (e, s, o, t) {
				if (!(e instanceof s) || (void 0 !== t && t in e))
					throw TypeError(o + ': incorrect invocation!');
				return e;
			};
		},
		'./node_modules/core-js/library/modules/_an-object.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_is-object.js');
			e.exports = function (e) {
				if (!t(e)) throw TypeError(e + ' is not an object!');
				return e;
			};
		},
		'./node_modules/core-js/library/modules/_array-from-iterable.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_for-of.js');
			e.exports = function (e, s) {
				var o = [];
				return t(e, !1, o.push, o, s), o;
			};
		},
		'./node_modules/core-js/library/modules/_array-includes.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_to-iobject.js'),
				r = o('./node_modules/core-js/library/modules/_to-length.js'),
				n = o('./node_modules/core-js/library/modules/_to-absolute-index.js');
			e.exports = function (e) {
				return function (s, o, i) {
					var a,
						l = t(s),
						c = r(l.length),
						d = n(i, c);
					if (e && o != o) {
						for (; c > d; ) if ((a = l[d++]) != a) return !0;
					} else for (; c > d; d++) if ((e || d in l) && l[d] === o) return e || d || 0;
					return !e && -1;
				};
			};
		},
		'./node_modules/core-js/library/modules/_array-methods.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_ctx.js'),
				r = o('./node_modules/core-js/library/modules/_iobject.js'),
				n = o('./node_modules/core-js/library/modules/_to-object.js'),
				i = o('./node_modules/core-js/library/modules/_to-length.js'),
				a = o('./node_modules/core-js/library/modules/_array-species-create.js');
			e.exports = function (e, s) {
				var o = 1 == e,
					l = 2 == e,
					c = 3 == e,
					d = 4 == e,
					u = 6 == e,
					m = 5 == e || u,
					f = s || a;
				return function (s, a, _) {
					for (
						var j,
							p,
							g = n(s),
							y = r(g),
							h = t(a, _, 3),
							b = i(y.length),
							S = 0,
							v = o ? f(s, b) : l ? f(s, 0) : void 0;
						b > S;
						S++
					)
						if ((m || S in y) && ((p = h((j = y[S]), S, g)), e))
							if (o) v[S] = p;
							else if (p)
								switch (e) {
									case 3:
										return !0;
									case 5:
										return j;
									case 6:
										return S;
									case 2:
										v.push(j);
								}
							else if (d) return !1;
					return u ? -1 : c || d ? d : v;
				};
			};
		},
		'./node_modules/core-js/library/modules/_array-species-constructor.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_is-object.js'),
				r = o('./node_modules/core-js/library/modules/_is-array.js'),
				n = o('./node_modules/core-js/library/modules/_wks.js')('species');
			e.exports = function (e) {
				var s;
				return (
					r(e) &&
						('function' != typeof (s = e.constructor) ||
							(s !== Array && !r(s.prototype)) ||
							(s = void 0),
						t(s) && null === (s = s[n]) && (s = void 0)),
					void 0 === s ? Array : s
				);
			};
		},
		'./node_modules/core-js/library/modules/_array-species-create.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_array-species-constructor.js');
			e.exports = function (e, s) {
				return new (t(e))(s);
			};
		},
		'./node_modules/core-js/library/modules/_classof.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_cof.js'),
				r = o('./node_modules/core-js/library/modules/_wks.js')('toStringTag'),
				n =
					'Arguments' ==
					t(
						(function () {
							return arguments;
						})()
					);
			e.exports = function (e) {
				var s, o, i;
				return void 0 === e
					? 'Undefined'
					: null === e
					? 'Null'
					: 'string' ==
					  typeof (o = (function (e, s) {
							try {
								return e[s];
							} catch (e) {}
					  })((s = Object(e)), r))
					? o
					: n
					? t(s)
					: 'Object' == (i = t(s)) && 'function' == typeof s.callee
					? 'Arguments'
					: i;
			};
		},
		'./node_modules/core-js/library/modules/_cof.js': function (e, s) {
			var o = {}.toString;
			e.exports = function (e) {
				return o.call(e).slice(8, -1);
			};
		},
		'./node_modules/core-js/library/modules/_collection-strong.js': function (e, s, o) {
			'use strict';
			var t = o('./node_modules/core-js/library/modules/_object-dp.js').f,
				r = o('./node_modules/core-js/library/modules/_object-create.js'),
				n = o('./node_modules/core-js/library/modules/_redefine-all.js'),
				i = o('./node_modules/core-js/library/modules/_ctx.js'),
				a = o('./node_modules/core-js/library/modules/_an-instance.js'),
				l = o('./node_modules/core-js/library/modules/_for-of.js'),
				c = o('./node_modules/core-js/library/modules/_iter-define.js'),
				d = o('./node_modules/core-js/library/modules/_iter-step.js'),
				u = o('./node_modules/core-js/library/modules/_set-species.js'),
				m = o('./node_modules/core-js/library/modules/_descriptors.js'),
				f = o('./node_modules/core-js/library/modules/_meta.js').fastKey,
				_ = o('./node_modules/core-js/library/modules/_validate-collection.js'),
				j = m ? '_s' : 'size',
				p = function (e, s) {
					var o,
						t = f(s);
					if ('F' !== t) return e._i[t];
					for (o = e._f; o; o = o.n) if (o.k == s) return o;
				};
			e.exports = {
				getConstructor: function (e, s, o, c) {
					var d = e(function (e, t) {
						a(e, d, s, '_i'),
							(e._t = s),
							(e._i = r(null)),
							(e._f = void 0),
							(e._l = void 0),
							(e[j] = 0),
							null != t && l(t, o, e[c], e);
					});
					return (
						n(d.prototype, {
							clear: function () {
								for (var e = _(this, s), o = e._i, t = e._f; t; t = t.n)
									(t.r = !0), t.p && (t.p = t.p.n = void 0), delete o[t.i];
								(e._f = e._l = void 0), (e[j] = 0);
							},
							delete: function (e) {
								var o = _(this, s),
									t = p(o, e);
								if (t) {
									var r = t.n,
										n = t.p;
									delete o._i[t.i],
										(t.r = !0),
										n && (n.n = r),
										r && (r.p = n),
										o._f == t && (o._f = r),
										o._l == t && (o._l = n),
										o[j]--;
								}
								return !!t;
							},
							forEach: function (e) {
								_(this, s);
								for (
									var o, t = i(e, arguments.length > 1 ? arguments[1] : void 0, 3);
									(o = o ? o.n : this._f);

								)
									for (t(o.v, o.k, this); o && o.r; ) o = o.p;
							},
							has: function (e) {
								return !!p(_(this, s), e);
							}
						}),
						m &&
							t(d.prototype, 'size', {
								get: function () {
									return _(this, s)[j];
								}
							}),
						d
					);
				},
				def: function (e, s, o) {
					var t,
						r,
						n = p(e, s);
					return (
						n
							? (n.v = o)
							: ((e._l = n = { i: (r = f(s, !0)), k: s, v: o, p: (t = e._l), n: void 0, r: !1 }),
							  e._f || (e._f = n),
							  t && (t.n = n),
							  e[j]++,
							  'F' !== r && (e._i[r] = n)),
						e
					);
				},
				getEntry: p,
				setStrong: function (e, s, o) {
					c(
						e,
						s,
						function (e, o) {
							(this._t = _(e, s)), (this._k = o), (this._l = void 0);
						},
						function () {
							for (var e = this._k, s = this._l; s && s.r; ) s = s.p;
							return this._t && (this._l = s = s ? s.n : this._t._f)
								? d(0, 'keys' == e ? s.k : 'values' == e ? s.v : [s.k, s.v])
								: ((this._t = void 0), d(1));
						},
						o ? 'entries' : 'values',
						!o,
						!0
					),
						u(s);
				}
			};
		},
		'./node_modules/core-js/library/modules/_collection-to-json.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_classof.js'),
				r = o('./node_modules/core-js/library/modules/_array-from-iterable.js');
			e.exports = function (e) {
				return function () {
					if (t(this) != e) throw TypeError(e + "#toJSON isn't generic");
					return r(this);
				};
			};
		},
		'./node_modules/core-js/library/modules/_collection.js': function (e, s, o) {
			'use strict';
			var t = o('./node_modules/core-js/library/modules/_global.js'),
				r = o('./node_modules/core-js/library/modules/_export.js'),
				n = o('./node_modules/core-js/library/modules/_meta.js'),
				i = o('./node_modules/core-js/library/modules/_fails.js'),
				a = o('./node_modules/core-js/library/modules/_hide.js'),
				l = o('./node_modules/core-js/library/modules/_redefine-all.js'),
				c = o('./node_modules/core-js/library/modules/_for-of.js'),
				d = o('./node_modules/core-js/library/modules/_an-instance.js'),
				u = o('./node_modules/core-js/library/modules/_is-object.js'),
				m = o('./node_modules/core-js/library/modules/_set-to-string-tag.js'),
				f = o('./node_modules/core-js/library/modules/_object-dp.js').f,
				_ = o('./node_modules/core-js/library/modules/_array-methods.js')(0),
				j = o('./node_modules/core-js/library/modules/_descriptors.js');
			e.exports = function (e, s, o, p, g, y) {
				var h = t[e],
					b = h,
					S = g ? 'set' : 'add',
					v = b && b.prototype,
					C = {};
				return (
					j &&
					'function' == typeof b &&
					(y ||
						(v.forEach &&
							!i(function () {
								new b().entries().next();
							})))
						? ((b = s(function (s, o) {
								d(s, b, e, '_c'), (s._c = new h()), null != o && c(o, g, s[S], s);
						  })),
						  _(
								'add,clear,delete,forEach,get,has,set,keys,values,entries,toJSON'.split(','),
								function (e) {
									var s = 'add' == e || 'set' == e;
									e in v &&
										(!y || 'clear' != e) &&
										a(b.prototype, e, function (o, t) {
											if ((d(this, b, e), !s && y && !u(o))) return 'get' == e && void 0;
											var r = this._c[e](0 === o ? 0 : o, t);
											return s ? this : r;
										});
								}
						  ),
						  y ||
								f(b.prototype, 'size', {
									get: function () {
										return this._c.size;
									}
								}))
						: ((b = p.getConstructor(s, e, g, S)), l(b.prototype, o), (n.NEED = !0)),
					m(b, e),
					(C[e] = b),
					r(r.G + r.W + r.F, C),
					y || p.setStrong(b, e, g),
					b
				);
			};
		},
		'./node_modules/core-js/library/modules/_core.js': function (e, s) {
			var o = (e.exports = { version: '2.6.10' });
			'number' == typeof __e && (__e = o);
		},
		'./node_modules/core-js/library/modules/_ctx.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_a-function.js');
			e.exports = function (e, s, o) {
				if ((t(e), void 0 === s)) return e;
				switch (o) {
					case 1:
						return function (o) {
							return e.call(s, o);
						};
					case 2:
						return function (o, t) {
							return e.call(s, o, t);
						};
					case 3:
						return function (o, t, r) {
							return e.call(s, o, t, r);
						};
				}
				return function () {
					return e.apply(s, arguments);
				};
			};
		},
		'./node_modules/core-js/library/modules/_defined.js': function (e, s) {
			e.exports = function (e) {
				if (null == e) throw TypeError("Can't call method on  " + e);
				return e;
			};
		},
		'./node_modules/core-js/library/modules/_descriptors.js': function (e, s, o) {
			e.exports = !o('./node_modules/core-js/library/modules/_fails.js')(function () {
				return (
					7 !=
					Object.defineProperty({}, 'a', {
						get: function () {
							return 7;
						}
					}).a
				);
			});
		},
		'./node_modules/core-js/library/modules/_dom-create.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_is-object.js'),
				r = o('./node_modules/core-js/library/modules/_global.js').document,
				n = t(r) && t(r.createElement);
			e.exports = function (e) {
				return n ? r.createElement(e) : {};
			};
		},
		'./node_modules/core-js/library/modules/_enum-bug-keys.js': function (e, s) {
			e.exports =
				'constructor,hasOwnProperty,isPrototypeOf,propertyIsEnumerable,toLocaleString,toString,valueOf'.split(
					','
				);
		},
		'./node_modules/core-js/library/modules/_export.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_global.js'),
				r = o('./node_modules/core-js/library/modules/_core.js'),
				n = o('./node_modules/core-js/library/modules/_ctx.js'),
				i = o('./node_modules/core-js/library/modules/_hide.js'),
				a = o('./node_modules/core-js/library/modules/_has.js'),
				l = function (e, s, o) {
					var c,
						d,
						u,
						m = e & l.F,
						f = e & l.G,
						_ = e & l.S,
						j = e & l.P,
						p = e & l.B,
						g = e & l.W,
						y = f ? r : r[s] || (r[s] = {}),
						h = y.prototype,
						b = f ? t : _ ? t[s] : (t[s] || {}).prototype;
					for (c in (f && (o = s), o))
						((d = !m && b && void 0 !== b[c]) && a(y, c)) ||
							((u = d ? b[c] : o[c]),
							(y[c] =
								f && 'function' != typeof b[c]
									? o[c]
									: p && d
									? n(u, t)
									: g && b[c] == u
									? (function (e) {
											var s = function (s, o, t) {
												if (this instanceof e) {
													switch (arguments.length) {
														case 0:
															return new e();
														case 1:
															return new e(s);
														case 2:
															return new e(s, o);
													}
													return new e(s, o, t);
												}
												return e.apply(this, arguments);
											};
											return (s.prototype = e.prototype), s;
									  })(u)
									: j && 'function' == typeof u
									? n(Function.call, u)
									: u),
							j && (((y.virtual || (y.virtual = {}))[c] = u), e & l.R && h && !h[c] && i(h, c, u)));
				};
			(l.F = 1),
				(l.G = 2),
				(l.S = 4),
				(l.P = 8),
				(l.B = 16),
				(l.W = 32),
				(l.U = 64),
				(l.R = 128),
				(e.exports = l);
		},
		'./node_modules/core-js/library/modules/_fails.js': function (e, s) {
			e.exports = function (e) {
				try {
					return !!e();
				} catch (e) {
					return !0;
				}
			};
		},
		'./node_modules/core-js/library/modules/_for-of.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_ctx.js'),
				r = o('./node_modules/core-js/library/modules/_iter-call.js'),
				n = o('./node_modules/core-js/library/modules/_is-array-iter.js'),
				i = o('./node_modules/core-js/library/modules/_an-object.js'),
				a = o('./node_modules/core-js/library/modules/_to-length.js'),
				l = o('./node_modules/core-js/library/modules/core.get-iterator-method.js'),
				c = {},
				d = {};
			((s = e.exports =
				function (e, s, o, u, m) {
					var f,
						_,
						j,
						p,
						g = m
							? function () {
									return e;
							  }
							: l(e),
						y = t(o, u, s ? 2 : 1),
						h = 0;
					if ('function' != typeof g) throw TypeError(e + ' is not iterable!');
					if (n(g)) {
						for (f = a(e.length); f > h; h++)
							if ((p = s ? y(i((_ = e[h]))[0], _[1]) : y(e[h])) === c || p === d) return p;
					} else
						for (j = g.call(e); !(_ = j.next()).done; )
							if ((p = r(j, y, _.value, s)) === c || p === d) return p;
				}).BREAK = c),
				(s.RETURN = d);
		},
		'./node_modules/core-js/library/modules/_global.js': function (e, s) {
			var o = (e.exports =
				'undefined' != typeof window && window.Math == Math
					? window
					: 'undefined' != typeof self && self.Math == Math
					? self
					: Function('return this')());
			'number' == typeof __g && (__g = o);
		},
		'./node_modules/core-js/library/modules/_has.js': function (e, s) {
			var o = {}.hasOwnProperty;
			e.exports = function (e, s) {
				return o.call(e, s);
			};
		},
		'./node_modules/core-js/library/modules/_hide.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_object-dp.js'),
				r = o('./node_modules/core-js/library/modules/_property-desc.js');
			e.exports = o('./node_modules/core-js/library/modules/_descriptors.js')
				? function (e, s, o) {
						return t.f(e, s, r(1, o));
				  }
				: function (e, s, o) {
						return (e[s] = o), e;
				  };
		},
		'./node_modules/core-js/library/modules/_html.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_global.js').document;
			e.exports = t && t.documentElement;
		},
		'./node_modules/core-js/library/modules/_ie8-dom-define.js': function (e, s, o) {
			e.exports =
				!o('./node_modules/core-js/library/modules/_descriptors.js') &&
				!o('./node_modules/core-js/library/modules/_fails.js')(function () {
					return (
						7 !=
						Object.defineProperty(
							o('./node_modules/core-js/library/modules/_dom-create.js')('div'),
							'a',
							{
								get: function () {
									return 7;
								}
							}
						).a
					);
				});
		},
		'./node_modules/core-js/library/modules/_invoke.js': function (e, s) {
			e.exports = function (e, s, o) {
				var t = void 0 === o;
				switch (s.length) {
					case 0:
						return t ? e() : e.call(o);
					case 1:
						return t ? e(s[0]) : e.call(o, s[0]);
					case 2:
						return t ? e(s[0], s[1]) : e.call(o, s[0], s[1]);
					case 3:
						return t ? e(s[0], s[1], s[2]) : e.call(o, s[0], s[1], s[2]);
					case 4:
						return t ? e(s[0], s[1], s[2], s[3]) : e.call(o, s[0], s[1], s[2], s[3]);
				}
				return e.apply(o, s);
			};
		},
		'./node_modules/core-js/library/modules/_iobject.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_cof.js');
			e.exports = Object('z').propertyIsEnumerable(0)
				? Object
				: function (e) {
						return 'String' == t(e) ? e.split('') : Object(e);
				  };
		},
		'./node_modules/core-js/library/modules/_is-array-iter.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_iterators.js'),
				r = o('./node_modules/core-js/library/modules/_wks.js')('iterator'),
				n = Array.prototype;
			e.exports = function (e) {
				return void 0 !== e && (t.Array === e || n[r] === e);
			};
		},
		'./node_modules/core-js/library/modules/_is-array.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_cof.js');
			e.exports =
				Array.isArray ||
				function (e) {
					return 'Array' == t(e);
				};
		},
		'./node_modules/core-js/library/modules/_is-object.js': function (e, s) {
			e.exports = function (e) {
				return 'object' == typeof e ? null !== e : 'function' == typeof e;
			};
		},
		'./node_modules/core-js/library/modules/_iter-call.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_an-object.js');
			e.exports = function (e, s, o, r) {
				try {
					return r ? s(t(o)[0], o[1]) : s(o);
				} catch (s) {
					var n = e.return;
					throw (void 0 !== n && t(n.call(e)), s);
				}
			};
		},
		'./node_modules/core-js/library/modules/_iter-create.js': function (e, s, o) {
			'use strict';
			var t = o('./node_modules/core-js/library/modules/_object-create.js'),
				r = o('./node_modules/core-js/library/modules/_property-desc.js'),
				n = o('./node_modules/core-js/library/modules/_set-to-string-tag.js'),
				i = {};
			o('./node_modules/core-js/library/modules/_hide.js')(
				i,
				o('./node_modules/core-js/library/modules/_wks.js')('iterator'),
				function () {
					return this;
				}
			),
				(e.exports = function (e, s, o) {
					(e.prototype = t(i, { next: r(1, o) })), n(e, s + ' Iterator');
				});
		},
		'./node_modules/core-js/library/modules/_iter-define.js': function (e, s, o) {
			'use strict';
			var t = o('./node_modules/core-js/library/modules/_library.js'),
				r = o('./node_modules/core-js/library/modules/_export.js'),
				n = o('./node_modules/core-js/library/modules/_redefine.js'),
				i = o('./node_modules/core-js/library/modules/_hide.js'),
				a = o('./node_modules/core-js/library/modules/_iterators.js'),
				l = o('./node_modules/core-js/library/modules/_iter-create.js'),
				c = o('./node_modules/core-js/library/modules/_set-to-string-tag.js'),
				d = o('./node_modules/core-js/library/modules/_object-gpo.js'),
				u = o('./node_modules/core-js/library/modules/_wks.js')('iterator'),
				m = !([].keys && 'next' in [].keys()),
				f = function () {
					return this;
				};
			e.exports = function (e, s, o, _, j, p, g) {
				l(o, s, _);
				var y,
					h,
					b,
					S = function (e) {
						if (!m && e in M) return M[e];
						switch (e) {
							case 'keys':
							case 'values':
								return function () {
									return new o(this, e);
								};
						}
						return function () {
							return new o(this, e);
						};
					},
					v = s + ' Iterator',
					C = 'values' == j,
					T = !1,
					M = e.prototype,
					O = M[u] || M['@@iterator'] || (j && M[j]),
					E = O || S(j),
					I = j ? (C ? S('entries') : E) : void 0,
					x = ('Array' == s && M.entries) || O;
				if (
					(x &&
						(b = d(x.call(new e()))) !== Object.prototype &&
						b.next &&
						(c(b, v, !0), t || 'function' == typeof b[u] || i(b, u, f)),
					C &&
						O &&
						'values' !== O.name &&
						((T = !0),
						(E = function () {
							return O.call(this);
						})),
					(t && !g) || (!m && !T && M[u]) || i(M, u, E),
					(a[s] = E),
					(a[v] = f),
					j)
				)
					if (((y = { values: C ? E : S('values'), keys: p ? E : S('keys'), entries: I }), g))
						for (h in y) h in M || n(M, h, y[h]);
					else r(r.P + r.F * (m || T), s, y);
				return y;
			};
		},
		'./node_modules/core-js/library/modules/_iter-detect.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_wks.js')('iterator'),
				r = !1;
			try {
				var n = [7][t]();
				(n.return = function () {
					r = !0;
				}),
					Array.from(n, function () {
						throw 2;
					});
			} catch (e) {}
			e.exports = function (e, s) {
				if (!s && !r) return !1;
				var o = !1;
				try {
					var n = [7],
						i = n[t]();
					(i.next = function () {
						return { done: (o = !0) };
					}),
						(n[t] = function () {
							return i;
						}),
						e(n);
				} catch (e) {}
				return o;
			};
		},
		'./node_modules/core-js/library/modules/_iter-step.js': function (e, s) {
			e.exports = function (e, s) {
				return { value: s, done: !!e };
			};
		},
		'./node_modules/core-js/library/modules/_iterators.js': function (e, s) {
			e.exports = {};
		},
		'./node_modules/core-js/library/modules/_library.js': function (e, s) {
			e.exports = !0;
		},
		'./node_modules/core-js/library/modules/_meta.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_uid.js')('meta'),
				r = o('./node_modules/core-js/library/modules/_is-object.js'),
				n = o('./node_modules/core-js/library/modules/_has.js'),
				i = o('./node_modules/core-js/library/modules/_object-dp.js').f,
				a = 0,
				l =
					Object.isExtensible ||
					function () {
						return !0;
					},
				c = !o('./node_modules/core-js/library/modules/_fails.js')(function () {
					return l(Object.preventExtensions({}));
				}),
				d = function (e) {
					i(e, t, { value: { i: 'O' + ++a, w: {} } });
				},
				u = (e.exports = {
					KEY: t,
					NEED: !1,
					fastKey: function (e, s) {
						if (!r(e)) return 'symbol' == typeof e ? e : ('string' == typeof e ? 'S' : 'P') + e;
						if (!n(e, t)) {
							if (!l(e)) return 'F';
							if (!s) return 'E';
							d(e);
						}
						return e[t].i;
					},
					getWeak: function (e, s) {
						if (!n(e, t)) {
							if (!l(e)) return !0;
							if (!s) return !1;
							d(e);
						}
						return e[t].w;
					},
					onFreeze: function (e) {
						return c && u.NEED && l(e) && !n(e, t) && d(e), e;
					}
				});
		},
		'./node_modules/core-js/library/modules/_microtask.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_global.js'),
				r = o('./node_modules/core-js/library/modules/_task.js').set,
				n = t.MutationObserver || t.WebKitMutationObserver,
				i = t.process,
				a = t.Promise,
				l = 'process' == o('./node_modules/core-js/library/modules/_cof.js')(i);
			e.exports = function () {
				var e,
					s,
					o,
					c = function () {
						var t, r;
						for (l && (t = i.domain) && t.exit(); e; ) {
							(r = e.fn), (e = e.next);
							try {
								r();
							} catch (t) {
								throw (e ? o() : (s = void 0), t);
							}
						}
						(s = void 0), t && t.enter();
					};
				if (l)
					o = function () {
						i.nextTick(c);
					};
				else if (!n || (t.navigator && t.navigator.standalone))
					if (a && a.resolve) {
						var d = a.resolve(void 0);
						o = function () {
							d.then(c);
						};
					} else
						o = function () {
							r.call(t, c);
						};
				else {
					var u = !0,
						m = document.createTextNode('');
					new n(c).observe(m, { characterData: !0 }),
						(o = function () {
							m.data = u = !u;
						});
				}
				return function (t) {
					var r = { fn: t, next: void 0 };
					s && (s.next = r), e || ((e = r), o()), (s = r);
				};
			};
		},
		'./node_modules/core-js/library/modules/_new-promise-capability.js': function (e, s, o) {
			'use strict';
			var t = o('./node_modules/core-js/library/modules/_a-function.js');
			function r(e) {
				var s, o;
				(this.promise = new e(function (e, t) {
					if (void 0 !== s || void 0 !== o) throw TypeError('Bad Promise constructor');
					(s = e), (o = t);
				})),
					(this.resolve = t(s)),
					(this.reject = t(o));
			}
			e.exports.f = function (e) {
				return new r(e);
			};
		},
		'./node_modules/core-js/library/modules/_object-assign.js': function (e, s, o) {
			'use strict';
			var t = o('./node_modules/core-js/library/modules/_descriptors.js'),
				r = o('./node_modules/core-js/library/modules/_object-keys.js'),
				n = o('./node_modules/core-js/library/modules/_object-gops.js'),
				i = o('./node_modules/core-js/library/modules/_object-pie.js'),
				a = o('./node_modules/core-js/library/modules/_to-object.js'),
				l = o('./node_modules/core-js/library/modules/_iobject.js'),
				c = Object.assign;
			e.exports =
				!c ||
				o('./node_modules/core-js/library/modules/_fails.js')(function () {
					var e = {},
						s = {},
						o = Symbol(),
						t = 'abcdefghijklmnopqrst';
					return (
						(e[o] = 7),
						t.split('').forEach(function (e) {
							s[e] = e;
						}),
						7 != c({}, e)[o] || Object.keys(c({}, s)).join('') != t
					);
				})
					? function (e, s) {
							for (var o = a(e), c = arguments.length, d = 1, u = n.f, m = i.f; c > d; )
								for (
									var f,
										_ = l(arguments[d++]),
										j = u ? r(_).concat(u(_)) : r(_),
										p = j.length,
										g = 0;
									p > g;

								)
									(f = j[g++]), (t && !m.call(_, f)) || (o[f] = _[f]);
							return o;
					  }
					: c;
		},
		'./node_modules/core-js/library/modules/_object-create.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_an-object.js'),
				r = o('./node_modules/core-js/library/modules/_object-dps.js'),
				n = o('./node_modules/core-js/library/modules/_enum-bug-keys.js'),
				i = o('./node_modules/core-js/library/modules/_shared-key.js')('IE_PROTO'),
				a = function () {},
				l = function () {
					var e,
						s = o('./node_modules/core-js/library/modules/_dom-create.js')('iframe'),
						t = n.length;
					for (
						s.style.display = 'none',
							o('./node_modules/core-js/library/modules/_html.js').appendChild(s),
							s.src = 'javascript:',
							(e = s.contentWindow.document).open(),
							e.write('<script>document.F=Object</script>'),
							e.close(),
							l = e.F;
						t--;

					)
						delete l.prototype[n[t]];
					return l();
				};
			e.exports =
				Object.create ||
				function (e, s) {
					var o;
					return (
						null !== e
							? ((a.prototype = t(e)), (o = new a()), (a.prototype = null), (o[i] = e))
							: (o = l()),
						void 0 === s ? o : r(o, s)
					);
				};
		},
		'./node_modules/core-js/library/modules/_object-dp.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_an-object.js'),
				r = o('./node_modules/core-js/library/modules/_ie8-dom-define.js'),
				n = o('./node_modules/core-js/library/modules/_to-primitive.js'),
				i = Object.defineProperty;
			s.f = o('./node_modules/core-js/library/modules/_descriptors.js')
				? Object.defineProperty
				: function (e, s, o) {
						if ((t(e), (s = n(s, !0)), t(o), r))
							try {
								return i(e, s, o);
							} catch (e) {}
						if ('get' in o || 'set' in o) throw TypeError('Accessors not supported!');
						return 'value' in o && (e[s] = o.value), e;
				  };
		},
		'./node_modules/core-js/library/modules/_object-dps.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_object-dp.js'),
				r = o('./node_modules/core-js/library/modules/_an-object.js'),
				n = o('./node_modules/core-js/library/modules/_object-keys.js');
			e.exports = o('./node_modules/core-js/library/modules/_descriptors.js')
				? Object.defineProperties
				: function (e, s) {
						r(e);
						for (var o, i = n(s), a = i.length, l = 0; a > l; ) t.f(e, (o = i[l++]), s[o]);
						return e;
				  };
		},
		'./node_modules/core-js/library/modules/_object-gops.js': function (e, s) {
			s.f = Object.getOwnPropertySymbols;
		},
		'./node_modules/core-js/library/modules/_object-gpo.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_has.js'),
				r = o('./node_modules/core-js/library/modules/_to-object.js'),
				n = o('./node_modules/core-js/library/modules/_shared-key.js')('IE_PROTO'),
				i = Object.prototype;
			e.exports =
				Object.getPrototypeOf ||
				function (e) {
					return (
						(e = r(e)),
						t(e, n)
							? e[n]
							: 'function' == typeof e.constructor && e instanceof e.constructor
							? e.constructor.prototype
							: e instanceof Object
							? i
							: null
					);
				};
		},
		'./node_modules/core-js/library/modules/_object-keys-internal.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_has.js'),
				r = o('./node_modules/core-js/library/modules/_to-iobject.js'),
				n = o('./node_modules/core-js/library/modules/_array-includes.js')(!1),
				i = o('./node_modules/core-js/library/modules/_shared-key.js')('IE_PROTO');
			e.exports = function (e, s) {
				var o,
					a = r(e),
					l = 0,
					c = [];
				for (o in a) o != i && t(a, o) && c.push(o);
				for (; s.length > l; ) t(a, (o = s[l++])) && (~n(c, o) || c.push(o));
				return c;
			};
		},
		'./node_modules/core-js/library/modules/_object-keys.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_object-keys-internal.js'),
				r = o('./node_modules/core-js/library/modules/_enum-bug-keys.js');
			e.exports =
				Object.keys ||
				function (e) {
					return t(e, r);
				};
		},
		'./node_modules/core-js/library/modules/_object-pie.js': function (e, s) {
			s.f = {}.propertyIsEnumerable;
		},
		'./node_modules/core-js/library/modules/_perform.js': function (e, s) {
			e.exports = function (e) {
				try {
					return { e: !1, v: e() };
				} catch (e) {
					return { e: !0, v: e };
				}
			};
		},
		'./node_modules/core-js/library/modules/_promise-resolve.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_an-object.js'),
				r = o('./node_modules/core-js/library/modules/_is-object.js'),
				n = o('./node_modules/core-js/library/modules/_new-promise-capability.js');
			e.exports = function (e, s) {
				if ((t(e), r(s) && s.constructor === e)) return s;
				var o = n.f(e);
				return (0, o.resolve)(s), o.promise;
			};
		},
		'./node_modules/core-js/library/modules/_property-desc.js': function (e, s) {
			e.exports = function (e, s) {
				return { enumerable: !(1 & e), configurable: !(2 & e), writable: !(4 & e), value: s };
			};
		},
		'./node_modules/core-js/library/modules/_redefine-all.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_hide.js');
			e.exports = function (e, s, o) {
				for (var r in s) o && e[r] ? (e[r] = s[r]) : t(e, r, s[r]);
				return e;
			};
		},
		'./node_modules/core-js/library/modules/_redefine.js': function (e, s, o) {
			e.exports = o('./node_modules/core-js/library/modules/_hide.js');
		},
		'./node_modules/core-js/library/modules/_set-collection-from.js': function (e, s, o) {
			'use strict';
			var t = o('./node_modules/core-js/library/modules/_export.js'),
				r = o('./node_modules/core-js/library/modules/_a-function.js'),
				n = o('./node_modules/core-js/library/modules/_ctx.js'),
				i = o('./node_modules/core-js/library/modules/_for-of.js');
			e.exports = function (e) {
				t(t.S, e, {
					from: function (e) {
						var s,
							o,
							t,
							a,
							l = arguments[1];
						return (
							r(this),
							(s = void 0 !== l) && r(l),
							null == e
								? new this()
								: ((o = []),
								  s
										? ((t = 0),
										  (a = n(l, arguments[2], 2)),
										  i(e, !1, function (e) {
												o.push(a(e, t++));
										  }))
										: i(e, !1, o.push, o),
								  new this(o))
						);
					}
				});
			};
		},
		'./node_modules/core-js/library/modules/_set-collection-of.js': function (e, s, o) {
			'use strict';
			var t = o('./node_modules/core-js/library/modules/_export.js');
			e.exports = function (e) {
				t(t.S, e, {
					of: function () {
						for (var e = arguments.length, s = new Array(e); e--; ) s[e] = arguments[e];
						return new this(s);
					}
				});
			};
		},
		'./node_modules/core-js/library/modules/_set-species.js': function (e, s, o) {
			'use strict';
			var t = o('./node_modules/core-js/library/modules/_global.js'),
				r = o('./node_modules/core-js/library/modules/_core.js'),
				n = o('./node_modules/core-js/library/modules/_object-dp.js'),
				i = o('./node_modules/core-js/library/modules/_descriptors.js'),
				a = o('./node_modules/core-js/library/modules/_wks.js')('species');
			e.exports = function (e) {
				var s = 'function' == typeof r[e] ? r[e] : t[e];
				i &&
					s &&
					!s[a] &&
					n.f(s, a, {
						configurable: !0,
						get: function () {
							return this;
						}
					});
			};
		},
		'./node_modules/core-js/library/modules/_set-to-string-tag.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_object-dp.js').f,
				r = o('./node_modules/core-js/library/modules/_has.js'),
				n = o('./node_modules/core-js/library/modules/_wks.js')('toStringTag');
			e.exports = function (e, s, o) {
				e && !r((e = o ? e : e.prototype), n) && t(e, n, { configurable: !0, value: s });
			};
		},
		'./node_modules/core-js/library/modules/_shared-key.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_shared.js')('keys'),
				r = o('./node_modules/core-js/library/modules/_uid.js');
			e.exports = function (e) {
				return t[e] || (t[e] = r(e));
			};
		},
		'./node_modules/core-js/library/modules/_shared.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_core.js'),
				r = o('./node_modules/core-js/library/modules/_global.js'),
				n = r['__core-js_shared__'] || (r['__core-js_shared__'] = {});
			(e.exports = function (e, s) {
				return n[e] || (n[e] = void 0 !== s ? s : {});
			})('versions', []).push({
				version: t.version,
				mode: o('./node_modules/core-js/library/modules/_library.js') ? 'pure' : 'global',
				copyright: '© 2019 Denis Pushkarev (zloirock.ru)'
			});
		},
		'./node_modules/core-js/library/modules/_species-constructor.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_an-object.js'),
				r = o('./node_modules/core-js/library/modules/_a-function.js'),
				n = o('./node_modules/core-js/library/modules/_wks.js')('species');
			e.exports = function (e, s) {
				var o,
					i = t(e).constructor;
				return void 0 === i || null == (o = t(i)[n]) ? s : r(o);
			};
		},
		'./node_modules/core-js/library/modules/_string-at.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_to-integer.js'),
				r = o('./node_modules/core-js/library/modules/_defined.js');
			e.exports = function (e) {
				return function (s, o) {
					var n,
						i,
						a = String(r(s)),
						l = t(o),
						c = a.length;
					return l < 0 || l >= c
						? e
							? ''
							: void 0
						: (n = a.charCodeAt(l)) < 55296 ||
						  n > 56319 ||
						  l + 1 === c ||
						  (i = a.charCodeAt(l + 1)) < 56320 ||
						  i > 57343
						? e
							? a.charAt(l)
							: n
						: e
						? a.slice(l, l + 2)
						: i - 56320 + ((n - 55296) << 10) + 65536;
				};
			};
		},
		'./node_modules/core-js/library/modules/_task.js': function (e, s, o) {
			var t,
				r,
				n,
				i = o('./node_modules/core-js/library/modules/_ctx.js'),
				a = o('./node_modules/core-js/library/modules/_invoke.js'),
				l = o('./node_modules/core-js/library/modules/_html.js'),
				c = o('./node_modules/core-js/library/modules/_dom-create.js'),
				d = o('./node_modules/core-js/library/modules/_global.js'),
				u = d.process,
				m = d.setImmediate,
				f = d.clearImmediate,
				_ = d.MessageChannel,
				j = d.Dispatch,
				p = 0,
				g = {},
				y = function () {
					var e = +this;
					if (g.hasOwnProperty(e)) {
						var s = g[e];
						delete g[e], s();
					}
				},
				h = function (e) {
					y.call(e.data);
				};
			(m && f) ||
				((m = function (e) {
					for (var s = [], o = 1; arguments.length > o; ) s.push(arguments[o++]);
					return (
						(g[++p] = function () {
							a('function' == typeof e ? e : Function(e), s);
						}),
						t(p),
						p
					);
				}),
				(f = function (e) {
					delete g[e];
				}),
				'process' == o('./node_modules/core-js/library/modules/_cof.js')(u)
					? (t = function (e) {
							u.nextTick(i(y, e, 1));
					  })
					: j && j.now
					? (t = function (e) {
							j.now(i(y, e, 1));
					  })
					: _
					? ((n = (r = new _()).port2), (r.port1.onmessage = h), (t = i(n.postMessage, n, 1)))
					: d.addEventListener && 'function' == typeof postMessage && !d.importScripts
					? ((t = function (e) {
							d.postMessage(e + '', '*');
					  }),
					  d.addEventListener('message', h, !1))
					: (t =
							'onreadystatechange' in c('script')
								? function (e) {
										l.appendChild(c('script')).onreadystatechange = function () {
											l.removeChild(this), y.call(e);
										};
								  }
								: function (e) {
										setTimeout(i(y, e, 1), 0);
								  })),
				(e.exports = { set: m, clear: f });
		},
		'./node_modules/core-js/library/modules/_to-absolute-index.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_to-integer.js'),
				r = Math.max,
				n = Math.min;
			e.exports = function (e, s) {
				return (e = t(e)) < 0 ? r(e + s, 0) : n(e, s);
			};
		},
		'./node_modules/core-js/library/modules/_to-integer.js': function (e, s) {
			var o = Math.ceil,
				t = Math.floor;
			e.exports = function (e) {
				return isNaN((e = +e)) ? 0 : (e > 0 ? t : o)(e);
			};
		},
		'./node_modules/core-js/library/modules/_to-iobject.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_iobject.js'),
				r = o('./node_modules/core-js/library/modules/_defined.js');
			e.exports = function (e) {
				return t(r(e));
			};
		},
		'./node_modules/core-js/library/modules/_to-length.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_to-integer.js'),
				r = Math.min;
			e.exports = function (e) {
				return e > 0 ? r(t(e), 9007199254740991) : 0;
			};
		},
		'./node_modules/core-js/library/modules/_to-object.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_defined.js');
			e.exports = function (e) {
				return Object(t(e));
			};
		},
		'./node_modules/core-js/library/modules/_to-primitive.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_is-object.js');
			e.exports = function (e, s) {
				if (!t(e)) return e;
				var o, r;
				if (s && 'function' == typeof (o = e.toString) && !t((r = o.call(e)))) return r;
				if ('function' == typeof (o = e.valueOf) && !t((r = o.call(e)))) return r;
				if (!s && 'function' == typeof (o = e.toString) && !t((r = o.call(e)))) return r;
				throw TypeError("Can't convert object to primitive value");
			};
		},
		'./node_modules/core-js/library/modules/_uid.js': function (e, s) {
			var o = 0,
				t = Math.random();
			e.exports = function (e) {
				return 'Symbol('.concat(void 0 === e ? '' : e, ')_', (++o + t).toString(36));
			};
		},
		'./node_modules/core-js/library/modules/_user-agent.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_global.js').navigator;
			e.exports = (t && t.userAgent) || '';
		},
		'./node_modules/core-js/library/modules/_validate-collection.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_is-object.js');
			e.exports = function (e, s) {
				if (!t(e) || e._t !== s) throw TypeError('Incompatible receiver, ' + s + ' required!');
				return e;
			};
		},
		'./node_modules/core-js/library/modules/_wks.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_shared.js')('wks'),
				r = o('./node_modules/core-js/library/modules/_uid.js'),
				n = o('./node_modules/core-js/library/modules/_global.js').Symbol,
				i = 'function' == typeof n;
			(e.exports = function (e) {
				return t[e] || (t[e] = (i && n[e]) || (i ? n : r)('Symbol.' + e));
			}).store = t;
		},
		'./node_modules/core-js/library/modules/core.get-iterator-method.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_classof.js'),
				r = o('./node_modules/core-js/library/modules/_wks.js')('iterator'),
				n = o('./node_modules/core-js/library/modules/_iterators.js');
			e.exports = o('./node_modules/core-js/library/modules/_core.js').getIteratorMethod =
				function (e) {
					if (null != e) return e[r] || e['@@iterator'] || n[t(e)];
				};
		},
		'./node_modules/core-js/library/modules/es6.array.iterator.js': function (e, s, o) {
			'use strict';
			var t = o('./node_modules/core-js/library/modules/_add-to-unscopables.js'),
				r = o('./node_modules/core-js/library/modules/_iter-step.js'),
				n = o('./node_modules/core-js/library/modules/_iterators.js'),
				i = o('./node_modules/core-js/library/modules/_to-iobject.js');
			(e.exports = o('./node_modules/core-js/library/modules/_iter-define.js')(
				Array,
				'Array',
				function (e, s) {
					(this._t = i(e)), (this._i = 0), (this._k = s);
				},
				function () {
					var e = this._t,
						s = this._k,
						o = this._i++;
					return !e || o >= e.length
						? ((this._t = void 0), r(1))
						: r(0, 'keys' == s ? o : 'values' == s ? e[o] : [o, e[o]]);
				},
				'values'
			)),
				(n.Arguments = n.Array),
				t('keys'),
				t('values'),
				t('entries');
		},
		'./node_modules/core-js/library/modules/es6.map.js': function (e, s, o) {
			'use strict';
			var t = o('./node_modules/core-js/library/modules/_collection-strong.js'),
				r = o('./node_modules/core-js/library/modules/_validate-collection.js');
			e.exports = o('./node_modules/core-js/library/modules/_collection.js')(
				'Map',
				function (e) {
					return function () {
						return e(this, arguments.length > 0 ? arguments[0] : void 0);
					};
				},
				{
					get: function (e) {
						var s = t.getEntry(r(this, 'Map'), e);
						return s && s.v;
					},
					set: function (e, s) {
						return t.def(r(this, 'Map'), 0 === e ? 0 : e, s);
					}
				},
				t,
				!0
			);
		},
		'./node_modules/core-js/library/modules/es6.object.assign.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_export.js');
			t(t.S + t.F, 'Object', {
				assign: o('./node_modules/core-js/library/modules/_object-assign.js')
			});
		},
		'./node_modules/core-js/library/modules/es6.object.to-string.js': function (e, s) {},
		'./node_modules/core-js/library/modules/es6.promise.js': function (e, s, o) {
			'use strict';
			var t,
				r,
				n,
				i,
				a = o('./node_modules/core-js/library/modules/_library.js'),
				l = o('./node_modules/core-js/library/modules/_global.js'),
				c = o('./node_modules/core-js/library/modules/_ctx.js'),
				d = o('./node_modules/core-js/library/modules/_classof.js'),
				u = o('./node_modules/core-js/library/modules/_export.js'),
				m = o('./node_modules/core-js/library/modules/_is-object.js'),
				f = o('./node_modules/core-js/library/modules/_a-function.js'),
				_ = o('./node_modules/core-js/library/modules/_an-instance.js'),
				j = o('./node_modules/core-js/library/modules/_for-of.js'),
				p = o('./node_modules/core-js/library/modules/_species-constructor.js'),
				g = o('./node_modules/core-js/library/modules/_task.js').set,
				y = o('./node_modules/core-js/library/modules/_microtask.js')(),
				h = o('./node_modules/core-js/library/modules/_new-promise-capability.js'),
				b = o('./node_modules/core-js/library/modules/_perform.js'),
				S = o('./node_modules/core-js/library/modules/_user-agent.js'),
				v = o('./node_modules/core-js/library/modules/_promise-resolve.js'),
				C = l.TypeError,
				T = l.process,
				M = T && T.versions,
				O = (M && M.v8) || '',
				E = l.Promise,
				I = 'process' == d(T),
				x = function () {},
				w = (r = h.f),
				k = !!(function () {
					try {
						var e = E.resolve(1),
							s = ((e.constructor = {})[
								o('./node_modules/core-js/library/modules/_wks.js')('species')
							] = function (e) {
								e(x, x);
							});
						return (
							(I || 'function' == typeof PromiseRejectionEvent) &&
							e.then(x) instanceof s &&
							0 !== O.indexOf('6.6') &&
							-1 === S.indexOf('Chrome/66')
						);
					} catch (e) {}
				})(),
				A = function (e) {
					var s;
					return !(!m(e) || 'function' != typeof (s = e.then)) && s;
				},
				N = function (e, s) {
					if (!e._n) {
						e._n = !0;
						var o = e._c;
						y(function () {
							for (
								var t = e._v,
									r = 1 == e._s,
									n = 0,
									i = function (s) {
										var o,
											n,
											i,
											a = r ? s.ok : s.fail,
											l = s.resolve,
											c = s.reject,
											d = s.domain;
										try {
											a
												? (r || (2 == e._h && R(e), (e._h = 1)),
												  !0 === a
														? (o = t)
														: (d && d.enter(), (o = a(t)), d && (d.exit(), (i = !0))),
												  o === s.promise
														? c(C('Promise-chain cycle'))
														: (n = A(o))
														? n.call(o, l, c)
														: l(o))
												: c(t);
										} catch (e) {
											d && !i && d.exit(), c(e);
										}
									};
								o.length > n;

							)
								i(o[n++]);
							(e._c = []), (e._n = !1), s && !e._h && U(e);
						});
					}
				},
				U = function (e) {
					g.call(l, function () {
						var s,
							o,
							t,
							r = e._v,
							n = P(e);
						if (
							(n &&
								((s = b(function () {
									I
										? T.emit('unhandledRejection', r, e)
										: (o = l.onunhandledrejection)
										? o({ promise: e, reason: r })
										: (t = l.console) && t.error && t.error('Unhandled promise rejection', r);
								})),
								(e._h = I || P(e) ? 2 : 1)),
							(e._a = void 0),
							n && s.e)
						)
							throw s.v;
					});
				},
				P = function (e) {
					return 1 !== e._h && 0 === (e._a || e._c).length;
				},
				R = function (e) {
					g.call(l, function () {
						var s;
						I
							? T.emit('rejectionHandled', e)
							: (s = l.onrejectionhandled) && s({ promise: e, reason: e._v });
					});
				},
				V = function (e) {
					var s = this;
					s._d ||
						((s._d = !0),
						((s = s._w || s)._v = e),
						(s._s = 2),
						s._a || (s._a = s._c.slice()),
						N(s, !0));
				},
				D = function (e) {
					var s,
						o = this;
					if (!o._d) {
						(o._d = !0), (o = o._w || o);
						try {
							if (o === e) throw C("Promise can't be resolved itself");
							(s = A(e))
								? y(function () {
										var t = { _w: o, _d: !1 };
										try {
											s.call(e, c(D, t, 1), c(V, t, 1));
										} catch (e) {
											V.call(t, e);
										}
								  })
								: ((o._v = e), (o._s = 1), N(o, !1));
						} catch (e) {
							V.call({ _w: o, _d: !1 }, e);
						}
					}
				};
			k ||
				((E = function (e) {
					_(this, E, 'Promise', '_h'), f(e), t.call(this);
					try {
						e(c(D, this, 1), c(V, this, 1));
					} catch (e) {
						V.call(this, e);
					}
				}),
				((t = function (e) {
					(this._c = []),
						(this._a = void 0),
						(this._s = 0),
						(this._d = !1),
						(this._v = void 0),
						(this._h = 0),
						(this._n = !1);
				}).prototype = o('./node_modules/core-js/library/modules/_redefine-all.js')(E.prototype, {
					then: function (e, s) {
						var o = w(p(this, E));
						return (
							(o.ok = 'function' != typeof e || e),
							(o.fail = 'function' == typeof s && s),
							(o.domain = I ? T.domain : void 0),
							this._c.push(o),
							this._a && this._a.push(o),
							this._s && N(this, !1),
							o.promise
						);
					},
					catch: function (e) {
						return this.then(void 0, e);
					}
				})),
				(n = function () {
					var e = new t();
					(this.promise = e), (this.resolve = c(D, e, 1)), (this.reject = c(V, e, 1));
				}),
				(h.f = w =
					function (e) {
						return e === E || e === i ? new n(e) : r(e);
					})),
				u(u.G + u.W + u.F * !k, { Promise: E }),
				o('./node_modules/core-js/library/modules/_set-to-string-tag.js')(E, 'Promise'),
				o('./node_modules/core-js/library/modules/_set-species.js')('Promise'),
				(i = o('./node_modules/core-js/library/modules/_core.js').Promise),
				u(u.S + u.F * !k, 'Promise', {
					reject: function (e) {
						var s = w(this);
						return (0, s.reject)(e), s.promise;
					}
				}),
				u(u.S + u.F * (a || !k), 'Promise', {
					resolve: function (e) {
						return v(a && this === i ? E : this, e);
					}
				}),
				u(
					u.S +
						u.F *
							!(
								k &&
								o('./node_modules/core-js/library/modules/_iter-detect.js')(function (e) {
									E.all(e).catch(x);
								})
							),
					'Promise',
					{
						all: function (e) {
							var s = this,
								o = w(s),
								t = o.resolve,
								r = o.reject,
								n = b(function () {
									var o = [],
										n = 0,
										i = 1;
									j(e, !1, function (e) {
										var a = n++,
											l = !1;
										o.push(void 0),
											i++,
											s.resolve(e).then(function (e) {
												l || ((l = !0), (o[a] = e), --i || t(o));
											}, r);
									}),
										--i || t(o);
								});
							return n.e && r(n.v), o.promise;
						},
						race: function (e) {
							var s = this,
								o = w(s),
								t = o.reject,
								r = b(function () {
									j(e, !1, function (e) {
										s.resolve(e).then(o.resolve, t);
									});
								});
							return r.e && t(r.v), o.promise;
						}
					}
				);
		},
		'./node_modules/core-js/library/modules/es6.string.iterator.js': function (e, s, o) {
			'use strict';
			var t = o('./node_modules/core-js/library/modules/_string-at.js')(!0);
			o('./node_modules/core-js/library/modules/_iter-define.js')(
				String,
				'String',
				function (e) {
					(this._t = String(e)), (this._i = 0);
				},
				function () {
					var e,
						s = this._t,
						o = this._i;
					return o >= s.length
						? { value: void 0, done: !0 }
						: ((e = t(s, o)), (this._i += e.length), { value: e, done: !1 });
				}
			);
		},
		'./node_modules/core-js/library/modules/es7.map.from.js': function (e, s, o) {
			o('./node_modules/core-js/library/modules/_set-collection-from.js')('Map');
		},
		'./node_modules/core-js/library/modules/es7.map.of.js': function (e, s, o) {
			o('./node_modules/core-js/library/modules/_set-collection-of.js')('Map');
		},
		'./node_modules/core-js/library/modules/es7.map.to-json.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_export.js');
			t(t.P + t.R, 'Map', {
				toJSON: o('./node_modules/core-js/library/modules/_collection-to-json.js')('Map')
			});
		},
		'./node_modules/core-js/library/modules/es7.promise.finally.js': function (e, s, o) {
			'use strict';
			var t = o('./node_modules/core-js/library/modules/_export.js'),
				r = o('./node_modules/core-js/library/modules/_core.js'),
				n = o('./node_modules/core-js/library/modules/_global.js'),
				i = o('./node_modules/core-js/library/modules/_species-constructor.js'),
				a = o('./node_modules/core-js/library/modules/_promise-resolve.js');
			t(t.P + t.R, 'Promise', {
				finally: function (e) {
					var s = i(this, r.Promise || n.Promise),
						o = 'function' == typeof e;
					return this.then(
						o
							? function (o) {
									return a(s, e()).then(function () {
										return o;
									});
							  }
							: e,
						o
							? function (o) {
									return a(s, e()).then(function () {
										throw o;
									});
							  }
							: e
					);
				}
			});
		},
		'./node_modules/core-js/library/modules/es7.promise.try.js': function (e, s, o) {
			'use strict';
			var t = o('./node_modules/core-js/library/modules/_export.js'),
				r = o('./node_modules/core-js/library/modules/_new-promise-capability.js'),
				n = o('./node_modules/core-js/library/modules/_perform.js');
			t(t.S, 'Promise', {
				try: function (e) {
					var s = r.f(this),
						o = n(e);
					return (o.e ? s.reject : s.resolve)(o.v), s.promise;
				}
			});
		},
		'./node_modules/core-js/library/modules/web.dom.iterable.js': function (e, s, o) {
			o('./node_modules/core-js/library/modules/es6.array.iterator.js');
			for (
				var t = o('./node_modules/core-js/library/modules/_global.js'),
					r = o('./node_modules/core-js/library/modules/_hide.js'),
					n = o('./node_modules/core-js/library/modules/_iterators.js'),
					i = o('./node_modules/core-js/library/modules/_wks.js')('toStringTag'),
					a =
						'CSSRuleList,CSSStyleDeclaration,CSSValueList,ClientRectList,DOMRectList,DOMStringList,DOMTokenList,DataTransferItemList,FileList,HTMLAllCollection,HTMLCollection,HTMLFormElement,HTMLSelectElement,MediaList,MimeTypeArray,NamedNodeMap,NodeList,PaintRequestList,Plugin,PluginArray,SVGLengthList,SVGNumberList,SVGPathSegList,SVGPointList,SVGStringList,SVGTransformList,SourceBufferList,StyleSheetList,TextTrackCueList,TextTrackList,TouchList'.split(
							','
						),
					l = 0;
				l < a.length;
				l++
			) {
				var c = a[l],
					d = t[c],
					u = d && d.prototype;
				u && !u[i] && r(u, i, c), (n[c] = n.Array);
			}
		}
	});
});
//# sourceMappingURL=engine.min.js.map
