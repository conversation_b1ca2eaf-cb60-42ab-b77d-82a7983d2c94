export default class RoomErrorCode {
	static JANUS_VIDEOROOM_ERROR_UNKNOWN_ERROR = 499;
	static JANUS_VIDEOROOM_ERROR_NO_MESSAGE = 421;
	static JANUS_VIDEOROOM_ERROR_INVALID_JSON = 422;
	static JANUS_VIDEOROOM_ERROR_INVALID_REQUEST = 423;
	static JANUS_VIDEOROOM_ERROR_JOIN_FIRST = 424;
	static JANUS_VIDEOROOM_ERROR_ALREADY_JOINED = 425;
	static JANUS_VIDEOROOM_ERROR_NO_SUCH_ROOM = 426;
	static JANUS_VIDEOROOM_ERROR_ROOM_EXISTS = 427;
	static JANUS_VIDEOROOM_ERROR_NO_SUCH_FEED = 428;
	static JANUS_VIDEOROOM_ERROR_MISSING_ELEMENT = 429;
	static JANUS_VIDEOROOM_ERROR_INVALID_ELEMENT = 430;
	static JANUS_VIDEOROOM_ERROR_INVALID_SDP_TYPE = 431;
	static JANUS_VIDEOROOM_ERROR_PUBLISHERS_FULL = 432;
	static JANUS_VIDEOROOM_ERROR_UNAUTHORIZED = 433;
	static JANUS_VIDEOROOM_ERROR_ALREADY_PUBLISHED = 434;
	static JANUS_VIDEOROOM_ERROR_NOT_PUBLISHED = 435;
	static JANUS_VIDEOROOM_ERROR_ID_EXISTS = 436;
	static JANUS_VIDEOROOM_ERROR_INVALID_SDP = 437;
}
