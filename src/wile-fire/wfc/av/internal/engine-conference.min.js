!(function (e, s) {
	if ('object' == typeof exports && 'object' == typeof module)
		module.exports = s(
			require('../../../config.js'),
			require('../../../platform.js'),
			require('../../messages/messageContentType'),
			require('../../model/conversation'),
			require('../../model/conversationType'),
			require('../../model/nullUserInfo'),
			require('../../util/longUtil.js'),
			require('../engine/avenginekit'),
			require('../engine/avenginekitproxy'),
			require('../engine/callEndReason'),
			require('../engine/callSession'),
			require('../engine/callState'),
			require('../engine/participantProfile'),
			require('../engine/subscriber'),
			require('../engine/videoProfile'),
			require('../engine/videoType'),
			require('../messages/addParticipantsMessageContent'),
			require('../messages/callAnswerMessageContent'),
			require('../messages/callByeMessageContent'),
			require('../messages/callModifyMessageContent'),
			require('../messages/callStartMessageContent'),
			require('../messages/conferenceChangeModeContent'),
			require('../messages/muteVideoMessageContent')
		);
	else if ('function' == typeof define && define.amd)
		define([
			'../../../config.js',
			'../../../platform.js',
			'../../messages/messageContentType',
			'../../model/conversation',
			'../../model/conversationType',
			'../../model/nullUserInfo',
			'../../util/longUtil.js',
			'../engine/avenginekit',
			'../engine/avenginekitproxy',
			'../engine/callEndReason',
			'../engine/callSession',
			'../engine/callState',
			'../engine/participantProfile',
			'../engine/subscriber',
			'../engine/videoProfile',
			'../engine/videoType',
			'../messages/addParticipantsMessageContent',
			'../messages/callAnswerMessageContent',
			'../messages/callByeMessageContent',
			'../messages/callModifyMessageContent',
			'../messages/callStartMessageContent',
			'../messages/conferenceChangeModeContent',
			'../messages/muteVideoMessageContent'
		], s);
	else {
		var o =
			'object' == typeof exports
				? s(
						require('../../../config.js'),
						require('../../../platform.js'),
						require('../../messages/messageContentType'),
						require('../../model/conversation'),
						require('../../model/conversationType'),
						require('../../model/nullUserInfo'),
						require('../../util/longUtil.js'),
						require('../engine/avenginekit'),
						require('../engine/avenginekitproxy'),
						require('../engine/callEndReason'),
						require('../engine/callSession'),
						require('../engine/callState'),
						require('../engine/participantProfile'),
						require('../engine/subscriber'),
						require('../engine/videoProfile'),
						require('../engine/videoType'),
						require('../messages/addParticipantsMessageContent'),
						require('../messages/callAnswerMessageContent'),
						require('../messages/callByeMessageContent'),
						require('../messages/callModifyMessageContent'),
						require('../messages/callStartMessageContent'),
						require('../messages/conferenceChangeModeContent'),
						require('../messages/muteVideoMessageContent')
				  )
				: s(
						e['../../../config.js'],
						e['../../../platform.js'],
						e['../../messages/messageContentType'],
						e['../../model/conversation'],
						e['../../model/conversationType'],
						e['../../model/nullUserInfo'],
						e['../../util/longUtil.js'],
						e['../engine/avenginekit'],
						e['../engine/avenginekitproxy'],
						e['../engine/callEndReason'],
						e['../engine/callSession'],
						e['../engine/callState'],
						e['../engine/participantProfile'],
						e['../engine/subscriber'],
						e['../engine/videoProfile'],
						e['../engine/videoType'],
						e['../messages/addParticipantsMessageContent'],
						e['../messages/callAnswerMessageContent'],
						e['../messages/callByeMessageContent'],
						e['../messages/callModifyMessageContent'],
						e['../messages/callStartMessageContent'],
						e['../messages/conferenceChangeModeContent'],
						e['../messages/muteVideoMessageContent']
				  );
		for (var t in o) ('object' == typeof exports ? exports : e)[t] = o[t];
	}
})(window, function (e, s, o, t, r, i, n, a, l, c, d, u, m, h, f, _, p, S, b, g, j, v, y) {
	return (function (e) {
		var s = {};
		function o(t) {
			if (s[t]) return s[t].exports;
			var r = (s[t] = { i: t, l: !1, exports: {} });
			return e[t].call(r.exports, r, r.exports, o), (r.l = !0), r.exports;
		}
		return (
			(o.m = e),
			(o.c = s),
			(o.d = function (e, s, t) {
				o.o(e, s) || Object.defineProperty(e, s, { enumerable: !0, get: t });
			}),
			(o.r = function (e) {
				'undefined' != typeof Symbol &&
					Symbol.toStringTag &&
					Object.defineProperty(e, Symbol.toStringTag, { value: 'Module' }),
					Object.defineProperty(e, '__esModule', { value: !0 });
			}),
			(o.t = function (e, s) {
				if ((1 & s && (e = o(e)), 8 & s)) return e;
				if (4 & s && 'object' == typeof e && e && e.__esModule) return e;
				var t = Object.create(null);
				if (
					(o.r(t),
					Object.defineProperty(t, 'default', { enumerable: !0, value: e }),
					2 & s && 'string' != typeof e)
				)
					for (var r in e)
						o.d(
							t,
							r,
							function (s) {
								return e[s];
							}.bind(null, r)
						);
				return t;
			}),
			(o.n = function (e) {
				var s =
					e && e.__esModule
						? function () {
								return e.default;
						  }
						: function () {
								return e;
						  };
				return o.d(s, 'a', s), s;
			}),
			(o.o = function (e, s) {
				return Object.prototype.hasOwnProperty.call(e, s);
			}),
			(o.p = ''),
			o((o.s = './av/internal/avenginekitImpl.js'))
		);
	})({
		'../../../config.js': function (s, o) {
			s.exports = e;
		},
		'../../../platform.js': function (e, o) {
			e.exports = s;
		},
		'../../messages/messageContentType': function (e, s) {
			e.exports = o;
		},
		'../../model/conversation': function (e, s) {
			e.exports = t;
		},
		'../../model/conversationType': function (e, s) {
			e.exports = r;
		},
		'../../model/nullUserInfo': function (e, s) {
			e.exports = i;
		},
		'../../util/longUtil.js': function (e, s) {
			e.exports = n;
		},
		'../engine/avenginekit': function (e, s) {
			e.exports = a;
		},
		'../engine/avenginekitproxy': function (e, s) {
			e.exports = l;
		},
		'../engine/callEndReason': function (e, s) {
			e.exports = c;
		},
		'../engine/callSession': function (e, s) {
			e.exports = d;
		},
		'../engine/callState': function (e, s) {
			e.exports = u;
		},
		'../engine/participantProfile': function (e, s) {
			e.exports = m;
		},
		'../engine/subscriber': function (e, s) {
			e.exports = h;
		},
		'../engine/videoProfile': function (e, s) {
			e.exports = f;
		},
		'../engine/videoType': function (e, s) {
			e.exports = _;
		},
		'../messages/addParticipantsMessageContent': function (e, s) {
			e.exports = p;
		},
		'../messages/callAnswerMessageContent': function (e, s) {
			e.exports = S;
		},
		'../messages/callByeMessageContent': function (e, s) {
			e.exports = b;
		},
		'../messages/callModifyMessageContent': function (e, s) {
			e.exports = g;
		},
		'../messages/callStartMessageContent': function (e, s) {
			e.exports = j;
		},
		'../messages/conferenceChangeModeContent': function (e, s) {
			e.exports = v;
		},
		'../messages/muteVideoMessageContent': function (e, s) {
			e.exports = y;
		},
		'./av/internal/avenginekitImpl.js': function (e, s, o) {
			'use strict';
			o.r(s),
				o.d(s, 'WfcAVEngineKitImpl', function () {
					return k;
				});
			var t = o('./node_modules/babel-runtime/core-js/map.js'),
				r = o.n(t),
				i = o('../../messages/messageContentType'),
				n = o.n(i),
				a = o('../messages/callByeMessageContent'),
				l = o.n(a),
				c = o('../messages/callAnswerMessageContent'),
				d = o.n(c),
				u = o('../messages/callStartMessageContent'),
				m = o.n(u),
				h = o('../messages/callModifyMessageContent'),
				f = o.n(h),
				_ = o('../../model/conversationType'),
				p = o.n(_),
				S = o('../engine/callEndReason'),
				b = o.n(S),
				g = o('../engine/avenginekitproxy'),
				j = o.n(g),
				v = o('../engine/callState'),
				y = o.n(v),
				C = o('./av/internal/callSessionImpl.js'),
				T = o('../messages/addParticipantsMessageContent'),
				I = o.n(T),
				M = o('../engine/avenginekit'),
				E = o.n(M),
				O = o('../../../config.js'),
				R = o.n(O);
			class k extends E.a {
				constructor() {
					super(),
						(this.sendMessageId = 0),
						(this.sendConferenceRequestId = 0),
						(this._fixLongSerializedIssue = e => (
							'string' != typeof e ||
								('string' == typeof (e = JSON.parse(e)) && (e = JSON.parse(e))),
							e
						)),
						(this.onSendMessageResult = (e, s) => {
							s = this._fixLongSerializedIssue(s);
							let o = this.sendMessageCallbackMap.get(s.sendMessageId);
							o && o(s.error, s.messageUid, s.timestamp),
								this.sendMessageCallbackMap.delete(s.sendMessageId);
						}),
						(this.onSendConferenceRequestResult = (e, s) => {
							console.log('av-c', 'sendConferenceRequestResult', e, s),
								(s = this._fixLongSerializedIssue(s));
							let o = this.sendConferenceRequestCallbackMap.get(s.sendConferenceRequestId);
							o &&
								(o(s.error, s.response),
								this.sendConferenceRequestCallbackMap.delete(s.sendConferenceRequestId));
						}),
						(this.onConferenceEvent = (e, s) => {
							(s = this._fixLongSerializedIssue(s)),
								this.currentSession && this.currentSession.onConferenceEvent(s);
						}),
						(this.onReceiveMessage = (e, s) => {
							if (
								((s = this._fixLongSerializedIssue(s)),
								console.log('av-c', 'receive voip message ', s),
								!(
									(s.conversation.type !== p.a.Single && s.conversation.type !== p.a.Group) ||
									(1 !== s.direction &&
										s.messageContent.type !== n.a.VOIP_CONTENT_TYPE_ACCEPT &&
										s.messageContent.type !== n.a.VOIP_CONTENT_TYPE_END)
								))
							) {
								let e = s.messageContent;
								if (s.messageContent.type === n.a.VOIP_CONTENT_TYPE_SIGNAL) {
									if (!A.currentSession || A.currentSession.status === y.a.STATUS_IDLE) return;
									let o = s.messageContent;
									o.callId !== A.currentSession.callId
										? A.rejectOtherCall(s.conversation, e.callId, null, s.messageUid)
										: !A.currentSession ||
										  (A.currentSession.status !== y.a.STATUS_CONNECTING &&
												A.currentSession.status !== y.a.STATUS_CONNECTED &&
												A.currentSession.status !== y.a.STATUS_OUTGOING) ||
										  A.onReceiveData(s.from, o.payload);
								} else if (s.messageContent.type === n.a.VOIP_CONTENT_TYPE_START) {
									console.log('av-c', 'callstart', s);
									let o = s.messageContent.targetIds;
									if (o.findIndex(e => e === s.selfUserInfo.uid) < 0) return;
									(o = o.filter(e => e !== s.selfUserInfo.uid)),
										o.push(s.from),
										A.currentSession && A.currentSession.status !== y.a.STATUS_IDLE
											? A.rejectOtherCall(s.conversation, e.callId, o, s.messageUid)
											: ((A.currentSession = C.default.newSession(
													s.conversation,
													s.from,
													e.callId,
													e.audioOnly,
													A.sessionCallback
											  )),
											  (A.currentSession.startMsgUid = s.messageUid),
											  (A.currentSession.pin = e.pin),
											  (A.currentSession.joinTime = s.timestamp),
											  (A.currentSession.callExtra = e.extra),
											  A.currentSession.initSession(
													!1,
													s.selfUserInfo,
													s.participantUserInfos,
													s.groupMemberUserInfos
											  ),
											  A.currentSession.setUserJoinTime(s.from, s.timestamp),
											  s.participantUserInfos.forEach(e => {
													A.currentSession.setUserJoinTime(e.uid, s.timestamp);
											  }),
											  A.currentSession.setUserAcceptTime(s.from, s.timestamp),
											  A.currentSession.setState(y.a.STATUS_INCOMING));
								} else if (
									s.messageContent.type === n.a.VOIP_CONTENT_TYPE_ACCEPT ||
									s.messageContent.type === n.a.VOIP_CONTENT_TYPE_ACCEPT_T
								) {
									if (A.currentSession && A.currentSession.status !== y.a.STATUS_IDLE) {
										if (e.callId !== A.currentSession.callId)
											return void (
												1 === s.direction &&
												A.rejectOtherCall(s.conversation, e.callId, [s.selfUserInfo.uid])
											);
										if (0 === s.direction && A.currentSession.status === y.a.STATUS_INCOMING)
											return void A.currentSession.endCall(b.a.REASON_AcceptByOtherClient);
										A.currentSession.status === y.a.STATUS_OUTGOING &&
											A.currentSession.setState(y.a.STATUS_CONNECTING),
											!A.currentSession.audioOnly &&
												e.audioOnly &&
												A.currentSession.setAudioOnly(!0),
											A.currentSession.setUserAcceptTime(s.from, s.timestamp);
										let o = A.queueOfferMessageMap.get(s.from);
										o &&
											o.forEach(e => {
												A.processSignalingMessage(s.from, e);
											}),
											A.queueOfferMessageMap.delete(s.from);
									}
								} else if (s.messageContent.type === n.a.VOIP_CONTENT_TYPE_END)
									if (
										A.currentSession &&
										A.currentSession.status !== y.a.STATUS_IDLE &&
										A.currentSession.callId === e.callId
									) {
										let o = e.reason;
										if (1 === s.direction) {
											switch (e.reason) {
												case b.a.REASON_Unknown:
													o = b.a.REASON_Unknown;
													break;
												case b.a.REASON_Busy:
													o = b.a.RemoteBusy;
													break;
												case b.a.REASON_SignalError:
													o = b.a.RemoteNetworkError;
													break;
												case b.a.REASON_Hangup:
													o = b.a.REASON_RemoteHangup;
													break;
												case b.a.REASON_MediaError:
													o = b.a.RemoteNetworkError;
													break;
												case b.a.REASON_RemoteHangup:
													o = b.a.REASON_Hangup;
													break;
												case b.a.REASON_OpenCameraFailure:
													o = b.a.RemoteNetworkError;
													break;
												case b.a.REASON_Timeout:
													o = b.a.RemoteTimeout;
													break;
												case b.a.REASON_AcceptByOtherClient:
													o = b.a.REASON_AcceptByOtherClient;
													break;
												case b.a.REASON_AllLeft:
													o = b.a.REASON_AllLeft;
													break;
												case b.a.RemoteBusy:
													o = b.a.REASON_Busy;
													break;
												case b.a.RemoteTimeout:
													o = b.a.REASON_Timeout;
													break;
												case b.a.RemoteNetworkError:
													o = b.a.REASON_SignalError;
													break;
												case b.a.RoomDestroyed:
													o = b.a.RoomDestroyed;
													break;
												case b.a.RoomNotExist:
													o = b.a.RoomNotExist;
													break;
												case b.a.RoomParticipantsFull:
													o = b.a.RoomParticipantsFull;
													break;
												case b.a.Interrupted:
													o = b.a.RemoteInterrupted;
													break;
												default:
													o = b.a.RemoteNetworkError;
											}
											A.currentSession.endUserCall(s.from, o);
										} else A.currentSession.endCall(o);
									} else console.log('av-c', 'invalid bye message, ignore it', A.currentSession, e);
								else if (s.messageContent.type === n.a.VOIP_CONTENT_TYPE_MODIFY)
									A.currentSession &&
										A.currentSession.status === y.a.STATUS_CONNECTED &&
										A.currentSession.callId === e.callId &&
										(e.audioOnly
											? ((A.currentSession.audioOnly = !0), A.currentSession.downgrade2Voice(!0))
											: console.log('av-c', 'cannot modify voice call to video call'));
								else if (s.messageContent.type === n.a.VOIP_Join_Call_Request)
									A.currentSession &&
										A.currentSession.status === y.a.STATUS_CONNECTED &&
										s.messageContent.callId === A.currentSession.callId &&
										A.currentSession.initiatorId === A.currentSession.selfUserInfo.uid &&
										A.currentSession.inviteNewParticipants([s.from], s.messageContent.clientId, !0);
								else if (s.messageContent.type === n.a.VOIP_CONTENT_TYPE_ADD_PARTICIPANT)
									if (e.participants.indexOf(s.selfUserInfo.uid) > -1) {
										if (A.currentSession && A.currentSession.status !== y.a.STATUS_IDLE) {
											if (A.currentSession.callId === s.messageContent.callId) return;
											let o = [];
											return (
												o.push(...s.messageContent.participants),
												s.messageContent.existParticipants &&
													o.push(...s.messageContent.existParticipants),
												o.push(s.from),
												(o = o.filter(e => e !== s.selfUserInfo.uid)),
												void A.rejectOtherCall(s.conversation, e.callId, o)
											);
										}
										(A.currentSession = C.default.newSession(
											s.conversation,
											s.from,
											e.callId,
											e.audioOnly,
											A.sessionCallback
										)),
											(A.currentSession.pin = e.pin),
											(A.currentSession.startMsgUid = s.messageUid);
										let o = s.participantUserInfos.filter(e => e.uid !== s.selfUserInfo.uid);
										(A.currentSession.joinTime = s.timestamp),
											(A.currentSession.callExtra = s.messageContent.extra),
											A.currentSession.initSession(!1, s.selfUserInfo, o, s.groupMemberUserInfos),
											o.forEach(e => {
												A.currentSession.setUserJoinTime(e.uid, s.timestamp);
											}),
											A.currentSession.updateExistParticipant(e.existParticipants, s.timestamp),
											A.currentSession.setState(y.a.STATUS_INCOMING),
											R.a.ENABLE_MULTI_CALL_AUTO_JOIN &&
												e.autoAnswer &&
												A.currentSession.answer(e.audioOnly);
									} else if (
										A.currentSession &&
										A.currentSession.status !== y.a.STATUS_IDLE &&
										A.currentSession.callId === e.callId
									) {
										let o = s.participantUserInfos.filter(s => e.participants.indexOf(s.uid) > -1);
										A.currentSession.didAddNewParticipants(e.participants, o),
											o.forEach(e => {
												A.currentSession.setUserJoinTime(e.uid, s.timestamp);
											});
									} else {
										let o = [];
										o.push(...s.messageContent.participants),
											s.messageContent.existParticipants &&
												o.push(...s.messageContent.existParticipants),
											o.push(s.from),
											(o = o.filter(e => e !== s.selfUserInfo.uid)),
											A.rejectOtherCall(s.conversation, e.callId, o);
									}
								else if (s.messageContent.type === n.a.VOIP_CONTENT_TYPE_MUTE_VIDEO) {
									let e = s.messageContent;
									A.currentSession &&
										A.currentSession.callId === e.callId &&
										A.currentSession.status !== y.a.STATUS_IDLE &&
										A.currentSession.updateVideoMute(s.from, e.videoMuted);
								} else if (s.messageContent.type === n.a.CONFERENCE_CONTENT_TYPE_CHANGE_MODE) {
									let e = s.messageContent;
									A.currentSession &&
										A.currentSession.callId === e.callId &&
										A.currentSession.onRequestChangeMode(e.audience);
								} else if (s.messageContent.type === n.a.CONFERENCE_CONTENT_TYPE_KICKOFF_MEMBER) {
									let e = s.messageContent;
									A.currentSession &&
										A.currentSession.callId === e.callId &&
										A.currentSession.onKickoff();
								}
							}
						}),
						(this.startCall = (e, s) => {
							let o = (s = this._fixLongSerializedIssue(s)).conversation,
								t = s.audioOnly;
							if (this.currentSession) return;
							(this.currentSession = C.default.newSession(
								o,
								s.selfUserInfo.uid,
								s.callId,
								t,
								A.sessionCallback
							)),
								(this.currentSession.pin = '');
							for (let e = 0; e < 6; e++) this.currentSession.pin += Math.floor(10 * Math.random());
							(this.currentSession.callExtra = s.callExtra),
								this.currentSession.initSession(
									!0,
									s.selfUserInfo,
									s.participantUserInfos,
									s.groupMemberUserInfos
								),
								this.currentSession.setState(y.a.STATUS_OUTGOING);
							let r = new m.a();
							(r.audioOnly = t),
								(r.callId = s.callId),
								(r.pin = this.currentSession.pin),
								(r.targetIds = this.currentSession.getParticipantIds()),
								(r.extra = s.callExtra),
								(r.sdkType = 2),
								this.currentSession.createRoom(e => {
									0 === e
										? this.sendSignalMessage(
												r,
												this.currentSession.getParticipantIds(),
												!0,
												!1,
												(e, s, o) => {
													A.currentSession &&
														(0 !== e
															? this.currentSession.endCall(b.a.REASON_SignalError)
															: ((this.currentSession.startMsgUid = s),
															  console.log(
																	'av-c',
																	'startMsgUid',
																	this.currentSession.startMsgUid,
																	s
															  ),
															  (this.currentSession.joinTime = o),
															  this.currentSession.setAcceptTime(o),
															  r.targetIds.forEach(e => {
																	this.currentSession.setUserJoinTime(e, o);
															  }, this)));
												}
										  )
										: this.currentSession.endCall(b.a.REASON_SignalError);
								});
						}),
						(this.startConference = (e, s) => {
							let o = (s = this._fixLongSerializedIssue(s)).audioOnly;
							this.currentSession ||
								((this.currentSession = C.default.newSession(
									null,
									s.selfUserInfo.uid,
									s.callId,
									o,
									A.sessionCallback
								)),
								(this.currentSession.pin = s.pin),
								(this.currentSession.host = s.host),
								(this.currentSession.title = s.title),
								(this.currentSession.desc = s.desc),
								(this.currentSession.conference = !0),
								(this.currentSession.defaultAudience = s.audience),
								(this.currentSession.advance = s.advance),
								(this.currentSession.record = s.record),
								(this.currentSession.extra = s.extra),
								(this.currentSession.callExtra = s.callExtra),
								(this.currentSession.videoMuted = s.muteVideo),
								(this.currentSession.audioMuted = s.muteAudio),
								this.currentSession.initSession(!0, s.selfUserInfo, null, null, !0),
								this.currentSession.setState(y.a.STATUS_OUTGOING),
								this.currentSession.createRoom(e => {
									0 !== e && this.currentSession.endCall(b.a.REASON_SignalError);
								}));
						}),
						(this._joinConference = (e, s) => {
							this.joinConference(s);
						}),
						(this.joinConference = e => {
							(e = this._fixLongSerializedIssue(e)),
								this.currentSession ||
									(console.log('av-c', 'joinConference', e),
									(this.currentSession = C.default.newSession(
										null,
										e.selfUserInfo.uid,
										e.callId,
										e.audioOnly,
										A.sessionCallback
									)),
									(this.currentSession.pin = e.pin),
									(this.currentSession.host = e.host),
									(this.currentSession.title = e.title),
									(this.currentSession.desc = e.desc),
									(this.currentSession.defaultAudience = e.audience),
									(this.currentSession.audience = e.audience),
									(this.currentSession.conference = !0),
									(this.currentSession.advance = e.advance),
									(this.currentSession.audioMuted = e.muteAudio),
									(this.currentSession.videoMuted = e.muteVideo),
									(this.currentSession.extra = e.extra),
									(this.currentSession.callExtra = e.callExtra),
									this.currentSession.initSession(!1, e.selfUserInfo, null, null, !0),
									this.currentSession.setState(y.a.STATUS_CONNECTING));
						}),
						(this.onConnectionStatusChange = (e, s) => {
							A.currentSession && A.currentSession.onIMConnectionStatusChange(Number(s));
						});
				}
				setup(e) {
					window &&
						!window._log &&
						((window._log = console.log),
						(console.log = (e, ...s) => {
							window._log(new Date().toLocaleString(), e, ...s);
						})),
						this.sendMessageCallbackMap &&
							console.log('av-c', 'wfc avengine-conference, setup multi times'),
						console.log('av-c', 'wfc avengine-conference'),
						j.a.listenVoipEvent('message', this.onReceiveMessage),
						j.a.listenVoipEvent('conferenceEvent', this.onConferenceEvent),
						j.a.listenVoipEvent('sendMessageResult', this.onSendMessageResult),
						j.a.listenVoipEvent('sendConferenceRequestResult', this.onSendConferenceRequestResult),
						j.a.listenVoipEvent('startCall', this.startCall),
						j.a.listenVoipEvent('startConference', this.startConference),
						j.a.listenVoipEvent('joinConference', this._joinConference),
						j.a.listenVoipEvent('connectionStatus', this.onConnectionStatusChange),
						(this.sendMessageCallbackMap = new r.a()),
						(this.sendConferenceRequestCallbackMap = new r.a()),
						(this.queueOfferMessageMap = new r.a()),
						(this.sessionCallback = e);
				}
				getCurrentSession() {
					return A.currentSession;
				}
				inviteNewParticipants(e, s, o, t) {
					let r = A.currentSession;
					if (!r) return;
					let i = new I.a();
					(i.callId = r.callId),
						(i.initiator = r.selfUserInfo.uid),
						console.log('av-c', 'inviteNewParticipants', 'initiator ' + r.selfUserInfo),
						(i.audioOnly = r.audioOnly),
						(i.pin = s),
						(i.participants = e),
						(i.existParticipants = r.getExistParticipantsStatus()),
						(i.autoAnswer = t),
						(i.clientId = o);
					let n = [...r.getParticipantIds()];
					n.push(...e),
						this.sendSignalMessage(i, n, !0, !1, (s, o, t) => {
							if ((console.log('av-c', 'send invite message success'), 0 !== s))
								return void console.log('av-c', 'send invite message error', s);
							let i = r.groupMemberUserInfos.filter(s => e.findIndex(e => e === s.uid) > -1);
							console.log('av-c', 'didAddNewParticipants av', e, i),
								r.didAddNewParticipants(e, i),
								e.forEach(e => {
									r.setUserJoinTime(e, t);
								});
						});
				}
				updateCallStartMessageContent(e, s) {
					let o = { messageUid: e, content: s };
					j.a.emitToMain('update-call-start-message', o);
				}
				clearInviteUnreadStatus(e) {}
				sendSignalMessage(e, s, o, t, r) {
					A.currentSession.conversation &&
						this.sendSignalMessageEx(A.currentSession.conversation, e, s, o, t, r);
				}
				sendSignalMessageEx(e, s, o, t, r, i) {
					o && r && o.push(this.currentSession.selfUserInfo.uid);
					let n = { conversation: e, content: s.encode(), toUsers: o };
					console.log('av-c', 'send signal message', s),
						i &&
							(this.sendMessageId++,
							(n.sendMessageId = this.sendMessageId),
							this.sendMessageCallbackMap.set(this.sendMessageId, i)),
						j.a.emitToMain('voip-message', n);
				}
				sendConferenceRequest(e, s, o, t, r) {
					this.sendConferenceRequestEx(e, s, o, t, this.currentSession.advance, r);
				}
				sendConferenceRequestEx(e, s, o, t, r, i) {
					let n = { sessionId: e, roomId: s, request: o, data: t, advance: r };
					i &&
						(this.sendConferenceRequestId++,
						(n.sendConferenceRequestId = this.sendConferenceRequestId),
						this.sendConferenceRequestCallbackMap.set(this.sendConferenceRequestId, i)),
						console.log('av-c', 'sendConferenceRequest', n),
						j.a.emitToMain('conference-request', n);
				}
				rejectOtherCall(e, s, o, t) {
					let r = new l.a();
					(r.callId = s),
						(r.inviteMsgUid = t),
						(r.reason = b.a.REASON_Busy),
						console.log('av-c', 'reject other call', r, o),
						this.sendSignalMessageEx(e, r, o, !1, !0);
				}
				onReceiveData(e, s) {
					let o = JSON.parse(s);
					if (A.currentSession.getPeerConnection(e)) A.processSignalingMessage(e, o);
					else {
						console.log('av-c', 'queue signal', e, o);
						let s = this.queueOfferMessageMap.get(e);
						s || ((s = []), this.queueOfferMessageMap.set(e, s)), s.push(o);
					}
				}
				processSignalingMessage(e, s) {
					console.log('av-c', 'process remote signal:' + s);
				}
				answerCurrentCall() {
					let e = new d.a();
					(e.audioOnly = A.currentSession.audioOnly),
						(e.callId = A.currentSession.callId),
						(e.inviteMessageUid = A.currentSession.startMsgUid),
						this.sendSignalMessage(
							e,
							this.currentSession.getParticipantIds(),
							!0,
							!0,
							(e, s, o) => {
								0 === e
									? this.currentSession.setAcceptTime(o)
									: this.currentSession.endCall(b.a.REASON_SignalError);
							}
						);
				}
				downgrade2VoiceCall() {
					let e = new f.a();
					(A.currentSession.audioOnly = !0),
						(e.audioOnly = A.currentSession.audioOnly),
						(e.callId = A.currentSession.callId),
						this.sendSignalMessage(e, this.currentSession.getParticipantIds(), !0, !1);
				}
			}
			const A = new k();
			s.default = A;
		},
		'./av/internal/callSessionImpl.js': function (e, s, o) {
			'use strict';
			o.r(s),
				o.d(s, 'default', function () {
					return K;
				});
			var t = o('./node_modules/babel-runtime/core-js/object/keys.js'),
				r = o.n(t),
				i = o('./node_modules/babel-runtime/helpers/extends.js'),
				n = o.n(i),
				a = o('./node_modules/babel-runtime/helpers/asyncToGenerator.js'),
				l = o.n(a),
				c = o('./node_modules/babel-runtime/core-js/map.js'),
				d = o.n(c),
				u = o('./node_modules/babel-runtime/core-js/json/stringify.js'),
				m = o.n(u),
				h = o('../../../config.js'),
				f = o.n(h),
				_ = o('../engine/callState'),
				p = o.n(_),
				S = o('./av/internal/avenginekitImpl.js'),
				b = o('../engine/callEndReason'),
				g = o.n(b),
				j = o('../messages/callByeMessageContent'),
				v = o.n(j),
				y = o('./av/internal/subscriberImpl.js'),
				C = o('../../../platform.js'),
				T = o('../engine/callSession'),
				I = o.n(T),
				M = o('../../util/longUtil.js'),
				E = o('../engine/avenginekit'),
				O = o.n(E),
				R = o('../../model/conversationType'),
				k = o.n(R),
				A = o('../messages/conferenceChangeModeContent'),
				P = o.n(A),
				V = o('../../model/conversation'),
				x = o.n(V),
				w = o('./av/internal/roomErrorCode.js'),
				N = o('./av/internal/soundMeter.js'),
				U = o('../engine/avenginekitproxy'),
				D = o.n(U),
				L = o('../messages/muteVideoMessageContent'),
				q = o.n(L),
				B = o('../engine/videoProfile'),
				J = o.n(B),
				G = o('./av/internal/publisherImpl.js'),
				F = o('./av/internal/screenSharingPublisherImpl.js'),
				H = o('../../model/nullUserInfo'),
				W = o.n(H),
				Y = o('../engine/participantProfile'),
				z = o.n(Y);
			class K extends I.a {
				constructor(...e) {
					var s;
					return (
						(s = super(...e)),
						(this.audience = !1),
						(this.lastActiveTime = 0),
						(this.joinTime = 0),
						(this.acceptTime = 0),
						(this.status = 0),
						(this.isAllBusy = !0),
						(this.videoCamera = 'user'),
						(this.rotateAng = 0),
						(this._isMuting = !1),
						(this._isSwitchAudience = !1),
						(this.onVoipWindowClose = e => {
							this.endCall(g.a.RemoteNetworkError),
								window.removeEventListener('beforeunload', this.onVoipWindowClose);
						}),
						(this.callTimeout = () => {
							let e = new Date().valueOf();
							this.callTimer || (this.callTimer = setInterval(this.callTimeout, 1e3));
							let s = Object(M.numberValue)(this.joinTime);
							if (0 !== s) {
								if (this.status === p.a.STATUS_INCOMING) {
									if (e - s > 6e4) return void this.endCall(g.a.REASON_Timeout);
								} else if (this.status !== p.a.STATUS_CONNECTED && e - s > 6e4)
									return void this.endCall(g.a.RemoteTimeout);
								this.subscriberMap.forEach((s, o) => {
									let t = Object(M.gt)(s.acceptTime, s.joinTime) ? s.acceptTime : s.joinTime;
									s.status !== p.a.STATUS_CONNECTED &&
										Object(M.gt)(t, 0) &&
										e - Object(M.numberValue)(t) > 6e4 &&
										(console.log(
											'av-c',
											'subscriber timeout',
											e,
											Object(M.numberValue)(s.acceptTime),
											Object(M.numberValue)(s.joinTime)
										),
										this.endUserCall(o, g.a.RemoteTimeout));
								});
							}
						}),
						(this.gotRemoteStream = (e, s) => {
							if (this.sessionCallback) {
								let o = this.getScreenSharingUserId(e);
								this.sessionCallback.didReceiveRemoteVideoTrack(o || e, s.streams[0], null !== o);
							}
							let o = s.streams[0];
							o.onremovetrack = s => {
								console.log('av-c', 'pc remove remote stream', s),
									this.sessionCallback && this.sessionCallback.didRemoveRemoteVideoTrack(e);
							};
							let t = this.getSubscriber(e);
							if (o.getAudioTracks().length > 0) {
								let e = new N.SoundMeter(window.audioContext);
								e.connectToSource(o, e => {
									console.log('av-c', 'connect to soundMeter', e);
								}),
									(t.soundMeter = e);
							}
							(t.stream = s.streams[0]),
								console.log(
									'av-c',
									'pc received remote stream',
									e,
									s.streams[0],
									s.streams[0].getVideoTracks().length,
									s.streams[0].getAudioTracks().length
								);
						}),
						(this.onIceCandidate = (e, s, o) => {
							if (!o.candidate) return;
							if (O.a.FORCE_MEDIA_OVER_TCP) return;
							let t = {};
							if (((t.session_id = this.sessionId), e === this.selfUserInfo.uid))
								t.handle_id = this.handleId;
							else {
								let s = this.getSubscriber(e);
								t.handle_id = s.handleId;
							}
							let r = {};
							(r.candidate = o.candidate.candidate),
								(r.sdpMLineIndex = o.candidate.sdpMLineIndex),
								(r.sdpMid = o.candidate.sdpMid),
								(r.pin = this.pin),
								(t.candidate = r),
								S.default.sendConferenceRequest(
									this.sessionId,
									this.callId,
									'trickle',
									m()(t),
									(e, s) => {
										0 !== e && console.log('av-c', 'conference request, trickle error', e);
									}
								);
						}),
						(this.onIceStateChange = (e, s, o) => {
							if (s) {
								if (
									(console.log('av-c', 'ICE state change event: ', e, s.iceConnectionState, o),
									'connected' === s.iceConnectionState)
								) {
									if (this.selfUserInfo.uid === e)
										return (
											this.connectedTime || (this.connectedTime = new Date().getTime()),
											void this.setState(p.a.STATUS_CONNECTED)
										);
									{
										let s = this.getSubscriber(e);
										if (
											(this.audience && this.setState(p.a.STATUS_CONNECTED),
											(s.status = p.a.STATUS_CONNECTED),
											this.sessionCallback)
										) {
											let s = this.getScreenSharingUserId(e);
											this.sessionCallback.didParticipantConnected(s || e, null !== s);
										}
									}
								}
								'disconnected' === s.iceConnectionState ||
									('failed' === s.iceConnectionState &&
										console.log('av-c', 'iceConnectionState failed, endCall'));
							}
						}),
						(this.bytesPrev = 0),
						(this.timestampPrev = 0),
						s
					);
				}
				static newSession(e, s, o, t, r) {
					console.log('av-c', 'newSession');
					let i = new K();
					return (
						(i.conversation = e),
						(i.initiatorId = s),
						(i.callId = o),
						(i.audioOnly = t),
						(i.sessionCallback = r),
						(window.__callSession = i),
						i
					);
				}
				getParticipantIds() {
					let e = [];
					for (const [s, o] of this.subscriberMap) e.push(s);
					return e;
				}
				setAcceptTime(e) {
					this.acceptTime || (this.acceptTime = e), this.updateInitiator();
				}
				setUserAcceptTime(e, s) {
					console.log('av-c', 'setUserAcceptTime', e, s);
					let o = this.getSubscriber(e);
					o
						? (o.acceptTime = s)
						: console.error(
								'av-c',
								'setUserAcceptTime undefined',
								e,
								this.status,
								this.subscriberMap
						  );
				}
				setUserJoinTime(e, s) {
					this.getSubscriber(e).joinTime = s;
				}
				getSubscriber(e, s) {
					return s && (e = 'screen_sharing_' + e), this.subscriberMap.get(e);
				}
				getParticipantProfiles() {
					let e = [];
					for (const s of this.subscriberMap.values()) {
						let o = new z.a();
						(o.userId = s.userId),
							(o.status = s.status),
							(o.joinTime = s.joinTime),
							(o.acceptTime = s.acceptTime),
							(o.audioMuted = s.audioMuted),
							(o.videoMuted = s.videoMuted),
							(o.audience = s.audience),
							e.push(o);
					}
					return e;
				}
				getSelfProfile() {
					let e = new z.a();
					return (
						(e.userId = this.userId),
						(e.status = this.status),
						(e.joinTime = this.joinTime),
						(e.acceptTime = this.acceptTime),
						(e.audioMuted = this.audioMuted),
						(e.videoMuted = this.videoMuted),
						(e.audience = this.audience),
						e
					);
				}
				getPeerConnection(e) {
					return this.subscriberMap.get(e).peerConnection;
				}
				answer(e, s) {
					this.status === p.a.STATUS_INCOMING &&
						(this.setState(p.a.STATUS_CONNECTING),
						this.audioOnly && !e && (e = !0),
						(this.audioOnly = e),
						(this.callExtra = s),
						S.default.answerCurrentCall());
				}
				setState(e) {
					if (
						this.status !== e &&
						(this.status !== p.a.STATUS_CONNECTED || e !== p.a.STATUS_CONNECTING)
					) {
						if (
							((this.status = e),
							console.log('av-c', 'set status', e, this.startMsgUid),
							e === p.a.STATUS_IDLE || e === p.a.STATUS_CONNECTED)
						) {
							if (this.startMsgUid) {
								let s = { audioOnly: this.audioOnly };
								(s.status = this.endReason ? this.endReason : 0),
									e === p.a.STATUS_CONNECTED
										? ((s.connectTime = new Date().getTime()), (this.startTime = s.connectTime))
										: (s.endTime = new Date().getTime()),
									S.default.updateCallStartMessageContent(this.startMsgUid, s);
							}
							e === p.a.STATUS_CONNECTED &&
								(this.conference
									? this.notifyMuteState()
									: !this.audioOnly && this.videoMuted && this.sendMuteVideoMessage());
						} else e === p.a.STATUS_CONNECTING && this.joinAndPublish();
						this.sessionCallback && this.sessionCallback.didChangeState(e);
					}
				}
				initSession(e, s, o = [], t = [], r = !1) {
					(this.moCall = e),
						(this.selfUserInfo = s),
						(this.participantUserInfos = o),
						(this.singleCall = o && 1 === o.length),
						(this.groupMemberUserInfos = t);
					let i = s;
					!e && o && o.length > 0 && (i = o.filter(e => e.uid === this.initiatorId)[0]),
						this.sessionCallback.onInitial(this, s, i, o, t);
					let n = [];
					o &&
						o.length > 0 &&
						o.forEach(e => {
							n.push(e.uid);
						}),
						this.initSubscriberMap(n),
						r ||
							(e
								? (this.setState(p.a.STATUS_OUTGOING), this.startPreview(this.audioOnly))
								: this.playIncomingRing()),
						this.callTimeout(),
						(window.AudioContext = window.AudioContext || window.webkitAudioContext),
						(window.audioContext = new AudioContext()),
						setTimeout(() => {
							window.addEventListener('beforeunload', this.onVoipWindowClose);
						}, 500),
						(this.soundMeterTimer = setInterval(() => {
							if (!this.sessionCallback) return;
							let e = this.cameraPublisher && this.cameraPublisher.soundMeter;
							if (e) {
								let s = e.instant.toFixed(2);
								e.slow.toFixed(2), e.clip;
								this.sessionCallback.didReportAudioVolume(this.selfUserInfo.uid, Number(s));
							}
							this.subscriberMap.forEach((e, s) => {
								if (!e.soundMeter) return;
								let o = e.soundMeter.instant.toFixed(2);
								e.soundMeter.slow.toFixed(2), e.soundMeter.clip;
								this.sessionCallback.didReportAudioVolume(s, Number(o));
							});
						}, 100)),
						localStorage.getItem('enable_voip_debug') &&
							(this.pcStatsTimer = setInterval(() => {
								this.subscriberMap.forEach((e, s) => {
									let o = e.peerConnection;
									o &&
										o.getStats().then(e => {
											this.showRemoteStats(s, e);
										});
								});
							}, 1e3));
				}
				initSubscriberMap(e) {
					console.log('av-c', 'initParticipantClientMap', e),
						this.subscriberMap || (this.subscriberMap = new d.a()),
						!e ||
							e.length < 1 ||
							e.forEach(e => {
								let s = new y.default(e, this);
								(s.status = p.a.STATUS_INCOMING), this.subscriberMap.set(e, s);
							}, this);
				}
				inviteNewParticipants(e, s, o) {
					e.length &&
						(e = e.filter(
							e =>
								e !== this.selfUserInfo.uid &&
								this.participantUserInfos.findIndex(s => s.uid === e) < 0
						)).length &&
						((this.singleCall = !1), S.default.inviteNewParticipants(e, this.pin, s, o));
				}
				getExistParticipantsStatus() {
					let e = [];
					return (
						e.push({
							userId: this.selfUserInfo.uid,
							acceptTime: this.acceptTime ? Object(M.numberValue)(this.acceptTime) : 0,
							joinTime: this.joinTime ? Object(M.numberValue)(this.joinTime) : 0,
							videoMuted: this.videoMuted
						}),
						this.participantUserInfos.forEach(s => {
							let o = this.getSubscriber(s.uid);
							e.push({
								userId: o.userId,
								acceptTime: o.acceptTime ? Object(M.numberValue)(o.acceptTime) : 0,
								joinTime: o.joinTime ? Object(M.numberValue)(o.joinTime) : 0,
								videoMuted: o.videoMuted
							});
						}, this),
						e
					);
				}
				didAddNewParticipants(e, s) {
					e.forEach(e => {
						-1 === s.findIndex(s => s.uid === e) && s.push(new W.a(e));
					}),
						console.log('av-c', 'didAddNewParticipants', e, s),
						e.forEach(e => {
							let s = new y.default(e, this);
							(s.status = p.a.STATUS_INCOMING), this.subscriberMap.set(e, s);
						}, this),
						s.forEach(e => {
							this.participantUserInfos.push(e),
								this.sessionCallback && this.sessionCallback.didParticipantJoined(e.uid, !1);
						}, this);
				}
				updateExistParticipant(e, s) {
					e.forEach(e => {
						let o = this.getSubscriber(e.userId);
						(o.status = p.a.STATUS_INCOMING),
							(o.joinTime = s),
							(o.videoMuted = e.videoMuted),
							(o.acceptTime = e.acceptTime);
					});
				}
				updateVideoMute(e, s) {
					let o = this.getSubscriber(e);
					o &&
						o.videoMuted !== s &&
						((o.videoMuted = s), this.sessionCallback && this.sessionCallback.didVideoMuted(e, s));
				}
				defaultVideoConstraints(e) {
					let s;
					if (e)
						s = {
							audio: { echoCancellation: !0, noiseSuppression: !0, autoGainControl: !0 },
							video: !1
						};
					else {
						let e = J.a.getVideoProfile(O.a.VIDEO_PROFILE);
						s = {
							audio: { echoCancellation: !0, noiseSuppression: !0, autoGainControl: !0 },
							video: {
								width: { ideal: e.width },
								height: { ideal: e.height },
								frameRate: { ideal: e.fps }
							}
						};
					}
					return (
						this.audioInputDeviceId && (s.audio.deviceId = this.audioInputDeviceId),
						e ||
							(this.videoInputDeviceId
								? (s.video.deviceId = this.videoInputDeviceId)
								: (s.video.facingMode = this.videoCamera)),
						console.log('av-c', 'camera stream constraints', m()(s)),
						s
					);
				}
				createLocalCameraVideoStream(e, s = !0) {
					var o = this;
					return l()(function* () {
						try {
							console.log('av-c', 'createLocalCameraVideoStream', e);
							const t = yield navigator.mediaDevices.getUserMedia(o.defaultVideoConstraints(e));
							console.log('av-c', 'Received local stream', t, t.getVideoTracks().length, e),
								s &&
									o.sessionCallback &&
									!o.videoMuted &&
									o.sessionCallback.didCreateLocalVideoTrack(t, !1);
							const r = t.getVideoTracks();
							e
								? r &&
								  r.length > 0 &&
								  (console.log('av-c', 'audioOnly, stop video track'),
								  r.forEach(function (e) {
										return e.stop();
								  }))
								: r && r.length > 0 && console.log('av-c', `Using video device: ${r[0].label}`);
							const i = t.getAudioTracks();
							return i.length > 0 && console.log('av-c', `Using audio device: ${i[0].label}`), t;
						} catch (e) {
							console.error('av-c', 'getUserMedia error', e);
						}
						return console.log('av-c', 'createLocalCameraVideoStream failed'), null;
					})();
				}
				startPreview(e) {
					var s = this;
					return l()(function* () {
						console.log('av-c', 'start preview'),
							(s.conference && s.audience) ||
								((s.previewStream = yield s.createLocalCameraVideoStream(e)),
								s.previewStream || (s.previewStream = yield s.createDummyVideoStream()),
								s.previewStream || s.endCall(g.a.REASON_MediaError));
					})();
				}
				getDesktopSources(e) {
					return C.desktopCapturer ? C.desktopCapturer.getSources({ types: e }) : null;
				}
				startScreenShare(e) {
					var s = this;
					return l()(function* () {
						if (!s.isScreenSharing() && !s.audioOnly) {
							if (
								(console.log('av-c', 'start screen share'),
								console.log('av-c', 'desktopCapturer ', C.desktopCapturer),
								C.desktopCapturer)
							)
								(s.screenShareStream = yield navigator.mediaDevices.getUserMedia({
									audio: !1,
									video: { mandatory: n()({ chromeMediaSource: 'desktop' }, e) }
								})),
									console.log('av-c', 'desktopCapturer screen share stream', s.screenShareStream);
							else {
								console.log('av-c', 'await screenShareStream');
								let o = {};
								e &&
									(o = {
										height: { max: e.maxHeight, min: e.minHeight, ideal: e.idealHeight },
										width: { max: e.maxWidth, min: e.minWidth, ideal: e.idealWidth },
										frameRate: e.frameRate
									}),
									(s.screenShareStream = yield navigator.mediaDevices.getDisplayMedia({
										audio: !1,
										video: o
									})),
									console.log('av-c', 'screenShareStream', s.screenShareStream);
							}
							if (
								((s.screenSharing = !0),
								s.sessionCallback &&
									s.sessionCallback.didCreateLocalVideoTrack(s.screenShareStream, !0),
								s.screenShareStream.getVideoTracks()[0].addEventListener('ended', function () {
									s.stopScreenShare();
								}),
								O.a.SCREEN_SHARING_REPLACE_MODE)
							) {
								if (s.cameraPublisher.videoSender) {
									if (
										(s.cameraPublisher.videoSender.replaceTrack(
											s.screenShareStream.getVideoTracks()[0]
										),
										!O.a.DISABLE_DUAL_STREAM)
									) {
										let e = s.screenShareStream.getVideoTracks()[0].clone();
										s.cameraPublisher.smallVideoSender.replaceTrack(e);
									}
									s.cameraPublisher.stopVideoTrack(s.cameraPublisher.cameraVideoStream);
								} else console.error('av-c', 'screen share error, publisher.videoSender is null');
								s.notifyMuteState();
							} else
								(s.screenSharingPublisher = new F.default(s)),
									s.screenSharingPublisher.attachAndPublishScreenSharing();
						}
					})();
				}
				isScreenSharing() {
					return !!this.screenShareStream;
				}
				stopScreenShare() {
					var e = this;
					console.log('av-c', 'stopScreenShare'),
						O.a.SCREEN_SHARING_REPLACE_MODE
							? (this.cameraPublisher.stopVideoStream(this.screenShareStream),
							  (this.screenShareStream = null),
							  (this.screenSharing = !1),
							  this.videoMuted
									? this.notifyMuteState()
									: l()(function* () {
											(e.cameraPublisher.cameraVideoStream = null),
												yield e.cameraPublisher.createMediaSenders(),
												e.sessionCallback &&
													e.sessionCallback.didCreateLocalVideoTrack(
														e.cameraPublisher.cameraVideoStream,
														!1
													);
									  })())
							: (this.screenSharingPublisher &&
									(this.screenSharingPublisher.stopVideoStream(this.screenShareStream),
									this.screenSharingPublisher.unPublishMedia(),
									this.screenSharingPublisher.deattach(),
									(this.screenSharingPublisher = null),
									this.sessionCallback &&
										(this.sessionCallback.didParticipantLeft(
											this.selfUserInfo.uid,
											g.a.REASON_Hangup,
											!0
										),
										this.cameraPublisher &&
											this.cameraPublisher.cameraVideoStream &&
											!this.videoMuted &&
											!this.audioMuted &&
											this.sessionCallback.didCreateLocalVideoTrack(
												this.cameraPublisher.cameraVideoStream,
												!1
											))),
							  (this.screenShareStream = null),
							  (this.screenSharing = !1)),
						this.sessionCallback && this.sessionCallback.didScreenShareEnded();
				}
				createPeerConnection(e, s) {
					let o = this.getSelectedSdpSemantics();
					if (
						(console.log('av-c', 'RTCPeerConnection configuration:', o),
						f.a.ICE_SERVERS && f.a.ICE_SERVERS.length)
					) {
						o.iceServers = [];
						for (const e of f.a.ICE_SERVERS)
							o.iceServers.push({
								credential: e[2],
								credentialType: 'password',
								urls: e[0],
								username: e[1]
							});
					}
					let t = O.a.FORCE_RELAY;
					void 0 === t && (t = O.a.FORCE_REPLAY),
						t &&
							(console.log('av-c', 'force relay', o.iceServers.length),
							(o.iceTransportPolicy = 'relay'));
					let r = new RTCPeerConnection(o);
					if (e !== this.selfUserInfo.uid) {
						let o = this.getSubscriber(e);
						(o.peerConnection = r), (o.isInitiator = s);
					}
					return (
						console.log('av-c', 'Created local peer connection object pc'),
						r.addEventListener('icecandidate', s => this.onIceCandidate(e, r, s)),
						r.addEventListener('iceconnectionstatechange', s => this.onIceStateChange(e, r, s)),
						r.addEventListener('track', s => this.gotRemoteStream(e, s)),
						r.addEventListener('connectionstatechange', s => this.onConnectionStateChange(e, r, s)),
						console.log('av-c', 'createPeerConnection', e, this.getSubscriber(e)),
						r
					);
				}
				getSelectedSdpSemantics() {
					return { bundlePolicy: 'max-bundle' };
				}
				onConnectionStateChange(e, s, o) {
					console.log('av-c', 'onConnectionStateChange', e, s, o), s.connectionState;
				}
				hangup() {
					console.log('av-c', 'Ending call'), this.endCall(g.a.REASON_Hangup);
				}
				setAudioOnly(e) {
					(this.audioOnly = e),
						this.conference || (this.sessionCallback && this.sessionCallback.didChangeMode(e));
				}
				downgrade2Voice(e = !1) {
					this.status === p.a.STATUS_CONNECTED &&
						(this.cameraPublisher.stopVideoTrack(this.cameraPublisher.cameraVideoStream),
						e || S.default.downgrade2VoiceCall(),
						this.setAudioOnly(!0));
				}
				downToVoice() {
					if (
						(console.log('av-c', 'down to voice'),
						this.stopIncomingRing(),
						this.status === p.a.STATUS_INCOMING)
					)
						return this.setAudioOnly(!0), void this.answerCall(!0);
					this.status === p.a.STATUS_CONNECTED &&
						(this.audioOnly || (this.setAudioOnly(!0), S.default.downgrade2VoiceCall()));
				}
				muteVideo(e) {
					var s = this;
					return l()(function* () {
						return yield s.setVideoEnabled(!e);
					})();
				}
				setVideoEnabled(e) {
					var s = this;
					return l()(function* () {
						return s.status !== p.a.STATUS_CONNECTED
							? (console.error('av-c', 'connecting, can not muteVideo'), !1)
							: s.videoMuted === !e
							? (console.log('av-c', 'setVideoEnabled error', s.videoMuted, e), !1)
							: s.audioOnly || (s.isScreenSharing() && O.a.SCREEN_SHARING_REPLACE_MODE)
							? (console.log(
									'av-c',
									'setVideoEnabled error audioOnly, or (isScreenSharing and replace mode)'
							  ),
							  !1)
							: s._isMuting
							? (console.log('av-c', 'mute operation is still in progress'), !1)
							: ((s.videoMuted = !e),
							  console.log('av-c', `setVideoEnabled ${e}, audience ${s.audience}`),
							  s.audience ||
									((s._isMuting = !0),
									e
										? (s.cameraPublisher ||
												((s.cameraPublisher = new G.default(s)),
												(s.cameraPublisher.handleId = s.handleId)),
										  s.cameraPublisher.stopVideoStream(s.cameraPublisher.cameraVideoStream),
										  (s.cameraPublisher.cameraVideoStream = null),
										  yield s.cameraPublisher.createMediaSenders())
										: s.cameraPublisher &&
										  s.cameraPublisher.stopVideoTrack(s.cameraPublisher.cameraVideoStream),
									s.conference ? s.notifyMuteState() : s.sendMuteVideoMessage(),
									s.sessionCallback && s.sessionCallback.didMuteStateChanged([s.selfUserInfo.uid]),
									console.log('av-c', 'mute video end'),
									(s._isMuting = !1)),
							  !0);
					})();
				}
				sendMuteVideoMessage() {
					let e = new q.a();
					(e.callId = this.callId),
						(e.videoMuted = this.videoMuted),
						(e.existParticipants = this.getExistParticipantsStatus()),
						S.default.sendSignalMessage(e, this.getParticipantIds(), !0, !1);
				}
				muteAudio(e) {
					var s = this;
					return l()(function* () {
						return yield s.setAudioEnabled(!e);
					})();
				}
				setAudioEnabled(e) {
					var s = this;
					return l()(function* () {
						return s.status !== p.a.STATUS_CONNECTED
							? (console.error('av-c', 'connecting, can not muteAudio'), !1)
							: s.audioMuted !== !e &&
									(s._isMuting
										? (console.log('av-c', 'mute operation is still in progress'), !1)
										: ((s.audioMuted = !e),
										  s.audience ||
												((s._isMuting = !0),
												console.log('av-c', 'setAudioEnabled', e),
												e
													? s.cameraPublisher && s.cameraPublisher.cameraVideoStream
														? s.cameraPublisher.cameraVideoStream.getTracks().forEach(function (e) {
																'audio' === e.kind && (e.enabled = !0);
														  })
														: ((s.cameraPublisher = new G.default(s)),
														  (s.cameraPublisher.handleId = s.handleId),
														  yield s.cameraPublisher.createMediaSenders())
													: s.cameraPublisher &&
													  s.cameraPublisher.stopAudioTrack(s.cameraPublisher.cameraVideoStream),
												s.conference &&
													s.status === p.a.STATUS_CONNECTED &&
													(s.notifyMuteState(),
													s.sessionCallback &&
														s.sessionCallback.didMuteStateChanged([s.selfUserInfo.uid])),
												console.log('av-c', 'mute audio end'),
												(s._isMuting = !1)),
										  !0));
					})();
				}
				forceEndMedia() {
					this.endMedia();
				}
				endMedia(e, s) {
					if (
						(console.log('av-c', 'Ending media'),
						this.setState(p.a.STATUS_IDLE),
						this.stopIncomingRing(),
						this.cameraPublisher && this.cameraPublisher.endMedia(),
						this.screenSharingPublisher && this.screenSharingPublisher.endMedia(),
						this.previewStream)
					) {
						console.log('av-c', 'stop previewStream');
						const e = this.previewStream;
						(this.previewStream = null),
							void 0 === e.getTracks
								? e.stop()
								: e.getTracks().forEach(e => {
										console.log('av-c', 'stop preview track', e.kind, e.id), e.stop();
								  });
					}
				}
				endUserCall(e, s) {
					if ((console.log('av-c', 'endUserCall', e, s), e === this.selfUserInfo.uid))
						return void this.endCall(s);
					if (!this.conference && e === this.initiatorId) {
						let o = !1;
						for (const [s, t] of this.subscriberMap)
							if (
								(console.log('av-c', 'endUserCall acceptTime', t.acceptTime),
								t.acceptTime && Object(M.gt)(t.acceptTime, 0) && e !== s)
							) {
								o = !0;
								break;
							}
						if (
							((o = o || (this.acceptTime && this.acceptTime && Object(M.gt)(this.acceptTime, 0))),
							!o)
						)
							return void this.endCall(s);
					}
					let o = this.getSubscriber(e);
					if (
						(this.subscriberMap.delete(e),
						this.participantUserInfos &&
							(this.participantUserInfos = this.participantUserInfos.filter(s => s.uid !== e)),
						o)
					) {
						if (o.peerConnection) {
							o.peerConnection.getSenders().forEach(e => o.peerConnection.removeTrack(e)),
								o.peerConnection.close(),
								(o.peerConnection = null);
						}
						if ((e === this.initiatorId && this.updateInitiator(), this.sessionCallback)) {
							let o = this.getScreenSharingUserId(e);
							this.sessionCallback.didParticipantLeft(o || e, s, null !== o);
						}
					}
					(this.isAllBusy = this.isAllBusy && s === g.a.RemoteBusy),
						this.conference ||
							0 !== this.subscriberMap.size ||
							(0 === this.conversation.type || this.singleCall
								? this.endCall(s)
								: this.endCall(this.isAllBusy ? g.a.RemoteBusy : g.a.REASON_AllLeft));
				}
				leaveConference(e) {
					this.conference ? this.endCall(g.a.REASON_Hangup, e) : this.endCall(g.a.REASON_Hangup);
				}
				endCall(e, s) {
					if (
						(console.log('av-c', 'endCall ---- ', e, new Error().stack),
						(this.endReason = e),
						this.status === p.a.STATUS_IDLE)
					)
						return;
					if (
						(this.setState(p.a.STATUS_IDLE),
						!this.conference &&
							e !== g.a.REASON_AcceptByOtherClient &&
							e !== g.a.REASON_AllLeft &&
							this.getParticipantIds().length > 0)
					) {
						let s = new v.a();
						(s.callId = this.callId),
							(s.inviteMsgUid = this.startMsgUid),
							(s.reason = e),
							S.default.sendSignalMessage(s, this.getParticipantIds(), !1, !0);
					}
					if (e !== g.a.REASON_AcceptByOtherClient) {
						let o = {};
						(o.handle_id = this.handleId),
							(s ||
								e === g.a.REASON_AllLeft ||
								(this.conversation &&
									this.conversation.type === k.a.Single &&
									[g.a.RemoteBusy, g.a.REASON_RemoteHangup, g.a.RemoteTimeout].indexOf(e) > -1)) &&
								(o.destroy = !0),
							console.log('av-c', 'leave', m()(o)),
							S.default.sendConferenceRequest(
								this.sessionId,
								this.callId,
								'leave',
								m()(o),
								(e, s) => {
									0 !== e && console.log('av-c', 'conference request, leave error', e);
								}
							);
					}
					let o = {
						callId: this.callId,
						audioOnly: this.audioOnly,
						pin: this.pin,
						host: this.host,
						title: this.title,
						desc: this.desc,
						audience: this.audience,
						advance: this.advance
					};
					this.keepAlive(!1), (this.endTime = new Date().valueOf());
					for (const [e, s] of this.subscriberMap)
						s.peerConnection && (s.peerConnection.close(), (s.peerConnection = null));
					(S.default.currentSession = null),
						clearInterval(this.callTimer),
						clearInterval(this.soundMeterTimer),
						this.pcStatsTimer && clearInterval(this.pcStatsTimer),
						this.endMedia(e, o),
						this.sessionCallback && this.sessionCallback.didCallEndWithReason(e);
				}
				defaultPublishMediaConstraints() {
					return { offerToReceiveAudio: !1, offerToReceiveVideo: !1 };
				}
				defaultSubscribeMediaConstraints() {
					return { offerToReceiveAudio: !0, offerToReceiveVideo: !0 };
				}
				restartPublishConstrains() {
					return { iceRestart: !0, offerToReceiveAudio: !1, offerToReceiveVideo: !1 };
				}
				createRoom(e) {
					let s = { participants: 9 };
					(s.pin = this.pin),
						(s.publishers = this.audioOnly
							? O.a.MAX_AUDIO_PARTICIPANT_COUNT
							: O.a.MAX_VIDEO_PARTICIPANT_COUNT),
						this.conference || (s.is_private = !0),
						this.record && (s.record = !0),
						console.log('av-c', 'create room'),
						S.default.sendConferenceRequest(
							this.sessionId,
							this.callId,
							'create_room',
							m()(s),
							(s, o) => {
								if ((console.log('av-c', 'create_room res', s, o), 0 !== s)) return void e(s);
								let t = JSON.parse(o);
								(this.sessionId = t.session_id), (this.handleId = t.handle_id);
								let r = t.data;
								this.callId,
									r.room,
									this.keepAlive(!0),
									e(0),
									this.conference && this.setState(p.a.STATUS_CONNECTING);
							}
						);
				}
				onConferenceEvent(e) {
					let s;
					if (
						((s = 'string' == typeof e ? JSON.parse(e) : e),
						console.log('av-c', 'onConferenceEvent', m()(e)),
						this.sessionId !== s.session_id)
					)
						return void console.log(
							'av-c',
							'on conference event, unknown sessionId',
							this.sessionId,
							s.session_id
						);
					let o = s.sender;
					if (o > 0 && this.screenSharingPublisher && this.screenSharingPublisher.handleId === o)
						return;
					let t = s.data,
						r = t ? t.videoroom : null;
					if ('event' === r) {
						let e = t.publishers;
						this.onPublish(e);
						let s = t.unpublished;
						s && this.onUnpublish(s);
						let o = t.leaving;
						o &&
							('ok' === o && 'kicked' === t.reason
								? this.onLeave(this.selfUserInfo.uid)
								: this.onLeave(o));
						let r = t.kicked;
						r && this.onLeave(r);
						let i = t.joining;
						i && this.onJoining([i]);
						let n = t.attendees;
						this.onJoining(n);
					} else if ('destroyed' === r) this.endCall(g.a.RoomDestroyed);
					else if ('participants' === r) {
						let e = t.attendees;
						if (e && e.length > 0) {
							let s = e.map(e => ({ id: e }));
							this.onJoining(s);
						}
						let s = t.leavings;
						s &&
							s.length > 0 &&
							s.forEach(e => {
								this.onLeave(e);
							});
					} else if ('mute' === r) {
						let e = t.mute;
						this.onMute(e);
					} else if ('slowlink' === r) {
						let e = s.handle_id;
						this.onSlowLink(this.sessionId, e, t);
					} else {
						'hangup' === s.janus || console.log('av-c', 'unknown event');
					}
				}
				keepAlive(e) {
					if (e) {
						if (this.keepAliveTimer) return;
						(this.lastActiveTime = new Date().getTime()),
							(this.keepAliveTimer = setInterval(() => {
								S.default.sendConferenceRequest(
									this.sessionId,
									this.callId,
									'keepalive',
									'',
									(e, s) => {
										let o = new Date().getTime();
										if (0 === e) this.lastActiveTime = o;
										else {
											if (253 === e || 61 === e)
												return (
													console.error('av-c', 'keepalive error', e),
													void this.endCall(g.a.REASON_SignalError)
												);
											this.lastActiveTime > 0 &&
												o - this.lastActiveTime > 6e4 &&
												(console.error('av-c', 'keepalive not response', o - this.lastActiveTime),
												this.endCall(g.a.REASON_SignalError));
										}
									}
								);
							}, 2e4));
					} else
						this.keepAliveTimer && (clearInterval(this.keepAliveTimer), (this.keepAliveTimer = 0));
				}
				joinAndPublish() {
					let e = {};
					(e.handle_id = this.handleId),
						(e.user_id = this.selfUserInfo.uid),
						(e.pin = this.pin),
						this.callExtra && (e.extra = this.callExtra),
						S.default.sendConferenceRequest(
							this.sessionId,
							this.callId,
							'join_pub',
							m()(e),
							(e, s) => {
								if ((console.log('av-c', 'join_pub', e, s), 0 !== e))
									return void this.endCall(g.a.REASON_SignalError);
								let o = JSON.parse(s),
									t = o.data,
									r = t.error_code;
								if (r > 0)
									return (
										console.log('av-c', 'conference request, join_pub error', r),
										void (r === w.default.JANUS_VIDEOROOM_ERROR_NO_SUCH_ROOM
											? this.endCall(g.a.RoomNotExist)
											: r === w.default.JANUS_VIDEOROOM_ERROR_ID_EXISTS &&
											  setTimeout(() => {
													this.status !== p.a.STATUS_IDLE &&
														(console.log('av-c', 'rejoin'), this.joinAndPublish());
											  }, 500))
									);
								(this.sessionId = o.session_id), (this.handleId = o.handle_id);
								let i = t.publishers;
								this.onPublish(i);
								t.id, t.videoroom;
								this.privateId = t.private_id;
								t.description, t.room;
								let n = t.attendees;
								this.onJoining(n),
									this.audience
										? this.setState(p.a.STATUS_CONNECTED)
										: ((this.cameraPublisher = new G.default(this)),
										  (this.cameraPublisher.handleId = this.handleId),
										  this.cameraPublisher.publishMedia()),
									this.keepAlive(!0);
							}
						);
				}
				updateInitiator() {
					let e,
						s = new Date().getTime() + 864e5;
					Object(M.gt)(this.acceptTime, 0) && ((s = this.acceptTime), (e = this.selfUserInfo.uid)),
						this.subscriberMap.forEach(o => {
							Object(M.gt)(o.acceptTime, 0) &&
								Object(M.gt)(s, o.acceptTime) &&
								((s = o.acceptTime), (e = o.userId));
						});
					let o = this.initiatorId;
					(this.initiatorId = e),
						this.subscriberMap.forEach(e => {
							e.isInitiator = this.initiatorId === e.userId;
						}),
						o !== e &&
							e !== this.selfUserInfo.uid &&
							this.sessionCallback &&
							this.sessionCallback.didChangeInitiator(this.initiatorId);
				}
				onParticipantPublish(e) {
					e.talking;
					let s = e.id;
					if (this.isSelfScreenSharing(s)) return;
					let o = e.display,
						t = !0,
						r = e.streams;
					for (const [e, i] of this.subscriberMap)
						if (e === s) {
							i.streams(r), (i.callExtra = o), i.subscribe(), (i.audience = !1), (t = !1);
							break;
						}
					if (t) {
						let e = new y.default(s, this);
						this.subscriberMap.set(e.userId, e);
						let t = new Date().valueOf();
						if (
							(this.setUserAcceptTime(e.userId, t),
							this.setUserJoinTime(e.userId, t),
							(e.status = p.a.STATUS_CONNECTED),
							(e.audience = !1),
							(e.callExtra = o),
							e.streams(r),
							e.subscribe(),
							this.sessionCallback)
						) {
							let s = this.getScreenSharingUserId(e.userId);
							this.sessionCallback.didParticipantJoined(s || e.userId, null !== s),
								this.reportAudienceChanged(e.userId, !1);
						}
					} else this.reportAudienceChanged(s, !1);
					let i = this.subscriberMap.get(s);
					(i.videoMuted || i.audioMuted) &&
						this.sessionCallback &&
						this.sessionCallback.didMuteStateChanged([s]);
				}
				onPublish(e) {
					if ((console.log('av-c', 'onPublish', e), e))
						for (const s of e) this.onParticipantPublish(s);
				}
				onJoining(e) {
					if (e) {
						console.log('av-c', 'onJoining', e);
						for (const s of e) {
							let e = s.id;
							if (this.isSelfScreenSharing(e)) continue;
							let o = !0,
								t = s.display;
							for (const [s, r] of this.subscriberMap)
								if (s === e) {
									(r.audience = !0), (r.callExtra = t), (o = !1);
									break;
								}
							if (o) {
								let s = new y.default(e, this);
								this.subscriberMap.set(s.userId, s);
								let o = new Date().valueOf();
								if (
									(this.setUserAcceptTime(s.userId, o),
									this.setUserJoinTime(s.userId, o),
									(s.status = p.a.STATUS_CONNECTED),
									(s.audience = !0),
									(s.callExtra = t),
									this.sessionCallback)
								) {
									let e = this.getScreenSharingUserId(s.userId);
									this.sessionCallback.didParticipantJoined(e || s.userId, null !== e);
								}
							}
						}
					}
				}
				onUnpublish(e) {
					for (const [s, o] of this.subscriberMap)
						if (s === e) {
							(o.audience = !0),
								(o.videoMuted = !1),
								(o.audioMuted = !1),
								o.peerConnection && (o.peerConnection.close(), (o.peerConnection = null)),
								(o.handleId = 0);
							break;
						}
					this.conference && e !== this.selfUserInfo.uid && this.reportAudienceChanged(e, !0);
				}
				reportAudienceChanged(e, s) {
					let o = this.getScreenSharingUserId(e);
					this.sessionCallback && this.sessionCallback.didChangeType(o || e, s, null !== o);
				}
				onSlowLink(e, s, o) {
					if (!this.sessionCallback) return;
					let t = o.media,
						r = o.uplink,
						i = o.lost;
					if (this.handleId === s) this.sessionCallback.didMediaLostPacket(t, i, !1);
					else if (this.screenSharingPublisher && this.screenSharingPublisher.handleId === s)
						this.sessionCallback.didMediaLostPacket(t, i, !0);
					else
						for (const [e, o] of this.subscriberMap)
							if (o.handleId === s) {
								if (e.startsWith('screen_sharing_')) {
									let s = e.substr('screen_sharing_'.length);
									this.sessionCallback.didUserMediaLostPacket(s, t, i, r, !0);
								} else this.sessionCallback.didUserMediaLostPacket(e, t, i, r, !1);
								break;
							}
				}
				onLeave(e) {
					this.getSubscriber(e)
						? this.endUserCall(e, g.a.REASON_RemoteHangup)
						: e === this.selfUserInfo.uid && this.endCall(g.a.REASON_Hangup, !1);
				}
				mediaStream() {
					return this.screenShareStream ? this.screenShareStream : this.cameraVideoStream;
				}
				requestChangeMode(e, s) {
					let o = new P.a(this.callId, s),
						t = new x.a(k.a.Single, e, 0);
					S.default.sendSignalMessageEx(t, o, this.getParticipantIds(), !1, !1);
				}
				onRequestChangeMode(e) {
					this.switchAudience(e);
				}
				onKickoff() {
					this.leaveConference(!1);
				}
				switchAudience(e) {
					var s = this;
					return l()(function* () {
						console.log('av-c', 'switchAudience', e),
							s.conference &&
								(s._isSwitchAudience || e === s.audience
									? console.log('av-c', `switchAudience isSwitching ${s._isSwitchAudience}`)
									: ((s._isSwitchAudience = !0),
									  e
											? s.audience ||
											  (s.screenSharingPublisher &&
													(yield s.screenSharingPublisher.unPublishMedia(),
													(s.screenSharingPublisher.screenShareStream = null)),
											  s.cameraPublisher &&
													(yield s.cameraPublisher.unPublishMedia(),
													(s.cameraPublisher.cameraVideoStream = null)),
											  s.reportAudienceChanged(s.selfUserInfo.uid, !0),
											  (s.audience = !0))
											: (s.cameraPublisher ||
													((s.cameraPublisher = new G.default(s)),
													(s.cameraPublisher.handleId = s.handleId)),
											  yield s.cameraPublisher.publishMedia(),
											  s.reportAudienceChanged(s.selfUserInfo.uid, !1),
											  s.notifyMuteState(),
											  (s.audience = !1)),
									  (s._isSwitchAudience = !1)));
					})();
				}
				onMute(e) {
					let s = [];
					this.subscriberMap.forEach((o, t) => {
						let r = e[t],
							i = !1,
							n = !1;
						r && ((i = !!r.hasOwnProperty('a') && r.a), (n = !!r.hasOwnProperty('v') && r.v)),
							(o.videoMuted === n && o.audioMuted === i) ||
								((o.videoMuted = n), (o.audioMuted = i), o.audience || s.push(t));
					}),
						(s = s.filter(e => e !== this.selfUserInfo.uid)),
						s.length > 0 && this.sessionCallback && this.sessionCallback.didMuteStateChanged(s);
				}
				notifyMuteState() {
					if (!this.conference || this.status === p.a.STATUS_IDLE) return;
					let e = { handle_id: this.handleId, a: this.audioMuted };
					O.a.SCREEN_SHARING_REPLACE_MODE
						? (e.v = this.videoMuted && !this.isScreenSharing())
						: (e.v = this.videoMuted),
						S.default.sendConferenceRequest(this.sessionId, this.callId, 'mute', m()(e), (e, s) => {
							console.log('av-c', 'mute res', e, s);
						});
				}
				kickoffParticipant(e, s, o) {
					if ((this.status === p.a.STATUS_IDLE && o && o(-1), this.getSubscriber(e))) {
						let t = { handle_id: this.handleId, user: e };
						S.default.sendConferenceRequest(this.sessionId, this.callId, 'kick', m()(t), (e, t) => {
							console.log('av-c', 'kick res', e, t), 0 === e ? s && s() : o && o(e);
						});
					}
				}
				closeVoipWindow() {
					localStorage.getItem('enable_voip_debug') ||
						(C.currentWindow ? C.currentWindow.close() : D.a.emitToMain('close-voip-div'));
				}
				setAudioInputDeviceId(e) {
					console.log('av-c', 'setInputAudioDeviceId', this.audioInputDeviceId, e),
						(e = 'null' === e ? null : e),
						(this.audioInputDeviceId = e),
						this.cameraPublisher &&
							(this.cameraPublisher.stopVideoStream(this.cameraPublisher.cameraVideoStream),
							(this.cameraPublisher.cameraVideoStream = null),
							this.cameraPublisher.createMediaSenders());
				}
				setVideoInputDeviceId(e) {
					console.log('av-c', 'setVideoInputDeviceId', this.videoInputDeviceId, e),
						(e = 'null' === e ? null : e),
						(this.videoInputDeviceId = e),
						this.cameraPublisher &&
							(this.cameraPublisher.stopVideoStream(this.cameraPublisher.cameraVideoStream),
							(this.cameraPublisher.cameraVideoStream = null),
							this.cameraPublisher.createMediaSenders());
				}
				setInputStream(e) {
					(this.externalStream = e),
						this.cameraPublisher &&
							(this.cameraPublisher.stopVideoStream(this.cameraPublisher.cameraVideoStream),
							(this.cameraPublisher.cameraVideoStream = null),
							this.cameraPublisher.createMediaSenders());
				}
				switchCamera() {
					return (
						!!navigator.mediaDevices.getSupportedConstraints().facingMode &&
						('user' === this.videoCamera
							? (this.videoCamera = 'environment')
							: (this.videoCamera = 'user'),
						console.log('av-c', 'switch camera', this.videoCamera),
						this.cameraPublisher.stopVideoStream(this.cameraPublisher.cameraVideoStream),
						this.cameraPublisher.createMediaSenders(),
						!0)
					);
				}
				rotate(e) {
					console.log('av-c', 'rotate', e), this.cameraPublisher && this.cameraPublisher.rotate(e);
				}
				screenSharingId() {
					return 'screen_sharing_' + this.selfUserInfo.uid;
				}
				getScreenSharingUserId(e = '') {
					return e.startsWith('screen_sharing_') ? e.substring('screen_sharing_'.length) : null;
				}
				isSelfScreenSharing(e = '') {
					return this.screenSharingId() === e;
				}
				setVideoMaxBitrate(e) {
					this.cameraPublisher && this.cameraPublisher.setVideoMaxBitrate(e);
				}
				scaleVideoResolutionDownBy(e) {
					this.cameraPublisher && this.cameraPublisher.scaleVideoResolutionDownBy(e);
				}
				dumpStats(e) {
					let s = '';
					return (
						e.forEach(e => {
							(s += '<h3>Report type='),
								(s += e.type),
								(s += '</h3>\n'),
								(s += `id ${e.id}<br>`),
								(s += `time ${e.timestamp}<br>`),
								r()(e).forEach(o => {
									'timestamp' !== o &&
										'type' !== o &&
										'id' !== o &&
										('object' == typeof e[o]
											? (s += `${o}: ${m()(e[o])}<br>`)
											: (s += `${o}: ${e[o]}<br>`));
								});
						}),
						s
					);
				}
				showRemoteStats(e, s) {
					const o = this.dumpStats(s);
					localStorage.getItem('enable_voip_debug.stats') &&
						console.log('av-c', 'statsString', e, o),
						s.forEach(s => {
							const o = s.timestamp;
							let t;
							if ('inbound-rtp' === s.type && 'video' === s.mediaType) {
								const e = s.bytesReceived;
								this.timestampPrev &&
									((t = (8 * (e - this.bytesPrev)) / (o - this.timestampPrev)),
									(t = Math.floor(t))),
									(this.bytesPrev = e),
									(this.timestampPrev = o);
							}
							t && ((t += ' kbits/sec'), console.log('av-c', 'Bitrate:', e, t));
						});
				}
				setParticipantVideoType(e, s, o) {
					s && (e = 'screen_sharing_' + e);
					for (const [s, t] of this.subscriberMap)
						if (e === s) {
							t.setVideoType(o);
							break;
						}
				}
				onIMConnectionStatusChange(e) {
					console.log('av-c', 'onIMConnectionStatusChange', e),
						1 === e &&
							(this.status === p.a.STATUS_CONNECTED && this.queryParticipantStatus(),
							this.cameraPublisher && this.cameraPublisher.restartICE(),
							this.screenSharingPublisher && this.screenSharingPublisher.restartICE(),
							this.subscriberMap &&
								this.subscriberMap.forEach((e, s) => {
									e.restartICE();
								}));
				}
				queryParticipantStatus() {
					let e = { handle_id: this.handleId, pin: this.pin };
					S.default.sendConferenceRequest(
						this.sessionId,
						this.callId,
						'query_participants',
						m()(e),
						(e, s) => {
							if (0 !== e)
								return void console.log('av-c', 'conference request, query_participants error', e);
							let o = JSON.parse(s).data;
							if (o) {
								let e = o.publishers,
									s = o.attendees;
								this.onJoining(s), this.onPublish(e);
							}
						}
					);
				}
			}
		},
		'./av/internal/publisherImpl.js': function (e, s, o) {
			'use strict';
			o.r(s),
				o.d(s, 'default', function () {
					return j;
				});
			var t = o('./node_modules/babel-runtime/core-js/json/stringify.js'),
				r = o.n(t),
				i = o('./node_modules/babel-runtime/core-js/promise.js'),
				n = o.n(i),
				a = o('./node_modules/babel-runtime/helpers/asyncToGenerator.js'),
				l = o.n(a),
				c = o('../engine/callState'),
				d = o.n(c),
				u = o('./av/internal/avenginekitImpl.js'),
				m = o('./av/internal/roomErrorCode.js'),
				h = o('../engine/callEndReason'),
				f = o.n(h),
				_ = o('../engine/avenginekit'),
				p = o.n(_),
				S = o('../engine/videoProfile'),
				b = o.n(S),
				g = o('./av/internal/soundMeter.js');
			class j {
				constructor(e) {
					this.callSession = e;
				}
				createMediaSenders() {
					var e = this;
					return l()(function* () {
						let s;
						if (
							(console.log(
								'av-c',
								'createMediaSenders -0',
								e.callSession.audioOnly,
								e.callSession.videoMuted
							),
							e.callSession.externalStream)
						)
							(e.cameraVideoStream = e.callSession.externalStream),
								e.callSession.sessionCallback &&
									!e.callSession.videoMuted &&
									e.callSession.sessionCallback.didCreateLocalVideoTrack(
										e.callSession.externalStream,
										!1
									);
						else {
							if (
								((e.cameraVideoStream = yield e.callSession.createLocalCameraVideoStream(
									e.callSession.audioOnly ||
										(p.a.SCREEN_SHARING_REPLACE_MODE && e.callSession.isScreenSharing())
								)),
								!e.cameraVideoStream)
							) {
								if (!e.callSession.audioOnly) {
									let o = yield e.callSession.createLocalCameraVideoStream(!0);
									o && (s = o.getAudioTracks()[0]);
								}
								if (((e.cameraVideoStream = yield e.createDummyVideoStream()), s)) {
									let o = e.cameraVideoStream.getAudioTracks()[0];
									e.cameraVideoStream.removeTrack(o), e.cameraVideoStream.addTrack(s);
								} else s = e.cameraVideoStream.getAudioTracks()[0];
							}
							if (!e.cameraVideoStream)
								return (
									console.log('av-c', 'can not getUserMedia'),
									void e.callSession.endCall(f.a.REASON_MediaError)
								);
						}
						if (((s = e.cameraVideoStream.getAudioTracks()[0]), !s && !e.callSession.audioOnly)) {
							console.warn('do not have audioTrack, create local audio track');
							let o = yield e.callSession.createLocalCameraVideoStream(!0, !1);
							o && (s = o.getAudioTracks()[0]);
						}
						if (
							((s.enabled = !e.callSession.audioMuted),
							(s.onended = function () {
								e.callSession.status !== d.a.STATUS_IDLE &&
									(console.log('av-c', 'audio track ended, re-createMediaSenders'),
									(e.callSession.audioInputDeviceId = null),
									e.stopVideoTrack(e.cameraVideoStream),
									(e.cameraVideoStream = null),
									e.createMediaSenders());
							}),
							e.cameraVideoStream.getVideoTracks().length > 0)
						) {
							e.cameraVideoStream.getVideoTracks()[0].onended = function () {
								console.log('av-c', 'video track ended'),
									e.callSession.endCall(f.a.REASON_MediaError);
							};
						}
						e.soundMeter && (e.soundMeter.stop(), (e.soundMeter = null));
						let o = new g.SoundMeter(window.audioContext);
						try {
							o.connectToSource(e.cameraVideoStream, function (e) {
								console.log('av-c', 'connect to self soundMeter', e);
							});
						} catch (e) {
							console.error(e);
						}
						e.soundMeter = o;
						let t = e.peerConnection;
						if (t) {
							let o = e.cameraVideoStream;
							if (e.callSession.audioOnly)
								e.audioSender
									? yield e.audioSender.replaceTrack(s)
									: (e.audioSender = t.addTrack(s, o));
							else if (
								(e.audioSender
									? yield e.audioSender.replaceTrack(s)
									: (e.audioSender = t.addTrack(s, o)),
								console.log(
									'av-c',
									'createMediaSenders -1',
									e.callSession.audioOnly,
									e.callSession.videoMuted,
									o,
									o.getVideoTracks().length
								),
								o && o.getVideoTracks().length > 0 && o.getVideoTracks()[0])
							) {
								let s = b.a.getVideoProfile(p.a.VIDEO_PROFILE);
								if (p.a.SCREEN_SHARING_REPLACE_MODE && e.callSession.isScreenSharing())
									e.setVideoMaxBitrate(s.bitrate);
								else {
									let r = new MediaStream([o.getVideoTracks()[0]]),
										i = o;
									if (
										(e.callSession.rotateAng &&
											e.callSession.sessionCallback.onRotateStream &&
											((i = e.callSession.sessionCallback.onRotateStream(
												r,
												e.callSession.rotateAng
											)),
											console.log(
												'av-c',
												'rotatedStream',
												i ? i.getVideoTracks().length : 'no videoTrack'
											),
											e.callSession.sessionCallback.didRotateLocalVideoTrack &&
												(i.addTrack(e.cameraVideoStream.getAudioTracks()[0]),
												e.callSession.sessionCallback.didRotateLocalVideoTrack(i))),
										e.videoSender
											? (console.log('av-c', 'pc replace video track', i.getVideoTracks()[0]),
											  yield e.videoSender.replaceTrack(i.getVideoTracks()[0]))
											: (console.log('av-c', 'pc add video track', i.getVideoTracks()[0]),
											  (e.videoSender = t.addTrack(i.getVideoTracks()[0]))),
										!p.a.DISABLE_DUAL_STREAM)
									) {
										let s;
										if (
											(console.log('av-c', 'to add small stream'),
											e.callSession.rotateAng && e.callSession.sessionCallback.onRotateStream)
										) {
											let o = e.callSession.sessionCallback.onRotateStream(
												r,
												e.callSession.rotateAng,
												{ width: 200, height: 200 }
											);
											(s = o.getVideoTracks()[0]),
												console.log(
													'av-c',
													'small rotatedStream',
													o ? o.getVideoTracks().length : 'no videoTrack'
												);
										} else s = o.getVideoTracks()[0].clone();
										e.smallVideoSender
											? (console.log('av-c', 'pc small replace video track', s),
											  yield e.smallVideoSender.replaceTrack(s))
											: (console.log('av-c', 'pc small add video track', s),
											  (e.smallVideoSender = t.addTrack(s)));
									}
									e.setVideoMaxBitrate(s.bitrate);
								}
							}
							e.callSession.videoMuted &&
								(console.log('av-c', 'videoMuted, stopVideoTrack'),
								e.stopVideoTrack(e.cameraVideoStream)),
								e.callSession.audioMuted &&
									(console.log('av-c', 'audioMuted, stopAudioTrack'),
									e.stopAudioTrack(e.cameraVideoStream)),
								console.log('av-c', 'Added local stream to pc');
						}
					})();
				}
				publishMedia(e = !1) {
					var s = this;
					return l()(function* () {
						e ||
							((s.peerConnection = s.callSession.createPeerConnection(
								s.callSession.selfUserInfo.uid
							)),
							yield s.createMediaSenders());
						try {
							let o = yield s.peerConnection.createOffer(
								e
									? s.callSession.restartPublishConstrains()
									: s.callSession.defaultPublishMediaConstraints()
							);
							console.log('av-c', 'publishMedia offer, m=video index', o.sdp.indexOf('m=video')),
								yield s.peerConnection.setLocalDescription(o);
							let t = {};
							(t.session_id = s.callSession.sessionId), (t.handle_id = s.handleId);
							let i = { request: 'configure', audio: !0, video: !0 };
							(i.pin = s.callSession.pin), (t.body = i);
							let a = {};
							return (
								(a.sdp = o.sdp),
								(a.type = 'offer'),
								(t.jsep = a),
								new n.a(function (e, o) {
									u.default.sendConferenceRequest(
										s.callSession.sessionId,
										s.callSession.callId,
										'message',
										r()(t),
										function (o, t) {
											if (0 !== o)
												return (
													console.log('av-c', 'conference request, message, error', o), void e()
												);
											let r = JSON.parse(t);
											if (s.callSession.sessionId !== r.session_id)
												return (
													console.log(
														'av-c',
														'conference request, message unknown sessionId',
														s.sessionId,
														r.session_id
													),
													void e()
												);
											let i = r.jsep;
											if (i && 'answer' === i.type)
												s.peerConnection && s.peerConnection.setRemoteDescription(i),
													s.callSession.notifyMuteState();
											else {
												let e = r.plugindata;
												if (e) {
													let o = e.data;
													if (o) {
														o.error_code === m.default.JANUS_VIDEOROOM_ERROR_PUBLISHERS_FULL
															? s.callSession.endCall(f.a.RoomParticipantsFull)
															: s.callSession.endCall(f.a.REASON_SignalError);
													}
												}
											}
											e();
										}
									);
								})
							);
						} catch (e) {
							console.error('av-c', 'publish media error', e),
								s.callSession.endCall(f.a.REASON_MediaError);
						}
					})();
				}
				unPublishMedia() {
					var e = this;
					return l()(function* () {
						if ((e.stopVideoStream(e.cameraVideoStream), e.soundMeter)) {
							try {
								e.soundMeter.stop();
							} catch (e) {}
							e.soundMeter = null;
						}
						e.peerConnection &&
							(e.peerConnection.close(),
							(e.peerConnection = null),
							(e.audioSender = null),
							(e.videoSender = null),
							(e.smallVideoSender = null));
						let s = {};
						return (
							(s.handle_id = e.handleId),
							(s.pin = e.callSession.pin),
							new n.a(function (o, t) {
								u.default.sendConferenceRequest(
									e.callSession.sessionId,
									e.callSession.callId,
									'unpublish',
									r()(s),
									function (s, t) {
										s > 0 && e.callSession.endCall(f.a.REASON_SignalError), o();
									}
								);
							})
						);
					})();
				}
				rotate(e) {
					var s = this;
					return l()(function* () {
						if (
							s.callSession.rotateAng !== e &&
							((s.callSession.rotateAng = e),
							!s.callSession.videoMuted &&
								s.cameraVideoStream &&
								s.cameraVideoStream.getVideoTracks().length > 0 &&
								s.cameraVideoStream.getVideoTracks()[0] &&
								s.callSession.sessionCallback.onRotateStream)
						) {
							let o = new MediaStream([s.cameraVideoStream.getVideoTracks()[0]]),
								t = s.callSession.sessionCallback.onRotateStream(o, e);
							if (
								(s.callSession.sessionCallback.didRotateLocalVideoTrack &&
									s.callSession.sessionCallback.didRotateLocalVideoTrack(
										new MediaStream([
											t.getVideoTracks()[0],
											s.cameraVideoStream.getAudioTracks()[0]
										])
									),
								s.videoSender && s.videoSender.replaceTrack(t.getVideoTracks()[0]),
								s.smallVideoSender)
							) {
								let t = s.callSession.sessionCallback.onRotateStream(o, e, {
									width: 200,
									height: 200
								});
								s.smallVideoSender.replaceTrack(t.getVideoTracks()[0]);
							}
						}
					})();
				}
				endMedia() {
					if (
						(this.peerConnection && (this.peerConnection.close(), (this.peerConnection = null)),
						this.stopVideoStream(this.cameraVideoStream),
						this.soundMeter)
					) {
						try {
							this.soundMeter.stop();
						} catch (e) {}
						this.soundMeter = null;
					}
					(this.cameraVideoStream = null),
						(this.audioSender = null),
						(this.videoSender = null),
						(this.smallVideoSender = null);
				}
				createDummyVideoStream() {
					return l()(function* () {
						const e = document.createElement('video');
						(e.autoplay = !0),
							(e.loop = !0),
							(e.volume = 0.5),
							(e.src = p.a.DUMMY_VIDEO_URI),
							e.load();
						try {
							yield e.play();
						} catch (e) {
							console.log('av-c', 'video auto play error', e);
						}
						let s = null;
						return (
							e.captureStream
								? (s = e.captureStream())
								: e.mozCaptureStream
								? (s = e.mozCaptureStream())
								: (console.log('av-c', 'video captureStream() not supported, fallback to canvas'),
								  (s = document.createElement('canvas').captureStream(60))),
							console.log('av-c', 'createDummyVideoStream', s, s.getVideoTracks().length),
							s
						);
					})();
				}
				tinyVideoTrackConstraints(e) {
					let s = p.a.SMALL_STREAM_WIDTH ? p.a.SMALL_STREAM_WIDTH : 200,
						o = p.a.SMALL_STREAM_HEIGHT ? p.a.SMALL_STREAM_HEIGHT : 200,
						t = { width: { ideal: s, max: s }, height: { ideal: o, max: o } };
					p.a.DISABLE_SMALL_STREAM_LOW_FPS
						? p.a.SMALL_STREAM_FPS &&
						  (t.frameRate = { ideal: p.a.SMALL_STREAM_FPS, max: p.a.SMALL_STREAM_FPS })
						: (t.frameRate = { max: 8, ideal: 8 });
					let r = e.getCapabilities();
					return (
						r.frameRate.max < t.frameRate.max && delete t.frameRate,
						r.height.max < t.height.max && delete t.height,
						r.width.max < t.width.max && delete t.width,
						t
					);
				}
				setVideoMaxBitrate(e) {
					let s = e;
					this.videoSender && this.setVideoSenderMaxBitrate(this.videoSender, s),
						this.smallVideoSender && this.setVideoSenderMaxBitrate(this.smallVideoSender, 160);
				}
				setVideoSenderMaxBitrate(e, s) {
					const o = e.getParameters();
					(o.encodings && 0 !== o.encodings.length) || (o.encodings = [{}]),
						o.encodings.length < 1
							? console.error('av-c', 'setVideoSenderMaxBitrate error: no encodings')
							: ((o.encodings[0].maxBitrate = 1e3 * s),
							  e.setParameters(o).catch(e => {
									console.error('av-c', 'set maxBitrate error', e);
							  }));
				}
				scaleVideoResolutionDownBy(e) {
					console.log('av-c', 'scaleVideoResolutionDownBy', e),
						this.videoSender && this.scaleVideoSenderVideoResolutionDownBy(this.videoSender, e),
						this.smallVideoSender &&
							this.scaleVideoSenderVideoResolutionDownBy(this.smallVideoSender, e);
				}
				scaleVideoSenderVideoResolutionDownBy(e, s) {
					const o = e.getParameters();
					o.encodings || (o.encodings = [{}]),
						o.encodings.length < 1
							? console.log('av-c', 'scaleVideoResolutionDownBy error: no encodings')
							: ((o.encodings[0].scaleResolutionDownBy = s),
							  e.setParameters(o).catch(e => {
									console.log('av-c', 'set scaleVideoResolutionDownBy error', e);
							  }));
				}
				stopVideoTrack(e) {
					console.log('av-c', 'stopVideoTrack'),
						e &&
							e.getTracks().forEach(e => {
								'video' === e.kind && e.stop();
							}),
						this.smallVideoSender &&
							this.smallVideoSender.track &&
							this.smallVideoSender.track.stop();
				}
				stopAudioTrack(e) {
					console.log('av-c', 'stopAudioTrack'),
						e &&
							e.getTracks().forEach(e => {
								'audio' === e.kind && (e.enabled = !1);
							});
				}
				stopVideoStream(e) {
					e &&
						(void 0 === e.getTracks
							? e.stop()
							: e.getTracks().forEach(e => {
									console.log('av-c', 'stop track', e.kind, e.id), e.stop();
							  }),
						this.smallVideoSender &&
							this.smallVideoSender.track &&
							this.smallVideoSender.track.stop());
				}
				restartICE() {
					this.peerConnection &&
						(console.log('av-c', 'restart publish ice'), this.publishMedia(!0));
				}
			}
		},
		'./av/internal/roomErrorCode.js': function (e, s, o) {
			'use strict';
			o.r(s),
				o.d(s, 'default', function () {
					return t;
				});
			class t {}
			(t.JANUS_VIDEOROOM_ERROR_UNKNOWN_ERROR = 499),
				(t.JANUS_VIDEOROOM_ERROR_NO_MESSAGE = 421),
				(t.JANUS_VIDEOROOM_ERROR_INVALID_JSON = 422),
				(t.JANUS_VIDEOROOM_ERROR_INVALID_REQUEST = 423),
				(t.JANUS_VIDEOROOM_ERROR_JOIN_FIRST = 424),
				(t.JANUS_VIDEOROOM_ERROR_ALREADY_JOINED = 425),
				(t.JANUS_VIDEOROOM_ERROR_NO_SUCH_ROOM = 426),
				(t.JANUS_VIDEOROOM_ERROR_ROOM_EXISTS = 427),
				(t.JANUS_VIDEOROOM_ERROR_NO_SUCH_FEED = 428),
				(t.JANUS_VIDEOROOM_ERROR_MISSING_ELEMENT = 429),
				(t.JANUS_VIDEOROOM_ERROR_INVALID_ELEMENT = 430),
				(t.JANUS_VIDEOROOM_ERROR_INVALID_SDP_TYPE = 431),
				(t.JANUS_VIDEOROOM_ERROR_PUBLISHERS_FULL = 432),
				(t.JANUS_VIDEOROOM_ERROR_UNAUTHORIZED = 433),
				(t.JANUS_VIDEOROOM_ERROR_ALREADY_PUBLISHED = 434),
				(t.JANUS_VIDEOROOM_ERROR_NOT_PUBLISHED = 435),
				(t.JANUS_VIDEOROOM_ERROR_ID_EXISTS = 436),
				(t.JANUS_VIDEOROOM_ERROR_INVALID_SDP = 437);
		},
		'./av/internal/screenSharingPublisherImpl.js': function (e, s, o) {
			'use strict';
			o.r(s),
				o.d(s, 'default', function () {
					return b;
				});
			var t = o('./node_modules/babel-runtime/core-js/json/stringify.js'),
				r = o.n(t),
				i = o('./node_modules/babel-runtime/helpers/asyncToGenerator.js'),
				n = o.n(i),
				a = o('../engine/videoProfile'),
				l = o.n(a),
				c = o('../engine/avenginekit'),
				d = o.n(c),
				u = o('./av/internal/publisherImpl.js'),
				m = o('./av/internal/avenginekitImpl.js'),
				h = o('./av/internal/roomErrorCode.js'),
				f = o('../engine/callEndReason'),
				_ = o.n(f),
				p = o('../engine/callState'),
				S = o.n(p);
			class b extends u.default {
				constructor(e) {
					super(e);
				}
				createMediaSenders() {
					var e = this;
					return n()(function* () {
						let s = e.peerConnection,
							o = e.callSession.screenShareStream;
						e.cameraVideoStream = o;
						let t = o;
						if (
							(e.callSession.rotateAng &&
								e.callSession.sessionCallback.onRotateStream &&
								((t = e.callSession.sessionCallback.onRotateStream(
									e.cameraVideoStream,
									e.callSession.rotateAng
								)),
								e.callSession.sessionCallback.didRotateLocalVideoTrack &&
									e.callSession.sessionCallback.didRotateLocalVideoTrack(
										new MediaStream([t.getVideoTracks()[0]])
									)),
							(e.videoSender = s.addTrack(t.getVideoTracks()[0])),
							!d.a.DISABLE_DUAL_STREAM)
						) {
							let t;
							if (e.callSession.rotateAng && e.callSession.sessionCallback.onRotateStream) {
								t = e.callSession.sessionCallback
									.onRotateStream(e.cameraVideoStream, e.callSession.rotateAng, {
										width: 200,
										height: 200
									})
									.getVideoTracks()[0];
							} else t = o.getVideoTracks()[0].clone();
							e.smallVideoSender = s.addTrack(t);
						}
						if (d.a.SCREEN_SHARE_MAX_BITRATE) e.setVideoMaxBitrate(d.a.SCREEN_SHARE_MAX_BITRATE);
						else {
							let s = l.a.getVideoProfile(d.a.VIDEO_PROFILE);
							e.setVideoMaxBitrate(s.bitrate);
						}
					})();
				}
				attachAndPublishScreenSharing() {
					let e = {};
					(e.session_id = this.callSession.sessionId),
						(e.handle_id = this.handleId),
						(e.user_id = this.callSession.screenSharingId()),
						(e.pin = this.callSession.pin),
						this.callSession.callExtra && (e.extra = this.callSession.callExtra),
						m.default.sendConferenceRequest(
							this.callSession.sessionId,
							this.callSession.callId,
							'join_pub_ss',
							r()(e),
							(e, s) => {
								if (0 !== e)
									return void console.log('av-c', 'conference request, join_pub_ss, error', e);
								let o = JSON.parse(s);
								this.handleId = o.handle_id;
								let t = o.data.error_code;
								t > 0
									? t === h.default.JANUS_VIDEOROOM_ERROR_NO_SUCH_ROOM
										? this.callSession.endCall(_.a.RoomNotExist)
										: e === h.default.JANUS_VIDEOROOM_ERROR_ID_EXISTS
										? this.reattachAndPublish()
										: this.callSession.endCall(_.a.REASON_SignalError)
									: this.publishMedia();
							}
						);
				}
				reattachAndPublish() {
					setTimeout(() => {
						this.callSession.static !== S.a.STATUS_IDLE && this.attachAndPublishScreenSharing();
					}, 5e3);
				}
				deattach() {
					let e = {};
					(e.session_id = this.callSession.sessionId),
						(e.handle_id = this.handleId),
						(e.pin = this.callSession.pin),
						m.default.sendConferenceRequest(
							this.callSession.sessionId,
							this.callSession.callId,
							'deattach',
							r()(e),
							(e, s) => {
								0 !== e &&
									(console.log('av-c', 'conference request, ss-deattach, error', e),
									this.callSession.endCall(_.a.REASON_SignalError));
							}
						);
				}
			}
		},
		'./av/internal/soundMeter.js': function (e, s, o) {
			'use strict';
			function t(e) {
				(this.context = e),
					(this.instant = 0),
					(this.slow = 0),
					(this.clip = 0),
					(this.script = e.createScriptProcessor(2048, 1, 1));
				const s = this;
				this.script.onaudioprocess = function (e) {
					const o = e.inputBuffer.getChannelData(0);
					let t,
						r = 0,
						i = 0;
					for (t = 0; t < o.length; ++t) (r += o[t] * o[t]), Math.abs(o[t]) > 0.99 && (i += 1);
					(s.instant = Math.sqrt(r / o.length)),
						(s.slow = 0.95 * s.slow + 0.05 * s.instant),
						(s.clip = i / o.length);
				};
			}
			o.r(s),
				o.d(s, 'SoundMeter', function () {
					return t;
				}),
				(t.prototype.connectToSource = function (e, s) {
					console.log('av-c', 'SoundMeter connecting');
					try {
						(this.mic = this.context.createMediaStreamSource(e)),
							this.mic.connect(this.script),
							this.script.connect(this.context.destination),
							void 0 !== s && s(null);
					} catch (e) {
						console.error('av-c', e), void 0 !== s && s(e);
					}
				}),
				(t.prototype.stop = function () {
					console.log('av-c', 'SoundMeter stopping'),
						this.mic.disconnect(),
						this.script.disconnect();
				});
		},
		'./av/internal/subscriberImpl.js': function (e, s, o) {
			'use strict';
			o.r(s),
				o.d(s, 'default', function () {
					return f;
				});
			var t = o('./node_modules/babel-runtime/helpers/asyncToGenerator.js'),
				r = o.n(t),
				i = o('./node_modules/babel-runtime/core-js/json/stringify.js'),
				n = o.n(i),
				a = o('../engine/subscriber'),
				l = o.n(a),
				c = o('./av/internal/avenginekitImpl.js'),
				d = o('../engine/avenginekit'),
				u = o.n(d),
				m = o('../engine/videoType'),
				h = o.n(m);
			class f extends l.a {
				constructor(e, s) {
					super(),
						(this.tickSinceLastFramesDecoded = 0),
						(this.availableVideoMids = []),
						(this.availableAudioMids = []),
						(this.ready = !1),
						(this.userId = e),
						(this.callSession = s),
						(this.availableVideoMids = []),
						(this.availableAudioMids = []),
						(this.videoType = s.defaultVideoType),
						(this.currentVideoType = this.videoType);
				}
				subscribe() {
					if (this.handleId) return;
					this.ready = !1;
					let e = {};
					(e.feed = this.userId), (e.pin = this.callSession.pin);
					let s = [];
					this.availableAudioMids.length > 0 &&
						s.push({ feed: this.userId, mid: this.availableAudioMids[0] }),
						this.callSession.audioOnly ||
							(this.availableVideoMids.length > 1
								? 0 === this.availableAudioMids.length
									? (console.log('av-c', 'subscribe screen share stream', this.videoType),
									  this.videoType === h.a.BIG_STREAM
											? s.push({ feed: this.userId, mid: this.availableVideoMids[0] })
											: ((this.videoType = h.a.SMALL_STREAM),
											  (this.currentVideoType = h.a.SMALL_STREAM),
											  s.push({ feed: this.userId, mid: this.availableVideoMids[1] })))
									: this.videoType === h.a.BIG_STREAM
									? s.push({ feed: this.userId, mid: this.availableVideoMids[0] })
									: this.videoType === h.a.SMALL_STREAM &&
									  s.push({ feed: this.userId, mid: this.availableVideoMids[1] })
								: 1 === this.availableVideoMids.length &&
								  s.push({ feed: this.userId, mid: this.availableVideoMids[0] })),
						(e.streams = s),
						c.default.sendConferenceRequestEx(
							this.callSession.sessionId,
							this.callSession.callId,
							'join_sub',
							n()(e),
							this.callSession.advance,
							(e, s) => {
								if (0 !== e) return void console.log('av-c', 'join_sub error', e);
								let o = JSON.parse(s),
									t = o.session_id;
								if (t !== this.callSession.sessionId)
									return void console.log(
										'av-c',
										'join response, unknown sessionId',
										this.callSession.sessionId,
										t
									);
								this.handleId = o.handle_id;
								let r = o.jsep;
								console.log('av-c', 'call subscribeMedia', r), this.subscribeMedia(r);
							}
						);
				}
				subscribeMedia(e) {
					var s = this;
					return r()(function* () {
						if ((console.log('av-c', 'subscribeMedia', e), e)) {
							s.peerConnection || (yield s.callSession.createPeerConnection(s.userId));
							try {
								u.a.FORCE_MEDIA_OVER_TCP &&
									((e.sdp = e.sdp.replaceAll(/a=candidate:\d+ \d+ udp.*\r\n?/gm, '')),
									console.log('av-c', 'subscribeMedia force tcp', e.sdp)),
									yield s.peerConnection.setRemoteDescription(e);
								let o = yield s.peerConnection.createAnswer(
									s.callSession.defaultSubscribeMediaConstraints()
								);
								yield s.peerConnection.setLocalDescription(o);
								let t = {};
								(t.session_id = s.callSession.sessionId), (t.handle_id = s.handleId);
								let r = { request: 'start' };
								(r.room = s.callSession.callId), (t.body = r);
								let i = {};
								(i.sdp = o.sdp),
									(i.type = 'answer'),
									(t.jsep = i),
									c.default.sendConferenceRequestEx(
										s.callSession.sessionId,
										s.callSession.callId,
										'message',
										n()(t),
										s.callSession.advance,
										function (e, o) {
											0 === e
												? s.setReady(!0)
												: console.log('av-c', 'conference request, message error', e);
										}
									);
							} catch (e) {
								console.log('av-c', 'subscribeMedia error', s.userId, e);
							}
						}
					})();
				}
				streams(e) {
					e &&
						((this.availableVideoMids.length = 0),
						(this.availableAudioMids.length = 0),
						console.log('av-c', 'streams', this.userId, e),
						e.forEach(e => {
							if ('video' === e.type) {
								let s = e.mid;
								this.availableVideoMids.push(s);
							} else if ('audio' === e.type) {
								let s = e.mid;
								this.availableAudioMids.push(s);
							}
						}));
				}
				subscribeStream(e, s) {
					let o = { handle_id: this.handleId, feed: this.userId };
					e && (o.subscribe = [{ feed: this.userId, mid: e }]),
						s &&
							(this.callSession.getScreenSharingUserId(this.userId)
								? (o.unsubscribe = [{ feed: this.userId }])
								: (o.unsubscribe = [{ feed: this.userId, mid: s }])),
						console.log('av-c', 'update request', o),
						c.default.sendConferenceRequestEx(
							this.callSession.sessionId,
							this.callSession.callId,
							'update',
							n()(o),
							this.callSession.advance,
							(e, s) => {
								if (0 !== e)
									return void console.log('av-c', 'conference request, message error', e);
								console.log('av-c', 'subscribe stream response', o);
								let t = JSON.parse(s).jsep;
								t && this.subscribeMedia(t);
							}
						);
				}
				switchTo(e) {
					let s = { handle_id: this.handleId, feed: this.userId };
					(s.streams = [
						{
							feed: this.userId,
							mid: e,
							sub_mid: this.callSession.getScreenSharingUserId(this.userId) ? '0' : '1'
						}
					]),
						console.log('av-c', 'wfc switch request', s),
						c.default.sendConferenceRequestEx(
							this.callSession.sessionId,
							this.callSession.callId,
							'switch',
							n()(s),
							this.callSession.advance,
							(e, s) => {
								0 !== e
									? console.log('av-c', 'wfc conference switch request, message error', e)
									: this.setReady(!0);
							}
						);
				}
				setReady(e) {
					(this.ready = e), e && this.setVideoType(this.videoType);
				}
				setVideoType(e) {
					if (
						(console.log(
							'av-c',
							'av setVideoType',
							this.userId,
							this.videoType,
							this.currentVideoType,
							e
						),
						(this.videoType !== e || this.currentVideoType !== e) &&
							!this.callSession.audioOnly &&
							0 !== this.availableVideoMids.length &&
							(1 !== this.availableVideoMids.length || e !== h.a.SMALL_STREAM) &&
							((this.videoType = e), this.handleId && this.ready))
					)
						if ((this.setReady(!1), this.currentVideoType !== e))
							if (
								(console.log('av-c', 'av setVideoType to', this.userId, e),
								this.currentVideoType === h.a.NONE)
							) {
								let s =
									e === h.a.BIG_STREAM ? this.availableVideoMids[0] : this.availableVideoMids[1];
								this.subscribeStream(s, null), (this.currentVideoType = e);
							} else if (e === h.a.NONE) {
								let s =
									this.currentVideoType === h.a.BIG_STREAM
										? this.availableVideoMids[0]
										: this.availableVideoMids[1];
								this.subscribeStream(null, s), (this.currentVideoType = e);
							} else {
								let s =
									e === h.a.BIG_STREAM ? this.availableVideoMids[0] : this.availableVideoMids[1];
								(this.currentVideoType = e), this.switchTo(s);
							}
						else this.setVideoType(!0);
				}
				restartICE() {
					if (this.audience || !this.peerConnection) return;
					console.log('av-c', 'restart subscribe ice');
					let e = { handle_id: this.handleId };
					c.default.sendConferenceRequestEx(
						this.callSession.sessionId,
						this.callSession.callId,
						'restart_ice',
						n()(e),
						this.callSession.advance,
						(e, s) => {
							if (0 !== e) return void console.log('av-c', 'restart ice error', e);
							let o = JSON.parse(s).jsep;
							console.log('av-c', 'call subscribeMedia', o), this.subscribeMedia(o);
						}
					);
				}
			}
		},
		'./node_modules/babel-runtime/core-js/json/stringify.js': function (e, s, o) {
			e.exports = {
				default: o('./node_modules/core-js/library/fn/json/stringify.js'),
				__esModule: !0
			};
		},
		'./node_modules/babel-runtime/core-js/map.js': function (e, s, o) {
			e.exports = { default: o('./node_modules/core-js/library/fn/map.js'), __esModule: !0 };
		},
		'./node_modules/babel-runtime/core-js/object/assign.js': function (e, s, o) {
			e.exports = {
				default: o('./node_modules/core-js/library/fn/object/assign.js'),
				__esModule: !0
			};
		},
		'./node_modules/babel-runtime/core-js/object/keys.js': function (e, s, o) {
			e.exports = {
				default: o('./node_modules/core-js/library/fn/object/keys.js'),
				__esModule: !0
			};
		},
		'./node_modules/babel-runtime/core-js/promise.js': function (e, s, o) {
			e.exports = { default: o('./node_modules/core-js/library/fn/promise.js'), __esModule: !0 };
		},
		'./node_modules/babel-runtime/helpers/asyncToGenerator.js': function (e, s, o) {
			'use strict';
			s.__esModule = !0;
			var t,
				r = o('./node_modules/babel-runtime/core-js/promise.js'),
				i = (t = r) && t.__esModule ? t : { default: t };
			s.default = function (e) {
				return function () {
					var s = e.apply(this, arguments);
					return new i.default(function (e, o) {
						return (function t(r, n) {
							try {
								var a = s[r](n),
									l = a.value;
							} catch (e) {
								return void o(e);
							}
							if (!a.done)
								return i.default.resolve(l).then(
									function (e) {
										t('next', e);
									},
									function (e) {
										t('throw', e);
									}
								);
							e(l);
						})('next');
					});
				};
			};
		},
		'./node_modules/babel-runtime/helpers/extends.js': function (e, s, o) {
			'use strict';
			s.__esModule = !0;
			var t,
				r = o('./node_modules/babel-runtime/core-js/object/assign.js'),
				i = (t = r) && t.__esModule ? t : { default: t };
			s.default =
				i.default ||
				function (e) {
					for (var s = 1; s < arguments.length; s++) {
						var o = arguments[s];
						for (var t in o) Object.prototype.hasOwnProperty.call(o, t) && (e[t] = o[t]);
					}
					return e;
				};
		},
		'./node_modules/core-js/library/fn/json/stringify.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_core.js'),
				r = t.JSON || (t.JSON = { stringify: JSON.stringify });
			e.exports = function (e) {
				return r.stringify.apply(r, arguments);
			};
		},
		'./node_modules/core-js/library/fn/map.js': function (e, s, o) {
			o('./node_modules/core-js/library/modules/es6.object.to-string.js'),
				o('./node_modules/core-js/library/modules/es6.string.iterator.js'),
				o('./node_modules/core-js/library/modules/web.dom.iterable.js'),
				o('./node_modules/core-js/library/modules/es6.map.js'),
				o('./node_modules/core-js/library/modules/es7.map.to-json.js'),
				o('./node_modules/core-js/library/modules/es7.map.of.js'),
				o('./node_modules/core-js/library/modules/es7.map.from.js'),
				(e.exports = o('./node_modules/core-js/library/modules/_core.js').Map);
		},
		'./node_modules/core-js/library/fn/object/assign.js': function (e, s, o) {
			o('./node_modules/core-js/library/modules/es6.object.assign.js'),
				(e.exports = o('./node_modules/core-js/library/modules/_core.js').Object.assign);
		},
		'./node_modules/core-js/library/fn/object/keys.js': function (e, s, o) {
			o('./node_modules/core-js/library/modules/es6.object.keys.js'),
				(e.exports = o('./node_modules/core-js/library/modules/_core.js').Object.keys);
		},
		'./node_modules/core-js/library/fn/promise.js': function (e, s, o) {
			o('./node_modules/core-js/library/modules/es6.object.to-string.js'),
				o('./node_modules/core-js/library/modules/es6.string.iterator.js'),
				o('./node_modules/core-js/library/modules/web.dom.iterable.js'),
				o('./node_modules/core-js/library/modules/es6.promise.js'),
				o('./node_modules/core-js/library/modules/es7.promise.finally.js'),
				o('./node_modules/core-js/library/modules/es7.promise.try.js'),
				(e.exports = o('./node_modules/core-js/library/modules/_core.js').Promise);
		},
		'./node_modules/core-js/library/modules/_a-function.js': function (e, s) {
			e.exports = function (e) {
				if ('function' != typeof e) throw TypeError(e + ' is not a function!');
				return e;
			};
		},
		'./node_modules/core-js/library/modules/_add-to-unscopables.js': function (e, s) {
			e.exports = function () {};
		},
		'./node_modules/core-js/library/modules/_an-instance.js': function (e, s) {
			e.exports = function (e, s, o, t) {
				if (!(e instanceof s) || (void 0 !== t && t in e))
					throw TypeError(o + ': incorrect invocation!');
				return e;
			};
		},
		'./node_modules/core-js/library/modules/_an-object.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_is-object.js');
			e.exports = function (e) {
				if (!t(e)) throw TypeError(e + ' is not an object!');
				return e;
			};
		},
		'./node_modules/core-js/library/modules/_array-from-iterable.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_for-of.js');
			e.exports = function (e, s) {
				var o = [];
				return t(e, !1, o.push, o, s), o;
			};
		},
		'./node_modules/core-js/library/modules/_array-includes.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_to-iobject.js'),
				r = o('./node_modules/core-js/library/modules/_to-length.js'),
				i = o('./node_modules/core-js/library/modules/_to-absolute-index.js');
			e.exports = function (e) {
				return function (s, o, n) {
					var a,
						l = t(s),
						c = r(l.length),
						d = i(n, c);
					if (e && o != o) {
						for (; c > d; ) if ((a = l[d++]) != a) return !0;
					} else for (; c > d; d++) if ((e || d in l) && l[d] === o) return e || d || 0;
					return !e && -1;
				};
			};
		},
		'./node_modules/core-js/library/modules/_array-methods.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_ctx.js'),
				r = o('./node_modules/core-js/library/modules/_iobject.js'),
				i = o('./node_modules/core-js/library/modules/_to-object.js'),
				n = o('./node_modules/core-js/library/modules/_to-length.js'),
				a = o('./node_modules/core-js/library/modules/_array-species-create.js');
			e.exports = function (e, s) {
				var o = 1 == e,
					l = 2 == e,
					c = 3 == e,
					d = 4 == e,
					u = 6 == e,
					m = 5 == e || u,
					h = s || a;
				return function (s, a, f) {
					for (
						var _,
							p,
							S = i(s),
							b = r(S),
							g = t(a, f, 3),
							j = n(b.length),
							v = 0,
							y = o ? h(s, j) : l ? h(s, 0) : void 0;
						j > v;
						v++
					)
						if ((m || v in b) && ((p = g((_ = b[v]), v, S)), e))
							if (o) y[v] = p;
							else if (p)
								switch (e) {
									case 3:
										return !0;
									case 5:
										return _;
									case 6:
										return v;
									case 2:
										y.push(_);
								}
							else if (d) return !1;
					return u ? -1 : c || d ? d : y;
				};
			};
		},
		'./node_modules/core-js/library/modules/_array-species-constructor.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_is-object.js'),
				r = o('./node_modules/core-js/library/modules/_is-array.js'),
				i = o('./node_modules/core-js/library/modules/_wks.js')('species');
			e.exports = function (e) {
				var s;
				return (
					r(e) &&
						('function' != typeof (s = e.constructor) ||
							(s !== Array && !r(s.prototype)) ||
							(s = void 0),
						t(s) && null === (s = s[i]) && (s = void 0)),
					void 0 === s ? Array : s
				);
			};
		},
		'./node_modules/core-js/library/modules/_array-species-create.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_array-species-constructor.js');
			e.exports = function (e, s) {
				return new (t(e))(s);
			};
		},
		'./node_modules/core-js/library/modules/_classof.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_cof.js'),
				r = o('./node_modules/core-js/library/modules/_wks.js')('toStringTag'),
				i =
					'Arguments' ==
					t(
						(function () {
							return arguments;
						})()
					);
			e.exports = function (e) {
				var s, o, n;
				return void 0 === e
					? 'Undefined'
					: null === e
					? 'Null'
					: 'string' ==
					  typeof (o = (function (e, s) {
							try {
								return e[s];
							} catch (e) {}
					  })((s = Object(e)), r))
					? o
					: i
					? t(s)
					: 'Object' == (n = t(s)) && 'function' == typeof s.callee
					? 'Arguments'
					: n;
			};
		},
		'./node_modules/core-js/library/modules/_cof.js': function (e, s) {
			var o = {}.toString;
			e.exports = function (e) {
				return o.call(e).slice(8, -1);
			};
		},
		'./node_modules/core-js/library/modules/_collection-strong.js': function (e, s, o) {
			'use strict';
			var t = o('./node_modules/core-js/library/modules/_object-dp.js').f,
				r = o('./node_modules/core-js/library/modules/_object-create.js'),
				i = o('./node_modules/core-js/library/modules/_redefine-all.js'),
				n = o('./node_modules/core-js/library/modules/_ctx.js'),
				a = o('./node_modules/core-js/library/modules/_an-instance.js'),
				l = o('./node_modules/core-js/library/modules/_for-of.js'),
				c = o('./node_modules/core-js/library/modules/_iter-define.js'),
				d = o('./node_modules/core-js/library/modules/_iter-step.js'),
				u = o('./node_modules/core-js/library/modules/_set-species.js'),
				m = o('./node_modules/core-js/library/modules/_descriptors.js'),
				h = o('./node_modules/core-js/library/modules/_meta.js').fastKey,
				f = o('./node_modules/core-js/library/modules/_validate-collection.js'),
				_ = m ? '_s' : 'size',
				p = function (e, s) {
					var o,
						t = h(s);
					if ('F' !== t) return e._i[t];
					for (o = e._f; o; o = o.n) if (o.k == s) return o;
				};
			e.exports = {
				getConstructor: function (e, s, o, c) {
					var d = e(function (e, t) {
						a(e, d, s, '_i'),
							(e._t = s),
							(e._i = r(null)),
							(e._f = void 0),
							(e._l = void 0),
							(e[_] = 0),
							null != t && l(t, o, e[c], e);
					});
					return (
						i(d.prototype, {
							clear: function () {
								for (var e = f(this, s), o = e._i, t = e._f; t; t = t.n)
									(t.r = !0), t.p && (t.p = t.p.n = void 0), delete o[t.i];
								(e._f = e._l = void 0), (e[_] = 0);
							},
							delete: function (e) {
								var o = f(this, s),
									t = p(o, e);
								if (t) {
									var r = t.n,
										i = t.p;
									delete o._i[t.i],
										(t.r = !0),
										i && (i.n = r),
										r && (r.p = i),
										o._f == t && (o._f = r),
										o._l == t && (o._l = i),
										o[_]--;
								}
								return !!t;
							},
							forEach: function (e) {
								f(this, s);
								for (
									var o, t = n(e, arguments.length > 1 ? arguments[1] : void 0, 3);
									(o = o ? o.n : this._f);

								)
									for (t(o.v, o.k, this); o && o.r; ) o = o.p;
							},
							has: function (e) {
								return !!p(f(this, s), e);
							}
						}),
						m &&
							t(d.prototype, 'size', {
								get: function () {
									return f(this, s)[_];
								}
							}),
						d
					);
				},
				def: function (e, s, o) {
					var t,
						r,
						i = p(e, s);
					return (
						i
							? (i.v = o)
							: ((e._l = i = { i: (r = h(s, !0)), k: s, v: o, p: (t = e._l), n: void 0, r: !1 }),
							  e._f || (e._f = i),
							  t && (t.n = i),
							  e[_]++,
							  'F' !== r && (e._i[r] = i)),
						e
					);
				},
				getEntry: p,
				setStrong: function (e, s, o) {
					c(
						e,
						s,
						function (e, o) {
							(this._t = f(e, s)), (this._k = o), (this._l = void 0);
						},
						function () {
							for (var e = this._k, s = this._l; s && s.r; ) s = s.p;
							return this._t && (this._l = s = s ? s.n : this._t._f)
								? d(0, 'keys' == e ? s.k : 'values' == e ? s.v : [s.k, s.v])
								: ((this._t = void 0), d(1));
						},
						o ? 'entries' : 'values',
						!o,
						!0
					),
						u(s);
				}
			};
		},
		'./node_modules/core-js/library/modules/_collection-to-json.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_classof.js'),
				r = o('./node_modules/core-js/library/modules/_array-from-iterable.js');
			e.exports = function (e) {
				return function () {
					if (t(this) != e) throw TypeError(e + "#toJSON isn't generic");
					return r(this);
				};
			};
		},
		'./node_modules/core-js/library/modules/_collection.js': function (e, s, o) {
			'use strict';
			var t = o('./node_modules/core-js/library/modules/_global.js'),
				r = o('./node_modules/core-js/library/modules/_export.js'),
				i = o('./node_modules/core-js/library/modules/_meta.js'),
				n = o('./node_modules/core-js/library/modules/_fails.js'),
				a = o('./node_modules/core-js/library/modules/_hide.js'),
				l = o('./node_modules/core-js/library/modules/_redefine-all.js'),
				c = o('./node_modules/core-js/library/modules/_for-of.js'),
				d = o('./node_modules/core-js/library/modules/_an-instance.js'),
				u = o('./node_modules/core-js/library/modules/_is-object.js'),
				m = o('./node_modules/core-js/library/modules/_set-to-string-tag.js'),
				h = o('./node_modules/core-js/library/modules/_object-dp.js').f,
				f = o('./node_modules/core-js/library/modules/_array-methods.js')(0),
				_ = o('./node_modules/core-js/library/modules/_descriptors.js');
			e.exports = function (e, s, o, p, S, b) {
				var g = t[e],
					j = g,
					v = S ? 'set' : 'add',
					y = j && j.prototype,
					C = {};
				return (
					_ &&
					'function' == typeof j &&
					(b ||
						(y.forEach &&
							!n(function () {
								new j().entries().next();
							})))
						? ((j = s(function (s, o) {
								d(s, j, e, '_c'), (s._c = new g()), null != o && c(o, S, s[v], s);
						  })),
						  f(
								'add,clear,delete,forEach,get,has,set,keys,values,entries,toJSON'.split(','),
								function (e) {
									var s = 'add' == e || 'set' == e;
									e in y &&
										(!b || 'clear' != e) &&
										a(j.prototype, e, function (o, t) {
											if ((d(this, j, e), !s && b && !u(o))) return 'get' == e && void 0;
											var r = this._c[e](0 === o ? 0 : o, t);
											return s ? this : r;
										});
								}
						  ),
						  b ||
								h(j.prototype, 'size', {
									get: function () {
										return this._c.size;
									}
								}))
						: ((j = p.getConstructor(s, e, S, v)), l(j.prototype, o), (i.NEED = !0)),
					m(j, e),
					(C[e] = j),
					r(r.G + r.W + r.F, C),
					b || p.setStrong(j, e, S),
					j
				);
			};
		},
		'./node_modules/core-js/library/modules/_core.js': function (e, s) {
			var o = (e.exports = { version: '2.6.10' });
			'number' == typeof __e && (__e = o);
		},
		'./node_modules/core-js/library/modules/_ctx.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_a-function.js');
			e.exports = function (e, s, o) {
				if ((t(e), void 0 === s)) return e;
				switch (o) {
					case 1:
						return function (o) {
							return e.call(s, o);
						};
					case 2:
						return function (o, t) {
							return e.call(s, o, t);
						};
					case 3:
						return function (o, t, r) {
							return e.call(s, o, t, r);
						};
				}
				return function () {
					return e.apply(s, arguments);
				};
			};
		},
		'./node_modules/core-js/library/modules/_defined.js': function (e, s) {
			e.exports = function (e) {
				if (null == e) throw TypeError("Can't call method on  " + e);
				return e;
			};
		},
		'./node_modules/core-js/library/modules/_descriptors.js': function (e, s, o) {
			e.exports = !o('./node_modules/core-js/library/modules/_fails.js')(function () {
				return (
					7 !=
					Object.defineProperty({}, 'a', {
						get: function () {
							return 7;
						}
					}).a
				);
			});
		},
		'./node_modules/core-js/library/modules/_dom-create.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_is-object.js'),
				r = o('./node_modules/core-js/library/modules/_global.js').document,
				i = t(r) && t(r.createElement);
			e.exports = function (e) {
				return i ? r.createElement(e) : {};
			};
		},
		'./node_modules/core-js/library/modules/_enum-bug-keys.js': function (e, s) {
			e.exports =
				'constructor,hasOwnProperty,isPrototypeOf,propertyIsEnumerable,toLocaleString,toString,valueOf'.split(
					','
				);
		},
		'./node_modules/core-js/library/modules/_export.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_global.js'),
				r = o('./node_modules/core-js/library/modules/_core.js'),
				i = o('./node_modules/core-js/library/modules/_ctx.js'),
				n = o('./node_modules/core-js/library/modules/_hide.js'),
				a = o('./node_modules/core-js/library/modules/_has.js'),
				l = function (e, s, o) {
					var c,
						d,
						u,
						m = e & l.F,
						h = e & l.G,
						f = e & l.S,
						_ = e & l.P,
						p = e & l.B,
						S = e & l.W,
						b = h ? r : r[s] || (r[s] = {}),
						g = b.prototype,
						j = h ? t : f ? t[s] : (t[s] || {}).prototype;
					for (c in (h && (o = s), o))
						((d = !m && j && void 0 !== j[c]) && a(b, c)) ||
							((u = d ? j[c] : o[c]),
							(b[c] =
								h && 'function' != typeof j[c]
									? o[c]
									: p && d
									? i(u, t)
									: S && j[c] == u
									? (function (e) {
											var s = function (s, o, t) {
												if (this instanceof e) {
													switch (arguments.length) {
														case 0:
															return new e();
														case 1:
															return new e(s);
														case 2:
															return new e(s, o);
													}
													return new e(s, o, t);
												}
												return e.apply(this, arguments);
											};
											return (s.prototype = e.prototype), s;
									  })(u)
									: _ && 'function' == typeof u
									? i(Function.call, u)
									: u),
							_ && (((b.virtual || (b.virtual = {}))[c] = u), e & l.R && g && !g[c] && n(g, c, u)));
				};
			(l.F = 1),
				(l.G = 2),
				(l.S = 4),
				(l.P = 8),
				(l.B = 16),
				(l.W = 32),
				(l.U = 64),
				(l.R = 128),
				(e.exports = l);
		},
		'./node_modules/core-js/library/modules/_fails.js': function (e, s) {
			e.exports = function (e) {
				try {
					return !!e();
				} catch (e) {
					return !0;
				}
			};
		},
		'./node_modules/core-js/library/modules/_for-of.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_ctx.js'),
				r = o('./node_modules/core-js/library/modules/_iter-call.js'),
				i = o('./node_modules/core-js/library/modules/_is-array-iter.js'),
				n = o('./node_modules/core-js/library/modules/_an-object.js'),
				a = o('./node_modules/core-js/library/modules/_to-length.js'),
				l = o('./node_modules/core-js/library/modules/core.get-iterator-method.js'),
				c = {},
				d = {};
			((s = e.exports =
				function (e, s, o, u, m) {
					var h,
						f,
						_,
						p,
						S = m
							? function () {
									return e;
							  }
							: l(e),
						b = t(o, u, s ? 2 : 1),
						g = 0;
					if ('function' != typeof S) throw TypeError(e + ' is not iterable!');
					if (i(S)) {
						for (h = a(e.length); h > g; g++)
							if ((p = s ? b(n((f = e[g]))[0], f[1]) : b(e[g])) === c || p === d) return p;
					} else
						for (_ = S.call(e); !(f = _.next()).done; )
							if ((p = r(_, b, f.value, s)) === c || p === d) return p;
				}).BREAK = c),
				(s.RETURN = d);
		},
		'./node_modules/core-js/library/modules/_global.js': function (e, s) {
			var o = (e.exports =
				'undefined' != typeof window && window.Math == Math
					? window
					: 'undefined' != typeof self && self.Math == Math
					? self
					: Function('return this')());
			'number' == typeof __g && (__g = o);
		},
		'./node_modules/core-js/library/modules/_has.js': function (e, s) {
			var o = {}.hasOwnProperty;
			e.exports = function (e, s) {
				return o.call(e, s);
			};
		},
		'./node_modules/core-js/library/modules/_hide.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_object-dp.js'),
				r = o('./node_modules/core-js/library/modules/_property-desc.js');
			e.exports = o('./node_modules/core-js/library/modules/_descriptors.js')
				? function (e, s, o) {
						return t.f(e, s, r(1, o));
				  }
				: function (e, s, o) {
						return (e[s] = o), e;
				  };
		},
		'./node_modules/core-js/library/modules/_html.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_global.js').document;
			e.exports = t && t.documentElement;
		},
		'./node_modules/core-js/library/modules/_ie8-dom-define.js': function (e, s, o) {
			e.exports =
				!o('./node_modules/core-js/library/modules/_descriptors.js') &&
				!o('./node_modules/core-js/library/modules/_fails.js')(function () {
					return (
						7 !=
						Object.defineProperty(
							o('./node_modules/core-js/library/modules/_dom-create.js')('div'),
							'a',
							{
								get: function () {
									return 7;
								}
							}
						).a
					);
				});
		},
		'./node_modules/core-js/library/modules/_invoke.js': function (e, s) {
			e.exports = function (e, s, o) {
				var t = void 0 === o;
				switch (s.length) {
					case 0:
						return t ? e() : e.call(o);
					case 1:
						return t ? e(s[0]) : e.call(o, s[0]);
					case 2:
						return t ? e(s[0], s[1]) : e.call(o, s[0], s[1]);
					case 3:
						return t ? e(s[0], s[1], s[2]) : e.call(o, s[0], s[1], s[2]);
					case 4:
						return t ? e(s[0], s[1], s[2], s[3]) : e.call(o, s[0], s[1], s[2], s[3]);
				}
				return e.apply(o, s);
			};
		},
		'./node_modules/core-js/library/modules/_iobject.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_cof.js');
			e.exports = Object('z').propertyIsEnumerable(0)
				? Object
				: function (e) {
						return 'String' == t(e) ? e.split('') : Object(e);
				  };
		},
		'./node_modules/core-js/library/modules/_is-array-iter.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_iterators.js'),
				r = o('./node_modules/core-js/library/modules/_wks.js')('iterator'),
				i = Array.prototype;
			e.exports = function (e) {
				return void 0 !== e && (t.Array === e || i[r] === e);
			};
		},
		'./node_modules/core-js/library/modules/_is-array.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_cof.js');
			e.exports =
				Array.isArray ||
				function (e) {
					return 'Array' == t(e);
				};
		},
		'./node_modules/core-js/library/modules/_is-object.js': function (e, s) {
			e.exports = function (e) {
				return 'object' == typeof e ? null !== e : 'function' == typeof e;
			};
		},
		'./node_modules/core-js/library/modules/_iter-call.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_an-object.js');
			e.exports = function (e, s, o, r) {
				try {
					return r ? s(t(o)[0], o[1]) : s(o);
				} catch (s) {
					var i = e.return;
					throw (void 0 !== i && t(i.call(e)), s);
				}
			};
		},
		'./node_modules/core-js/library/modules/_iter-create.js': function (e, s, o) {
			'use strict';
			var t = o('./node_modules/core-js/library/modules/_object-create.js'),
				r = o('./node_modules/core-js/library/modules/_property-desc.js'),
				i = o('./node_modules/core-js/library/modules/_set-to-string-tag.js'),
				n = {};
			o('./node_modules/core-js/library/modules/_hide.js')(
				n,
				o('./node_modules/core-js/library/modules/_wks.js')('iterator'),
				function () {
					return this;
				}
			),
				(e.exports = function (e, s, o) {
					(e.prototype = t(n, { next: r(1, o) })), i(e, s + ' Iterator');
				});
		},
		'./node_modules/core-js/library/modules/_iter-define.js': function (e, s, o) {
			'use strict';
			var t = o('./node_modules/core-js/library/modules/_library.js'),
				r = o('./node_modules/core-js/library/modules/_export.js'),
				i = o('./node_modules/core-js/library/modules/_redefine.js'),
				n = o('./node_modules/core-js/library/modules/_hide.js'),
				a = o('./node_modules/core-js/library/modules/_iterators.js'),
				l = o('./node_modules/core-js/library/modules/_iter-create.js'),
				c = o('./node_modules/core-js/library/modules/_set-to-string-tag.js'),
				d = o('./node_modules/core-js/library/modules/_object-gpo.js'),
				u = o('./node_modules/core-js/library/modules/_wks.js')('iterator'),
				m = !([].keys && 'next' in [].keys()),
				h = function () {
					return this;
				};
			e.exports = function (e, s, o, f, _, p, S) {
				l(o, s, f);
				var b,
					g,
					j,
					v = function (e) {
						if (!m && e in I) return I[e];
						switch (e) {
							case 'keys':
							case 'values':
								return function () {
									return new o(this, e);
								};
						}
						return function () {
							return new o(this, e);
						};
					},
					y = s + ' Iterator',
					C = 'values' == _,
					T = !1,
					I = e.prototype,
					M = I[u] || I['@@iterator'] || (_ && I[_]),
					E = M || v(_),
					O = _ ? (C ? v('entries') : E) : void 0,
					R = ('Array' == s && I.entries) || M;
				if (
					(R &&
						(j = d(R.call(new e()))) !== Object.prototype &&
						j.next &&
						(c(j, y, !0), t || 'function' == typeof j[u] || n(j, u, h)),
					C &&
						M &&
						'values' !== M.name &&
						((T = !0),
						(E = function () {
							return M.call(this);
						})),
					(t && !S) || (!m && !T && I[u]) || n(I, u, E),
					(a[s] = E),
					(a[y] = h),
					_)
				)
					if (((b = { values: C ? E : v('values'), keys: p ? E : v('keys'), entries: O }), S))
						for (g in b) g in I || i(I, g, b[g]);
					else r(r.P + r.F * (m || T), s, b);
				return b;
			};
		},
		'./node_modules/core-js/library/modules/_iter-detect.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_wks.js')('iterator'),
				r = !1;
			try {
				var i = [7][t]();
				(i.return = function () {
					r = !0;
				}),
					Array.from(i, function () {
						throw 2;
					});
			} catch (e) {}
			e.exports = function (e, s) {
				if (!s && !r) return !1;
				var o = !1;
				try {
					var i = [7],
						n = i[t]();
					(n.next = function () {
						return { done: (o = !0) };
					}),
						(i[t] = function () {
							return n;
						}),
						e(i);
				} catch (e) {}
				return o;
			};
		},
		'./node_modules/core-js/library/modules/_iter-step.js': function (e, s) {
			e.exports = function (e, s) {
				return { value: s, done: !!e };
			};
		},
		'./node_modules/core-js/library/modules/_iterators.js': function (e, s) {
			e.exports = {};
		},
		'./node_modules/core-js/library/modules/_library.js': function (e, s) {
			e.exports = !0;
		},
		'./node_modules/core-js/library/modules/_meta.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_uid.js')('meta'),
				r = o('./node_modules/core-js/library/modules/_is-object.js'),
				i = o('./node_modules/core-js/library/modules/_has.js'),
				n = o('./node_modules/core-js/library/modules/_object-dp.js').f,
				a = 0,
				l =
					Object.isExtensible ||
					function () {
						return !0;
					},
				c = !o('./node_modules/core-js/library/modules/_fails.js')(function () {
					return l(Object.preventExtensions({}));
				}),
				d = function (e) {
					n(e, t, { value: { i: 'O' + ++a, w: {} } });
				},
				u = (e.exports = {
					KEY: t,
					NEED: !1,
					fastKey: function (e, s) {
						if (!r(e)) return 'symbol' == typeof e ? e : ('string' == typeof e ? 'S' : 'P') + e;
						if (!i(e, t)) {
							if (!l(e)) return 'F';
							if (!s) return 'E';
							d(e);
						}
						return e[t].i;
					},
					getWeak: function (e, s) {
						if (!i(e, t)) {
							if (!l(e)) return !0;
							if (!s) return !1;
							d(e);
						}
						return e[t].w;
					},
					onFreeze: function (e) {
						return c && u.NEED && l(e) && !i(e, t) && d(e), e;
					}
				});
		},
		'./node_modules/core-js/library/modules/_microtask.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_global.js'),
				r = o('./node_modules/core-js/library/modules/_task.js').set,
				i = t.MutationObserver || t.WebKitMutationObserver,
				n = t.process,
				a = t.Promise,
				l = 'process' == o('./node_modules/core-js/library/modules/_cof.js')(n);
			e.exports = function () {
				var e,
					s,
					o,
					c = function () {
						var t, r;
						for (l && (t = n.domain) && t.exit(); e; ) {
							(r = e.fn), (e = e.next);
							try {
								r();
							} catch (t) {
								throw (e ? o() : (s = void 0), t);
							}
						}
						(s = void 0), t && t.enter();
					};
				if (l)
					o = function () {
						n.nextTick(c);
					};
				else if (!i || (t.navigator && t.navigator.standalone))
					if (a && a.resolve) {
						var d = a.resolve(void 0);
						o = function () {
							d.then(c);
						};
					} else
						o = function () {
							r.call(t, c);
						};
				else {
					var u = !0,
						m = document.createTextNode('');
					new i(c).observe(m, { characterData: !0 }),
						(o = function () {
							m.data = u = !u;
						});
				}
				return function (t) {
					var r = { fn: t, next: void 0 };
					s && (s.next = r), e || ((e = r), o()), (s = r);
				};
			};
		},
		'./node_modules/core-js/library/modules/_new-promise-capability.js': function (e, s, o) {
			'use strict';
			var t = o('./node_modules/core-js/library/modules/_a-function.js');
			function r(e) {
				var s, o;
				(this.promise = new e(function (e, t) {
					if (void 0 !== s || void 0 !== o) throw TypeError('Bad Promise constructor');
					(s = e), (o = t);
				})),
					(this.resolve = t(s)),
					(this.reject = t(o));
			}
			e.exports.f = function (e) {
				return new r(e);
			};
		},
		'./node_modules/core-js/library/modules/_object-assign.js': function (e, s, o) {
			'use strict';
			var t = o('./node_modules/core-js/library/modules/_descriptors.js'),
				r = o('./node_modules/core-js/library/modules/_object-keys.js'),
				i = o('./node_modules/core-js/library/modules/_object-gops.js'),
				n = o('./node_modules/core-js/library/modules/_object-pie.js'),
				a = o('./node_modules/core-js/library/modules/_to-object.js'),
				l = o('./node_modules/core-js/library/modules/_iobject.js'),
				c = Object.assign;
			e.exports =
				!c ||
				o('./node_modules/core-js/library/modules/_fails.js')(function () {
					var e = {},
						s = {},
						o = Symbol(),
						t = 'abcdefghijklmnopqrst';
					return (
						(e[o] = 7),
						t.split('').forEach(function (e) {
							s[e] = e;
						}),
						7 != c({}, e)[o] || Object.keys(c({}, s)).join('') != t
					);
				})
					? function (e, s) {
							for (var o = a(e), c = arguments.length, d = 1, u = i.f, m = n.f; c > d; )
								for (
									var h,
										f = l(arguments[d++]),
										_ = u ? r(f).concat(u(f)) : r(f),
										p = _.length,
										S = 0;
									p > S;

								)
									(h = _[S++]), (t && !m.call(f, h)) || (o[h] = f[h]);
							return o;
					  }
					: c;
		},
		'./node_modules/core-js/library/modules/_object-create.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_an-object.js'),
				r = o('./node_modules/core-js/library/modules/_object-dps.js'),
				i = o('./node_modules/core-js/library/modules/_enum-bug-keys.js'),
				n = o('./node_modules/core-js/library/modules/_shared-key.js')('IE_PROTO'),
				a = function () {},
				l = function () {
					var e,
						s = o('./node_modules/core-js/library/modules/_dom-create.js')('iframe'),
						t = i.length;
					for (
						s.style.display = 'none',
							o('./node_modules/core-js/library/modules/_html.js').appendChild(s),
							s.src = 'javascript:',
							(e = s.contentWindow.document).open(),
							e.write('<script>document.F=Object</script>'),
							e.close(),
							l = e.F;
						t--;

					)
						delete l.prototype[i[t]];
					return l();
				};
			e.exports =
				Object.create ||
				function (e, s) {
					var o;
					return (
						null !== e
							? ((a.prototype = t(e)), (o = new a()), (a.prototype = null), (o[n] = e))
							: (o = l()),
						void 0 === s ? o : r(o, s)
					);
				};
		},
		'./node_modules/core-js/library/modules/_object-dp.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_an-object.js'),
				r = o('./node_modules/core-js/library/modules/_ie8-dom-define.js'),
				i = o('./node_modules/core-js/library/modules/_to-primitive.js'),
				n = Object.defineProperty;
			s.f = o('./node_modules/core-js/library/modules/_descriptors.js')
				? Object.defineProperty
				: function (e, s, o) {
						if ((t(e), (s = i(s, !0)), t(o), r))
							try {
								return n(e, s, o);
							} catch (e) {}
						if ('get' in o || 'set' in o) throw TypeError('Accessors not supported!');
						return 'value' in o && (e[s] = o.value), e;
				  };
		},
		'./node_modules/core-js/library/modules/_object-dps.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_object-dp.js'),
				r = o('./node_modules/core-js/library/modules/_an-object.js'),
				i = o('./node_modules/core-js/library/modules/_object-keys.js');
			e.exports = o('./node_modules/core-js/library/modules/_descriptors.js')
				? Object.defineProperties
				: function (e, s) {
						r(e);
						for (var o, n = i(s), a = n.length, l = 0; a > l; ) t.f(e, (o = n[l++]), s[o]);
						return e;
				  };
		},
		'./node_modules/core-js/library/modules/_object-gops.js': function (e, s) {
			s.f = Object.getOwnPropertySymbols;
		},
		'./node_modules/core-js/library/modules/_object-gpo.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_has.js'),
				r = o('./node_modules/core-js/library/modules/_to-object.js'),
				i = o('./node_modules/core-js/library/modules/_shared-key.js')('IE_PROTO'),
				n = Object.prototype;
			e.exports =
				Object.getPrototypeOf ||
				function (e) {
					return (
						(e = r(e)),
						t(e, i)
							? e[i]
							: 'function' == typeof e.constructor && e instanceof e.constructor
							? e.constructor.prototype
							: e instanceof Object
							? n
							: null
					);
				};
		},
		'./node_modules/core-js/library/modules/_object-keys-internal.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_has.js'),
				r = o('./node_modules/core-js/library/modules/_to-iobject.js'),
				i = o('./node_modules/core-js/library/modules/_array-includes.js')(!1),
				n = o('./node_modules/core-js/library/modules/_shared-key.js')('IE_PROTO');
			e.exports = function (e, s) {
				var o,
					a = r(e),
					l = 0,
					c = [];
				for (o in a) o != n && t(a, o) && c.push(o);
				for (; s.length > l; ) t(a, (o = s[l++])) && (~i(c, o) || c.push(o));
				return c;
			};
		},
		'./node_modules/core-js/library/modules/_object-keys.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_object-keys-internal.js'),
				r = o('./node_modules/core-js/library/modules/_enum-bug-keys.js');
			e.exports =
				Object.keys ||
				function (e) {
					return t(e, r);
				};
		},
		'./node_modules/core-js/library/modules/_object-pie.js': function (e, s) {
			s.f = {}.propertyIsEnumerable;
		},
		'./node_modules/core-js/library/modules/_object-sap.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_export.js'),
				r = o('./node_modules/core-js/library/modules/_core.js'),
				i = o('./node_modules/core-js/library/modules/_fails.js');
			e.exports = function (e, s) {
				var o = (r.Object || {})[e] || Object[e],
					n = {};
				(n[e] = s(o)),
					t(
						t.S +
							t.F *
								i(function () {
									o(1);
								}),
						'Object',
						n
					);
			};
		},
		'./node_modules/core-js/library/modules/_perform.js': function (e, s) {
			e.exports = function (e) {
				try {
					return { e: !1, v: e() };
				} catch (e) {
					return { e: !0, v: e };
				}
			};
		},
		'./node_modules/core-js/library/modules/_promise-resolve.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_an-object.js'),
				r = o('./node_modules/core-js/library/modules/_is-object.js'),
				i = o('./node_modules/core-js/library/modules/_new-promise-capability.js');
			e.exports = function (e, s) {
				if ((t(e), r(s) && s.constructor === e)) return s;
				var o = i.f(e);
				return (0, o.resolve)(s), o.promise;
			};
		},
		'./node_modules/core-js/library/modules/_property-desc.js': function (e, s) {
			e.exports = function (e, s) {
				return { enumerable: !(1 & e), configurable: !(2 & e), writable: !(4 & e), value: s };
			};
		},
		'./node_modules/core-js/library/modules/_redefine-all.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_hide.js');
			e.exports = function (e, s, o) {
				for (var r in s) o && e[r] ? (e[r] = s[r]) : t(e, r, s[r]);
				return e;
			};
		},
		'./node_modules/core-js/library/modules/_redefine.js': function (e, s, o) {
			e.exports = o('./node_modules/core-js/library/modules/_hide.js');
		},
		'./node_modules/core-js/library/modules/_set-collection-from.js': function (e, s, o) {
			'use strict';
			var t = o('./node_modules/core-js/library/modules/_export.js'),
				r = o('./node_modules/core-js/library/modules/_a-function.js'),
				i = o('./node_modules/core-js/library/modules/_ctx.js'),
				n = o('./node_modules/core-js/library/modules/_for-of.js');
			e.exports = function (e) {
				t(t.S, e, {
					from: function (e) {
						var s,
							o,
							t,
							a,
							l = arguments[1];
						return (
							r(this),
							(s = void 0 !== l) && r(l),
							null == e
								? new this()
								: ((o = []),
								  s
										? ((t = 0),
										  (a = i(l, arguments[2], 2)),
										  n(e, !1, function (e) {
												o.push(a(e, t++));
										  }))
										: n(e, !1, o.push, o),
								  new this(o))
						);
					}
				});
			};
		},
		'./node_modules/core-js/library/modules/_set-collection-of.js': function (e, s, o) {
			'use strict';
			var t = o('./node_modules/core-js/library/modules/_export.js');
			e.exports = function (e) {
				t(t.S, e, {
					of: function () {
						for (var e = arguments.length, s = new Array(e); e--; ) s[e] = arguments[e];
						return new this(s);
					}
				});
			};
		},
		'./node_modules/core-js/library/modules/_set-species.js': function (e, s, o) {
			'use strict';
			var t = o('./node_modules/core-js/library/modules/_global.js'),
				r = o('./node_modules/core-js/library/modules/_core.js'),
				i = o('./node_modules/core-js/library/modules/_object-dp.js'),
				n = o('./node_modules/core-js/library/modules/_descriptors.js'),
				a = o('./node_modules/core-js/library/modules/_wks.js')('species');
			e.exports = function (e) {
				var s = 'function' == typeof r[e] ? r[e] : t[e];
				n &&
					s &&
					!s[a] &&
					i.f(s, a, {
						configurable: !0,
						get: function () {
							return this;
						}
					});
			};
		},
		'./node_modules/core-js/library/modules/_set-to-string-tag.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_object-dp.js').f,
				r = o('./node_modules/core-js/library/modules/_has.js'),
				i = o('./node_modules/core-js/library/modules/_wks.js')('toStringTag');
			e.exports = function (e, s, o) {
				e && !r((e = o ? e : e.prototype), i) && t(e, i, { configurable: !0, value: s });
			};
		},
		'./node_modules/core-js/library/modules/_shared-key.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_shared.js')('keys'),
				r = o('./node_modules/core-js/library/modules/_uid.js');
			e.exports = function (e) {
				return t[e] || (t[e] = r(e));
			};
		},
		'./node_modules/core-js/library/modules/_shared.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_core.js'),
				r = o('./node_modules/core-js/library/modules/_global.js'),
				i = r['__core-js_shared__'] || (r['__core-js_shared__'] = {});
			(e.exports = function (e, s) {
				return i[e] || (i[e] = void 0 !== s ? s : {});
			})('versions', []).push({
				version: t.version,
				mode: o('./node_modules/core-js/library/modules/_library.js') ? 'pure' : 'global',
				copyright: '© 2019 Denis Pushkarev (zloirock.ru)'
			});
		},
		'./node_modules/core-js/library/modules/_species-constructor.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_an-object.js'),
				r = o('./node_modules/core-js/library/modules/_a-function.js'),
				i = o('./node_modules/core-js/library/modules/_wks.js')('species');
			e.exports = function (e, s) {
				var o,
					n = t(e).constructor;
				return void 0 === n || null == (o = t(n)[i]) ? s : r(o);
			};
		},
		'./node_modules/core-js/library/modules/_string-at.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_to-integer.js'),
				r = o('./node_modules/core-js/library/modules/_defined.js');
			e.exports = function (e) {
				return function (s, o) {
					var i,
						n,
						a = String(r(s)),
						l = t(o),
						c = a.length;
					return l < 0 || l >= c
						? e
							? ''
							: void 0
						: (i = a.charCodeAt(l)) < 55296 ||
						  i > 56319 ||
						  l + 1 === c ||
						  (n = a.charCodeAt(l + 1)) < 56320 ||
						  n > 57343
						? e
							? a.charAt(l)
							: i
						: e
						? a.slice(l, l + 2)
						: n - 56320 + ((i - 55296) << 10) + 65536;
				};
			};
		},
		'./node_modules/core-js/library/modules/_task.js': function (e, s, o) {
			var t,
				r,
				i,
				n = o('./node_modules/core-js/library/modules/_ctx.js'),
				a = o('./node_modules/core-js/library/modules/_invoke.js'),
				l = o('./node_modules/core-js/library/modules/_html.js'),
				c = o('./node_modules/core-js/library/modules/_dom-create.js'),
				d = o('./node_modules/core-js/library/modules/_global.js'),
				u = d.process,
				m = d.setImmediate,
				h = d.clearImmediate,
				f = d.MessageChannel,
				_ = d.Dispatch,
				p = 0,
				S = {},
				b = function () {
					var e = +this;
					if (S.hasOwnProperty(e)) {
						var s = S[e];
						delete S[e], s();
					}
				},
				g = function (e) {
					b.call(e.data);
				};
			(m && h) ||
				((m = function (e) {
					for (var s = [], o = 1; arguments.length > o; ) s.push(arguments[o++]);
					return (
						(S[++p] = function () {
							a('function' == typeof e ? e : Function(e), s);
						}),
						t(p),
						p
					);
				}),
				(h = function (e) {
					delete S[e];
				}),
				'process' == o('./node_modules/core-js/library/modules/_cof.js')(u)
					? (t = function (e) {
							u.nextTick(n(b, e, 1));
					  })
					: _ && _.now
					? (t = function (e) {
							_.now(n(b, e, 1));
					  })
					: f
					? ((i = (r = new f()).port2), (r.port1.onmessage = g), (t = n(i.postMessage, i, 1)))
					: d.addEventListener && 'function' == typeof postMessage && !d.importScripts
					? ((t = function (e) {
							d.postMessage(e + '', '*');
					  }),
					  d.addEventListener('message', g, !1))
					: (t =
							'onreadystatechange' in c('script')
								? function (e) {
										l.appendChild(c('script')).onreadystatechange = function () {
											l.removeChild(this), b.call(e);
										};
								  }
								: function (e) {
										setTimeout(n(b, e, 1), 0);
								  })),
				(e.exports = { set: m, clear: h });
		},
		'./node_modules/core-js/library/modules/_to-absolute-index.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_to-integer.js'),
				r = Math.max,
				i = Math.min;
			e.exports = function (e, s) {
				return (e = t(e)) < 0 ? r(e + s, 0) : i(e, s);
			};
		},
		'./node_modules/core-js/library/modules/_to-integer.js': function (e, s) {
			var o = Math.ceil,
				t = Math.floor;
			e.exports = function (e) {
				return isNaN((e = +e)) ? 0 : (e > 0 ? t : o)(e);
			};
		},
		'./node_modules/core-js/library/modules/_to-iobject.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_iobject.js'),
				r = o('./node_modules/core-js/library/modules/_defined.js');
			e.exports = function (e) {
				return t(r(e));
			};
		},
		'./node_modules/core-js/library/modules/_to-length.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_to-integer.js'),
				r = Math.min;
			e.exports = function (e) {
				return e > 0 ? r(t(e), 9007199254740991) : 0;
			};
		},
		'./node_modules/core-js/library/modules/_to-object.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_defined.js');
			e.exports = function (e) {
				return Object(t(e));
			};
		},
		'./node_modules/core-js/library/modules/_to-primitive.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_is-object.js');
			e.exports = function (e, s) {
				if (!t(e)) return e;
				var o, r;
				if (s && 'function' == typeof (o = e.toString) && !t((r = o.call(e)))) return r;
				if ('function' == typeof (o = e.valueOf) && !t((r = o.call(e)))) return r;
				if (!s && 'function' == typeof (o = e.toString) && !t((r = o.call(e)))) return r;
				throw TypeError("Can't convert object to primitive value");
			};
		},
		'./node_modules/core-js/library/modules/_uid.js': function (e, s) {
			var o = 0,
				t = Math.random();
			e.exports = function (e) {
				return 'Symbol('.concat(void 0 === e ? '' : e, ')_', (++o + t).toString(36));
			};
		},
		'./node_modules/core-js/library/modules/_user-agent.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_global.js').navigator;
			e.exports = (t && t.userAgent) || '';
		},
		'./node_modules/core-js/library/modules/_validate-collection.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_is-object.js');
			e.exports = function (e, s) {
				if (!t(e) || e._t !== s) throw TypeError('Incompatible receiver, ' + s + ' required!');
				return e;
			};
		},
		'./node_modules/core-js/library/modules/_wks.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_shared.js')('wks'),
				r = o('./node_modules/core-js/library/modules/_uid.js'),
				i = o('./node_modules/core-js/library/modules/_global.js').Symbol,
				n = 'function' == typeof i;
			(e.exports = function (e) {
				return t[e] || (t[e] = (n && i[e]) || (n ? i : r)('Symbol.' + e));
			}).store = t;
		},
		'./node_modules/core-js/library/modules/core.get-iterator-method.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_classof.js'),
				r = o('./node_modules/core-js/library/modules/_wks.js')('iterator'),
				i = o('./node_modules/core-js/library/modules/_iterators.js');
			e.exports = o('./node_modules/core-js/library/modules/_core.js').getIteratorMethod =
				function (e) {
					if (null != e) return e[r] || e['@@iterator'] || i[t(e)];
				};
		},
		'./node_modules/core-js/library/modules/es6.array.iterator.js': function (e, s, o) {
			'use strict';
			var t = o('./node_modules/core-js/library/modules/_add-to-unscopables.js'),
				r = o('./node_modules/core-js/library/modules/_iter-step.js'),
				i = o('./node_modules/core-js/library/modules/_iterators.js'),
				n = o('./node_modules/core-js/library/modules/_to-iobject.js');
			(e.exports = o('./node_modules/core-js/library/modules/_iter-define.js')(
				Array,
				'Array',
				function (e, s) {
					(this._t = n(e)), (this._i = 0), (this._k = s);
				},
				function () {
					var e = this._t,
						s = this._k,
						o = this._i++;
					return !e || o >= e.length
						? ((this._t = void 0), r(1))
						: r(0, 'keys' == s ? o : 'values' == s ? e[o] : [o, e[o]]);
				},
				'values'
			)),
				(i.Arguments = i.Array),
				t('keys'),
				t('values'),
				t('entries');
		},
		'./node_modules/core-js/library/modules/es6.map.js': function (e, s, o) {
			'use strict';
			var t = o('./node_modules/core-js/library/modules/_collection-strong.js'),
				r = o('./node_modules/core-js/library/modules/_validate-collection.js');
			e.exports = o('./node_modules/core-js/library/modules/_collection.js')(
				'Map',
				function (e) {
					return function () {
						return e(this, arguments.length > 0 ? arguments[0] : void 0);
					};
				},
				{
					get: function (e) {
						var s = t.getEntry(r(this, 'Map'), e);
						return s && s.v;
					},
					set: function (e, s) {
						return t.def(r(this, 'Map'), 0 === e ? 0 : e, s);
					}
				},
				t,
				!0
			);
		},
		'./node_modules/core-js/library/modules/es6.object.assign.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_export.js');
			t(t.S + t.F, 'Object', {
				assign: o('./node_modules/core-js/library/modules/_object-assign.js')
			});
		},
		'./node_modules/core-js/library/modules/es6.object.keys.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_to-object.js'),
				r = o('./node_modules/core-js/library/modules/_object-keys.js');
			o('./node_modules/core-js/library/modules/_object-sap.js')('keys', function () {
				return function (e) {
					return r(t(e));
				};
			});
		},
		'./node_modules/core-js/library/modules/es6.object.to-string.js': function (e, s) {},
		'./node_modules/core-js/library/modules/es6.promise.js': function (e, s, o) {
			'use strict';
			var t,
				r,
				i,
				n,
				a = o('./node_modules/core-js/library/modules/_library.js'),
				l = o('./node_modules/core-js/library/modules/_global.js'),
				c = o('./node_modules/core-js/library/modules/_ctx.js'),
				d = o('./node_modules/core-js/library/modules/_classof.js'),
				u = o('./node_modules/core-js/library/modules/_export.js'),
				m = o('./node_modules/core-js/library/modules/_is-object.js'),
				h = o('./node_modules/core-js/library/modules/_a-function.js'),
				f = o('./node_modules/core-js/library/modules/_an-instance.js'),
				_ = o('./node_modules/core-js/library/modules/_for-of.js'),
				p = o('./node_modules/core-js/library/modules/_species-constructor.js'),
				S = o('./node_modules/core-js/library/modules/_task.js').set,
				b = o('./node_modules/core-js/library/modules/_microtask.js')(),
				g = o('./node_modules/core-js/library/modules/_new-promise-capability.js'),
				j = o('./node_modules/core-js/library/modules/_perform.js'),
				v = o('./node_modules/core-js/library/modules/_user-agent.js'),
				y = o('./node_modules/core-js/library/modules/_promise-resolve.js'),
				C = l.TypeError,
				T = l.process,
				I = T && T.versions,
				M = (I && I.v8) || '',
				E = l.Promise,
				O = 'process' == d(T),
				R = function () {},
				k = (r = g.f),
				A = !!(function () {
					try {
						var e = E.resolve(1),
							s = ((e.constructor = {})[
								o('./node_modules/core-js/library/modules/_wks.js')('species')
							] = function (e) {
								e(R, R);
							});
						return (
							(O || 'function' == typeof PromiseRejectionEvent) &&
							e.then(R) instanceof s &&
							0 !== M.indexOf('6.6') &&
							-1 === v.indexOf('Chrome/66')
						);
					} catch (e) {}
				})(),
				P = function (e) {
					var s;
					return !(!m(e) || 'function' != typeof (s = e.then)) && s;
				},
				V = function (e, s) {
					if (!e._n) {
						e._n = !0;
						var o = e._c;
						b(function () {
							for (
								var t = e._v,
									r = 1 == e._s,
									i = 0,
									n = function (s) {
										var o,
											i,
											n,
											a = r ? s.ok : s.fail,
											l = s.resolve,
											c = s.reject,
											d = s.domain;
										try {
											a
												? (r || (2 == e._h && N(e), (e._h = 1)),
												  !0 === a
														? (o = t)
														: (d && d.enter(), (o = a(t)), d && (d.exit(), (n = !0))),
												  o === s.promise
														? c(C('Promise-chain cycle'))
														: (i = P(o))
														? i.call(o, l, c)
														: l(o))
												: c(t);
										} catch (e) {
											d && !n && d.exit(), c(e);
										}
									};
								o.length > i;

							)
								n(o[i++]);
							(e._c = []), (e._n = !1), s && !e._h && x(e);
						});
					}
				},
				x = function (e) {
					S.call(l, function () {
						var s,
							o,
							t,
							r = e._v,
							i = w(e);
						if (
							(i &&
								((s = j(function () {
									O
										? T.emit('unhandledRejection', r, e)
										: (o = l.onunhandledrejection)
										? o({ promise: e, reason: r })
										: (t = l.console) && t.error && t.error('Unhandled promise rejection', r);
								})),
								(e._h = O || w(e) ? 2 : 1)),
							(e._a = void 0),
							i && s.e)
						)
							throw s.v;
					});
				},
				w = function (e) {
					return 1 !== e._h && 0 === (e._a || e._c).length;
				},
				N = function (e) {
					S.call(l, function () {
						var s;
						O
							? T.emit('rejectionHandled', e)
							: (s = l.onrejectionhandled) && s({ promise: e, reason: e._v });
					});
				},
				U = function (e) {
					var s = this;
					s._d ||
						((s._d = !0),
						((s = s._w || s)._v = e),
						(s._s = 2),
						s._a || (s._a = s._c.slice()),
						V(s, !0));
				},
				D = function (e) {
					var s,
						o = this;
					if (!o._d) {
						(o._d = !0), (o = o._w || o);
						try {
							if (o === e) throw C("Promise can't be resolved itself");
							(s = P(e))
								? b(function () {
										var t = { _w: o, _d: !1 };
										try {
											s.call(e, c(D, t, 1), c(U, t, 1));
										} catch (e) {
											U.call(t, e);
										}
								  })
								: ((o._v = e), (o._s = 1), V(o, !1));
						} catch (e) {
							U.call({ _w: o, _d: !1 }, e);
						}
					}
				};
			A ||
				((E = function (e) {
					f(this, E, 'Promise', '_h'), h(e), t.call(this);
					try {
						e(c(D, this, 1), c(U, this, 1));
					} catch (e) {
						U.call(this, e);
					}
				}),
				((t = function (e) {
					(this._c = []),
						(this._a = void 0),
						(this._s = 0),
						(this._d = !1),
						(this._v = void 0),
						(this._h = 0),
						(this._n = !1);
				}).prototype = o('./node_modules/core-js/library/modules/_redefine-all.js')(E.prototype, {
					then: function (e, s) {
						var o = k(p(this, E));
						return (
							(o.ok = 'function' != typeof e || e),
							(o.fail = 'function' == typeof s && s),
							(o.domain = O ? T.domain : void 0),
							this._c.push(o),
							this._a && this._a.push(o),
							this._s && V(this, !1),
							o.promise
						);
					},
					catch: function (e) {
						return this.then(void 0, e);
					}
				})),
				(i = function () {
					var e = new t();
					(this.promise = e), (this.resolve = c(D, e, 1)), (this.reject = c(U, e, 1));
				}),
				(g.f = k =
					function (e) {
						return e === E || e === n ? new i(e) : r(e);
					})),
				u(u.G + u.W + u.F * !A, { Promise: E }),
				o('./node_modules/core-js/library/modules/_set-to-string-tag.js')(E, 'Promise'),
				o('./node_modules/core-js/library/modules/_set-species.js')('Promise'),
				(n = o('./node_modules/core-js/library/modules/_core.js').Promise),
				u(u.S + u.F * !A, 'Promise', {
					reject: function (e) {
						var s = k(this);
						return (0, s.reject)(e), s.promise;
					}
				}),
				u(u.S + u.F * (a || !A), 'Promise', {
					resolve: function (e) {
						return y(a && this === n ? E : this, e);
					}
				}),
				u(
					u.S +
						u.F *
							!(
								A &&
								o('./node_modules/core-js/library/modules/_iter-detect.js')(function (e) {
									E.all(e).catch(R);
								})
							),
					'Promise',
					{
						all: function (e) {
							var s = this,
								o = k(s),
								t = o.resolve,
								r = o.reject,
								i = j(function () {
									var o = [],
										i = 0,
										n = 1;
									_(e, !1, function (e) {
										var a = i++,
											l = !1;
										o.push(void 0),
											n++,
											s.resolve(e).then(function (e) {
												l || ((l = !0), (o[a] = e), --n || t(o));
											}, r);
									}),
										--n || t(o);
								});
							return i.e && r(i.v), o.promise;
						},
						race: function (e) {
							var s = this,
								o = k(s),
								t = o.reject,
								r = j(function () {
									_(e, !1, function (e) {
										s.resolve(e).then(o.resolve, t);
									});
								});
							return r.e && t(r.v), o.promise;
						}
					}
				);
		},
		'./node_modules/core-js/library/modules/es6.string.iterator.js': function (e, s, o) {
			'use strict';
			var t = o('./node_modules/core-js/library/modules/_string-at.js')(!0);
			o('./node_modules/core-js/library/modules/_iter-define.js')(
				String,
				'String',
				function (e) {
					(this._t = String(e)), (this._i = 0);
				},
				function () {
					var e,
						s = this._t,
						o = this._i;
					return o >= s.length
						? { value: void 0, done: !0 }
						: ((e = t(s, o)), (this._i += e.length), { value: e, done: !1 });
				}
			);
		},
		'./node_modules/core-js/library/modules/es7.map.from.js': function (e, s, o) {
			o('./node_modules/core-js/library/modules/_set-collection-from.js')('Map');
		},
		'./node_modules/core-js/library/modules/es7.map.of.js': function (e, s, o) {
			o('./node_modules/core-js/library/modules/_set-collection-of.js')('Map');
		},
		'./node_modules/core-js/library/modules/es7.map.to-json.js': function (e, s, o) {
			var t = o('./node_modules/core-js/library/modules/_export.js');
			t(t.P + t.R, 'Map', {
				toJSON: o('./node_modules/core-js/library/modules/_collection-to-json.js')('Map')
			});
		},
		'./node_modules/core-js/library/modules/es7.promise.finally.js': function (e, s, o) {
			'use strict';
			var t = o('./node_modules/core-js/library/modules/_export.js'),
				r = o('./node_modules/core-js/library/modules/_core.js'),
				i = o('./node_modules/core-js/library/modules/_global.js'),
				n = o('./node_modules/core-js/library/modules/_species-constructor.js'),
				a = o('./node_modules/core-js/library/modules/_promise-resolve.js');
			t(t.P + t.R, 'Promise', {
				finally: function (e) {
					var s = n(this, r.Promise || i.Promise),
						o = 'function' == typeof e;
					return this.then(
						o
							? function (o) {
									return a(s, e()).then(function () {
										return o;
									});
							  }
							: e,
						o
							? function (o) {
									return a(s, e()).then(function () {
										throw o;
									});
							  }
							: e
					);
				}
			});
		},
		'./node_modules/core-js/library/modules/es7.promise.try.js': function (e, s, o) {
			'use strict';
			var t = o('./node_modules/core-js/library/modules/_export.js'),
				r = o('./node_modules/core-js/library/modules/_new-promise-capability.js'),
				i = o('./node_modules/core-js/library/modules/_perform.js');
			t(t.S, 'Promise', {
				try: function (e) {
					var s = r.f(this),
						o = i(e);
					return (o.e ? s.reject : s.resolve)(o.v), s.promise;
				}
			});
		},
		'./node_modules/core-js/library/modules/web.dom.iterable.js': function (e, s, o) {
			o('./node_modules/core-js/library/modules/es6.array.iterator.js');
			for (
				var t = o('./node_modules/core-js/library/modules/_global.js'),
					r = o('./node_modules/core-js/library/modules/_hide.js'),
					i = o('./node_modules/core-js/library/modules/_iterators.js'),
					n = o('./node_modules/core-js/library/modules/_wks.js')('toStringTag'),
					a =
						'CSSRuleList,CSSStyleDeclaration,CSSValueList,ClientRectList,DOMRectList,DOMStringList,DOMTokenList,DataTransferItemList,FileList,HTMLAllCollection,HTMLCollection,HTMLFormElement,HTMLSelectElement,MediaList,MimeTypeArray,NamedNodeMap,NodeList,PaintRequestList,Plugin,PluginArray,SVGLengthList,SVGNumberList,SVGPathSegList,SVGPointList,SVGStringList,SVGTransformList,SourceBufferList,StyleSheetList,TextTrackCueList,TextTrackList,TouchList'.split(
							','
						),
					l = 0;
				l < a.length;
				l++
			) {
				var c = a[l],
					d = t[c],
					u = d && d.prototype;
				u && !u[n] && r(u, n, c), (i[c] = i.Array);
			}
		}
	});
});
//# sourceMappingURL=engine-conference.min.js.map
