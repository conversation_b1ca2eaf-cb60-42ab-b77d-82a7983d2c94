// why?
// Object.assign 是 shadow copy，内部 object 属性，Object.assign之后， 其实还是指向原来的那个内部对象

export function deepAssign(e, r) {
	let t = e => e && 'object' == typeof e;
	return t(e) && t(r)
		? (Object.keys(r).forEach(a => {
				let c = e[a],
					o = r[a];
				Array.isArray(c) && Array.isArray(o)
					? (e[a] = o)
					: t(o)
					? (e[a] = deepAssign(Object.create(o), o))
					: (e[a] = o);
		  }),
		  e)
		: r;
}

// FYI: https://gist.github.com/ahtcx/0cd94e62691f539160b32ecda18af3d6?permalink_comment_id=2930530#gistcomment-2930530
/*
export function deepAssign(target, source) {
    const isObject = (obj) => obj && typeof obj === 'object';

    if (!isObject(target) || !isObject(source)) {
        return source;
    }

    Object.keys(source).forEach(key => {
        const targetValue = target[key];
        const sourceValue = source[key];

        if (Array.isArray(targetValue) && Array.isArray(sourceValue)) {
            target[key] = sourceValue;
            //} else if (isObject(targetValue) && isObject(sourceValue)) {
        } else if (isObject(sourceValue)) {
            //target[key] = mergeDeep(Object.assign({}, targetValue), sourceValue);
            target[key] = deepAssign(Object.create(sourceValue), sourceValue);
        } else {
            target[key] = sourceValue;
        }
    });

    return target;
}
*/
