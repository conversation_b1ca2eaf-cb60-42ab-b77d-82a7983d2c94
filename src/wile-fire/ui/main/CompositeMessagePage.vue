<template>
	<section class="composite-page">
		<div v-if="!sharedMiscState.isElectron" class="close-button-container">
			<div class="chatTitle">{{ message.content.content }}</div>
			<i class="yehuo-ion-close" @click="hideCompositeMessagePage"></i>
		</div>
		<div v-if="!compositeMessage">
			{{ 'Null CompositeMessagePage' }}
		</div>
		<ul v-else>
			<li v-for="message in compositeMessage.messageContent.messages" :key="message.uid">
				<div class="message-container">
					<div class="portrait-container">
						<!-- v-if=" index === 0 || message.from !== compositeMessage.messageContent.messages[index -
						1].from " -->
						<img class="portrait-container-img" alt="" :src="message._from.portrait" />
					</div>
					<div class="name-time-content-container">
						<div class="name-time-container">
							<p class="name">{{ message._from._displayName }}</p>
							<p class="time">{{ message._timeStr }}</p>
							<!--                            <p class="time"> 1223</p>-->
						</div>
						<div class="content">
							<!--message content-->
							<TextMessageContentView
								:message="message"
								v-if="message.messageContent.type === 1"
								:style="{ '--out-arrow-color': '#a8bdff', '--in-arrow-color': 'white' }"
								v-bind:class="{
									leftarrow: message.direction === 1,
									rightarrow: message.direction === 0
								}"
							/>
							<!--                            <AudioMessageContentView :message="message"-->
							<!--                                                     v-else-if="message.messageContent.type === 2"/>-->
							<ImageMessageContentView
								:message="message"
								:is-in-composite-view="true"
								v-else-if="message.messageContent.type === 3"
							/>
							<!--                           v-bind:class="{leftarrow:message.direction === 1, rightarrow: message.direction === 0}"/>-->
							<FileMessageContentView
								:message="message"
								v-else-if="message.messageContent.type === 5"
								v-bind:class="{
									leftarrow: message.direction === 1,
									rightarrow: message.direction === 0
								}"
							/>
							<VideoMessageContentView
								:message="message"
								:is-in-composite-view="true"
								v-else-if="message.messageContent.type === 6"
							/>
							<!--                           v-bind:class="{leftarrow:message.direction === 1, rightarrow: message.direction === 0}"/>-->
							<StickerMessageContentView
								:message="message"
								v-else-if="message.messageContent.type === 7"
							/>
							<CompositeMessageContentView
								:message="message"
								v-else-if="message.messageContent.type === 11"
							/>
							<!--                            <CallStartMessageContentView :message="message"-->
							<!--                                                         v-else-if="message.messageContent.type === 400"/>-->
							<!--                            <ConferenceInviteMessageContentView :message="message"-->
							<!--                                                                v-else-if="message.messageContent.type === 408"/>-->
							<UnsupportMessageContentView
								:message="message"
								v-else-if="[2, 10, 400, 408].indexOf(message.messageContent.type) >= 0"
							/>
							<UnknowntMessageContentView
								:message="message"
								v-else
								v-bind:class="{
									leftarrow: message.direction === 1,
									rightarrow: message.direction === 0
								}"
							/>
						</div>
					</div>
				</div>
			</li>
		</ul>
	</section>
</template>

<script>
import TextMessageContentView from './conversation/message/content/TextMessageContentView';
import UnsupportMessageContentView from './conversation/message/content/UnsupportMessageContentView';
import store from '../../store';
import CompositeMessageContentView from './conversation/message/content/CompositeMessageContentView';
import ImageMessageContentView from './conversation/message/content/ImageMessageContentView';
import VideoMessageContentView from './conversation/message/content/VideoMessageContentView';
import FileMessageContentView from './conversation/message/content/FileMessageContentView';
import StickerMessageContentView from './conversation/message/content/StickerMessageContentView';
import UnknowntMessageContentView from './conversation/message/content/UnknownMessageContentView';
import Message from '../../wfc/messages/message';
import { stringValue } from '../../wfc/util/longUtil';
import wfc from '../../wfc/client/wfc';
import FavItem from '../../wfc/model/favItem';
import Conversation from '../../wfc/model/conversation';
import axios from 'axios';
import { isElectron } from '../../platform';

export default {
	name: 'CompositeMessagePage',
	props: {
		message: {
			required: false,
			type: Message,
			default: null
		}
	},
	data() {
		return {
			compositeMessage: null,
			sharedMiscState: store.state.misc
		};
	},

	mounted() {
		if (this.message) {
			console.log(this.message, 99);
			this.compositeMessage = this.message;
			this.loadMediaCompositeMessage(this.compositeMessage);
			return;
		}
		let hash = window.location.hash;
		if (hash.indexOf('messageUid=') >= 0) {
			let messageUid = hash.substring(hash.indexOf('=') + 1);
			this.compositeMessage = store.getMessageByUid(messageUid);
			if (!this.compositeMessage) {
				wfc.loadRemoteMessage(
					messageUid,
					msg => {
						this.compositeMessage = msg;

						if (this.compositeMessage) {
							store._patchMessage(this.compositeMessage, 0);
							document.title = this.compositeMessage.messageContent.title;
							this.loadMediaCompositeMessage(this.compositeMessage);
						}
					},
					err => {
						console.error('load remote message error', err);
					}
				);
			}
			if (!this.compositeMessage) {
				return;
			}
		} else {
			let faveItemData = hash.substring(hash.indexOf('=') + 1);
			let favItemRaw = JSON.parse(wfc.b64_to_utf8(wfc.unescape(faveItemData)));
			let favItem = Object.assign(new FavItem(), favItemRaw);
			favItem.conversation = new Conversation(
				favItem.convType,
				favItem.convTarget,
				favItem.convLine
			);
			favItem.favType = favItem.type;
			this.compositeMessage = favItem.toMessage();
		}
		if (this.compositeMessage) {
			store._patchMessage(this.compositeMessage, 0);
			document.title = this.compositeMessage.messageContent.title;
			this.loadMediaCompositeMessage(this.compositeMessage);
		}
	},

	methods: {
		hideCompositeMessagePage() {
			this.$modal.hide('show-composite-message-modal' + '-' + stringValue(this.message.messageUid));
		},
		loadMediaCompositeMessage(msg) {
			let content = msg.messageContent;
			if (content.remotePath) {
				if (isElectron()) {
					if (content.localPath && require('fs').existsSync(content.localPath)) {
						return;
					}
				} else {
					// web 每次加载
					// do nothing
				}
				axios.get(content.remotePath, { responseType: 'arraybuffer' }).then(value => {
					content._decodeMessages(new TextDecoder('utf-8').decode(value.data));
					store._patchMessage(this.compositeMessage, 0);
					content.loaded = true;
				});
			}
		},
		previewCompositeMessage(focusMessageUid) {
			store.previewCompositeMessage(this.compositeMessage, focusMessageUid);
		}
	},

	components: {
		UnknowntMessageContentView,
		// ConferenceInviteMessageContentView,
		CompositeMessageContentView,
		// AudioMessageContentView,
		// CallStartMessageContentView,
		UnsupportMessageContentView,
		TextMessageContentView,
		ImageMessageContentView,
		VideoMessageContentView,
		FileMessageContentView,
		StickerMessageContentView
	}
};
</script>

<style scoped lang="scss">
.composite-page {
	width: var(--composite-message-page-width);
	height: var(--composite-message-page-height);
	/* background: #f7f7f7; */
	background: #fff;
	/* overflow: scroll; */
}

.close-button-container {
	/* position: absolute; */
	padding: 27px 30px 34px 30px;
	/* top: 0;
	right: 0; */
	display: flex;
	align-items: center;
	.chatTitle {
		/* flex: 1; */
		font-weight: 800;
		font-size: 18px;
		color: #303133;
		line-height: 24px;
	}
	.yehuo-ion-close {
		cursor: pointer;
		margin-left: auto;
		color: #606266;
	}
}

/* .close-button-container:active {
	background: lightgrey;
} */

.composite-page ul {
	width: 100%;
	height: calc(483px - 85px);
	padding: 0px 30px 24px 30px;
	list-style-position: inside;
	overflow: auto;
	@include noScrollBar;
}

.composite-page ul li {
	position: relative;
	padding: 10px 0;
}

.composite-page ul li:not(:last-child)::after {
	content: '';
	width: calc(100% - 55px);
	position: absolute;
	margin-left: 55px;
	padding: 5px 0;
	border-bottom: 1px solid #f0f0f0;
}

.message-container {
	width: 100%;
	display: flex;
}

.name-time-content-container {
	width: 100%;
}

.name-time-container {
	width: 100%;
	display: flex;
	justify-content: space-between;
	margin-bottom: 4px;
	.name {
		font-weight: 400;
		font-size: 12px;
		color: $subTextColor;
		line-height: 20px;
		text-align: left;
		font-style: normal;
		text-transform: none;
	}
	.time {
		font-weight: 400;
		font-size: 12px;
		color: $subTextColor;
		line-height: 20px;
		text-align: left;
		font-style: normal;
		text-transform: none;
	}
}

.name-time-content-container .content {
	display: inline-block;
	margin-left: -10px;
}

.portrait-container {
	width: 40px;
	height: 40px;
	margin: 5px 10px 10px 10px;
}

.portrait-container-img {
	width: 36px;
	height: 36px;
	border-radius: 6px;
}

::v-deep .text-message-container.out {
	/* background-color: #f7f7f7; */
	background-color: #fff;
}

::v-deep .text-message-container {
	/* background-color: #f7f7f7; */
	background-color: #fff;
	padding: 0;
	.text {
		font-weight: 400;
		font-size: 14px;
		color: $textColor;
		line-height: 22px;
		text-align: left;
		font-style: normal;
		text-transform: none;
	}
}

::v-deep .rightarrow::before {
	display: none;
}

::v-deep .leftarrow::before {
	display: none;
}
</style>
