<template>
	<div class="emoji">
		<div class="emoji-tab">
			<div
				v-for="(v, i) in emojiStickerList"
				:key="i"
				class="emoji-tab-item"
				@click="onCategoryClick(i)"
				:class="{ active: i === currentEmojiStickerIndex }"
			>
				<img class="emoji-tab-item-img" :src="v.poster" />
			</div>
		</div>
		<div class="emoji-con">
			<div
				v-for="(v, i) in emojiStickerList[0].emojis"
				:key="i"
				class="emoji-con-item"
				@click="onClickEmoji(v)"
			>
				{{ v }}
			</div>
		</div>
	</div>
</template>

<script>
import emojiStickerConfig from '@/utils/emojiStickerConfig';

export default {
	name: 'emoji',
	data() {
		return {
			emojiStickerList: emojiStickerConfig,
			currentEmojiStickerIndex: 0
		};
	},
	methods: {
		onCategoryClick(i) {
			this.currentEmojiStickerIndex = i;
		},
		onClickEmoji(emoji) {
			this.$emit('onClickEmoji', emoji);
		}
	}
};
</script>

<style scoped lang="scss">
.emoji {
	position: absolute;
	bottom: 40px;
	height: 280px;
	width: 360px;
	display: flex;
	align-items: flex-start;
	justify-content: flex-start;
	flex-direction: column;
	background: #ffffff;
	box-shadow: 0px 2px 6px -1px rgb(0 0 0 / 5%), 0px 4px 8px 0px rgb(0 0 0 / 6%);
	border-radius: 12px;
	&-tab {
		width: 100%;
		display: flex;
		align-items: center;
		height: 40px;
		padding: 0 10px;
		border-bottom: 1px solid #e4e4e4;
		&-item {
			width: 32px;
			height: 32px;
			@include flexBox();
			&-img {
				width: 22px;
				height: 22px;
			}
		}
		.active {
			background: #e4e4e4;
			border-radius: 8px;
		}
	}
	&-con {
		flex: 1;
		display: flex;
		align-items: flex-start;
		justify-content: flex-start;
		flex-wrap: wrap;
		overflow: auto;
		@include noScrollBar;
		&-item {
			width: 40px;
			height: 40px;
			font-size: 22px;
			@include flexBox();
		}
	}
}
</style>
