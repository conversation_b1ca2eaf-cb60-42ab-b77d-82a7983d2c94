<template>
	<div class="summar-page">
		<header class="header">
			<span class="title">纪要整理</span>
			<span class="title button" @click="close">关闭</span>
		</header>
		<article class="article">
			<p class="titles">请选择纪要整理范围</p>
			<el-row :gutter="10">
				<el-col :span="24">
					<div class="form-item">
						<span>快速整理:</span>
						<!-- <el-input
							v-model="formData.test1"
							placeholder="请输入内容"
							style="width: calc(100% - 80px)"
						></el-input> -->
						<el-select
							style="width: calc(100% - 80px)"
							@change="selectChange(summeryTime)"
							v-model="summeryTime"
							slot="prepend"
							clearable
							placeholder="请选择"
						>
							<el-option
								v-for="(el, i) in quickFinishing"
								:key="i"
								:label="el.label"
								:value="el.value"
							></el-option>
						</el-select>
					</div>
				</el-col>
				<el-col :span="19">
					<div class="form-item">
						<span>自定义时间:</span>
						<el-date-picker
							@change="changeStart"
							@focus="focusStart"
							v-model="formData.summaryDay"
							type="date"
							placeholder="年-月-日"
							style="width: 156px"
							value-format="yyyy-MM-dd"
						></el-date-picker>
						<div class="line">-</div>
						<el-date-picker
							@change="changeEnd"
							@focus="focusEnd"
							v-model="formData.summaryDayEnd"
							type="date"
							placeholder="年-月-日"
							style="width: 155px"
							value-format="yyyy-MM-dd"
						></el-date-picker>
					</div>
				</el-col>
				<el-col :span="5">
					<div style="margin-top: 12px">
						<el-button type="primary" size="medium" style="width: 100%" @click="onCreat">
							立即整理
						</el-button>
					</div>
				</el-col>
			</el-row>

			<div v-loading="loading" class="content">
				<div class="titles">
					<span class="titles-title">全部纪要</span>
					<i class="coos-iconfont icon-refresh titles-icon" @click="resh"></i>
				</div>
				<div class="allSummery" @scroll="handleScroll">
					<div v-for="(item, index) in list" :key="index" class="list-box">
						<div class="name">{{ isToday(item.title) }}</div>
						<div class="work">
							<div
								v-for="(ltem, i) in splitArray(item.content)"
								:key="i"
								v-show="i < 3 || showList[index]"
								class="work-con"
							>
								<span>{{ ltem }}</span>
								<div v-if="item.status === 1" class="loading">
									<div class="loading-icon" v-loading="true"></div>
									<span>努力整理中...</span>
								</div>
							</div>
							<div class="arr" @click="launch(index)" v-if="splitArray(item.content).length > 3">
								{{ showList[index] ? '收起' : '展开' }}
								<i v-if="showList[index]" class="coos-iconfont icon-nav-top quick-button-icon"></i>
								<i v-else class="coos-iconfont icon-nav-bottom quick-button-icon"></i>
							</div>
						</div>
						<div class="list-bottom">
							<div class="time">
								<i class="el-icon-time"></i>
								最近更新：{{ item.updateTime ? item.updateTime : item.createTime }}
							</div>
							<el-popover
								:ref="'popover' + index"
								placement="left-start"
								width="120"
								trigger="click"
							>
								<ul class="operation">
									<li
										@click.prevent="menuSummery('copy', item, index)"
										class="coos-iconfont icon-fuzhi icon"
									>
										<span>复制</span>
									</li>
									<li
										@click.prevent="menuSummery('update', item, index)"
										class="coos-iconfont icon-refresh icon"
									>
										<span>更新纪要</span>
									</li>
									<li
										@click.prevent="menuSummery('delete', item, index)"
										class="coos-iconfont icon-trash icon"
									>
										<span>删除</span>
									</li>
								</ul>
								<div slot="reference" class="config">···</div>
							</el-popover>
						</div>
					</div>
					<div class="loadingBottom" v-if="list.length > 2">
						{{ loadingBottom ? '加载中...' : '到底了' }}
					</div>
				</div>
			</div>
		</article>
	</div>
</template>

<script>
import { copyText } from '@/wile-fire/ui/util/clipboard.js';
import {
	getSummeryList,
	createSummery,
	deleteSummery,
	rehashSummery
} from '@/wile-fire/ui/util/summary';
export default {
	data() {
		return {
			queryPage: {
				pageNo: 1,
				pageSize: 10
			},
			copyData: {},
			today: '',
			scrollFlag: true,
			pageFlag: true,
			loadingBottom: false,
			quickFinishing: [
				{
					label: '今天',
					value: 0
				},
				{
					label: '近三天',
					value: 3
				},
				{
					label: '近七天',
					value: 7
				},
				{
					label: '近三十天',
					value: 30
				}
			],
			summeryTime: '',
			formData: {
				summaryDay: '',
				summaryDayEnd: ''
			},
			loading: false,
			list: [],
			contentShow: []
		};
	},
	props: {
		conversationInfo: {
			type: Object,
			default: () => {}
		}
	},
	computed: {
		showList() {
			return this.contentShow;
		}
	},
	watch: {
		queryPage(val) {
			this.getList();
		}
	},
	mounted() {
		let date = new Date();
		let m = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1;
		let d = date.getDate() < 10 ? '0' + date.getDate() : date.getDate();
		this.today = date.getFullYear() + '-' + m + '-' + d;
		//this.selectChange(0);
		this.getList();
	},
	methods: {
		/**刷新*/
		resh() {
			this.getList(0, '', { pageNo: 1, pageSize: 10 });
		},
		/***
		 * 列表
		 */
		async getList(num = 0, msg = null, queryPage = null) {
			if (queryPage || this.queryPage.pageNo === 1) {
				this.loading = true;
			}
			/* if (msg == 'db') {
				this.loading = false;
				this.loadingBottom = false;
			} */
			let page = queryPage ? queryPage : this.queryPage;
			const params = {
				...page,
				target: this.conversationInfo.conversation.target,
				origin: this.conversationInfo.conversation.type
			};
			await getSummeryList(params).then(({ result, code, message }) => {
				//console.log(result);
				if (code === 200) {
					if (queryPage) {
						this.list = result.records;
					} else {
						if (result.records.length == 0) {
							this.loading = false;
							this.pageFlag = false;
							this.scrollFlag = true;
							this.loadingBottom = false;
							return;
						} else {
							this.list = [...this.list, ...result.records];
						}
					}
					let arr = [];
					this.list.map(() => {
						arr.push(false);
					});
					this.contentShow = [...this.contentShow, ...arr];
					setTimeout(() => {
						this.loading = false;
						this.scrollFlag = true;
						this.pageFlag = true;
						if (msg && num > 1000) {
							this.$message.success(msg);
						}
					}, num);
				} else {
					this.$message.error(message || '数据异常');
				}
			});
		},
		/**
		 * 标题数据处理
		 */
		splitArray(content) {
			if (content.split('\\n').length > 1) {
				return content.split('\\n');
			} else {
				return content.split('\n');
			}
		},
		/**
		 * 生成
		 */
		async onCreat() {
			this.loading = true;
			let d = {};
			Object.keys(this.formData).map(el => {
				if (this.formData[el] != '' && this.formData[el]) {
					d[el] = this.formData[el];
				}
			});
			const data = {
				...d,
				target: this.conversationInfo.conversation.target,
				origin: this.conversationInfo.conversation.type
			};
			await createSummery(data).then(res => {
				if (res.code == 200) {
					this.$message.success(res.message);
					this.pageFlag = true;
					this.scrollFlag = true;
					this.queryPage.pageNo = 1;
					this.getList(1500, '工作纪要生成中，请稍后！', { pageNo: 1, pageSize: 10 });
				} else {
					this.loading = false;
					this.$message.error(res.message || '数据异常');
				}
			});
		},
		/**
		 * 删除
		 */
		delete(ids) {
			this.loading = true;
			deleteSummery(ids).then(res => {
				if (res.code == 200) {
					this.getList(1500, '删除成功', { pageNo: 1, pageSize: this.list.length - 1 });
				} else {
					this.$message.warning(res.message || '删除失败');
					this.loading = false;
				}
			});
		},
		/**
		 * 刷新
		 */
		rehash(id) {
			rehashSummery(id).then(res => {
				if (res.code == 200) {
					//this.$message.success(res.message || '更新中');
					this.getList(1500, '更新成功', { pageNo: 1, pageSize: this.list.length });
				} else {
					this.$message.warning(res.message || '更新失败');
					this.loading = false;
				}
			});
		},
		selectChange(value) {
			let date = new Date();
			let end = new Date(date.getTime() - 24 * 60 * 60 * 1000 * value);
			let m = end.getMonth() + 1 < 10 ? '0' + (end.getMonth() + 1) : end.getMonth() + 1;
			let d = end.getDate() < 10 ? '0' + end.getDate() : end.getDate();
			this.formData.summaryDayEnd = this.today;
			this.formData.summaryDay = end.getFullYear() + '-' + m + '-' + d;
		},
		/**
		 * 滚动底部
		 */
		handleScroll(e) {
			const dom = e.target;
			var scrollTop = dom.scrollTop; //滑入屏幕上方的高度
			var windowHeitht = dom.clientHeight; //能看到的页面的高度
			var scrollHeight = dom.scrollHeight; //监控的整个div的高度（包括现在看到的和上下隐藏起来看不到的）
			let total = scrollTop + windowHeitht;
			if (total >= scrollHeight - 20) {
				if (this.scrollFlag) {
					this.scrollFlag = false;
					if (this.pageFlag) {
						this.queryPage.pageNo++;
						this.loadingBottom = true;
						this.getList(500);
					}
				}
			}
		},
		/**
		 * 时间逻辑
		 */
		changeStart(value) {
			if (value) {
				let s = new Date(value);
				let now = new Date();
				if (now.getTime() < new Date(this.formData.summaryDay)) {
					this.formData.summaryDay = this.copyData.summaryDay;
					this.$message.error('不得大于当前时间');
					return;
				}
				if (this.formData.summaryDayEnd != '') {
					if (s > new Date(this.formData.summaryDayEnd)) {
						this.formData.summaryDayEnd = '';
						return;
					}
				}
			} else {
				this.formData.summaryDay = '';
				this.formData.summaryDayEnd = '';
				this.summeryTime = '';
			}
		},
		focusStart(value) {
			this.copyData['summaryDay'] = value.value;
		},
		focusEnd(value) {
			this.copyData['summaryDayEnd'] = value.value;
		},
		changeEnd(value) {
			if (value) {
				let e = new Date(value);
				let now = new Date();
				if (now.getTime() < e) {
					this.formData.summaryDayEnd = this.copyData.summaryDayEnd;
					this.$message.error('不得大于当前时间');
					return;
				}
				if (e < new Date(this.formData.summaryDay)) {
					this.formData.summaryDayEnd = this.copyData.summaryDayEnd;
					this.$message.error('不得小于开始时间');
					return;
				}
			}
		},
		/** */
		close() {
			this.$emit('close');
		},
		launch(index) {
			this.contentShow.splice(index, 1, !this.contentShow[index]);
		},
		menuSummery(type, arr, index) {
			switch (type) {
				case 'copy':
					let list = [];
					arr.content.split('\n').map(el => {
						list.push(el);
					});
					let str = arr.detailTitle ? arr.detailTitle : arr.title + '的纪要整理：';
					this.copy(str + '\n' + list.join('\n'));
					break;
				case 'update':
					this.rehash(arr.id);
					break;
				case 'delete':
					this.$confirm('是否删除该条纪要?', '提示', {
						confirmButtonText: '确定',
						cancelButtonText: '取消',
						type: 'warning'
					}).then(() => {
						this.delete(arr.id);
					});
					break;
				default:
					break;
			}
			this.$refs['popover' + index][0].showPopper = false;
		},
		isToday(title) {
			let arr = title.split('—');
			let list = this.today.split('-');
			if (arr[1] == arr[0] || arr.length == 1) {
				let date = list[0] + '年' + list[1].split('0').join('') + '月' + list[2] + '日';
				if (arr[0] == date) {
					return arr[0] + '（今天）';
				} else {
					return arr[0];
				}
			} else {
				return title;
			}
		},
		/***
		 * 复制
		 */
		copy(message) {
			let content = message;
			//if (content instanceof TextMessageContent) {
			let selectedText = window.getSelection().toString();
			if (selectedText) {
				copyText(selectedText);
				this.$message.success('复制成功');
			} else {
				copyText(content);
				this.$message.success('复制成功');
			}
			/* } else if (content instanceof ImageMessageContent) {
				copyImg(content.remotePath);
				this.$message.success('复制成功');
			} else if (content instanceof MediaMessageContent) {
				if (isElectron()) {
					ipcRenderer.send(IPCEventType.FILE_COPY, { path: content.localPath });
					this.$message.success('复制成功');
				}
			} */
		}
	}
};
</script>

<style lang="scss" scoped>
.summar-page {
	border-left: #dce3e7 solid 1px;
	position: fixed;
	top: 12px;
	bottom: 12px;
	right: 12px;
	z-index: 667;
	width: 570px;
	display: flex;
	background: #ffffff;
	flex-direction: column;
	border-radius: 0 12px 12px 0;
}
.header {
	border-radius: 0 12px 0 0;
	padding: 0px 12px 0px 22px;
	width: 100%;
	height: 54px;
	background: #ffffff;
	border-bottom: 1px solid #dce3e7;
	display: flex;
	flex-direction: row;
	justify-content: space-between;

	.title {
		font-family: PingFang SC, PingFang SC;
		font-weight: 800;
		font-size: 16px;
		color: $primaryTextColor;
		line-height: 54px;
	}

	.button {
		font-weight: 400;
		font-size: 14px;
		color: $subTextColor;
		cursor: pointer;
	}
}
.article {
	flex: 1;
	overflow: hidden;
	padding: 16px 24px;
	width: 100%;
	display: flex;
	flex-direction: column;
}
.titles {
	@include flexBox(space-between);
	&-title {
		font-weight: 800;
		font-size: 16px;
		color: $primaryTextColor;
	}
	&-icon {
		font-size: 20px;
		color: $primaryTextColor;
		margin-right: 12px;
		cursor: pointer;
	}
}
.form-item {
	margin-top: 12px;
	display: flex;
	justify-content: space-between;
	align-items: center;
	font-family: PingFang SC, PingFang SC;
	span {
		color: rgba(0, 0, 0, 0.6);
		font-size: 14px;
		display: inline-block;
		width: 80px;
	}
	.line {
		margin: 0 10px;
		color: $holderTextColor;
	}
}
.operation {
	span {
		cursor: pointer;
		display: inline-block;
		margin-left: 6px;
		font-family: PingFang SC, PingFang SC;
		font-size: 14px;
		color: $textColor;
	}
}
.content {
	flex: 1;
	overflow: hidden;
	margin-top: 40px;
	display: flex;
	flex-direction: column;
	.allSummery {
		flex: 1;
		overflow-y: scroll;
		display: flex;
		flex-direction: column;
		@include noScrollBar;
		.loadingBottom {
			display: flex;
			align-items: center;
			justify-content: center;
			padding: 8px;
			color: rgb(195, 195, 195);
		}
	}
	.titles {
		margin: 0 0 8px 0;
	}
	.list-box {
		margin: 0 0 8px 0;
		padding: 12px;
		border-radius: 9px;
		border: 1px solid #f0f0f0;
		font-family: PingFang SC, PingFang SC;
		.name {
			font-size: 15px;
			color: $textColor;
			border-bottom: 1px solid #f0f0f0;
			padding-bottom: 6px;
		}
		.work {
			.work-con {
				margin-top: 8px;
				font-size: 14px;
				color: $textColor;
				@include flexBox(space-between);
				.loading {
					display: inline-flex;
					align-items: center;
					&-icon {
						width: 15px;
						height: 15px;
						margin-right: 8px;
						@include flexBox();
						::v-deep .circular {
							width: 15px;
							height: 15px;
						}
						::v-deep .el-loading-spinner {
							top: 0 !important;
							margin-top: 0 !important;
						}
					}
				}
			}
			.arr {
				margin-top: 8px;
				cursor: pointer;
				font-size: 12px;
				color: var(--brand-6);
				i {
					font-size: 18px;
					position: relative;
					top: 2px;
				}
			}
		}
		.list-bottom {
			display: flex;
			margin-top: 10px;
			justify-content: space-between;
			padding-top: 14px;
			border-top: 1px solid #f0f0f0;
			align-items: center;
			.time {
				font-size: 11px;
				color: $subTextColor;
				i {
					font-size: 13px;
					margin-right: 5px;
				}
			}
			.config {
				cursor: pointer;
				letter-spacing: 2px;
				color: $subTextColor;
				font-size: 12px;
				font-weight: 600;
			}
		}
	}
}
</style>
