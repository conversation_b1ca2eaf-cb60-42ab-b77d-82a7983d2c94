<template>
	<section class="multi-selection-action-container">
		<ul>
			<li>
				<div class="action" @click="forwardComposite">
					<i class="coos-iconfont icon-hebingzhuanfa icon"></i>
					<p>{{ $t('conversation.forward_composite') }}</p>
				</div>
			</li>
			<li>
				<div class="action" @click="forwardOneByOne">
					<i class="coos-iconfont icon-zhuanfa icon"></i>
					<p>{{ $t('conversation.forward_one_by_one') }}</p>
				</div>
			</li>
			<!-- <li>  收藏功能屏蔽
				<div class="action" @click="fav">
					<div class="icon">
						<i class="yehuo-ion-android-favorite"></i>
					</div>
					<p>{{ $t('common.fav') }}</p>
				</div>
			</li> -->
			<li>
				<div class="action" @click="deleteMultiMessage">
					<i class="coos-iconfont icon-trash icon"></i>
					<p>{{ $t('common.delete') }}</p>
				</div>
			</li>
			<li>
				<div class="action" @click="hideMultiSelectionActionView">
					<i class="coos-iconfont icon-guanbi1 icon" style="background: transparent"></i>
				</div>
			</li>
		</ul>
	</section>
</template>

<script>
import store from '../../../store';
import ForwardType from './message/forward/ForwardType';

export default {
	name: 'MessageMultiSelectionActionView',
	data() {
		return {
			sharedPickState: store.state.pick
		};
	},
	methods: {
		deleteMultiMessage() {
			this.$confirm(`你确定要删除这条消息吗?`, '提示', {
				confirmButtonText: '确定删除',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {
				//store.deleteSelectedMessages() 返回一个删除所有操作结果后的Promise对象
				Promise.all(store.deleteSelectedMessages())
					.then(() => {
						this.$message.success('删除成功');
					})
					.catch(() => {
						this.$message.error('删除失败');
					});
			});
		},

		hideMultiSelectionActionView() {
			store.toggleMessageMultiSelection();
		},

		forwardOneByOne() {
			let messages = [...this.sharedPickState.messages];
			this.$forwardMessage({
				forwardType: ForwardType.ONE_BY_ONE,
				messages
			});
			store.toggleMessageMultiSelection();
		},

		forwardComposite() {
			let messages = [...this.sharedPickState.messages];
			this.$forwardMessage({
				forwardType: ForwardType.COMPOSITE,
				messages
			});
			store.toggleMessageMultiSelection();
		},

		fav() {
			let messages = [...this.sharedPickState.messages];
			this.$parent.favMessages(messages);
			store.toggleMessageMultiSelection();
		}
	}
};
</script>

<style lang="css" scoped>
.multi-selection-action-container {
	width: 100%;
	height: 141px;
	background: #eaecf0;
	border-radius: 0px;
	min-height: 141px;
	text-align: center;
	display: flex;
	justify-content: center;
	align-items: center;
	border-top: 1px solid #e2e2e2;
}

ul {
	list-style: none;
}

ul li {
	display: inline-block;
}

.action {
	width: 44px;
	height: 61px;
	font-size: 13px;
	padding: 0 15px;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	margin: 0 16px;
	cursor: pointer;
}

.action .icon {
	width: 44px;
	height: 44px;
	background: #ffffff;
	border-radius: 6px;
	display: flex;
	justify-content: center;
	align-items: center;
	font-size: 28px;
}
.action > p {
	font-family: PingFang SC, PingFang SC;
	font-weight: 400;
	font-size: 10px;
	color: $subTextColor;
	line-height: 13px;
	text-align: left;
	font-style: normal;
	text-transform: none;
	white-space: nowrap;
	margin-top: 4px;
}
</style>
