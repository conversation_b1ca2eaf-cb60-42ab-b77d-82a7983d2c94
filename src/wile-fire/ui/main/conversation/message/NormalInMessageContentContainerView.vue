<template>
	<section class="container">
		<div
			class="message-time-container"
			v-bind:class="{ checked: sharedPickState.messages.indexOf(message) >= 0 }"
		>
			<p v-if="index === 0 || this.message._showTime" class="time">{{ message._timeStr }}</p>

			<div class="message-avatar-content-container">
				<el-popover
					:disabled="
						sharedConversationState.currentConversationInfo.conversation._target.type === 1
					"
					placement="right"
					trigger="click"
					popper-class="customPopoverClass"
				>
					<AvatorPop :user-detail="userdetail" :user-id="userUid" />
					<input
						slot="reference"
						id="checkbox"
						v-if="sharedConversationState.enableMessageMultiSelection"
						type="checkbox"
						:value="message"
						class="checkbox"
						v-model="sharedPickState.messages"
					/>
					<div
						slot="reference"
						v-if="sharedConversationState.enableMessageMultiSelection"
						class="icon"
					>
						<i
							class="coos-iconfont icon-xuanzhong"
							:style="{
								color: sharedPickState.messages.includes(message) ? '#0F45EA' : 'transparent'
							}"
						></i>
					</div>
					<img
						slot="reference"
						ref="userCardTippy"
						:name="'infoTrigger' + this.message.messageId"
						@click="onClickUserPortrait(message.from)"
						@contextmenu.prevent="openMessageSenderContextMenu($event, message)"
						class="avatar"
						draggable="false"
						:src="messageSenderPortrait"
					/>
				</el-popover>
				<!--消息内容 根据情况，if-else-->
				<div class="message-name-content-container">
					<p v-if="[1, 2].indexOf(message.conversation.type) >= 0" class="name">
						{{ message._from._displayName }}
					</p>
					<div class="flex-column flex-align-start">
						<div class="flex-row">
							<MessageContentContainerView
								class="message-content-container"
								v-bind:class="{ highlight: highLight }"
								:message="message"
								@contextmenu.prevent.native="openMessageContextMenu($event, message)"
							>
								<slot></slot>
								<QuoteMessageView
									style="padding: 5px 0; max-width: 80%"
									v-if="quotedMessage"
									:message="message"
									:quoted-message="quotedMessage"
									:enable-message-preview="true"
									:message-digest="
										this.message.messageContent.quoteInfo &&
										this.message.messageContent.quoteInfo.messageDigest
									"
									:show-close-button="false"
								/>
							</MessageContentContainerView>
						</div>
					</div>
				</div>
			</div>
		</div>
	</section>
</template>

<script>
import UserCardView from '../../user/UserCardView';
import AvatorPop from './AvatorPop.vue';
import MessageContentContainerView from './MessageContentContainerView.vue';
import QuoteMessageView from './QuoteMessageView.vue';
import store from '../../../../store';
import wfc from '../../../../wfc/client/wfc';
import ConversationType from '../../../../wfc/model/conversationType';
import ChannelCardView from '../../contact/ChannelCardView';
import GroupMemberType from '../../../../wfc/model/groupMemberType';
import { getDetailInfo } from '@/api/modules/address-book';
export default {
	name: 'NormalInMessageContentView',
	props: {
		index: {
			type: Number,
			default: () => {
				return 0;
			}
		},
		message: null
	},
	data() {
		return {
			sharedConversationState: store.state.conversation,
			sharedPickState: store.state.pick,
			highLight: false,
			quotedMessage: null,
			userdetail: {},
			userUid: ''
		};
	},
	methods: {
		onClickUserPortrait(userId) {
			this.userUid = userId;
			getDetailInfo(userId, { origin: 'im' }).then(res => {
				this.userdetail = res.result;
			});
			if (this.message.conversation.type === ConversationType.Channel) {
				wfc.getChannelInfo(this.message.conversation.target, true);
			} else {
				wfc.getUserInfo(userId, true);
			}
		},
		closeUserCard() {
			console.log('closeUserCard');
			this.$refs['userCardTippy']._tippy.hide();
		},
		openMessageContextMenu(event, message) {
			this.$parent.$emit('openMessageContextMenu', event, message);
			this.highLight = true;
		},
		openMessageSenderContextMenu(event, message) {
			this.$parent.$emit('openMessageSenderContextMenu', event, message);
		},

		onContextMenuClosed() {
			this.highLight = false;
		}
	},
	mounted() {
		this.$parent.$on('contextMenuClosed', this.onContextMenuClosed);

		if (this.message.messageContent.quoteInfo) {
			let messageUid = this.message.messageContent.quoteInfo.messageUid;
			let msg = store.getMessageByUid(messageUid);
			if (!msg) {
				wfc.loadRemoteMessage(
					messageUid,
					ms => {
						msg = store._patchMessage(ms[0]);
						this.quotedMessage = msg;
					},
					err => {
						console.log('load remote message error', messageUid, err);
					}
				);
			} else {
				this.quotedMessage = msg;
			}
		}
	},

	beforeDestroy() {
		this.$parent.$off('contextMenuClosed', this.onContextMenuClosed);
	},

	computed: {
		isDownloading() {
			return store.isDownloadingMessage(this.message.messageId);
		},

		messageSenderPortrait() {
			if (this.message.conversation.type === 3) {
				let channelInfo = wfc.getChannelInfo(this.message.conversation.target, false);
				return channelInfo.portrait;
			} else {
				return this.message._from.portrait;
			}
		},
		enableClickMessageSenderPortrait() {
			if (this.message.conversation.type === ConversationType.Group) {
				let groupInfo = wfc.getGroupInfo(this.message.conversation.target);
				let groupMember = wfc.getGroupMember(this.message.conversation.target, wfc.getUserId());
				if (
					groupInfo.privateChat === 1 &&
					[GroupMemberType.Manager, GroupMemberType.Owner].indexOf(groupMember.type) === -1
				) {
					return false;
				}
			}
			return true;
		}
	},
	components: {
		ChannelCardView,
		MessageContentContainerView,
		UserCardView,
		QuoteMessageView,
		AvatorPop
	}
};
</script>
<style>
/* el-popover本身和App.vue平级 所以需要写在全局样式里 */
.customPopoverClass {
	padding: 0 !important;
	border-radius: 16px !important;
}
</style>
<style lang="css" scoped>
.container {
	display: flex;
	align-items: flex-start;
}

.message-time-container {
	width: 100%;
	display: flex;
	flex-direction: column;
	padding: 10px 20px;
	align-items: flex-start;
}

.message-time-container .time {
	width: 100%;
	margin-bottom: 14px;
	text-align: center;
	font-family: PingFang SC, PingFang SC;
	font-weight: 400;
	font-size: 12px;
	color: #b8b8b8;
	line-height: 20px;
}

.message-time-container.checked {
	background-color: #e7e7e7;
}

.message-avatar-content-container {
	display: flex;
	max-width: calc(100% - 60px);
	align-items: flex-start;
	overflow: hidden;
	/*max-height: 800px;*/
	text-overflow: ellipsis;
}
.avatar {
	width: 40px;
	height: 40px;
	border-radius: 6px;
}
.avatar-container {
	display: flex;
	padding-left: 2px;
	align-items: center;
}

.avatar-container input {
	margin-right: 20px;
	flex: 1;
}

.message-name-content-container {
	display: flex;
	flex-direction: column;
	justify-content: space-around;
}

.message-name-content-container .name {
	margin-left: 10px;
	color: #bdbdbd;
	font-size: 12px;
	margin-bottom: 2px;
}

.message-content-container.highlight {
	background-color: #dadada;
	opacity: 0.5;
	--in-arrow-color: #dadada !important;
}
.checkbox {
	display: none;
}
.icon {
	width: 20px;
	height: 20px;
	border: 1px solid $borderColor;
	background: transparent;
	border-radius: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 10px;
	cursor: pointer;
}
.icon > i::before {
	font-size: 20px;
}
::v-deep .el-popover__reference-wrapper {
	display: flex;
}
</style>
