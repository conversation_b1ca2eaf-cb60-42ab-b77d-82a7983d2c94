<template>
	<section>
		<div
			class="message-time-container"
			v-bind:class="{ checked: sharedPickState.messages.indexOf(message) >= 0 }"
		>
			<p v-if="index === 0 || this.message._showTime" class="time">{{ message._timeStr }}</p>
			<div
				class="message-content-container"
				v-bind:class="{ checked: sharedPickState.messages.indexOf(message) >= 0 }"
			>
				<input
					id="checkbox"
					v-if="sharedConversationState.enableMessageMultiSelection"
					type="checkbox"
					class="checkbox"
					:value="message"
					placeholder=""
					v-model="sharedPickState.messages"
				/>
				<div v-if="sharedConversationState.enableMessageMultiSelection" class="icon">
					<i
						class="coos-iconfont icon-xuanzhong"
						:style="{
							color: sharedPickState.messages.includes(message) ? '#0F45EA' : 'transparent'
						}"
					></i>
				</div>

				<div class="message-avatar-content-container">
					<!-- 文件的进度条有点特殊，有进度的消息的进度条有点特殊 -->
					<!--          <button>progress...</button>-->
					<LoadingView v-if="message.status === 0 && message.messageContent.type !== 5" />
					<i
						v-if="message.status === 2"
						class="yehuo-ion-close-circled"
						style="color: red"
						@click="resend"
					/>
					<div class="flex-column flex-align-end">
						<MessageContentContainerView
							:message="message"
							class="message-content-container-view"
							v-bind:class="{ highlight: highLight }"
							@contextmenu.prevent.native="openMessageContextMenu($event, message)"
						>
							<slot></slot>
							<QuoteMessageView
								v-if="quotedMessage"
								style="padding: 5px 0; max-width: 80%"
								:message="message"
								:quoted-message="quotedMessage"
								:enable-message-preview="true"
								:message-digest="this.message.messageContent.quoteInfo.messageDigest"
								:show-close-button="false"
							/>
						</MessageContentContainerView>
						<!-- <slot></slot> -->
					</div>
					<el-popover placement="right" trigger="click" popper-class="customPopoverClass">
						<AvatorPop :user-detail="userdetail" :user-id="userUid" />
						<img
							slot="reference"
							ref="userCardTippy"
							:name="'infoTrigger' + this.message.messageId"
							class="avatar"
							@click="onClickUserPortrait(message.from)"
							draggable="false"
							:src="message._from ? message._from.portrait : ''"
						/>
					</el-popover>
				</div>
			</div>
			<el-popover
				v-if="shouldShowMessageReceipt"
				@show="userShow"
				placement="right"
				width="220"
				v-model="popover"
				trigger="click"
			>
				<el-tabs ref="tabs" v-model="activeName" @tab-click="handleClick">
					<el-tab-pane :label="`未读(${unreadUsers.length})`" name="first">
						<ul class="infinite-list" style="overflow: auto">
							<li v-for="item of unreadUsers" :key="item.name" class="infinite-list-item">
								<div class="user-box">
									<img :src="item.portrait" alt="" class="user-box-img" />
									<div class="user-box-name">{{ item.displayName }}</div>
								</div>
							</li>
						</ul>
					</el-tab-pane>
					<el-tab-pane :label="`已读(${readUsers.length})`" name="second">
						<ul class="infinite-list" style="overflow: auto">
							<li v-for="item of readUsers" :key="item.name" class="infinite-list-item">
								<div class="user-box">
									<img :src="item.portrait" alt="" class="user-box-img" />
									<div class="user-box-name">{{ item.displayName }}</div>
								</div>
							</li>
						</ul>
					</el-tab-pane>
				</el-tabs>

				<p
					slot="reference"
					v-if="shouldShowMessageReceipt"
					class="receipt"
					@click="showMessageReceiptDetail"
				>
					{{ messageReceipt }}
				</p>
				<!-- <el-button  > {{ messageReceipt }}</el-button> -->
			</el-popover>
			<!-- <p v-if="shouldShowMessageReceipt" class="receipt" @click="showMessageReceiptDetail">
				{{ messageReceipt }}
			</p> -->
		</div>
	</section>
</template>

<script>
import UserCardView from '../../user/UserCardView.vue';
import AvatorPop from './AvatorPop.vue';
import Message from '../../../../wfc/messages/message';
import MessageContentContainerView from './MessageContentContainerView.vue';
import store from '../../../../store';
import LoadingView from '../../../common/LoadingView.vue';
import wfc from '../../../../wfc/client/wfc';
import ConversationType from '../../../../wfc/model/conversationType';
import { gte } from '../../../../wfc/util/longUtil';
import MessageReceiptDetailView from './MessageReceiptDetailView.vue';
import QuoteMessageView from './QuoteMessageView.vue';
import Config from '../../../../config';
import { getDetailInfo } from '@/api/modules/address-book';
import { getTenantConfig } from '@/api/modules/login';
import { get_token } from '@/utils/auth';
export default {
	name: 'NormalOutMessageContentView',
	props: {
		index: {
			type: Number,
			default: () => {
				return 0;
			}
		},
		message: {
			type: Message,
			required: true
		}
	},
	data() {
		return {
			sharedConversationState: store.state.conversation,
			sharedPickState: store.state.pick,
			highLight: false,
			readUsers: [],
			unreadUsers: [],
			activeName: 'first',
			popover: false,
			quotedMessage: null,
			userdetail: {},
			userUid: ''
		};
	},
	components: {
		QuoteMessageView,
		LoadingView,
		MessageContentContainerView,
		UserCardView,
		AvatorPop
		// TextMessageContentView,
	},
	mounted() {
		this.$parent.$on('contextMenuClosed', this.onContextMenuClosed);
		if (this.message.messageContent.quoteInfo) {
			let messageUid = this.message.messageContent.quoteInfo.messageUid;
			let msg = store.getMessageByUid(messageUid);
			if (!msg) {
				wfc.loadRemoteMessage(
					messageUid,
					ms => {
						msg = store._patchMessage(ms[0]);
						this.quotedMessage = msg;
					},
					err => {
						console.log('load remote message error', messageUid, err);
					}
				);
			} else {
				this.quotedMessage = msg;
			}
		}
	},
	beforeDestroy() {
		this.$parent.$off('contextMenuClosed', this.onContextMenuClosed);
	},

	methods: {
		onContextMenuClosed() {
			this.highLight = false;
		},
		onClickUserPortrait(userId) {
			this.userUid = userId;
			getDetailInfo(userId, { origin: 'im' }).then(res => {
				this.userdetail = res.result;
			});
			wfc.getUserInfo(userId, true);
		},
		closeUserCard() {
			this.$refs['userCardTippy']._tippy.hide();
		},
		resend() {
			wfc.deleteMessage(this.message.messageId);
			wfc.sendMessage(this.message);
		},
		openMessageContextMenu(event, message) {
			console.log(event, message);
			this.$parent.$emit('openMessageContextMenu', event, message);
			this.highLight = true;
		},
		handleClick(item) {
			// console.log(item);
		},
		userShow() {
			this.$refs.tabs.$emit('tab-click', { index: '0' });
			this.$refs.tabs.$el.querySelector('.el-tabs__active-bar').style.width = '54px';
		},
		showMessageReceiptDetail() {
			let conversation = this.message.conversation;
			if (conversation.type === ConversationType.Single) {
				this.popover = true;
				return;
			}

			let timestamp = this.message.timestamp;
			let readEntries = this.sharedConversationState.currentConversationRead;

			if (conversation.type === ConversationType.Group) {
				let groupMembers = wfc.getGroupMemberIds(conversation.target, false);
				if (!groupMembers || groupMembers.length === 0) {
					// do nothing
				} else {
					let readUserIds = [];
					let unreadUserIds = [];
					groupMembers.forEach(memberId => {
						let readDt = readEntries ? readEntries.get(memberId) : 0;
						if (readDt && gte(readDt, timestamp)) {
							readUserIds.push(memberId);
						} else if (memberId != this.message.from) {
							unreadUserIds.push(memberId);
						}
					});
					let readUsers = store.getUserInfos(readUserIds, conversation.target);
					let unreadUsers = store.getUserInfos(unreadUserIds, conversation.target);
					this.readUsers = readUsers;
					this.unreadUsers = unreadUsers;
					// this.activeName='unreadUsers'
					// this.$modal.show(
					// 	MessageReceiptDetailView,
					// 	{
					// 		readUsers: readUsers,
					// 		unreadUsers: unreadUsers
					// 	},
					// 	{
					// 		name: 'message-receipt-detail-modal',
					// 		width: 480,
					// 		height: 300,
					// 		clickToClose: true
					// 	},
					// 	{}
					// );
				}
			}
		}
	},

	computed: {
		messageReceipt() {
			let conversation = this.message.conversation;
			let timestamp = this.message.timestamp;
			let receiptDesc = '';
			let deliveries = this.sharedConversationState.currentConversationDeliveries;
			let readEntries = this.sharedConversationState.currentConversationRead;

			if (conversation.type === ConversationType.Single) {
				let readDt = readEntries ? readEntries.get(conversation.target) : 0;
				readDt = readDt ? readDt : 0;
				let recvDt = deliveries ? deliveries.get(conversation.target) : 0;
				recvDt = recvDt ? recvDt : 0;
				let isReceipt = localStorage.getItem('isReceipt');
				if (gte(readDt, timestamp)) {
					receiptDesc = isReceipt == 'true' ? '已读' : '';
				} else {
					receiptDesc = isReceipt == 'true' ? '未读' : '';
				}
			} else {
				let groupMembers = wfc.getGroupMemberIds(conversation.target, false);
				if (!groupMembers || groupMembers.length === 0) {
					receiptDesc = '';
				} else {
					let memberCount = groupMembers.length - 1;
					let readCount = 0;

					let readUserIds = [];
					let unreadUserIds = [];
					groupMembers.forEach(memberId => {
						let readDt = readEntries ? readEntries.get(memberId) : 0;
						if (readDt && gte(readDt, timestamp)) {
							readCount++;
							readUserIds.push(memberId);
						} else {
							unreadUserIds.push(memberId);
						}
					});
					receiptDesc = `${readCount}/${memberCount} 已读 `;
				}
			}
			return receiptDesc;
		},

		isDownloading() {
			return store.isDownloadingMessage(this.message.messageId);
		},

		shouldShowMessageReceipt() {
			return (
				this.sharedConversationState.isMessageReceiptEnable &&
				['FireRobot', Config.FILE_HELPER_ID].indexOf(this.message.conversation.target) < 0
			);
		}
	}
};
</script>

<style lang="css" scoped>
.message-time-container {
	width: 100%;
	display: flex;
	flex-direction: column;
	align-items: flex-end;
}

.message-time-container.checked {
	background-color: #e7e7e7;
}

.message-time-container .time {
	width: 100%;
	margin-bottom: 14px;
	text-align: center;
	font-family: PingFang SC, PingFang SC;
	font-weight: 400;
	font-size: 12px;
	color: #b8b8b8;
	line-height: 20px;
}

.message-time-container .receipt {
	margin-right: 70px;
	font-size: 12px;
	color: #b4b4b4;
}

.message-content-container {
	width: 100%;
	display: flex;
	padding: 10px 20px 4px 20px;
	justify-content: space-between;
	align-items: center;
	position: relative;
}

.message-avatar-content-container {
	display: flex;
	max-width: calc(100% - 60px);
	overflow: hidden;
	/*max-height: 800px;*/
	margin-left: auto;
	text-overflow: ellipsis;
	align-items: flex-start;
}

.message-avatar-content-container .avatar {
	width: 40px;
	height: 40px;
	border-radius: 6px;
}

.message-content-container-view {
	cursor: pointer;
	background: #95ec69 !important;
}

.message-content-container-view.highlight {
	background-color: #dadada;
	opacity: 0.5;
	--out-arrow-color: #dadada !important;
}

.infinite-list {
	height: 220px;
	overflow: hidden;
}

.user-box {
	width: 168px;
	height: 32px;
	border-radius: 0px 0px 0px 0px;
	padding: 4px 8px;
	display: flex;
	align-items: center;
}

.user-box-img {
	width: 24px;
	height: 24px;
	background: #3088ff;
	border-radius: 3px 3px 3px 3px;
	margin-right: 8px;
}

.user-box-name {
	font-family: PingFang SC, PingFang SC;
	font-weight: 400;
	font-size: 12px;
	color: $primaryTextColor;
	line-height: 20px;
}

.checkbox {
	display: none;
}

.icon {
	width: 20px;
	height: 20px;
	border: 1px solid $borderColor;
	background: transparent;
	border-radius: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
}

.icon > i::before {
	font-size: 20px;
}

.rightarrow:before {
	border-left-color: #95ec69;
}
</style>
