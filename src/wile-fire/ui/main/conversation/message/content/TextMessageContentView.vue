<template>
	<div
		class="text-message-container"
		:class="{
			out: message.direction === 0,
			textMessageContent: !isMulti,
			MultiContent: contentObj.title
		}"
	>
		<div v-if="contentObj.type && contentObj.title" class="title">
			{{ contentObj.title }}
			<span v-if="statusDictText" class="label" :class="status == -1 ? 'label-disabled' : ''">
				{{ statusDictText }}
			</span>
		</div>
		<span :class="{ 'item-mg-bt': isMulti }" v-if="contentObj.type === 'text'">
			{{ contentObj.content }}
		</span>
		<img
			:class="{ 'item-mg-bt': isMulti }"
			v-else-if="contentObj.type === 'img'"
			:src="contentObj.content"
			alt=""
			style="max-width: 400px"
		/>
		<div :class="{ 'item-mg-bt': isMulti }" v-else-if="contentObj.type === 'action'" class="button">
			<div
				v-for="(item, index) of contentObj.content.web"
				:key="index"
				class="button-item"
				:class="{ disabled: status === -1 }"
				@click="status !== -1 && toHandelButton(item)"
			>
				{{ item.text }}
			</div>
		</div>
		<div :class="{ 'item-mg-bt': isMulti }" v-else-if="contentObj.type === 'multi'">
			<TextMessageContentView
				v-for="(msg, i) of contentObj.content"
				:key="i"
				:message="msg"
				:isMulti="true"
				:status="status"
			></TextMessageContentView>
		</div>
		<v-md-preview
			v-else-if="contentObj.type === 'md'"
			:class="{ 'item-mg-bt': isMulti }"
			:text="contentObj.content"
		></v-md-preview>
		<!--		<div v-else-if="contentObj.type === 'md'" :class="{ 'item-mg-bt': isMulti }">-->
		<!--			<p-->
		<!--				class="text"-->
		<!--				v-html="contentObj.content"-->
		<!--				@mouseup="mouseUp"-->
		<!--				@contextmenu="preventContextMenuTextSelection"-->
		<!--			></p>-->
		<!--		</div>-->

		<!--		<ImageMessageContentView></ImageMessageContentView>-->
		<div
			:class="{ 'item-mg-bt': isMulti }"
			v-else
			class="text"
			@mouseup="mouseUp"
			@contextmenu="preventContextMenuTextSelection"
		>
			<slot></slot>
			<span v-html="this.textContent"></span>
		</div>
		<supperForm ref="supperForm" />
		<!-- 待办 -->
		<waitDetail ref="waitDetail"></waitDetail>
	</div>
</template>

<script>
import Message from '../../../../../wfc/messages/message';
import { parser as emojiParse } from '../../../../util/emoji';
import helper from '../../../../util/helper';
import request from '@/utils/request';
import TextMessageContentView from '@/wile-fire/ui/main/conversation/message/content/TextMessageContentView';
import { param2Obj } from '@/utils/index.js';
import waitDetail from '@/components/wait-detail/index.vue';
import { mapState } from 'vuex';
// import ImageMessageContentView from '@/wile-fire/ui/main/conversation/message/content/ImageMessageContentView';
//import {marked} from "marked";

export default {
	name: 'TextMessageContentView',
	components: {
		waitDetail,
		TextMessageContentView,
		// 供应商评价模板
		supperForm: () => {
			if (process.env.VUE_APP_OMIP === 'true') {
				return import('@/third-party/omip/views/supplier-management/evaluation/handle-form.vue');
			} else {
				return 'div';
			}
		}
		// ImageMessageContentView
	},
	props: {
		/**外部传入的拓展信息*/
		extra: {
			type: String,
			default: () => {
				return '';
			}
		},
		/**是否组合*/
		isMulti: {
			type: Boolean,
			default: () => {
				return false;
			}
		},
		message: {
			type: [Message, Object, String],
			required: true
		},
		/**是否系统信息*/
		isSystem: {
			type: Boolean,
			default: () => {
				return false;
			}
		}
	},
	data() {
		return {
			textSelected: false
		};
	},
	methods: {
		cleanAndFixInvalidImgTags(html) {
			if (!html || typeof html !== 'string') return '';
			html = html.replace(/<a\b[^>]*>(.*?)<\/a>/gi, '');
			html = html.replace(/(&nbsp;|\\u00A0|\\xa0|\s+)/gi, ' ');
			html = html.replace(/<img([^>]*)>/gi, (match, attrs) => {
				attrs = attrs.replace(/\s+/g, ' ').trim();
				attrs = attrs.replace(/(\bsrc=)([^"'][^ ]*)/gi, '$1"$2"');
				attrs = attrs.replace(/(\bstyle=)([^"'][^ ]*)/gi, '$1"$2"');
				attrs = attrs.replace(/(src="blob:http:)([^"]*)/gi, (m, p1, p2) => {
					return p1 + p2.replace(/ /g, '').replace('nbsp', '');
				});
				return `<img${attrs}>`;
			});
			html = html.replace(/<img([^>]*?)>/gi, '<img$1 />');
			return this.decodeHTMLEntities(html);
		},
		decodeHTMLEntities(text) {
			const textarea = document.createElement('textarea');
			textarea.innerHTML = text;
			return textarea.value;
		},
		/**处理按钮*/
		toHandelButton(item) {
			if (item.btn === 'api') {
				let url = /coos_api/.test(item.url) ? item.url.split('coos_api')[1] : item.url;
				request({
					url,
					method: 'get'
				}).then(res => {
					if (res.code === 200) {
						this.$message.success('操作成功！');
					} else {
						this.$message.error(res.message);
					}
				});
			} else if (item.btn === 'link') {
				if (/http/.test(item.url)) {
					window.open(item.url);
				} else if (/dialog/.test(item.url)) {
					//弹窗  - 供应商评价
					let { type } = param2Obj(item.url);
					if (type === 'supper') {
						let { formId, id, status } = param2Obj(item.url);
						// 3 已删除
						if (status == '3') {
							return;
						}
						this.$refs.supperForm.open({
							type: status == '2' ? 'detail' : 'newAdd',
							formId,
							detailId: id,
							title: status == '2' ? '查看' : '评价'
						});
					}
				} else {
					let url = /\/#\//.test(item.url) ? item.url.split('#')[1] : item.url;
					if (url.indexOf('wait-handle') !== -1) {
						let row = this.parseQueryString(url);
						if (this.openDetailType === '2') {
							const route = this.$router.resolve({ name: 'WaitDetail' });
							window.open(`${route.href}?blank_id=${row.id}`, '_blank');
						} else {
							this.$refs.waitDetail.openDetail(row.id);
						}
					} else {
						// 项目链接
						this.$router.push(url);
					}
				}
			}
		},
		parseQueryString(url) {
			// 获取查询字符串部分
			const queryString = url.split('?')[1];
			// 将查询字符串分割成键值对
			const params = queryString.split('&');
			// 构建对象
			const result = {};
			params.forEach(param => {
				const [key, value] = param.split('=');
				result[key] = decodeURIComponent(value);
			});
			return result;
		},
		mouseUp(event) {
			let selection = window.getSelection().toString();
			this.textSelected = !!selection;
		},
		preventContextMenuTextSelection(event) {
			if (!this.textSelected) {
				if (window.getSelection) {
					if (window.getSelection().empty) {
						// Chrome
						window.getSelection().empty();
					} else if (window.getSelection().removeAllRanges) {
						// Firefox
						window.getSelection().removeAllRanges();
					}
				} else if (document.selection) {
					// IE?
					document.selection.empty();
				}
			}
		}
	},

	computed: {
		...mapState('user', ['waitConfig']),
		// 打开待办的方式
		openDetailType() {
			return this.waitConfig.customerTaskMenu
				? this.waitConfig.customerTaskMenu.detailOpenType
				: '1';
		},
		/**拓展消息*/
		status() {
			try {
				let extra = JSON.parse(this.extra || this.message.messageContent.extra);
				return extra.flag;
			} catch (err) {
				return 1;
			}
		},
		/**拓展文字消息*/
		statusDictText() {
			try {
				let extra = JSON.parse(this.extra || this.message.messageContent.extra);
				return extra.flagTitle;
			} catch (err) {
				return '';
			}
		},
		/**组合消息的渲染*/
		contentObj() {
			let content =
				this.isMulti || this.isSystem ? this.message : this.message.messageContent.content;
			if (!this.isMulti && typeof content === 'string' && /^{/.test(content)) {
				try {
					content = JSON.parse(content);
				} catch (err) {
					// markdown不会被解析，手动处理
					if (/^{"type":"md"/gi.test(content)) {
						let jsonArr = content.split(/,"content":/);
						content = JSON.parse(jsonArr[0] + '}'); // 除了markdown消息对象
						// markdown消息
						content.content = jsonArr[1].replace(/}$/, '').replace(/"/gi, '');
					}
					// 组合里面含有markdown
					else if (/^{"type":"multi"/gi.test(content)) {
						let jsonArr = content.split(/\{"type":"md","content":/);
						content = jsonArr[0] + `{"type":"md","content":`;
						let endContentArr = jsonArr[1].split('}');
						let mdContent = endContentArr[0].replace(/"/gi, '');
						content = content + '""}' + endContentArr.slice(1).join('}');
						content = JSON.parse(content);
						content.content.forEach(item => {
							if (item.type === 'md') {
								item.content = mdContent;
							}
						});
					}
				}
			}
			return content;
		},
		/**原im消息的渲染*/
		textContent() {
			let content = '';
			if (!this.isMulti && !this.isSystem) {
				content = this.message.messageContent.digest(this.message).trim();
				let lines = content.replace(/\r\n/g, '\n').split('\n');
				if (lines.length > 1) {
					content = lines
						.map(line => `<span>${helper.escapeHtml(line)}</span></br>`)
						.reduce((total, cv, ci, arr) => total + cv, '');
				} else {
					content = helper.escapeHtml(content);
				}
				content = emojiParse(content);
				// tmp = marked.parse(tmp);
				if (content.indexOf('<img') >= 0) {
					content = content.replace(/<img/g, '<img style="max-width:400px;"');
					return content;
					// return this.cleanAndFixInvalidImgTags(content);
				}
			}
			return content;
			// return this.cleanAndFixInvalidImgTags(content);
		}
	}
};
</script>

<style lang="css" scoped>
.text-message-container {
	background-color: white;
	position: relative;
	border-radius: 5px;
	font-size: 14px;
	/*display: flex;*/
	/*align-items: center;*/
}

.dashBorder {
	border: 1px dashed var(--brand-6);
	border-radius: 6px;
	padding: 8px;
}

.item-mg-bt {
	margin-bottom: 8px;
}

.textMessageContent {
	margin: 0 10px;
	padding: 12px;
}

.title {
	margin-bottom: 8px;
	font-weight: 600;
}

.button {
	display: flex;
	align-items: center;
	flex-wrap: wrap;
}
.button-item {
	cursor: pointer;
	width: calc(50% - 16px);
	border: 1px solid var(--brand-6);
	margin: 0 8px 8px;
	border-radius: 6px;
	padding: 4px 8px;
	font-weight: 400;
	font-size: 14px;
	color: var(--brand-6);
	line-height: 22px;
	justify-content: center;
	align-items: center;
	display: flex;
}

.text-message-container >>> p {
	user-select: text;
	/*white-space: pre-line;*/
}

.text-message-container >>> code {
	background: #f5f5f5;
	display: inline-block;
	border-radius: 3px;
	padding: 0 5px;
	user-select: text;
}

.text-message-container.out {
	background-color: #a8bdff;
}

.text-message-container .text {
	color: #050505;
	font-size: 13px;
	line-height: 20px;
	/*max-height: 1000px;*/
	max-width: 400px;
	word-spacing: normal;
	word-break: break-word;
	overflow: hidden;
	display: inline-block;
	text-overflow: ellipsis;
	user-select: text;
}

/*style for v-html */
.text-message-container .text >>> img {
	max-width: 400px !important;
	display: inline-block;
}

.text-message-container .text >>> a {
	white-space: normal;
}

.text-message-container .text >>> .emoji {
	vertical-align: middle;
}
::v-deep .github-markdown-body {
	padding: 0;
}
.MultiContent {
	width: 500px;
}
.disabled {
	background: #f5f5f5;
	border: 1px solid #bccadb;
	color: #b9bdc9;
}
.label {
	margin-left: 10px;
	padding: 0 8px;
	background: #40c274;
	border-radius: 3px;
	font-family: PingFang SC, PingFang SC;
	font-weight: 400;
	font-size: 12px;
	color: #ffffff;
	line-height: 22px;
	text-align: left;
	font-style: normal;
	text-transform: none;
}
.label-disabled {
	color: #515b6e;
	background: #e6eaf2;
}
</style>
