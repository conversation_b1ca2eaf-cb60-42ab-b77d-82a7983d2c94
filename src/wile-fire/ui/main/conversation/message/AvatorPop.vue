<template>
	<div
		class="userCard"
		:style="{ backgroundImage: `url('@/assets/${rentThem}/address_book/dialogImg.png')` }"
	>
		<div class="userTop">
			<div class="userTop-avator">
				<img v-if="userDetail.avatarUrl" :src="userDetail.avatarUrl" />
				<!-- 此处进行userDetail.realname && !userDetail.avatarUrl判断为了避免 slice报错-->
				<label v-if="userDetail.realname && !userDetail.avatarUrl">
					{{ userDetail.realname.slice(-1) }}
				</label>
			</div>
			<div class="userTop-desc">
				<div class="userTop-descName">
					{{ userDetail.realname }}
					<span v-if="userDetail.sex === 1" class="coos-iconfont sex man">&#xe664;</span>
					<span v-else class="coos-iconfont sex woman">&#xe665;</span>
				</div>
				<div class="userTop-descPosition">{{ userDetail.labelTitles }}</div>
				<!-- <div style="display: flex; align-items: center">
					<span class="status-text">
						<span class="coos-iconfont status-icon">&#xe614;</span>
						差旅中
					</span>
					<span class="time">截止2024年04月15日</span>
				</div> -->
			</div>
		</div>
		<!-- <div class="userCenter">设置备注和描述</div> -->
		<div class="userBottom">
			<div v-for="item of list" :key="item.key" class="userBottom-detail-item">
				<span class="coos-iconfont detail-item-icon">{{ item.icon }}</span>
				<span class="detail-item-text">{{ userDetail[item.key] }}</span>
			</div>
			<div class="userBottom-btn" @click="toMessage">发送消息</div>
		</div>
	</div>
</template>
<script>
import store from '../../../../store';
import ConversationType from '@/wile-fire/wfc/model/conversationType';
import Conversation from '@/wile-fire/wfc/model/conversation';
import IpcSub from '../../../../ipc/ipcSub';
export default {
	name: '',
	data() {
		return {
			list: [
				{
					icon: '\ue653',
					key: 'email'
				},
				{
					icon: '\ue96c',
					key: 'phone'
				},
				{
					icon: '\ue604',
					key: 'departName'
				}
			]
		};
	},
	props: {
		userDetail: {
			type: Object,
			required: true
		},
		userId: {
			type: String,
			required: true
		}
	},
	watch: {
		userDetail() {
			console.log(this.userDetail);
		},
		userId() {
			console.log(this.userId);
		}
	},
	methods: {
		toMessage() {
			let conversation = new Conversation(ConversationType.Single, this.userId, 0);
			console.log(store.isConversationInCurrentWindow(conversation), 11111);
			if (store.isConversationInCurrentWindow(conversation)) {
				store.setCurrentConversation(conversation);
			} else {
				IpcSub.startConversation(conversation);
			}
			this.$emit('close');
			// 跳转到会话列表页
			if (this.$router.currentRoute.path !== '/wile-fire/home/<USER>') {
				this.$router.replace('/wile-fire/home/<USER>');
			}
		}
	}
};
</script>
<style scoped lang="css">
.userCard {
	width: 330px;
	box-shadow: 0px 2px 13px -1px rgba(209, 218, 233, 0.6);
	border-radius: 16px;
	background-size: 100% 100%;
	padding: 27px 17px 35px 19px;
	text-align: left;
	font-style: normal;
	text-transform: none;
}
.userTop {
	display: flex;
}
.userTop-avator {
	width: 64px;
	height: 64px;
	border-radius: 12px;
	background: #3088ff;
	margin-right: 9px;
}
.userTop-avator > img {
	width: 100%;
	height: 100%;
	border-radius: 12px;
}
.userTop-avator > label {
	display: inline-flex;
	font-weight: 800;
	font-size: 32px;
	color: #ffffff;
	line-height: 22px;
	width: 100%;
	height: 100%;
	justify-content: center;
	align-items: center;
}
.userTop-desc {
	display: flex;
	flex-direction: column;
}
.userTop-descName {
	height: 22px;
	font-weight: 800;
	font-size: 16px;
	color: $primaryTextColor;
	line-height: 22px;
}

.man {
	color: #3088ff;
}

.woman {
	color: #ffa88c;
	background: linear-gradient(162deg, #ffdcd8 0%, #ffa88c 100%);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
}
.userTop-descPosition {
	height: 20px;
	font-weight: 500;
	font-size: 12px;
	color: $textColor;
	line-height: 20px;
}
.status-text {
	display: inline-flex;
	align-items: center;
	justify-content: center;
	height: 20px;
	background: linear-gradient(180deg, #d7f5e3 0%, rgba(206, 255, 226, 0) 100%);
	border-radius: 23px;
	border: 1px solid;
	border-image: linear-gradient(180deg, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0)) 1 1;
	font-weight: 400;
	font-size: 10px;
	color: #26c88e;
	line-height: 20px;
	padding: 0 5px;
	margin-right: 16px;
}
.status-icon {
	font-size: 16px;
	color: #26c88e;
}
.time {
	display: inline-block;
	height: 20px;
	font-weight: 400;
	font-size: 12px;
	color: $subTextColor;
	line-height: 20px;
}
.userCenter {
	width: 100%;
	height: 32px;
	background: #f2f7fc;
	border-radius: 6px;
	margin: 18px 0 12px;
	font-weight: 400;
	font-size: 12px;
	color: $subTextColor;
	line-height: 20px;
}
.userBottom-detail-item {
	display: flex;
	margin-top: 12px;
	//align-items: center;
}
.detail-item-icon {
	color: #aab3c6;
	margin-right: 9px;
}
.detail-item-text {
	font-weight: 400;
	font-size: 14px;
	color: $primaryTextColor;
	line-height: 22px;
}

.userBottom-btn {
	width: 243px;
	height: 32px;
	background: var(--brand-6);
	border-radius: 6px;
	border: 1px solid var(--brand-6);
	font-weight: 400;
	font-size: 14px;
	color: #ffffff;
	line-height: 22px;
	display: flex;
	align-items: center;
	justify-content: center;
	margin: 33px auto 0 auto;
	cursor: pointer;
}
</style>
