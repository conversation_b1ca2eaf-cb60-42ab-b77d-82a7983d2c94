<template>
	<div class="notification-container">
		<p v-if="isCreateNotice" class="time">{{ message._timeStr }}</p>
		<p class="notification">{{ message.messageContent.digest(message) }}</p>
	</div>
</template>

<script>
import Message from '../../../../wfc/messages/message';
import CreateGroupNotification from '@/wile-fire/wfc/messages/notification/createGroupNotification';

export default {
	name: 'NotificationMessageContentView',
	props: {
		message: {
			type: Message,
			required: true
		}
	},
	computed: {
		/**判断当前消息通知类型是否属于创建群聊消息*/
		isCreateNotice() {
			return this.message.messageContent instanceof CreateGroupNotification;
		}
	},
	components: {}
};
</script>

<style lang="css" scoped>
.notification-container {
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
}
.notification-container .time {
	align-self: center;
	margin-bottom: -20px;
	color: #b4b4b4;
	height: 20px;
	font-size: 10px;
}
.notification-container .notification {
	font-family: PingFang SC, PingFang SC;
	color: #b8b8b8;
	margin: 20px 20px;
	font-size: 12px;
}
</style>
