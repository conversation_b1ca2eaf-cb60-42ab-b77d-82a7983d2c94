<template>
	<div class="quoted-message-container">
		<div class="quoted-message">
			<div
				class="media-content"
				v-if="[3, 6, 7].indexOf(this.quotedMessage.messageContent.type) >= 0"
			>
				<div class="name">{{ this.quotedMessage._from._displayName + ':' }}</div>
				<div class="imgContent">
					<img :src="mediaSrc" alt="" @click="onMessageClick" />
					<i
						v-if="showCloseButton"
						@click="cancelQuoteMessage"
						class="coos-iconfont icon-cuowu-fullcopy"
					></i>
				</div>
			</div>
			<div
				v-else-if="enableMessagePreview && this.quotedMessage.messageContent.type === 1"
				class="other-content"
			>
				<tippy
					:to="
						'messagePreview' +
						this.message.messageId +
						this.quotedMessage.messageId +
						enableMessagePreview
					"
					interactive
					:animate-fill="false"
					placement="left"
					distant="7"
					theme="light"
					animation="fade"
					trigger="click"
				>
					<PreviewQuotedMessageView :message="quotedMessage" />
				</tippy>
				<div class="tip-name">
					<p
						:name="
							'messagePreview' +
							this.message.messageId +
							this.quotedMessage.messageId +
							enableMessagePreview
						"
					>
						{{ this.quotedMessage._from._displayName + '：' }}
					</p>
					<i @click="zhidingclick(message)" class="coos-iconfont icon-zhiding icon"></i>
				</div>
				<p @click="onMessageClick" class="tip-name-desc">
					{{ this.quotedMessageStr }}
				</p>
			</div>
			<div v-else @click="onMessageClick" class="name">
				{{ this.quotedMessage._from._displayName + '：' }}
				<div class="p-icon">
					<p @click="onMessageClick" class="desc">{{ this.quotedMessageStr }}</p>
					<i
						v-if="showCloseButton"
						@click="cancelQuoteMessage"
						class="coos-iconfont icon-cuowu-fullcopy"
					></i>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import store from '../../../../store';
import MessageContentType from '../../../../wfc/messages/messageContentType';
import Message from '../../../../wfc/messages/message';
import PreviewQuotedMessageView from './PreviewQuotedMessageView.vue';
import { fs, isElectron, shell } from '../../../../platform';
import { downloadFile, previewMM } from '../../../../platformHelper';

export default {
	name: 'QuoteMessageView',
	props: {
		showCloseButton: {
			type: Boolean,
			required: false,
			default: false
		},
		// 原始消息
		message: {
			type: Message,
			required: false
		},
		// 被引用的消息
		quotedMessage: {
			type: Message,
			required: true
		},
		quotedMessageDigest: {
			type: String,
			required: false,
			default: ''
		},
		enableMessagePreview: {
			type: Boolean,
			required: false,
			default: false
		}
	},

	data() {
		return {
			shareConversation: store.state.conversation
		};
	},
	methods: {
		zhidingclick(messageid) {
			console.log(messageid);
		},
		cancelQuoteMessage() {
			this.$emit('cancelQuoteMessage');
		},

		onMessageClick() {
			if (!this.enableMessagePreview) {
				return;
			}
			if (this.quotedMessage) {
				switch (this.quotedMessage.messageContent.type) {
					case MessageContentType.Video:
					case MessageContentType.Image:
						previewMM(this.quotedMessage);
						break;
					case MessageContentType.File:
						this.downloadQuotedFile(this.quotedMessage);
						break;
					case MessageContentType.Text:
						// do nothing
						break;

					default:
						// TODO
						console.log('TODO: preview quotedMessage');
						break;
				}
			}
		},

		downloadQuotedFile(quotedFileMessage) {
			if (isElectron()) {
				let localPath = quotedFileMessage.messageContent.localPath;
				if (localPath && fs.existsSync(localPath)) {
					shell.openPath(localPath);
				} else {
					if (!store.isDownloadingMessage(quotedFileMessage.messageId)) {
						downloadFile(quotedFileMessage);
						store.addDownloadingMessage(quotedFileMessage.messageId);
					} else {
						// TODO toast 下载中
						console.log('file isDownloading');
					}
				}
			} else {
				downloadFile(quotedFileMessage);
			}
		}
	},
	computed: {
		quotedMessageStr() {
			let str = '';
			if (this.quotedMessage) {
				if (
					[MessageContentType.Image, MessageContentType.Video, MessageContentType.Sticker].indexOf(
						this.quotedMessage.messageContent.type
					) < 0
				) {
					str += this.quotedMessage.messageContent.digest(this.quotedMessage);
				}
				if (
					MessageContentType.RecallMessage_Notification === this.quotedMessage.messageContent.type
				) {
					str = '引用内容已撤回';
				}
			} else {
				str = this.quotedMessageDigest;
			}
			return str;
		},
		mediaSrc() {
			let src;
			let msgCnt = this.quotedMessage.messageContent;
			src = msgCnt.thumbnail ? 'data:video/jpeg;base64,' + msgCnt.thumbnail : msgCnt.remotePath;
			return src;
		}
	},
	components: {
		PreviewQuotedMessageView
	}
};
</script>

<style lang="css" scoped>
.quoted-message-container {
	display: flex;
	align-items: center;
	max-width: 100% !important;
}

.quoted-message {
	display: flex;
	max-width: 100%;
	background-color: #f3f4f6;
	flex-direction: column;
	border-radius: 5px;
	padding: 4px 8px;
	font-size: 13px;
	color: #737373;
	border-left: 2px solid #c8c9cc;
}

.quoted-message > p {
	max-width: 100%;
	max-height: 50px;
	flex: 1;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	overflow: hidden;
	text-overflow: ellipsis;
	outline: none;
}

.other-content p {
	max-width: 100%;
	max-height: 50px;
	flex: 1;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	overflow: hidden;
	text-overflow: ellipsis;
	outline: none;
	white-space: nowrap;
}

.media-content {
	max-width: 100%;
	display: flex;
	flex-direction: column;
}

.media-content p {
	width: 100px;
	max-height: 50px;
	max-width: 100px;
	text-overflow: ellipsis;
	overflow: hidden;
	white-space: nowrap;
}

.quoted-message img {
	border-radius: 3px;
	max-width: 100px;
	max-height: 100px;
}
.p-icon {
	display: flex;
	align-items: center;
}
.p-icon > i {
	margin-left: 33px;
	cursor: pointer;
}
.name {
	font-family: PingFang SC, PingFang SC;
	font-weight: 400;
	font-size: 12px;
	color: $textColor;
	line-height: 20px;
}
.desc {
	font-family: PingFang SC, PingFang SC;
	font-weight: 400;
	font-size: 14px;
	color: $textColor;
	line-height: 22px;
}
.imgContent {
	display: flex;
	align-items: center;
}
.imgContent > i {
	margin-left: 33px;
	cursor: pointer;
}
.tip-name {
	display: flex;
	align-items: center;
	justify-content: space-between;
	width: 100%;
}
.tip-name > p:first-child {
	font-family: PingFang SC, PingFang SC;
	font-weight: 400;
	font-size: 12px;
	color: $textColor;
	line-height: 20px;
}
.tip-name-desc {
	font-family: PingFang SC, PingFang SC;
	font-weight: 400;
	font-size: 14px;
	color: $textColor;
	line-height: 22px;
	white-space: pre-line !important;
}
.icon {
	cursor: pointer;
}
</style>
