<template>
	<div class="pick-conversation-container">
		<section class="conversation-list-panel">
			<div class="input-container">
				<i class="coos-iconfont icon-search icon"></i>
				<input type="text" :placeholder="'请输入姓名'" v-model="query" />
			</div>
			<section class="conversation-list-container">
				<!-- <div class="create-group" @click="showForwardByCreateConversationModal">
					<p>{{ $t('conversation.create_group') }}</p>
				</div> -->
				<div v-if="false" class="more" @click="openPopup">
					<span>更多联系人</span>
					<i class="coos-iconfont icon-nav-right more-icon"></i>
				</div>
				<p>{{ $t('conversation.recent_conversation') }}</p>
				<ul class="conversation-list" v-if="conversationInfos.length">
					<li v-for="(conversationInfo, index) in conversationInfos" :key="index">
						<div
							class="conversation-item"
							@click.stop="onConversationItemClick(conversationInfo.conversation)"
						>
							<input
								class="checkbox"
								v-bind:value="conversationInfo.conversation"
								type="checkbox"
								v-model="sharedPickState.conversations"
								placeholder=""
							/>
							<div class="header">
								<img class="avatar" :src="conversationInfo.conversation._target.portrait" alt="" />
							</div>
							<p class="title single-line">
								{{ conversationInfo.conversation._target._displayName }}
							</p>
						</div>
					</li>
				</ul>
			</section>
		</section>
		<section class="checked-conversation-list-container">
			<header>
				<div class="header-title">
					转发至:
					<span>已选{{ sharedPickState.conversations.length }}个联系人</span>
				</div>
				<span v-if="sharedPickState.conversations.length === 0">
					{{ $t('conversation.not_select_conversation') }}
				</span>
				<span v-else @click="removeClick()">
					<i class="coos-iconfont icon-shanchu icon"></i>
					清空
				</span>
			</header>
			<div class="content">
				<div
					class="picked-user-container"
					v-for="(conversation, index) in sharedPickState.conversations"
					:key="index"
				>
					<div class="picked-user">
						<img class="avatar" :src="conversation._target.portrait" alt="" />
						<span class="name single-line">{{ conversation._target._displayName }}</span>
					</div>
					<div @click="unpConversation(conversation)" class="unpick-button">
						<i class="coos-iconfont icon-guanbi1 icon"></i>
					</div>
				</div>
			</div>
			<ForwardMessageView
				ref="forwardMessageView"
				v-if="sharedPickState.conversations.length > 0"
				:forward-type="forwardType"
				:messages="messages"
			/>
			<footer>
				<button @click="cancel" class="cancel">{{ $t('common.cancel') }}</button>
				<button @click="confirm" class="confirm">{{ $t('common.send') }}</button>
			</footer>
		</section>
		<orgPersonnelDialog
			title="选择转发人员"
			:visible="visible"
			disable-all
			need-all-data
			:data-source="dataSource"
			@sure="sure"
			@close="close"
		></orgPersonnelDialog>
	</div>
</template>

<script>
import store from '../../../../../store';
import ForwardMessageView from './ForwardMessageView.vue';
import orgPersonnelDialog from '@/components/org-personnel-dialog/index.vue';
import ConversationType from '@/wile-fire/wfc/model/conversationType';
import { createConversation } from '@/utils/wile-fire-login';
import wfc from '@/wile-fire/wfc/client/wfc';

export default {
	name: 'ForwardMessageByPickConversationView',
	props: {
		forwardType: {
			// 可参考ForwardType
			type: Number,
			required: false
		},
		messages: {
			type: Array,
			required: true
		}
	},
	data() {
		return {
			dataSource: ['depart', 'user'],
			visible: false,
			sharedConversationState: store.state.conversation.currentConversationInfo,
			sharedConversation: store.state.conversation,
			sharedPickState: store.state.pick,
			query: '',
			sharedSearchState: store.state.search
		};
	},
	methods: {
		openPopup() {
			this.visible = true;
		},
		close() {
			this.visible = false;
		},
		sure(checkArr) {
			checkArr
				.map(item => item.id)
				.forEach(item => {
					let conversation = createConversation(ConversationType.Single, item, 0);
					wfc.getUserInfoEx(conversation.target, false, userInfo => {
						conversation._target = userInfo;
						store.pickOrUnpickConversation(conversation, true);
					});
				});
			this.visible = false;
		},
		removeClick() {
			this.sharedPickState.conversations.forEach(item => {
				store.pickOrUnpickConversation(item, false);
			});
		},
		onConversationItemClick(conversation) {
			console.log('conversation', conversation);
			store.pickOrUnpickConversation(conversation, true);
		},
		unpConversation(conversation) {
			store.pickOrUnpickConversation(conversation, false);
		},

		showForwardByCreateConversationModal() {
			this.sharedPickState.conversations.length = 0;
			this.$modal.hide('forward-by-pick-conversation-modal', {
				toCreateConversation: true,
				forwardType: this.forwardType,
				messages: this.messages
			});
		},

		cancel() {
			this.sharedPickState.conversations.length = 0;
			this.$modal.hide('forward-by-pick-conversation-modal', { confirm: false });
		},

		confirm() {
			let pickedConversations = [...this.sharedPickState.conversations];
			this.sharedPickState.conversations.length = 0;
			this.$modal.hide('forward-by-pick-conversation-modal', {
				confirm: true,
				groupTitle: this.sharedConversationState.conversation._target._displayName,
				conversations: pickedConversations,
				forwardType: this.forwardType,
				messages: this.messages,
				extraMessageText: this.$refs['forwardMessageView'].extraMessageText
			});
		}
	},

	computed: {
		conversationInfos() {
			if (this.query && this.query.trim()) {
				return store.filterConversation(this.query);
			} else {
				return this.sharedConversation.conversationInfoList.filter(item => {
					return item.conversation._target.type != 1;
				});
			}
		}
	},

	components: { orgPersonnelDialog, ForwardMessageView }
};
</script>

<style lang="scss" scoped>
.pick-conversation-container {
	display: flex;
	height: 100%;
	width: 100%;
}

.conversation-list-panel {
	display: flex;
	width: 286px;
	flex-direction: column;
	justify-content: flex-start;
	overflow: hidden;
	border-right: 1px solid #f0f0f0;
}

.conversation-list-panel .input-container {
	display: flex;
	padding: 10px 0 10px 15px;
	border-radius: 6px;
	width: 278px;
	height: 36px;
	background: #ffffff;
	border-radius: 6px;
	border: 1px solid $borderColor;
	align-items: center;
}

.conversation-list-panel .input-container input {
	height: 22px;
	font-family: PingFang SC, PingFang SC;
	font-weight: 400;
	font-size: 14px;
	color: $holderTextColor;
	line-height: 22px;
	font-style: normal;
	text-transform: none;
	flex: 1;
	text-align: left;
	outline: none;
	border: none;
}

.conversation-list-panel .create-group {
	background-color: #f7f7f7;
	height: 40px;
	font-size: 13px;
	padding-left: 15px;
	display: flex;
	align-items: center;
}

.conversation-list-panel .create-group:active {
	background-color: #e5e5e5;
}

.conversation-list-container {
	overflow-y: auto;
	@include noScrollBar;
}
.conversation-list-container > p {
	position: sticky;
	z-index: 1;
	height: 22px;
	font-family: PingFang SC, PingFang SC;
	font-weight: 800;
	font-size: 14px;
	color: $primaryTextColor;
	text-align: left;
	font-style: normal;
	text-transform: none;
	margin: 8px 0;
}

.conversation-item {
	width: 100%;
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: flex-start;
	width: 274px;
	height: 48px;
	margin: 1px 0;
	cursor: pointer;
}

.conversation-item:active {
	background-color: #d6d6d6;
}

.conversation-item .header {
	height: 100%;
}

.conversation-item .header .avatar {
	position: relative;
	width: 32px;
	height: 32px;
	display: inline-block;
	border-radius: 6px;
	margin: 8px 10px;
}

.conversation-item .title {
	font-family: PingFang SC, PingFang SC;
	font-weight: 400;
	font-size: 14px;
	color: $primaryTextColor;
	line-height: 22px;
	text-align: left;
	font-style: normal;
	text-transform: none;
}

.checkbox {
	margin-right: 0;
	width: 16px;
	height: 16px;
	cursor: pointer;
}

.checked-conversation-list-container {
	flex: 1;
	display: flex;
	flex-direction: column;
}

.checked-conversation-list-container header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 5px 12px 9px 16px;
}
.checked-conversation-list-container header .header-title {
	font-family: PingFang SC, PingFang SC;
	font-weight: 800;
	font-size: 14px;
	color: $primaryTextColor;
	line-height: 22px;
	text-align: left;
}
.checked-conversation-list-container header .header-title > span {
	font-family: PingFang SC, PingFang SC;
	font-weight: 400;
	font-size: 14px;
	color: $primaryTextColor;
	line-height: 22px;
	text-align: left;
}
.checked-conversation-list-container header h2 {
	font-size: 16px;
	font-weight: normal;
	margin-left: 30px;
}

.checked-conversation-list-container header > span {
	display: flex;
	font-family: PingFang SC, PingFang SC;
	font-weight: 400;
	font-size: 14px;
	color: $textColor;
	line-height: 22px;
	text-align: center;
	font-style: normal;
	text-transform: none;
	align-items: center;
	cursor: pointer;
}

.checked-conversation-list-container .content {
	height: 100%;
	flex: 1;
	display: flex;
	padding: 4px 6px 0 16px;
	flex-wrap: wrap;
	justify-content: flex-start;
	align-items: flex-start;
	align-content: flex-start;
	overflow: auto;
	scrollbar-color: #ffffff #ffffff;
}

.checked-conversation-list-container .content .picked-user-container {
	width: 100%;
	height: 56px;
	display: flex;
	column-count: 1;
	justify-content: space-between;
	align-items: center;
	margin: 4px 0;
}

.checked-conversation-list-container .content .picked-user-container .name {
	text-align: center;
	max-width: 80px;
	font-size: 12px;
}

.checked-conversation-list-container .content .picked-user-container .picked-user {
	position: relative;
	height: 56px;
	display: flex;
	align-items: center;
}

.checked-conversation-list-container .content .avatar {
	width: 32px;
	height: 32px;
	margin: 12px 8px 12px 12px;
	border-radius: 6px;
}

.checked-conversation-list-container .content .unpick-button {
	cursor: pointer;
	transition: all 0.3s inliner;
}
.checked-conversation-list-container .content .unpick-button:hover {
	transition: tran;
}
.checked-conversation-list-container footer {
	display: flex;
	justify-content: flex-end;
	align-items: center;
}

.checked-conversation-list-container footer button {
	width: 58px;
	height: 32px;
	background: #ffffff;
	border-radius: 6px 6px 6px 6px;
	border: 1px solid $borderColor;
	font-family: PingFang SC, PingFang SC;
	font-weight: 400;
	font-size: 14px;
	color: $primaryTextColor;
	line-height: 22px;
	text-align: center;
	font-style: normal;
	text-transform: none;
}

.checked-conversation-list-container footer button.confirm {
	width: 58px;
	height: 32px;
	background: var(--brand-6);
	border-radius: 6px;
	font-family: PingFang SC, PingFang SC;
	font-weight: 400;
	font-size: 14px;
	color: rgba(255, 255, 255, 0.9);
	line-height: 22px;
	text-align: center;
	font-style: normal;
	text-transform: none;
	margin-left: 12px;
}

.checked-conversation-list-container label {
	width: 100%;
	padding: 5px 10px;
	height: 30px;
}

.icon {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 16px;
	height: 16px;
	color: $subTextColor;
	margin-right: 5px;
	font-size: 16px;
}
.more {
	display: flex;
	justify-content: space-between;
	font-weight: 800;
	font-size: 14px;
	margin-top: 12px;
	cursor: pointer;
	color: #15224c;
	&-icon {
		font-size: 16px;
	}
}
</style>
