<template>
	<section class="forward-message-container">
		<div class="forward-message">
			<div v-if="forwardType === 0">
				<img
					v-if="[3, 6].indexOf(messages[0].messageContent.type) >= 0"
					:src="'data:video/jpeg;base64,' + messages[0].messageContent.thumbnail"
					alt=""
				/>
				<p v-else>
					{{ this.forwardMessageStr }}
				</p>
			</div>
			<div v-else>
				<p>
					{{ this.forwardMessageStr }}
				</p>
			</div>
		</div>
		<label>
			<input type="text" :placeholder="'留言'" v-model="extraMessageText" />
		</label>
	</section>
</template>

<script>
import MessageContentType from '../../../../../wfc/messages/messageContentType';
import ForwardType from './ForwardType';
import ConversationType from '../../../../../wfc/model/conversationType';
import store from '../../../../../store';
export default {
	name: 'ForwardMessageView',
	props: {
		forwardType: {
			// 可参考ForwardType
			type: Number,
			required: false
		},
		messages: {
			type: Array,
			required: true
		}
	},
	data() {
		return {
			extraMessageText: '',
			sharedConversationState: store.state.conversation.currentConversationInfo
		};
	},
	methods: {},
	computed: {
		forwardMessageStr() {
			let str;
			let firstMsg = this.messages[0];
			switch (this.forwardType) {
				case ForwardType.NORMAL:
					str = !firstMsg._from ? '' : firstMsg._from._displayName + ':';
					if (
						[MessageContentType.Image, MessageContentType.Video].indexOf(
							firstMsg.messageContent.type
						) < 0
					) {
						str += firstMsg.messageContent.digest(this.quotedMessage);
					}
					break;
				case ForwardType.ONE_BY_ONE:
					str = '[' + this.$t('conversation.forward_one_by_one') + ']';
					if (firstMsg.conversation.type === ConversationType.Single) {
						str += this.$t('conversation.user_message_records', [firstMsg._from._displayName]);
					} else {
						str += this.$t('conversation.group_message_records');
					}
					break;
				case ForwardType.COMPOSITE:
					str = '[' + this.$t('conversation.forward_composite') + ']';
					if (firstMsg.conversation.type === ConversationType.Single) {
						str += this.$t('conversation.user_message_records', [firstMsg._from._displayName]);
					} else {
						str += this.sharedConversationState.conversation._target._displayName;
						str += this.$t('conversation.group_message_records');
					}
					break;
				default:
					break;
			}
			return str;
		}
	}
};
</script>

<style lang="css" scoped>
.forward-message-container {
	width: 100%;
	padding-left: 16px;
}

.forward-message {
	display: flex;
	width: 100%;
	border-radius: 6px;
	min-height: 38px;
	max-height: 100px;
	overflow: hidden;
}
.forward-message > div {
	width: 100%;
}
.forward-message p {
	width: 100%;
	padding: 8px;
	border-radius: 5px;
	word-wrap: break-word;
	word-break: break-all;
	font-family: PingFang SC, PingFang SC;
	font-weight: 400;
	font-size: 14px;
	color: $textColor;
	line-height: 22px;
	font-style: normal;
	overflow: hidden;
	background: #f3f4f6;
	text-overflow: ellipsis;
}

.forward-message img {
	max-width: 200px;
	max-height: 200px;
	border-radius: 3px;
}

.forward-message-container label input {
	width: 100%;
	height: 40px;
	background: #ffffff;
	border-radius: 6px;
	border: 1px solid $borderColor;
	padding: 9px 0 9px 13px;
	margin: 12px 0 34px;
	font-family: PingFang SC, PingFang SC;
	font-weight: 400;
	font-size: 14px;
	color: $holderTextColor;
	line-height: 22px;
	text-align: left;
}
</style>
