<template>
	<div
		ref="message-input-container"
		class="message-input-container"
		:style="{ maxHeight: activeHeight ? '' : '200px', minHeight: activeHeight ? '' : '200px' }"
	>
		<div
			v-if="muted"
			style="
				width: 100%;
				height: 50px;
				margin-top: -2px;
				background: lightgrey;
				display: flex;
				flex-direction: row;
				justify-content: center;
				align-items: center;
			"
		>
			<p style="color: white">群禁言或者你被禁言</p>
		</div>
		<section
			v-else-if="!sharedConversationState.showChannelMenu"
			style="display: flex; flex-direction: column; flex: 1; position: relative"
		>
			<div class="tips" v-if="noHandleAffairNum > 0 && !close" @click="clickMark(1)">
				<span class="number">
					{{ noHandleAffairNum }}
				</span>
				<span class="tips-content">项工作内容未完成</span>
				<i class="coos-iconfont icon-guanbi1 close_icon" @click.stop="closeIcon"></i>
			</div>
			<section class="input-action-container">
				<emoji
					v-if="showEmojiDialog"
					v-click-outside="hideEmojiView"
					@onClickEmoji="onClickEmoji"
				></emoji>
				<ul>
					<!--  改造Start  -->
					<li
						v-for="(item, index) of utils"
						:key="index"
						:style="{ marginRight: item.right + 'px', cursor: 'pointer' }"
						@click="handleUtilBtn(item.name)"
					>
						<svg-icon
							v-if="item.type === 'svg'"
							:icon-class="item.class"
							:style="{ width: item.size + 'px', height: item.size + 'px' }"
						></svg-icon>
						<i
							v-else
							:id="item.id"
							:class="item.class"
							class="coos-iconfont"
							:style="{ fontSize: item.size || 20 + 'px', color: '#515B6E' }"
						></i>
					</li>
					<!--  改造End  -->

					<!--					<li v-if="!inputOptions['disableEmoji']">-->
					<!--						<i id="showEmoji" @click="toggleEmojiView" class="yehuo-ion-ios-heart" />-->
					<!--					</li>-->
					<!--  文件上传  -->
					<li v-if="!inputOptions['disableFile']">
						<i
							@click="pickFile('file')"
							class="coos-iconfont icon-dakai"
							style="font-size: 20px; color: #515b6e"
						/>
						<input
							ref="fileInput"
							@change="onPickFile($event)"
							class="yehuo-ion-android-attach"
							type="file"
							:accept="accept"
							style="display: none"
						/>
					</li>
					<li v-if="!inputOptions['disableScreenShot'] && sharedMiscState.isElectron">
						<div style="display: inline-block; text-align: center">
							<i id="screenShot" @click="screenShot(false)" class="yehuo-ion-scissors" />
							<i
								class="yehuo-ion-chevron-down"
								style="font-size: 10px; color: #494849; padding-left: 5px"
							/>
							<span @click="screenShot(true)" class="screen-shot-button">隐藏当前窗口截图</span>
						</div>
					</li>
					<li v-if="!inputOptions['disableHistory'] && sharedMiscState.isElectron">
						<i id="messageHistory" @click="showMessageHistory" class="yehuo-ion-android-chat" />
					</li>
					<li v-if="enablePtt">
						<i
							id="ptt"
							v-bind:class="{ active: isPttTalking }"
							@mousedown="requestPttTalk(true)"
							class="yehuo-ion-android-radio-button-on ptt-icon"
						/>
					</li>
					<!--  语音  -->
					<!--					<li>-->
					<!--						<i-->
					<!--							id="voice"-->
					<!--							v-bind:class="{ active: isRecording }"-->
					<!--							@mousedown="recordAudio(true)"-->
					<!--							class="icon-voice iconfont"-->
					<!--							style="font-size: 20px; color: #515b6e; margin-left: 8px"-->
					<!--						/>-->
					<!--					</li>-->
				</ul>
				<ul
					v-if="
						!inputOptions['disableVoip'] &&
						sharedContactState.selfUserInfo.uid !== conversationInfo.conversation.target
					"
				>
					<!--  改造Start  -->
					<!--					<div class="block">-->
					<!--						<div class="code">-->
					<!--							<i class="coos-iconfont icon-a-daimakuai3x"></i>-->
					<!--						</div>-->
					<!--						<span class="btn">发送代码</span>-->
					<!--					</div>-->

					<!--					<div class="block">-->
					<!--						<div class="huiyi">-->
					<!--							<i class="coos-iconfont icon-caidanlan-bangong-kuaisulaidianjilu"></i>-->
					<!--						</div>-->
					<!--						<span class="btn">快速会议</span>-->
					<!--					</div>-->
					<!--  改造End  -->

					<!--					<li v-if="!inputOptions['disableAudioCall']">-->
					<!--						<i @click="startAudioCall" class="yehuo-ion-ios-telephone" />-->
					<!--					</li>-->
					<!--					<li v-if="!inputOptions['disableVideoCall']">-->
					<!--						<i @click="startVideoCall" class="yehuo-ion-ios-videocam" />-->
					<!--					</li>-->
					<li
						v-if="
							!inputOptions['disableChannelMenu'] &&
							conversationInfo.conversation.type === 3 &&
							conversationInfo.conversation._target.menus &&
							conversationInfo.conversation._target.menus.length
						"
					>
						<i @click="toggleChannelMenu" class="yehuo-ion-android-menu" />
					</li>
				</ul>
				<!-- <ul :class="['mark', { active1: noHandleAffairNum > 0 }]" @click="clickMark"> -->
				<ul :class="['mark']">
					<li class="right-li" @click="clickMark(1)" style="margin-right: 12px">
						<i class="coos-iconfont icon-fuzhi icon"></i>
						<span style="margin-left: 6px">工作备忘</span>
						<i v-show="noHandleLoading" class="el-icon-loading loading-icon"></i>
						<div v-if="noHandleAffairNum > 0" class="active2"></div>
					</li>
					<li class="right-li" @click="clickMark(2)">
						<i class="coos-iconfont icon-shuji icon"></i>
						<span style="margin-left: 6px">纪要整理</span>
					</li>
				</ul>
			</section>
			<QuoteMessageView
				v-if="quotedMessage"
				style="padding: 10px 20px"
				v-on:cancelQuoteMessage="cancelQuoteMessage"
				:enable-message-preview="false"
				:quoted-message="quotedMessage"
				:show-close-button="true"
			/>
			<!--      @keydown.13="send($event)"-->
			<!--      @keydown.229="() => {}"-->
			<div
				@keydown="handleKeydown"
				ref="input"
				class="input"
				@paste="handlePaste"
				draggable="false"
				title="请输入消息"
				autofocus
				@input="onInput"
				@contextmenu.prevent="$refs.menu.open($event)"
				onmouseover="this.setAttribute('org_title', this.title); this.title='';"
				onmouseout="this.title = this.getAttribute('org_title');"
				v-on:tribute-replaced="onTributeReplaced"
				contenteditable="true"
			></div>
			<div class="send-box">
				<div class="send-btn" @click="sendMsg">发送</div>
				<el-popover ref="popover" placement="top-start" width="220" trigger="click">
					<div>
						<div class="send-select" @click="handleSendType(1)">
							<div class="sand-select-box">
								<i v-show="sendType === 1" class="coos-iconfont icon-dagou dagou"></i>
							</div>
							<div class="send-dec-text">按Enter键发送消息</div>
						</div>
						<div class="send-select" @click="handleSendType(2)">
							<div class="sand-select-box">
								<i v-show="sendType === 2" class="coos-iconfont icon-dagou dagou"></i>
							</div>
							<div class="send-dec-text">按Ctrl+Enter键发送消息</div>
						</div>
					</div>
					<div slot="reference" class="send-icon-box">
						<i class="el-icon-arrow-down send-icon"></i>
					</div>
				</el-popover>
			</div>
			<vue-context ref="menu" :lazy="true">
				<!--				<li>-->
				<!--					<a @click.prevent="handlePaste($event, 'menu')">-->
				<!--						{{ $t('common.paste') }}-->
				<!--					</a>-->
				<!--				</li>-->
				<li v-show="hasInputTextOrImage">
					<a @click.prevent="copy">
						{{ $t('common.copy') }}
					</a>
				</li>
				<li>
					<a @click.prevent="cut">{{ $t('common.cut') }}</a>
				</li>
			</vue-context>
		</section>
		<ChannelMenuView
			v-else
			:menus="conversationInfo.conversation._target.menus"
			:conversation="conversationInfo.conversation"
		></ChannelMenuView>
	</div>
</template>

<script>
import wfc from '../../../wfc/client/wfc';
import TextMessageContent from '../../../wfc/messages/textMessageContent';
import store from '../../../store';
import ClickOutside from 'vue-click-outside';
import Tribute from 'tributejs';
import '../../../tribute.css';
import ConversationType from '../../../wfc/model/conversationType';
import ConversationInfo from '../../../wfc/model/conversationInfo';
import GroupInfo from '../../../wfc/model/groupInfo';
import GroupMemberType from '../../../wfc/model/groupMemberType';
import QuoteInfo from '../../../wfc/model/quoteInfo';
import Draft from '../../util/draft';
import Mention from '../../../wfc/model/mention';
import { parser as emojiParse } from '../../util/emoji';
import QuoteMessageView from '../../main/conversation/message/QuoteMessageView';
import { fileFromDataUri } from '../../util/imageUtil';
import StickerMessageContent from '../../../wfc/messages/stickerMessageContent';
import { ipcRenderer, isElectron } from '../../../platform';
import { copyText } from '../../util/clipboard';
import EventType from '../../../wfc/client/wfcEvent';
import IpcEventType from '../../../ipcEventType';
import ChannelMenuView from './ChannelMenuView';
import pttClient from '../../../wfc/ptt/client/pttClient';
import TalkingCallback from '../../../wfc/ptt/client/talkingCallback';
import Config from '../../../config';
import SoundMessageContent from '../../../wfc/messages/soundMessageContent';
import BenzAMRRecorder from 'benz-amr-recorder';
import TypingMessageContent from '../../../wfc/messages/typingMessageContent';
import { currentWindow, fs } from '../../../platform';
import emoji from '@/wile-fire/ui/main/conversation/emoji';
export default {
	name: 'MessageInputView',
	components: {
		ChannelMenuView,
		QuoteMessageView,
		emoji
	},
	props: {
		noHandleLoading: {
			type: Boolean,
			default: () => {
				return false;
			}
		},
		activeHeight: {
			type: Boolean,
			default: () => {
				return false;
			}
		},
		conversationInfo: {
			type: ConversationInfo,
			required: true,
			default: null
		},
		inputOptions: {
			type: Object,
			required: false,
			default: () => ({})
		},
		muted: {
			type: [Boolean, null],
			required: true,
			default: false
		},
		//未完成事务数量
		noHandleAffairNum: {
			type: Number,
			default: 0
		}
	},
	data() {
		return {
			accept: '*',
			sharedConversationState: store.state.conversation,
			sharedContactState: store.state.contact,
			sharedMiscState: store.state.misc,
			showEmojiDialog: false,
			tribute: null,
			mentions: [],
			lastConversationInfo: null,
			storeDraftIntervalId: 0,
			tributeReplaced: false,
			enablePtt: wfc.isCommercialServer() && Config.ENABLE_PTT,
			amrRecorder: null,
			lastTypingMessageTimestamp: 0,

			isPttTalking: false,
			isRecording: false,
			utils: [
				{
					name: 'biaoqing',
					class: 'icon-xiaolian-line',
					right: 12,
					id: 'showEmoji'
				},
				// {
				// 	name: 'jietu',
				// 	class: 'icon-jianqie',
				// 	right: 2
				// },
				// {
				// 	name: 'down1',
				// 	class: 'down',
				// 	size: '10',
				// 	type: 'svg',
				// 	right: 11
				// },
				{
					name: 'image',
					class: 'icon-tupian2',
					right: 10
				}
				// {
				// 	name: 'doc',
				// 	class: 'icon-wendang1',
				// 	right: 14
				// },
				// {
				// 	name: 'date',
				// 	class: 'icon-calendar-copy-copy',
				// 	right: 13
				// },
				// {
				// 	name: 'dir',
				// 	class: 'icon-dakai',
				// 	right: 2
				// },
				// {
				// 	name: 'down',
				// 	class: 'down',
				// 	size: '10',
				// 	type: 'svg',
				// 	right: 11
				// },
				// {
				// 	name: 'message',
				// 	class: 'icon-tubiaozhizuomoban'
				// }
			],
			sendType: 1,
			close: false
		};
	},
	methods: {
		handleSendType(item) {
			this.sendType = item;
			this.$refs.popover.doClose();
		},
		onClickEmoji(emoji) {
			this.$refs['input'].innerHTML = this.$refs['input'].innerHTML + emoji;
		},
		closeIcon() {
			this.$emit('setClose');
			this.close = true;
		},
		/** 点击工作备忘录 */
		clickMark(e) {
			this.$emit('clickMark', e);
		},
		handleUtilBtn(type) {
			switch (type) {
				case 'biaoqing': {
					this.toggleEmojiView();
					break;
				}
				case 'image': {
					this.pickFile('img');
					break;
				}
			}
		},
		onTributeReplaced(e) {
			// 正常下面这两行应当就生效了，不知道为啥不生效，所以采用了后面的 trick
			e.detail.event.preventDefault();
			e.detail.event.stopPropagation();

			this.tributeReplaced = true;
		},
		canisend() {
			let target = this.conversationInfo.conversation._target;
			if (target instanceof GroupInfo) {
				let groupInfo = target;
				let groupMember = wfc.getGroupMember(groupInfo.target, wfc.getUserId());
				if (groupInfo.mute === 1) {
					return (
						[GroupMemberType.Owner, GroupMemberType.Manager, GroupMemberType.Allowed].indexOf(
							groupMember.type
						) >= 0 || groupMember.type === GroupMemberType.Allowed
					);
				}
			}

			return true;
		},

		cancelQuoteMessage() {
			this.conversationInfo._quotedMessage = null;
			store.quoteMessage(null);
		},

		onInput(e) {
			this.notifyTyping(TypingMessageContent.TYPING_TEXT);
		},

		notifyTyping(type) {
			if (
				[ConversationType.Single, ConversationType.Group].indexOf(
					this.conversationInfo.conversation.type
				) >= 0
			) {
				let now = new Date().getTime();
				if (now - this.lastTypingMessageTimestamp > 10 * 1000) {
					let typing = new TypingMessageContent(type);
					wfc.sendConversationMessage(this.conversationInfo.conversation, typing);
					this.lastTypingMessageTimestamp = now;
				}
			}
		},
		async handlePaste(e, source) {
			let text;
			e.preventDefault();
			console.log('-----', e);
			if ((e.originalEvent || e).clipboardData) {
				text = (e.originalEvent || e).clipboardData.getData('text/plain');
			} else {
				text = await navigator.clipboard.readText();
			}
			if (isElectron() && false) {
				let args = ipcRenderer.sendSync(IpcEventType.FILE_PASTE);
				if (args.hasImage) {
					document.execCommand('insertText', false, ' ');
					document.execCommand('insertImage', false, 'local-resource://' + args.filename);
					return;
				} else if (args.hasFile) {
					args.files.forEach(file => {
						store.sendFile(this.conversationInfo.conversation, file);
					});
					return;
				}
			} else {
				const dT = e.clipboardData || window.clipboardData;
				if (dT) {
					let fileList = dT.files;
					if (fileList.length > 0) {
						for (let i = 0; i < fileList.length; i++) {
							let file = fileList.item(i);
							if (file.type.indexOf('image') !== -1) {
								// image
								document.execCommand('insertImage', false, URL.createObjectURL(file));
								this.styleImageInEditor();
							} else {
								// file
								if (isElectron()) {
									if (fs.lstatSync(file.path).isDirectory()) {
										this.$notify({
											// title: '不支持',
											text: this.$t('conversation.not_support_send_folder'),
											type: 'warn'
										});
										break;
									}
								} else {
									// TODO 浏览器端，不能判断是否是文件夹
									if (file.size < 1024 && file.type === '') {
										this.$notify({
											// title: '不支持',
											text: this.$t('conversation.not_support_send_such_file'),
											type: 'warn'
										});
										break;
									}
								}
								store.sendFile(this.conversationInfo.conversation, file);
							}
						}
						return;
					}
				} else {
					const clipboardContents = await navigator.clipboard.read();
					for (const item of clipboardContents) {
						if (item.types.includes('image/png')) {
							const blob = await item.getType('image/png');
							document.execCommand('insertImage', false, URL.createObjectURL(blob));
							this.styleImageInEditor();
							return;
						}
					}
				}
			}

			if (text && text.trim()) {
				document.execCommand('insertText', false, text);
				// Safari 浏览器 execCommand 失效，可以采用下面这种方式处理粘贴
				// this.$refs.input.innerText += text;
			}
		},

		styleImageInEditor() {
			let imgs = this.$refs.input.getElementsByTagName('img');
			for (let img of imgs) {
				img.style.maxWidth = '100px';
				img.style.maxHeight = '100px';
			}
		},

		mention(groupId, memberId) {
			let displayName = wfc.getGroupMemberDisplayName(groupId, memberId);
			this.mentions.push({
				key: displayName,
				value: '@' + memberId
			});
			let text = this.$refs.input.innerText;
			let mentionValue;
			if (text.endsWith(' ')) {
				mentionValue = '@' + displayName + ' ';
			} else {
				mentionValue = ' @' + displayName + ' ';
			}
			document.execCommand('insertText', false, mentionValue);
		},

		insertText(text) {
			// this.$refs['input'].innerText = text;
			this.$refs.input.focus();
			document.execCommand('insertText', false, text);
		},

		copy() {
			let text = this.$refs['input'].innerText;
			if (text) {
				copyText(text);
			}
		},

		cut() {
			this.copy();
			this.$refs['input'].innerHTML = '';
		},
		handleKeydown(e) {
			if (this.sendType === 1 && e.key === 'Enter' && !e.ctrlKey && !e.shiftKey) {
				this.send(e);
			}
			if (this.sendType === 2 && e.key === 'Enter' && e.ctrlKey) {
				this.send(e);
			}
		},
		sendMsg() {
			this.send({ ctrlKey: false, preventDefault: () => {} });
		},
		async send(e) {
			if (this.tribute && this.tribute.isActive) {
				this.tributeReplaced = false;
				return;
			}

			// let text = this.$refs['input'].textContent;
			// if (!text.trim()) {
			//   return;
			// }
			// this.$refs['input'].textContent = '';
			// // 发送消息时，会话消息列表需要滚动到最后
			// store.setShouldAutoScrollToBottom(true)
			//
			// let textMessageContent = this.handleMention(text)
			// let conversation = this.conversationInfo.conversation;
			// wfc.sendConversationMessage(conversation, textMessageContent);
			//

			let input = this.$refs['input'];
			let message = input.innerHTML.trim();
			let conversation = this.conversationInfo.conversation;

			if (!conversation || !this.canisend() || !message) return;

			if (e.shiftKey) {
				// e.preventDefault();
				// this.refs.input.innerHTML = this.refs.input.innerHTML+ "<div><br></div>";
				if (window.getSelection) {
					let nextChar = window
						.getSelection()
						.focusNode.textContent.charAt(window.getSelection().focusOffset);
					if (!nextChar) {
						document.execCommand('InsertHTML', true, '<br>');
					}

					let selection = window.getSelection(),
						range = selection.getRangeAt(0),
						br = document.createElement('br');
					range.deleteContents();
					range.insertNode(br);
					range.setStartAfter(br);
					range.setEndAfter(br);
					// range.collapse(false);
					selection.removeAllRanges();
					selection.addRange(range);
					// return false;
				}
				return;
			}

			// if(!message.startsWith('<')){
			//     message = message.replace(/<br>/g, '\n').trim()
			// }

			let imgs = [...input.getElementsByTagName('img')];
			if (imgs) {
				for (const img of imgs) {
					if (img.className.indexOf('emoji') >= 0) {
						continue;
					}
					let src = img.src;
					let file;
					// 截图
					if (isElectron() && src.startsWith('local-resource')) {
						// 'local-resource://' + 绝对路径
						file = decodeURI(src.substring(17, src.length));
					} else {
						if (src.startsWith('blob:')) {
							let blob = await fetch(src).then(r => r.blob());
							file = new File([blob], new Date().getTime() + '.png');
						} else {
							file = fileFromDataUri(src, new Date().getTime() + '.png');
						}
					}
					this.$eventBus.$emit('uploadFile', file);
					store.setShouldAutoScrollToBottom(true);
					store.sendFile(this.conversationInfo.conversation, file);
					// 会影响 input.getElementsByTagName 返回的数组，所以上面拷贝了一下
					img.parentNode.removeChild(img);
				}
			}
			message = input.innerHTML.trim();
			message = message
				.replace(/<div><br><\/div>/g, '\n')
				.replace(/<br>/g, '\n')
				.replace(/<div>/g, '\n')
				.replace(/<\/div>/g, '')
				.replace(/<b>/g, '')
				.replace(/<\/b>/g, '')
				.replace(/&lt;/g, '<')
				.replace(/&gt;/g, '>')
				.replace(/&nbsp;/g, ' ')
				.replace(/&amp;/g, '&');

			//  自行部署表情时，需要手动替换下面的正则
			// TODO 在正则中使用变量，避免手动替换
			let p = `" src="${Config.emojiBaseUrl()}72x72\\/[0-9a-z-]+\\.png">`;
			let re = new RegExp(p, 'g');
			message = message.replace(/<img class="emoji" draggable="false" alt="/g, '').replace(re, '');

			if (message && message.trim()) {
				let textMessageContent = this.handleMention(message);
				let quotedMessage = this.sharedConversationState.quotedMessage;
				if (quotedMessage) {
					let quoteInfo = QuoteInfo.initWithMessage(quotedMessage);
					textMessageContent.setQuoteInfo(quoteInfo);
				}
				store.setShouldAutoScrollToBottom(true);
				wfc.sendConversationMessage(conversation, textMessageContent);
				this.$refs['input'].innerHTML = '';
			}

			input.innerHTML = '';
			store.quoteMessage(null);
			this.conversationInfo._quotedMessage = null;
			Draft.setConversationDraft(conversation, '', null, null);
			e.preventDefault();
		},

		toggleEmojiView() {
			this.showEmojiDialog = !this.showEmojiDialog;
			this.focusInput();
		},

		screenShot(hideCurrentWindow = false) {
			if (hideCurrentWindow) {
				currentWindow.hide();
			}
			ipcRenderer.send(IpcEventType.START_SCREEN_SHOT, {});
		},
		showMessageHistory() {
			let hash = window.location.hash;
			let url = window.location.origin;
			if (hash) {
				url = window.location.href.replace(hash, '#/conversation-message-history');
			} else {
				url += '/conversation-message-history';
			}
			let conversation = this.conversationInfo.conversation;
			ipcRenderer.send(IpcEventType.showConversationMessageHistoryPage, {
				url: url,
				type: conversation.type,
				target: conversation.target,
				line: conversation.line
			});
		},

		hideEmojiView(e) {
			if (e.target.id !== 'showEmoji') {
				this.showEmojiDialog = false;
			}
		},

		onSelectEmoji(emoji) {
			this.showEmojiDialog = false;
			if (emoji.data.indexOf('http') >= 0) {
				let sticker = new StickerMessageContent('', emoji.data, 200, 200);
				wfc.sendConversationMessage(this.conversationInfo.conversation, sticker);

				return;
			}

			this.$refs.input.focus();
			this.insertHTML(emojiParse(emoji.data));
			this.focusInput();
		},

		createElementFromHTML(htmlString) {
			let div = document.createElement('div');
			div.innerHTML = htmlString.trim();

			// Change this to div.childNodes to support multiple top-level nodes
			return div.firstChild;
		},

		insertHTML(html) {
			let sel, range;

			if (window.getSelection && (sel = window.getSelection())) {
				range = sel.getRangeAt(0);
				range.collapse(true);
				let imgEmoji = this.createElementFromHTML(html);
				range.insertNode(imgEmoji);

				// Move the caret immediately after the inserted span
				range.setStartAfter(imgEmoji);
				range.collapse(true);
				sel.removeAllRanges();
				sel.addRange(range);
			} else if (document.selection && document.selection.createRange) {
				document.selection.createRange().text = html;
			}
		},

		pickFile(type) {
			this.accept = type === 'img' ? 'image/*' : '*';
			this.$nextTick(() => {
				this.$refs['fileInput'].click();
			});
			this.notifyTyping(TypingMessageContent.TYPING_FILE);
		},

		startAudioCall() {
			let conversation = this.conversationInfo.conversation;
			this.$startVoipCall({ audioOnly: true, conversation: conversation });
		},

		startVideoCall() {
			let conversation = this.conversationInfo.conversation;
			this.$startVoipCall({ audioOnly: false, conversation: conversation });
		},

		toggleChannelMenu(toggle = true) {
			if (toggle) {
				this.$parent.$refs['conversationMessageList'].style.flexGrow = 1;
				this.storeDraft(this.lastConversationInfo);
			} else {
				if (this.$parent.messageInputViewResized) {
					this.$parent.$refs['conversationMessageList'].style.flexGrow = 0;
				}
			}
			store.toggleChannelMenu(toggle);
		},

		onPickFile(event) {
			// this.batchProcess(e.target.files[0]);
			let file = event.target.files[0];
			event.target.value = '';

			// TODO
			// var showMessage = snackbar.showMessage;
			//
			// if (!file || file.size === 0) {
			//   showMessage('You can\'t send an empty file.');
			//   return false;
			// }
			//
			// if (!file
			//     || file.size >= 100 * 1024 * 1024) {
			//   showMessage('Send file not allowed to exceed 100M.');
			//   return false;
			// }
			if (isElectron()) {
				if (
					new Date().getTime() - file.lastModified < 30 * 1000 &&
					file.path.indexOf('/var/folders') === 0
				) {
					this.$notify({
						text: ' 不支持的文件类型',
						type: 'warn'
					});
					return;
				}
			}
			this.$eventBus.$emit('uploadFile', file);
			store.sendFile(this.conversationInfo.conversation, file);
		},

		initEmojiPicker() {
			window.__twemoji_base_url__ = Config.emojiBaseUrl();
			if (this.conversationInfo.conversation.type === ConversationType.SecretChat) {
				this.$refs.emojiPicker.changeCategory({ name: 'Peoples' });
			}
		},

		initMention(conversation) {
			// TODO group, channel

			if (this.tribute) {
				this.tribute.detach(this.$refs['input']);
				this.tribute = null;
			}
			let type = conversation.conversationType;
			if (
				type === ConversationType.Single ||
				type === ConversationType.ChatRoom ||
				type === ConversationType.Channel
			) {
				return;
			}

			let mentionMenuItems = [];
			let groupInfo = wfc.getGroupInfo(conversation.target);
			mentionMenuItems.push({
				key: this.$t('conversation.all_people'),
				value: '@' + conversation.target,
				avatar: groupInfo.portrait ? groupInfo.portrait : Config.DEFAULT_GROUP_PORTRAIT_URL,
				//searchKey: '所有人' + pinyin.letter('所有人', '', null)
				searchKey: this.$t('conversation.all_people') + 'suoyouren' + 'syr'
			});

			let groupMemberUserInfos = store.getGroupMemberUserInfos(conversation.target, false);
			groupMemberUserInfos.forEach(e => {
				mentionMenuItems.push({
					key: e._displayName,
					value: '@' + e.uid,
					avatar: e.portrait,
					searchKey: e._displayName + e._pinyin + e._firstLetters
				});
			});

			this.tribute = new Tribute({
				values: mentionMenuItems,
				selectTemplate: item => {
					if (typeof item === 'undefined') return null;
					// if (this.range.isContentEditable(this.current.element)) {
					//     return '<span contenteditable="false"><a href="http://zurb.com" target="_blank" title="' + item.original.email + '">' + item.original.value + '</a></span>';
					// }
					this.mentions.push({ key: item.original.key, value: item.original.value });

					return '@' + item.original.key;
				},
				menuItemTemplate: function (item) {
					return (
						'<img width="24" height="24" src="' + item.original.avatar + ' "> ' + item.original.key
					);
				},
				noMatchTemplate: function () {
					return '<span style:"visibility: hidden;"></span>';
				},
				lookup: item => {
					return item.searchKey;
				},
				menuContainer: document.getElementById('conversation-content')
			});
			this.tribute.attach(this.$refs['input']);
		},

		handleMention(text) {
			let textMessageContent = new TextMessageContent();
			textMessageContent.content = text.trim();
			this.mentions.forEach(e => {
				if (text.indexOf(e.key) > -1) {
					if (e.value === '@' + this.conversationInfo.conversation.target) {
						textMessageContent.mentionedType = 2;
					} else {
						if (textMessageContent.mentionedType !== 2) {
							textMessageContent.mentionedType = 1;
							textMessageContent.mentionedTargets.push(e.value.substring(1));
						}
					}
				}
			});

			this.mentions.length = 0;
			return textMessageContent;
		},

		focusInput() {
			this.$nextTick(() => {
				if (this.$refs['input']) {
					this.$refs['input'].focus();
				}
			});
		},

		moveCursorToEnd(contentEditableDiv) {
			let range = document.createRange();
			range.selectNodeContents(contentEditableDiv);
			range.collapse(false);
			let sel = window.getSelection();
			sel.removeAllRanges();
			sel.addRange(range);
		},

		restoreDraft() {
			let draft = Draft.getConversationDraftEx(this.conversationInfo);
			if (!draft) {
				return;
			}
			store.quoteMessage(draft.quotedMessage);
			let input = this.$refs['input'];
			if (input.innerHTML.trim()) {
			} else {
				input.innerHTML = draft.text.replace(/ /g, '&nbsp').replace(/\n/g, '<br>');
				this.moveCursorToEnd(input);
			}
		},

		storeDraft(conversationInfo) {
			if (!this.$refs['input']) {
				return;
			}
			let draftText = this.$refs['input'].innerHTML.trim();
			let p = `" src="${Config.emojiBaseUrl()}72x72\\/[0-9a-z-]+\\.png">`;
			let re = new RegExp(p, 'g');
			draftText = draftText
				.replace(/<br>/g, '\n')
				.replace(/<div>/g, '\n')
				.replace(/<\/div>/g, '')
				.replace(/<div><\/div>/g, ' ')
				.replace(/&nbsp;/g, ' ')
				.replace(/<img class="emoji" draggable="false" alt="/g, '')
				.replace(re, '')
				.replace(/<img src="local-resource:.*">/g, '')
				.trimStart()
				.replace(/\s+$/g, ' ');

			let mentions = [];
			this.mentions.forEach(e => {
				let mention;
				/**
				 *  e.key: "13866666666"
				 *  e.value: "@q0H7q7MM"
				 */
				let start = draftText.indexOf('@' + e.key);
				let end = start + 1 + e.key.length;
				if (start > -1) {
					if (e.value === '@' + this.conversationInfo.conversation.target) {
						mention = new Mention(start, end, this.conversationInfo.conversation.target, true);
					} else {
						mention = new Mention(start, end, e.value.substring(1), false);
					}
					mentions.push(mention);
				}
			});

			let mentionCount = this.mentions ? this.mentions.length : 0;
			if (mentionCount > 0 && draftText.endsWith('@' + this.mentions[mentionCount - 1].key + ' ')) {
				// @的最后一个空格不能删除
				// do nothing
			} else {
				draftText = draftText.trimEnd();
			}

			let quoteInfo = null;
			if (conversationInfo._quotedMessage) {
				quoteInfo = QuoteInfo.initWithMessage(conversationInfo._quotedMessage);
			}

			if (draftText.length === 0 && !quoteInfo) {
				if (conversationInfo.draft !== '') {
					Draft.setConversationDraft(conversationInfo.conversation, draftText, quoteInfo, mentions);
				}
			} else {
				if (draftText !== conversationInfo.draft || (!conversationInfo.draft && quoteInfo)) {
					Draft.setConversationDraft(conversationInfo.conversation, draftText, quoteInfo, mentions);
				}
			}
		},

		onGroupMembersUpdate(groupId, groupMembers) {
			if (
				this.conversationInfo &&
				this.conversationInfo.conversation.type === ConversationType.Group &&
				this.conversationInfo.conversation.target === groupId
			) {
				this.initMention(this.conversationInfo.conversation);
				let groupMember = wfc.getGroupMember(groupId, wfc.getUserId());
				if (groupMember && groupMember.type === GroupMemberType.Muted) {
					this.muted = true;
				}
			}
		},

		requestPttTalk(request) {
			if (request) {
				let talkingCallback = new TalkingCallback();
				talkingCallback.onStartTalking = conversation => {
					this.isPttTalking = true;
					this.$notify({
						text: '请开始说话',
						type: 'info'
					});
				};
				talkingCallback.onRequestFail = (conversation, reason) => {
					this.$notify({
						text: '对讲请求失败: ' + reason,
						type: 'error'
					});
				};
				talkingCallback.onTalkingEnd = (conversation, reason) => {
					if (conversation.equal(this.conversationInfo.conversation)) {
						this.isPttTalking = false;
					}
				};
				pttClient.requestTalk(this.conversationInfo.conversation, talkingCallback);
				window.addEventListener('mouseup', this.handleMouseUp);
			} else {
				this.isPttTalking = false;
				pttClient.releaseTalk(this.conversationInfo.conversation);
			}
		},

		recordAudio(start) {
			this.notifyTyping(TypingMessageContent.TYPING_VOICE);
			if (start) {
				if (!this.amrRecorder) {
					this.amrRecorder = new BenzAMRRecorder();
					this.amrRecorder
						.initWithRecord()
						.then(() => {
							this.isRecording = true;
							this.amrRecorder.startRecord();
							this.$notify({
								text: '请开始说话',
								type: 'info'
							});
						})
						.catch(e => {
							this.$notify({
								text: '录音失败',
								type: 'error'
							});
							this.amrRecorder = null;
						});
				}
				window.addEventListener('mouseup', this.handleMouseUp);
			} else {
				this.isRecording = false;
				if (this.amrRecorder) {
					this.amrRecorder.finishRecord().then(() => {
						let duration = this.amrRecorder.getDuration();
						if (duration > 1) {
							let blob = this.amrRecorder.getBlob();
							let file = new File([blob], new Date().getTime() + '.amr');
							let content = new SoundMessageContent(file, null, Math.ceil(duration));
							wfc.sendConversationMessage(this.conversationInfo.conversation, content);
						} else {
							this.$notify({
								text: '录音时间太短',
								type: 'warn'
							});
						}
						this.amrRecorder = null;
					});
				}
			}
		},
		handleMouseUp() {
			if (this.isPttTalking) {
				this.requestPttTalk(false);
			} else if (this.isRecording) {
				this.recordAudio(false);
			}
			window.removeEventListener('mouseup', this.handleMouseUp);
		},

		setupConversationInput() {
			this.$refs.input.innerHTML = '';
			this.restoreDraft();
			this.initMention(this.conversationInfo.conversation);
			this.focusInput();
			// this.initEmojiPicker();
		}
	},

	activated() {
		if (!this.sharedConversationState.showChannelMenu) {
			this.restoreDraft();
			this.focusInput();
		}
	},

	deactivated() {
		if (!this.sharedConversationState.showChannelMenu) {
			this.storeDraft(this.lastConversationInfo);
			this.$refs['input'].innerHTML = '';
		}
	},

	mounted() {
		if (!this.sharedConversationState.showChannelMenu) {
			if (this.conversationInfo) {
				this.initMention(this.conversationInfo.conversation);
				// this.initEmojiPicker();
				this.restoreDraft();
			}
			this.focusInput();
		}
		this.lastConversationInfo = this.conversationInfo;

		if (isElectron()) {
			ipcRenderer.on('screenshots-ok', (event, args) => {
				if (args.filePath) {
					setTimeout(() => {
						document.execCommand('insertImage', false, 'local-resource://' + args.filePath);
						this.styleImageInEditor();
					}, 100);
				}
			});
		}
		this.storeDraftIntervalId = setInterval(() => {
			this.storeDraft(this.conversationInfo);
		}, 5 * 1000);
	},

	created() {
		wfc.eventEmitter.on(EventType.GroupMembersUpdate, this.onGroupMembersUpdate);
	},

	destroyed() {
		if (isElectron()) {
			ipcRenderer.removeAllListeners('screenshots-ok');
		}
		if (this.storeDraftIntervalId) {
			clearInterval(this.storeDraftIntervalId);
		}
		wfc.eventEmitter.removeListener(EventType.GroupMembersUpdate, this.onGroupMembersUpdate);
	},

	watch: {
		conversationInfo() {
			if (
				this.lastConversationInfo &&
				!this.conversationInfo.conversation.equal(this.lastConversationInfo.conversation)
			) {
				this.$nextTick(() => {
					if (this.sharedConversationState.showChannelMenu) {
						this.$parent.$refs['conversationMessageList'].style.flexGrow = 1;
						return;
					}
					if (this.$parent.messageInputViewResized) {
						this.$parent.$refs['conversationMessageList'].style.flexGrow = 0;
					}
					if (
						this.lastConversationInfo &&
						!this.conversationInfo.conversation.equal(this.lastConversationInfo.conversation)
					) {
						this.storeDraft(this.lastConversationInfo);
					}

					if (
						!this.muted &&
						this.conversationInfo &&
						(!this.lastConversationInfo ||
							!this.conversationInfo.conversation.equal(this.lastConversationInfo.conversation))
					) {
						this.setupConversationInput();
					}
					this.lastConversationInfo = this.conversationInfo;
				});
			} else {
				// 其他端更新了草稿
				// fixme
				// this.restoreDraft();
				this.lastConversationInfo = this.conversationInfo;
			}
		},
		muted: {
			handler(newValue) {
				if (!newValue) {
					this.$nextTick(() => {
						this.setupConversationInput();
					});
				} else {
					this.$parent.$refs['conversationMessageList'].style.flexGrow = 1;
				}
			}
		}
	},

	computed: {
		quotedMessage() {
			// side affect
			this.$refs.input && this.$refs.input.focus();
			return this.sharedConversationState.quotedMessage;
		},

		hasInputTextOrImage() {
			// TODO 监听input的输入情况
			return true;
		}
	},
	directives: {
		ClickOutside,
		focus
	}
};
</script>

<style lang="scss" scoped>
.message-input-container {
	min-height: 140px;
	flex: 1;
	display: flex;
	flex-direction: column;
	position: relative;
}

#emoji {
	position: absolute;
	bottom: 55px;
}

/*pls refer to https://vue-loader.vuejs.org/guide/scoped-css.html#child-component-root-elements*/
#emoji >>> .container-emoji {
	height: 280px;
}

.input-action-container {
	padding: 0 14px;
	height: 40px;
	min-height: 40px;
	display: flex;
	justify-content: space-between;
	align-items: center;
	position: relative;
	background: #ffffff;
	border-bottom: 1px solid #dce3e7;
	border-top: 1px solid #dce3e7;
}

.block {
	display: inline-flex;
	align-items: center;
	justify-content: center;

	.code {
		width: 15px;
		height: 11px;
		border: 1px solid #2b2f36;
		@include flexBox();

		& > i {
			color: #3088ff;
			font-size: 10px;
			line-height: 10px;
		}
	}

	.huiyi {
		margin-left: 21px;

		& > i {
			font-size: 10px;
		}
	}

	.btn {
		font-size: 12px;
		font-weight: 400;
		color: $textColor;
		line-height: 20px;
		margin-left: 4px;
		cursor: pointer;
	}
}

.input {
	flex: 1 1 auto;
	min-height: 100px;
	max-height: 100%;
	outline: none;
	padding: 9px 13px;
	overflow: auto;
	user-select: text;
	-webkit-user-select: text;
	font-size: 13px;
	background: #ffffff;
}

.input:empty:before {
	content: attr(title);
	font-family: PingFang SC, PingFang SC;
	font-weight: 400;
	font-size: 14px;
	color: $holderTextColor;
	line-height: 22px;
}

.input-action-container ul li {
	display: inline;
}

.right-li {
	display: inline-flex !important;
	padding: 2px 8px;
	border-radius: 6px;
	text-align: center;
	line-height: 24px;
	position: relative;
	cursor: pointer;
	background: #f5f7fa;
	align-items: center;
}

.loading-icon {
	font-size: 14px;
}

.input-action-container .mark {
	.icon {
		font-family: PingFang SC, PingFang SC;
		font-weight: 400;
		font-size: 12px;
		color: $textColor;
	}
}

.active1::after {
	content: '';
	display: block;
	top: 0px;
	right: 0px;
	position: absolute;
	width: 6px;
	height: 6px;
	background: #f5222d;
	border-radius: 50%;
	border: 1px solid #ffffff;
}

.active2 {
	display: block;
	top: 0px;
	right: 0px;
	position: absolute;
	width: 6px;
	height: 6px;
	background: #f5222d;
	border-radius: 50%;
	border: 1px solid #ffffff;
}

i {
	font-size: 24px;
	color: #000b;
	cursor: pointer;
}

i:hover {
	color: #3f64e4;
}

.input-action-container ul li .screen-shot-button {
	position: absolute;
	left: 0;
	top: 100%;
	display: none;
	padding: 5px 10px;
	font-size: 12px;
	background-color: #b8b8b8;
	border-radius: 5px;
	color: #fff;
}

.input-action-container ul li:hover .screen-shot-button {
	display: inline-block;
	width: 120px;
}

.tips {
	display: flex;
	align-items: center;
	cursor: pointer;
	position: absolute;
	top: -50px;
	right: 20px;
	width: 160px;
	height: 38px;
	padding: 0 6px;
	line-height: 38px;
	background: rgba(255, 255, 255, 0.6);
	box-shadow: 0px 4px 14px 0px rgba(0, 0, 0, 0.1);
	border-radius: 6px 6px 6px 6px;
	font-family: PingFang SC, PingFang SC;
	font-weight: 400;
	font-size: 14px;

	.number {
		cursor: pointer;
		color: var(--brand-6);
		margin-right: 2px;
		flex-shrink: 0;
	}

	.tips-content {
		flex: 1;
		@include aLineEllipse;
	}

	.close_icon {
		flex-shrink: 0;
		cursor: pointer;
		font-size: 12px;
		margin-left: 8px;
	}
}

.input img {
	width: auto;
	max-width: 100px;
	max-height: 100px;
}

@keyframes glow {
	0% {
		opacity: 1;
	}

	50% {
		opacity: 0.5;
	}

	100% {
		opacity: 1;
	}
}

.ptt-icon {
	color: #000b;
}

.ptt-icon.active {
	color: red;
	animation: glow 2s infinite;
}

.record-icon {
	color: #000b;
}

.record-icon.active {
	color: red;
	animation: glow 2s infinite;
}

>>> .emoji-picker {
	box-shadow: 5px 5px 20px 0 #c0c0c0;
	--ep-color-active: #3f64e4 !important;
}

// 表情弹窗样式调整
::v-deep .custom-emoji {
	border-radius: 12px !important;
	left: 10px;
	z-index: 666;
	bottom: 50px !important;
	background-color: #ffffff !important;
	width: 460px !important;
	box-shadow: 0px 2px 6px -1px rgba(0, 0, 0, 0.05), 0px 4px 8px 0px rgba(0, 0, 0, 0.06);

	#Categories {
		background: #ffffff !important;
	}

	.category.active {
		border-bottom-color: var(--brand-6) !important;
	}

	.grid-emojis {
		grid-template-columns: repeat(6, 17%) !important;
	}

	.container-emoji {
		height: 270px !important;
	}

	.emoji-c {
		font-size: 22px !important;
		line-height: 30px !important;
		width: 24px !important;
		height: 30px !important;
	}

	.container-search > input {
		outline: none;
	}
}

.quick-button-icon {
	cursor: pointer;
	margin-left: 12px;
	font-size: 24px;
}

.notes {
	padding: 5px 10px;
	background: #f5f7fa;
	border-radius: 6px;
	font-size: 12px;
	color: $textColor;
}
.send-box {
	cursor: pointer;
	position: absolute;
	bottom: 10px;
	right: 10px;
	display: flex;
	align-items: center;
	width: 88px;
	background: #ffffff;
	border-radius: 3px 3px 3px 3px;
	border: 1px solid #dce3e7;
	.send-btn {
		padding: 3px 15px;
		font-family: PingFang SC, PingFang SC;
		font-weight: 400;
		font-size: 14px;
		color: #2f446b;
		line-height: 22px;
		text-align: left;
		font-style: normal;
		text-transform: none;
	}
	.send-icon-box {
		width: 28px;
		height: 28px;
		border-radius: 0px 0px 0px 0px;
		border-left: 1px solid #dce3e7;
		display: flex;
		align-items: center;
		justify-content: center;
		.send-icon {
			font-size: 16px;
		}
	}
}
.send-dec-text {
	height: 20px;
	font-family: PingFang SC, PingFang SC;
	font-weight: 400;
	font-size: 14px;
	color: #2f446b;
	line-height: 20px;
}
.dagou {
	color: var(--brand-6);
	font-size: 16px;
}
.send-select {
	display: flex;
	align-items: center;
	cursor: pointer;
	height: 32px;
	.sand-select-box {
		width: 16px;
		cursor: pointer;
		border-radius: 0px 0px 0px 0px;
		margin-right: 8px;
	}
}
</style>
