<template>
	<div class="search-input-container">
		<div class="im-search">
			<svg-icon icon-class="search" class-name="im-search-icon"></svg-icon>
			<el-input
				id="searchInput"
				ref="input"
				v-model="params.keywords"
				@input="search"
				class="im-search-input"
				:placeholder="$t('common.search')"
				color="@d1d3db"
				autocomplete="off"
				@focus="showResult = true"
				@blur="blur"
				@keydown.esc="cancel"
				type="text"
			></el-input>
			<!--			<el-input-->
			<!--				id="searchInput"-->
			<!--				ref="input"-->
			<!--				v-model.trim="sharedSearchState.query"-->
			<!--				class="im-search-input"-->
			<!--				:placeholder="$t('common.search')"-->
			<!--				color="@d1d3db"-->
			<!--				autocomplete="off"-->
			<!--				@keydown.esc="cancel"-->
			<!--				type="text"-->
			<!--			></el-input>-->
		</div>
		<div v-loading="loading" v-if="params.keywords && showResult" class="result">
			<div
				v-for="(item, index) of result"
				:key="index"
				class="item"
				@click="toMessage(item.userId)"
			>
				<img v-if="item.avatarUrl" :src="item.avatarUrl" class="item-avatar" />
				<div v-else class="item-avatar">{{ item.realname.slice(-1) }}</div>
				<!--				<img v-else class="item-avatar" :src="assetsUrl + '/common/img/default-avatar.png'" />-->
				<div class="name">{{ item.realname }}</div>
			</div>
			<el-empty
				style="background: #ffffff; height: 100%"
				v-if="!loading && result.length === 0"
				:image="assetsUrl + '/common/img/no-search.png'"
			></el-empty>
		</div>
		<!--		<svg-icon-->
		<!--			v-if="showAddButton"-->
		<!--			@click="showCreateConversationModal"-->
		<!--			icon-class="add"-->
		<!--			class-name="im-search-icon add"-->
		<!--		></svg-icon>-->
	</div>
</template>

<script>
import store from '../../../store';
import Config from '../../../config';
import { getMemberList } from '@/api/modules/address-book';
import { debounce } from '@/utils';
import Conversation from '@/wile-fire/wfc/model/conversation';
import ConversationType from '@/wile-fire/wfc/model/conversationType';
import { assetsUrl } from '@/config';
import { createConversation } from '@/utils/wile-fire-login';
export default {
	name: 'SearchView',
	props: {
		showAddButton: {
			type: Boolean,
			default: true
		},
		searchType: {
			type: String,
			default: ''
		}
	},
	data() {
		return {
			assetsUrl,
			loading: true,
			params: {
				keywords: '',
				pageNo: 1,
				pageSize: 10
			},
			result: [],
			totalPages: 0, // 总页数
			sharedSearchState: store.state.search,
			sharedContactState: store.state.contact,
			showResult: true
		};
	},
	methods: {
		/**关闭搜索*/
		blur() {
			setTimeout(() => {
				this.showResult = false;
			}, 500);
		},
		/**去聊天*/
		toMessage(userId) {
			this.params.keywords = '';
			this.result = [];
			let conversation = createConversation(ConversationType.Single, userId, 0);
			console.log('点击', conversation);
			store.setCurrentConversation(conversation);
		},
		/**触发搜索*/
		search: debounce(
			function () {
				this.params.pageNo = 1;
				this.result = [];
				this.loading = true;
				getMemberList(this.params).then(res => {
					this.loading = false;
					if (res.code === 200) {
						this.result = this.result.concat(res.result.records);
						this.totalPages = res.result.pages;
					} else {
						this.$message.error(res.message);
					}
				});
			},
			500,
			false
		),
		showCreateConversationModal() {
			let successCB = users => {
				store.createConversation(users);
			};
			let users = this.sharedContactState.favContactList.concat(this.sharedContactState.friendList);
			users = users.filter(u => {
				return u.uid !== Config.FILE_HELPER_ID;
			});
			this.$pickContact({
				users,
				successCB,
				showOrganization: true
			});
		},
		cancel() {
			store.hideSearchView();
			this.$refs['input'].blur();
		}
	}
};
</script>

<style lang="scss" scoped>
.search-input-container {
	height: 54px;
	display: flex;
	justify-content: center;
	align-items: center;
	background-color: #ffffff;
	-webkit-app-region: drag;
	position: relative;
	padding: 0 20px;
	border-bottom: 1px solid #e6e6e6;
}

.im-search {
	height: 32px;
	width: 100%;
	display: flex;
	align-items: center;
	padding: 7px 12px;
	background: #f3f5f6;
	border-radius: 6px;
	&-icon {
		flex-shrink: 0;
		width: 16px;
		height: 16px;
		cursor: pointer;
	}
	&-input {
		flex: 1;
		height: 100%;
		width: 100%;
		border-radius: 6px;
		::v-deep .el-input__inner {
			height: 100%;
			width: 100%;
			background: #f3f5f6;
			border-radius: 6px;
			border: none;
			outline: none;
			padding: 0 0 0 4px;
			&:focus {
				box-shadow: none;
			}
			&::placeholder {
				font-size: 14px;
				font-weight: 400;
				color: $disabledTextColor;
				line-height: 22px;
			}
		}
	}
}
/*.search-input-container input {*/
/*	height: 25px;*/
/*	margin-left: 10px;*/
/*	margin-right: 10px;*/
/*	padding: 0 10px 0 20px;*/
/*	text-align: left;*/
/*	!* flex: 1; *!*/
/*	!* 兼容Firefox 52 *!*/
/*	width: 209px;*/
/*	border: 1px solid #e5e5e5;*/
/*	border-radius: 3px;*/
/*	outline: none;*/
/*	background-color: #eeeeee;*/
/*}*/

/*.search-input-container input:active {*/
/*	border: 1px solid #4168e0;*/
/*}*/

/*.search-input-container input:focus {*/
/*	border: 1px solid #4168e0;*/
/*}*/

/*.search-input-container i {*/
/*	position: absolute;*/
/*	left: 15px;*/
/*	!* 兼容Firefox 52 *!*/
/*	top: 50%;*/
/*	transform: translate(0, -50%);*/
/*}*/

.add {
	width: 20px;
	height: 20px;
	margin-left: 10px;
}
.result {
	position: absolute;
	top: 54px;
	left: 0;
	width: 100%;
	height: 100vh;
	background: #f3f3f3e5;
	z-index: 666;
	.item {
		display: flex;
		align-items: center;
		background: #ffffff;
		padding: 12px 16px;
		cursor: pointer;
		&-avatar {
			width: 40px;
			height: 40px;
			border-radius: 6px;
			margin-right: 8px;
			display: flex;
			align-items: center;
			justify-content: center;
			background: var(--brand-6);
			color: #ffffff;
		}
		.name {
			font-weight: 500;
			font-size: 14px;
			color: $primaryTextColor;
			line-height: 22px;
		}
	}
}
</style>
