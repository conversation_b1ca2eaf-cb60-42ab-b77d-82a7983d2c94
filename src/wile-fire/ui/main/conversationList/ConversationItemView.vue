<template>
	<div
		class="conversation-item-container"
		@dragover="dragEvent($event, 'dragover')"
		@dragleave="dragEvent($event, 'dragleave')"
		@dragenter="dragEvent($event, 'dragenter')"
		@drop="dragEvent($event, 'drop')"
		@click="showConversation"
		v-bind:class="{
			drag: dragAndDropEnterCount > 0,
			active:
				shareConversationState.currentConversationInfo &&
				shareConversationState.currentConversationInfo.conversation.equal(source.conversation),
			top: source.top,
			highlight:
				shareConversationState.contextMenuConversationInfo &&
				shareConversationState.contextMenuConversationInfo.conversation.equal(source.conversation)
		}"
		@contextmenu.prevent="showConversationInfoContextMenu"
	>
		<div class="conversation-item">
			<div class="header">
				<img class="avatar" draggable="false" :src="portrait" alt="" />
				<em v-if="unread > 0" class="badge" v-bind:class="{ silent: source.isSilent }">
					{{ unread > 99 ? '···' : unread }}
				</em>
			</div>
			<div class="content-container">
				<div class="title-time-container">
					<i
						v-if="source.conversation.type === 5"
						class="yehuo-ion-android-lock"
						style="padding-right: 5px"
					></i>
					<div
						v-if="isOrganizationGroupConversation"
						style="display: flex; align-items: center; max-width: calc(100% - 60px)"
					>
						<h2 class="title single-line">{{ conversationTitle }}</h2>
						<p
							class="single-line"
							style="
								background: #3f64e4;
								border-radius: 2px;
								color: white;
								padding: 1px 2px;
								font-size: 9px;
							"
						>
							官方
						</p>
					</div>
					<h2 v-else class="title single-line">{{ conversationTitle }}</h2>
					<p class="time single-line">{{ source._timeStr }}</p>
				</div>
				<div class="content">
					<p class="draft single-line" v-if="shouldShowDraft" v-html="draft"></p>
					<p
						class="draft single-line"
						v-else-if="shouldShowVoipStatus"
						v-html="voipOngoingDesc"
					></p>
					<p class="last-message-desc single-line" v-else>
						<i v-if="unreadMention > 0">[有人@我]</i>
						{{ lastMessageContent }}
					</p>
					<i v-if="source.isSilent" class="yehuo-ion-android-notifications-off"></i>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import store from '../../../store';
import Draft from '../../util/draft';
import FileMessageContent from '../../../wfc/messages/fileMessageContent';
import Message from '../../../wfc/messages/message';
import wfc from '../../../wfc/client/wfc';
import NotificationMessageContent from '../../../wfc/messages/notification/notificationMessageContent';
import Config from '../../../config';
import ConversationType from '../../../wfc/model/conversationType';
import GroupType from '../../../wfc/model/groupType';
import { getItem } from '@/utils/localstorage';

export default {
	name: 'ConversationItemView',
	props: {
		source: {
			type: Object,
			required: true
		}
	},
	data() {
		return {
			dragAndDropEnterCount: 0,
			shareConversationState: store.state.conversation,
			groupPortrait: Config.DEFAULT_GROUP_PORTRAIT_URL
		};
	},
	mounted() {
		try {
			if (!store.state.conversation.currentConversationInfo) {
				store.setCurrentConversationInfo(this.source);
			}
		} catch (e) {
			store.setCurrentConversationInfo(this.source);
		}
	},
	methods: {
		dragEvent(e, v) {
			if (v === 'dragenter') {
				this.dragAndDropEnterCount++;
			} else if (v === 'dragleave') {
				this.dragAndDropEnterCount--;
			} else if (v === 'drop') {
				this.dragAndDropEnterCount--;
				let length = e.dataTransfer.files.length;
				if (length > 0 && length <= 5) {
					for (let i = 0; i < length; i++) {
						this.$eventBus.$emit('uploadFile', e.dataTransfer.files[i]);
						store.sendFile(this.source.conversation, e.dataTransfer.files[i]);
					}
				} else {
					// TODO
					let url = e.dataTransfer.getData('URL');
					if (url) {
						store.sendFile(this.source.conversation, url);
					} else {
						let text = e.dataTransfer.getData('text');
						if (text.startsWith('{')) {
							let obj = JSON.parse(text);
							let file = new FileMessageContent(null, obj.url, obj.name, obj.size);
							let message = new Message(this.source.conversation, file);
							wfc.sendMessage(message);
						}
					}
				}
			} else if (v === 'dragover') {
				// If not st as 'copy', electron will open the drop file
				e.dataTransfer.dropEffect = 'copy';
			}
		},
		// imgUrlAlt(e) {
		// 	if (this.source.conversation.type === ConversationType.Group) {
		// 		e.target.src = Config.DEFAULT_GROUP_PORTRAIT_URL;
		// 	} else {
		// 		e.target.src = Config.DEFAULT_PORTRAIT_URL;
		// 	}
		// },

		showConversation() {
			store.setCurrentConversationInfo(this.source);
			if (this.unread > 0) {
				wfc.clearConversationUnreadStatus(this.source.conversation);
			}
			// this.refreshGroupPortrait();
		},
		showConversationInfoContextMenu(event) {
			this.$eventBus.$emit('showConversationContextMenu', event, this.source);
		}
	},
	computed: {
		conversationTitle() {
			let info = this.source;
			if (info.conversation._target) {
				return info.conversation._target._displayName;
			}
			return '';
		},

		isOrganizationGroupConversation() {
			let info = this.source;
			if (
				info.conversation.type === ConversationType.Group &&
				info.conversation._target &&
				info.conversation._target.type === GroupType.Organization
			) {
				return true;
			}
			return false;
		},
		shouldShowDraft() {
			if (
				this.shareConversationState.currentConversationInfo &&
				this.shareConversationState.currentConversationInfo.conversation.equal(
					this.source.conversation
				)
			) {
				return false;
			}
			if (this.source.unreadCount.unreadMention + this.source.unreadCount.unreadMentionAll > 0) {
				return false;
			}
			let draft = Draft.getConversationDraftEx(this.source);
			return draft.text.trim() !== '' || draft.quotedMessage !== null;
		},

		shouldShowVoipStatus() {
			return this.source._isVoipOngoing;
		},

		draft() {
			let draft = Draft.getConversationDraftEx(this.source);
			let draftText = `<em>[${this.$t('common.draft')}]</em>` + draft.text;
			draftText = draftText.replace(/<img [:a-zA-Z0-9_+; ,\-=\/."]+>/g, '[图片]');
			draftText = draftText.replace(/&nbsp;/g, ' ');
			draftText = draftText.replace(/<br>/g, '');
			if (draft.quotedMessage) {
				draftText += '...';
			}
			return draftText;
		},

		voipOngoingDesc() {
			let voipStatus = `<em>[音视频通话进行中]</em>`;
			return voipStatus;
		},

		lastMessageContent() {
			let conversationInfo = this.source;
			if (conversationInfo.lastMessage && conversationInfo.lastMessage.messageContent) {
				let senderName = '';
				if (
					conversationInfo.conversation.type === 1 &&
					conversationInfo.lastMessage.direction === 1 &&
					!(conversationInfo.lastMessage.messageContent instanceof NotificationMessageContent)
				) {
					if (conversationInfo.lastMessage._from) {
						senderName = conversationInfo.lastMessage._from._displayName + ': ';
					} else {
						conversationInfo.lastMessage = store._patchMessage(conversationInfo.lastMessage, 0);
						if (conversationInfo.lastMessage._from) {
							senderName = conversationInfo.lastMessage._from._displayName + ': ';
						} else {
							senderName = '<' + conversationInfo.lastMessage.from + '>: ';
						}
					}
				}
				let content;
				let messageContent = conversationInfo.lastMessage.messageContent.content;
				if (typeof messageContent === 'string' && /^{/.test(messageContent)) {
					try {
						content = JSON.parse(messageContent).title;
					} catch (err) {
						if (/^{"type":"md"/gi.test(messageContent)) {
							let jsonArr = messageContent.split(/,"content":/);
							let contentObj = JSON.parse(jsonArr[0] + '}'); // 除了markdown消息对象
							contentObj.content = jsonArr[1]; // markdown消息
							content = contentObj.title;
						}
						// 组合里面含有markdown
						else if (/^{"type":"multi"/gi.test(messageContent)) {
							let jsonArr = messageContent.split(/\{"type":"md","content":/);
							let newContent = jsonArr[0] + `{"type":"md","content":`;
							let endContentArr = jsonArr[1].split('}');
							let mdContent = endContentArr[0];
							newContent = newContent + '""}' + endContentArr.slice(1).join('}');
							newContent = JSON.parse(newContent);
							newContent.content.forEach(item => {
								if (item.type === 'md') {
									item.content = mdContent;
								}
							});
							content = newContent.title;
						} else {
							content =
								senderName +
								conversationInfo.lastMessage.messageContent.digest(conversationInfo.lastMessage);
						}
					}
				} else {
					content =
						senderName +
						conversationInfo.lastMessage.messageContent.digest(conversationInfo.lastMessage);
				}
				return content;
			} else {
				return '';
			}
		},

		unread() {
			let conversationInfo = this.source;
			let unreadCount = conversationInfo.unreadCount;
			return unreadCount
				? unreadCount.unread + unreadCount.unreadMention + unreadCount.unreadMentionAll
				: 0;
		},
		unreadMention() {
			let conversationInfo = this.source;
			let unreadCount = conversationInfo.unreadCount;
			return unreadCount ? unreadCount.unreadMention + unreadCount.unreadMentionAll : 0;
		},

		portrait() {
			let info = this.source;
			if (info.conversation.type === ConversationType.Group) {
				if (info.conversation._target.portrait) {
					return info.conversation._target.portrait;
				} else {
					return this.groupPortrait;
				}
			} else {
				return info.conversation._target.portrait;
			}
		}
	}
};
</script>

<style scoped lang="scss">
.conversation-item-container {
	padding: 2px 10px;
	background-color: #ffffff;
	&:hover {
		.conversation-item {
			background-color: #f7f7f7;
		}
	}
}

//.conversation-item-container.drag {
//	border: 1px solid #4168e0;
//}

.conversation-item-container.active {
	& > .conversation-item {
		background: linear-gradient(90deg, var(--brand-2) 0%, var(--brand-1) 100%) !important;
		//background: linear-gradient(90deg, #d5e3ff 0%, #e8f4ff 100%, #e5f3ff 100%) !important;
	}
	/*background-color: #dedede;*/
}

.conversation-item-container.top {
	& > .conversation-item {
		background-color: #f7f7f7;
	}
}

//.conversation-item-container.highlight {
//	box-shadow: 0 0 0 1px #4168e0 inset;
//	z-index: 100;
//}

.conversation-item-container.active.top {
	& > .conversation-item {
		background-color: #d3d3d3;
	}
}

.conversation-item {
	width: 100%;
	height: 70px;
	display: flex;
	/*border-bottom: 1px solid #eeeeee;*/
	align-items: center;
	justify-content: center;
	border-radius: 9px;
	padding: 0 8px;
	cursor: pointer;
}

.header {
	height: 100%;
	padding: 10px 12px 10px 0;
	margin-right: 2px;
	position: relative;
}

.header .avatar {
	position: relative;
	width: 40px;
	height: 40px;
	min-width: 40px;
	min-height: 40px;
	background: #ffffff;
	top: 50%;
	transform: translateY(-50%);
	border-radius: 6px;
}

.header .badge {
	position: absolute;
	color: white;
	font-size: 10px;
	background-color: red;
	border-radius: 8px;
	min-width: 16px;
	height: 16px;
	padding: 0 5px;
	line-height: 16px;
	font-style: normal;
	text-align: center;
	right: 8px;
	top: 8px;
	vertical-align: center;
}

.header .badge.silent {
	width: 8px;
	height: 8px;
	min-width: 8px;
	padding: 0;
	font-size: 0;
}

.content-container {
	width: 100%;
	height: 45px;
	overflow: hidden;
	display: flex;
	flex-direction: column;
	justify-content: space-around;
	padding-right: 12px;
}

.content-container .title-time-container {
	display: flex;
	width: 100%;
	max-width: 100%;
	align-content: center;
	justify-content: space-between;
}

.content-container .title-time-container .title {
	display: inline-block;
	font-size: 14px;
	color: $primaryTextColor;
	font-style: normal;
	padding-right: 10px;
	flex: 1;
	font-weight: 500;
	font-size: 14px;
	color: $primaryTextColor;
	line-height: 22px;
}

.content-container .title-time-container .time {
	display: inline-block;
	color: gray;
	font-family: PingFang SC, PingFang SC;
	font-weight: 400;
	font-size: 12px;
	color: $subTextColor;
	line-height: 20px;
}

.content-container .content {
	display: flex;
	justify-content: space-between;
}

.content .draft {
	font-size: 12px;
	height: 20px;
	color: #b8b8b8;
}

/*refer to: https://blog.csdn.net/weixin_42412046/article/details/80804285*/
>>> .content .draft em {
	color: red;
	font-style: normal;
	padding-right: 5px;
}

.last-message-desc {
	font-family: PingFang SC, PingFang SC;
	font-weight: 400;
	font-size: 12px;
	line-height: 20px;
	color: $subTextColor;
}

//.conversation-item-container.top .content .last-message-desc {
//	color: #949494;
//}
//
//.content .last-message-desc {
//	color: #acacac;
//	font-size: 12px;
//}
//
//.content .last-message-desc i {
//	font-style: normal;
//	color: red;
//}

.content i {
	color: #b8b8b8;
}
</style>
