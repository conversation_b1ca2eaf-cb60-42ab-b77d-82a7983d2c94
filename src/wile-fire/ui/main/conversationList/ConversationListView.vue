<template>
	<section class="conversation-list">
		<customizationCard v-on="$listeners" v-bind="$attrs"></customizationCard>
		<virtual-list
			v-if="openIm === 'true'"
			:data-component="conversationItemView"
			:data-sources="conversationInfoList"
			:data-key="conversationInfoKey"
			:estimate-size="30"
			class="virtual-list"
			style="height: calc(100% - 78px); background: #ffffff; overflow-y: auto"
		/>
		<vue-context
			ref="menu"
			v-slot="{ data: conversationInfo }"
			v-on:close="onConversationItemContextMenuClose"
		>
			<li class="liItem" @click.prevent="setConversationTop(conversationInfo)">
				<i class="coos-iconfont icon-zhiding liIcon"></i>
				<a>
					{{
						conversationInfo && conversationInfo.top
							? $t('conversation.cancel_sticky_top')
							: $t('conversation.sticky_top')
					}}
				</a>
			</li>
			<li
				class="liItem"
				v-if="sharedMiscState.isElectron"
				@click.prevent="showConversationFloatPage(conversationInfo.conversation)"
			>
				<a>
					{{ $t('conversation.show_in_float_window') }}
				</a>
			</li>
			<li class="liItem" @click.prevent="setConversationSilent(conversationInfo)">
				<i class="coos-iconfont icon-xiaoximiandarao liIcon"></i>
				<a>
					{{
						conversationInfo && conversationInfo.isSilent
							? $t('conversation.enable_notification')
							: $t('conversation.disable_notification')
					}}
				</a>
			</li>
			<li
				class="liItem"
				v-show="
					conversationInfo &&
					(!sharedConversationState.currentConversationInfo ||
						!sharedConversationState.currentConversationInfo.conversation.equal(
							conversationInfo.conversation
						)) &&
					conversationInfo._unread === 0
				"
				@click.prevent="markConversationAsUnread(conversationInfo.conversation)"
			>
				<i class="coos-iconfont icon-chat1 liIcon"></i>
				<a>{{ $t('conversation.mark_as_unread') }}</a>
			</li>
			<li
				class="liItem"
				v-show="
					conversationInfo &&
					(!sharedConversationState.currentConversationInfo ||
						!sharedConversationState.currentConversationInfo.conversation.equal(
							conversationInfo.conversation
						)) &&
					conversationInfo._unread > 0
				"
				@click.prevent="clearConversationUnreadStatus(conversationInfo.conversation)"
			>
				<i class="coos-iconfont icon-chat1 liIcon"></i>
				<a>{{ $t('conversation.mark_as_read') }}</a>
			</li>
			<li class="liItem">
				<i class="coos-iconfont icon-trash liIconDelete"></i>
				<a @click.prevent="removeConversation(conversationInfo)">
					{{ $t('common.delete') }}
				</a>
			</li>
		</vue-context>
	</section>
</template>

<script>
import ConversationItemView from './ConversationItemView.vue';
import store from '../../../store';
import wfc from '../../../wfc/client/wfc';
import IpcEventType from '../../../ipcEventType';
import { ipcRenderer } from '../../../platform';
import customizationCard from '@/wile-fire/ui/main/conversationList/custom/customization-card';
import { mapGetters } from 'vuex';
import { msgReminder } from '@/api/modules/address-book';
import { isEmpty } from '@/utils';
export default {
	name: 'ConversationListView',
	components: {
		customizationCard
	},
	data() {
		return {
			sharedConversationState: store.state.conversation,
			sharedMiscState: store.state.misc,
			conversationItemView: ConversationItemView
		};
	},

	created() {
		this.$eventBus.$on('showConversationContextMenu', (event, conversationInfo) => {
			this.showConversationItemContextMenu(event, conversationInfo);
		});
	},

	destroyed() {
		this.$eventBus.$off('showConversationContextMenu');
	},

	methods: {
		setConversationTop(conversationInfo) {
			store.setConversationTop(conversationInfo.conversation, conversationInfo.top > 0 ? 0 : 1);
		},

		setConversationSilent(conversationInfo) {
			store.setConversationSilent(conversationInfo.conversation, !conversationInfo.isSilent);
			this.msgReminderRecord(conversationInfo.conversation, !conversationInfo.isSilent);
		},

		removeConversation(conversationInfo) {
			store.removeConversation(conversationInfo.conversation);
		},

		conversationInfoKey(conversationInfo) {
			let conv = conversationInfo.conversation;
			return conv.target + '-' + conv.type + '-' + conv.line;
		},
		scrollActiveElementCenter() {
			let el = this.$el.getElementsByClassName('active')[0];
			el && el.scrollIntoView({ behavior: 'instant', block: 'center' });
		},

		showConversationItemContextMenu(event, conversationInfo) {
			if (!this.$refs.menu) {
				return;
			}
			this.sharedConversationState.contextMenuConversationInfo = conversationInfo;
			this.$refs.menu.open(event, conversationInfo);
		},

		onConversationItemContextMenuClose() {
			this.sharedConversationState.contextMenuConversationInfo = null;
		},

		clearConversationUnreadStatus(conversation) {
			wfc.clearConversationUnreadStatus(conversation);
		},

		markConversationAsUnread(conversation) {
			wfc.markConversationAsUnread(conversation, true);
		},

		showConversationFloatPage(conversation) {
			let hash = window.location.hash;
			let url = window.location.origin;
			if (hash) {
				url = window.location.href.replace(hash, '#/conversation-window');
			} else {
				url += '/conversation-window';
			}
			ipcRenderer.send(IpcEventType.showConversationFloatPage, {
				url: url,
				type: conversation.type,
				target: conversation.target,
				line: conversation.line
			});

			store.addFloatingConversation(conversation);
			if (
				this.sharedConversationState.currentConversationInfo &&
				this.sharedConversationState.currentConversationInfo.conversation.equal(conversation)
			) {
				store.setCurrentConversation(null);
			}
		},

		/**
		 * 同步记录用户设置会话免打扰情况
		 * @param target 会话对象(单聊IM用户编号/群聊编号)
		 * @param notNotice 消息免打扰设置值
		 * 操作：不处理响应
		 */
		msgReminderRecord(conversation, notNotice) {
			msgReminder({
				convType: conversation.type,
				target: conversation.target,
				messageNoNotice: notNotice
			});
		}
	},
	activated() {
		this.scrollActiveElementCenter();
	},
	computed: {
		...mapGetters(['openIm']),
		conversationInfoList() {
			return this.sharedConversationState.conversationInfoList
				.filter(ci => {
					const index = this.sharedConversationState.floatingConversations.findIndex(c =>
						c.equal(ci.conversation)
					);
					return index === -1;
				})
				.filter(item => {
					return !isEmpty(item.lastMessage);
				});
		}
	}
};
</script>

<style lang="scss" scoped>
.conversation-list {
	height: 100%;
	overflow: auto;
	@include noScrollBar;
}
.virtual-list {
	@include noScrollBar;
}
.v-context {
	padding: 6px 0px;
}
.v-context > li > a {
	padding-left: 0px;
}
.liItem {
	display: flex;
	align-items: center;
	font-weight: 400;
	font-size: 14px;
	color: #323233;
	line-height: 20px;

	.liIcon {
		font-size: 16px;
		margin-bottom: 2px;
		margin-right: 10px;
		margin-left: 17px;
	}
	.liIconDelete {
		font-size: 16px;
		color: $textColor;
		margin-top: 2px;
		margin-left: 17px;
		margin-right: 10px;
	}
}
.liItem:hover {
	background: rgb(248, 249, 250);
}
</style>
