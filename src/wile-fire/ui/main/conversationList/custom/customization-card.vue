<template>
	<div>
		<div
			v-for="(item, index) of list"
			:key="index"
			class="coos-card"
			@click="changeContent(item.type)"
			:class="{ select: messageType === item.type }"
		>
			<img :src="item.avatar" alt="" class="avatar" />
			<div class="text">
				<div class="text-name">
					<div class="text-name-font">{{ item.name }}</div>
					<div v-if="item.iconName" class="text-name-icon">{{ item.iconName }}</div>
					<div class="time">{{ item.newMessage.createTime }}</div>
				</div>
				<div class="text-message">
					<div class="text-message-con">
						{{ item.type === 'system' ? item.newMessage.title : '让工作更轻松，让生活更精彩' }}
					</div>
					<div v-if="item.type === 'system' && unread" class="text-message-more">
						<div class="bage">{{ unread }}</div>
						<!--						<i class="coos-iconfont yehuo-xiaoxizhongxin bage-icon"></i>-->
						<!--						<div class="bage-circle"></div>-->
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import { mapMutations } from 'vuex';
import { CoosEventTypes } from '@/utils/bus';
import { getItem } from '@/utils/localstorage';
export default {
	name: 'coos-card',
	props: {
		list: {
			type: Array,
			default: () => {
				return [];
			}
		},
		messageType: {
			type: String,
			default: () => {
				return 'coos';
			}
		}
	},
	data() {
		return {
			unread: 0
		};
	},
	computed: {},
	mounted() {
		this._BUS.$on(CoosEventTypes.updateSystemCount, this.updateSystemCount);
	},
	destroyed() {
		this._BUS.$off(CoosEventTypes.updateSystemCount, this.updateSystemCount);
	},
	activated() {
		console.log(this.messageType, 'messageTypemessageTypemessageTypemessageType');
		// 如果激活的时候就在系统会话，清空未读
		if (this.messageType === 'system') {
			this.SET_IM_COUNT(0);
		}
	},
	methods: {
		...mapMutations('app', ['SET_IM_COUNT']),
		/**通知更新系统消息*/
		updateSystemCount(value) {
			this.$emit('updateSystemList', value); // 更新系统消息列表
			// 如果本来就在当前会话，就不添加未读消息提醒
			if (this.messageType !== 'system') {
				this.unread += 1;
				this.SET_IM_COUNT(1);
			}
			// 如果在当前会话，但是不在当前页面要通知框架上面的未读
			else if (this.$route.path !== '/wile-fire/home/<USER>') {
				this.SET_IM_COUNT(1);
			}
		},
		/**切换内容*/
		changeContent(type) {
			let messageShowAi = false;
			try {
				let coos_desk_rentInfo = JSON.parse(getItem('rentInfo'));
				let extend = JSON.parse(coos_desk_rentInfo.extend);
				messageShowAi = extend.applicationConfig.webConfig.messageShowAi || false;
			} catch (e) {
				messageShowAi = false;
			}
			if (type === 'coos' && messageShowAi && messageShowAi !== 'false') {
				this.$router.push(`/coos-ai?goBack=1`);
				return;
			}
			// 未读清空
			if (type === 'system') {
				this.unread = 0;
				this.SET_IM_COUNT(0);
			}
			this.$emit('changeContent', type);
			// store.setCurrentConversationInfo();
		}
	}
};
</script>

<style scoped lang="scss">
.coos-card {
	margin: 6px 10px 2px;
	height: 70px;
	background: #ffffff;
	padding: 0 8px;
	cursor: pointer;
	border-radius: 9px;
	@include flexBox(flex-start);
	&:hover {
		background: #f7f7f7;
	}
	.text {
		flex: 1;
		overflow: hidden;
		&-name {
			font-size: 14px;
			font-weight: 500;
			color: $primaryTextColor;
			@include flexBox(flex-start);
			&-icon {
				margin-left: 4px;
				padding: 4px 5px;
				background: var(--brand-3);
				border-radius: 3px;
				@include flexBox();
				font-size: 12px;
				font-weight: 400;
				color: #ffffff;
				line-height: 12px;
			}
			.time {
				flex: 1;
				text-align: right;
				font-size: 12px;
				font-weight: 400;
				color: $subTextColor;
				line-height: 20px;
			}
		}
		&-message {
			margin-top: 6px;
			font-size: 12px;
			font-weight: 400;
			color: $subTextColor;
			overflow: hidden;
			@include flexBox(space-between);
			&-con {
				@include aLineEllipse;
			}
			&-more {
				flex-shrink: 0;
				@include flexBox(flex-start);
				margin-left: 8px;
				.bage {
					min-width: 22px;
					height: 13px;
					background: #ff4d4f;
					border-radius: 23px;
					padding: 0 6px;
					@include flexBox();
					font-size: 10px;
					font-weight: 400;
					color: #ffffff;
					line-height: 13px;
				}
				.bage-icon {
					font-size: 14px;
				}
				.bage-circle {
					margin-left: 9px;
					width: 6px;
					height: 6px;
					background: #ff4d4f;
					border-radius: 23px;
				}
			}
		}
	}
}
.select {
	background: linear-gradient(90deg, var(--brand-2) 0%, var(--brand-1) 100%) !important;
	//background: linear-gradient(90deg, #d5e3ff 0%, #e8f4ff 100%, #e5f3ff 100%) !important;
}
.avatar {
	width: 40px;
	height: 40px;
	border-radius: 6px;
	margin-right: 8px;
}
</style>
