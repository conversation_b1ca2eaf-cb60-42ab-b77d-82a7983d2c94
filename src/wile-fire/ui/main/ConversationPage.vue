<template>
	<section id="conversation-content" class="conversation-page">
		<ConversationListPanel
			class="conversation-list-panel"
			@changeContent="changeContent"
			@updateSystemList="updateSystemList"
			:list="customization"
			:messageType="messageType"
		/>
		<ConversationView
			v-show="messageType === 'normal'"
			class="conversation-view"
			@changeContent="changeContent"
			ref="conversationView"
		/>
		<imSystem
			ref="systemList"
			class="conversation-view"
			:messageType="messageType"
			v-show="messageType === 'system'"
		/>
		<imCoos ref="imContentCoos" v-show="messageType === 'coos'"></imCoos>
		<password ref="password" />
	</section>
</template>

<script>
import ConversationView from './conversation/ConversationView';
import ConversationListPanel from './ConversationListPanel.vue';
import imCoos from '@/views/coos/index.vue';
import imSystem from '@/views/im/system.vue';
import { mapGetters } from 'vuex';
import { getNewSystemMessage } from '@/api/modules/coos';
import Password from '@/layout/components/setting/password';
import Conversation from '@/wile-fire/wfc/model/conversation';
import ConversationType from '@/wile-fire/wfc/model/conversationType';
import store from '@/wile-fire/store';
import { getDictionary } from '@/utils/data-dictionary';
import { CoosEventTypes } from '@/utils/bus';
import { getItem } from '@/utils/localstorage';

export default {
	name: 'ConversationPage',
	data() {
		return {
			messageType: 'normal',
			params: {
				pageSize: 10,
				pageNo: 1,
				keywords: ''
			},
			systemNewMessage: ''
		};
	},
	computed: {
		...mapGetters(['rentInfo', 'openIm', 'coosConfig', 'aiEnterList']),
		// 智能会话列表数据
		coosAiList() {
			return [
				getDictionary('AI编码/创意智绘'),
				getDictionary('AI编码/智答宝库'),
				getDictionary('AI编码/智控领航'),
				getDictionary('AI编码/数据智析')
			];
		},
		customization() {
			let arr = [];
			if (
				this.aiEnterList.some(item => {
					return this.coosAiList.includes(item.permsCode);
				})
			) {
				arr.push({
					avatar: require(`@/assets/${this.rentThem}/coos/coos-avatar.png`),
					name: this.coosConfig.robotName || 'COOS智能助手',
					iconName: '机器人',
					type: 'coos',
					newMessage: {}
				});
			}
			if (this.openIm === 'false') {
				arr.push({
					avatar: this.rentInfo.logoUrl,
					name: '系统消息',
					iconName: '系统',
					type: 'system',
					newMessage: this.systemNewMessage
				});
			}
			return arr;
		}
	},
	watch: {
		messageType(newVal) {
			if (this.openIm === 'false') {
				this.getNewMessage();
			}
			if (this.messageType !== 'normal') {
				store.setCurrentConversationInfo();
			}
		}
	},
	created() {
		//  强制修改密码
		let forceEditInitPwd = localStorage.getItem('forceEditInitPwd');
		if (forceEditInitPwd && forceEditInitPwd == 'true') {
			this.$nextTick(() => {
				this.$refs.password.open();
			});
		}
		this.messageType = this.openIm === 'false' ? 'system' : 'normal';
		// this.messageType = this.customization[0] ? this.customization[0].type : 'normal';
	},
	activated() {
		let params = this.$route.query;
		if (params.target) {
			let conversation =
				params.type == 1
					? new Conversation(ConversationType.Group, params.target, 0)
					: new Conversation(ConversationType.Single, params.target, 0);
			store.setCurrentConversation(conversation);
		}
	},
	mounted() {
		if (this.openIm === 'false') {
			this.getNewMessage();
		}
		this._BUS.$on(CoosEventTypes.changeIm, this.changeIm);
	},
	destroyed() {
		this._BUS.$off(CoosEventTypes.changeIm, this.changeIm);
	},
	methods: {
		/**改变im*/
		changeIm(type) {
			this.messageType = 'coos';
			this.$refs.imContentCoos.changeModeType(type);
		},
		/**更新系统消息列表*/
		updateSystemList(value) {
			this.$refs.systemList.update();
			this.systemNewMessage = value;
		},
		/**获取最新的系统消息*/
		getNewMessage() {
			getNewSystemMessage().then(res => {
				if (res.code === 200) {
					this.systemNewMessage = res.result;
				} else {
					this.$message.error(res.message);
				}
			});
		},

		changeContent(type) {
			this.messageType = type;
			// 切换消息关闭备忘录
			if (this.messageType === 'normal') {
				this.$refs.conversationView.close();
				this.$refs.conversationView.resetData();
			}
			// 获取备忘录统计
			// this.$refs.conversationView.getState();
			// this.$refs.conversationView.getList();
		}
	},
	components: {
		Password,
		ConversationListPanel,
		ConversationView,
		imCoos,
		imSystem
	}
};
</script>

<style lang="css" scoped>
.conversation-page {
	flex: 1;
	overflow: hidden;
	display: flex;
	height: 100%;
}

.conversation-list-panel {
	width: calc(30% - 12px);
	min-width: 240px;
	max-width: 330px;
	height: 100%;
	margin-right: 8px;
}

.conversation-view {
	flex: 1;
}
</style>
