import store from '../../store';
import ForwardMessageByPickConversationView from '../main/conversation/message/forward/ForwardMessageByPickConversationView';
import ForwardMessageByCreateConversationView from '../main/conversation/message/forward/ForwardMessageByCreateConversationView';

export default {
	install(Vue) {
		Vue.prototype.$forwardMessage = function (options) {
			const pickConversationAndForwardMessage = (forwardType, messages) => {
				return new Promise((resolve, reject) => {
					let beforeClose = event => {
						// What a gamble... 50% chance to cancel closing
						if (event.params.toCreateConversation) {
							// Promise.race([createConversationAndForwardMessage(forwardType, messages)])
							//     .then(resolve)
							//     .catch(reject);
							createConversationAndForwardMessage(forwardType, messages)
								.then(resolve)
								.catch(reject);
						} else if (event.params.confirm) {
							console.log('event.params----', event.params);
							let conversations = event.params.conversations;
							let extraMessageText = event.params.extraMessageText;
							let groupTitle = event.params.groupTitle;
							store.forwardMessage(
								forwardType,
								conversations,
								messages,
								extraMessageText,
								groupTitle
							);
							resolve();
						} else {
							reject();
						}
					};

					this.$modal.show(
						ForwardMessageByPickConversationView,
						{
							forwardType: forwardType,
							messages: messages
						},
						{
							name: 'forward-by-pick-conversation-modal',
							width: 728,
							height: 584,
							styles: {
								borderRadius: `${16}px`,
								background: '#FFFFFF',
								boxShadow:
									'0px 8px 10px -5px rgba(0,0,0,0.08), 0px 16px 24px 2px rgba(0,0,0,0.04), 0px 6px 30px 5px rgba(0,0,0,0.05)',
								padding: '24px 30px'
							},
							clickToClose: false
						},
						{
							'before-close': beforeClose
						}
					);
				});
			};

			const createConversationAndForwardMessage = (forwardType, messages) => {
				return new Promise((resolve, reject) => {
					let beforeClose = event => {
						if (event.params.backPickConversation) {
							// Promise.race([pickConversationAndForwardMessage(forwardType, messages)])
							//     .then(resolve)
							//     .catch(reject);
							pickConversationAndForwardMessage(forwardType, messages).then(resolve).then(reject);
						} else if (event.params.confirm) {
							let users = event.params.users;
							let extraMessageText = event.params.extraMessageText;
							store.forwardByCreateConversation(forwardType, users, messages, extraMessageText);
							resolve();
						} else {
							reject();
						}
					};
					this.$modal.show(
						ForwardMessageByCreateConversationView,
						{
							forwardType: forwardType,
							messages: messages,
							users: store.state.contact.friendList
						},
						{
							name: 'forward-by-create-conversation-modal',
							width: 600,
							height: 480,
							clickToClose: false
						},
						{
							'before-close': beforeClose
						}
					);
				});
			};
			//return pickConversationAndForwardMessage(ForwardType.NORMAL, [message]);
			return pickConversationAndForwardMessage(options.forwardType, options.messages);
		};
	}
};
