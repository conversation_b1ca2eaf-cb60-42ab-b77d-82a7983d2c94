import request from '@/utils/request';
/**纪要列表*/
export function getSummeryList(params) {
	return request({
		url: `/api/message/summarys/search`,
		method: 'get',
		params: params
	});
}
/** 生成纪要 */
export function createSummery(data) {
	return request({
		url: `/api/message/summary`,
		method: 'post',
		data
	});
}
/** 删除纪要 */
export function deleteSummery(ids) {
	return request({
		url: `/api/message/summarys/${ids}`,
		method: 'delete'
	});
}
/** 刷新纪要 */
export function rehashSummery(id) {
	return request({
		url: `/api/message/summary/${id}`,
		method: 'put'
	});
}

/**获取图表配置*/
export function getChartConfig(data) {
	return request({
		url: `/api/message/summarys/search`,
		method: 'get',
		data
	});
}
/**获取对话的UUID*/
export function getUUID() {
	return request({
		url: `/api/sys/common/generateId`,
		method: 'get',
		params: { num: 1 }
	});
}
