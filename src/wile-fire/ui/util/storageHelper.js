import { isElectron } from '../../platform';
import Config from '../../config';
const catch_pre = 'coos_im_';
let storage = !isElectron()
	? Config.CLIENT_ID_STRATEGY === 1
		? sessionStorage
		: Config.CLIENT_ID_STRATEGY === 2
		? localStorage
		: null
	: localStorage;

export function removeItem(key) {
	if (storage) {
		storage.removeItem(catch_pre + key);
	}
}

export function getItem(key) {
	if (storage) {
		return storage.getItem(catch_pre + key);
	}
}

export function setItem(key, value) {
	if (storage) {
		storage.setItem(catch_pre + key, value);
	}
}

export function clear() {
	if (storage) {
		storage.clear();
	}
}
