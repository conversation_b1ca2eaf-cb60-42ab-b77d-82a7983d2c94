const createImage = options => {
	options = options || {};
	const img = document.createElement('img');
	if (options.src) {
		img.src = options.src;
	}
	return img;
};

function oldCopy(value) {
	//创建一个input框
	const input = document.createElement('input');
	//将指定的DOM节点添加到body的末尾
	document.body.appendChild(input);
	//设置input框的value值为直播地址
	input.setAttribute('value', value);
	//选取文本域中的内容
	input.select();
	//copy的意思是拷贝当前选中内容到剪贴板
	//document.execCommand（）方法操纵可编辑内容区域的元素
	//返回值为一个Boolean，如果是 false 则表示操作不被支持或未被启用
	if (document.execCommand('copy')) {
		document.execCommand('copy');
	}
	//删除这个节点
	document.body.removeChild(input);
}

export const copyToClipboard = async blob => {
	try {
		await navigator.clipboard.write([
			// eslint-disable-next-line no-undef
			new ClipboardItem({
				[blob.type]: blob
			})
		]);
		console.log('content copied');
	} catch (error) {
		console.error(error);
	}
};

const convertToPng = imgBlob => {
	const canvas = document.createElement('canvas');
	const ctx = canvas.getContext('2d');
	const imageEl = createImage({ src: window.URL.createObjectURL(imgBlob) });
	imageEl.onload = e => {
		canvas.width = e.target.width;
		canvas.height = e.target.height;
		ctx.drawImage(e.target, 0, 0, e.target.width, e.target.height);
		canvas.toBlob(copyToClipboard, 'image/png', 1);
	};
};

export const copyImg = async src => {
	const img = await fetch(src);
	const imgBlob = await img.blob();
	if (imgBlob.type !== 'image/png') {
		return convertToPng(imgBlob);
	} else {
		return copyToClipboard(imgBlob);
	}
};

export const copyText = text => {
	if (navigator?.clipboard?.write) {
		const blob = new Blob([text], { type: 'text/plain' });
		copyToClipboard(blob);
	} else {
		oldCopy(text);
	}
};
