import HomePage from './ui/main/HomePage';
import ConversationPage from './ui/main/ConversationPage';

const routers = [
	{
		path: 'home',
		name: 'HomePage',
		component: HomePage,
		redirect: '/wile-fire/home/<USER>',
		children: [
			{
				path: 'index',
				name: 'conversation',
				// component: () => import('@/views/calendar/index')
				component: ConversationPage,
				meta: { keepAlive: true }
			}
		]
	}
];
export default routers;
