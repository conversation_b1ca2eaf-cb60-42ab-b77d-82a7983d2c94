#!/usr/bin/env node

var commander = require('commander');

commander.
  version(require('../package').version).
  usage('[options] 汉字').
  option('-v, --version', 'output the version number').
  option('-s, --style <style>', 'pinyin styles: [NORMAL,TONE,TONE2,INITIALS,FIRST_LETTER]').
  option('-S, --segment', 'segmentation word to phrases').
  option('-h, --heteronym', 'output heteronym pinyins').
  option('-p, --separator <separator>', 'separator between words').
  parse(process.argv);

if (commander.list) {
  process.exit()
}

// output help and exit if no args found
if (commander.args.length === 0) {
  commander.help();
}

var pinyin = require("../");
var options = {
  style: pinyin["STYLE_" + (commander.style || "TONE").toUpperCase()],
  heteronym: commander.heteronym || false,
  segment: commander.segment || false,
};

var separator = commander.separator === undefined ? ' ' : commander.separator;
var words = commander.args.join(" ");
var py = pinyin(words, options).join(separator);
console.log(py);

// vim:ft=javascript
