{"_args": [["pinyin@2.9.1", "/Users/<USER>/bitbucket/wildfirechat/vue-chat-pc"]], "_from": "pinyin@2.9.1", "_id": "pinyin@2.9.1", "_inBundle": false, "_integrity": "sha1-RXDT4D8UMPacCNQWawhzkskidxg=", "_location": "/pinyin", "_phantomChildren": {"keypress": "0.1.0"}, "_requested": {"type": "version", "registry": true, "raw": "pinyin@2.9.1", "name": "pinyin", "escapedName": "pinyin", "rawSpec": "2.9.1", "saveSpec": null, "fetchSpec": "2.9.1"}, "_requiredBy": ["/"], "_resolved": "https://registry.npm.taobao.org/pinyin/download/pinyin-2.9.1.tgz", "_spec": "2.9.1", "_where": "/Users/<USER>/bitbucket/wildfirechat/vue-chat-pc", "author": {"name": "闲耘", "email": "<EMAIL>"}, "bin": {"pinyin": "bin/pinyin"}, "browser": "lib/web-pinyin.js", "bugs": {"url": "https://github.com/hotoo/pinyin/issues"}, "dependencies": {"commander": "~1.1.1", "@node-rs/jieba": "^0.6.0", "object-assign": "^4.0.1"}, "description": "汉语拼音转换工具。", "devDependencies": {"benchmark": "~1.0.0", "eslint": "~0.24.0", "expect.js": "~0.3.1", "istanbul": "~0.3.17", "mocha": "1.17.1", "request": "~2.68.0"}, "directories": {"example": "examples", "test": "tests"}, "files": ["bin", "data", "lib", "index.js", "src"], "homepage": "http://pinyin.hotoo.me/", "keywords": ["拼音", "汉语", "汉字", "中文", "<PERSON><PERSON><PERSON>"], "license": "MIT", "main": "lib/index.js", "name": "pinyin", "optionalDependencies": {"nodejieba": "^2.2.1"}, "repository": {"type": "git", "url": "git+https://github.com/hotoo/pinyin.git"}, "scripts": {"test": "make test"}, "spm": {"main": "lib/web-pinyin.js", "devDependencies": {"expect.js": "0.3.1", "url": "1.2.0"}, "build": {"babel": {}}, "tests": "tests/*-spec.js", "dependencies": {"object-assign": "~4.0.1"}}, "version": "2.9.1"}