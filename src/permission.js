import router from './router';
import NProgress from 'nprogress';
import 'nprogress/nprogress.css';
import { getToken } from '@/utils/auth';
import getPageTitle from '@/utils/get-page-title';
import store from '@/store';
import { mdByAppId } from '@/api/modules/md';
import { Message } from 'element-eoss';
import { deepClone } from '@/utils';
import { defaultTenantId } from '@/api/modules/login';
import { getItem, setSessionItem } from '@/utils/localstorage';

/**进度条设置*/
NProgress.configure({ showSpinner: false });
/**白名单设置*/
const whiteList = [
	'/login',
	'/auto-login',
	'/application-market',
	'/energy-om/index',
	'/energy-om/otherSystem'
];
/**路由拦截*/
router.beforeEach(async (to, from, next) => {
	NProgress.start();
	// layout中的this.$route.query.layout是单页面临时控制
	// 这里是控制整个项目隐藏框架的
	// 因为有些业务自己有路由重定向，所以只能放在这里操作
	if (to.query.hideLayoutForever) {
		setSessionItem('hideLayout', true);
	}
	if (to.query.hideFloatAiForever) {
		setSessionItem('hideFloatAi', true);
	}
	// 如果需要加入标签页的路由加入到标签页数组中
	if (to.meta?.isPageTab || to.query.isPageTab) {
		let pageTabs = deepClone(store.getters.pageTabs);
		let index = pageTabs.findIndex(item => {
			return to.fullPath === item.fullPath;
		});
		if (index === -1) {
			let newTab = {
				path: to.path,
				fullPath: to.fullPath,
				name: to.name,
				meta: Object.assign(to.meta, { query: to.query })
			};
			pageTabs.push(newTab);
			store.commit('user/SET_PAGE_TABS', pageTabs);
		}
	}
	// 只有模式2   并且    需要加入菜单面包屑
	if (store.getters.clientSystemMode === '2' && (to.meta?.isMenuTab || to.query.isMenuTab)) {
		let menuTabs = deepClone(store.getters.menuTabs);
		let index = menuTabs.findIndex(item => {
			return to.fullPath === item.fullPath;
		});
		// 未加入到面包屑的才执行
		if (index === -1) {
			let mapTitle = to.query.name // 优先取自定义的名字
				? to.query.name
				: to.query.url // 其次匹配配置的菜单映射出来的名字
				? store.getters.menuTitleMap[to.query.url]
				: store.getters.menuTitleMap[to.fullPath];
			let newTab = {
				path: to.path,
				fullPath: to.fullPath,
				name: to.name,
				meta: {
					...to.meta,
					title: mapTitle || to.meta.title // 三方系统从映射中找标题，否则路由配置的标题
				}
			};
			// 如果是第三种框架模式，需要绑定当前打开面包屑所在一级菜单的索引
			// if (store.getters.clientSystemMode === '3' && store.getters.currentLevelMenu > -1) {
			// 	newTab.levelIndex = store.getters.currentLevelMenu;
			// }
			menuTabs.push(newTab);
			store.commit('user/SET_MENU_TABS', menuTabs);
		}
	}
	// 只有模式1   store.getters.clientSystemMode === '1'(因为业务上依赖这个应用信息，所以开放所有模式)
	// 并且需要加入应用列表
	if (to.meta?.isApplication || to.query.isApplication) {
		let { title, icon, id } = to.meta;
		let data = {
			appId: id,
			id: id + '-' + store.getters.workApp.length,
			title: to.query.name || title,
			url: to.fullPath,
			icon: to.query.logoUrlPath || icon
		};
		store.commit('user/SET_WORKAPP', data);
	}
	const hasToken = getToken();
	// 埋点
	if (hasToken && to.meta?.appId) {
		mdByAppId(to.meta.appId).then(res => {
			if (res.code !== 200) {
				Message({
					type: 'error',
					message: res.message,
					duration: 3 * 1000
				});
			}
		});
	}

	// 如果是other-system，要获取菜单与标题的映射关系
	if (to.path === '/other-system') {
		let title = to.query.name // 优先取自定义的名字
			? to.query.name
			: to.query.url // 其次匹配配置的菜单映射出来的名字
			? store.getters.menuTitleMap[to.query.url]
			: to.meta.title;
		document.title = getPageTitle(title);
	} else {
		document.title = getPageTitle(to.query.title || to.meta.title);
	}
	// 判断是否登录
	if (hasToken) {
		// 重定向到配置的首页
		if (to.path === '/login') {
			let home = store.getters.systemInfo.main.indexUrl || '/';
			next({ path: home });
		}
		// 默认首页应该是配置的首页
		else if (
			store.getters.systemInfo.main.indexUrl &&
			store.getters.systemInfo.main.indexUrl !== '/' &&
			to.redirectedFrom === '/'
		) {
			next({ path: store.getters.systemInfo.main.indexUrl });
		} else {
			next();
		}
		NProgress.done();
	} else {
		// 白名单校验
		if (whiteList.indexOf(to.path) !== -1) {
			try {
				// 登录界面根据  路由参数租户id =》缓存租户id =》配置的租户id   优先级进行配置请求
				if (to.path === '/login') {
					// 路由参数租户id
					let tenantId = to.query.tenantId;
					if (!tenantId) {
						// 如果没有，判断缓存是否有租户id
						let systemInfo = getItem('systemInfo');
						systemInfo = systemInfo ? JSON.parse(systemInfo) : {};
						// 缓存的租户信息如果是代码写死默认的，就要请求配置的租户id
						if (systemInfo && systemInfo.isSystemInfo) {
							const res = await defaultTenantId();
							if (res.code === 200 && res.result) {
								tenantId = res.result;
							}
						}
					}
					// 如果拿到了租户id，请求租户配置以及主题配置
					if (tenantId) {
						await store.dispatch('app/getTenantConfig', { tenantId });
						await store.dispatch('them/SET_THEM');
					}
					// 设置系统名称
					document.title = getPageTitle(to.query.title || to.meta.title);
				}
			} catch (e) {
				console.log('路由拦截登录获取租户配置报错：', e);
			}
			next();
			NProgress.done();
		}
		// 没有token并且不是白名单
		else {
			store.commit('user/REMOVE_INFO'); // 可能手动清除token
			next(`/login?redirect=${encodeURIComponent(to.fullPath)}`);
			NProgress.done();
		}
	}
});

router.afterEach(() => {
	NProgress.done();
});
