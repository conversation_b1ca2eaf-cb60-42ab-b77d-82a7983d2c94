import Layout from '@/layout';
import wileFireRouter from '@/wile-fire/routers';
import { getDictionary } from '@/utils/data-dictionary';

export default [
	{
		path: '/login',
		component: () => import('@/views/login/index'),
		hidden: true,
		meta: {
			title: '登录'
		}
	},
	{
		path: '/auto-login',
		component: () => import('@/views/auto-login/index'),
		hidden: true,
		meta: {
			title: '自动登录'
		}
	},
	{
		path: '/',
		component: Layout,
		redirect: '/wile-fire',
		children: [
			{
				path: 'wile-fire',
				name: 'WileFire',
				redirectName: 'conversation',
				component: () => import('@/wile-fire/App'),
				redirect: '/wile-fire/home',
				meta: {
					title: '消息',
					isMenu: true,
					isMenuTab: true,
					icon: 'im',
					appId: getDictionary('应用ID/消息'),
					keepAlive: true
				},
				children: [...wileFireRouter]
			},
			{
				path: 'coos-ai',
				name: 'CoosAi',
				component: () => import('@/views/coos/index-new'),
				meta: { title: 'AI助手', isApplication: true }
			},
			{
				path: 'calendar',
				name: 'Calendar',
				component: () => import('@/views/calendar/index'),
				meta: {
					title: '日历',
					isMenu: true,
					isMenuTab: true,
					icon: 'calendar',
					appId: getDictionary('应用ID/日历'),
					keepAlive: true
				}
			},
			{
				path: 'ppt',
				name: 'ppt',
				component: () => import('@/views/ppt/index'),
				meta: { title: 'PPT生成', isApplication: true, keepAlive: true }
			},
			{
				path: 'wait-handle',
				name: 'WaitHandle',
				component: () => import('@/views/wait-handle/index'),
				meta: {
					title: '待办',
					isMenu: true,
					isMenuTab: true,
					icon: 'wait-handle',
					appId: getDictionary('应用ID/待办'),
					keepAlive: true
				}
			},
			{
				path: 'document-content',
				name: 'documentContent',
				component: () => import('@/views/document-content/index'),
				meta: {
					title: '文档',
					isMenu: true,
					isMenuTab: true,
					icon: 'document-content',
					appId: getDictionary('应用ID/文档'),
					keepAlive: true
				}
			},
			{
				path: 'address-book',
				name: 'AddressBook',
				component: () => import('@/views/address-book/index'),
				meta: {
					title: '通讯录',
					isMenu: true,
					isMenuTab: true,
					icon: 'address-book',
					appId: getDictionary('应用ID/通讯录'),
					keepAlive: true
				}
			},
			{
				path: '/departments-members',
				name: 'DepartmentsMembers',
				component: () => import('@/views/departments-members/index'),
				meta: { title: '部门与成员' }
			},
			{
				path: 'work-setting',
				name: 'WorkSetting',
				component: () => import('@/views/work-setting/index'),
				meta: {
					title: '工作台',
					isMenu: true, // 是菜单
					isMenuTab: true, // 是面包屑
					icon: 'work-setting',
					isPageTab: true, // 是工作台标签页
					query: { id: getDictionary('应用ID/工作台') },
					appId: getDictionary('应用ID/工作台'),
					keepAlive: true
				}
			},
			{
				path: 'smartBi',
				name: 'smartBi',
				component: () => import('@/views/smartbi/diagram'),
				meta: {
					title: 'smartBi',
					icon: 'document-content'
				}
			},
			{
				path: 'other-system',
				name: 'OtherSystem',
				component: () => import('@/views/other-system/index'),
				meta: { title: '外部系统', isMenuTab: true, id: 'OtherSystem', isApplication: true }
			},
			{
				path: 'form-page',
				name: 'FormPage',
				component: () => import('@/views/form-page/index'),
				meta: { title: '系统表单', isPageTab: true, appId: getDictionary('应用ID/系统表单') }
			},
			{
				path: '/draft-writing',
				name: 'DraftWriting',
				component: () => import('@/views/draft-writing/index'),
				meta: { title: '长文撰写' }
			},
			{
				path: '/error-correction',
				name: 'ErrorCorrection',
				component: () => import('@/views/draft-writing/error-correction.vue'),
				meta: { title: '文稿纠错' }
			},
			{
				path: '/recording-transcript',
				name: 'recordingTranscript',
				component: () => import('@/views/recording-transcript/index'),
				meta: { title: '录音智记' }
			},
			{
				path: '/recording-transcript/transcribe-file',
				name: 'transcribeFile',
				component: () => import('@/views/recording-transcript/transcribe-file'),
				meta: { title: '转写记录' }
			},
			{
				path: '/recording-transcript/details',
				name: 'transcribeFileDetails',
				component: () => import('@/views/recording-transcript/details'),
				meta: { title: '记录详情' }
			},
			{
				path: '/ai-uses-statistics',
				name: 'AiUsesStatistics',
				component: () => import('@/views/ai-uses-statistics/index'),
				meta: { title: '大模型ai访问记录统计' }
			},
			{
				path: '/ai-uses-statistics/details',
				name: 'AiUsesStatisticsDetails',
				component: () => import('@/views/ai-uses-statistics/details'),
				meta: { title: '大模型ai访问记录统计详情' }
			},
			/**开发期间菜单*/
			{
				path: 'dev',
				name: 'Dev',
				component: () => import('@/views/dev/index'),
				meta: { title: '组件开发' }
			},
			{
				path: 'svg',
				name: 'Svg',
				component: () => import('@/views/svg-icon/index'),
				meta: { title: '系统图标' }
			},
			{
				path: 'mark',
				name: 'Mark',
				component: () => import('@/views/dev-mark/index'),
				meta: { title: '组件开发' }
			}
		]
	},
	{
		path: '/application-market',
		component: () => import('@/views/application-market/index'),
		hidden: true,
		meta: {
			keepAlive: false,
			title: '应用超市'
		}
	},
	/**引导页*/
	{
		path: '/guide-page',
		component: () => import('@/views/guide-page/index'),
		hidden: true,
		meta: {
			keepAlive: false,
			title: '引导页'
		}
	},
	{
		path: '/process/startProcess',
		name: 'StartProcess',
		component: () => import('@/views/process/start-process/index'),
		meta: { title: '流程' }
	},
	{
		path: '/process/flowView',
		name: 'formView',
		component: () => import('@/views/process/flow-view/index.vue'),
		meta: { title: '流程查看' }
	},
	{
		path: '/wait-detail',
		name: 'WaitDetail',
		component: () => import('@/components/wait-detail/index.vue'),
		meta: { title: '办理详情' }
	},
	/**提供给外部系统独访问页Start*/
	{
		path: '/coos',
		name: 'Coos',
		component: () => import('@/views/coos/index.vue'),
		meta: { title: '智能助手' }
	},
	{
		path: '/document',
		name: 'Document',
		component: () => import('@/views/document-content/index'),
		meta: { title: '知识空间' }
	},
	{
		path: '/sma',
		name: 'Sma',
		component: () => import('@/views/document-content/index'),
		meta: { title: '知识空间' }
	},
	{
		path: '/coosAi',
		name: 'CoosAiPage',
		component: () => import('@/views/coos/index-new'),
		meta: { title: 'AI助手' }
	},
	{
		path: '/group',
		name: 'Group',
		component: () => import('@/views/address-book/content-info/mygroup'),
		meta: { title: '群组' }
	},
	{
		path: '/recording-transcript',
		name: 'recordingTranscript',
		component: () => import('@/views/recording-transcript/index'),
		meta: { title: '录音智记' }
	},
	{
		path: '/risk-identification',
		name: 'RiskIdentification',
		component: () => import('@/views/risk-identification/index'),
		meta: { title: '风险识别' }
	}
	/**提供给外部系统独访问页End*/
];
