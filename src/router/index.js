import Vue from 'vue';
import Router from 'vue-router';
Vue.use(Router);
/**自动引入module下面的路由文件*/
const modulesFiles = require.context('./modules', true, /\.js$/);
const modules = modulesFiles.keys().reduce((modules, modulePath) => {
	const moduleRouter = modulesFiles(modulePath);
	modules = modules.concat(moduleRouter.default);
	return modules;
}, []);
/**自动引入三方集成的下面的路由文件(OLD旧版全部引入)*/
// const thirdRouteFiles = require.context('../third-party', true, /router.js/);
// const thirdRoute = thirdRouteFiles.keys().reduce((modules, modulePath) => {
// 	const moduleRouter = thirdRouteFiles(modulePath);
// 	modules = modules.concat(moduleRouter.default);
// 	return modules;
// }, []);
/**自动引入三方集成的下面的路由文件(NEW新版按需引入，但是引起了内存溢出，百思不得其解)*/
// let thirdRoute = [];
// const thirdParty = process.env['VUE_APP_THIRD_PARTY'].split(',');
// thirdParty.forEach(module => {
// 	let router = require(`../third-party/${module}/router.js`);
// 	thirdRoute = thirdRoute.concat(router.default);
// });
/**自动引入三方集成的下面的路由文件(NEW新版按需引入)*/
let thirdRoute = [];
if (process.env['VUE_APP_OMIP'] === 'true') {
	let router = require(`../third-party/omip/router.js`);
	thirdRoute = thirdRoute.concat(router.default);
}
if (process.env['VUE_APP_ENERGY'] === 'true') {
	let router = require(`../third-party/energy/router.js`);
	thirdRoute = thirdRoute.concat(router.default);
}
if (process.env['VUE_APP_COOS'] === 'true') {
	let router = require(`../third-party/coos/router.js`);
	thirdRoute = thirdRoute.concat(router.default);
}
if (process.env['VUE_APP_RESEARCH'] === 'true') {
	let router = require(`../third-party/research/router.js`);
	thirdRoute = thirdRoute.concat(router.default);
}
if (process.env['VUE_APP_GZJG'] === 'true') {
	let router = require(`../third-party/gzjg/router.js`);
	thirdRoute = thirdRoute.concat(router.default);
}
if (process.env['VUE_APP_CONTRACT'] === 'true') {
	let router = require(`../third-party/contract/router.js`);
	thirdRoute = thirdRoute.concat(router.default);
}
if (process.env['VUE_APP_PERFORMANCE'] === 'true') {
	let router = require(`../third-party/performance/router.js`);
	thirdRoute = thirdRoute.concat(router.default);
}
// 运维
if (process.env['VUE_APP_OMS'] === 'true') {
	let router = require(`../third-party/oms/router.js`);
	thirdRoute = thirdRoute.concat(router.default);
}
// 纪检
if (process.env['VUE_APP_DMS'] === 'true') {
	let router = require(`../third-party/dms/router.js`);
	thirdRoute = thirdRoute.concat(router.default);
}
// 资质
if (process.env['VUE_APP_QMS'] === 'true') {
	let router = require(`../third-party/qms/router.js`);
	thirdRoute = thirdRoute.concat(router.default);
}
/**
 * name:'router-name'             路由名 (必须设置!!!)，判断当前页面是否为某个路由的根据
 * meta : {
 title: 'title'               菜单名称
 icon: 'im'                   菜单图标，svg-icon
 isMenu: false                是否在左侧菜单显示为菜单项
 isMenuTab: false             是否在设置为面包屑
 isApplication: false             是否在左侧显示应用图标
 isPageTab: false             是否保存为工作台的标签页
 query: {}                    路由路径默认携带的参数
 appId: 'A1006'               埋点对应的应用id
 noCache: false                不缓存，默认false，意思就是默认缓存(弃用)
 keepAlive: false              是否缓存
 }
 */

export const constantRoutes = [
	...modules, // 自动引入产品基础路由
	...thirdRoute, // 集成的三方路由，自动引入
	{
		path: '/403',
		component: () => import('@/views/403'),
		hidden: true
	},
	{
		path: '/404',
		component: () => import('@/views/404'),
		hidden: true
	},
	// 404 页面必须放在末尾
	{ path: '*', redirect: '/404', hidden: true }
];
console.log('constantRoutes', constantRoutes);
const createRouter = () =>
	new Router({
		mode: 'hash',
		scrollBehavior: () => ({ y: 0 }),
		routes: constantRoutes
	});

const router = createRouter();
// 详情文档 参考: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465
export function resetRouter() {
	const newRouter = createRouter();
	router.matcher = newRouter.matcher;
}

export default router;

/**获取菜单*/
export function getDefaultMenuList() {
	let list = [];
	const filterList = arr => {
		arr.forEach(item => {
			if (item.children && item.children.length > 0) filterList(item.children);
			if (item.meta && item.meta.isMenu)
				list.push({
					path: item.path,
					name: item.redirectName || item.name,
					meta: item.meta
				});
		});
	};
	filterList(constantRoutes);
	return list;
}
