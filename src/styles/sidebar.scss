#app {
	.main-container {
		height: 100%;
		transition: margin-left 0.28s;
		position: relative;
	}
	.hideSidebar {
		// 过渡时文字就开始隐藏
		.menu-title {
			height: 0;
			width: 0;
			overflow: hidden;
			visibility: hidden;
			display: inline-block;
		}
	}
	/**菜单栏样式*/
	.sidebar-container {
		transition: width 0.1s;
		background-color: #f5f6f7;
		height: calc(100% - 44px);
		padding-bottom: 48px;
		position: fixed;
		font-size: 0px;
		top: 44px;
		bottom: 0;
		left: 0;
		z-index: 6;
		overflow: hidden;
		.el-scrollbar {
			height: 100%;
			.scrollbar-wrapper {
				margin-right: 0 !important;
				@include noScrollBar;
				.el-menu {
					border-right: none;
					background: transparent !important;
				}
				.el-menu--collapse {
					.el-submenu__title,
					.el-menu-item {
						min-width: 10px !important;
						// 关闭菜单时
						.menu-title {
							height: 0;
							width: 0;
							overflow: hidden;
							visibility: hidden;
							display: inline-block;
						}
					}
					.el-submenu__icon-arrow {
						display: none;
					}
				}
				/**菜单项的选中样式*/
				.el-menu-item.is-active {
					color: #045dda !important;
					// color: var(--brand-6) !important;
					background-color: #ffffff !important;
					& i {
						color: var(--brand-6, #045dda) !important;
					}
				}
				/**菜单项的样式*/
				.el-submenu__title,
				.el-menu-item {
					background: transparent !important;
					min-width: 100px !important;
					height: 40px;
					margin: 4px 10px;
					padding: 13px 14px;
					border-radius: $borderRadius;
					display: flex;
					align-items: center;
					font-size: 14px;
					font-weight: 400;
					color: $primaryTextColor;
					line-height: 22px;
					&:hover {
						background-color: #ffffff !important;
						color: var(--brand-6) !important;
						& i {
							color: var(--brand-6) !important;
						}
					}
					& span {
						margin-left: 9px;
					}
					& i {
						color: #2f446b;
					}
					& > div {
						padding-left: 6px;
						width: 100%;
					}
					.el-submenu__icon-arrow {
						right: 8px;
					}
				}
				ul > div {
					overflow: hidden;
				}
			}
		}
	}
}
