// sidebar
$menuText: #2f446b; // 菜单文字样式
$subMenuActiveText: #2f446b; //子菜单选中后，父级样式

$menuBg: #ffffff; // 菜单和头部导航栏背景色
$menuHover: transparent; // 一级菜单放上去的样式

$subMenuBg: #1f2d3d;
$subMenuHover: #2f446b; // 子菜单放上的文字颜色
$buttonHeight: 36px; // 全局统一按钮高度
$borderRadius: 6px; // 全局统一按钮圆角
$popupRadius: 16px; // 全局统一弹窗圆角

// UI规范后的字体颜色相关
$primaryTextColor: #15224c; // 主要文字262626
$textColor: #2f446b; // 常规文字404040
$subTextColor: #737a94; // 次要文字8c8c8c
$holderTextColor: #b9bdc9; // 占位文字bfbfbf
$disabledTextColor: #d1d3db; // 占位文字#BBBFC4
$borderColor: #d9e2ec; // 边框颜色
$lineColor: #cdd9e4; // 分割线#EBEDF0
// the :export directive is the magic sauce for webpack
// https://www.bluematador.com/blog/how-to-share-variables-between-js-and-sass
:export {
	menuText: $menuText;
	subMenuActiveText: $subMenuActiveText;
	menuBg: $menuBg;
	menuHover: $menuHover;
	subMenuBg: $subMenuBg;
	subMenuHover: $subMenuHover;
	primaryTextColor: $primaryTextColor;
	textColor: $textColor;
	subTextColor: $subTextColor;
	holderTextColor: $holderTextColor;
	disabledTextColor: $disabledTextColor;
}
