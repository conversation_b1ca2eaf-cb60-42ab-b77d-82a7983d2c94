@import './variables.scss';
@import './mixin.scss';
@import './transition.scss';
@import './element-ui.scss';
@import './sidebar.scss';
@import './theme/index.scss';
@import './animation.scss';
@import './coos-table.scss';

body {
	height: 100%;
	-moz-osx-font-smoothing: grayscale;
	-webkit-font-smoothing: antialiased;
	text-rendering: optimizeLegibility;
	font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial,
		sans-serif;
}

label {
	font-weight: 700;
}

html {
	height: 100%;
	box-sizing: border-box;
}

#app {
	height: 100%;
}

*,
*:before,
*:after {
	box-sizing: inherit;
}

a:focus,
a:active {
	outline: none;
}

a,
a:focus,
a:hover {
	cursor: pointer;
	color: inherit;
	text-decoration: none;
}

div:focus {
	outline: none;
}

.clearfix {
	&:after {
		visibility: hidden;
		display: block;
		font-size: 0;
		content: ' ';
		clear: both;
		height: 0;
	}
}

// main-container global css
.app-container {
	padding: 20px;
}
//在全局设置
input[aria-hidden='true'] {
	display: none !important;
}

// 合同那边的样式
.table_link_span {
	cursor: pointer;
	border-bottom: 1px solid #1f69f4;
	color: #1f69f4;
}

// 合同那边的样式
.btn_span {
	display: inline-block;
	padding: 0 4px;
	cursor: pointer;
	color: #1f69f4;
}
