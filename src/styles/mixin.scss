@mixin clearfix {
	&:after {
		content: '';
		display: table;
		clear: both;
	}
}

@mixin noScrollBar {
	scrollbar-width: none;
	&::-webkit-scrollbar {
		width: 0;
	}
}

@mixin scrollBar {
	&::-webkit-scrollbar-track-piece {
		background: var(--brand-1);
	}

	&::-webkit-scrollbar {
		width: 6px;
	}

	&::-webkit-scrollbar-thumb {
		background: var(--brand-2);
		border-radius: 7px;
	}
}

@mixin relative {
	position: relative;
	width: 100%;
	height: 100%;
}

/**弹性布局*/
@mixin flexBox($jc: center, $ai: center) {
	display: flex;
	justify-content: $jc;
	align-items: $ai;
}

/**单行溢出省略号*/
@mixin aLineEllipse {
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}

/**多行溢出省略号*/
@mixin countEllipse($count: 2) {
	-webkit-line-clamp: $count;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	overflow: hidden;
	text-overflow: ellipsis;
}

@mixin flexBox($jc: center, $ai: center) {
	display: flex;
	justify-content: $jc;
	align-items: $ai;
}

//生成默认的padding 和margin 以及padding/margin(top,bottom,left,right)的1-40px中5/4/8的倍数的class
$box-list: (pd, padding), (pdt, padding-top), (pdb, padding-bottom), (pdl, padding-left),
	(pdr, padding-right), (mg, margin), (mgt, margin-top), (mgb, margin-bottom), (mgl, margin-left),
	(mgr, margin-right);
@each $box, $name in $box-list {
	@for $i from 1 through 40 {
		@if ($i % 5==0 or $i%4==0) {
			.#{$box}-#{$i} {
				#{$name}: $i + px;
			}
		}
	}
}

/**文字前蓝色竖杠 (仅块级元素可用)*/
@mixin verticalStripe($width: 4px, $height: 100%) {
	&:before {
		content: '';
		display: inline-block;
		width: $width;
		height: $height;
		margin-right: 6px;
		background-color: var(--brand-6, #0f45ea);
	}
}
