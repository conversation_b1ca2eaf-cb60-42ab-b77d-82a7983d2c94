// cover some element-ui styles

.el-breadcrumb__inner,
.el-breadcrumb__inner a {
	font-weight: 400 !important;
}
.el-progress-bar__innerText {
	color: var(--brand-4) !important;
}
.el-upload {
	input[type='file'] {
		display: none !important;
	}
}
.el-radio {
	.el-radio__input.is-checked:not(.is-disabled) .el-radio__inner {
		background: var(--brand-6);
	}
}
.el-upload__input {
	display: none;
}

// to fixed https://github.com/ElemeFE/element/issues/2461
.el-dialog {
	transform: none;
	left: 0;
	position: relative;
	margin: 0 auto;
	border-radius: $popupRadius;
}

// refine element ui upload
.upload-container {
	.el-upload {
		width: 100%;

		.el-upload-dragger {
			width: 100%;
			height: 200px;
		}
	}
}

// dropdown
.el-dropdown-menu {
	a {
		display: block;
	}
}

// to fix el-date-picker css style
.el-range-separator {
	box-sizing: content-box;
}

// 表格多出的下划线
.el-table td,
.el-table th.is-leaf {
	border-bottom: none;
}

// 多选框图标居中
.el-checkbox__inner {
	&::after {
		left: 5px;
	}
}

/**自定义element的ui样式*/
.desk-el {
	// 表单
	&-form {
		.el-select,
		.el-date-editor {
			width: 100%;
		}
		.el-input__inner {
			width: 100%;
			height: 40px !important;
			line-height: 40px !important;
			border: 1px solid $borderColor;
			border-radius: $borderRadius;
			&::placeholder {
				font-size: 14px;
				font-weight: 400;
				color: $holderTextColor;
				line-height: 22px;
			}
		}
		.el-textarea__inner {
			border-radius: 6px;
		}
	}
	// 表格
	&-table-border,
	&-table {
		border-radius: $borderRadius;
		.el-table__header-wrapper {
			border-radius: $borderRadius;
			// 滚动条隐藏后，头部留出的滚动条空缺
			col[name='gutter'] {
				width: 0 !important;
			}
		}
		.el-table__body-wrapper {
			@include scrollBar;
			&::-webkit-scrollbar {
				width: 0;
				height: 8px;
			}
		}
		//// 滚动条隐藏后，右侧固定列出现的空缺
		.el-table__fixed-right {
			right: 0 !important;
			&-patch {
				display: none;
			}
		}
		.el-table__fixed,
		.el-table__fixed-right {
			height: auto !important;
			bottom: 11px;
			&::before {
				background-color: transparent;
			}
		}
	}
	&-table {
		th,
		td {
			border-bottom: none !important;
		}
		.el-table__body {
			// 滚动条隐藏后，留出的滚动条位置
			//width: 100% !important;
			tr.el-table__row > td:first-child {
				border-radius: 6px 0 0 6px;
			}
			tr.el-table__row > td:last-child {
				border-radius: 0 6px 6px 0;
			}
		}
	}
	&-scroll-table {
		border-radius: $borderRadius;
		th,
		td {
			border-bottom: none !important;
		}
		.el-table__body {
			tr.el-table__row > td:first-child {
				border-radius: 6px 0 0 6px;
			}
			tr.el-table__row > td:last-child {
				border-radius: 0 6px 6px 0;
			}
		}
		.el-table__header-wrapper {
			border-radius: $borderRadius;
		}
		.el-table__body-wrapper {
			@include scrollBar;
			&::-webkit-scrollbar {
				height: 8px;
			}
		}
		.el-table__fixed,
		.el-table__fixed-right {
			height: auto !important;
			bottom: 11px;
			&::before {
				background-color: transparent;
			}
		}
	}
	// 分页
	&-pagination {
		padding: 0;
		.el-pagination__total {
			display: inline-flex !important;
			align-items: center;
			font-size: 12px !important;
			font-weight: 400;
			color: #2f446b;
			line-height: 20px !important;
		}
		.el-pagination__sizes {
			.el-input__inner {
				height: 26px;
				font-size: 12px !important;
				font-weight: 400;
				color: #2f446b;
				line-height: 20px;
			}
		}
		.el-pager {
			.number {
				font-size: 12px;
				font-weight: 400;
				line-height: 28px;
			}
		}
		.el-pagination__jump {
			margin-left: 13px;
			font-size: 12px;
			font-weight: 400;
			color: #2f446b;
			line-height: 20px;
			.el-pagination__editor {
				margin: 0 4px;
				.el-input__inner {
					width: 50px;
					height: 26px;
					background: #ffffff;
					border-radius: 3px;
					border: 1px solid $borderColor;
				}
			}
		}
	}

	// tab项
	&-tabs {
		.el-tabs__header {
			.el-tabs__item {
				font-size: 14px;
				font-weight: 400;
				color: #2f446b;
				line-height: 48px;
				height: 48px;
			}
			.is-active {
				font-weight: 600;
				color: var(--brand-6);
			}
			.el-tabs__active-bar {
				height: 3px;
				border-radius: 9px;
				background: var(--brand-6);
			}
			.el-tabs__nav-wrap {
				&::after {
					background-color: #f0f0f0;
				}
			}
		}
	}

	// 动态表单
	&-active-form {
		.el-form-item__label {
			font-size: 14px;
			font-weight: 400;
			color: #2f446b;
			line-height: 36px;
		}
		.el-input__inner {
			height: 40px !important;
			line-height: 40px !important;
			border: 1px solid $borderColor;
			border-radius: 6px;
			&::placeholder {
				font-size: 14px;
				font-weight: 400;
				color: $holderTextColor;
				line-height: 22px;
			}
		}
		.el-input-number__decrease,
		.el-input-number__increase {
			height: 20px !important;
			line-height: 19px !important;
		}
	}

	// 桌面端自定义弹窗 例：@/layout/components/setting/password 访问路径：点击头像-修改头像
	// 有的页面定制全局搜索desk-el-dialog参考
	&-custom-dialog {
		display: flex;
		align-items: center;
		justify-content: center;
		::v-deep .el-dialog {
			border-radius: 16px;
			min-width: 530px;
			max-height: 80%;
			margin: 0;
			overflow: auto;
			@include noScrollBar;
			.el-dialog__header {
				font-size: 18px;
				font-weight: 800;
				color: #303133;
				height: 46px;
				display: flex;
				align-items: center;
				//line-height: 24px;
				//padding: 27px 30px 4px;
			}
			.el-dialog__body {
				padding: 12px 30px;
			}
			.el-dialog__headerbtn {
				top: 15px;
				right: 20px;
			}
		}
	}

	// 租户端cv过来的弹窗dialog,和桌面端区分开 例：@/views/seal/index 访问路径/seal
	// 有footer使用-footer
	&-rent-dialog,
	&-rent-dialog-footer {
		.el-dialog {
			min-width: 750px;
			min-height: 600px;
			height: 80%;
			max-height: 80%;
			width: 70%;
			margin-top: 10vh !important;
			box-shadow: 0px 8px 10px -5px rgba(0, 0, 0, 0.08), 0px 16px 24px 2px rgba(0, 0, 0, 0.04),
				0px 6px 30px 5px rgba(0, 0, 0, 0.05);
			border-radius: $popupRadius;
			margin-bottom: 0;
		}
		.el-dialog__header {
			padding: 27px 30px 26px;
			.el-dialog__headerbtn {
				top: 30px;
				right: 31px;
			}
			.el-dialog__title {
				font-size: 18px;
				font-weight: 800;
				color: #303133;
				line-height: 24px;
			}
		}
		.el-dialog__body {
			padding: 0 32px 16px;
			height: calc(100% - 77px);
			display: flex;
			flex-direction: column;
			overflow-y: auto;
			@include noScrollBar;
		}
		.el-dialog__footer {
			padding: 24px 30px 20px;
		}
	}
	&-rent-dialog-footer {
		.el-dialog__body {
			height: calc(100% - 77px - 80px);
		}
	}
	// 输入框
	&-input {
		height: 40px;
		border-radius: $borderRadius;
		.el-input__prefix {
			left: 15px;
		}
		.el-input__inner {
			padding-left: 36px;
			height: 40px;
			border-radius: $borderRadius;
		}
	}

	// 下拉框
	&-select {
		margin-right: 6px;
		.el-input__inner {
			width: 140px;
			height: $buttonHeight;
			background: #ffffff;
			border-radius: $borderRadius;
			border: 1px solid $borderColor;
		}
		.el-input__icon {
			display: flex;
			flex-direction: column;
			justify-content: center;
		}
	}

	// 级联选择
	&-cascader {
		margin-right: 6px;
		.el-input__inner {
			width: 140px;
			height: $buttonHeight;
			background: #ffffff;
			border-radius: $borderRadius;
			border: 1px solid $borderColor;
		}
		.el-input__icon {
			display: flex;
			flex-direction: column;
			justify-content: center;
		}
	}

	// 搜索框
	&-search {
		width: 196px;
		margin-right: 9px;
		.el-input__inner {
			width: 100%;
			height: $buttonHeight;
			background: #ffffff;
			border-radius: $borderRadius;
			opacity: 1;
			border: 1px solid $borderColor;
			&::placeholder {
				font-size: 14px;
				font-weight: 400;
				color: $holderTextColor;
				line-height: 22px;
			}
		}
		.el-input__suffix {
			display: flex;
			align-items: center;
			margin-top: 2px;
		}
	}

	// 多选框
	&-checkbox-item {
		height: 38px;
		margin-bottom: 2px;
		font-size: 14px;
		font-weight: 500;
		color: $primaryTextColor;
		line-height: 22px;
		padding: 0 8px;
		@include flexBox(flex-start);
		.el-checkbox__inner {
			height: 16px;
			width: 16px;
		}
		.el-checkbox__label {
			padding-left: 12px;
		}
	}
	&-checkbox {
		.el-checkbox {
			height: 48px;
			margin-bottom: 2px;
			margin-right: 0;
			font-size: 14px;
			font-weight: 500;
			color: $primaryTextColor;
			line-height: 22px;
			padding: 0 8px;
			@include flexBox(flex-start);
		}
		.el-checkbox__inner {
			height: 16px;
			width: 16px;
		}
		.el-checkbox__label {
			font-size: 14px;
			font-weight: 400;
			color: #2f446b;
			line-height: 14px;
			padding-left: 8px;
			position: relative;
			width: 100%;
			padding-right: 60px;
			@include flexBox(flex-start);
		}
	}

	// 树形控件
	&-three {
		.el-tree-node__expand-icon {
			color: $subTextColor;
		}
		.el-tree-node__label {
			font-size: 14px;
			font-weight: 400;
			color: $primaryTextColor;
			line-height: 22px;
		}
	}

	// 空状态
	&-empty.el-empty {
		height: 100%;
		width: 100%;
		padding: 0;
	}
	// 按钮
	&-button {
		.el-button {
			width: 68px;
			height: 36px;
			border-radius: $borderRadius;
			font-size: 14px;
			font-weight: 400;
			line-height: 22px;
		}
		.el-button.is-disabled {
			background: #bae7ff;
		}
		.el-button--default {
			color: $primaryTextColor;
		}
		.el-button--primary {
			background: var(--brand-6);
			color: rgba(255, 255, 255, 0.9);
		}
	}
}
.el-menu--popup {
	.el-menu-item,
	.el-submenu__title {
		height: 40px;
		line-height: 40px;
		margin: 4px 10px;
		border-radius: 6px;
		&:hover {
			// 顶部菜单弹出层样式
			// background: var(--brand-6) !important;
			background: var(--brand-1) !important;
			// color: #ffffff !important;
			.el-submenu__icon-arrow {
				color: #ffffff !important;
				margin-top: -5px;
			}
		}
	}
	.el-submenu__title {
		& > div {
			margin-left: 0 !important;
		}
	}
	.nest-menu {
		.el-menu-item {
			padding-left: 20px !important;
		}
	}
	.menu-title {
		max-width: 126px;
	}
}
.el-menu-item.is-active {
	color: var(--brand-6) !important; //选中颜色
	background: var(--brand-1) !important;
}
.el-menu-item:hover {
	color: var(--brand-6) !important;
	background: var(--brand-1) !important;
}
// 有边框的table手动处理
.border-table {
	::v-deep .el-table__row {
		td {
			border-bottom: 1px solid #f0f0f0 !important;
		}
	}
}
// 提示窗口圆角
.el-message {
	min-width: 0;
}
.el-message-box {
	border-radius: $popupRadius !important;
}
// 所有按钮的圆角样式
.el-button {
	border-radius: $borderRadius !important;
}
// 图片预览的关闭弹窗
.el-image-viewer__btn.el-image-viewer__close {
	.el-icon-circle-close {
		color: #ffffff;
	}
}
// primary主色按钮  需去除，现在会让按钮无动态效果
// .el-button.el-button--primary {
// 	background-color: var(--brand-6);
// 	border-color: var(--brand-6);
// 	&:hover {
// 		background-color: var(--brand-6);
// 		border-color: var(--brand-6);
// 	}
// 	&:focus {
// 		background-color: var(--brand-6);
// 		border-color: var(--brand-6);
// 	}
// }
// .el-button.el-button--primary.is-ghost {
// 	background: 0 0;
// 	color: var(--brand-6) !important;
// }
.el-button--primary.is-disabled {
	background: $disabledTextColor !important;
	border-color: $disabledTextColor;
	&:hover {
		border-color: $disabledTextColor;
	}
}

.el-tabs__item.is-active {
	color: var(--brand-6) !important;
}
.el-tabs__active-bar {
	border: 1px solid var(--brand-6);
	background-color: var(--brand-6);
}

.el-switch.is-checked .el-switch__core {
	background: var(--brand-6) !important;
	border-color: var(--brand-6);
}
.el-range-editor.is-disabled,
.el-range-editor.is-disabled .el-input__inner,
.el-input.is-disabled .el-input__inner,
.el-range-editor.is-disabled input,
.el-textarea.is-disabled .el-textarea__inner {
	background: #ffffff !important;
	color: $textColor !important;
}
.el-input-number .el-input__inner {
	text-align: left !important;
}
// 新能源需求弹窗关闭按钮
.el-dialog__headerbtn {
	top: 18px;
	right: 18px;
	width: 28px;
	height: 28px;
}
.el-dialog__headerbtn .el-dialog__close {
	font-size: 22px;
	font-weight: bold;
}
// el-table show-overflow-tooltip 当内容过长被隐藏时显示 tooltip 最长不超过页面的80%
.el-tooltip__popper {
	max-width: 80%;
}
