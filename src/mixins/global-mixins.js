import { mapGetters } from 'vuex';
// import { preUrl } from '@/config';
export default {
	computed: {
		...mapGetters(['rentThem'])
	}
	// mounted() {
	// 	if (process.env.VUE_APP_ENV === 'development') {
	// 		this.$nextTick(() => {
	// 			const images = document.getElementsByTagName('img');
	// 			if (images.length === 0) return; // 优化：提前退出，避免无意义遍历
	// 			// 预编译正则表达式
	// 			const localhostPattern = /http:\/\/localhost:\d+/;
	// 			Array.from(images).forEach(item => {
	// 				try {
	// 					if (item.src && typeof item.src === 'string' && localhostPattern.test(item.src)) {
	// 						item.src = item.src.replace(localhostPattern, preUrl);
	// 					}
	// 				} catch (error) {
	// 					console.error('替换 img.src 时发生错误:', error); // 捕获并记录异常
	// 				}
	// 			});
	// 		});
	// 	}
	// }
};
