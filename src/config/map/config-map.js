/**可选打包配置*/
const CHOICES_LIST = [
	{
		name: '显示进度条',
		value: 'process'
	},
	/**
	 * // 与aieditor的babel-loader冲突，暂时停用
	{
		name: '开启多进程打包',
		value: 'multiCpu'
	},
	 */
	{
		name: '清除控制台打印',
		value: 'clearConsole'
	},
	{
		name: '启用拆包机制(正式环境必选)',
		value: 'splitChunks'
	},
	{
		name: '单独部署文件预览(正式环境可选，独立服务器，单独部署了文件预览服务，才选！)',
		value: 'independentPreview'
	},
	{
		name: '开启GZIP(正式环境可选，nginx配置支持GZIP，才选！)',
		value: 'gzip'
	},
	{
		name: '开启依赖分析(需要分析打包性能，才选！)',
		value: 'dependencies'
	}
];
module.exports = {
	type: 'checkbox',
	name: 'choices',
	message: '请选择打包配置',
	choices: CHOICES_LIST
};
