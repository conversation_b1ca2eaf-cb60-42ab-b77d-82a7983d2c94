const serveUrl = ''; //'https://dayuding.wisesoft.net.cn'
const socketUrl = '';

module.exports = {
	serveUrl,
	baseUrl: serveUrl + '/coos_api', // 本项目后端的接口前缀
	proxy: serveUrl, // 其他接口nginx代理
	preUrl: serveUrl, // 接口资源前缀
	assetsUrl: serveUrl + '/coos_img', // 静态资源前缀
	previewUrl:
		(process.env.VUE_APP_BUILD_CONFIG?.includes('independentPreview')
			? ''
			: 'http://coos.wisesoft.org.cn') + '/coos_file_preview_server/onlinePreview', // 预览资源地址
	webviewSocketUrl: socketUrl + '/coos_api/websocket/sys/', // 工作台 、第三方系统的socket通信连接
	// 其他系统
	otherSystem: [
		// 桌面端
		{
			name: 'COOS租户端',
			url: serveUrl + '/coos_rent/#/'
		}
	]
};
