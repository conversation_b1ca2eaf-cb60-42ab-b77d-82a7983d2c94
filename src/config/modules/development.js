// const serveUrl = 'http://192.168.201.34:9999'; // 曾睿
// const socketUrl = 'ws://192.168.201.34:9999'; // 曾睿
// const serveUrl = 'http://172.16.9.85:10055'; // 测试
// const socketUrl = 'ws://172.16.9.85:10055'; // 测试

const serveUrl = 'http://172.16.118.160:10999';
const socketUrl = 'ws://172.16.118.160:10999';
module.exports = {
	serveUrl,
	baseUrl: '/api',
	// baseUrl: '/api/coos_api', // /api  本项目后端的接口前缀
	proxy: '/api', // 其他接口nginx代理
	preUrl: serveUrl, // 接口资源前缀
	assetsUrl: serveUrl + '/coos_img', // 静态资源前缀
	previewUrl: 'http://coos.wisesoft.org.cn/coos_file_preview_server/onlinePreview', // 预览资源地址
	webviewSocketUrl: socketUrl + '/coos_api/websocket/sys/', // 工作台 、第三方系统的socket通信连接
	// 其他系统
	otherSystem: [
		// 租户端
		{
			name: 'COOS租户端',
			url: 'http://localhost:8082/coos_rent/#/'
		}
	]
};
