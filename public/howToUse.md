# 全局组件的使用文档

## 一、组织人员选择组件

### 使用示例

```vue
<template>
	<el-dialog :visible.sync="dialogVisible">
		<orgPersonnelSelect
			ref="orgPersonnelSelect"
			:init-func="initFunc"
			:init-params="initParams"
			:initValues="initValues"
			height="580px"
			@change="changeValue"
		></orgPersonnelSelect>
		<span slot="footer" class="dialog-footer">
			<el-button @click="close">取 消</el-button>
			<el-button type="primary" @click="sure">确 定</el-button>
		</span>
	</el-dialog>
</template>

<script>
import orgPersonnelSelect from '@/components/org-personnel-select';
export default {
	components: {
		orgPersonnelSelect
	},
	data() {
		return {
			initValues: [{ dataType: 'user', title: '王家梅', id: '1731598352319598593' }],
			dialogVisible: false,
			initFunc: getAvailableRange,
			initParams: {
				authorityType: 1, //类型;1.可用成员范围,2.禁止使用范围
				applicationId: ''
			}
		};
	},
	methods: {
		/**选择发生变化*/
		changeValue(val) {
			console.log(val);
		},
		/**确定*/
		sure() {
			this.$refs.orgPersonnelSelect.reset();
			this.dialogVisible = false;
		},
		/**取消*/
		close() {
			this.$refs.orgPersonnelSelect.reset();
			this.dialogVisible = false;
		}
	}
};
</script>

<style scoped lang="scss"></style>
```

### 参数 API 说明

| 参数               | 说明                                                 | 类型     | 默认值                                                  |
| ------------------ | ---------------------------------------------------- | -------- | ------------------------------------------------------- |
| `initFunc`         | 初始值回显的请求函数                                 | Function | null                                                    |
| `initParams`       | 请求函数对应的请求参数                               | Object   | null                                                    |
| `height`           | 组件高度                                             | String   | 580px                                                   |
| `dataSource`       | 数据来源，depart 部门，user 人员，label 标签         | Array    | ['depart', 'user', 'label']                             |
| `needAllData`      | change 回调时候是否返回全部数据                      | Boolean  | false(默认返回{objectId,ObjectType})                    |
| `includeUsers`     | 是否包含部门下面的人员                               | Boolean  | true                                                    |
| `canSelectDepart`  | 是否可以选择部门 ,false 的话就只能选择部门下面的人员 | Boolean  | true                                                    |
| `initValues`       | 回显的数据                                           | Array    | { dataType: '', title: '', id: '', avatarUrl(logo):'' } |
| `isRadio`          | 是否单选，只支持 data-source=depart 的时候           | Boolean  | false                                                   |
| `disabledIds`      | 指定不可选中的对象 id 集合                           | Array    | []                                                      |
| `default-org-name` | 默认选中的组织名称                                   | String   | 智汇数诚                                                |
| `disable-org`      | 是否禁选组织                                         | Boolean  | true                                                    |

| 事件回调名称 | 触发时机               | 参数示例              |
| ------------ | ---------------------- | --------------------- |
| change       | 选择数据发生变化时触发 | [{id:'',dataType:''}] |
| changeValue  | 数据是否被编辑过       | Boolean(false)        |

| 内部事件 | 作用               | 触发时机           |
| -------- | ------------------ | ------------------ |
| reset    | 重置组件的初始状态 | 弹窗关闭开启时重置 |

## 二、弹窗组织人员选择组件

### 使用示例

```vue
<!--  配置弹窗  -->
<orgPersonnelDialog
	:visible="visible"
	:init-func="initFunc"
	:init-params="initParams"
	:initValues="initValues"
	@change="changeSelect"
	@sure="sure"
	@close="close"
></orgPersonnelDialog>
<script>
import orgPersonnelDialog from '@/components/org-personnel-dialog';
export default {
	components: {
		orgPersonnelDialog
	},
	data() {
		return {
			initValues: [{ dataType: 'user', title: '王家梅', id: '1731598352319598593' }],
			visible: false,
			initFunc: getAvailableRange,
			initParams: {
				authorityType: 1, //类型;1.可用成员范围,2.禁止使用范围
				applicationId: ''
			}
		};
	},
	methods: {
		/**确定*/
		sure() {
			this.visible = false;
		},
		/**取消*/
		close() {
			this.visible = false;
		},
		/**人员选择发生变化*/
		changeSelect(val) {
			console.log('新值', val);
		},
		/**打开配置弹窗*/
		openConfig(type) {
			this.initParams.authorityType = type;
			this.visible = true;
		}
	}
};
</script>
```

### 参数 API 说明

| 参数               | 说明                                                 | 类型     | 默认值                                                  |
| ------------------ | ---------------------------------------------------- | -------- | ------------------------------------------------------- |
| `title`            | 弹窗标题                                             | String   | '应用可用范围管理'                                      |
| `visible`          | 弹窗显示隐藏                                         | Boolean  | false                                                   |
| `disableAll`       | 是否禁用全部标签                                     | Boolean  | false                                                   |
| `initFunc`         | 初始值回显的请求函数                                 | Function | null                                                    |
| `initParams`       | 请求函数对应的请求参数                               | Object   | null                                                    |
| `dataSource`       | 数据来源，depart 部门，user 人员，label 标签         | Array    | ['depart', 'user', 'label']                             |
| `needAllData`      | change 回调时候是否返回全部数据                      | Boolean  | false(默认返回{objectId,ObjectType})                    |
| `sureButton`       | 确定按钮的文字                                       | String   | 保存                                                    |
| `caleButton`       | 关闭按钮的文字                                       | String   | 取消                                                    |
| `includeUsers`     | 是否包含部门下面的人员                               | Boolean  | true                                                    |
| `canSelectDepart`  | 是否可以选择部门 ,false 的话就只能选择部门下面的人员 | Boolean  | true                                                    |
| `sureLoading`      | 确认按钮的加载状态，调用接口时候可以设置为 true      | Boolean  | false                                                   |
| `isAll`            | 打开弹窗的时候默认选中全部                           | Boolean  | false                                                   |
| `initValues`       | 回显的数据                                           | Array    | { dataType: '', title: '', id: '', avatarUrl(logo):'' } |
| `isReset`          | 是否在关闭弹窗时重置表单                             | Boolean  | false                                                   |
| `isRadio`          | 是否单选，只支持 data-source=depart 的时候           | Boolean  | false                                                   |
| `disabledIds`      | 指定不可选中的对象 id 集合                           | Array    | []                                                      |
| `default-org-name` | 默认选中的组织名称                                   | String   | 智汇数诚                                                |
| `disable-org`      | 是否禁选组织                                         | Boolean  | true                                                    |

| 事件回调名称 | 触发时机                                     | 参数示例                    |
| ------------ | -------------------------------------------- | --------------------------- |
| change       | 选择数据发生变化时触发(选择的集合，是否全选) | [{id:'',dataType:''}],false |
| sure         | 弹窗确认事件(选择的集合，是否全选)           | [{id:'',dataType:''}],false |
| close        | 弹窗关闭事件                                 | [{id:'',dataType:''}]       |
| changeRadio  | 部分成员和全部成员的切换                     | 1 部分成员，2 全部成员      |

## 三、上传组件 UpLoadFile

### 使用示例

```vue
<template>
	<uploadFile
		v-model="ids"
		:other-params="otherParams"
		:can-download="true"
		:multiple="false"
		mode="file"
		:limit="3"
	/>
</template>

<script>
export default {
	data() {
		return {
			otherParams: {
				applicationId: '1729387429589024769',
				dataId: '1729387429589024769',
				fieldCode: 'logoUrl',
				moduleCode: 'sysApplication'
			},
			ids: '' //,1730107568341004290
		};
	}
};
</script>

<style scoped lang="scss"></style>
```

### 参数 API 说明

| 参数           | 说明                                                                                                                          | 类型                    | 默认值 |
| -------------- | ----------------------------------------------------------------------------------------------------------------------------- | ----------------------- | ------ |
| `v-model`      | 双向绑定的文件 ID，可以回显，也可以快捷取得已经上传文件的 ID，多条数据逗号隔开                                                | ref<String>             | ''     |
| `accept`       | 文件上传类型 可以传具体的文件类型 png,excel,doc 也可以传类型 image,video,audio                                                | Array                   | ['*']  |
| `multiple`     | 是否支持多传                                                                                                                  | Boolean                 | true   |
| `mode`         | 组件模式,可选值 image/file                                                                                                    | String                  | image  |
| `modelSize`    | 当 mode=image 时控制组件大小的属性                                                                                            | Number                  | 120    |
| `modelHeight`  | 当 mode=image 时，控制图片和上传按钮的高度                                                                                    | Number                  | 0      |
| `modelWidth`   | 当 mode=image 时，控制图片和上传按钮的宽度                                                                                    | Number                  | 0      |
| `otherParams`  | 上传文件时需要带上的参数                                                                                                      | Obje                    | {}     |
| `canDownLoad`  | 是否支持下载                                                                                                                  | Boolean                 | false  |
| `limit`        | 最多可以传几张                                                                                                                | Number                  | 1      |
| `data-tye`     | 数据类型,url 的话，value 传值要是 url，返回的值也是 url                                                                       | String('url','id')      | id     |
| `customButton` | 上传按钮是否自定义插槽，上传按钮配合插槽 customButton                                                                         | Boolean                 | false  |
| `isPreview`    | 当 mode=image 时，是否要回显预览图片                                                                                          | Boolean                 | true   |
| `customFile`   | 上传的样式以及预览的样式是否自定义，预览插槽 customFile，slot-scoped={upLoadFile,showPreviewArr} 上传成功的数组，文件预览数组 | Boolean                 | false  |
| `noFlex`       | 不使用弹性布局                                                                                                                | Boolean                 | false  |
| `uploadType`   | 上传接口 单文件                                                                                                               | String<single/multiple> | single |
| `disabled`     | 是否禁用                                                                                                                      | Boolean                 | false  |

| 插槽名称       | 说明                                                                                              |
| -------------- | ------------------------------------------------------------------------------------------------- |
| `customButton` | 当设置了 customButton 为 true 时，上传按钮自定义插槽，按钮自定义样式                              |
| `customFile`   | 当设置了 customFile 为 true 时，上传组件自定义插槽，按钮和预览都自定义样式 slot-scoped=upLoadFile |

| 事件        | 说明                                                 |
| ----------- | ---------------------------------------------------- |
| `input`     | 上传成功之后返回的 id 拼接，例如 a,b                 |
| `change`    | 上传成功之后返回的 id 拼接，例如 a,b                 |
| `success`   | 上传成功之后返回的数据组合，[]                       |
| `onProcess` | 上传过程中实时进度监听，返回上传的数据组合[]         |
| `del`       | 用$refs 实例调用 del 方法，参数是 onProcess 数组索引 |

## 四、文件回显组件 FileById

### 使用示例

```vue
<template>
	<FileById :value="ids" :size="120" :canPreView="true" />
</template>

<script>
export default {
	data() {
		return {
			ids: ''
		};
	}
};
</script>

<style scoped lang="scss"></style>
```

### 参数 API 说明

| 参数          | 说明                             | 类型        | 默认值 |
| ------------- | -------------------------------- | ----------- | ------ |
| `value`       | 需要回显的文件 ID,多个逗号，隔开 | ref<String> | []     |
| `width`       | 单独控制宽度                     | Number      | 0      |
| `height`      | 单独控制宽高度                   | Number      | 0      |
| `size`        | 统一控制宽高，正方形布局         | Number      | 80     |
| `moreStyle`   | 控制回显 dom 的样式拓展          | Object      | {}     |
| `canPreView`  | 是 否支持预览                    | Boolean     | false  |
| `canDownLoad` | 是否支持下载                     | Boolean     | false  |

## 五、树结构

### 使用示例

```vue
<template>
	<customTree></customTree>
</template>

<script>
import customTree from '@/third-party/energy/components/custom-tree';
export default {
	components: { customTree },
	data() {
		return {};
	}
};
</script>

<style scoped lang="scss"></style>
```

### 参数 API 说明(支持 el-tree 全部参数，可参考 element 官网)

| 参数           | 说明                       | 类型    | 默认值 |
| -------------- | -------------------------- | ------- | ------ |
| `showCheckBox` | 是否多选                   | Boolean | false  |
| `customDialog` | 是否自定义新增或者编辑弹窗 | Boolean | false  |
| `treeData`     | 树数据                     | Array   | []     |

### 插槽

| 插槽名称 | 参数                                      | 说明                                                          |
| -------- | ----------------------------------------- | ------------------------------------------------------------- |
| `popup`  | slot-scoped="{currentNode}"当前选中的节点 | 新增编辑弹窗 body 的内容插槽，customDialog 设置为 true 时无效 |

### 事件(支持 el-tree 全部事件派发，可参考 element 官网)

| 事件名称  | 参数                                                                             | 说明                            |
| --------- | -------------------------------------------------------------------------------- | ------------------------------- |
| `add`     | 当前选中的节点 currentNode                                                       | customDialog 设置为 true 时有效 |
| `edit`    | 当前选中的节点 currentNode                                                       | customDialog 设置为 true 时有效 |
| `delNode` | 当 showCheckBox 为 true 时返回选择的节点集合，否则返回当前选中的节点 currentNode | 删除节点                        |
| `refresh` | --                                                                               | 刷新                            |

## 六、编辑表格

### 使用示例

```vue
<template>
	<editTable
		:can-edit="true"
		:show-checkbox="true"
		:show-delete="true"
		:show-add="true"
		:sort="true"
		:options="{
			subId: subId
		}"
		:table-data="tableData"
		:table-column="tableColumn"
	></editTable>
</template>

<script>
import editTable from '@/components/editTable';
export default {
	components: { editTable },
	data() {
		return {
			subId: [
				{
					label: '测试1',
					value: 'test1'
				},
				{
					label: '测试2',
					value: 'test2'
				}
			],
			tableData: [
				{
					checkDate: '2024-10-09',
					controlPoints: '测试1',
					checkOrg: '智胜集成01',
					subId: 'test1'
				},
				{
					checkDate: '2024-10-08',
					controlPoints: '测试2',
					checkOrg: '智胜集成02',
					subId: 'test2'
				}
			],
			tableColumn: [
				{
					prop: 'checkDate',
					label: '检查日期',
					type: 'date',
					minWidth: '260',
					formInputConfig: {
						format: 'yyyy-MM-dd',
						valueFormat: 'yyyy-MM-dd'
					}
				},
				{
					prop: 'subId',
					label: '检查子项',
					minWidth: '180',
					type: 'select'
				},
				{
					prop: 'controlPoints',
					label: '检查内容',
					isReadOnly: true,
					type: 'text',
					minWidth: '240',
					formInputConfig: {
						autosize: {
							minRows: 10
						},
						maxLength: 2000,
						type: 'textarea'
					}
				},
				{
					prop: 'checkOrg',
					label: '组织单位',
					type: 'input',
					minWidth: '180',
					formInputConfig: {
						maxLength: 50
					}
				}
			]
		};
	}
};
</script>

<style scoped lang="scss"></style>
```

### 参数 API 说明(支持 el-tree 全部参数，可参考 element 官网)

| 参数           | 说明                                                        | 类型          | 默认值 |
| -------------- | ----------------------------------------------------------- | ------------- | ------ |
| `其他`         | element-table 所支持的属性                                  | string        | ''     |
| `showDelete`   | 是否显示删除按钮                                            | Boolean       | false  |
| `showAdd`      | 是否显示新增按钮                                            | Boolean       | false  |
| `sort`         | 是否显示排序                                                | Boolean       | false  |
| `showCheckbox` | 是否显示多选                                                | Boolean       | false  |
| `tableColumn`  | 表格渲染列(详细配置减后面 tableColumn 的说明)               | Array<Object> | []     |
| `tableData`    | 表格数据                                                    | Array<Object> | []     |
| `options`      | 下拉选择数据映射，key 为对应 column 的 prop，value 为选择项 | Object        | {}     |
| `canEdit`      | 是否可以编辑                                                | Boolean       | true   |
| `showCheckbox` | 是否多选                                                    | Boolean       | true   |

### tableColumn 的说明

| 参数              | 值说明                                                                                        |
| ----------------- | --------------------------------------------------------------------------------------------- |
| `支持`            | element-table-tableColumn 所支持的属性，例如：prop、label、minWidth                           |
| `type`            | 渲染表单类型 select、date、input、text、time、dateTime、daterange、view、file、string、number |
| 'formInputConfig' | 对应表单类型在 element-ui 中支持的更多属性                                                    |
| 'isReadOnly'      | 是否只读                                                                                      |

### 事件

| 事件名称          | 参数                                                                                      | 说明                             |
| ----------------- | ----------------------------------------------------------------------------------------- | -------------------------------- |
| `click`           | key:当前点击表单项的 key row:当前表单项所在行的数据                                       | 表格中表单项点击的时候触发       |
| `focus`           | key:当前点击表单项的 key row:当前表单项所在行的数据 index:点击数据的索引                  | 表格中表单项聚焦的时候触发       |
| `change`          | key:当前点击表单项的 key val:当前改变的值 row:当前表单项所在行的数据 index:点击数据的索引 | 表格中的数据发生改变触发         |
| `uploadFile`      | index:点击数据的索引                                                                      | 点击上传按钮或者查看附件的按钮   |
| `handleCellAdd`   | key:当前点击表单项的 key row:当前表单项所在行的数据 index:点击数据的索引                  | 绑定在某个字段上的添加按钮       |
| `handleClickShow` | key:当前点击表单项的 key row:当前表单项所在行的数据 index:点击数据的索引                  | 绑定在某个字段上的显示和隐藏按钮 |
| `handleItemAdd`   | index:点击数据的索引                                                                      | 操作栏的添加按钮                 |
| `handleItemDlt`   | index:点击数据的索引                                                                      | 操作栏的删除按钮                 |

## 七、数据选择之表格

### 使用示例

```vue
<template>
	<div>
		<div class="dev-table-select" @click="openSelectTable">
			<span v-if="tableSelectValue.length > 0">{{ tableSelectValue.join(',') }}</span>
			<span v-else class="placeholder">请选择数据</span>
		</div>
		<tableSelect ref="tableSelect" :tableHeader="tableHeader" :tableData="tableData"></tableSelect>
	</div>
</template>

<script>
import tableSelect from '@/components/table-select';
export default {
	components: { tableSelect },
	data() {
		return {
			tableSelectValue: [],
			tableHeader: [
				{
					label: '编码',
					prop: 'code',
					minWidth: 160,
					align: 'left',
					showOverflowTooltip: false
				},
				{
					label: '名称',
					prop: 'name',
					minWidth: 160,
					align: 'left',
					showOverflowTooltip: false
				}
			],
			tableData: [
				{
					code: 'test1',
					name: '测试1'
				},
				{
					code: 'test2',
					name: '测试2'
				}
			]
		};
	},
	methods: {
		/**打开弹窗*/
		openSelectTable() {
			this.$refs.tableSelect.open();
		}
	}
};
</script>

<style scoped lang="scss"></style>
```

### 描述：支持函数+参数，也支持 tableData 数据传入

### 参数 API 说明(支持 el-table 全部参数，可参考 element 官网)

| 参数            | 说明                                         | 类型            | 默认值 |
| --------------- | -------------------------------------------- | --------------- | ------ |
| `其他`          | element-table 所支持的属性                   | String          | ''     |
| `title`         | 弹窗标题                                     | String          | ''     |
| `requestFun`    | 请求函数                                     | Function/Object | null   |
| `requestParams` | 请求参数，除了关键词和分页数据               | Object          | {}     |
| `tableHeader`   | 表头，支持 el-table 中 tableColumns 所有参数 | Array<Object>   | []     |
| `tableData`     | 表格数据                                     | Array<Object>   | []     |

### 事件

| 事件名称              | 参数                              | 说明             |
| --------------------- | --------------------------------- | ---------------- |
| `reset`               | 空                                | 重置             |
| `search`              | keywords                          | 搜索             |
| `handleCurrentChange` | pageNo 分页页码 pageSize 分页大小 | 当前页码发生变化 |
| `handleSizeChange`    | pageNo 分页页码 pageSize 分页大小 | 分页大小发生变化 |
| `confirm`             | 当前选中行                        | 确认或者双击触发 |

## 八、流程办理判断是否需要选择下一步处理人

### 使用示例

```vue
<template>
	<div>
		<button type="primary" @click="next">下一步</button>
		<flowNext ref="flowNext" @nextSelected="nextSelected"></flowNext>
	</div>
</template>

<script>
import flowNext from '@/components/flow-next';
export default {
	components: { flowNext },
	data() {
		return {};
	},
	methods: {
		/**确认节点选择*/
		nextSelected(form) {
			console.log('节点选中--------', form);
		},
		/**打开下一步节点*/
		async next() {
			let boolean = await this.$refs.flowNext.check('AFenergy-om-safetyins-template', {});
			if (boolean) {
				this.$message.success('提交成功');
			}
		}
	}
};
</script>

<style scoped lang="scss"></style>
```

### 内部事件，调用打开

| 事件名称 | 参数                               | 说明                                                                            |
| -------- | ---------------------------------- | ------------------------------------------------------------------------------- |
| `check`  | id:流程 id params:填写好的表单数据 | 检测需不需要选择下一步节点，不需要的话返回 true，需要的话自动弹窗渲染出选择逻辑 |

### 事件

| 事件名称       | 参数                       | 说明                                        |
| -------------- | -------------------------- | ------------------------------------------- |
| `nextSelected` | vars:下一步节点的 map 映射 | 下一步节点的 key，选择后的 value 组成的 map |

## 九、公共弹窗新增或者编辑

### 使用示例

调用使用 ref 实例调用 open 和 close 方法

```vue
<template>
	<div>
		<button type="primary" @click="openDialog">打开</button>
		<customDialog ref="customDialog" @save="saveDialog"></customDialog>
	</div>
</template>

<script>
import customDialog from '@/third-party/energy/components/custom-dialog';
export default {
	components: { customDialog },
	data() {
		return {};
	},
	methods: {
		/**存储弹窗*/
		saveDialog() {
			console.log('存储弹窗');
			setTimeout(() => {
				this.$refs.customDialog.close();
			}, 1000);
		},
		/**打开弹窗*/
		openDialog() {
			this.$refs.customDialog.open();
		}
	}
};
</script>

<style scoped lang="scss"></style>
```

### 参数 API

| 参数            | 说明                     | 类型    | 默认   |
| --------------- | ------------------------ | ------- | ------ |
| `readonly`      | 是否只读                 | Boolean | false  |
| `btnLoading`    | 保存按钮是否异步 loading | Boolean | true   |
| `title`         | 弹窗标题                 | String  | '标题' |
| `submitBtnText` | 提交按钮文字             | String  | '提交' |
| `closeBtnText`  | 关闭按钮文字             | String  | '取消' |

### 事件

| 事件名称 | 参数 | 说明 |
| -------- | ---- | ---- |
| `save`   | 无   | 保存 |

### 插槽

| 插槽名称  | 参数 | 说明 |
| --------- | ---- | ---- |
| `defatul` | 无   | body |

## 十、UI 规范

```json
第一组主题相关
#2778e5=>#0f45ea
#175ABF=>#0235BA
#CFEAFF=>#d1e2Fb
#509CF2=>#3667FC
第二组文字相关
#262626=>#15224C
#404040=>#2F446B
#8c8c8c=>#737a94
#bfbfbf=>#b9bdc9
#BBBFC4=>#d1d3dB
边框
#D9D9D9=>#BCCADB
分割线
#EBEDF0=>#CDD9E4
```

## 十一、全局待办弹窗的使用

### 说明

直接打开待办弹窗

### 使用示例

```vue
<template>
	<waitDetail ref="waitDetail"></waitDetail>
</template>
<script>
import waitDetail from '@/components/wait-detail/index.vue';
export default {
	components: {
		waitDetail
	},
	methods: {
		open() {
			this.$refs.waitDetail.open(data);
		}
	}
};
</script>
```
