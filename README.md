# COOS 产品桌面端

## 版本管理：

### git：http://git.wisesoft.net.cn/coos2023/coos-desktop-app.git

### 分支：dev(开发)、alpha(内侧)、beta(公测)、release(产线)、1.0(历史版本)

## 使用说明：

```js
cnpm install // 依赖安装

cnpm run build:dll // 依赖抽取

cnpm run dev:development // 启动
```

## 集成说明

1、在 third-party 目录下拉取业务板块的代码，如果历史开发的，比如 omip 和 energy，路由引用的文件地址
使用的 omip 和 energy，所以拉取代码后要修改目录名称。

2、src/third-party/third-git.js 是业务板块 git 的映射文件，新增的业务板块可以把映射加进去。

3、为了优化打包耦合性，在@/config/third-party-map 加上配置，便于启动、打包交互选择业务板块

4、在 router/index 下添加一个环境判断引入对应模块的 router.js，暂不支持动态判断，后续优化

## 集成示例

![集成示例.png](http://dayuding.eimm.wisesoft.net.cn:8099/coos_img/desk-app/md.png)

## 更多使用说明和代码规范可参考：

http://dayuding.eimm.wisesoft.net.cn:8099/coos_docs/docs/book/coos-web/a-specification
