/*
 * @Description: 选择环境和文件对umd进行打包
 * @Version: 1.0
 * @Autor: fanghonghao
 * @Date: 2024-02-04 17:07
 * @LastEditors: fanghonghao
 * @LastEditTime:  2024-02-04 17:07
 */
const program = require('commander');
const spawn = require('cross-spawn');
const inquirer = require('inquirer'); // 模板选择
const { version } = require('../../package.json');
const envMap = require('../../src/config/map/env-map.js');
const thirdAPartyMap = require('../../src/config/map/third-party-map');
const configMap = require('../../src/config/map/config-map.js');
const projectMap = require('../../src/config/map/project-config-map.js');
const fs = require('fs');
const path = require('path');
const colors = require('colors-console')
const updateRepos = require('../../pull-git');
/**打包*/
program
	.version(version) // 设置版本号
	.command('build:project') // 添加命令
	.description('打包项目工程') // 描述
	.action(async () => {
		try {
			// 选择打包环境
			const env = await inquirer.prompt(envMap);
			const envName = env.type;
      // 选择项目配置
      const projectArr = await inquirer.prompt(projectMap);
      process.env.VUE_APP_PROJECT = projectArr.choices;
      // 选择集成的第三方业务板块
      const data = await inquirer.prompt(thirdAPartyMap);
      const choices = data.choices;
      // 选择打包配置
      const config = await inquirer.prompt(configMap);
      process.env.VUE_APP_BUILD_CONFIG = config.choices;
      const thirdPartyPath = '../../src/third-party';
      try{
        choices.forEach(choice => {
          const exists = fs.existsSync(path.join(__dirname,  `${thirdPartyPath}/${choice}`));
          if(exists){
            // console.log(colors('green',`三方模块${choice}检测通过  √√√√√√√√`));
          }else{
            // TODO 拉取对应模块的git 业务模块的git映射在third-party-git.map
            throw new Error(`在third-party目录下不存在三方模块${choice}   ××××××××`)
          }
        })
      }catch(err){
        console.error(colors('red',err.message));
        return
      }
      // 循环添加业务板块_变量(vue文件中判断直接用VUE_APP_THIRD_PARTY不生效，只能一个一个的添加变量)
      thirdAPartyMap.choices.forEach(choice => {
        if(choices.includes(choice.value)){
          process.env['VUE_APP_'+choice.value.toUpperCase()] = 'true'
        }else{
          process.env['VUE_APP_'+choice.value.toUpperCase()] = 'false'
        }
      })
      // 业务板块集合
      process.env.VUE_APP_THIRD_PARTY = choices
      // 定义 version.json 的路径
      const versionFilePath = path.join(__dirname, '../../public','version.json');
      // 要写入的内容（当前时间戳）
      const versionData = {
        version: new Date().getTime() // 获取毫秒时间戳
      };
      // 异步写入文件
      try{
        fs.writeFileSync(
          versionFilePath,
          JSON.stringify(versionData, null, 2) // 格式化 JSON，缩进 2 空格
        )
        console.log(colors('green',`version.json 已更新:${versionData.version}`));
      }catch(err){
        console.error(colors('red',`写入 version.json 失败:----${err}`));
      }
			// 指令
			const command_query = [`VUE_APP_ENV=${envName}`,'vue-cli-service', `build`, '--mode', envName];
      try {
        // 打包强制更新最新代码
        await updateRepos();
        // 执行指令
        spawn.sync('cross-env', command_query, {
          stdio: 'inherit'
        });
      } catch (error) {
        console.error('更新仓库失败:', error);
      }

		} catch (e) {
			process.exit(1); // 出现异常关闭当前进程
		}
	});
/**开发启动*/
program
  .version(version) // 设置版本号
  .command('dev:project') // 添加命令
  .description('启动项目工程') // 描述
  .action(async () => {
    try {
      // ，开发环境启动，只用选择集成的第三方业务板块，暂不支持环境切换
      const data = await inquirer.prompt(thirdAPartyMap);
      const choices = data.choices;
      const thirdPartyPath = '../../src/third-party';
      try{
        choices.forEach(choice => {
          const exists = fs.existsSync(path.join(__dirname,  `${thirdPartyPath}/${choice}`));
          if(exists){
            console.log(colors('green',`三方模块${choice}检测通过  √√√√√√√√`));
          }else{
            // TODO 拉取对应模块的git 业务模块的git映射在third-party-git.map
            throw new Error(`在third-party目录下不存在三方模块${choice}   ××××××××`)
          }
        })
      }catch(err){
        console.error(colors('red',err.message));
        return
      }
      // 循环添加业务板块_变量(vue文件中判断直接用VUE_APP_THIRD_PARTY不生效，只能一个一个的添加变量)
      thirdAPartyMap.choices.forEach(choice => {
        if(choices.includes(choice.value)){
          process.env['VUE_APP_'+choice.value.toUpperCase()] = 'true'
        }else{
          process.env['VUE_APP_'+choice.value.toUpperCase()] = 'false'
        }
      })
      // 业务板块集合
      process.env.VUE_APP_THIRD_PARTY = choices
      // 指令
      const command_query = ['VUE_APP_ENV=development','vue-cli-service', `serve`, '--port=8081', '--mode', 'development'];
      // 执行指令
      spawn.sync('cross-env', command_query, {
        stdio: 'inherit'
      });
    } catch (e) {
      process.exit(1); // 出现异常关闭当前进程
    }
  });
program.parse(process.argv);
