const path = require('path');
const webpack = require('webpack');
// 管理打包后的文件
const FileManagerPlugin = require('filemanager-webpack-plugin');
const ProgressBarPlugin = require('progress-bar-webpack-plugin');
// const CompressionPlugin = require('compression-webpack-plugin');
// dll保存目录
const dllPath = path.resolve(__dirname, './vendor');
module.exports = {
	performance: { hints: false },
	entry: {
		// 要提取成dll的依赖包
		libs: ['vue', 'element-eoss', 'js-cookie', 'axios']
	},
	output: {
		// dll打包文件输出的目录
		path: dllPath,
		// dll包命名
		filename: '[name].dll.js',
		// 会将值（dll文件）作为变量声明导出（当使用 script 标签时，其执行后在全局作用域可用）
		// 为了保持变了和umd组件统一不使用hash:8
		library: '[name]_dll'
	},
	plugins: [
		new ProgressBarPlugin(),
		new FileManagerPlugin({
			events: {
				onStart: [
					{
						// 开始编译之前先清除之前的dll
						delete: [dllPath]
					}
				]
			}
		}),
		// new CompressionPlugin({
		// 	test: /\.(js|css)(\?.*)?$/i, //需要压缩的文件正则
		// 	threshold: 1024, //文件大小大于这个值时启用压缩
		// 	deleteOriginalAssets: false //压缩后保留原文件
		// }),
		new webpack.DllPlugin({
			// 描述动态链接库的 manifest.json 文件输出时的文件名称
			path: path.join(dllPath, '[name]-manifest.json'),
			// 该字段的值也就是输出的 manifest.json 文件 中 name 字段的值
			name: '[name]_dll', // 要与output.library一致
			// 关闭进程
			context: process.cwd()
		})
	]
};
