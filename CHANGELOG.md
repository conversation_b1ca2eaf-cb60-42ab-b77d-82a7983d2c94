# 1.0.0 (2025-04-11)


### Bug Fixes

*  coosAi助手联调 ([144e341](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/144e341dd42bcf962842ec574b0ed0991792c096))
* 'coos智能助手拖动完成' ([31cf924](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/31cf9243a96c288c6afc37b43d2fcbaaaa5ac94c))
* 阿里icon线上链接 ([f472037](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f47203709f4e0f08ab53edbef73e06f2df617b37))
* 安全指标增加状态 ([202172c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/202172c64efecea79c8bc8d20dbb7fd3d8805ea4))
* 安装依赖 ([d89186f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d89186f73d0827875054b3961c4f8896fa8a8cd5))
* 按钮效果 ([aec3fb2](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/aec3fb29c259f8ba0529950abbbc135f69d8b3e1))
* 版本不一致时候的处理逻辑 ([4cafdd8](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/4cafdd853200c43cb58d5fc04e20c29989884062))
* 办理 ([21ff014](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/21ff014bcfc273a13c91f954305edc4cd3e3cabe))
* 包冲突 ([d51340b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d51340b01fc34c89304588d9b210c55f4da03f98))
* 保持页面状态 ([f0c1f5a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f0c1f5a0897242cb27a0708158c34062ed9c6da3))
* 保存loading效果 ([d83561f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d83561fad5292f0316b2aa3f6d6d8a3003dfad0b))
* 报错和解析打印 ([f767da7](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f767da7b7a92ddd9b7854c9eeda328e0b10b668a))
* 报错信息 ([7bd734a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7bd734a46ebec0b83aafbf389ad289d1c0e0ec6d))
* 备忘录编辑 ([fa8bfae](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/fa8bfaea0ce594100b49b32da3c3d113888094f8))
* 备忘录判断显示 ([589ccab](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/589ccab89796107c3941bca6a10d7b22a4d22331))
* 备忘录升级 ([eac7142](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/eac7142417b7069e17edf6b8926630badaab7994))
* 备忘录跳转 ([daca293](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/daca2938f6d93aed57520d8df54a1bbbae63d4ca))
* 备忘录样式 ([6c80e1a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/6c80e1aea258409e5511e324db49e35c7b1ca6c5))
* 备忘录样式调整 ([e2b00e4](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e2b00e4df21aecb594de5bcba91fb3b73a00f3e8))
* 背景图不被压缩 ([92471d0](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/92471d0eb45b4891ef31863ccedd15187fa295fc))
* 本地调试出的bug ([dc13c6b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/dc13c6b85116b097dcdf7df8e78b801bf5e8705c))
* 必填项目提醒 ([74b2eb9](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/74b2eb92d7d20973b66c34f875913213124e143f))
* 边框颜色和分割线颜色 ([feb0f7c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/feb0f7c075b466acbcb31e8f67cb365363feba5c))
* 编辑标题 ([0e99dc5](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0e99dc5c56b5a6c2e27ff7fae29d8f67de187b74))
* 编辑表格使用文档 ([afa183b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/afa183b240454c609ed5fc8aa926700bcc320337))
* 编辑弹窗(开发中) ([37e8717](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/37e87173abe1d3ddfbffec85822f698c90a3a189))
* 变量统一 ([d36c5f8](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d36c5f848a2c153410c1a5cc09cc6caed20923e7))
* 标记判断处理 ([a61882c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a61882cc1f9dda4376da5f269f3d1b880afe4a17))
* 标记完成 ([cf6fb0d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/cf6fb0d65f378b0480cc9426f95a2f06cc9466b2))
* 标记样式改为行内 ([d2d42fd](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d2d42fd58544c53eb16e8cb66437b7eacb290d23))
* 标签面包屑重复显示的问题 ([a785635](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a785635586958ecf8ecab2783481c8f93ab30f79))
* 标签问题 ([f3585fe](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f3585fe935027ab5cb3aa19b2ee2e73c38e21586))
* 标签项 ([b0c9794](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b0c979402dee8b41ab9fb866bd1e358483ebedf4))
* 标签修复 ([621b545](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/621b545da4ee1593949835778e140be30305105e))
* 标签样式 ([0120a42](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0120a4223504768b8d6f13c48d6e414987778763))
* 标签页的动态把控 ([a4dd271](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a4dd271753962d016d44a2095dfe4423880fc5c1))
* 标题快速生成 ([144c310](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/144c3103dccf71578a1858b248c5608be41a12a9))
* 表单和三方系统的ai入口 ([a8694f4](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a8694f4b73f70ae5248894af4a14964416c8b855))
* 表单切换bug ([47ae604](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/47ae604a4c6af379e703980c383965ed14d069fe))
* 表单缩放 ([556dcad](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/556dcadbb821e39e295934d8230dd42d1db74226))
* 表单提交防抖节流 ([6eb362c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/6eb362cb6589edc28c02530429b30c78d946f236))
* 表单校验 ([06b39d4](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/06b39d43f65cd905c5bd73687575f0d7e558e308))
* 表单样式 ([d66639b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d66639b2377d3975df368eb80a199be6138a1038))
* 表单优化 ([1e83871](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/1e838717f2fbaaaf35626968d152d0f6ac91a063))
* 表单增加删除功能 ([64fc39b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/64fc39bc7cd2d0d18a8d6ab6166a5a8f028a64f0))
* 表单增加搜索功能 ([cc98652](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/cc986522e0768e31087a8015605f75256f3579fe))
* 表单增加搜索功能 ([8c0abef](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/8c0abef0703126f8ae15756f1e3c2260b41ac478))
* 表单智能对话不用请求历史记录 ([e821642](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e821642a990ec6134a39f3b5a9fde5c39e751a7e))
* 表格固定 ([750eb04](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/750eb04320e35647720057d15448bdca8029441a))
* 表格没对齐的问题 ([adfcff6](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/adfcff62637f68faa4a0adbcd1d71a0dfaac81c5))
* 表格样式 ([4487fdf](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/4487fdf008be0df4c17d51465507b2cdfe4a965e))
* 表格溢出样式调整 ([37aa924](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/37aa92484a05e84ab5e7316aaad49e78a12ed4b3))
* 表格最小宽度 ([d2c817f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d2c817f251bd5c7ff2529fc07990b4ba4f059166))
* 表情包同步 ([16e02c6](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/16e02c6a180956f95fd14e79b22db069ae3d8d59))
* 补充文件下载字段更改 ([3e23162](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/3e231624e585c5c93cce1f07853cad6b1ccb2696))
* 补充文件源文件名 ([dd18e3a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/dd18e3a56d7aad9a40db8119ee792752ceed08ee))
* 补充用户信息无效后退出三方系统 ([0ca5914](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0ca59144a77ad1aea0c2148123cc6bbaa121a71c))
* 不监听频道 ([b769a19](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b769a1937589df98be914bbe5f565e157da38d78))
* 不进行加密 ([aaabd55](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/aaabd558067b3ba99d4782b8b14b8eaf4fdb53e5))
* 不同布局的兼容 ([185f95f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/185f95fef532c7393dacd2c429000671c0bca5bf))
* 不同框架的不同主体背景色 ([e54f76b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e54f76bd7ba2e60ea0c9de772cc7d3f818948f50))
* 不需要开始节点,优化流程图下一步办理人的显示 ([671c951](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/671c95146055220b2a32248253748005345b804e))
* 布局响应式 ([5dca5b6](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5dca5b69cab6cbefc05e44311a09da2e6d3f3928))
* 部门搜索 ([c15d821](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c15d82165edb77eaa2dfb746de8f2ca1f6522fb5))
* 部门搜索 ([7ba4f0a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7ba4f0a3f451cb2064c21c79de35e270c5721153))
* 采购管理的待办渲染 ([ef1ff10](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ef1ff10d6806d4d6dde03636edc3a76846c132cc))
* 采购金额 ([300ed33](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/300ed3304a52c2368b26462ed5d22944b3d84461))
* 采购申请流程下一步发起对接 ([b0cfcd5](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b0cfcd51515d0540ef6fb8aa765fe095ab9ab3b3))
* 采购台账 ([1956a2f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/1956a2f2507826b417ad21dc326dc5be00fd6312))
* 菜单标签问题 ([b4722a5](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b4722a58b04dd834c8672cb402df7899eb81df5e))
* 菜单标签页 ([c9481a4](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c9481a40dda4892c7d6c273e6c438a3bf1e87ecb))
* 菜单间距 ([d3141ca](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d3141ca710547af971309a546bfde632dbd9f191))
* 菜单交互 ([19e0d54](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/19e0d54a1575ef9d85ff4c4267ac1a133bd77e8b))
* 菜单懒图标更多交互 ([eb17bfa](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/eb17bfa4cd4d73e2ac0b05df9ea34e5eba244303))
* 菜单名字映射关系 ([339c2ba](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/339c2ba1823e6470191940d23757981cfdcb0fff))
* 菜单双向选中绑定 ([b9a5b43](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b9a5b43ff9a988c5589493effab14a7148863300))
* 菜单项 ([6ccd6c4](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/6ccd6c4dd2dafd141b12d563033cf3e6b134ce44))
* 参会人员必选和可选不能重复 ([88a451d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/88a451d8a5993e2b267782c639ba76a84cdf64e6))
* 参数控制是否显示框架 ([3b7cc12](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/3b7cc125bf0497a09affe0b70b181ea95f1d7b8c))
* 侧边栏菜单处理逻辑 ([d9c9e5e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d9c9e5e3b356dc4318d7633578b67d555794e6c8))
* 侧边栏图标 ([b0a3f0f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b0a3f0f0531f272946e11bac3d0bbc12a019e311))
* 侧边栏图标跳转bug ([4fcde24](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/4fcde247eefd4920d9ad4735361e3fce62d94472))
* 测试 ([284f2f6](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/284f2f61d4c224b2821299348495b4db08b07450))
* 测试 ([3bcce10](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/3bcce10ea1dedf3bc418b2f25ef5e502293a8079))
* 测试地址 ([0a68a53](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0a68a536b19309b03aa1b771eedaac9fc1c9d1f1))
* 测试环境 ([55e6e3f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/55e6e3f7aae47abd23e6083520b9f0301786e3f3))
* 测试环境待办loading Bug解决 ([7414c04](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7414c041799b81a2d0a3952cc28b09d3fa650f47))
* 测试校验 ([f98f4ab](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f98f4ab0d20af6ead8160f0ba25a357930c481ab))
* 测试校验 ([8f30eba](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/8f30ebaeca255f7c248bea2b7079afc7e24ece01))
* 测试git推送 ([db4098d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/db4098dd910157052ca6bd9f43c8e517ff05ddac))
* 插件模式 ([dbaf3af](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/dbaf3af052301b798acc12c5c698249db021500b))
* 插件重置 ([358d1b5](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/358d1b565588d0a0d48cee3f8288357a1f8cd2e1))
* 查看数据为空占位 ([e8cd5cb](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e8cd5cbb01f919b3158c06f509d24d8827b20034))
* 查看转发聊天记录ui修改 ([ebcaef6](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ebcaef6ba419ba7ec71158c24ce55f9a5fa8977d))
* 查询重置bug ([2ed114f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/2ed114f2faab08173455aa55bafc300799aad6e4))
* 拆分组件 ([47e8d54](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/47e8d540d68299a27675c4eb95989d36e2513dde))
* 拆分组件 ([78ed595](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/78ed5954e3ffc2a77c9a77d340fa6b657c9cfdb8))
* 禅道问题解决 ([c3dcb54](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c3dcb544efe1f56d8f2ba0073d4cb10f06dfc0cb))
* 禅道问题解决 ([1bc0437](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/1bc04377e2619f1ff4038e15910f6ccfd74145ec))
* 禅道问题解决 ([877b8b7](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/877b8b739185d9a8109323ecda3f987c4630167e))
* 禅道问题修改 ([7847adf](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7847adf9c6e6f910fb522f24222ff06186561ef5))
* 禅道bug解决 ([93b82d1](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/93b82d19f58d84c69f61d1e42f3f4cb24bc55ccb))
* 禅道bug优化 ([e6f1723](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e6f17232f6891e671636403d1d8dd74c102f9474))
* 常规模式的样式调整 ([8abd5be](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/8abd5bea5cfb301457197b72ceb4e51894c12f8c))
* 超时也取消 ([2bdd6ff](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/2bdd6ff9846f4b3bcd2715f88b81223a0ceac8d5))
* 超时重试连接 ([ab476f7](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ab476f7785060024d5f38dc4a519b7b33c05f2a1))
* 车辆api接口调整 ([91f0ac7](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/91f0ac70b2f3c02350da3d3138e76205c44a109e))
* 撤回、提交 ([58c692c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/58c692c13a86d2623d8219bc3ab54bb8159b58f4))
* 撤回提示 ([109cec3](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/109cec3b7d042470437400755ddc7ece8db16cf2))
* 成交价格 ([bc6b5be](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/bc6b5be80a343d55abd646f9d393d11d342f9798))
* 冲突解决 ([2227c74](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/2227c7455053245f3ad156c5f1fd5daf13b450fb))
* 抽离组件 ([8ef369d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/8ef369df17a05334fa463d2c8fe23be15356eff0))
* 抽离coos的业务板块 ([da3df80](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/da3df8032df62f03c9b7ce1b1a6f87a51e09142b))
* 初步调试im ([21763fc](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/21763fcb95998a2816bbc0c8375a919e20c65bab))
* 初始值 ([cac6411](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/cac6411b0a11563d0addeb0a75fe7fbe73bfbb99))
* 初试项目SDK ([5ddfc9f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5ddfc9f060abecfcaeccefeed0a6869be97b66e0))
* 处理办理的bug ([713f30c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/713f30c20dbf336656299ad9b21081b73b3bd8c2))
* 处理表单设计器的问题 ([712d17e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/712d17e5a6005150470edfdffbbf6d242c01376f))
* 处理表单设计器修改dataSourceType,后数据不加载问题 ([083c0ac](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/083c0ac00b47fde358e13d370594cfe94be3eaea))
* 处理表单设计器修改dataSourceType,后数据不加载问题 ([c33758d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c33758d8ae3f58bb70cd85070a9b860e6f60e47f))
* 处理表单搜索组件和表单渲染冲突的问题 ([789595d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/789595d91ec184ce74e3826ef329f2e0ec0be455))
* 处理表单渲染问题 ([a1fc031](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a1fc031e90111af233caff5f2b12c07017384b23))
* 处理表单渲select值回显的问题 ([431c1c4](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/431c1c467f133897fc00af5daac4e40eddc213ba))
* 处理车辆费用登记选择了下一步办理人,无法提交问题 ([d01a34f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d01a34f4005650a23f8a363bb01873fa72bfa3d9))
* 处理冲突 ([edfe20e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/edfe20e10a76134e705103ff520a01f03805bf79))
* 处理冲突 ([366b64e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/366b64e2c9e410da296898f77e894d42444d0da1))
* 处理导出报错的问题 ([1381ffd](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/1381ffd12069ba404475463452aff4e7312ebac0))
* 处理地图的标点，连线，切换中心点操作。优化样式 ([ac1e265](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ac1e2659ac9884f040a02678562adbc26611e8f2))
* 处理地图切换到瓦片范围外切换为高德卫星正常地图 ([655ad37](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/655ad375836b0f05d3969882601f25e78158a364))
* 处理动态表单一系列问题 ([a2eebe9](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a2eebe90d8cf286159bbb279ecb5af3d196229a3))
* 处理发起时多实例问题 ([bab5062](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/bab5062cc7ed0535c9a107b382f79e0553023a9b))
* 处理工单数据未找到的bug ([b13d513](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b13d513bc985a0bd877c828fd3455963f8dfa97a))
* 处理兼容川投的跳转bug ([69cd53c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/69cd53cc487bb4eaab70f00797769077972756de))
* 处理接口问题 ([d8275f4](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d8275f4ac301c78c9274eb2e8f7c4aa09373cb19))
* 处理节点没有高亮的问题 ([0b63b9a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0b63b9afa09b3cfcd9035d492038de46c86a49f8))
* 处理框架动态计算宽度 ([63df64c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/63df64cd3a8e7084e2a66b2339496a2c8851dc82))
* 处理两个小bug ([faf37f4](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/faf37f487258959c6ad4c4b7644dc95429274887))
* 处理两个小bug ([49863ba](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/49863ba6133a4377f69595674f49a3395c7f6c81))
* 处理流程提交按钮丢失问题 ([99881e4](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/99881e4097b69542cf1be58c95844d241015dfb8))
* 处理流程下一步办理人选择 ([b156953](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b1569539b4a6be19bd4f3ec0f136c508004650d3))
* 处理流程下一步办理人选择 ([9176c92](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/9176c924db932a01337a24882c9967aa6114277f))
* 处理流程下一步办理人选择 ([cb79004](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/cb7900492eea37fb8158d9a8d11f05648863b5ce))
* 处理流程下一步办理人选择 ([71d047e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/71d047ea7d3016fc56142c9392dd13ede9292f80))
* 处理流程展示问题 ([2eff6e2](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/2eff6e280eb1da96b21a896c06406b0f3f81793a))
* 处理流程AI展示问题 ([64a9210](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/64a92101348408b682769020c266f1660c87bd80))
* 处理模式omip框架问题 ([6fe25ed](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/6fe25ed1ba5de567b9a07102be5f138df3d20d6e))
* 处理权限搜索问题 ([70ee690](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/70ee6904cf4430d8a3be1508ea315c56fc325bbe))
* 处理人员持证，培训记录，信息档案页面引用报错问题 ([70ecf04](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/70ecf04d2e87881e9b0b918d2d46d7ae35348eed))
* 处理删除问题 ([aaf50b7](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/aaf50b79099a2af8dcf3ed1372a4079b9c826907))
* 处理时间范围无法显示的问题 ([87db0b9](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/87db0b93a906c6ab53fd805aa6b642ea514d7d43))
* 处理提交 ([85e698d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/85e698d7b2fa1aaf78a41814989c153713750932))
* 处理提交时候变量没有复制的问题 ([b504fa4](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b504fa4e28255b267509060fe48d70bf93597772))
* 处理脱硫电价没有显示问题 ([5904bba](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5904bba5e4fee9df2e7558ae5ac7e34a65c3d342))
* 处理脱硫电价没有显示问题 ([c4be56c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c4be56cdcd779b533e334b3e14f363b7ec6aa565))
* 处理下一步选人没出来的问题 ([18f5d84](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/18f5d84347e24a39cb652cab59e386cdb47cf590))
* 处理下一步选人没出来的问题 ([55bf8c0](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/55bf8c02d2d136b8bb9ec07c06e2e541aea497f7))
* 处理显示问题 ([ea1b209](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ea1b20904010b793fa8588d668f36d5491446644))
* 处理样式 ([73e3c6d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/73e3c6df7b01900245b63d26fb1fd9ba013d164e))
* 处理一些显示问题 ([7b77447](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7b774476369ef0bc90d512a021feca62acf0021b))
* 处理一些显示问题 ([0e653ae](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0e653aeb5bf9dfecd08708e5a3301a8a52a059c9))
* 处理一些显示问题 ([e4326df](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e4326dfcffa41b6bc0fc8f2a0b73472efe229b82))
* 处理一些显示问题 ([8f519ba](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/8f519ba64fb8031676950a2539dc59c9dc7caebf))
* 处理一些样式问题 ([fde07e9](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/fde07e9a968423f04aa7e0219675775f91ad8c0e))
* 处理一些样式问题 ([9df57a1](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/9df57a1c1cfba88d4cd7d7382e99264be59cd77a))
* 处理有多个下一步选择节点渲染问题 ([84a27f1](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/84a27f100162d25ec66287f36a9ff97e68271336))
* 处理中的判断 ([2219547](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/2219547e56d7d5be62adbed87ecbc1823c3ffdb9))
* 处理终止时审核意见丢失问题 ([5464636](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5464636bab40cbd8ba547c6c82932dfebfef6861))
* 处理自定义选人选部门 ([a4422d9](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a4422d924d38a30cb72c6383922a57409699965f))
* 处理omip模式 ([455bf5e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/455bf5eeefc0469be57b8f85bd647ed29fcc3b42))
* 处理socket不自动拼接问题 ([504950b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/504950b73a956a7bf4228b1883eb46781b92f50f))
* 传参类型修改 ([40a8c3d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/40a8c3d84bc04d57807b16bca3f9e5cbf41060dc))
* 创建群聊对接 ([78d022d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/78d022d2254237ed141838d7a2fc01b482e67fe4))
* 从收藏进入知识库面包屑显示问题 ([edbc608](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/edbc608723c5b36ac80b975e27eef72ac498799f))
* 存储上一个用户信息的逻辑 ([760c91e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/760c91e25a61a7d9d32fbdd854a3bf4adb260947))
* 错误解析提示 ([7bd4295](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7bd4295075fc97cecd83cf706e46ee56e8c94c13))
* 打包环境判断 ([41f67b5](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/41f67b550423ab18c1c6adceb4349b39a826493d))
* 打包环境配置 ([af35cfa](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/af35cfae7fb031297f983e34cd5388c6abd1bb16))
* 打包配置 ([d78e8f8](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d78e8f8c3fb7e56f6c958b47cf2f1c3dc4309dcd))
* 打包配置动态dll链接 ([fe128e1](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/fe128e19788704b150f57b8c3035d4a4e6f49a60))
* 打包配置修改 ([13bad6c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/13bad6cd229e2aca1fb3059f203add75284d6cd5))
* 打开外部链接 ([0102e47](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0102e4783c9f2267bce6791f8028c3e5d2ac31d9))
* 打开应用的支持 ([4a1fc4f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/4a1fc4f5869e1a1816b64dc55ec0293bb1f19a39))
* 打开应用逻辑 ([e5b67e8](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e5b67e8009b2985fd8b6b40939233057bed5ca33))
* 打开应用提示 ([d80e90b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d80e90b5383fc71ba880da7ae51031b53fef9d44))
* 打开注释 ([4159cf6](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/4159cf606b6861e4cbbb240cbdfdee0693792c30))
* 打印 ([42e7333](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/42e7333dc9a3cabeadcd6c1997f01f4c4787e219))
* 打印优化 ([afdbff7](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/afdbff76128e12050d04c35f57f754f199eddc80))
* 大部分svg的替换 ([5af6584](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5af658448d8d03758a481b317c0eae3fc8d4c63c))
* 大模型的模型 ([5d31d1b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5d31d1bed713f5859eb50d27a3d51af7945c0bcb))
* 大模型对接的demo ([d7e1558](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d7e1558f271f97d4b9250f40bef6cf4a51770539))
* 大模型接口 ([3154f15](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/3154f154a7021b19336f9c2e8a12365f2f3d91fc))
* 大屏飞行报告弹窗引用路径更新 ([27605a2](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/27605a2322d3718b38c03b8a21882b2db77ba5bc))
* 大屏接口对接 ([2623b6c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/2623b6c0cdd7569599956de13cbd5be230f9d850))
* 大屏设备查询接口处理，地图交互优化 ([356ae0a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/356ae0af2bac56643b98e7c32587248386e68ace))
* 大屏首页接口对接 ([5e5db97](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5e5db97139c4163c047a5ede97e355cbadea6483))
* 大屏首页接口联调，从电站管理添加大屏入口。处理大屏打点，画线交互 ([cc059ac](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/cc059accb8657d4f649a980a38db0c4644b18413))
* 大屏首页接口联调，从电站管理添加大屏入口。处理大屏打点，画线交互 ([827f922](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/827f922d3381a382e7286be7a12e0f2696054622))
* 大屏替换光伏板图片 ([76bd813](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/76bd8133b02c98ccad965e410fd977fda151a608))
* 大屏页面标题优化 ([0fb8be9](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0fb8be9480a40e91e1e9a6ff5bd5544b4a2e0d02))
* 大屏页面模块处理优化，样式布局优化 ([c853499](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c853499cb98790771ec370c6b74d6864499ed9c5))
* 大屏优化地图加载 ([df32444](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/df324445e850c6c81bd4d5027799879b7252e300))
* 大屏中间模块开发 ([dc92e22](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/dc92e2213c2b0bdc5cad4c3f463e618a2d83a285))
* 代理 ([8657b95](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/8657b954cb314a931b5f85be152f33984bb932aa))
* 代理 ([ee0e219](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ee0e2197193d265e9c3bd754db7519f451a3ea1b))
* 代理大模型接口 ([086378c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/086378c204db2f43fcda444cc88bd31523b978fa))
* 代理恢复 ([f16b8cd](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f16b8cd3798096d28044a95e07879548b2e28a57))
* 代码层级调整 ([8f370e1](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/8f370e1afde61ae75458c767316453306101a3bd))
* 代码格式化 ([1d53a07](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/1d53a07f714ad25577302700dee6fa8044aebfc1))
* 代码格式化 ([0a2c120](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0a2c1206049c5f0dc940aec9f5a0b0df3fdc677f))
* 代码格式化 ([f41a003](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f41a0031f117a8cc9a81a91c52a2662506f53599))
* 代码格式化 ([7cfee20](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7cfee203a6a14b60fb60eb6f422c7755108d3c54))
* 代码格式化 ([51fec83](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/51fec8309f22498753dcd77350183c8db69ca5fc))
* 代码规范 ([d798414](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d7984141520c9ef71cf6dff467cf7c19df786f5b))
* 代码示例处理 ([374bc94](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/374bc948a37b8a1274e0a6ca4133bd8dd2953513))
* 待办 ([9a99df6](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/9a99df68403994ddd4587538ee39047767649252))
* 待办标题栏及日历选择框颜色匹配 ([b5844ae](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b5844ae5d1b883fbd3ce6a4b3ac7411f85516123))
* 待办表单待办类型样式调整 ([3a8ed3b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/3a8ed3bbae20efa75194fc07996757a35074a3fa))
* 待办表单交互 ([798553b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/798553bbeb8d86b5cb485d25c81e78b90a519af7))
* 待办参数拼接 ([693cf93](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/693cf9374b58e425113d6dcbb3dce987e965e84d))
* 待办测试环境 ([d577914](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d577914579c29b2495599201a25e129a6550d87f))
* 待办处理标记加处理人参数 ([3426d4c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/3426d4c6b2e1b05f1a264b0cfe050d5a1dffc003))
* 待办处理逻辑 ([3da317a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/3da317a06edbc85274eaaa5a47b5c27b35a7c9db))
* 待办处理时间 ([4a9b7db](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/4a9b7dbd2526a09de8fed712322921018b887e9c))
* 待办处理之后调用处理中的接口再关闭弹窗的逻辑 ([eaca009](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/eaca00999e3e5d8c144011f34841255b509cae4c))
* 待办打开方式的配置化 ([a1ae93c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a1ae93c1ea5bcc9be9a59ec68b7f8bc0d612e7d3))
* 待办代码走查优化V1 ([d366c1d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d366c1da408510890410e0f6b60557d8906c6d8d))
* 待办弹窗关闭时清除路由参数 ([d0b575d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d0b575d3c7009f672d3042156271e0e1c7640963))
* 待办的搜索 ([aeacfb1](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/aeacfb12b7098546925cb5880faf93223e6003cb))
* 待办调整 ([fecbf40](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/fecbf408897ad725252e221a6a7b6faa63a215be))
* 待办对话 ([e29251f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e29251f85835f7d0504aab2dffc8f27d535d0db9))
* 待办对话多加字段 ([129c082](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/129c0821392114cd12baf63b7456ffd0d2f91fdf))
* 待办对话历史记录 ([70449d9](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/70449d98d524a40e375de0ce948871f5c85f31b1))
* 待办后定位到滚动位置 ([fdf1efb](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/fdf1efb08a9c39161653938053f0ffed84366a62))
* 待办界面优化 ([a94be42](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a94be42017317307b6d4d188c2f09bedda3d3112))
* 待办界面优化 ([0151da9](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0151da9429e9b4473c4d59d84c2aa335229bec51))
* 待办界面优化 ([0954f9b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0954f9b6aa3fc508d0acf420bcab9f392225da75))
* 待办来源获取 ([c028de0](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c028de01bad0e18bd43d71f00a1a0557a2132ede))
* 待办列表错乱 ([6845371](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/6845371413ae00229a9d9fd84e580cd30a65d94b))
* 待办列表的处理 ([aee051b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/aee051b0702d5763c2a722d84068af1636a41149))
* 待办列表来源字段空值处理 ([a248bb7](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a248bb7dca47abb237d686f0cfeaac33ef649c8a))
* 待办列表优化 ([7236bc3](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7236bc352b4cb81c0eafc9e12d7a77410b9489fe))
* 待办列表优化以及滚动优化 ([17923ad](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/17923ad0f5f1dc763c5eb17fc1e18e058a79f246))
* 待办列宽调整 ([3213098](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/32130982ef50a315d54da8fb77f44c582ef2e3af))
* 待办名称解决 ([30e34c1](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/30e34c1de76fb104dea72faf322179d9748c257e))
* 待办切换处理 ([e5b6824](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e5b682414338f66e5192ca4d26210ce63bd5007a))
* 待办实时通知更新 ([cf80ee8](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/cf80ee8cebff736270f9f3f81c58ba338f6b3fe6))
* 待办数据处理 ([d793bad](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d793bad6b69ab039534c83e1301e40d90b17c46e))
* 待办数量展示 ([3644c71](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/3644c719b9eda9d45b0a4b3f074598a814883397))
* 待办搜索调整 ([28b7509](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/28b75091fa8a2ba4c17c05d70e2fb6662d97626c))
* 待办提取弹窗 ([1b57ccc](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/1b57ccc007258b328c11f52b8256c2d9bbef8e43))
* 待办添加搜索和列表字段 ([4940928](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/49409285f64af436dbf7a501cf1de207f2f2c81d))
* 待办跳转逻辑优化 ([6c0aeb8](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/6c0aeb8cc9d23f37f5668f91c2eea675754399ae))
* 待办通知标识 ([e881b58](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e881b58fd807b57172b5ba6d42b8b21488be6e34))
* 待办头像 ([b1a447c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b1a447ca88623ce6d2ff648fe74096ff12f3e98e))
* 待办图标 ([d24bca5](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d24bca53e207bdb4d76d5df1e1eba32fa47dcbdf))
* 待办详情重构UI ([14279c4](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/14279c4abe6e00866a8ac655fad2515e57ee72c9))
* 待办样式问题 ([be3ec40](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/be3ec40658edfd08b0eda100b34cfed1079b40cd))
* 待办优化 ([67f6499](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/67f6499b6ef65de419755a76f08587a4df6eb25d))
* 待办增加字段 ([a1bfc9b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a1bfc9b7c73699d45b0f15a52a84b88f592cc271))
* 待办智能助手 ([781963c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/781963c9010f7ce1110b7d85d4db33f177f0d4ff))
* 待办重构 ([ec2f592](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ec2f592236ee06b90a3e54dbaca1e92522ff36c0))
* 待办状态扩展(增加等待处理中) ([0b132b4](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0b132b4374eb4aa304cf0da11e4cb89aa9390145))
* 待办状态扩展(增加等待处理中) ([e6a4b94](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e6a4b94b27b1d76f66f722791fb9ccf8bc5de65f))
* 待办组件引用 ([6fecb84](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/6fecb845a9363156b2cbee78ed8bc716dba37c09))
* 待办bug处理 ([f2562f6](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f2562f6e34af48fd0c8df7a074089c18e80d10f7))
* 待办loading  Bug ([4d48f28](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/4d48f28d0db5ad567c67916ba7782a7758bc081f))
* 待办loading Bug解决 ([edb508f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/edb508fe42c518d98fdc1a3d7b8978579d26b966))
* 待办ui数据修改 ([ee94017](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ee940173ffa01bc0c886210108010bad6dc12434))
* 单项消息删除操作 ([f7a8d26](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f7a8d2622e8f7a9d3343fa2f13d9f8c10a9128bb))
* 弹窗 ([940fb8b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/940fb8b876b3234aedf24b60095323489c5adaad))
* 弹窗标题优化 ([468ab17](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/468ab170d5ac37a090f9ba33bc77a26b10a9cab5))
* 弹窗更多插槽以及优化弹窗 ([156de95](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/156de95a04f8948ca6e2e86ce5ce6b4bf810fd0f))
* 弹窗开发 ([e69ffad](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e69ffaddf02516e744c96f415ceabe209ceea991))
* 弹窗宽度 ([64c11b6](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/64c11b62b9e1ac52bb4ba467112b2709a9c3fae5))
* 弹窗全局样式 ([ad5608d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ad5608d40057dfdd88409ffcc7dcd3b4febc0503))
* 弹窗使用 ([cd6b4c9](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/cd6b4c94db960002353e9d38bf8d5fb0db67d88c))
* 弹窗统一 ([06a1d46](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/06a1d4620e0775143251200b48b7ff4d35f2c90f))
* 弹窗详情分辨率兼容问题 ([3d8b1bf](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/3d8b1bf8bff9f31913a004aa3316209cf7e64223))
* 弹窗样式调整 ([2d5933c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/2d5933c127571aa36d59857d72e4e2b5460d2328))
* 弹窗样式问题 ([3adfab5](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/3adfab504082fbc2a46ab50b5424bb998c639ad5))
* 弹窗样式修改 ([394f32a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/394f32a9f51a8adaa9866c85035dcaa42128f629))
* 弹性布局调整 ([de8915b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/de8915bcda0dfa4dcf5fb73a14b44d33c88cc06c))
* 当只有一条数据的时候默认选中第一条 ([122e23f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/122e23f41b94d441ea0a6dc2ef3ed93c4b82c9a6))
* 当v-loading绑定了事件的时候做防抖节流 ([d9a842d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d9a842db0c86aa9b6370df61a0054560e2486038))
* 导出 ([dcda1eb](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/dcda1eb71263561a2fbf1a9437beb70ec19c77ce))
* 导出 ([4a7f21f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/4a7f21f12ef7f6ce6953ec0b0e24d1bc72ebaa7b))
* 导出摸板 ([049c6ff](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/049c6ffeec32a3b7e583f443fc10f7e391109371))
* 导出下载接口统一处理 ([80817e4](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/80817e48730269f9725a9da872f570e5f8bbbaaa))
* 导航 ([4e5384e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/4e5384e3ef955f6131e673644f0b34d7dfc07dcd))
* 德阳云-预览地址修改 ([688722b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/688722bbb9ccf8df31c0ebeb207cb055411c83f1))
* 地图操作，卫星地图和切片的交互优化 ([291b576](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/291b576e0004088bebf09f0968adb14407bdb9bd))
* 登陆之后刷新页面加载CDN的逻辑处理 ([c607b19](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c607b19040221a8ff9a8e01aada0fe40bdf25826))
* 登录 ([934f0bb](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/934f0bbb13dbbec34e348bf3fc307e74a58f6ed1))
* 登录才能埋点 ([b2d600b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b2d600bed2b2d339ce8d6af52fa04d949e3efb8b))
* 登录后根据配置跳转对应的首页地址 ([fd94c97](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/fd94c977450d7b8996826f3e9fedeb4fbaea4f95))
* 登录后跳转对应页面 ([6788240](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/6788240b6c2d9e8c10d2bff9f6469dfa91aee18a))
* 登录结果处理提取方法 ([f13fda1](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f13fda1f25c1b8e1b75a1b8b9ff7a6e0bad0ec7b))
* 登录框回车 ([accee20](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/accee2094043a69aaee1cd30ab20603b98fbb1b2))
* 登录逻辑改造优化 ([e78fe81](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e78fe81c4ff2b76d16338792ce5a9f711d9ee79d))
* 登录配置为空时的逻辑处理 ([169dca6](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/169dca6b85147a8ccbcdb7b66da23a8b45543e08))
* 登录权限校验 ([7f0c058](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7f0c05851363504392e1b9c8355b4c517bc85d03))
* 登录失效的时候清除登录信息 ([7bc236f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7bc236f412cbf953d1d4cf39abd5ed88a1973d0e))
* 登录限制 ([30376f3](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/30376f37fe8b6e91a1d58301c1a60f44cf98a89a))
* 登录验证码规则 ([62c8240](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/62c82409a264ddfab4d665a70dcf6432f1da0d41))
* 登录重定向 ([ba82871](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ba8287193408164be28791c1f3d1806c32df1bd3))
* 等待对接联调监视总览 ([a493945](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a4939451cc7f2d132241442fc6eb2eaa32cd9a64))
* 第一种模式omip兼容 ([7c7ad53](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7c7ad5307940188c07cfe72973491b1bad9a7a13))
* 点击插件的处理逻辑 ([8782bdc](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/8782bdc7593e9bb7fa6bfe857bad1365970f20b5))
* 点击区域切换区域 ([56de248](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/56de248e7d2a14c84fa0faac33e2fe8b861409e7))
* 电站接口联调 ([29fae5e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/29fae5ed9c1b4c8655b036c0ff7b733e0b241a71))
* 电站设备联调自测 ([6d31ecf](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/6d31ecf4e621809adae64fbcd3bc9abd2b762022))
* 电站设置 ([a4ad21d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a4ad21d92fe7d87b01f9cc9086dc8154ca99e81f))
* 电站设置 ([1a595cd](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/1a595cd36abba045e629e0f1d54f648dc40ea6c2))
* 电站设置接口联调 ([70885bc](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/70885bcdc880f073c2c75ff6e8011d8f6e914425))
* 电站总览 ([b1187ba](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b1187ba47cdd423aa05809680119c3ab2d03a92f))
* 电站总览的路由 ([636eeef](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/636eeef07bd5ae65e3adaf4d5dd438a17bd52153))
* 电站总览添加导出按钮（暂还未对接接口） ([5ae5632](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5ae56327bc385c28e245271235fdb7b25c15d843))
* 调试 ([561bfc5](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/561bfc5213894faaffd16312f06290c066b0a213))
* 调试中复制粘贴 ([6a351c5](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/6a351c5920c2a5626ea5421b905df65b0e5ee55d))
* 调试注解 ([625599f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/625599f9acbed9988c0d7f5cbe2bda44d4362292))
* 调整按钮盒子对齐方式 ([0a012a1](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0a012a132c934330ac38593369b17ead3ba9263f))
* 调整备忘录 ([bd4e59e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/bd4e59ef78d824dcb593914875c6e62500c389f4))
* 调整边距 ([fce3837](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/fce3837f0f3df07ba6a9ac38340c66810220f641))
* 调整标签页 ([65283ce](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/65283ce9de81dec22d17e44b280c3a43d47abb1a))
* 调整表格样式 ([dd9a0d2](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/dd9a0d21c85f800c39179b25a3fb9be239121a08))
* 调整布局 ([ec22d3c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ec22d3c0f5d4e789440a9be8b2e12def17abe7b4))
* 调整布局和逻辑 ([09f6267](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/09f62674f75a708c92fc8762ff42fc396608d29a))
* 调整布局样式 ([212a63a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/212a63a363f38ed1b9e75eb5fd84894f2432dea5))
* 调整侧边栏样式 ([0aa501b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0aa501b7e193b3590925e31a7a8e2a830fd1feff))
* 调整插件布局 ([d2df147](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d2df147e0a5a298285422c3f5b92f28a19301b6d))
* 调整拆分组件的逻辑 ([b87ab71](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b87ab7105a4c835e99b942d61687cd6c0c3bb1ae))
* 调整创建者的宽度 ([c952740](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c952740b87d846a021981dca649d125c78a8922f))
* 调整大模型接口参数 ([0c5d36f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0c5d36f66e865d794211bb9a871d0efe876389de))
* 调整弹唱样式层级 ([3bb2180](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/3bb2180be05a748a601d1dd24addf2cca22d3762))
* 调整登录样式和悬浮样式 ([dcc0b72](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/dcc0b72308be50afbe230747375f42e542a94f09))
* 调整电站设置的样式 ([f0477c1](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f0477c18347f20acead051594c5b6e650e6d36f8))
* 调整分区总览样式 ([70a406d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/70a406d3e4d237578f977bf063b3514654a54b34))
* 调整高度 ([8003209](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/80032090a24269d7d9cba92b09b59e3719ebe8e9))
* 调整各种申请 ([2d6ec23](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/2d6ec23de72f3de898b9c563d9161bdcab294632))
* 调整更多应用显示的个数 ([e22d1a2](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e22d1a25320ba632a8c17aa45a087eb4cc66e56b))
* 调整机器人色系 ([ef78496](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ef784965d8c778d6eb9155d686300102620b4250))
* 调整兼容样式 ([decca9d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/decca9d562ef50bd6375482f3e3449a4eda25b45))
* 调整接口数据格式 ([a039024](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a039024e34bfaf266e62b76508a390661384625f))
* 调整框架样式 ([e319640](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e31964091feb1364f91213574dae369a0165b2b1))
* 调整聊天im跳转 ([b20ce24](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b20ce24fd3570ac43159505ab7811f049c9a4872))
* 调整列表样式 ([5d79577](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5d795771a949fe07a17cc9d721ca50959edb36d6))
* 调整目录结构 ([86e6518](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/86e6518583c9fc757c4f6a27d4235f0bc4157b11))
* 调整目录结构 ([7e4639e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7e4639ea64f2754550dd935ec26c741185664caf))
* 调整配置 ([f34e7a5](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f34e7a5266f14de0ef82892d267d1837dd7d23c0))
* 调整日程逻辑 ([4943fc3](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/4943fc3338df6131a01e5cb2a10aa75461451936))
* 调整收起导航 ([da9482f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/da9482f93196ed7d37b830bd1f3f5b8d65094307))
* 调整数据结构 ([36a64fa](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/36a64fadd8e5e831f49f2065aa1193b3529d229f))
* 调整顺序 ([14c5344](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/14c5344366b7e74cdec8ccfdd3cd53981ba6d73a))
* 调整搜索表单按钮对齐方式 ([e29ee76](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e29ee76bf7c9f23ba19c156f708ba382d1b01945))
* 调整搜索表单的内边距样式 ([32e54c6](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/32e54c6a2af5365b3dfc806936c5742c3cf6f54b))
* 调整搜索表单样式，优化内边距和字段间距 ([5bea901](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5bea901fecc8b8b6258e9d7fb7e0f1ad71b28a86))
* 调整搜索样式 ([8fafeba](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/8fafeba6c07c6a3751642ff544cff3298774936a))
* 调整图标 ([1d492fa](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/1d492fa7c0b3d012dec36894254c812b572700f8))
* 调整图片资源引用 ([5e7a0f2](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5e7a0f2150e690249c77b344e1d6c68acfa76cdc))
* 调整详情赋值逻辑 ([e661e23](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e661e230c69be493990078b8e7382f0301313a4a))
* 调整样式 ([1a1341f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/1a1341f659ba307d2138d03039425f06bf46580d))
* 调整样式 ([551138c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/551138c7be2e65d52678972f0a856d14a1057936))
* 调整样式 ([780cf3e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/780cf3e61eb572023fc5b89ef37adeca213bc70f))
* 调整样式 ([9744429](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/9744429d2163810063ec9273371540e6b615dae9))
* 调整样式 ([7c82bac](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7c82bac2e7a211b7de959b9ff822e8507f7188ea))
* 调整样式 ([b8ee1ac](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b8ee1ac7a805b75f99c8e99198ac4f436ddcf6ae))
* 调整样式，退出群聊后的弹窗关闭 ([7db12bd](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7db12bd913b764c15d2a737d56b2ae68a9db054e))
* 调整样式滚动条 ([3affd2a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/3affd2a73107f568f2022fdd4606676c647f4034))
* 调整样式间距 ([555d891](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/555d891a2a16d33f1db606e71b4ecedb1c00c521))
* 调整样式问题 ([ae57f53](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ae57f53a8f7b748896e6bec90272b154cc0ebce5))
* 调整展开收起状态 ([9027da5](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/9027da52bd333afdb4aae2ce5d4a36fa48a8eb62))
* 调整主题选择逻辑 ([e4cd140](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e4cd1406dfc5868d7656e2f27bf8a731caaf7c8f))
* 调整资源目录 ([595f247](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/595f247ee6dab63749fd4bb515d5ab9f67a4ebda))
* 调整字段 ([5377cbe](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5377cbecd344e3632cd22aa96cbb8a483742f5ea))
* 调整字体大小 ([1ec3a75](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/1ec3a754a618d2439133c46a0bf39d362b89f834))
* 调整ppt样式 ([8706053](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/8706053959887ff29156305ad543da3fa19ce49d))
* 定期检查样式修改 ([15e6657](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/15e6657115eb83a1532a5176d085baf0ec94c12a))
* 定制工作台的滚动修复 ([8f2aa58](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/8f2aa586809520eec382a9839d17fd42940c66b2))
* 动态绑定值变换的函数 ([230ecf4](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/230ecf45a8b62c846deeff3e6e0deec78b009269))
* 动态表单 ([8b8029e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/8b8029ea525ddae7ebf59a1c7621058470eabcc4))
* 动态表单处理回显bug ([ea4e93a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ea4e93afe494fe1b6307455531b0e4988822fd4e))
* 动态表单的操作 ([c561965](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c5619657212f68a5358852fd1154106eecb5b314))
* 动态表单逻辑调整 ([7368318](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/73683186653021a15ad6660a47556c8c85e09074))
* 动态表单数据请求 ([39feb66](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/39feb66ea4ed83019e21bdc000083bd315881a43))
* 动态表单跳转 ([e0246ac](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e0246acaa702678d1c323eada35c9dea15dc167b))
* 动态表单渲染 ([e5ba74d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e5ba74d622826bd571db275a82cbd6df46f53e71))
* 动态表单渲染器 ([a7d25d5](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a7d25d58331e6c0865ec2b572a72c7521fdf8eaa))
* 动态表单渲染组件 ([443939c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/443939cf8935694a6d726fbb86db5a73c4d1db02))
* 动态表单组件展示 ([c0b7229](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c0b722953d58832fd506ad68b2fcf05e56287ea3))
* 动态链接适配 ([fc530d6](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/fc530d61a3e71de692f2c3d4f0e0092aa8c7e3ee))
* 动态列表 ([0591da8](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0591da8a1665aa2a0366dde3fb12553642e08d43))
* 动态列表集成coos助手 ([0346857](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/03468570aea176b317a979e19513a5437cce32f4))
* 动态判断登录方式 ([319ee70](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/319ee700c01e17a6ae20a78ce57da4005246554e))
* 动态渲染表单兼容交接保管 ([5ff9308](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5ff93083075ac7d636ef31de7b9ce4ed6d260e79))
* 动态状态 ([259d992](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/259d992b990d70a9389c1f4770684ea715b95a61))
* 独特显示 ([cdcb5e2](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/cdcb5e204292ced435e038a655059e63b16818a4))
* 短信登录样式 ([fa1deb5](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/fa1deb5941ed61ac652c5409ef849b482b50304f))
* 对接我的群聊 ([76a8448](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/76a8448eb98d759ea2fe205664f5e970f4f9fb32))
* 对接消息免打扰同步记录到coos系统 ([f062b4c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f062b4c51c1e1360b95f02a7cec2e1559eea2a25))
* 对接智控模式表单通信 ([8b0544d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/8b0544d00bf51620694d618d3dd1d1b17c38f6ba))
* 对应字段 ([b6be514](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b6be514b480e0711c3f0715f4e4d1154f72b01a0))
* 多选框的样式 ([c9a94d5](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c9a94d5fc512cfa312366ae40372252e5f250ab2))
* 发包环境 ([13cda5d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/13cda5dcb2d32120c120662ad7a703b37bbff770))
* 发送按钮悬浮问题 ([a179ca3](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a179ca3c5e2813d3d5e97c445aafd5307d0fcb1e))
* 发送验证码的防抖节流 ([a8426dd](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a8426dd748404de48eb194be402811ad8bd7488a))
* 法人申请表单动态渲染增加类型 ([4dcd302](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/4dcd302903bd3520578021a1fae4497cc28db9dd))
* 法人身份证申请 ([8d1c1ab](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/8d1c1ab9733c2892a3a8bd2af71e3b561226fbf1))
* 法人所属公司数据选择处理 ([8b50d41](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/8b50d41fd014f6780297aa412cf3374d7d712421))
* 反选选中样式 ([25eb79b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/25eb79b2bca644eb8b1f89c6c7e7d38fa2a7f4db))
* 防抖节流 ([8b5cacf](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/8b5cacf264cf4a06b0c6b62b744b733c3259a2d1))
* 防抖节流的时机 ([4ee94fe](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/4ee94fed0bcac2714cbab04d9611010e4cf23452))
* 防抖节流交互效果 ([8875f33](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/8875f339f987e514ef6ec1f145185c7a9ba50fe9))
* 防止报错写法 ([1e41b10](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/1e41b103dd717774e9160457e02330cce1142678))
* 防止出问题的代码 ([73db310](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/73db310af4756882805bbf619fe4d3f9728b583d))
* 防止登录页反复跳转 ([dceba8a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/dceba8ab4ce86a9731a52b897c51ad2bf05b4abd))
* 防止抖动 ([98b42ae](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/98b42ae6d9c597994eb7397aca6f75c5e401e7ab))
* 防止接口问题导致没有返回体而报错 ([e6d61d5](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e6d61d5150851e29ee20e924ae94b907d3e490ed))
* 防止看到校验信息，关闭弹窗延迟重置 ([6c4301c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/6c4301c58c28abe1f6fae8cfc5d2bef4f6cba6d6))
* 防止页面重新加载 ([86b94dd](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/86b94ddc9335443c7edd45ed0362a2fd51847c43))
* 防止组件死循环 ([c4eb27a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c4eb27adbf603805c4d05cc9087c3b5de434e4c9))
* 飞行报告点击入口 ([9adca6e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/9adca6ea62f2dc0663eb344a294bbf56531a994c))
* 飞行报告接口对接 --no-verify ([ee96414](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ee96414f97253982821145eae1907da3ae11f151))
* 分辨率过小滚动条看不见问题 ([35438c2](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/35438c29aa150d7750d5648ab534dd57264eac05))
* 分辨率小显示问题 ([fc30bb1](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/fc30bb1e05bb74afb557b32fff1d7fff1cdb7cc8))
* 分区预览的逆变器联调 ([b03ff34](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b03ff3489bd995141b91d024c8123bd530206b3b))
* 分区总览接口对接、列表对接 ([6aa335b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/6aa335bd0c65e9ee36e234907d9a5997db3aeba7))
* 分区总览开发页面 ([d074a5c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d074a5c0aa8f921adb1d90c49b39819207ffd549))
* 分页间距调整 ([c493748](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c493748205de23a3a6b61da5a1b4d95711846009))
* 分页数据 ([526f630](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/526f630cef976a04e64e0d0b18fced2a2a3f96e2))
* 封装item ([88e2873](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/88e287342876335d8ea43857f163eba848766478))
* 服务不可用提示修改 ([3ae6cf2](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/3ae6cf21feb6ced3fdf5295309c5dde4529ad9bc))
* 服务器域名统一管理 ([687ac96](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/687ac96ddb590d60ee1bc46f113004b7bfbec301))
* 附件不希望点击空白也会触发上传事件 ([1ef4959](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/1ef49596d4519224c8562dd31bb92e3e085d993f))
* 复制粘贴 ([7608348](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7608348a9db92ac07870b53f1d3b5cda32208ccf))
* 富文本 ([8276cfa](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/8276cfa080f2b12e5aa67a03cbe48e72907a62e7))
* 富文本 ([df9033e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/df9033e447334ec27496e523974e98a9706daf84))
* 富文本处理解析 ([0f717b3](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0f717b30f292b913d606c69a53781bb0608e2be0))
* 富文本调整 ([0c0881b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0c0881b0761c40b2eb23e16f2dd55b8dc654aa26))
* 富文本加载过长优化 ([b74da80](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b74da808090b88049b9111fb1dec2c064671dedf))
* 富文本全局注册 ([f5ae58a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f5ae58a235718ebe16ad5d7588f4556acb1f0ea5))
* 该变量 ([7710268](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7710268b52f2f5fa7b79a2d071e47e079ecb4ef2))
* 改变判断方法 ([ff255e6](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ff255e60017ed2e9c17fa8e9bf4139964e9bc219))
* 改变样式 ([5754670](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/575467005ea095492b832452d3d593bf878931d2))
* 改变状态的按钮bug ([0346fe7](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0346fe7b47427f45ca0118765a398182d4f8d505))
* 改了注释 ([38e4c0b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/38e4c0bfbbde7d585a4c4be1d31bf448001d4338))
* 改名字 ([3576b96](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/3576b96578427859b59eecd39803c5a73fd14ae8))
* 高度计算改为弹性布局 ([7d5368d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7d5368d1d6302cab86c7b5156bc7fba7d4831de9))
* 告警列表以及运维建议弹窗 ([00d1b75](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/00d1b750d056a01f33b63201f5911877847d37dc))
* 告警总览，综合管理相关模块弹窗样式统一处理 ([66d920a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/66d920abaacec6e215a0ebfcbf5f3b89e6924ae1))
* 告警总览级别字典添加 ([ce5791a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ce5791a3d4eda90d30e8307dc203596750068f62))
* 告警总览列表接口对接 ([0ffaa4d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0ffaa4d465e98666d74182e7ea43cb63ffa13e78))
* 告警总览列表接口修改优化 ([cf22326](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/cf22326036c04ea9399a49ac6d4b90a02a98ace7))
* 格式 ([c72b40a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c72b40a7e6af6c4c209a1a2a4f5f1e80f28ef094))
* 格式 ([dd35fe3](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/dd35fe30b06aae62d76c9d8db8e62c513df7c0d1))
* 格式化 ([427c7f1](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/427c7f1887e8281c1ebec79522e93cf89b091959))
* 格式化 ([7700470](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7700470104dd498160239f1c4666fef8d160ee30))
* 格式化 ([8d1eb68](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/8d1eb682466ff896c5e86e893db442573fb3658c))
* 格式化 ([30bd072](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/30bd0722486ce43c9708545cd345fe230aa7e60c))
* 格式化 ([6f36e08](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/6f36e08a88ccaaee1aba7ba0c382999b46af86a0))
* 格式化 ([5098b90](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5098b9012e9782faf4724ab8998fc0afcda4b66c))
* 格式化 ([93315ea](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/93315eac536a6ac8449c5252ff12dfcfa558e9a9))
* 格式化 ([22bcc58](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/22bcc589c836c709667718616104b6562affc64a))
* 格式化 ([afe7789](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/afe77899487b81d6e84156e13edabd08de57c15d))
* 格式化 ([adf5a13](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/adf5a13f5221e0e66f8fdc7176f499892dcf86a6))
* 格式化代码 ([3c604d4](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/3c604d4c6b2b01f2d4cf3cba04637fc49a971c6e))
* 格式化代码 ([a22b3d1](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a22b3d1a53674be79101bb2981564e75085d91a1))
* 格式化代码 ([73083d6](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/73083d6cf1e0831e389d974f814a554d7f56f13f))
* 格式化代码 ([117996c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/117996c01dae20e79d4cefdcbd15eeec5a410c19))
* 格式化代码 ([b6eed11](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b6eed1187105acdde356e3c7dc8f18655d990f86))
* 格式化代码 ([20dfe1c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/20dfe1cb345d7d5822b5c07092c2036fe3702f35))
* 格式化代码 ([6242695](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/62426955c514e301cc153d6f006522bcd6662f2c))
* 格式化代码 ([7899e3c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7899e3c584ab085782beac4d8e656443fd1e0402))
* 格式化代码 ([944157e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/944157eef1b5f21bfa5bc7855dd163644e489277))
* 格式化代码 ([ed72737](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ed72737dbd9894818621fda23fe6f625724b2fb7))
* 格式化代码 ([b2d2ec6](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b2d2ec6b46bb19447cadf041df0c95b41331fbb0))
* 格式化代码 ([21d627b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/21d627bbe163e7b3e13bf3e6b02da35cf752d484))
* 格式化代码 ([d00cfd9](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d00cfd95fb79c14e445a773dc9a54a0ffc9fb0c2))
* 格式化代码 ([7af3193](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7af3193710635ea9693f40d3871bf079c43fbf78))
* 格式化代码 ([e10af8a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e10af8a2984811b10f17207ad0343c7153bb9ef1))
* 格式化代码 ([1970485](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/197048570b9ad3e3ce0c9ea966aac8a587bf17f9))
* 格式化代码 ([98ccc73](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/98ccc73155ef60cd352311fb4a38376486749e97))
* 格式化代码 ([e75b2c3](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e75b2c3c06de28ef60737c553447ef4958de2926))
* 格式化代码 ([56643b4](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/56643b40afc1045caf72062be263798cee2ece1b))
* 格式化代码 ([662dff0](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/662dff02d6a1cf8684dc0cb5cb5bbe393744b825))
* 格式化代码 ([5eada94](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5eada94c3552a40152acb75c45cf9afaa3929976))
* 格式化代码 ([649d234](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/649d2346b38b75803b55870e4c1f34360a90ff96))
* 格式化代码以及不缓存 ([8ed3727](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/8ed37275493d9b736e554ad9a2419f25b91fc509))
* 格式化代码以及调整弹窗样式 ([69fff15](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/69fff153214fedd6ea75124e7fa5546f7784011a))
* 格式化修复 ([bdd214c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/bdd214c5b9e52ad3eb592cdc3c8828392bf2ef28))
* 格式化以及消息通知的判断逻辑 ([f078076](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f078076b2bd54f14fbd453b0713c943e79858297))
* 格式化余智洋代码 ([4e93aa0](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/4e93aa0e8ee02a3af7e0dbde8a29785870f34e7d))
* 格式化tangdaibing代码 ([ef5b72d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ef5b72d3cc400f456d96eba5ef23f467b7028303))
* 格式修改 ([92bdc89](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/92bdc89ccf9241a6a6de139c7bf4d36b687ec92e))
* 格式修改 ([f89b543](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f89b5436e20ad60afbbc009547b9a65e8fa50459))
* 根据参数控制控制台打印 ([268ba40](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/268ba40583adcd2dafc2023490db0b032f97602a))
* 根据链接的参数控制默认模式 ([e339da7](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e339da76a3e10b06914939d957675f241e56e55f))
* 根据配置修改文字 ([fe01a87](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/fe01a87a168bcfa684f77cb028bcaf125f818a49))
* 根据配置隐藏多个主菜单 ([38ed417](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/38ed4178c1ca54cb3e51c96356fe9e5e5a56e18f))
* 根据项目环境设置umd中的token字段 ([0612acc](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0612acca465df5ffdd0e7ed82996e888a4c58661))
* 根据租户主题切换 ([2fbf848](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/2fbf848d014b9f0f319e7ae4cdc97786753c2662))
* 跟新页面表单sdk ([1481245](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/14812456b7c89c7065e952e6f80bb11bbce75acb))
* 跟新umd ([19bbd3b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/19bbd3b5453b4c6a432070e795ebe31c8f693043))
* 更多推荐 ([d693bf1](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d693bf1e93402801ba0b073d3b39f9014b7b5d53))
* 更改判断逻辑 ([f79419f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f79419fb68ed0199519240acda95463eac4ad75d))
* 更改下载文件名 ([fb35295](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/fb352958eebcded60d6becac42f60c3d639215e6))
* 更改引用 ([eac209f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/eac209fd435b8611a5da398aff9c0f48402aaa7d))
* 更改展示逻辑 ([734152f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/734152f94ded9fc0458fb8a6aeb1aef87fa0ca4a))
* 更换静态资源路径 ([d51c4ee](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d51c4eeb1eafd366d63935b202f22e5c6601bab8))
* 更换数据钻取的名称 ([5202786](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5202786334d2216ddbff2dc825ae5abb461c5c40))
* 更换数据钻取的名称 ([b5fd878](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b5fd87869a8cbc21ffa2275254b1ab285c8aa7d0))
* 更换图标 ([0842e25](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0842e2538ef1519c9dd33d061239a4e6b8d1043f))
* 更换图标 ([00d1085](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/00d108557820191ad4853b9a2efe517703024a81))
* 更换图标 ([4fa3d76](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/4fa3d760a1bc3cefb486c3a6a2c2591538fcf7c2))
* 更换图标 ([8c6520e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/8c6520e0eb319c9ecca347fc50195ff3e20aec28))
* 更换系统信息字段 ([1dee3f7](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/1dee3f7a35dc9d2ea6fbc72e47289d0f48755246))
* 更换新皮肤 ([fa394fa](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/fa394fa75eca34688b280d34953c162e7011e878))
* 更换api ([291f919](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/291f919a3419681749b1ae78fb0f56c42948bb35))
* 更新代码 ([c8f3f25](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c8f3f25149662136b6968bc02769260933e4255d))
* 更新待办选择问题 ([59995e7](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/59995e7f0ac80859b878d6d9c530037e0b5a4a79))
* 更新分页组件样式 ([20de18f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/20de18fa7114c67affb1881c53601c8759ad9ecd))
* 更新流程ai展示问题、新增coosAi页面 ([263052a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/263052a6e1e84549e954c9a67056e4724d922a1d))
* 更新图标 ([6de2773](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/6de2773dada2ef1ae3945ef9f3a0bc557d9d1576))
* 更新文档 ([e2c94ca](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e2c94ca49ce960490c1321fc11adbc27ae4a3f34))
* 更新文档以反映最新功能 ([aa4d274](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/aa4d2746f594a6c83aeae09f5daef9f466b07231))
* 更新文档组件 ([42b9b6a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/42b9b6a4e794986fff6dcd5478750a9f23d8e6d8))
* 更新选人组件 ([6cca0dc](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/6cca0dc9d26f568efdd42f8053006850f8d33883))
* 更新业务表单 ([ddb5afe](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ddb5afe3089c13bee5fa45384fd20a82b7876c9b))
* 更新业务表单 ([3eeb576](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/3eeb576e1ce381b55f90b6be76cfa1dff2705a4e))
* 更新业务表单参数 ([a2bae2e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a2bae2e360afe8e5be488bf3b4bb00014048494f))
* 更新业务表单上传文件格式问题 ([196ff94](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/196ff9442d90af355ac4d5352cfd3e3418345557))
* 更新业务表单umd ([9445ec7](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/9445ec7ad8969472666f4d3d5456b22eabfb6abe))
* 更新业务表单umd ([17ca4cc](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/17ca4cc679b852e8d30380e633a3c5df45a6180f))
* 更新业务表单umd ([8e6763a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/8e6763a34305c09d5dcf510f2989aff8233d96de))
* 更新业务表单umd ([af02123](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/af0212368c7cef7b23dece37c09c004baed99c6a))
* 更新业务表单umd ([6b75730](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/6b75730f6b067e14d31551227a1cf744da902271))
* 更新业务表单umd ([72a1f0e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/72a1f0e3db8c78c7b6a72a1f1a9b912dffd021e0))
* 更新业务表单umd ([c1d6590](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c1d6590616b8b1fdc487d24a3d6f8a8addda2152))
* 更新页面动效图片 ([bfac746](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/bfac746958014a3da2ace02ffc78aa60b513cdf6))
* 更新依赖 ([b9c3308](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b9c330812a3f2ef4b6aa9fe414bfdb6bdf1b5e93))
* 更新依赖 ([7a45e45](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7a45e45fd43bfb0da1912e10967fe8adc9321f2e))
* 更新组件库 ([33ec411](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/33ec411845d30960357d7d6e49ff23a0e746a979))
* 更新组件库 ([a678dba](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a678dba4463407b3aef7850043142842cc420af2))
* 更新组件库,修复表格组件、表单组件bug ([8c58fe3](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/8c58fe3bc260e711af6bbbcd45716afd52181f66))
* 更新组件库,修复表格组件、弹窗组件bug ([904da6d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/904da6d28688e86626259711bab53750ff48453b))
* 更新组件库,修复表格组件操作列显示隐藏控制bug ([ee4038d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ee4038d66bf095df685f309de632109908dccb65))
* 更新组件库样式 ([1d7fbf8](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/1d7fbf88857636f280a8e93c4fc1bc16b07142e0))
* 更新umd ([c7676b4](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c7676b428ad9182ba13468ca8b8abcaaf1a4a6d8))
* 更新umd业务表单 ([73f7438](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/73f74387937bff821d0146d08daa34c7f5ee88eb))
* 更新umd业务表单数据请求问题 ([60aa9e2](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/60aa9e22ac63b450126a08b39a4f828b39ef9c48))
* 工单、电量结算、用车缺陷处理 ([9414d0e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/9414d0e755ee620bd12423c60eddc94ee5ee24d3))
* 工单信息bug处理 ([e62384f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e62384ffbed9f300ecd40fa8e0fe3cdbfe8f8a40))
* 工作备忘录 ([6086a28](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/6086a28c4f36010b4f1ebaeb4d5872ffe05a43bb))
* 工作备忘录编辑 ([1af3746](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/1af3746f2a3f0c0728f4c486213fe5f0cf5917a1))
* 工作备忘录和日程详情弹窗优化 ([b538370](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b5383700ff744392552080ca93e5bd0a76a52d26))
* 工作纪要状态优化以及刷新交互 ([2a561fc](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/2a561fc24898fc088e249b6a8c883d85d2f8df85))
* 工作台标签动画问题 ([83179ae](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/83179aeab29bff57374dcbbadeb9cea944b0e3d6))
* 工作台标签页 ([f6ed0b7](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f6ed0b7b394b55dc3f1457e73a3f0e9403127659))
* 工作台参数问题 ([7aaf32a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7aaf32a2bbdb741e7e155b0b246d0672ffb5050c))
* 工作台交互优化 ([3aee942](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/3aee942ca2870ac6eb642289dd9018e14662ba88))
* 工作台列表 ([ab766ec](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ab766ecd465f48b6d980355f88005b4d809b2ad9))
* 工作台排布 ([f6db5a4](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f6db5a46bf0469de7a7322098d650b36e4676cc8))
* 工作台配置新增路由 ([1f8f3fd](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/1f8f3fdff876218d85702956f91fd94805628c1c))
* 工作台跳转应用添加和跳转方式判断 ([08fe99d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/08fe99deaf52bdb2adec6aa4b91ec8b19c8a3f82))
* 工作台渲染 ([4ba667a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/4ba667aa42830197a9c11eeb1fd273d0dc1ed1ca))
* 工作台渲染 ([f52882c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f52882c1fca3222c91f0d3ceec6fb63cd8132a5a))
* 工作台bug ([9480dae](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/9480daeef0fab62c7135845640eb9142474a846c))
* 工作台bug ([6e73dc2](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/6e73dc2b7e18aa4b06a00afb0bffdcee4e5409aa))
* 工作台bug ([157de18](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/157de188d9df66db8d09f47bed6a62bfd2fc3e11))
* 公告管理 ([5a6670e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5a6670eec3912edb5496c2a97883e7d052e4097f))
* 公告流程 ([9b6e1a4](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/9b6e1a446c7eb6d79968d117e1c9fc103572c935))
* 公告模板 ([3324b17](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/3324b175b73d45174619c900304c1af512580040))
* 公告模板 ([3979ec8](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/3979ec8c9518b1ea8168a7347bbd26cb00b69255))
* 公告模板 ([5e41969](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5e419697bf4e7b8a27546be2b89e9bb5fe1f3efc))
* 公众号关注操作提示调整 ([c1f14a9](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c1f14a9769a34872a22c4088f329f1ab2c7dbe61))
* 公众号关注操作提示调整 ([7c97190](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7c97190b76e772b0cb690bf112c4baa06021907f))
* 供应商版本优化v3 ([7a8b188](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7a8b1885c12881774d82834f7ec14db3dae7033b))
* 供应商办理路由调整 ([9c0a4c9](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/9c0a4c921f8d4e123126b65ab016c9f5de41e417))
* 供应商变更流程优化 ([4bcd1f7](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/4bcd1f78dc87cf191c8f1cfe3c1d350ffc4cfdc3))
* 供应商禅道bug解决 ([b8f37a9](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b8f37a9a3f981e3bfcfdbc5e6a0884bbf4c57a49))
* 供应商禅道bug修复 ([8ab1b95](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/8ab1b95d8430fed4db0af83c19e02dc0a36196d1))
* 供应商初始版本v1 ([e46bfa1](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e46bfa142309cced9f8ae60ff9dfaf08230eeb59))
* 供应商导航栏调整 ([adbe5b5](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/adbe5b52cbd156f8b709b3538f0594e5aeb03741))
* 供应商管理增加不合格状态和交互 ([4204334](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/420433411712e1405d0e8c4f11a2ad8c9bc5d28c))
* 供应商和客户管理 禅道问题修复 ([a8347f5](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a8347f5bbb70b4c79388a97e9f03921cc28f5bba))
* 供应商及客户导航栏调整 ([b99eeff](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b99eeff15e32a40cfebcf3ff9035fbf1d07dd39a))
* 供应商流程对接版本v2 ([d8cae4f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d8cae4f6ad708edc7d6cd7e432829f7d00e1e9a0))
* 供应商评价调整 ([85ed633](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/85ed6335d22029fd8f441ae2ea4ab428c761879b))
* 供应商新增等级配置 ([924a600](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/924a600b8d7a45b61846dcc80e1eb08045f2fb84))
* 供应商新增系统配置 ([153824b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/153824b0bd1209212e8640873efae47a8e983cdc))
* 供应商页面内容抽离 ([55bd85f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/55bd85f7568028cfce7438809a2156da9840cfc5))
* 挂载逻辑 ([fdb382e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/fdb382e5a56a25c4f8cd328da7e2f8b25965296d))
* 关闭菜单标签 ([dc543fa](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/dc543fa6eee6c3a129b6b02bf102297f0272a7e1))
* 关闭弹窗回复默认选中 ([8b0a0e4](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/8b0a0e49450fd0cbc40ea84b547b9275ac572606))
* 关闭调试，修复退出登录 ([a9fbdcf](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a9fbdcf0d6d34f38e94e5ad88daef8d052488627))
* 关闭调试代码 ([723caa8](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/723caa8ba42321f4e819c403e0eb629b138d0aa4))
* 关联值 ([ec18bf9](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ec18bf956c93259cb25eb53417e7713a7e029661))
* 管理员展示优化 ([1ae8e33](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/1ae8e336e4ff82fa86c0df86aa5e203f5bc49228))
* 规范全局css变量 ([821abb1](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/821abb1f4165a0d36fef7eb162746fa09048efa0))
* 滚动条 ([09bd00a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/09bd00a667fa95ae73dbe7c0e3f2ad1dc07aa625))
* 滚动条兼容 ([547083d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/547083d12af890d85ab8d105234d431b9ef46890))
* 滚动条人员锁定 ([71de1db](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/71de1dbd76fb2b122ed9528550dcb4ef37150228))
* 滚动条样式 ([4e36d0d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/4e36d0dc47aa634adda0d3bc194ed2431de8b0f4))
* 滚动条样式重置 ([17eefb9](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/17eefb91198b28e4d6ce712d8af32855bae96aa2))
* 滚动条样式重置 ([602dd51](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/602dd51ff6c40448b7d96d671a2eb1d9865ae779))
* 滚动条隐藏 ([b1d312b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b1d312ba2f9b49b72827a9d76901186b7975b1e1))
* 滚动自动触发的bug ([5d0bcc2](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5d0bcc2c202afe57497fb6e9334a2ef7c153cde7))
* 国资监管的git ([3af345e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/3af345ec5c3460f3c94c4b6d6b19938de61a1484))
* 还原dev环境baseurl ([5f4b6f2](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5f4b6f2802c96b1ec0deca320c4e7abdf875f76c))
* 还原iconfont的变更 ([8758d34](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/8758d340e3f48dba83f9eecc4e174b15f73dbd0a))
* 函数名字更改 ([53130af](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/53130af87f4afabee3427ed8d86282366b7251d8))
* 合并冲突,一系列修改弹窗等 ([ee82e7a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ee82e7a1cd1517c481a9fed5f29d5ffc234597b0))
* 合并代码 ([28b6a5f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/28b6a5feb5d5b60237da9ebeaf71e085185f6c80))
* 合并代码 ([d93cdd0](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d93cdd0038268f16f207e91a38f59cacbf938a54))
* 合并无界开发版本 ([395c70e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/395c70e9b681ef2f5714d76ccad68a6aafdb528e))
* 合同信息弹窗优化，改成使用customDialog组件的相关调整 ([320719d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/320719d1ef825e2fc10e57ce2aa82912a2259be5))
* 合同选择bug ([4baeb13](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/4baeb1360fe0590019dd0a8a0ba531b2545a0a9d))
* 和app统一名称 ([3d51789](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/3d51789dc3055ca385bf5ee27891c4d4ae40e611))
* 忽略文件 ([0617fc1](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0617fc12681ca972f5a38347d5736754818ec170))
* 环境 ([c183903](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c1839037ddbcf197be08059587c45d2c75616469))
* 环境判断 ([92cab96](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/92cab962502cacdebea02ee58e26559739f333d0))
* 环境判断打包项目 ([05ef00d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/05ef00d464c5ba7b412e9aaeeaddfcfba9c3ec28))
* 环境配置 ([33520ef](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/33520ef72889b6493fefad4ad2bb9541ee2491d0))
* 环境配置 ([5f1492f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5f1492feb14b727c6aa0d18e7d8decaf760debc0))
* 环境配置 ([bcd2add](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/bcd2addda6b835446ade30d36fd5a30ca2223f1b))
* 缓存，减少请求 ([17bab25](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/17bab25fdec982bedb768881b7e5638b5c9899da))
* 缓存隔离 ([b10a409](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b10a4094d0bcf35f6a84a3dac4db13c4d77f015c))
* 缓存清除修复 ([0f0aa13](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0f0aa1390e36a7f6b4fbe4202dfeab41e17dd37c))
* 换eoss ([96eb1aa](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/96eb1aa71f551f415ca55f60bc547a7434045db5))
* 恢复代理 ([e642231](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e64223160ab0c97978e0a3a8c31dc95665e43187))
* 恢复智能对话代码 ([8ca9ea9](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/8ca9ea93e409d955ee424b77cd2896397c06b683))
* 回答判断 ([8d34d07](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/8d34d07f2d76dca4f44cdc296c90d7a44bf4992d))
* 回调修改 ([5c1147a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5c1147adc1ea8424d53d08f9a66533842ab5c170))
* 回退通信问题 ([17e0c5b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/17e0c5b31f04736cfd947b86efb1aedd39e41f16))
* 会话coos和im的目录重构 ([b676d18](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b676d1830878ea94e911759d1a2ca2e22fdab85f))
* 会议内容屏蔽 ([5e968dc](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5e968dce328303115dae22433d53205f77a6183f))
* 获取菜单 ([bc74fbd](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/bc74fbd102341e6e7440fd284c3a62fcc8c53e67))
* 获取但应用配置 ([9b5ab82](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/9b5ab825c8a91f68bb934e5952da9e993048e21b))
* 机器人主任初始化的bug ([bc04432](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/bc0443265e3b3b20f2d2d152acce3f7b7a27df25))
* 基本的表单渲染样式还原 ([bb9d708](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/bb9d7080f7cbebd9a707a3507a7e251683d53bb4))
* 基础设置样式调整 ([f05394b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f05394b78f26cdface6e99977bdc7d8a0463d4e7))
* 集成coos初版 ([a25e4ff](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a25e4ff5fc6e8d6f16098dfcca3b8946ce1cb9e0))
* 计算高度的弥补 ([2ec5249](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/2ec524973b8381b2f2c8451e229d4601adfec8e9))
* 计算一级菜单的索引 ([d0b92e4](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d0b92e44ed9e332d860134cccfe3530d256fa3e8))
* 加参数 ([a0ca802](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a0ca802d2845074ef9bf02f0598947480eee5257))
* 加一个iframe通知事件挂载 ([76fad13](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/76fad13f8af905061058959a239b9f7fc88a2cf2))
* 加载效果 ([e8df418](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e8df418d92825a376199062da8b06c19041e6be0))
* 驾驶员备案申请增加流程图和流转记录 ([83ee7f5](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/83ee7f5f40252c3c072595a2117d4ee2b5692faf))
* 兼容表单配置 ([534d64d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/534d64d79ce9ac0ec2853755dcb5371dc2dcf259))
* 兼容川投跳转重定向 ([a98bb03](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a98bb03b6382d61c7ece6bd25d696d8fae2a9b06))
* 兼容待办打开的样式 ([016ebfc](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/016ebfcbbb26ff8b6d659ff2641482be7a22564b))
* 兼容弹窗样式 ([eeeb813](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/eeeb813648088f0052fb560522607a4f5e56712d))
* 兼容多个图片 ([5ce4c0f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5ce4c0fd28b26b0d3f717613f018869a5f1df5bd))
* 兼容多选 ([b18627d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b18627d454bee74b61a6a49788f10863a1e91fee))
* 兼容框架图标调整 ([8aa2a12](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/8aa2a120b32d1cc5caff81a4038232b56375875a))
* 兼容配置 ([e5f42a3](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e5f42a382f3710d50d55524041dc57f11ae518f7))
* 兼容普通文本展示处理 ([c1f265c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c1f265cbcdb02c3be0b316f501b5ec3f51353b6e))
* 兼容文字溢出 ([80f9925](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/80f9925cf7a3ae01f4253ce1a15aa12d7feaa59d))
* 兼容element组件的显隐，改为方法 ([14adf34](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/14adf345511c1f576b460bfda858456d45e58597))
* 兼容EOSS直接打开的链接 ([5ee69e7](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5ee69e74809889622ac1e5f6d36bb44dc6a26956))
* 监视设置搜索的样式 ([644db85](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/644db85d9d0efa46c017752bb3b1002ddf7cac16))
* 监视设置样式 ([20f1e3f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/20f1e3f86681c0c68f7f1a06ec419506793978f0))
* 减少请求 ([dffbfad](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/dffbfade3d8705d464a987a05d59c07e9e3c7258))
* 检测登录是否真正过期 ([2cf3d92](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/2cf3d92fa295361afecd458bbd340893f5c5e367))
* 交互回显 ([e759c92](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e759c92c0b0b99da0f20c5d065d1f7581b90f44e))
* 交互效果 ([6c5a7b5](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/6c5a7b5586055fd732c03c54d6999bedea3e3215))
* 交互样式 ([1b184aa](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/1b184aa03e82e454ee07fcae64ac03a8375dcd1a))
* 交互优化 ([5abdf3e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5abdf3e74759495bfab8b22c1a43d7cd9d45ff4e))
* 矫正拖拽的定位问题 ([9ce1259](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/9ce125951a6b5e4dda5c90a78818248a19e6750e))
* 脚本拼接 ([0f5ff74](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0f5ff74dc7ebe60bd610f3fe5adb0a6457a3cc73))
* 接口地址修改 ([0bb24b6](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0bb24b69d5557f528be4ae7e2120f14e82f8bfd0))
* 接口调整 ([32de404](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/32de404cb0b9e03d5964d9db52b2d41b8f32cf31))
* 接口调整 ([e71a3f6](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e71a3f63e1fba0db050802b649e0dbf8a721143e))
* 接口加参数 ([c0f2f42](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c0f2f42e45c0729649ad7f2443b28c5a12c164cb))
* 接口加参数 ([7c17ce7](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7c17ce7d2b1f3263be9668718f02c75cf0c65a25))
* 接口联调 ([c4e86c0](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c4e86c075e02de48a242c2a942cd0125728f0a98))
* 接口数据兼容处理 ([593404d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/593404d1c53e2b387276197b886cb0dc8becd98b))
* 接口未返回数据的时候不存储 ([d5aad24](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d5aad24b327efe9faea1a2f3839db581fbcb491e))
* 结合上下文回答 ([6199ba0](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/6199ba09f1a95660a77431bbcc05ba80cf32e07a))
* 解决报错 ([2f6dc7e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/2f6dc7ebeb8f47832d2eac2aa25c03a0fe1bc023))
* 解决报错 ([6df4701](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/6df4701421cdb26ffe6ce59d430dee55c63b4d7d))
* 解决备忘录bug ([32a1da3](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/32a1da34500909047f6237622f6500ba77100fe8))
* 解决标题溢出 ([24db17b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/24db17b2509901faa32d98eb56cca251a7aad369))
* 解决表格错位 ([65f4bec](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/65f4bec2b7c76cd6f783ab370cd1db37228d6b13))
* 解决菜单跳转问题 ([c1e4ebf](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c1e4ebf29cb4e749356e8705c5f4be10fcf458ab))
* 解决侧边栏不定位应用的bug ([e04da09](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e04da094f2037cb6cc6cfccb9d9e83b19df4c961))
* 解决冲突 ([0377a50](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0377a507006a4850cc9a993374494fd69f4cf2ee))
* 解决冲突 ([444482a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/444482a3faf23c531a4825256d5cd0dc087b727f))
* 解决冲突 ([7810323](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7810323a5ff16447d6096b64f3e1a7511ec1edb4))
* 解决第三种框架带来的加载bug ([ecdb8f3](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ecdb8f3175b70a8b724ab93884873b7dc08a7892))
* 解决定位聊天 ([36c0a9a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/36c0a9a791b7e244956a0557f063e996d133a919))
* 解决抖动的问题 ([d5810ee](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d5810ee2ac85edf2676555b00671d2662414f3d9))
* 解决对话数据处理的兼容 ([675e6f3](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/675e6f3cb5b13bdc22387ab59d4397d6b868ba82))
* 解决分页问题 ([0246f54](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0246f543df40c642912c5060a882eb4b53967ca7))
* 解决滚动条的问题 ([a4ed51e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a4ed51e9ce665780abb3891916e078c575f02757))
* 解决路由变化，数据未发生改变的问题 ([6289329](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/62893296f2943a4081a77416481ca5f86087f8f4))
* 解决没有名字slice报错的情况 ([c2033de](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c2033deb02e06aa891f8ffaf42955a5d55c0a74c))
* 解决日程表单渲染插槽问题 ([48c4538](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/48c453850724e3cb59f85b0c380d868c506f04ec))
* 解决搜索框值变化发生的抖动 ([58b926e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/58b926e3ce04865838e8df936590cb27ed8bce0a))
* 解决推荐插件没有出来的bug ([7b8d4f4](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7b8d4f4fce00721d0072338645c52987caef69e2))
* 解决文档下载的bug ([81e123d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/81e123d3fd9fad8a237380890e080f7a6b5479ef))
* 解决无法预览的问题 ([4a53fd8](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/4a53fd84d8382ccde84dee929ac41054c3696b1c))
* 解决无数据时候的报错 ([5075c8a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5075c8af458c9bf048a6670259efb6b4afa21ec0))
* 解决溢出问题 ([d63ed5f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d63ed5f6e2906199ffdfd04d2bbabef6f126fae4))
* 解决组串展示问题的bug ([7efe4c6](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7efe4c698de5a9bfd9b1a95ce7b03a129d3a123f))
* 解决cancel关闭提示 ([af16a1b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/af16a1b5723641eae3e149940a2a7ed97e8fd898))
* 解决iframe打开白屏 ([c9d4fc2](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c9d4fc23af9ad386fec9265c097efc12443734a9))
* 解决imbug ([92c859c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/92c859ce39b79a92d8fba5d854afe3b5700294a1))
* 解耦 ([c397069](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c397069e49395c6a9441648e804a7d6c352d639b))
* 解析文档 ([0aa8eea](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0aa8eea7f76efe61b9218e731e21c0fd8085492f))
* 解析修复 ([ca0f597](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ca0f597089dd1c5096cb51453069f4acbdda1c58))
* 解析json ([e9321dd](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e9321dde2df8ce492f6670bec74a109a9ba361f1))
* 界面优化 ([a947fcd](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a947fcdf0f7f5e43b0aaaa0eae4cc702306df8f2))
* 今日发电量和昨日发电量数据互换 ([72123ee](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/72123eedc8850d784376f2b713dbdc830a6775e9))
* 进入首页刷新问题 ([6e9cf8f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/6e9cf8f7454c63b033ece6cfc315ebb195ac5fcb))
* 禁用样式 ([eede117](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/eede1173698d704b606a8dff79c8787208ab1b65))
* 经办人详情的时候禁止选择 ([e99cd4b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e99cd4bb3782ffe2f014bbae2a1d5de0c10bf471))
* 经营管理类逻辑 ([1b1cd09](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/1b1cd09e12bb3055d0015a151c0d39448f2c4ef8))
* 经营管理类逻辑 ([324202e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/324202e41dfd58ef47175688d6fdf4b603a16cd4))
* 警告规范 ([95ce8d6](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/95ce8d6c948b47e9439681bdf72a5e35fc19918c))
* 静态优化 ([7fa8f43](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7fa8f43ccab34d53181c03348497110c39d02717))
* 纠正key值 ([148686c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/148686cdc3d5891820685e7e82efd924f40c280f))
* 聚合接口对接 ([06aeb14](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/06aeb1423ea6a15cb1293d0c6a5f197f10a3be81))
* 聚焦弹出 ([84ae97c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/84ae97c96244bf83ad35f66eb165b257e964183f))
* 卡片解析，消息拉动调整 ([90de061](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/90de06163942150a647cbdf83f7ae66ea06117a6))
* 开标时间和发布时间 ([5e03543](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5e0354388501dd8b7a1380797d931ae167e37545))
* 开发启动的脚本修改 ([21a1ff5](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/21a1ff56a6d1707d0997aecefd6a289580405547))
* 科研管理流程 ([9769a05](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/9769a052c1782b7426c15a33fc1352090a26ab67))
* 刻制申请表单配置漏项 ([73ab6f0](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/73ab6f0207e7b4e8a5a1cfa61529f9d5c92ef99e))
* 刻制申请完成联调 ([d0ae556](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d0ae5563c512cfecd6809534f915b09cf16ea38a))
* 客户管理参数优化 ([5a26482](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5a26482ffe35421a4dc8a501dfbfa01d5bee464a))
* 客户管理禅道bug解决 ([ea47656](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ea47656f840bb1f3fb2f48a8f83c10b20fc846aa))
* 客户管理返回办公台优化 ([85d5411](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/85d54114ac603d1cedb956c16f1f6623912982d6))
* 客户管理验证| 供应商审核 问题处理 ([b80c308](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b80c3083e819b938e340fa3045cc05f5c82cd0f1))
* 客户管理v1 ([d55f186](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d55f1867a5927f45471ae9d3335664847fa100df))
* 客户和供应商取消页面缓存 ([ac3dc63](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ac3dc637953cf042e8392bc3c59a03d416345b10))
* 空间列表优化 ([33ccad4](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/33ccad430cdb98ceee7dc40495a1fd1b102eb887))
* 空图标居中 ([a26a0d3](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a26a0d3ee8f65907adcda933847ba4b2029c551f))
* 快捷智能会话的入口 ([7ad64bf](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7ad64bf294b92a28001f2ecee1376281d4694131))
* 宽度统一 ([f032d4c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f032d4ca7d03fc01736da766b56a22e1912c79dd))
* 框架的coos ([25976a8](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/25976a8b3c5c3b3096adf8f9af4b9a1c36022fae))
* 框架调整样式 ([405304a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/405304a3079c4009ae3e7b9e657f7096c8ded975))
* 框架兼容 ([fc7a62e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/fc7a62ec794ef35d91740a08275daa5f158998a4))
* 框架模式拆分 ([c60d47e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c60d47e962d358feee6e552685c672cee758e326))
* 框架启动打包的方式修改 ([6bfc52b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/6bfc52bdce43bed3dbc76676c83f394a6c40821f))
* 框架丝滑滚动 ([ca9744d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ca9744d5c00a7fbb33194441206f88cd33caa32b))
* 框架新布局头部 ([b008017](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b008017a02c885e77120782f403b0de6bd95b3ca))
* 历史记录 ([a51853d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a51853d4cf14c7b2d4286385d21a3cce9cb2310b))
* 历史枚举 ([573a426](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/573a426781d0c240e475ae650b10b08343bc948e))
* 历史数据接口判断 ([e5409a6](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e5409a6f60f83ef763887da15f25f7fbb666b220))
* 历史消息回显 ([d554778](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d5547787435ce337c0d888844c7f9fb41054d815))
* 联调备忘录接口 ([36a3460](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/36a34607af0575b39f345a5ae01bbdae276c312e))
* 联调监视数据 ([0ca16e6](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0ca16e6139453a8f813540191b659c9988707fc3))
* 联调逆变器总览接口 ([10994f1](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/10994f15b3d66c09dff437ea46ee51ef4da5bbbc))
* 聊天记录 ([d27acf3](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d27acf329bf07e467a8b293bdc70eeba179c0382))
* 聊天记录对接 ([ab939e2](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ab939e236e7218765ec321d70401625b07a4312a))
* 聊天界面bug修复 ([6970a2a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/6970a2a0f69403bb303b321367548cabd487e9a4))
* 聊天框气泡 ([c06177a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c06177a3bbfe1aab5d4c90494f66a7b33c83b7d9))
* 聊天区域电站信息 ([466627d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/466627d86228f5634d8120c273e0974a42c766c5))
* 聊天消息回车展示 ([28a2877](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/28a2877bf3dc27cceed9bc03aa64bcd0cde251ef))
* 聊天消息交互优化 ([beb30c9](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/beb30c958747771f40f5efc4de6ec6c789a4b49d))
* 聊天消息区分类型展示块，提取组件 ([b4104f3](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b4104f3f134d1591254c0d8fe40db4284ea4dfaf))
* 聊天效果 ([ca4a203](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ca4a203296f4a314d101a537a80791fd4c255070))
* 聊天优化交互 ([b783777](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b783777de85150cc8dcdc68b3d15955c492c9926))
* 列表表单智能对话 ([ca4925a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ca4925a00ade1840f1211880420f12e2959d497e))
* 列表对话交互 ([baa6b96](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/baa6b96bb20ced9bfb9201596c633e6a38fafb61))
* 列表对话UI部分 ([f66688c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f66688c4a80a67171f574d034546b03ad50afa60))
* 列表和表单对话 ([c21decd](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c21decdc5c83d7d77a950c27adb4ee8f91ebeca2))
* 列表样式修改 ([4f6de44](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/4f6de4488bcee52698f8f33023730d0685eb5582))
* 列表优化 ([6f42afb](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/6f42afb6b24a769e2c91d0f4ae38c1f2e16d6553))
* 列表智能对话 ([94a34e3](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/94a34e35c4b1d09e6d47a4eb9cd019a6da900a4b))
* 列表智能对话 ([b60024e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b60024ef6bc36f2c46dbfb2bf3b9e1df1bbe06b3))
* 临时性修复 ([d4f6e31](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d4f6e31f0f9bec0a6ce94e7717e3a5d1d440202a))
* 临时优化创建者的显示 ([c7d5024](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c7d50247858dc6d1bafa93edbdf5c58a030e92a7))
* 临时增加拖拽改变表格宽度的线 ([d62ab19](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d62ab19e7d9a4f42c57d3e8176ec9dc18d1ea498))
* 另一种框架模式的开发 ([ab57fab](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ab57fabdc5af82132ac71b93af5ea3d2533ee99a))
* 另一种模式的兼容 ([7ee4ef0](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7ee4ef031ea9458ae3ef1e13bc92aa9fe940f093))
* 流程 ([14c2e72](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/14c2e726bba1b15e67c449dfafd051791b04788c))
* 流程换接口、 新增接口参数 ([595d037](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/595d03709127d4179b9a1ffd37d5004ed5d10bd6))
* 流程图更新 ([ede2f1d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ede2f1d73d872aa90d5300e42973d8ab4f35265d))
* 流程选中默认配置 ([e78e165](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e78e16534a6a9c53957ffbcd7fac6849d709c040))
* 流程组件方式引入 ([fd2ab87](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/fd2ab873fbda26b497db27bc7d12ab8aff1c5282))
* 路由 ([25a72e9](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/25a72e95599ed4bd08cfcb4f497883add7dc8383))
* 路由 ([519ae33](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/519ae33dc84dd8f307388545cc20ab90c83656af))
* 路由拦截 ([7b581ad](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7b581ad114e49fb5a02621da4e01aa7b0d05266e))
* 路由配置 ([780966a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/780966a0135e73e8895d30ec023cef8a0d00034a))
* 路由切换导致的问题 ([f57d17e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f57d17ec6aa3604d95aaa25ca37cc4b929372a63))
* 路由渲染方式 ([bd63d7a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/bd63d7ae7808802a36273a9bac0340610402304b))
* 路由引入 ([d52376a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d52376a522210d3b1dbf6c58014db5c1247b8df1))
* 逻辑优化 ([0a2c472](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0a2c47206fec00717d6bdca19ea8a4d05ff1d8e3))
* 没有设置搜索条件时候 不需要展示搜索 ([2b248cc](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/2b248ccb20b7e189b4aeb99027b6e8ef7037d954))
* 没有im的时候消息通知角标 ([f029f43](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f029f43e48f1d06201cd5f9d751c355cb220cbd8))
* 枚举值管理 ([52707e0](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/52707e0e3510e09931fd2f374d6f2d3afcf3cb61))
* 媒体查询 ([14bc3bc](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/14bc3bc208685985a3b0ab1fcfbd17a4aa4424d3))
* 每次请求前清空数组 ([1489f22](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/1489f227da83b7ddbb6ce956802323e59b21624e))
* 每个实例校验key ([78f7103](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/78f7103605c40d1de7051e8bca153a5997526b4f))
* 免密登录以及流程刷新 ([0fcd8b5](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0fcd8b5b1da1e2cefdf74ac9a3ea446361dc46df))
* 免密判断修改 ([713e523](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/713e5239eca0822cac8ba728281d14f296455d0d))
* 名字重复 ([4b6e139](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/4b6e1391b5c90b2fc7b48e2028fdbc1ce307cb9a))
* 模式二大致样式调整 ([b154807](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b15480746c1c9e37063054c140091e83a9eea7e6))
* 默认背景色 ([491c175](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/491c1757e77b50c819fba3c851fefae514cb6247))
* 默认不要coos ([e5bb0e2](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e5bb0e25433d832ff4a88bfaf6b53c7cf7bd38da))
* 默认密码文案修改 ([0b27ed6](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0b27ed607545cf56249315a218c7a415ea02ad6d))
* 默认全部知识库 ([9780328](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/9780328409af8a45d9a04544e50df3615ad68567))
* 默认全选 ([72709c3](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/72709c39a6cde42a9b13743363609b9ce4f3b4ed))
* 默认日历新建日期是选中日期 ([9487907](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/9487907b2e40138d1d2d465e599e327c8685a476))
* 默认色值 ([8487491](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/8487491d0a82dd9b12682c5e6317eceb4830f0e3))
* 默认头像 ([eececec](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/eecececf132dc1d38bf2c1ecfa1ce485e2b0bb57))
* 默认图标 ([d8b12df](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d8b12df640050fef7ac0148ec598283a167d0254))
* 默认图标 ([762ab66](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/762ab668567984d83e4c85cb7a4cd556ff54c407))
* 默认logo ([1b58f5e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/1b58f5e47a097b5be13941aeb85594101de39540))
* 目录调整 ([431dd8d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/431dd8d63a033fae22b4f8a6cf92f78ca0f8b4d6))
* 内部组件判断方法 ([a9b28a5](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a9b28a5f3542c2756bc52c8376116e57171bf37d))
* 内容区域计算高度 ([889b1ed](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/889b1edc3f572eaf23f4c07c9da178e26bcb1cf2))
* 逆变器调试 ([710147d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/710147d14103219fa013406903cf9871609867a4))
* 逆变器交互和数据展示bug ([a685746](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a6857466195ec80a00e30b486d8ff906cb0929bf))
* 判断是否需要机器人助手 ([5cf0581](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5cf058148ef1d2face31ac14016e1b1c5c0fe294))
* 判断数据类型 ([2eafd81](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/2eafd81cf15223b0aba1556f153f799471e006d6))
* 判断跳转内链外链 ([f4caa8b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f4caa8bc6365b05c2f4e0dd2e5895be8835c9a11))
* 培训记录编辑操作按钮展示逻辑处理 ([18e5745](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/18e57457395cca6bc776a287f0d5ff933d659072))
* 配置 ([21b1ef5](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/21b1ef51a478bbb8bd44ac2d5a679946265f8e96))
* 配置环境 ([057e241](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/057e2413203b896860fd852538811fefecd62df0))
* 配置路由加参数判断菜单和标签页 ([71f4906](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/71f490686aa371cd28a8fc4e90c53c85e840c8bc))
* 配置目录输出 ([d95cbec](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d95cbeceb2009d7188b2b661a4a7de7e2cbeaa3c))
* 配置页面渲染器 ([1fe1dc6](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/1fe1dc622c4883f7c4e5db425c63a99c3188fcf1))
* 批量删除操作 ([15c9c79](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/15c9c796fcb707580ad30d4dca1ab8fafc9e27ea))
* 拼写错误 ([40ff3c3](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/40ff3c331a14d01341a50a5060efb51cebedb8c5))
* 屏蔽还没有用到的引入 ([5820647](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5820647f30e19ab91e37ef90a612181f2e860a89))
* 屏蔽聊天记录入口 ([0b673a9](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0b673a993a37eab010b13d074372e6b372388d64))
* 屏蔽日历新增参数 ([b29bb10](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b29bb10e147657ae4b631e619bca6edb045d4212))
* 屏蔽未开发模块 ([58d4cda](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/58d4cda4a9d12d4130a8a9d7952c429973c7b681))
* 屏蔽验证码登录方式 ([3fde852](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/3fde8528937c4156ffd4064d14b20e72d202fd74))
* 屏蔽语音 ([70ee46d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/70ee46d79f0de9220596ec15c9894358ab52401c))
* 启用组件的注释 ([bb48558](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/bb485582d9862d8d2ceb078c29b595ed0afce219))
* 弃用接口删除 ([f61e8a1](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f61e8a164ffcbd2f040fc858b3b72c61a823e445))
* 弃用noCache ([b3b3192](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b3b3192248873c18d539040b941769945a1c0750))
* 迁移新能源运维 ([6afbf77](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/6afbf773b9f033a2088000a6b0d5a5904b1b33c0))
* 切换报错 ([d3af3f5](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d3af3f58f16f5fd49647e49249b8a9e2a519441f))
* 切换身份bug ([1a31ed0](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/1a31ed0ac678feabfd7fd171778437349cfd0e7c))
* 切换租户更新水印 ([d183bb0](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d183bb0220ea8870085f0ab05a4fe2fe4c93cca7))
* 切换租户逻辑 ([3930b72](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/3930b729fe0773b5d63bf77251f78252c0004f95))
* 切换租户重新获取配置 ([7b2ddf5](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7b2ddf546e282cb9af2f27f004c07b35a96c9814))
* 切换组环调用接口 ([cafad48](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/cafad4863f6c614d907674cbe15d020a15e5846a))
* 清除打印 ([acb812d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/acb812df15bcb499d257cd407ff4e41b09796aba))
* 清除缓存 ([84f11bc](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/84f11bc0ed53bd70a66f33c93a919d1cdd8978b3))
* 清除全局缓存的逻辑更改 ([224460b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/224460bd426c2e8ee90ee3378c4b3cef91e6cb0a))
* 清除RToken ([afe00a3](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/afe00a3e5f960a5e7e6568b4ffd7288571863d37))
* 清空打印 ([45d4aa2](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/45d4aa2c3c33b79b60542f66e032cb21bca81b47))
* 请求参数 ([084d269](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/084d269e7f5026cfb76ca1e69556e71176a0ebf2))
* 请求超时处理 ([9ccdb1d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/9ccdb1d39889e22d150abacb38d05af4b173d1de))
* 请求超时限制 ([2895a28](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/2895a28c9dabb8ff4404f21779e681942d13188b))
* 请求失败的封装 ([36204ed](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/36204ed9e80f9de1d7813eb1774a167d2638db31))
* 请求问题优化 ([1d31356](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/1d31356f317ef8727d50d29c7d29b4fafae97067))
* 请求响应404提示 ([d3ea930](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d3ea9303b50e59a37fbe2a2d4727bda04267136b))
* 请求重试 ([e4dd099](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e4dd09941f9b4b26564adbf8636445522e4ab6e7))
* 区域请求逻辑 ([4976e3b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/4976e3b49ab39806ed66e0f3d7da23c1e148532b))
* 区域设置重置表单 ([4785690](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/47856908f443a2ab1fe7a8f06c7eaf2f48c10387))
* 区域设置自测 ([66be6e7](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/66be6e7d84fae90da5591573d4869bfb1bcb6871))
* 区域使用站点id查询 ([a1dad5d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a1dad5de0f492f4f3ef195b9f270c45b8f99a92e))
* 取参问题 ([63c0114](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/63c0114ecfa0161c3a8b7a61019316da5d770a47))
* 取后台配置的标题 ([1b3b409](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/1b3b40911623a7b15da87657f19bd6e926191521))
* 取缓存的导航栏状态 ([cf5b7e0](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/cf5b7e01d8282b36207e10721d1c59cc9f85e3c2))
* 取名字最后一位 ([6ec187a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/6ec187a66904902f81f500d42211a5b81ab8f156))
* 取消操作权限校验 ([19330cc](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/19330cc3498e6ad0cc0179d8e81db001c06ed5d8))
* 取消进入权限判断 ([42ebda9](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/42ebda9bb29320e431dad280339545b002dbee4d))
* 取消前端调用api实现应用退出登录 ([69b7b7b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/69b7b7bdad8681c16081a4cecebbf07d699a16ab))
* 取消收藏权限校验 ([9f78479](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/9f7847910e16928aa920462a1a320d592e6d70cc))
* 取消搜索条件的默认值 ([b61ac67](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b61ac67a184f299923baf3105738fef5504db0ae))
* 取消图标 ([7bdf476](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7bdf47674719782af7923a9b529f3167a41d73b4))
* 去除边距 ([5381687](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/538168726eeb10586ef7a245230ac8ccd3c7e8f0))
* 去除打印 ([10fa0de](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/10fa0de6a7edf9d5f95a25cb969f0deb773d1f47))
* 去除打印 ([5e22566](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5e2256613924bf6b01dea045ca6adf39774610e7))
* 去除打印 ([79267c7](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/79267c70f21ecb4e2e7c22446807dd2dbf4e8443))
* 去除打印 ([92e9371](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/92e9371153d450d02faa50a9d7ba165babcab55f))
* 去除打印 ([036c1a1](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/036c1a1eefc6e149c8b3b0129e7e201ab3cdb4b2))
* 去除打印 ([d7fded2](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d7fded2a77a24bea659ef3583ed2e2b8fabbba3d))
* 去除打印 ([8cbdee7](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/8cbdee77ca4277b118441335f8c226a15d61a3e4))
* 去除调试代码 ([2d17dc6](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/2d17dc6f9ea941d730d88188806771777bbc6b8d))
* 去除调试代码 ([2a32f4b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/2a32f4b3d8338d52167170b2fe7599d53e92aa04))
* 去除多余参数 ([f9a6428](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f9a6428dcd052c982484262decf32b0668cea001))
* 去除控制台 ([cf22e86](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/cf22e868056f04d3d4675cf90c05b1f763d5682e))
* 去掉不必要的功能 ([72dd83e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/72dd83ece1153618bf15fc00d9921ec0622c7f39))
* 去掉多余的:key ([e71b616](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e71b61685803f6877ee9277a5e32bed51ab3f7ba))
* 去掉冗余的调用 ([7b50723](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7b50723039fc0a8d69f6e1cb50d3033fa2cc223e))
* 去掉外层判断 ([385e0a4](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/385e0a4e7f19737ed8197107788dd3de932c5c29))
* 去掉限制只能选择本机构的数据 ([fb2cc44](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/fb2cc4425bfb0161a3690683b13eb929506bdc97))
* 全部改为公共弹窗 ([d21ae24](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d21ae24d0ba132c374a305abd78dfdc976062a90))
* 全部改为自定义弹窗 ([2fcdff6](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/2fcdff6e3235745600d9be24b1ba8bfddbd20a8c))
* 全局保留智能对话快捷入口的浮动入口 ([4c64695](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/4c64695c2858e30b7873f77ad06d317f191406dc))
* 全局的浮动ai入口 ([2ee0ba8](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/2ee0ba814a36ee08009c336ebce3c37af8573041))
* 全局的滚动条mixin提取 ([f061d82](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f061d82c257e7bba546507c30792ec29a6643742))
* 全局的ui组件颜色样式统一 ([a33ac08](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a33ac085309d9f0aaccc24e64692d59c53fbc000))
* 全局禁用样式统一修改 ([dcb2c85](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/dcb2c85fe955fe504d5b42e1f3e338033df6b567))
* 全局三防系统的智能助手入口隐藏动画效果 ([0271f13](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0271f13c6c5a23ba6f9a82c42fffcb3831d558ab))
* 全局色值 ([48d14f6](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/48d14f624690b0cda28875962b46bc0d1b773f92))
* 全局事件管理 ([dbcdc18](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/dbcdc18cd46ed5ce2746d1cdd47bc6d6a64535da))
* 全局搜素replace ([07d625f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/07d625f268a35555675246c549278b48b4cd7330))
* 全局搜索 ([4cff83b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/4cff83bc969f5c438a46bc91ce8cd9a525e6857f))
* 全局搜索后滚动条定位到目标 ([9c14728](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/9c14728e8a13e0543c0d152cc27f0d463b499ff5))
* 全局拖拽入口统一共享位置信息 ([5206ff3](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5206ff3cf5055be0d18f38ad93f489453e7033cf))
* 全局修改您为你 ([95d8df1](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/95d8df15eba09e12ad67a95b35e013a357e63317))
* 全局修改el组件样式，前缀名修改 ([cee4892](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/cee4892b7630af77db310485860661379ba0c0e5))
* 权限校验 ([489a559](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/489a559d9f07c488796e501a469a2101cb2e16c3))
* 权限校验 ([7e621c9](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7e621c99371287e60e88dfd08194ad9be3742730))
* 缺陷管理样式 ([947ea0b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/947ea0b39808db06b7ef404fe36a8aced36e9b95))
* 群成员创建逻辑 ([c5b96a9](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c5b96a9a9bb3ddd983201752b0297a82c8e496bb))
* 群聊提示词 ([9a10be2](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/9a10be227fac60d6fba88be00b44a99be3ea4a12))
* 群列表优化 ([2c1ebf8](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/2c1ebf893cd9f3494397c24a9d07568b5ab2acf3))
* 群主判断以及滚动bug ([d4bf0fd](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d4bf0fd285dc7cb4eeff449b9c0473bc5ad9f0bb))
* 人员持证，人员档案模块，弹窗组件替换优化 ([e18a0fd](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e18a0fdc8259cc33e1bd7898d8399796bc23fccb))
* 人员持证弹窗优化 ([b384389](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b384389b6a4596d1b680a3606e866fd6bfabeb28))
* 人员搜索优化 ([abf3a2f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/abf3a2f2068f62fb6909a1f6bb9734628975fb65))
* 人员选择事件派发动态处理 ([732917d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/732917d08ae029ebf7b87c0e0f0efb5e6c1b493c))
* 任务标记重复 ([ba35c92](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ba35c92650fc974fde82b035c8d19b61f258e517))
* 日程编辑弹窗优化 ([4ab7d2d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/4ab7d2d77ecda29bd74c92a265d7ff6c38cb2ea3))
* 日程编辑及详情新增扩展信息 ([cb0443d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/cb0443d2d72791a1b0abfcf0c544293db2fe755b))
* 日程类型选项传参修改 ([24c14db](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/24c14dbe50d89a64d8c0e2ad91d5cdecd9a68b56))
* 日程联调新增扩展信息 ([e31377a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e31377ae41a7636887fc176d2dccff6e55c67fb9))
* 日程添加 ([2e8e66f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/2e8e66f0cd6a8ad65e0dc3700650a2aa3987bc6f))
* 日程显示bug ([5e0810b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5e0810b891cf623a19269fed1ca99b5074b66079))
* 日程详情日程时段展示调整 ([b632b9c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b632b9cfb7efa94964970a00a82f6b8dd74c41a8))
* 日程新增 ([1afdd25](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/1afdd25ea3edd08e86feaae88d663eb03fdc7335))
* 日程新增主题非必选 ([062e9a4](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/062e9a483c7343d75245698652b7d78e41d8b09f))
* 日程修改 ([a116bcd](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a116bcd2e669ae5fb37a8352869fb3a64d92dd45))
* 日历：详情标题展示换为内容 ([f57d948](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f57d9480a538d368fc6126dd4b6fa8e14a9d0453))
* 日历编辑弹窗bug调整 ([ae9e8eb](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ae9e8eb1ddf44ad4825ab39ff763f56c4a295b71))
* 日历点击bug ([86b31a2](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/86b31a2132af6b8da92683ac7227986f77370830))
* 日历调试 ([ffd0d15](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ffd0d15ac36e9045d1bf6903894a9ca8b1807104))
* 日历交互效果 ([da4aa73](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/da4aa73ce74b6f111c043ab0c56038bd32259dca))
* 日历交互效果 ([64b3282](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/64b32825dcc2e9f0ecc71ea81c8c41da2bb46e95))
* 日历接口对接 ([6feb110](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/6feb1107228221af0e6bd8c6a3ca4295261378f5))
* 日历开发 ([dad9186](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/dad918673585f5b72858cbf6e162629a0a129765))
* 日历控制台报错Bug ([0806009](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0806009103f520e55ef2eb850a64bcc9f1fcb3a0))
* 日历列表显示交互 ([c4e0a94](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c4e0a941cc0aa7b94a997907069ffded72ff2a41))
* 日历逻辑修复 ([57d4f6c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/57d4f6ccb79d7d0dde60f8546631d330026157ce))
* 日历屏蔽主题 ([ec0f43d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ec0f43d1b9503ec202d572f7f406d371f2c7b6bf))
* 日历全局搜索功能优化 ([5d739ea](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5d739ea3154b0eb0db75b305619f582cad865c2c))
* 日历事件报错 ([d907d0c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d907d0c1ab3a72eda16af4e10e8e7d32c2210f34))
* 日历数据显示调整 ([d66df37](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d66df37eb2a813d253a8840ac16473417141c940))
* 日历跳调整 ([b1ed357](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b1ed357ffdd3ba26c41e6a8b05bd6f52fc3e7136))
* 日历新增 ([31e71aa](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/31e71aad6649b7ec4fc87ee848701729c424789d))
* 日历展示 ([bd17792](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/bd17792d250fc341dbd250d36d8a3e7b26f29cf3))
* 日历主题屏蔽调整 ([d494c8f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d494c8fab7046886d176bb08880576c04d12bf78))
* 日历主题显示问题bug ([0357ad7](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0357ad7a650492fee1fa45f34f22b72616f656bf))
* 日历主题bug解决 ([7e2a42b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7e2a42be474da4aa81b17253177561cca3fde02f))
* 日期/时间为搜索条件时,应该修改为日期/时间范围 ([6e2d114](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/6e2d1144cb6b501429467ddf6114f5b469e86f98))
* 融合待办的对话窗口 ([b359021](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b359021ca3ec2f6540cf89c331834a28691b1ba0))
* 融合完成 ([8f9cac4](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/8f9cac4686fe5f779aba99dfefb1b38cecd4207a))
* 融合文档 ([49b8a80](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/49b8a80be6e618e764f1403d44ed01906b27bbfd))
* 融合智能对话 ([876f41a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/876f41a28612e050011b6018eab63871f3f09934))
* 融合组件 ([195d112](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/195d112e1c3e6c3ce609f166f060888349fbb3c5))
* 入口打开 ([cf22ccc](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/cf22cccdbd09d58c6b83bcf8704baf13bd9604bc))
* 入口图标拖拽出现的bug ([a81944a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a81944ac7c3e366ce5113edd7f97abe36088da61))
* 入口图标拖拽问题修复 ([a29be07](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a29be07f53215244a3179b589304616149df2946))
* 入库申请 ([74a3b01](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/74a3b01067c82a9bb1001395d010ce49bb2c20d1))
* **入库申请:**  职务改为必填项 ([2e70a27](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/2e70a27604f108c7b63e82866466efd3220c475f))
* 入库时间和头像 ([17f759c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/17f759caef71e94464e9dd44338ef21125dc8839))
* 三方集成的路由进行规范统一 ([902165d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/902165df04e1a1f3bd25168fcb992e98767a84e8))
* 三方集成通信 ([3a634c3](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/3a634c3054ec58e2cf8daa18568ab8cb43e8f292))
* 三方退出地址兼容多域处理 ([3cd7d38](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/3cd7d385ad8eca839290d37b4e3953e5a3c2cafc))
* 三方系統遮罩层bug ([bffc9ea](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/bffc9ea15c81f3aa520d4b73588a727e92a5bf9f))
* 扫码登录 ([4e9a605](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/4e9a605b672d03c12697fb29978c4a6b3abeb143))
* 扫码登录 ([fcc71da](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/fcc71daf4f66e60aeb9e6c83645eaa644b64b0fb))
* 扫码登录权限控制 ([b044430](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b04443073e99acc2119337a20d8f6d1ae6bce8bf))
* 扫码登录用户端 ([30cee74](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/30cee74e0747cce84d14ba9b669736b49944dd28))
* 筛选出叶子节点第一条路由进行跳转 ([a26d162](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a26d162e2c10e8899a5ebe6f4ac059b1d1a5df9e))
* 筛选的逻辑 ([b78531f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b78531f912f16b203f65f637c3132da4f93715ba))
* 删除操作 ([2ec77a3](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/2ec77a38fd21a270892f233a1f65b556a9f43576))
* 删除操作 ([ba9437f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ba9437fc0d72797d1e8c4a3b7c40c43819b185bd))
* 删除测试页面 ([ebb90d0](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ebb90d00ea02f5a16561f956ba8df2e6fe606add))
* 删除调试代码 ([9d4e05c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/9d4e05c8f371b0da66cb03265792b8a360eaa9c1))
* 删除调试代码 ([d04a38e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d04a38ef70869e24b0b26cf2547845f4f9da0b27))
* 删除多余变量 ([c3a1c9b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c3a1c9b9089954a8452b84d301257c78f2dc89d9))
* 删除多余的代码 ([dfbaada](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/dfbaada54092e1f12efbfa75e262b67be13401f2))
* 删除多余的控制台打印 ([91c6132](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/91c61326e60d8ae5c382917579ecef9d28009f1c))
* 删除多余文件 ([798585c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/798585c1dd9dcf08c61bd23a53f03b0fe4f2b2dc))
* 删除多余依赖 ([e6552f5](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e6552f53f954bdb27299190a17dd37005c220aa5))
* 删除冗余 ([86cb78d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/86cb78d117d657a38c0857fb949c462d294114d4))
* 删除冗余 ([7d451f6](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7d451f684699c568a2cb6e2b70f4356f18735a20))
* 删除冗余 ([2acab3f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/2acab3f90ebe8639f3abb427407ec0f7898fb4ec))
* 删除冗余变量 ([451c31a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/451c31afa1b6cc9abea52c75c2f3558bf284c89d))
* 删除冗余代码 ([0ff17a9](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0ff17a9557356b28c3343d7a914229351da04eee))
* 删除冗余的代码 ([98e8a62](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/98e8a62993c6ccfc4acc17c094c43072d930e5d5))
* 删除停止的请求 ([2cbecef](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/2cbecef95b580118ee548798356e5f436c6780c0))
* 删除无用代码 ([4a06322](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/4a0632201537abfcf3fb65f4b74169fd70e8482d))
* 删除注释的reload ([710af55](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/710af55d4ccca1f3a4a403debc891123d687e3fe))
* 删除im联系人菜单 ([42310be](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/42310be2beab4d6a467ff1a2761d84919160024e))
* 上传 ([42db783](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/42db783695d13c0e058cd715a4486fed9e5ecdd8))
* 上传进度监听 ([3c67898](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/3c67898f80724c36926ffac09b531ef518803ee7))
* 上传进度业务优化 ([4286a8e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/4286a8e7e1fccd8f182da34741c65ec99ed85e84))
* 上传进度最大限制99 ([d6da136](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d6da1365992f383918e33f16362338f06c841491))
* 上传时间优化 ([6218b0f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/6218b0f74fab1137d3a694736e51f22e9b6884eb))
* 上传提示 ([0a31f34](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0a31f34ec4b7c3f8ea5e0f58a892592a3254b2fd))
* 上传图片 ([2d8df12](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/2d8df1208700649e06714d40b8208efc0e1f073b))
* 上传文档 ([ca7c002](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ca7c0026046cfca16e7e9b60741596dc9ef4e173))
* 上传文件空值判断 ([83aabe3](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/83aabe3254a924b76879a5b14b5b28749fe5a499))
* 上传限制 ([d7683ad](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d7683ad2734d0dfe77f66a978dd9b47123fa8e6c))
* 上传限制 ([ef670a1](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ef670a1e758da765d15fd33797394f6623777174))
* 上传优化 ([79c6e60](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/79c6e60651a069cf0f396205ab0b40b8c1820b0e))
* 上传中断 ([619a385](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/619a3857d149528e0e5bb40ffbc932e372f77a5a))
* 上传组件 ([a7c52b5](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a7c52b59c847ad2c5cd7c4875f17bec754f6d00d))
* 上传组件的升级迭代 ([5666136](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/56661362034430ffbe59da5b18b15333ad9e03d9))
* 上传组件关闭上传中的数据修复bug ([60bbd00](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/60bbd006a5abea72b3948b60b711619fd7cdea66))
* 上传bug修复 ([325af80](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/325af80f020226a7eed4c86c6105ae97c100fb91))
* 上传UI ([9a98b37](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/9a98b372b6db38b7f2f58b2c474151c82caddcb8))
* 设备设置联调 ([80ef845](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/80ef8455202bb9f45de9bc7eb0405299fce304c7))
* 设置视频信息列表和弹窗接口对接 ([1ebeb45](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/1ebeb453f49678a2c61931ba5cdcb8b5cbdda2da))
* 设置头像交互 ([4b93c99](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/4b93c991b8db7770f928b552ab4b5010bb3da8dc))
* 设置主题 ([0782cd1](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0782cd192fca79fee5864d186033ddcb25264b20))
* 申明校验 ([95c68f7](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/95c68f73bebeafe8b0a162e5c2ab3a9b30b6fb28))
* 申请静态页面 ([3722c2e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/3722c2e58d8acd81ca647495ddac34794b42c046))
* 身份切换租户切换根据引导页配置进行菜单加载 ([963d037](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/963d0372aedd831a04622205d2885bbb7747cd05))
* 身份做兼容 ([ac24e35](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ac24e354fc1969c841e9ac5f3a3fcbf449919135))
* 生产管理修改一些样式问题 ([05b9d09](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/05b9d0944d3f235c9b9a2bee81c04fff11fee3a3))
* 生产管理修改一些样式问题 ([0a51314](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0a5131425a5e49f4001605bd84c033685cf90e56))
* 生成ppt不限制字数 ([039c622](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/039c622f66f9fe9ea368b290030799067c868472))
* 生成ppt修改域名 ([26dcc1d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/26dcc1d179d253ba9ed36f11fb23b01b651f407e))
* 生成ppt修改域名 ([29a4365](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/29a436526a3f324654a48b6a685fd5e4d4a2f422))
* 生成ppt修改域名 ([f3b22a8](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f3b22a89fdd7709598aa2f1be35b12a8700ae247))
* 失败提示居中 ([1e65a23](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/1e65a230f66a52f8866f9986e3b25954405384f9))
* 失焦的时候延迟收起弹窗，防止事件中止 ([925aaf5](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/925aaf5eb83cf7be1d63a98de76c64335be79b1f))
* 时间计时器的bug ([da20360](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/da20360826cff1ba09edad0e66c3956155550daf))
* 时间来源样式优化 ([5592398](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5592398e4e247fd3ecd726c7612912b4c340839d))
* 时间统一样式 ([88eb7f7](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/88eb7f7d4db8397d2f91fe4960be197cda658664))
* 时间只读 ([05921f6](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/05921f662c0260eeca6e2d4715154075c9ac7750))
* 使用说明 ([9ef55e1](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/9ef55e1295d9a722b354d7766a6d55b377267daa))
* 使用说明 ([911a510](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/911a51047fe14739dfce067659810e9639cac498))
* 使用cdn共享dll依赖包 ([8755fb7](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/8755fb77957b5fa115e7ca02eebe6fd0f7db38dc))
* 事故快报样式 ([453b3fd](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/453b3fd1b46215cbff61452093dddc884b543bcf))
* 事件背景的修改 ([26a730c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/26a730ce3ca8d0a60df8cbd3acd27edae0836cee))
* 视频监控页面接口对接，告警总览，视频设置，视频厂商设置页面交互优化 ([448e565](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/448e5651fdef33d79fb51a26441d3efdc1f3df7f))
* 视频监控页面组织树优化，设置视频监控字段优化。视频监控列表页面样式优化。告警预览样式优化 ([f9f6c19](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f9f6c192354f176319ad684cc6851bf28e9c3cff))
* 是否缓存页面 ([7fe5833](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7fe5833eced9808c4d7841260e3bceba4ce95440))
* 是否全部知识库中查询的 ([983810d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/983810dc2a41655d76c5ce5af2bab3f716cacc9f))
* 是否是标签，路由动态控制 ([0900f0d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0900f0da769b16d6fde47e42e20856a6b067fb25))
* 是否外链打开 ([01ce677](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/01ce677981ace08f5167b10e2e02c46e70bf0582))
* 收藏 ([c2b68e0](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c2b68e02444d27cbf1a37b6c145e13051fdcc0a3))
* 收藏调整 ([0555d33](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0555d33b40644740e1a39cf4f2a18199eaad876c))
* 收藏列表问题 ([5fd79ba](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5fd79bac38876618164043f3a780d53605a60ec0))
* 收藏文件文件夹进入方式 ([5e47430](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5e47430c0940b49273dde7c326dc29df4ee5e72d))
* 首页接口对接 ([1418a3c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/1418a3cc04d138315353c2e44704db42b5372059))
* 首页接口对接 ([7aeea12](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7aeea12a20698bda7077543042434154488e0bdf))
* 书写消息备忘录 ([7105901](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/71059014f29169f9bf5e8cc9e4c4c5ee91ccd16d))
* 输入框高度，调整引用位置 ([8546e26](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/8546e261140b42ec0a65e2eb731fdd2c8b3593f5))
* 树 ([9b0e17f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/9b0e17f239aec279643a91dd1c4b496dc0e70ecc))
* 树形结构 ([c8e9bb4](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c8e9bb48c19c3e1086800b207a7b1b46e30a99bd))
* 树形数据兼容插槽字段 ([efe1148](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/efe11480a5ce19f98fd669515649b15207d4d7a9))
* 树形组件开发 ([06fe9ef](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/06fe9ef48f3f761878446564859069a2abcc833c))
* 数据不足直接隐藏按钮 ([e965caa](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e965caa8a40d7e624dad8f3f20d68004b64a29d0))
* 数据处理 ([8767c0d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/8767c0dcf86cffb2cff5988dbadee2d4cef69af5))
* 数据处理 ([9fbd7ea](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/9fbd7ea53782769ddd4a7302bb329eacf5e2c2e3))
* 数据恢复 ([c035e72](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c035e72d1719666efb41f5a4a4bcc292e1d055ac))
* 数据排查 ([129f1fe](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/129f1fe4e93e8ff357891478b1a99e7affb517ad))
* 数据请求优化 ([377dbd4](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/377dbd4a314fc019775b9bebec7abc96fd58f76c))
* 数据为空时候的报错 ([55b6638](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/55b66387acb2a63a19e16fb6d056e7fc331c3039))
* 数据选择的提示 ([5a231c2](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5a231c2348643425414e7609a97364178a162670))
* 数据钻取模式 ([c38e78c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c38e78cef9784977d3b0f5b59cabf0144df98e81))
* 数字输入框 ([804d2d1](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/804d2d195f480349bfc71ff79af72df91e1b8c39))
* 刷新配置获取逻辑 ([94f6d79](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/94f6d79c62957480ebf68c48c5734b354074fe52))
* 刷新消息推送的数据 ([bd6bce5](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/bd6bce5d064d6e30da42920312fe4b0c9d418f77))
* 刷新CDN ([a85f746](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a85f746000c2baf4b89ec6316876946c6e019ccf))
* 搜索 ([e1f2162](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e1f21624ae05a50d9b0f6200a80a9f74ddad03fb))
* 搜索词修改 ([b8129af](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b8129afdc85cce80d7cbb68db4928efc6825b69c))
* 搜索控件控制调整 ([87e96a7](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/87e96a7dc3a8de72b3797cf9dac80fe030c49887))
* 搜索跳转统一 ([da617f4](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/da617f4c1a1af28faad6659d848e571e7f3bb346))
* 搜索消息跳转 ([0bf9645](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0bf9645f76f64f12e8210b6e9006100a1b88969a))
* 搜索修改展示问题 ([6bb7a24](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/6bb7a245b8ee4cbe68e4845e76cc94d5962c0c37))
* 搜索渲染的bug ([e15f1b4](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e15f1b45bd36c41450aeb61f767518ca50ec4b99))
* 搜索优化 ([0ba8152](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0ba81522530c72699e49773e080735b4c52cde2a))
* 索引删除上传文件 ([6f38443](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/6f38443a3812755a76da6302b9fc0e0d753ca7b2))
* 台账对接 ([d7c8299](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d7c82991c36bfa7f9d9519aa17ca76ff5654be63))
* 台账工作台预对接 ([d9a9ec1](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d9a9ec1574f0f831fea7153ea9d2c022be8b1d4f))
* 提供给第三方的用户信息获取 ([0798129](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/07981293ae2218deb900f523bf29723c06ac2098))
* 提交 ([256bb49](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/256bb491797888bec58013790003164e6db0aa59))
* 提交 ([9f36619](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/9f36619f3415db8d65f22aa89dbb2cd2303b53aa))
* 提交 ([2936b54](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/2936b54a1c69c0dd56ca6fbd72b5a509ca5e8747))
* 提交 ([0d6e713](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0d6e713a095689a7bf8888b5f18aab92b2272e30))
* 提交 ([059bf1d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/059bf1d6e1ece0e8736acba2dc480ee357384bf6))
* 提交 ([b231e89](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b231e8974ea0427070ac8a20850a64879533f956))
* 提交及时通知的联调 ([1b9f946](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/1b9f946c9c62edcda90ed1efe61841790ac8f6af))
* 提交三方集成目录 ([0a358d4](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0a358d45b0a111dba6d13b562138e20d397271fa))
* 提交之前自动修复 ([fa8bf00](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/fa8bf00c3f5b68f527f67eab3acbc4fcfd102bee))
* 提取待办弹窗并且简单的走查了代码 ([c47e502](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c47e5028e0870d927e0c83861439823877eec1f1))
* 提取公共组件 ([fc1d356](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/fc1d3561bafed6ec1076df5eac2caf58b693931d))
* 提示语句 ([bdf20bb](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/bdf20bb2323c56a5409596d30c10b84ddcffa322))
* 提示占屏幕一半 ([0a5faf3](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0a5faf3906148013102c993448eab0aa602112fc))
* 替换图片 ([f382cab](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f382cab80b206f07902e1ca0dfe0c81fbb5b2893))
* 替换图片 ([b1bc89c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b1bc89c2c1fe225d9ced366a2c2a7a53bfb1f97d))
* 添加待办详情作为页面路由 ([d91d2de](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d91d2de1e40dd78459bf0dc876abb0f54cfa726c))
* 添加工作台取消应用跳转 ([c952bb6](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c952bb6451305b00e249c3d452bc8fc2672e9537))
* 添加配置 ([171349d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/171349d9dd9637a6103e78fa66c72276a5a86efb))
* 添加右对齐按钮盒子样式 ([c429614](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c429614aeb75d856b4b9335169a5a4bd028d2758))
* 添加deepseek兼容模式 ([e9c6f18](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e9c6f181a4e45d59bef8f5f80bf5696cbe2ac280))
* 跳转工作台应用参数问题 ([1b59f45](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/1b59f45c286b314de3bf9b8954936cd364dc04e1))
* 跳转具体的文件目录 ([a0467c2](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a0467c2dfc1da52633d20150ddccf0e09482a88f))
* 跳转首页 ([e9e4ac6](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e9e4ac68475dee242015606bbb88e15856861929))
* 跳转首页 ([f17c830](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f17c830dbbbd7d548ee846dceca82e2ce296feb0))
* 跳转租户端访问域名修改 ([54a7efb](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/54a7efbbf4cb77ef227d103019078b1cddb830ea))
* 停用销毁申请 ([01392a5](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/01392a5668c4341cb3c5ad60c026dde7a20186fe))
* 通过userId链接的socket要在登录页退出 ([7358196](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7358196531c364a86df284e3a8420a4794f7dd80))
* 通信统一 ([f6cd90e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f6cd90ebab745cc0d8c994d208f39e49454a1206))
* 通信问题 ([4c928b5](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/4c928b5799172597875adf37e3b521158d57229d))
* 通信载体完善 ([f000fe9](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f000fe90fcd46887f125140f95cfe6a4b9f9c985))
* 通讯录的交互bug ([6af1de7](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/6af1de7827273f330ade607ca83b7d7e05f21ca9))
* 通讯录对接im ([3e5580f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/3e5580f51413578cb6ea13748ba7ea67a94ad6bd))
* 通讯录父节点点击就刷新 ([bb8fe0e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/bb8fe0e1490c8dd5d4209c9efbeab1f6cd092a27))
* 通讯录个人名片跳转会话 ([a2e9c81](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a2e9c81fad3999d248c83c6491aa2a9ff95d0f6a))
* 通讯录图标问题 ([7a147dd](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7a147dd6aa3b6c2e05a5707e98ef5db2d26a08a3))
* 通讯录为空居中 ([73b21d5](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/73b21d5743709cbcae78409158d1dcb826865801))
* 通讯录新建目录 ([c308fe8](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c308fe8c501208184c76332cd2adb0e93e853ba3))
* 通讯录选中 ([b998865](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b9988655677e3085d140a371ea7a2f239d393dc1))
* 通讯录也要通过是否有im配置进行展示数据 ([208e291](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/208e2918a4856c5c55c9c7fddb893ffeaa66296a))
* 通讯录溢出问题 ([c46c02f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c46c02f20d3f1fd9ca0a7a394a991578dbe4ba93))
* 通讯录展示、表单组件新增插槽 ([92b8294](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/92b8294558d22babd70bd4d140dc7720d500b5c7))
* 通讯录展示、表单组件新增插槽 ([96862a9](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/96862a90ab294fa0d6afb98e106404f68bf43681))
* 通讯录enter搜索 ([2a54e62](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/2a54e6288316051a3082cfc4fa562a8ed311dcba))
* 通用打包测试 ([b6e20be](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b6e20be03062dc29e0f1e13b4f512c3917e4a95e))
* 通知系统跳转页面 ([fd4f900](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/fd4f900b8aca43dd1787bd17f4992893d83bbdac))
* 通知消息重复的问题处理 ([509e51f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/509e51f961f041972da46f04aae4869f716b772e))
* 同步登录跳转调整 ([4373209](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/4373209002f03d251bfad0d989df1e4573f903ae))
* 同步方法函数 ([3b58796](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/3b587969c92c32f29ab534329c14a7f9b92a34db))
* 同步更改 ([b3c325b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b3c325b41c4c13df19e7e5c57bac310aac9df1e7))
* 同步更改用线上umd渲染 ([84b5f75](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/84b5f753de8ab0aea00ab4c885db469f5d95e160))
* 同步更新socket ([39825eb](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/39825eb915c0437691512389051cff075da61b2a))
* 同步逻辑 ([b56874a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b56874a8f6c116cfc355bcab76bb2499f983be96))
* 同步配置 ([dadf360](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/dadf3604a4f1c7a6ae8a7b5f6c4aa132c154eab3))
* 同步人员选择的代码 ([8d8f8d5](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/8d8f8d54fa757f188407f7f7d4b47fa568a67288))
* 同步拖拽编辑组件 ([2ce19c0](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/2ce19c05798250a9755e33244d0e177d8beae5ee))
* 同步文件 ([c55c54e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c55c54e66115d8b25ca06e969d8656ab56779239))
* 同步文件上传 ([947252c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/947252c6a1f34b0b001204d3d1d338b9405969d3))
* 同步小组件开发 ([38465fc](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/38465fc616621490c3c20dbe61182531656c659c))
* 同步租户端配置 ([8d7df53](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/8d7df53525faaf72b9b3594346002b910163cd76))
* 同步租户端修改 ([3fe5a2a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/3fe5a2a9b0c20c576ec5bffc7a759f4010221b5d))
* 同步app的写法 ([1be3119](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/1be31197136b038be10354ea7ee3cf0e1fbf92c4))
* 同步APP目录结构 ([029b839](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/029b8395851b4e975f782a3ef8ed1e66d88602f2))
* 统一菜单背景大小 ([ae05c2e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ae05c2e56e8b234b0eddc978d2b201dc4e582394))
* 统一参数接收 ([569680d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/569680d7c83e0934097959d70abff3a43407b722))
* 统一处理电站树的加载问题 ([f03f232](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f03f232aaca1b0308267a3296fc2427c96598410))
* 统一封装组件 ([14ff6a5](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/14ff6a54af155ec9a85d68eaaadfa5d255f7f898))
* 统一更改边框变量色 ([365b40f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/365b40f171bf4faf740d4c13200a9cde294c548b))
* 统一环境变量 ([e358776](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e358776ee06fe7d5e27c9b792fc4c4c8d28d7a09))
* 统一禁用颜色 ([c5ca15e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c5ca15e59dd701e1b0e1a41f697fb97742b100c9))
* 统一色值 ([8d14bf8](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/8d14bf845cdc6717bf5cc043e07b20a692010062))
* 统一色值 ([29517b4](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/29517b423c28cd6fa2250b6a2a6a74df57c7832c))
* 统一样式 ([f8341f4](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f8341f486133edeeab88301933f1a4350aec30b1))
* 头像 ([00d83f3](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/00d83f3f03dfdc7f5e9835abbeb8ee234799f7f9))
* 头像边框 ([d880af1](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d880af1e0476fe9c3442c574b12090f2d5e0e2f1))
* 头像调整 ([9f4cf2d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/9f4cf2dccdbf48fabcc7f9ca091ce9dcf520830b))
* 头像请求资源代理环境判断 ([40eecd5](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/40eecd58f387914e3f654045a7e9e95f155c4153))
* 头像取全连接 ([41fbd13](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/41fbd130efe23d402ca3262ddfadce12f2f3dd7b))
* 头像样式修改 ([7f6faf5](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7f6faf5cbd971825c7fec84bc1adb27521e181bd))
* 头像bug修复 ([85e1d4f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/85e1d4f086f9dd19592b783cac780c593637fee0))
* 图标冲突的问题 ([238e8d1](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/238e8d1b04a38c1f8f4efb5cf5824f6f24caca7f))
* 图标调整 ([b076eec](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b076eece8b57528faad8271f43b16465dd0451db))
* 图标更换 ([74bdd09](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/74bdd092a11ec1862a9154301cf20810b2900379))
* 图标距离兼容 ([30c851b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/30c851bfda44fc747defebc8576ca6f225a464b0))
* 图标快捷 ([ade7a38](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ade7a38836a4c5020524465c5d59ebf6c2bf2576))
* 图标没对齐 ([a6d8680](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a6d8680151b1fda30c762770dd46e04530978dfc))
* 图标未显示的问题 ([f70961a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f70961aaee82cfbfafbab300975d3ea1fe0a30e7))
* 图标显示异常 ([466ef51](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/466ef51d90f38ba57ae301aa7357b1dfe8d6b1a5))
* 图标修复 ([50e7cf2](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/50e7cf27da738d119c9f9b2648b3599789a19c03))
* 图表保存操作 ([d91f08b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d91f08bc4da34eae8aef53037cceb3db5ea91cca))
* 图表联调 ([78371fd](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/78371fd2fcb1a85d7688fe2c1d4cb0f072d6a5a9))
* 图片排序 ([b3fb0dc](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b3fb0dc532aa710145acdaf5c3aed4113cf08bef))
* 图片上传 ([a7b3ab9](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a7b3ab99e68a03aa74cf3684f43d2c75883c5ad5))
* 图片上传 ([59161ad](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/59161ad48bd35f2ff549d2c02d1620c9c32cda7e))
* 图片问题 ([aa666bf](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/aa666bf6b40c04fa0d83be7fe90a3c47f2ad7339))
* 图片显示类型判断 ([a3b12ef](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a3b12efd08ce30c7a1c19c5a12de1a5d88cb80a9))
* 图片引用报错问题 ([12fa663](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/12fa663193845ff1edbd1dac4847acf39201f6ef))
* 图片资源动态 ([a8ddd57](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a8ddd5730b222a2244ad803dea8540a4f5946b2e))
* 图片资源根据主题判断 ([4ea3f57](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/4ea3f573702e7d134627ab4b43d6ab0d6f2d1cd3))
* 推送 ([7bb886f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7bb886f8c389f8c0097c95018a94ec46186f7498))
* 退出登录带上当前的租户id ([f93887b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f93887b40063bb1cd2f8f2106c3b1899eedb6bfd))
* 退出登录的时候除了白名单，清除所有缓存 ([533c6a5](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/533c6a5f0dd5d96f6ae426c4ad21c88eea12b13e))
* 退出登录判断cdn没有的情况 ([7cde638](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7cde63840572a5d740345bf38e8a3a72bbf9b80b))
* 退出登录之后默认头像的问题 ([39cb94c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/39cb94ce9eec2761861069843c3cf6fec437fbac))
* 退出群聊的优化 ([623b63c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/623b63cb3476ff265d0d43ee47210252b802dfb5))
* 拖动过程中增加遮罩层 ([65c9fc6](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/65c9fc6c6d4a0fea937a3518897d3eb986466911))
* 拖动交互优化 ([f092548](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f092548059f0d556e0316cec87b306a25bf91bb8))
* 拖曳 ([7717630](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/771763012d0b721911c4f614c928da5f9762c239))
* 拖拽调整顺序 ([ecb3ed2](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ecb3ed223e64070cba9de6108f330b680001d33d))
* 拖拽方位数据结构的修改 ([d029ab8](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d029ab88daf35a3e34955470156ffe31fb1ea24f))
* 外部标签 ([2fd5324](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/2fd532475ff5fb53ffe2e2f8a1749335709d76f0))
* 外部系统链接跳转转码操作 ([c2ef020](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c2ef0202c83407b6d49ba13a507aaab0bd9ba136))
* 完成备忘录的优化 ([4f699a6](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/4f699a6d9846fbaf31c69d97011113470b1ab689))
* 完成备忘录联调 ([d60db98](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d60db98e2123d30882200efc959229ef51fc440d))
* 完成item的封装 ([55832d6](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/55832d61eb04a6d83afecffe5b2efd3eac80b27e))
* 完善大屏首页交互优化 ([5b5fbd2](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5b5fbd2a2df94e454e4c02a5859083f2eb0d76fe))
* 完善基础配置细节增加提交验证 ([86eedbe](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/86eedbe7569edad8d57ff100d3f1e20dc2e81052))
* 完善ai入口的动画 ([7e27883](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7e27883550f825d7e4ad0b1e0a41311e2309b98e))
* 网页刷新之后路由菜单的匹配 ([494fb39](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/494fb39a818c96ee7f9129d793fb3a9994ea041f))
* 网页重载 ([0b9f755](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0b9f7554c9ae6bc28f0a201f77dfb26be1215dbd))
* 往来单位的弹窗统一 ([2d7de56](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/2d7de568eb49d1a3a40dcd3798ee2627417b7a1e))
* 往来单位的修改 ([04303bd](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/04303bdad260d360b3b12865ebe492b77d017ed5))
* 忘记密码调整 ([337d54c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/337d54c157f11e2d15e2b809c52551d11ce05138))
* 忘记密码样式调整 ([8267e78](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/8267e7865c59c74fd4ad55336ec310bb7af68beb))
* 未关联组织时的处理逻辑 ([cd56d8b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/cd56d8bb1a76fc537e285ad6b04cb3833a7535db))
* 文本修改 ([3034437](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/3034437b1d4444c0c9af4b51c5854937956adca2))
* 文本优化 ([05ee9e6](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/05ee9e69af8b716afc92ed71451e5d2b24b0141d))
* 文档 ([28807c3](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/28807c3b352cfaf9ca813c5c27fee77f2ff88bda))
* 文档大小限制bug ([c7d2715](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c7d27157aaa2c2c84e58b922839f89854c629b87))
* 文档更新 ([d6fd790](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d6fd790f687bbc40bdc98b0e143bb0e1358eac18))
* 文档解析 ([0f5e21e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0f5e21e9ac9e64f407bf1d295685837a0009eca8))
* 文档来源收起操作 ([2012721](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/2012721c2d2989866d7e25b32e4341d59e62cccf))
* 文档搜索和布局调整 ([7136d84](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7136d8410ebe4e311acc09321265edaf48905cd0))
* 文档跳转 ([b28051d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b28051d244b683ddee894898cb28d0e11de4320a))
* 文档跳转处理 ([f4f9a3d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f4f9a3dcd4a3138020a1cc5003a4b453d96e2987))
* 文档图片问答 ([0a7771e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0a7771e9a44d49d2e4dacebdabbf69a14055c2ec))
* 文档预览链接兼容 ([52d2da4](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/52d2da456bba2ef534edf4932c10f5e73376fa26))
* 文档增加操作权限 ([c86c2ff](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c86c2fff54d6a02241836cc9b69cb223d88e3f2e))
* 文件回显的样式调整 ([96b5cef](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/96b5cef505eb7ee1407e86c5f0eddc4f4d379e08))
* 文件夹bug修复 ([ec8dac9](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ec8dac9504968ee53f4e4ba4959885e4f9e65b43))
* 文件删除问题 ([a4f39b8](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a4f39b8d600d56737fafa4767036e36c1ce8f74b))
* 文件移动成功以后弹窗关闭 ([fc89292](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/fc8929229551b15a54a87c5ef6b4290cac4ab749))
* 文件用印初配置 ([f4fb32d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f4fb32d08bd5d2d6ff2e954ca8b3d12d055736a4))
* 文件预览 ([13b0ccd](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/13b0ccd83351228543628981079a5d769d06dbb8))
* 文件预览 ([939a738](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/939a738da9af3e718f1415b8de6f02887e67b752))
* 文件预览大小限制判断 ([60d9214](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/60d9214802e1350b23b8dc431aeb368472239a2b))
* 文件预览地址编码调整 ([e7d63df](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e7d63df95f2ed7bebcc0b6202bad47ab599a6d54))
* 文件预览调试 ([da32ced](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/da32cedfdf60ef07c270ea85e1f35149098f0885))
* 文件预览工具地址修改(新的) ([84ce046](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/84ce046b7af92a55d05ae63e5c22dcf6ec7a6d85))
* 文件预览下载 ([fca6610](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/fca6610ee58c49e1614cd231502f048ff5a34fd1))
* 文件重命名处理 ([48e826f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/48e826fc14b8a0f97b13ed692550155ecefd7709))
* 文字复制 ([1805351](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/1805351a34309a285e7b5f39859af50f0b4cbfae))
* 文字修改 ([97557fb](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/97557fba4009b71a202fd9ee11e8a459d085f881))
* 文字溢出 ([94e5aa3](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/94e5aa39953c9ba00acaff2603a8834e2e668d22))
* 问答bug ([6e10644](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/6e106440a4f79f96f6a0c4e7c2d2f8c1e78e54c2))
* 问题列表优化 ([fa20f06](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/fa20f06753d00a5b06746a8ea576a6680715f063))
* 问题修复 ([690fb7b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/690fb7b4a974b4e9265aa471372039b0172f3f8b))
* 问题修改 ([ab9aeb9](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ab9aeb96c5efd8c1af20dc29c9ee766c85518943))
* 我的空间表格样式 ([50eb7f4](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/50eb7f4374e2438f0875a14977ec9cfc89996fb3))
* 无法预览的问题 ([eaa12e5](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/eaa12e5efa91fd804034cc0f62740d67f66a8fbe))
* 无界初版 ([a0b3860](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a0b38609aa297f52d5eb3566d90b97b9f00b3c95))
* 无界初版 ([5c4de3f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5c4de3f52d488bfd8cf1390b886d221f3825c25c))
* 无界配置 ([8edc63a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/8edc63a31d1edbe68f04e9612137578eeeab0356))
* 无界预加载 ([e482fc1](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e482fc1704dcce3822585f314e9bd07a2db6f5db))
* 无界暂停使用，iframe做缓存 ([7428ec3](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7428ec3fb34faec2581d9fa768fe534761aebe7f))
* 无人机事件查询 ([a18dbb8](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a18dbb8f3173a9bae8ef0f9ee01ad236d14ac511))
* 无im模式socket通知数量更新 ([4819185](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/48191852587c604e3d00f851e0add34ad105dffe))
* 物料设置修改 ([f15739e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f15739ebaedd16d0016d90e04a9581b1ae40a747))
* 系统通知的工具 ([a7be31e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a7be31e662b88f621f5fbd23cdf0c9ff0980cd04))
* 系统消息调整 ([8448c7b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/8448c7b977af0487a5b72ad38371c68e853c3922))
* 系统消息禁用状态调试 ([15e05a8](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/15e05a853dd7d824bbaabe52846c123a516288df))
* 系统助手模版消息处理 ([19dfcb8](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/19dfcb8c80292a02ab5fa9ba9de8f0988cb41f3b))
* 细则指标设置 ([c07116a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c07116acfa5fdf478792abe890204362cce38348))
* 细则指标修改样式 ([9377e9e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/9377e9e0c00d943f4e74e75c85a1f173cb9b28ed))
* 细则指标修改样式 ([2fe72c4](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/2fe72c42291c4fde68d1d949644e49a47c82e7d9))
* 细则指标修改样式 ([ed21bb6](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ed21bb6e2980122535934cf0ad8259895cc76dbb))
* 下载的效果交互 ([7c442be](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7c442be707a0910829dd6b65a3b064d7c7842905))
* 下载交互优化 ([6bf9276](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/6bf9276502c4f6b73f2869d7d7728000f45e51e5))
* 下载接口换成axios ([8141f16](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/8141f169edfd8d5aa29d4b7167ac48925cfd329f))
* 下载链接处理 ([b18a22b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b18a22b4ae1a26fbb829b2e658327b690aa21514))
* 下载优化 ([9d0d36c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/9d0d36c9c56cfa6d26edf012f779d20833cf37b0))
* 下载预览 ([8a4fa3c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/8a4fa3ceddf55f9c33a720b67f6dd53f65326bd5))
* 详情编辑弹窗支持单独打开 ([7dddeb7](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7dddeb79496bb86651eb6cb2794874ea38245386))
* 详情里面的状态 ([0896830](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0896830570994d051d3de3aa6206796d78963424))
* 详情跳转 ([6b22e13](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/6b22e13ef40f37a17b6ca52c4d1f22cb49bbd02d))
* 响应式布局 ([65de127](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/65de1277656fa89a66560c92f7ec04d4e32dbff5))
* 响应式菜单 ([491f5ab](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/491f5ab359e761171c800e3fd9989b67f4392668))
* 项目选择分页变更接收参数调整 ([1968137](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/19681373bed02a4b17de0cc4829276c9c1fd8fab))
* 消息滚动到原有位置 ([cf58fc7](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/cf58fc7e35a820120df017654f2c3fc9cdf617da))
* 消息记录对接 ([e2c6c8b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e2c6c8b1328d9c6fcfa0fff79ddd588d18bd15b0))
* 消息界面优化 ([3c8f934](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/3c8f9341930c5563964ccce6dadda801606a4adc))
* 消息卡片展示最后一条消息 ([d99e80e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d99e80e0af1788c8ec427727ceab67e2bc2b2613))
* 消息搜索改版 ([b75bf50](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b75bf50c9c3495beae9f2fd2456dfd0130eed30f))
* 消息页面缓存 ([762f44a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/762f44ab20f9aa82b08464f5020643bc6a93f228))
* 校验 ([69caf0d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/69caf0daea1169e5d484f9989b2feb7169cccc88))
* 校验代码 ([0dfb424](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0dfb42427dcb8e31293b4f6eabe2bdd4c9f0d074))
* 校验是否登录过期 ([2617311](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/261731128f2bbb94c88470b338ed0b0f2d3d8690))
* 校验图标穿模 ([bc6d198](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/bc6d198898ffcf2579b0638019e60434b89adbe2))
* 携带参数appcationnId ([e600570](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e600570ce49a38e9bc8181a7efc981e598420139))
* 写死的数据删掉 ([b8571fd](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b8571fdfb7d7c385b6b79e6cd499d66cc07d5b7a))
* 写死的cdn配置删除 ([f961bc0](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f961bc0d9b331179e2f829069aac123937df66b5))
* 卸载CDN ([e418192](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e418192780237936b08aab7be8121f768a6ad607))
* 新能源菜单模式的打开方式兼容 ([de123f0](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/de123f0b3469637c2de44927f93541f0e05f7d7c))
* 新能源定制演示代码 ([c46efb5](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c46efb568756a76c7a12ed8eb340bfb10b147c61))
* 新能源配置socket ([2e26b8f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/2e26b8f301f2444d2f5dc60ef92cf10f5417ca2c))
* 新能源外链跳转需要带上token ([cbab7a7](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/cbab7a72e1faf897c2dd3578465a074db9f89622))
* 新能源需求全局修改弹窗 ([2b1e05a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/2b1e05aa56ec8b6b831cf8cf46468429c868ec11))
* 新增备忘录 ([26aca87](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/26aca872d8838d0668629015d576f73d1d7aa2e8))
* 新增弹窗 ([a6e301d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a6e301d9dc1447b5def6c119f3429c823808ed54))
* 新增分摊金额 ([6ba867b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/6ba867b781b20e8aedac46e79dee55073b6af98e))
* 新增供应商变更撤回 ([1bc2cfc](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/1bc2cfc3596858a37b128bd05a368a158ba3ee20))
* 新增加token的有效期 ([56fb5db](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/56fb5dbaa798206790b7955d9e7403af61db6e72))
* 新增接口 ([f5e308d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f5e308d014356944da7ec74cd12614bbcdde60fc))
* 新增科研成果目录 ([28f0fc5](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/28f0fc56a9636cb73b30e41a8b09b38cf9a810a4))
* 新增客户管理导航 | 供应商优化 ([a606812](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a606812826cdfe829eb33ac8ec138983b4f1ff59))
* 新增默认图 ([36d7dc8](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/36d7dc809c612900c893d1f2281e48c1b10fae9f))
* 新增日历报错 ([13d2333](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/13d233397024ac66799245cec221c0f2a5bcbf77))
* 新增消息发送按钮 ([a9a3998](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a9a399862b76d01056dd55b6e21261751229b0cf))
* 新增ai快捷入口 ([53c8c9f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/53c8c9f8a3eca4777ec180988208f9b94d7b3e4d))
* 信息档案模块， 车辆信息模块，弹窗处理，字段处理 ([c6bb33d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c6bb33d9b3bc6e346757f4d619725b8fdeb7a02a))
* 信息档案模块，培训记录模块，合同管理模块弹窗添加表单是否需要选择下一步办理人/部门。 ([f71cc91](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f71cc91a258e770a31f9332511910cd48feb60ed))
* 信息方案列表页面字段处理，弹窗字段处理 ([7579990](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/757999074b22b445777fc6ae03f14178848c1d09))
* 信息管理 ([2061bb2](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/2061bb2f1faf9be34b1470008518ceccadca757e))
* 修复菜单定位 ([3f0360e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/3f0360e4099b2bc102ca8d5d6c69407fd4eab822))
* 修复菜单四级样式 ([9999fe3](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/9999fe3d0ecf1ec4355ceba3ba12e240a2c158b4))
* 修复出车任务tab问题、修复驾驶员备案详情附件不显示问题、修复提交按钮问题 ([b6e3c30](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b6e3c30993a11b44c4f7088ada36969a5fe7fe4b))
* 修复单机双击逻辑 ([f79f7bd](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f79f7bdd0cb861c36157f348e6e370d1afef91cb))
* 修复顶部菜单布局位置 ([b30b230](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b30b23068a1ac9be22411e1d099765941c1ce4b6))
* 修复多个错误提示的bug ([e025ca7](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e025ca7a12fa0b940826834f1bf5399c64bce3a1))
* 修复回显 ([7db343b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7db343b9888f9172d4af5a8cb9e32d09e9dece71))
* 修复判断 ([3b87b86](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/3b87b86afb2d996348518d37c8a9f26af15ff8d5))
* 修复切换角色/租户，应用未更新的bug ([20ea180](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/20ea18050e48267c23a6bc9d6d393078675690cb))
* 修复日程备忘选择人员时能选择部门的bug ([94ae380](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/94ae38018121316ac500f817b1673659a686524e))
* 修复日程备忘bug ([4e2115c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/4e2115cbeffa4bb287ce3854064bdacca7786bf9))
* 修复日程备忘bug ([a8a0845](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a8a08454f33d3101554c581de4771c6cbd47d91d))
* 修复三级菜单及菜单收起后的样式展示问题 ([3dd91c0](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/3dd91c0bccfa7a59e492a404d6cea56705922199))
* 修复删除代码导致的bug ([424e75b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/424e75bf85426ed64d966b62c7e2ddf5c431ce97))
* 修复数据覆盖的问题 ([086a2fd](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/086a2fd59cc1129d80bc0011c57bf02c08cf1aff))
* 修复跳转bug ([769e729](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/769e729906a9db3087ba8e033ed40bae010fe22c))
* 修复渲染bug ([f185c19](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f185c193204ac7d035fbfae3cc8a15be178db64c))
* 修复巡检管理新增电站问题 ([a854b14](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a854b1426af3666737e3da95bfbe65e2a0fbbdfd))
* 修复样式 ([8245330](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/82453305aa0ab86993117387162a97723d9c84b7))
* 修复样式问题 ([f6ca0e3](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f6ca0e320168d78cb119ea15ba2f62423a56cd1d))
* 修复样式问题 ([53495e0](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/53495e0b1c416dfc147bac1eb94b78498b532282))
* 修复应用和路由定位回显 ([f72981b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f72981bbb88ae54d0a871e374cf906b868a566b3))
* 修复主题色初始化无效的bug ([c22298e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c22298e1ffe57e740c9ba3fbf489b93ed1bc7739))
* 修复主题色初始化无效的bug ([f57a83e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f57a83edbc086b1b6ec010d1f189172cd0aff267))
* 修复主题色切换时的样式问题 ([2ee5c9a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/2ee5c9ad25ef6b4c93c14df39c1ca84794f44c7e))
* 修复主题无数据报错问题 ([59e591e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/59e591ea377d8ad7f45d8a2b3241118a3c6d4590))
* 修复字段未定义问题 ([04c41f0](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/04c41f069457f586b4866b8d42bc8638570860c3))
* 修复组件库样式符合coosUI设计规范 ([d2c7122](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d2c71228cb664ffbfe92c7c06525a2cd938ea62d))
* 修复组件库bug，优化组件，更新所有分页组件样式 ([8197353](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/8197353d623a648786cf11476ca4def3b33e0cbc))
* 修复bug ([86c6b21](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/86c6b21c6e05eaf5513f5e2cbdfa102259a46a36))
* 修复bug ([390d906](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/390d906718f9fce00f6b73161ae21ba32ef2d059))
* 修复bug ([c328053](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c328053d056884b06c09c2e80609b198455f3a5f))
* 修复bug ([16cb8d3](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/16cb8d3b28039ecc04f1898b0c2f0069c7e603cc))
* 修复bug ([651fcff](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/651fcffecef9826d684632333732963112f9f082))
* 修复bug ([f64e353](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f64e353d9bd1245a32930c19bbcec02ca869b997))
* 修复bug ([21cbc14](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/21cbc148e1f3276bdc1b883d9b63eb8e09581f69))
* 修复bug ([3d508cd](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/3d508cd850e51184a4bed439bf21699f33822183))
* 修复bug ([cea62be](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/cea62be0692c43c2bd03ba7368409a227f27d36d))
* 修复bug ([25ed6df](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/25ed6df9d154168b4302801f0f17943fc77b46bb))
* 修复bug ([c9e7f7a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c9e7f7a92a8c7e9bb647d7d859e9a49fe2c3d008))
* 修复bug ([7185797](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/718579794b85f4a1a7c3e1c72d9f430a14658f9b))
* 修复cdn地址拼接 ([745a7d9](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/745a7d97ccc0fb12e01615c2f33872ed81ed08f2))
* 修复im消息bug ([44fa190](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/44fa190566e2efd4bcaddb306d45279197953efb))
* 修改 ([d3dc54f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d3dc54f4e356c86603491024b76d9821e856f108))
* 修改 ([9e825f1](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/9e825f1089eb82d7a421404c005b8bed37b03d4e))
* 修改 ([6cfd617](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/6cfd61727c2a89fff90454792532486d3951fd9a))
* 修改 ([3e41991](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/3e4199173b9e8e447c7b6e89c8565704f30fa310))
* 修改 ([f10b08f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f10b08fa3ccf1b171b4a97ea0cf7d8d0600eaabf))
* 修改 ([c5eb281](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c5eb281c147400d78eed693bc56ff141421a6b24))
* 修改 ([7e5d524](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7e5d524f42aeb7c7e469089ea0cba556e85a0c95))
* 修改 ([0eb1b5a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0eb1b5a3fea9e557142d55075499be1c9d94a920))
* 修改360浏览器选择合同后无限拉伸问题 ([0eaf6a8](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0eaf6a85bf4650202d01555ee12d97b55935ca1b))
* 修改安全指标上传组件第二次上传失效问题 ([e6a3965](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e6a396583d5f14f25bf7253ebcb9cc82e49f57aa))
* 修改版本和名字 ([c15892a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c15892a40b06ae5b6d60969f1b9323f80bf7fc6c))
* 修改办理时候的意见 ([faca4d5](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/faca4d58f274ee520c2a4a568310872db63d37cd))
* 修改标题 ([825d06c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/825d06ce3ff6e455b86afedd17c1263e7c3b148d))
* 修改表单列表超出展示问题 ([6a2cf6a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/6a2cf6a4acb188d2d6bd57ee2906f706bc4c5ea8))
* 修改表单设计器的级联组件 ([909b667](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/909b667d4d84746e242797c92359eecbaf4eefea))
* 修改表单时间展示问题 ([5831e4f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5831e4f3e25846239ae33099018c4590869542b4))
* 修改表单提交时候的验证 ([5620379](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5620379a0f3b49366b8da5426f8d8570902959a3))
* 修改表单提交loading问题 ([f0d6eb6](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f0d6eb6734b3f188178dc480cb8adede334f9e4b))
* 修改表单新增details问题 ([781c4ce](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/781c4ceb784946f210c59e1d67d80a5ea7cf2eee))
* 修改菜单bug ([d85f65d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d85f65df8117fb37a98b9dc50ac224850ff7cef4))
* 修改参数问题 ([ecc3f4f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ecc3f4f22df6b55293b9f5c7506258bd97084ae9))
* 修改侧边导航没有图标问题、修复侧边更多导航没有删除问题 ([64223fd](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/64223fd04d539a4413216a106fa3eb2a88266bc2))
* 修改抄送的办理 ([ec7846f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ec7846fbce11f4b72a20e478e976eae5582942ee))
* 修改抄送的办理 ([9219e24](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/9219e24cbb5fc643ea699746d0e6151d8c18deea))
* 修改车辆台账车俩图片不显示问题 ([ccf2bab](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ccf2babfaf4d170f74581b47ded68ea09258b59d))
* 修改车辆台账搜索展示问题 ([328c4e7](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/328c4e703df23b7b1d0fc4e936006d3ab6facdd0))
* 修改车辆台账详情列表展示问题 ([5cbac0b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5cbac0b7163788999bfb60d2445fac1e56c7bc70))
* 修改车辆台账详情展示 ([082d95e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/082d95e81cb8fcde50f7f4a472ca87d80815d67a))
* 修改车辆台账展示图标 ([3f031b6](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/3f031b63ccde4373cfda189fe6e6a2d651528aed))
* 修改打印参数问题 ([81d3e71](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/81d3e714acbb25df8dfda13c3fc19d232859470a))
* 修改代理 ([849d71b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/849d71b842f718ceb88f87a189a395926354988b))
* 修改代理 ([6a16253](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/6a16253f53e11aa721bb2038f3f8a07079499253))
* 修改代理地址 ([01c42d7](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/01c42d79ae124349b404f86277e759c3cbce1a58))
* 修改代理地址 ([f091e2f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f091e2f3c0e2732eb0fd99a03e7be886a79bee67))
* 修改代码 ([0dcfedd](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0dcfedd5f7b5ccabed3d1c2230ca743a01827000))
* 修改代码eslint警告问题 ([d909529](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d909529d8c83515c614af602e4b3c08a9c6ca4f3))
* 修改带派车办理弹窗问题 ([1550813](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/155081330de638b5f2f28b43706c44b5bafa20f5))
* 修改待办打开详情 ([ecd0171](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ecd0171784351fa654fa9dcca4fe49d881c631d0))
* 修改待办控制台报错问题 ([9b2da1e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/9b2da1efd26ae6daf68e437077cc505729388f3b))
* 修改待办列表请求问题 ([44ab2d5](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/44ab2d5e36b876f748431190f64b40f245b89949))
* 修改待办搜索参数问题 ([82eda4b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/82eda4b686137a6483015f19f3eb96d42415c3de))
* 修改待办文案 ([7c01511](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7c01511f6aac23b3a2c0fd93288ada13a12b27ca))
* 修改待办显示判断逻辑 ([7ef6df5](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7ef6df5c5f9278d6bf7496cd8b042c37fc47d1d7))
* 修改待办显示问题 ([b711322](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b7113228beb0b2f6a39e1de5cc07ed14a7d7dac4))
* 修改待办选人不出来问题 ([78a4adb](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/78a4adbaf31b7eee3aedd284768ab612002be406))
* 修改待办以及办理结果重复的问题 ([8176f1a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/8176f1af59d2dcbc91045be2f3f89651be07ea79))
* 修改待办用车申请详情展示问题 ([f77ee07](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f77ee075addbc3272f1c9654ce887b7a858cbebb))
* 修改待办展示、选中回显问题 ([3f14074](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/3f14074cb9e2dc4909db45bf8ca181529742a1e4))
* 修改待办中的有外部表单和内部表单的问题 ([06408c7](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/06408c77cc747d17f5905429e627614d9bc9f66a))
* 修改待办中的有外部表单和内部表单的问题 ([2fa6954](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/2fa69542329ea4a2eee4b65afbf3d5bd0b9c35a1))
* 修改弹窗详情 ([49834fe](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/49834fede9180121af457ef17fc1c4f0c4ac7bd5))
* 修改弹窗详情 ([b952174](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b952174191f2df635eee95b0ce2ad2be7a28acae))
* 修改弹窗样式 ([56449ff](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/56449ff1a44b16f2dc21f71307fe5b4d0cba967a))
* 修改弹窗样式 ([88acee7](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/88acee72a652e1821a3f4e945939094b73f1dd17))
* 修改弹窗样式 ([929034a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/929034a1fbabf04023c9d6b8e9a17d4fa9cfd128))
* 修改导出图标问题 ([fc45d1c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/fc45d1c809464d7723832a7b63560582e3950a52))
* 修改地图 ([44f9a18](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/44f9a18c0747d36bd8bd11eb2336f9a7962cdede))
* 修改地图组件 ([49c41fd](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/49c41fdc87f50e042428314e11dd1132dc325694))
* 修改地图组件 ([6ebc740](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/6ebc74031f0b2262ba4156e196490cbcbf875cb5))
* 修改登录弹窗问题 ([c4500be](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c4500bed927c0fdbaaf48bc9f2d14ff19edd314e))
* 修改登录控制台报错问题 ([14f66a5](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/14f66a5cbd10c2d355ae1baa68491b06afd786bc))
* 修改电站导出名称 ([8638a5c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/8638a5ce0f06bf12b3f7b34d204fe273c782a448))
* 修改电站配置非空提示 ([7bdfc36](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7bdfc3637bceb2dba85de86b6b46dd35e0f092b2))
* 修改电站配置排序报错 ([f5b1f5d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f5b1f5d1816cc699e4aeb104c4ada1bf5b1c3a12))
* 修改电站配置头部样式 ([1d32169](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/1d32169410908b77453f4c524e573abd3f38356e))
* 修改电站配置子列表展示问题 ([0469b89](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0469b8993c40d99db8d5b5c10fc1734a068039d0))
* 修改电站配置组织展示问题 ([e7e0deb](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e7e0deb8821316bb08628a34b8d7fdd1cbbe4ffb))
* 修改定义到原文文字 ([cf12b54](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/cf12b54ee06001742c6ae03f4fa5a8b69a94ac59))
* 修改动画 ([d92522e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d92522e68dfe61803928baa7f9db8a42efc52bc9))
* 修改对话弹窗长度 ([e923fbc](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e923fbc43cdbbcabfa6433c66f13e2f632e5a544))
* 修改对话逻辑 ([604c238](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/604c2385c0180f3b36af6b4810209b6299a486a8))
* 修改访问地址 ([a692f9c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a692f9c7d4d71808e9e86951524c2b7332f934e7))
* 修改高级组件的dataSourceType为4 ([99bd601](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/99bd601e2ba879bc9180ddcf03f7c8910b300c68))
* 修改工单 ([932e3bf](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/932e3bf746fb13ed1c4c0d87972595ff9f36d6a9))
* 修改工单不能新增的问题 ([9f91361](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/9f913618e3c858bdfd85da429cb7441561dd2a18))
* 修改工单管理界面 ([9dd1ccb](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/9dd1ccb30a461352da5b059c617942b38b5166f8))
* 修改工单管理问题、修改用车印章问题、修改搜索问题 ([009e751](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/009e751a44017e807d7536afd480151f5875e0f1))
* 修改工单申请新增问题 ([87f60fd](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/87f60fda526c0ea42e34b931b21510fb31974c19))
* 修改公共搜索组件 ：问题 ([d1dbeba](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d1dbeba6e013f95030dacb6d88e10391ccf677c0))
* 修改公共组件搜索样式 ([dbd9608](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/dbd960820b1c48a98b5920a96906fbb0ca973805))
* 修改管理页待办问题 ([897a389](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/897a3894b767a2a9f14e621db9dda3664ac97a18))
* 修改合并冲突报错 ([cbc9bdc](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/cbc9bdc8b280d5a88623b3fb8a74e316caddc196))
* 修改后端接口前缀 ([a21b1a0](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a21b1a00e750bdd3e5c632753fc3bc331ebf1eca))
* 修改机器人助手tab跳转问题 ([f274944](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f274944c7463f76c3c76e2383a7d4225414a6929))
* 修改基础配置bug修改 ([7af097b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7af097b56b77dcb45a4388abf9ed683c6272c1b4))
* 修改基础设置样式 ([982f96a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/982f96ad29ae759e7a3fa25051aeae2ec90ed0e6))
* 修改驾驶员备案申请流程参数 ([4bf345d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/4bf345d5564aabf4e2fd88a09cd5a10655fcc279))
* 修改驾驶员备案提交参数问题 ([2f7acf7](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/2f7acf78930158813e66e17ccd571b7724ec5257))
* 修改驾驶员申请流程 ([3c7362c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/3c7362c4be82eff4ac2dc0857c835da25129d8a1))
* 修改检修停电搜索样式 ([16eda3a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/16eda3ab9ca01d6961a9b223f872be986712fb8f))
* 修改接口问题 ([a55b1f3](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a55b1f3b52bd92bb526588e36b7facf44e7f0e0a))
* 修改节点跳过展示 ([df89fcf](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/df89fcfc47dd87599c0664ce70aab64f7e6ff81e))
* 修改节点悬浮显示问题 ([22ec455](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/22ec455d33d2a8d70eae54e7e748be24d5502411))
* 修改结算科目详情展示问题 ([55103e6](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/55103e60a5ebb443744e62344edaa1b83cf417a2))
* 修改结算科目展示问题 ([5d68606](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5d68606b9c8be2b52f7fb5fc5e3126fec22f54ec))
* 修改结算信息样式 ([d505034](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d505034a41c729e7abd93ef10822f3bb594f8316))
* 修改结算信息样式 ([9c24a03](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/9c24a0300526b83a686c7f08f6bf95d41edf32dc))
* 修改经纬度浮点数提示错误问题 ([1a3cbdf](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/1a3cbdfa2d39f8b1b50334a0bb1ccf3bbb2a68bd))
* 修改开关展示请求 ([4c5a8b9](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/4c5a8b9257f8f4865a20ca1b121b1c6ac480994a))
* 修改空信息提示 ([6cb9e44](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/6cb9e44081970223a477f13e333ff80a9365dad0))
* 修改框架的交互逻辑 ([8e9ad27](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/8e9ad27b49f067b89a0db931da8ea303e5d785f2))
* 修改聊天记录文件展示 ([0b769e5](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0b769e5b0ea95326fe014895913e7434befd6ef8))
* 修改聊天记录问题 ([3a584ea](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/3a584ea76a0c90a5de63f67ae3b45961eded640e))
* 修改列表问题 ([8792b2f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/8792b2f4e91c4ac5529c9ed4954e1b62adf49c0c))
* 修改列表悬浮样式 ([fe39b52](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/fe39b52d105fef454d297b2525677ac9dae26689))
* 修改列表展示问题 ([67190c5](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/67190c5a6834dbb415967bc5bdb75c6fcab74d46))
* 修改列表展示问题 ([076ad26](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/076ad2604308da9cdb701f99ca122f01e6953360))
* 修改流程接口 ([7927149](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/792714930b23eda3e0e2d53206821e0febb01d19))
* 修改流程节点待办展示问题 ([71cbd5b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/71cbd5b050509460cdf8f333bacd8aae248bab79))
* 修改流程开关判断 ([f5b4ae2](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f5b4ae27d04aa26ccab41f1aa69899aa74295da8))
* 修改流程上的一些小bug ([e2c9ae5](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e2c9ae5a5dd07cd0be8a2fde5488353deaddb65b))
* 修改流程图高亮问题 ([acbfd3f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/acbfd3fb8ca5fec3a933797c380f8810cfbe5cfd))
* 修改流程图和流转记录上的时间显示 ([1e18014](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/1e1801452ae52203ed60239947c699d290ed6717))
* 修改流程委派执行操作参数、修改ai审核展示问题 ([26eddad](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/26eddad3195edf18e5ae8e3abdafdf986755c9cb))
* 修改流程问题 ([53f303c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/53f303cc19276ea8543459229640dd0ed69e5efd))
* 修改流程问题 ([ee0aedc](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ee0aedc417dcb434ae8f0ec35ce19db587f773c7))
* 修改流程问题 ([6eedfcf](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/6eedfcf63783240194c97ba6ee0f7a9b6176567a))
* 修改流程显示问题 ([7ce66c7](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7ce66c7b44298ad3b35ef2e99b4ac91a4275969d))
* 修改流程显示问题 ([c87bad9](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c87bad92f85c61ba17d4209a4245fcb444ade154))
* 修改流程显示问题 ([6739840](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/67398404839f446adda6b96b42a6d8e210c17421))
* 修改流程选择 ([08a17dd](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/08a17ddbafffa72b7e000c3db9011f04e33ac777))
* 修改流程展示问题 ([8e71ace](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/8e71acefa7468f1c1a1a2eb2d3347e04a96050c2))
* 修改流程AI节点显示问题、 ([50f1b88](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/50f1b88f9cf8934a17a1a7ca579256d4813cb224))
* 修改流程AI节点显示问题、 ([03a6892](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/03a6892dba3a5436d11d30150ddefbb4f6e3633a))
* 修改路由 ([58fd12a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/58fd12a9cc4e9127ddb815c9cbe3344746d3edf5))
* 修改路由警告 ([3a441c9](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/3a441c9d3d058ab9799c4ebac2d92a7bd73a07ef))
* 修改没有显示未完成事务的问题 ([c2594c1](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c2594c1f0091ce6a780bbc0e30aa1556d84921ef))
* 修改蒙尘遮罩展示问题 ([2c64553](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/2c64553e86068a38f950ce1846c369fc013f9ab1))
* 修改密码接口切换 ([1d928c7](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/1d928c7a6b6afab9a65b6bb7be7f598d1b615081))
* 修改密码居中 ([7e9d7e4](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7e9d7e4d0aac712622bfcd6c413f4bcd60b73b5c))
* 修改名字 ([364cc7a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/364cc7a35e8152b60d2980907079f86394baf570))
* 修改名字 ([a4c034f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a4c034f676052c4ea38b04210b00f1332c069efa))
* 修改模版图片不展示问题 ([69bd577](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/69bd577966120ceda3b12da3fcb5525557ab2f91))
* 修改模版图片不展示问题 ([e457676](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e457676f2a28b384f77b50f63bda51e2476d54e1))
* 修改默认弹窗样式 ([127b45f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/127b45fb0dd19f4bde6ab0f02dff381683da5a13))
* 修改配置 ([a2235d8](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a2235d8daa8edc7649696ed10a7d12eff59c9da4))
* 修改配置 ([4accbb0](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/4accbb0c0b800b65bb46d517f3af678a796bdc59))
* 修改配置文件 ([272ad2c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/272ad2c7cf9d13118b0ceed4198d9bcb73db3e06))
* 修改切换问题、修改状态显示问题 ([7308a6c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7308a6c79cc9c9e9da73fca5e9d32e5f23b76ba0))
* 修改切换tab问题 ([5e5d09e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5e5d09e1b3318d05c0ee36dc56c739b43bc7fd52))
* 修改缺陷管理bug ([e77d03d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e77d03d7ee832060a776b405668f160ff98dea30))
* 修改群聊侧边框公用问题 ([ba3ef4e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ba3ef4e5f84a98f9f83e210e03f9e2459f6d535b))
* 修改人员选择详情是还能打开弹窗问题 ([4a5b589](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/4a5b589620ee18e8158b39fe441fd552f106c09f))
* 修改上传数据回显问题 ([b9a0744](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b9a074420a83b6998a4701657601a21686834706))
* 修改识别报错问题 ([329d14a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/329d14af834843c5edb2c0e8a7a4bc23be8d5ec6))
* 修改使用方式 ([4d4d4eb](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/4d4d4ebd61cb707d2fe807599107803d56f294b5))
* 修改事务助手打开待办弹窗逻辑 ([a9daad4](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a9daad4fae5faa66f2f1ed2fed353bad4c304e68))
* 修改试验检验表单新增弹窗按钮问题 ([4e14579](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/4e1457941c53d8a66aa7072e335a65227629e152))
* 修改首页默认图片加载不出来问题 ([bb377b1](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/bb377b1941660685be63f74135c80b09ea8f8eff))
* 修改水印参数 ([dba25dd](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/dba25ddf491510980f4d6e3f8f6b20e730eb0c7a))
* 修改水印密度问题 ([aabd98b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/aabd98b72edc3d388e3bb8b6f922a18a9ae754d9))
* 修改水印样式问题 ([7fffeb1](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7fffeb1fc33c9d5b0606da133df3ac7f6ed08662))
* 修改搜索表单渲染的文本对齐方式 ([5309b77](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5309b77e6c182010705d6ffda15f517c012ab6ba))
* 修改搜索的布局，调整下载的动画 ([4930ec1](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/4930ec1d97f5fc5037a68edfde31836cf3e2da98))
* 修改搜索条件的queryType ([16a8c5b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/16a8c5bee45c2f98de84d4672866a8c1eb5c94f4))
* 修改搜索样式 ([98814e3](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/98814e389c301c40dca8bc58b1d8a1bfccd9617b))
* 修改搜索展示问题 ([48db70c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/48db70c79f323394e8f73b1850cff013c0f2f5c7))
* 修改损失原因回显失败的问题 ([ba9b70c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ba9b70c7b55bdef6ef1d9dad95dab7d001327d9d))
* 修改提交时间问题 ([59a4c32](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/59a4c3233a3450bf339a74b60b2dbdfa6df7d034))
* 修改提交时选择问题 ([432299c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/432299c62a9c830da1465d71eba83d2dfc1e0e7b))
* 修改提交bug ([3ff2676](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/3ff2676b4002875f52d05d6a3dece8caf969ebc0))
* 修改提交dialog样式 ([23f4765](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/23f4765b276db97c13a89ba3137456f7651c2637))
* 修改天气接口没返回数据报错问题 ([bfdc11e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/bfdc11e42df0fa3df52441705396601c9f9f330a))
* 修改通讯录问题 ([b1290ad](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b1290ad5afef865bee0b1df3a03298896afdd7c4))
* 修改通讯录消息发送判断 ([550f3ea](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/550f3ea8ac4aec3ae34b9e8dfdea6c006f82a847))
* 修改通讯录样式兼容问题 ([54270d8](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/54270d88724b5b696ec3f21bf05936daebe3d3d6))
* 修改通讯录样式问题 ([7029d0c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7029d0cf92ea26f8929d2daeae04bd238a1e7a36))
* 修改通讯录展示问题 ([f10b401](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f10b401ac7dccdfa87bcac3dc790865359ad0585))
* 修改头像的弹窗居中 ([b30dd43](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b30dd433d1e1324e8b6d0fcefd2949e3fd278005))
* 修改未选择电站提示 ([65a482a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/65a482abdcdac323b09b8860410b582844ac0c74))
* 修改文案 ([7730aac](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7730aacac179c28a405fe373bc3ce3d6d2ec139f))
* 修改文档水印样式 ([03bc775](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/03bc775743953776b8b9a8780147624067d48fdc))
* 修改文档文件会跳转预览问题 ([ebc54e8](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ebc54e810962eaca4d332051da3207f48cc3094a))
* 修改文档重名命判断 ([6c5b835](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/6c5b8357890ae59683ec59d14028e05f9bdd8710))
* 修改文件来源跳转问题 ([c0cbbad](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c0cbbad6cf182a4756ef86089373b4ccc3be2ec9))
* 修改文件名称 ([733c79b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/733c79b3fced93b8e010f32975106ff9349ab6d1))
* 修改文件上传数据校验问题 ([498ac2a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/498ac2aff51d202dc1a15d5812afeb72d95a139e))
* 修改文件下载文件名称不对问题 ([2278493](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/2278493a69a754f9538d08ce48c3cb92fd701512))
* 修改文字 ([abe8cdb](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/abe8cdbb889afae65363a42781f2c634f53b5dbd))
* 修改文字 ([9ae8764](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/9ae8764d85708e6f79f04c1a44ed7ae94efb9378))
* 修改文字 ([4a5ffbe](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/4a5ffbe3df6f431f1f8c4b25711560d6f4e94fe2))
* 修改文字 ([e06f3e3](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e06f3e385b7e94a9d42c0509a4ba66569aec1c95))
* 修改文字 ([eb17a49](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/eb17a49f5810acd4fb70ec26a6e46951a704da1a))
* 修改文字 ([dee9fdf](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/dee9fdf49ebb3a423ca8c22956e9b8fa5fe7b3db))
* 修改文字错误问题 ([35cac3c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/35cac3c0c77efff1e677b173042aa8d2e17c98b5))
* 修改文字显示以及中文显示的问题 ([5b77f65](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5b77f65d913f46a6f46fbce4cb0566e0ef4372e5))
* 修改文字logo ([ee449aa](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ee449aa03872c5bb28bb5a39dae17207be59d01a))
* 修改无人机报告列表文案 ([80e5cec](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/80e5cec961fb1831e6276e7a0541a80ced129e25))
* 修改无人机管理列表图片展示 ([50d5fdf](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/50d5fdfc937f50353555aee9bfad8fe5811cf184))
* 修改无人机管理bug、修改无人机样式 ([f1a5c06](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f1a5c0601dbf23cab6548d196bf1573df0e78cdc))
* 修改无人机列表样式 ([b38afeb](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b38afebe99a62315f73e3f544ad16cee306ac8aa))
* 修改无人机事件管理操作栏 ([289db11](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/289db11ad1a597978bf3d22c9affc135f72bdcee))
* 修改无人机文案 ([b894cc7](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b894cc71e7f31d2a398855887e64e6bd3170e7e4))
* 修改无人机文案 ([30373db](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/30373dbfb96def4378c8c1c1f28aafe43ce0c420))
* 修改物料设置新增字段 ([85734f6](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/85734f66b45f37bf8254bdc52e4d34a4dc369ed1))
* 修改细则模板table填报问题 ([b3eeab8](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b3eeab83f464c5be14bd955841822539c77fe222))
* 修改下一步办理人的问题 ([ba497f0](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ba497f0eb63f86cbddaa121d4b23335616129abc))
* 修改下载逻辑 ([c06665b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c06665bc5625546aa5c817a8ea03319539e37388))
* 修改线损样式问题 ([19bd6c1](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/19bd6c1a210a51cc274994395529b898bebb17ff))
* 修改消息待办弹窗问题 ([3ffe49e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/3ffe49e8f7e8e6d8f1e1fbfd09f5588bfc9e6fe8))
* 修改消息水印 ([782eb97](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/782eb97ccd1e2748633f74995a632a69cd7bad1c))
* 修改消息跳转问题、修改机器人助手tabs选中问题 ([612cd17](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/612cd175e83aa3dd827b7e9221e2f9acb152f32e))
* 修改写法 ([270631f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/270631fcd796b7ea5fac8a5e53b9f749daa498b9))
* 修改写法 ([31b785a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/31b785a435e182ab3a6902b82bf35fcc250c6e90))
* 修改新增表单提交时候选中项不能取消选中问题 ([f7ba221](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f7ba221f5939ec5ca2c7b49335695ee3082032a0))
* 修改新增参数请求问题 ([5342381](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5342381c6b36525a75d876a9d53c58d32e200105))
* 修改选择人员问题 ([e219eaf](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e219eaf8d405fc63ad6cbfd55983dcafec17ddfd))
* 修改选择问题、修改聊天展开来源问题 ([5b65230](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5b65230240b06613c9a96ca0cd751fa145336757))
* 修改选中下标问题 ([904af63](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/904af63dc507414c827d91f6731f9f2900672176))
* 修改巡检管理、运维管理细则填报、缺陷管理bug ([0ead765](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0ead765484d4ae23fb2cebe6e8c4b754d61b543e))
* 修改样式 ([e8bbc81](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e8bbc8187fc792d3703fee8966130ee63842a38d))
* 修改样式 ([d6d0780](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d6d0780ab12c3c1c925990d7cb07cad9b1ab375d))
* 修改业务表单弹窗组件综管兼容问题 ([9932d9b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/9932d9b8e6e031e2828b74a820a0af0978b31f84))
* 修改业务表单列表展示问题、修改日历搜索参数字段 ([650d0dc](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/650d0dc8020fee541b36fd71d9d0f2ba372d557b))
* 修改业务表单请求地址问题 ([602aa76](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/602aa76c53cf73f7f2cabf177f7ee5da7f6e7dca))
* 修改业务表单时间默认选项冲突问题、更新baseUrl获取方式 ([d79bc20](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d79bc20f4c846aea8e8fde45b7af439d74555750))
* 修改业务表单搜索统一风格问题、修改展示不同类型展示问题 ([03f88c9](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/03f88c9f2a514f90dc97dd10e9d0bce540c31c3a))
* 修改业务表单详情弹窗数据不对问题 ([a2e825a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a2e825a5f30ba351481aebefe1244bafe30e26d4))
* 修改业务表单渲染组件 ([33303d2](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/33303d24a6448c429075cda956743dccf53f1613))
* 修改一系列弹窗的样式 ([ab5446e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ab5446e77ac3f9a96c88fba4dc6967cdbe464dc4))
* 修改一系列样式 ([bb80be0](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/bb80be045b939e481f7c45ac65551ece31adc624))
* 修改一些样式和bug ([0da9fe3](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0da9fe39b630fc8d32480b2822ba5573df45e49b))
* 修改一些样式和bug ([ac7e4f9](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ac7e4f9f1d5ef5a5dec52a1ea590cc89f0ca1c4b))
* 修改一些样式问题 ([086e690](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/086e690890667a4f5effb8458282cbee7ca7accf))
* 修改依赖问题 ([fff3021](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/fff30214b1f223d96781e6acf0bede9713234c02))
* 修改隐患管理列表样式 ([0e4e142](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0e4e14279c74ca790e5b9f9e39f54b87a7afe9fb))
* 修改印章 用车展示调整 ([50d561f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/50d561f1c023064f96f62dd0ab3ea5b264b0afbf))
* 修改印章列表数据编辑时对不上问题 ([18550fc](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/18550fc2cd3be2aebd75e11b52c114aece2c5572))
* 修改应用权限问题 ([5472d7e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5472d7ee63cef3a0e9d7134d54f9b2116f011b44))
* 修改应用申请不同屏幕展示问题 ([a052b7e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a052b7e4a31941da1d51103e89021baccfcd2a83))
* 修改应用申请富文本滚动问题 ([0a3ddb3](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0a3ddb32ece1779c6855679d271bfafa55002741))
* 修改应用申请屏幕兼容性问题 ([a7293e1](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a7293e1d971f184f71afeb5bfce6ec59fe8423c7))
* 修改应用申请下拉问题 ([d5423ed](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d5423ed6276579fc381e8fa04578d6779696b4ce))
* 修改应用申请样式问题、新增列表滚动加载 ([c3619cf](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c3619cfcfb373c5f2bada2ad9c06d856eff4e6a5))
* 修改应用展示问题 ([d51d78e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d51d78ecc733c18f512fe663b4a233d596744ce9))
* 修改用车 车辆台账搜索bug ([9f27b1f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/9f27b1fd0d6725e9d2a093172361466b49fb1f85))
* 修改用车、印章展示问题 ([bbd6369](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/bbd6369c0fec4b436a9bd683cc557fdfe6c5a676))
* 修改用车办理列表 ([c2e5bf3](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c2e5bf339ffd6ca61914ca25e341edc86d1a50ac))
* 修改用车管理 用车的首页模式修改 ([0bfec1b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0bfec1b17a13fdefb273e27e7edf9d1a55cc389a))
* 修改用车驾驶员列表参数问题 ([18950d8](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/18950d8f11bbd8cb624c1c7b7976a25a65eefdc4))
* 修改用车目录结构 ([430b5d6](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/430b5d6bdd4dbcde814ba77ec7d3a040aa31877b))
* 修改用车申请 下拉请求参数 ([7e40991](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7e40991c4cb86880d97ee3d70d46f4a3fba84b67))
* 修改用车申请、洗车申请、保养申请bug ([f4deced](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f4deced1bc5c02ececf3bf901d9618abdc5b764c))
* 修改用车申请、用车台账列表展示问题、修改用车申请用车人填写判断显示问题 ([397f489](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/397f489fbfc733381683e9c2ce4028e604e0df68))
* 修改用车申请出发公里数获取问题 ([8324d74](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/8324d7478bebb7153d0219f05da57485e608944d))
* 修改用车申请图片下载问题 ([ef8ef9e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ef8ef9ea0f16745ee80681bd296037a4b1ccb2a0))
* 修改用车申请项目选择问题 ([e3c3b97](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e3c3b97d43503e228102678f5399faf4303f8889))
* 修改用车申请中选择用车下拉接口 ([8e3b173](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/8e3b1730db65a8be07fd873f70f32999df1a4aea))
* 修改用车台账搜索展示问题、 修改车辆资源日历的展示问题 ([71f238e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/71f238e0262791f17ec866ec6f7d5f5a76a9349b))
* 修改用车展示问题 ([2e577ef](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/2e577ef962a854c0a237297e6dabf3aad270a59f))
* 修改用户端聊天记录搜索问题 ([9607cee](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/9607ceebefbcce905775c858a5584af0cb84d05b))
* 修改用户端权限选择问题 ([7a9de4f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7a9de4ffb1eca326137a93f0e92a840798b3d2b8))
* 修改用户端日历选择逻辑 ([5084fbd](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5084fbd7a0e081c414acb101919c4017fb0172b1))
* 修改预览 ([3ef9b57](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/3ef9b575739db547ee60a5f91d0f068446ebee1d))
* 修改运维管理搜索样式问题 ([6f6f401](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/6f6f4011cab039bc8548512724f01b20e8a92241))
* 修改运维设置 ([455b9fb](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/455b9fb9a278e5ef9f9a2033495266390437b47e))
* 修改在表单在提交时,验证没通过和提交失败,取消loading效果 ([be4bd3b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/be4bd3b6c8016554b593cfff3cd0316ec4e34009))
* 修改在表单在提交时,验证没通过和提交失败,取消loading效果 ([bef086a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/bef086a7a174420c5a59ee2a3f502e21009bd114))
* 修改在表单在提交时,验证没通过和提交失败,取消loading效果 ([ad27ebb](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ad27ebbe7a61b4b7d33605d136664785caa4d160))
* 修改展示问题 ([c75c24f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c75c24f02d08bb740b307463ae4b37dcb255f345))
* 修改展示问题 ([e3b418a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e3b418a07716cd90b07833689df0c58f95e146c2))
* 修改智能助手点击跳转tab选择问题 ([201d608](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/201d608076edaa1873e0029a909ad0e97bed1607))
* 修改终端唯一码 ([e174cb3](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e174cb38e2e7bbfaa0f5ec75275c49a477aebc31))
* 修改终端唯一码 ([25a9077](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/25a90771b6b66601632e56631c55b33e8770a907))
* 修改主题缓存 ([76ce306](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/76ce3068085ce4609ff0c29f84fe673942e1c107))
* 修改主题色[#2778](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/issues/2778)e5为[#0](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/issues/0)f45ea ([6965db9](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/6965db9a4b6c101535d190e30e8e500261f64ad9)), closes [#2778e5](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/issues/2778e5) [#0f45](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/issues/0f45)
* 修改注释 ([8884c48](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/8884c48b685ef0fd54cf561f58732c00e7b50668))
* 修改转工单不刷新页面问题 ([4fd4c63](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/4fd4c6322a3040f77f70128ba77491e45ecc1956))
* 修改字段 ([0d33c89](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0d33c89e6e49825e55260c5062ef7fa3b1dbf3e2))
* 修改子表单渲染问题 ([d162af8](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d162af82bc2827091f0cb20e5ce7d6a50b485641))
* 修改组织管理查看详情时候展示问题 ([84c38a7](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/84c38a726a8312dfeaa2c3a9a4ccc8735e6d6fde))
* 修改组织管理展示问题 ([20579e4](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/20579e43d9057643d17464fc1222c03631ad071a))
* 修改amapEslint报错问题 ([e969bc1](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e969bc14aad6e68740d4d44ee72dd2098a659dc6))
* 修改baseURl问题 ([0de8ef0](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0de8ef07e07204eb58c38830bb43903672a68caa))
* 修改bug ([da05545](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/da055453e244983518cea0cd90e2efb043fc57fb))
* 修改bug ([44a607d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/44a607d54d6a06a000b866716b3d7e03869246e7))
* 修改bug ([b07ddc7](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b07ddc7a430aba5e33bda7c8d019c2a5d9951c45))
* 修改config配置 ([d05fda2](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d05fda2e3f933268ded5b2032e5d7a2ef5a3569f))
* 修改dom为隐藏显示 ([b569fbe](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b569fbec3c25c095fba077830a5234db6430ca1d))
* 修改element Cascader 控制台报错问题、修改新能源的组织管理用户部门保存问题 ([64ee6f8](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/64ee6f819e2a48666910ef6cde143cb67b320a26))
* 修改formaddData组件参数获取 ([9b56e32](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/9b56e3244995fc82c702d1f0f08b03570610e5a6))
* 修改FormTableRender组件报错问题 ([f38a095](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f38a095a93cc10c09b35df3708385c9e58d34b59))
* 修改json展示配置、新增关注公众号弹窗 ([aaa8510](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/aaa8510ff4b89b09450d2f88c45211bdd553023d))
* 修改logo图 ([57f9bde](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/57f9bde1e258072132541ec97befbc04bc35ace6))
* 修改menu展示 ([d2ad75b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d2ad75b6affca9f6a887a4319c8926e30a517a59))
* 修改number输入框展示风格 统一居左对齐 ([2ecf15a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/2ecf15a551732a6b0e7e0c90eaf1bf41d5f839b6))
* 修改OCR上传校验 ([97a270c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/97a270c0cb811ff347371c89c2827604deb4e98a))
* 修改ocr识别按钮样式 ([5133f1e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5133f1e6f0c033f35c4a56e203d4d4ed95ae5c41))
* 修改ppt报错问题 ([999ec23](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/999ec232a1cbd639c2cd413ea68c31c9d2eca636))
* 修改ppt地址 ([af71cac](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/af71cac175b0477bcde4a2e42336b79b125cd0fe))
* 修改ppt地址 ([d322851](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d32285106f277fbd15e3a8eb8aa4404a2bc5ac72))
* 修改ppt地址 ([53f9754](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/53f9754696c664362798432d712dd4cce535fcca))
* 修改ppt路径 ([6f9c1e4](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/6f9c1e4ed09270b2ecaf8f49c20ce135b0d00739))
* 修改r日程bug ([ab24a0e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ab24a0e0e291243270a0c41b1dc04bb0ff737706))
* 修改svg ([fd3888f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/fd3888fef3ce1e7984c7c58330ba09a63ea105ad))
* 修改tabs名称 ([daad69b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/daad69b1c38223e1e3c84154e8031f496f726d97))
* 修改umd、修改流程参数问题 ([998da44](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/998da44d358a104ed3e1786e5c95118356b637a2))
* 修正页面表单展示问题 ([c41bd17](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c41bd17be9daf1724f73f924ae98fd38de9a02fd))
* 需改电量统计的问题 ([46c2de4](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/46c2de4e6078653106c8cd4332ae4ad07ff30729))
* 悬浮模式图标问题 ([85c3b35](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/85c3b3569942435e03f108690a0c89a20a0279d3))
* 选人事件的派发 ([4d7ca32](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/4d7ca328764b53914b3ad1185b1ac205fdbc42cd))
* 选择按钮样式优化 ([f96ef05](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f96ef05a1d5adc6766d54a85bec986752294dc43))
* 选择插件后自动关闭 ([8244571](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/8244571426101e85e21fca4a82a20856bef1e7ef))
* 选择人员标题 ([9e14724](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/9e14724c3ad50eb1bcb5b20f983307285d61669a))
* 选择人员限制 ([cf04287](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/cf04287e2645fd0a0684eb64437b3199d06cbc81))
* 选择项目 ([cc801b5](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/cc801b5e831cb0deb2d64095088cd352985d2472))
* 选择招标方案 ([c266def](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c266def5768eb9a5cd8d86144821b2362e18a545))
* 选择组织 ([865b7ed](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/865b7edfbd3829ebf82c556b7bd87f7bef3a023a))
* 选中样式 ([d97f1ac](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d97f1aca5d8406e28160310de6341fee0726820c))
* 选中bug ([37ba5c5](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/37ba5c57d78b013688d1b58a7aec18b69df3d18f))
* 渲染问题 ([0fafdc7](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0fafdc7f9090f0c1425cdc953c994abe20b47853))
* 延迟刷新界面 ([cea0539](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/cea05391a3d8a5aeac0d4b81dfbd27ce569790be))
* 延迟显示更多 ([caf7c66](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/caf7c667de258773ad40104f131701ded03be7ef))
* 延时提示 ([f03a242](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f03a24247c15378ffec65e7edb8b82062db2189c))
* 眼睛文件查看图片显示 ([eb3cd01](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/eb3cd019eaee373ed5f857c9f1f7065f8377f76a))
* 验证码发送节流 ([b4d8232](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b4d823270efe5a971b8539a8912f3062411cacc9))
* 样式调整 ([d0a2a18](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d0a2a181e0249c405e647c3c1856f7a42bc0dca9))
* 样式调整 ([32357b0](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/32357b008f355d84d1591e8d0327814a90d5fbca))
* 样式调整 ([928818f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/928818f3df00abb8a6b025befae9f3be041615ab))
* 样式调整 ([f425eee](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f425eee364028ca21299ad14e055fe98435afe15))
* 样式调整 ([b65b8ee](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b65b8eeebdf6c7934494f1ac9b8c2f0c5fc6d76f))
* 样式调整 ([b19e39e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b19e39ed8d7b268627362bd9246323e1c6dace3d))
* 样式调整 ([5f0549a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5f0549a909cec7bac6a046f4ab3daa6fbce81b3b))
* 样式调整 ([2581e47](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/2581e47d4ec669487f046bf49990778fde030e2e))
* 样式调整 ([d9df9cc](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d9df9cca60f94b29f43e6e0cea2f3f690fdece8b))
* 样式调整 ([8823122](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/8823122bba769a6412bf10e2c79a71ce09bad8d9))
* 样式调整 ([69a14ac](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/69a14ac1b11481ebedbe482eb71989ed592f7eb4))
* 样式调整 ([750675a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/750675a639bc6a2b4a6aa83a633408a5c2f6bf23))
* 样式调整 ([6ee7065](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/6ee7065f803b9490d2d8e24d40e96f5f4dd5ff1d))
* 样式调整 ([b049d18](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b049d184ef1c79644dadcebc7d52136b551478e4))
* 样式调整 ([8590ea4](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/8590ea438de5e55a9ac5a4fa883bff9b09a73a73))
* 样式调整 ([79d019c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/79d019c153f8a44fde5d69f98fdf36e0a3322b20))
* 样式调整 ([b147f0a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b147f0a7ce8558c75702e66eb44726afafaeb705))
* 样式调整 ([024f26d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/024f26dd10a0595ea2d9604c94394b04fb6134af))
* 样式统一 ([f3c169f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f3c169f19cfec6f87ba4c4c6c1fe127791cf1b06))
* 样式统一封装 ([23269bb](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/23269bbb7ac4021aebbe77ca2f23b5a125b2a568))
* 样式修复 ([9cc29db](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/9cc29db92feab735eba0b1df8fc6ee3d1d129ecf))
* 样式修改 ([b3b8ef3](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b3b8ef32b8080e72c47ee3792ab9b513c9af6042))
* 样式修改 ([617c07a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/617c07ae102f85ce23a3b0a85debf427f15d6240))
* 样式修改 ([c95c2c7](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c95c2c77799dd4b3baeba7487e52f09803b2fce7))
* 样式修改 ([d73887a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d73887a8c57806fc85a22ae43abe666a3a34a72f))
* 样式修改 ([aa975d9](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/aa975d9d49cf8dc5bef0e3d88fce681c56376fd0))
* 样式修改 ([1275d25](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/1275d2510a2af3a3e0832630e6a817ab9e52b6bd))
* 样式修改 ([ad38d07](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ad38d07fd432c5e6916d65893575f0b7d661fe43))
* 样式优化 ([68b9c25](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/68b9c25270274443abbff1b6c7f62b0424c2a28a))
* 样式优化 ([ce2c223](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ce2c2238b8899feadeb90eeeac394bdb460511b4))
* 样式优化调整 ([b71223b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b71223b4e169496b17c879de25b6d90ccceb8824))
* 野火路由 ([f26176a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f26176a1dd4df1a745a82fe6d2eeba85678c0139))
* 野火无感登录 ([104e42e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/104e42e6191d4f1ad96e910ee7bee4d09a644466))
* 业务板块的引用 ([53b3101](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/53b31012904a3a18420feaf998bdad7376be184c))
* 业务上的ai入口隐藏交互 ([0c53d88](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0c53d880161fb5e1c8dccc21283262fdea629c74))
* 业务用车-车辆台账去掉百公里耗油费字段 ([a51d013](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a51d0131cb764f8873ce0f391bba9713593aa3b2))
* 业务用车-只在通过审核时才进行审核业务表单的填写提交 ([422a8b2](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/422a8b2340b1938af542b93cd8b5c42decea4e93))
* 业务用车-资源日历申请状态示例文本修改 ([711cbbd](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/711cbbd51636a0977fd4b67dad1c5f73be4929da))
* 业务用车，优化选择出车任务标签页 ([83f1bb1](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/83f1bb18d1446b79b7afc1871e0811fdb6842b1b))
* 业务用车：车辆损伤选项调整 ([c6aa0d4](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c6aa0d474dfcc8468c68be7953ea41c8cacff1be))
* 业务用车：车辆状态调整 ([b58a96c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b58a96c395c49658285e496f4a08919bbb8b3afa))
* 业务用车申请入口加参数 ([351b710](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/351b7102d25195edd8136bbbd73c638fd394a8fc))
* 业务用车数量接口添加参数 ([dfea457](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/dfea457a3975b8499075f0505a1345403dfd3044))
* 页面搭建 ([f650e04](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f650e04f7c73a378a84bf1809a806bd69ff7bcd0))
* 页面组件采用路由的写法，跳转使用replace ([b88267c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b88267ca4382b8c20bc3500382f2786eee579c4d))
* 一系列修改弹窗等 ([a3730c8](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a3730c8ac5155412dc0f85aa71596d138ad9129c))
* 一些显示问题, ([91c2f99](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/91c2f9904e3b510fca1481e9c02403615e31cf47))
* 依赖抽出打包 ([ca71b2a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ca71b2ac106e549662377303de4f032c8fb1cf7a))
* 依赖关系 ([b4a4be2](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b4a4be27df13670267f68fda7d8f891ebe513e13))
* 移除多余的空行，优化代码结构 ([57cd7a6](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/57cd7a6b1afc8c5236c7921b14058281f567c84e))
* 移动文件夹默认不展示 ([2463b79](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/2463b79e8bb46ec406cf1decc370351b0f4bf0d6))
* 溢出显示省略号 ([4f998fb](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/4f998fbfac338a4d8dc0044157390b23f628795b))
* 阴影样式 ([3f61df7](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/3f61df7eee9ba8b9a57a59f9a1383f003664d140))
* 引导页存放位置修改 ([68db265](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/68db2656601d0a1eb823e67b86996122cef05edc))
* 引入组件的判断 ([ce99b3b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ce99b3b09baf2fca679124ef2eb3294b199c4d33))
* 引入tinymce富文本 ([5bd18c6](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5bd18c62a4f3d9a154a13869df889604517f88c7))
* 引用方法 ([f92f97b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f92f97b9a496f713f8cfdbc2e7899d6dd7531fe5))
* 隐藏多余功能 ([8919a2d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/8919a2d52cf9cf77920a97fc5989176a9eeb4922))
* 隐藏ai入口初版 ([e9d758c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e9d758c15095a11cec088ff668e73a893b03cb94))
* 印章管理 ([5c885ca](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5c885ca900bbbd216e92c09cbee7915c5c1b06c6))
* 印章管理联调 ([d1a12a3](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d1a12a34e3f4d44cc71c37840065eb653ec7ec8d))
* 印章路由修复和租户切换 ([dbb1c15](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/dbb1c1583777ee53633ffb710b20e267564955d1))
* 印章目录重构 ([3a3a85e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/3a3a85e16a6e346e01bc4ad48012a2fa096ef2c1))
* 印章台账初步开发 ([bc2012b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/bc2012b96384c04b7de5e6277d6a5298827bc450))
* 印章台账列表 ([603c0be](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/603c0be0caa619f3c83e3433483d32f1ed408d73))
* 印章相关的动态表单样式调整 ([4fb3e53](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/4fb3e530acff31baaef4e140846136fab4d05fa0))
* 应用的标签动态配置 ([fc7fb7d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/fc7fb7d46ece0aca821ec13ba4a89b68cc2a2072))
* 应用埋点 ([025b9da](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/025b9da4ecac1d87e46dc54653e898d2225cd678))
* 用车/用印申请根据版本获取当前用户部门 ([d5bda70](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d5bda70e033018223efcf17716648884a781f214))
* 用车申请字段回显修改 ([748c8a7](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/748c8a727e32569ebf2aa5e78a8b70f431d0723c))
* 用车新增打不开的问题 ([bab5885](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/bab588549f4e9cb54e2091ae0de90f13c2e36f3f))
* 用户端弹窗高度调整 ([ead6af4](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ead6af478505f3e0013b6f91d8093f22169c92a8))
* 用户端同步登录 ([dcd74db](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/dcd74db6a3e913776a1913da82c9025ceffe99a4))
* 用户端渲染 ([dc8757f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/dc8757f8a07894678c2d242f43cce6f9aa84dcff))
* 用户端应用市场接口对接 ([2bb2ae0](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/2bb2ae0c9d388829f415fe482c83f705ed89b60b))
* 用户信息 ([e4c3b76](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e4c3b765a1542722c856634078eaeb13240b64c8))
* 用线上的umd渲染 ([62fd514](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/62fd514c21495390d49e6e42d3ef63d2eba272a4))
* 用印表单调整优化 ([9f68d51](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/9f68d51f54c55492d6615603620ab99bd421f1c0))
* 优化 ([4214a06](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/4214a06957107c85b7b998a22f4ecab75acf66f0))
* 优化 ([65aaf13](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/65aaf132c8e5e524aa1db608c74c6387efc37033))
* 优化 ([f8b8873](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f8b88739ab70ae2e51a6be2ee4e43c77239648e8))
* 优化 ([821787d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/821787d3e389cbd493803c01df30af32b1e08ac8))
* 优化 ([86d3a8e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/86d3a8ef0eb1fc4884174b3f64ca99f5734cbfcd))
* 优化 ([b05bcfd](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b05bcfd68dc7a45836f4be7203099a52fbd38715))
* 优化 ([b1fe87f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b1fe87fd17c554f7bb3b796b874a08d88bf2359d))
* 优化 ([37241cf](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/37241cfb1ffcc9cadf88aa464d590182f14e071d))
* 优化 ([5ed3727](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5ed37278d2f26ef8f1b9a9b1195a080b76b55f70))
* 优化 ([957b171](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/957b171725c89697bda9efb67298a546f34d0e5c))
* 优化 ([0ddde27](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0ddde27160208c72879ec04ae42ea474c7f2fa17))
* 优化 ([b98b948](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b98b9482e3fd418fcc2af8a791987fa779c1b071))
* 优化备忘录 ([4c399d0](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/4c399d01c215f44fb409b42b064eb276c2699b6b))
* 优化标签 ([be41109](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/be411093c210df9a650a36f2a5f5cf3f18186fa4))
* 优化标签页展示 ([5fb94f0](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5fb94f054506473b5262fa741e3fe23cb8f88f52))
* 优化表单校验 ([fcfca71](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/fcfca7146234626f1749227e2a409caaf69150f3))
* 优化菜单反选 ([d4e5c60](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d4e5c605905e66e610a8ada9954ba0b00256fc9c))
* 优化操作栏错位的问题 ([f1f6e91](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f1f6e91053a5002c1d2a89144cdd46a8d065df27))
* 优化初始化的时候的逻辑 ([9be985f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/9be985f8e1290eeac8edd96c4bf2d350fc51173e))
* 优化大屏地图加载切片时候的白线 ([86018a6](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/86018a6f49b79dcf6be904fa1640da01fc397da9))
* 优化大屏样式和用户交互问题 ([5e759dd](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5e759ddd60a3d133a119cc20f79b358262a20c6d))
* 优化大屏展示 ([1c4596b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/1c4596b3a602756a14aa83a8940a473d65428da9))
* 优化代码 ([70ad48f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/70ad48f5d091705950155a038bdea85cdc4c8859))
* 优化代码逻辑 ([8574509](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/85745092fa194d60428e1d549528c07ba5780e48))
* 优化弹窗 ([6cde30a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/6cde30ad11016a8bde66da7dda7c2c3cd7d9c078))
* 优化导航结构 ([0c66129](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0c661298cd75f6cef7814de7bd8a7151a3df647a))
* 优化地图切换中心点的时候白屏问题 ([1854d8d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/1854d8d6bdf467aca1255f5a2a3fccbade367d75))
* 优化登录失效的跳转 ([5991a9b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5991a9ba8e5f583602f9cba369f26dd0a518df31))
* 优化飞行记录弹窗 ([2693b51](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/2693b510f953bbb6d8c0005e72b41b678f07d645))
* 优化交互 ([f2bf05a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f2bf05a7c8c42bf7bbb476ff7acd7999c590896e))
* 优化交互 ([21f87be](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/21f87be421aafc3c443dcc68179a490174a9b9ab))
* 优化交互 ([85a1fc4](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/85a1fc44d91fe34bf79cdaf29b847eeef6f6ae41))
* 优化交互 ([8429898](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/842989816c9214aea0f68f8a586c8dac4b9acac9))
* 优化交互 ([ae1394b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ae1394b43df0da20d0c6c7eed883561fc8403713))
* 优化交互 ([5b4e061](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5b4e061b3bde730c39fd781b2ded1cffec8ca5c2))
* 优化靠边隐藏的动画 ([c8cc6c5](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c8cc6c5bcad1215440cb13741cb890c698b7babc))
* 优化两票管理图表样式 ([a8b2096](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a8b209685c6082dad735066f68dedcf750912e93))
* 优化列表展示 ([bef2cef](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/bef2cef595ad3c7d05efa616e8153a9371007159))
* 优化流程图显示问题 ([558d74e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/558d74e5f783b434a7f542a1d6dce2944956b83c))
* 优化流程图显示问题 ([f926487](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f926487ecf86e9d6601c02401ff52c9dcd6ddbfe))
* 优化流程图显示问题 ([42d6517](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/42d6517a7891a7ff5189c826706d8440b5584852))
* 优化流程图显示问题 ([13d7c39](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/13d7c399e49ea1e14b7c376f0f59a552146c4277))
* 优化流程图显示问题 ([232c26a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/232c26aca0bcc012605a3af0f767e13bc3a79ed9))
* 优化流程图展示 ([dbf8dfe](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/dbf8dfe305b672acd9cdbdba7f91f1dd7d537a4c))
* 优化逻辑 ([4c1d9a8](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/4c1d9a8fa7a4739aa179ccfd6502a79a8c630b01))
* 优化目录结构 ([8b7c433](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/8b7c4331329fd787e5135256c57fba01345220d5))
* 优化判断 ([091c065](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/091c065f133fc4a4cadf452d122e72a0d2e7e2eb))
* 优化日程添加会议表单 ([4e7389e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/4e7389e5c8e1c54ed7e46de26b4c1c5d2ae8ebfe))
* 优化日历的展示方式 ([e05e5fd](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e05e5fddcd6d90987413000846660c50a164dbb9))
* 优化事务助手待办数据加载问题 ([b945ba5](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b945ba5418a2497cde001dd63f67c2f996702449))
* 优化视频显示 ([c234b85](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c234b8500587aaecf2c9872584b8e5b8e29a80df))
* 优化搜索框 ([17af028](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/17af028f7b06fe9605034f73fba7d15aeff4f664))
* 优化下载交互体验 ([ac3d6eb](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ac3d6eb04045090150d11d3de2252f298084d807))
* 优化显示 ([9389012](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/9389012a1b753f9dd832ff845a0c41f64fa7979b))
* 优化显示 ([bd99978](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/bd999783f74e2485c2e579a2c62d76a10449f315))
* 优化显示 ([851b523](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/851b5234a810cdbe7335283229e18786fd57cf98))
* 优化消息发送 ([6f45475](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/6f4547559d56be2b62b37ffd2892be9bb8dcc3c7))
* 优化写法 ([c93b963](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c93b9635f1d673831157e87ef26e7c8bb0ab77a0))
* 优化写法，以及本地开发调试组件 ([9829d7e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/9829d7ef3bec7f2cb946f266bcdc3d85a153f53f))
* 优化选择组织的空间 ([77c9e8a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/77c9e8a903359ea0d1071e5d17e1c5fe6afa5d2b))
* 优化一系列问题 ([dd37dfd](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/dd37dfd765a0ce3aebaa5d86e020cc31ea2a24e1))
* 优化一系列问题 ([d13ca60](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d13ca6022a7b2e39c79d6c6f30e5aee608fedd72))
* 优化一系列问题,增加流程和流程图页面 ([29be2c2](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/29be2c20a333b42e577675649213fa12e6d151be))
* 优化一系列问题,增加流程和流程图页面 ([c558488](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c55848850e72f429512f9a240d7e6a1f6fc9bef9))
* 优化一系列问题,增加流程和流程图页面 ([702dbf2](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/702dbf2c15e9a1135e127c5404164facada3e80e))
* 优化一些问题 ([4bb5c2f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/4bb5c2f631ece3fc282d8a069de5734130a0f386))
* 优化一些问题 ([7452ca3](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7452ca3aee3d138dfbb79e554797ac560afdaeb8))
* 优化一些样式 ([e5a8abc](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e5a8abc60c3378440ceb3c88805d32ff4d2814cc))
* 优化展示 ([24402f2](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/24402f25dd49c3a27d1b875b6c921ef361beade4))
* 优化coos交互 ([8e2ae9c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/8e2ae9cdcab753df0971237f7835f58d28d44548))
* 优化tab ([635f620](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/635f620780c0e1f8570ecd1f9bcccce7396f0a91))
* 优化tab栏 ([8e79275](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/8e792753a925e72066a4877037698eb90b49e340))
* 邮编 ([a6a1eff](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a6a1effcccc3186c894dd2ded24e6913a0844f13))
* 有机器人助手才处理参数 ([3ddbf41](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/3ddbf41cb1acba57a6d4ef08b5bf7049d6202529))
* 有无手机渠道判断修改密码的方式 ([da72763](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/da72763d312484152194ce92b50abf1d9dec684e))
* 有无指定知识库作答 ([6e8c7cf](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/6e8c7cf60fdbf2f1d828e4b122a7b5e2864ab114))
* 右键菜单效果 ([5e85bbe](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5e85bbe6426aecbd90e5abf80fe88c26af52d06b))
* 语义化目录结构 ([0c57da2](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0c57da2e64c4daca3cc9993490e8bea0dc41b874))
* 预览处理 ([8f25bfe](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/8f25bfe59522c59eecd1c41fe43caadb40585b2b))
* 允许多方式配置页签、菜单应用、面包屑 ([15de36e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/15de36ec19fea023385a4b56dfe2cebccfd21ada))
* 运维系统-告警接口地址调整 ([18dcb3b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/18dcb3beeea0fc7ce7e65779a9e6334c5126fc27))
* 运维系统-监控设备添加定位 ([a0bd9a0](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a0bd9a05f9ecea58b808e04496ef2f86fa8f2e92))
* 在没有流程时候 隐藏流程tab项 ([ce17bf9](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ce17bf93954dd9a11673e8c970457aabf09379fd))
* 在引用的时候做处理 ([6b033dc](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/6b033dc7968fd75ad5c110864e8a3ac690f78e42))
* 暂时不解码url参数，route.query自带解码 ([a58ed30](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a58ed30778518ba18b243502661cdf87f33f7b44))
* 暂时调整待办处理的页面 ([0cfa194](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0cfa19433a2d87e3dcbbd654f300dbc4057b22f1))
* 暂时屏蔽会议时间 ([b15e816](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b15e8162527d9bdb9e7926e1df29cc1f83418daf))
* 暂时屏蔽控制台清理 ([bfd2cb7](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/bfd2cb782f1fb268cbc4269201ce3b7e380c3e31))
* 暂时屏蔽新能源未解耦的数据 ([c1b4431](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c1b443178c08087a3113c37887a89a6cda112a28))
* 增加保存按钮 ([ff6bd07](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ff6bd0744bf1110269ff9a7ed39225b442f55669))
* 增加变量 ([0d9bf24](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0d9bf24487302518bf4077e954d221d5362d2345))
* 增加查询电站的装机容量(MW) ([3245e85](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/3245e85a2e2cdbc77141d7a9238909e77cfc1039))
* 增加抄送后的已阅功能 ([dbe7d16](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/dbe7d16d4f2697a729ad0eaf05c8897c0c48bd04))
* 增加弹出时初始化值,防止下一条数据弹出值没有情况 ([4764e26](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/4764e265a2ea077da9cb2ba18138cd840a392d6a))
* 增加动态列表 ([8895782](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/8895782b58b96f79ed058df198d933ca62385b9e))
* 增加防重复提交 ([816b640](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/816b640cd2794a18481851941e5125a41da96752))
* 增加工单详情,搜索优化 ([0fda543](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0fda5430d0e192819ab49d5f1d2cda2bf388fbd6))
* 增加关闭项目按钮 ([1c0d063](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/1c0d0633998abe0311cd7a58f9937e93f1b4380e))
* 增加接口调用时间 ([c6a9a00](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c6a9a000f411ab8069356b47c7ecd699bb2cdf1d))
* 增加流程办理的加载状态 ([9a96954](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/9a96954d7aaca76ed77d2ce9ef47bbaecd3129da))
* 增加流程办理的加载状态 ([b8bbe7f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b8bbe7fe5107ac4b44952e4fc9be37aef172cbfd))
* 增加流程表单界面 ([107dce3](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/107dce3a8b792b6cff30121c415b2746aa6ae5a0))
* 增加流程已经办理的提示 ([9470d55](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/9470d5546b083f4fe7b1a870c29421073ba53b29))
* 增加流程已经办理的提示 ([849b149](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/849b1499027fe9a4a2b364fee67729bf1b1ca8e6))
* 增加删除操作 ([80629ec](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/80629ec45991ef8c8f724cc4b1d59be6beef76a0))
* 增加上一个用户的用户信息 ([22b0294](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/22b029442db411a6d7323b2099ab033f04930a78))
* 增加是否需要外部表单响应 ([69f0e7c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/69f0e7cc1c3675640f2b85d4aacc270b9006219e))
* 增加树展开第一个模块,优化三个模块的提交 ([8ffc4ae](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/8ffc4ae5685afa88618751741c28387c11487409))
* 增加外部表单传递的参数 ([fe455e4](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/fe455e487445052eafb89a58388d53fbbeb5ad2d))
* 增加颜色 ([d731674](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d731674dd769edafc1895a75d6345d7d0d6afa13))
* 增加验证码修改方式 ([d50e45e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d50e45e61c39a95096ab58bb3638b481b1ff76fb))
* 增加页签操作按钮 ([4748a86](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/4748a86162b7c4f676e238a63606996f6b5a75f9))
* 增加一些共用的ui样式 ([cdf706a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/cdf706a3d32949ea27cb3c8348d09a9008857e45))
* 增加一种类型 ([80a2c6a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/80a2c6a0a0bcc32ddc8adf4315247094c8d66302))
* 增加只有通过时候才有下一步处理人 ([c561ea5](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c561ea52be2a359f042e1a6c2ab0944ad8021068))
* 增加转工单功能 ([e2de15c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e2de15c7e25ee8e0bd7de875ef418050bcd552cf))
* 增加转工单功能 ([3e0a6f3](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/3e0a6f32174c822146e775b7a6affe1745639258))
* 展开收起详情 ([03bad94](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/03bad94e3e9054ddeabb940b20f194baf42cc413))
* 展开收起状态 ([fc17575](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/fc17575fce9e05a626d3345580fce448b02cc7e7))
* 展示图标 ([0d6e243](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0d6e2435b9e149eb85492b513b54cc767a491de7))
* 账号登录验证码接口对接 ([e890ef8](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e890ef818fb60ef7c9c12b1ad67101b65b5ba9e1))
* 招标 ([198d3a6](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/198d3a6400e34be34881370fc7aa5e04c6b3c8c2))
* 招标 ([9c07f52](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/9c07f524d6b19f1d5954fbec478061037c8351cc))
* 招标 ([367fa61](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/367fa61f452d9e7f36bdda04bd91aef85acf38c2))
* 招标方案，信息，结果iframe嵌入改造提供给采购 ([55a11d4](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/55a11d49bdd7e5dd3ba32f3f11a3e2ef27e60c30))
* 招标方案管理 ([bef5a83](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/bef5a83eb8f123d0f616fc13a11537404a37ff4e))
* 招标方案管理 ([0d148b4](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0d148b4ecfbf660c1deedb66e48da69bd2f1abe4))
* 招标方案管理 ([61881de](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/61881dea9b84199e38e59cd8d15dbe7d9eb8921d))
* 招标方案管理优化 ([0db20e3](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0db20e37f20a72b432a45a5f6884bf9c08f26c15))
* 招标公告 ([6515857](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/6515857ff81605ae6839ce2d3bb7803dcbec2f3f))
* 招标公告模板 ([e9f3e28](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e9f3e2861955f994d0ac8cf91389187a714ea14e))
* 招标管理重构 ([35eac83](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/35eac83b6892a17d1ec093ac050f97d110dc93db))
* 招标结果 ([f089a7f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f089a7f51a6e0b02fbc1b1e7dbf17cb4184f8a87))
* 招标信息管理 ([9ed2ee0](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/9ed2ee041a3359ee0c26b423f226e53166e65160))
* 招标信息管理 ([1ca363d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/1ca363df55d7eb493571c8f031d1d7e3ecf44c76))
* 招标信息管理修改 ([ef84624](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ef84624a3add6e7e3fdfc021d403fd3a1b704ea0))
* 招投标 ([ce36cf0](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ce36cf08129b96f4eb064f81392f2fd4eb14cb7d))
* 招投标 ([114e6e3](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/114e6e3277028571d39ae1d0b3d894bb970052dd))
* 招投标 ([3e73415](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/3e73415ec013d188fec83b7bbfcd08552cd0ad63))
* 招投标 ([80c157d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/80c157d63d8e09d1cccd18ce51310a1de1969185))
* 招投标 ([f293230](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f293230c50217973a534e49cd6861fc0bbb72012))
* 招投标 ([418ee60](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/418ee60560f8002f65b8fffb72daa9ce1559e520))
* 招投标 ([ff4661b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ff4661b31756e99b791776d86bffad98535dc628))
* 招投标 ([335b029](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/335b029009b05a55539e12da3aee85cab14d2f89))
* 招投标 ([a5fc67d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a5fc67d218a2039e156fd1534d6fc18377de2e70))
* 招投标 ([842acaf](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/842acaf8c3feb6f806770b79a6c7f3bfc5f48965))
* 招投标 ([2b17f43](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/2b17f43e209600d2a08ff8c7f24302f0f5b4976c))
* 招投标 ([a869990](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a869990305f0ec4a10997529991501ce178cd3bf))
* **招投标、专家库:** 选择项目弹框优化 ([8d55cc4](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/8d55cc4972a67d1ed94e628529e5c8dd033a5e85))
* **招投标/专家库:** vue组件name属性修改，专家入库汇总模块删除 ([255caaf](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/255caaf2485d5be4c76d2b2f8a87ee655dec0b6a))
* 招投标按钮样式 ([87270c2](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/87270c2f9078dc7eba1bef1849bee323c4bc0b3d))
* 招投标方案管理 ([b2d6ffd](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b2d6ffd3a858bfded4039e42e14d0ccacf6b199c))
* 招投标和专家库修改 ([8449e01](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/8449e01bccaa75c1b0104dfe5adf34d202ff22ab))
* 招投标和专家库优化 ([709e004](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/709e00485cffc3fb4d6df4f0b0be731ba2e3b5fa))
* 招投标蓝湖修改 ([aecd3cd](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/aecd3cd08c13d7e440ab08a7eead3061f436b2f4))
* 招投标路由修改 ([82b3bba](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/82b3bbafbd2a481d48bef95d6941022960714463))
* 招投标修改 ([cfca8ee](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/cfca8ee6cf384ac51260448a50b5846fe86ed6a4))
* 招投标样式修改 ([48e051b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/48e051bff3fb5b07949b1407590b501109ec32db))
* 招投标样式修改 ([e79e20c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e79e20cad4e7aae6cd641961bd9a8d8df98b4872))
* 招投标样式修改 ([91e57db](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/91e57db8c2595efea27b29ccd7437a1284cac410))
* **招投标:** 招标结果请示业务流程逻辑修改 ([7ec7397](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7ec7397b1060fd829dc9f9e1a5821d9e0aca0328))
* **招投标:** 招投标菜单跳转问题和招投标管理员权限 ([db9fc90](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/db9fc90ba6aeaadbdc187cb349c9420ec5849936))
* 整个框架的coos智能对话悬浮窗口 ([937ae17](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/937ae174135e12b17850cfb3debf5ce6943fd88a))
* 正式环境报错 ([929ee03](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/929ee03c4e16f8b755eeacfda33685322b0169a5))
* 证章台账项目类型渲染字段修改 ([847cec1](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/847cec12da46920bd908641d5a8d025b61325818))
* 证章项目选择保存额外数据 ([c4b776a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c4b776abb2eef5bbc8771909697b71e61ded2053))
* 证章信息数据获取数量修改 ([5a123e5](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5a123e5685832078ee2a22586a22251adb252196))
* 证章信息数据获取数量修改 ([49b7550](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/49b7550d03f62aa803dac600afa1290ef2257259))
* 证照申请类型参数调整 ([f6732b4](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f6732b4e018574aca6552cfa26cc9e0152429db1))
* 证照字典值调整 ([d18d284](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d18d28451a8c9d4d37282f59387c277852d94b99))
* 支持回到引导页 ([afefa9b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/afefa9b5e3c8ee1bc999ef2d0f2803a63b886ae9))
* 支持新能源定制需求 ([beeafcf](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/beeafcf4802cebb2765007eae0639b8996862895))
* 知识空间增加标签 ([c8eda65](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c8eda6549d058661beb6a6f1370ea6979cec11c0))
* 知识库保持一个选中状态 ([2bce46f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/2bce46fd8d53ef3cb6e563f354ec8bdee8f861d6))
* 知识库单独访问 ([3e0527f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/3e0527f287714bca6073edca48580d2c9ee6d95f))
* 知识库动态选择 ([1f94ff2](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/1f94ff2b4a3c5dcbc6c2ad77d81b521806ad3415))
* 知识库默认选中 ([b1afa35](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b1afa350b3089283333c6904d85dff18dfbacdb5))
* 知识库添加链接 ([357decf](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/357decfe028c95fac67b4b19f9a87eee4169f635))
* 执行顺序 ([5d533a7](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5d533a7e8c5c1ac2c91e7c9ec7b657d0cf55416e))
* 直接选择插件优化跳转判断 ([f9ce09c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f9ce09c39c0703136cc4a536a3e643d706b2fa2f))
* 只显示一个浮动入口 ([c00a34a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c00a34a13ed49c3d0dab34740b08f0d72413452a))
* 只有一个插槽的时候屏蔽不回显 ([f5c9d49](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f5c9d49cdd96d92dcecc25beb7ef3954c4c821e7))
* 只有用户任务的时候 才选人 ([31346de](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/31346de6ce7897ed24daa7e7aedd9948052f6ea1))
* 智答宝库才加上知识库id ([54836e9](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/54836e94f5e47143a680bbc221171c301aa7e614))
* 智答私库灵活配置 ([36aaa0f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/36aaa0f6a615d47731b6d9cb56750f08b9fd202b))
* 智能对话窗口的融合 ([fd0f327](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/fd0f327c4e89988c9a3ff5eb76bcdbfee7650498))
* 智能对话的标题动态获取逻辑 ([0789a2b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0789a2b5eec94c093c0a05e594eedc5420f9f693))
* 智能对话动态调整 ([8ef6987](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/8ef69874c993a136badd6fb3cbf73521c354e6b5))
* 智能对话复制功能 ([4f26df8](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/4f26df82da20869f88e668e23bb4f9d2ccac54a8))
* 智能对话根据租户切换显示逻辑 ([3fc566b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/3fc566b268fe4c68ca806c59f20536ec4c712332))
* 智能对话欢迎语逻辑调整 ([376fc25](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/376fc25c0862145c08881ee12d7899ff1534611e))
* 智能对话没有答案的处理 ([e63b04d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e63b04d45669b137e22a154986afe1eeb6f23ec1))
* 智能对话融合 ([e4d06f5](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e4d06f5bd89aa6e216657bf54177d6bbdc2188db))
* 智能对话三种模式的集成 ([f87c476](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f87c476e48dd3d3e00d9d83f46fe49fc40ddf16e))
* 智能对话数据智析bug ([0ff16b4](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0ff16b4a62aaaf12e317550687410a3b2ac2566b))
* 智能问数的历史记录以及更多推荐 ([7b44fae](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7b44fae480ef8ad06ed7262743020180830d4cfc))
* 智能助手弹窗优化 ([a129c9a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a129c9a5d1d77c33c5afee48d0baf7fa01ed99d6))
* 智能助手动态配置 ([5b2ad9d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5b2ad9db72a9bebbc765dbfea89e3f2dfb9b9dc6))
* 智能助手交互UI升级 ([ba865fd](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ba865fdd252820e6496b6831ec2fedb9c43b214f))
* 智能助手样式 ([0bf7677](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0bf76779a4ed9f75f48244adb4827feb70bea384))
* 置灰 ([04e2e2c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/04e2e2caee695ffc1d7b1e8fee5126cb3f8f4e93))
* 中标单位 ([d24b65b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d24b65b5b61460aaf7b71381aee5b13e8c093c3e))
* 中标公告 ([96fa867](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/96fa867d472e88bd93aa726a1cdee2f4f03a251e))
* 中标公告 ([0019c1b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0019c1bd55a95f698c70bc5492a1a4dfed53d300))
* 重复选中重复请求的问题 ([6b0acf6](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/6b0acf60ac3e6f8655747a81802ea3f4941dace9))
* 重构的供应商管理申请 ([55c74c1](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/55c74c1162fb5e460f029ac03361ff8bd5fb25bd))
* 重构后的bug ([2134512](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/2134512438bf1f045092ff8bd311c6b307d6beca))
* 重构目录 ([7ccb849](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7ccb849b179d1c514bf69b2ebcbec35fe7f8db49))
* 重构目录结构 ([4320b0f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/4320b0fba2f848aa0e74bdcbebfe0efee09b66a8))
* 重构新能源目录 ([32dae0e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/32dae0e8f94377d6e4edd115de1c002a7415bf4b))
* 重构智能对话逻辑 ([6ee1e3c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/6ee1e3cfa458e45d790ebf811d3feec31a3de73b))
* 重新调整项目选择插槽 ([53180b1](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/53180b1d030d7a67764ebc1e29972c67b6186ccb))
* 重置的bug ([db87257](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/db872577aa814b9cd27a5130dc248fa07e4ff502))
* 重置智控对话 ([0c1ab84](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0c1ab84f0e64a0199cdb2fe884d4422485981980))
* 主题的bug修复 ([b42ef08](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b42ef089ecc2582f0e084aeb4e7f01b3389ac1e0))
* 主题色层级 ([43ea1c5](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/43ea1c5dd8776cfe1a0317d8646cb1aea3f9e43e))
* 主题图标切换 ([523d4be](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/523d4be84ef864fe20777a6ad8a7e2120b671995))
* 主要文字颜色修改 ([290d337](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/290d337ccd6ef155354b1ad4d8972cf3bc90fd1f))
* 主子应用通信移到主框架 ([a38ed36](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a38ed361ba10d62941be2e3ade23e9a0cf5a2c25))
* 注释 ([1366415](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/13664155baa44fe08678530f3f6e13091bd954de))
* 注释 ([5fed21d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5fed21d8cbcfd2c88132ad24b824ca86eb6c4833))
* 注释滚动条 ([3bec742](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/3bec74264cc98914cd22b95d2057f0dc9dab5236))
* 专家 ([fb828b0](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/fb828b0e0b2983aa25a9f983875a473435050540))
* 专家:有效/综合分数 ([ecd1281](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ecd12811eea96a8398e6c7c8d395a0d1b6925a32))
* **专家管理代码优化:** 专家管理剔除多余印章的代码；部分方法增加注释；人员选择器优化（当没有选人的时候提示没有选人）;自定义列表优化（二次加载高度异常问题） ([2eb71f1](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/2eb71f1e068602a484760ad0240919033d1a11af))
* 专家管理历史版本 ([d5d87ff](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d5d87ffaabdde27448e2d8a040a2f313f7a507bc))
* **专家管理:** 专家人员选择器优化；专家使用字段更改（采购申请、项目类型、所属公司、所属部门）；专家抽取页面搭建 ([f751910](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f7519101ed8ce6257eb89044884cd799f3d30ca4))
* 专家库 ([b715363](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b7153634f037dae84bb20c43c08fb4efd8bb1e27))
* 专家库 ([54c7dfc](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/54c7dfcc087c05d63e25d011aa5565eb1f5bbace))
* 专家库 ([9e346a8](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/9e346a809626619e99952a0dddfc67dd17cd2464))
* 专家库 ([307e5fc](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/307e5fcd8de07257a0113de3c9df24c2188dc885))
* 专家库 ([66952d5](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/66952d57cc0520d7c201bbfad688f25e398ecb04))
* 专家库 ([db08637](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/db08637eec0e6b2e0e3cf199b11b315482bec5f4))
* 专家库不使用富文本 ([c9222ac](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c9222ac6dce832a8f0527b702e9e5f455d52a42b))
* 专家库导航 ([c81df37](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c81df3706de8c3d20f70922036eed881f15e671b))
* 专家库管理 ([6b5660a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/6b5660a55cddaec79c6744690666bf894df8dce7))
* 专家库管理 ([9de2023](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/9de2023e0f3b5e7f033178ed67b44d060fdb5c49))
* 专家库管理多选框和性别修改 ([c7f44ef](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c7f44efcc26633e3f6654f78b6e61ca8f07531ce))
* 专家库和招投标权限控制 ([a997c7f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a997c7fd9f91473ffd85e4fabe9c4f966dd0e2bf))
* 专家库蓝湖图设计更改 ([9a60674](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/9a60674b02b9bef87fbcccddda3b0bc0df5f7f5d))
* **专家库:** 如果不是管理员连菜单路由都看不到，增加限制 ([de400de](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/de400de70d8e0c41200210054b62b129f6dd6ccb))
* 专家库信息配置 ([ed56d7b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ed56d7b675e257c31efd7ae5c4726233744abd71))
* 专家库样式修改 ([ad0bd1e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ad0bd1ed8b569270601179d02d5be53c68851611))
* 专家入库 ([42d35c4](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/42d35c4bd93a7d7d2ffbea97874269e8cbf7755b))
* **专家入库申请:** 必填项更改。籍贯、工作单位、单位性质、职称、评定时间 ([460455d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/460455d28d6b5071409008fd65f8cc0376830aca))
* 专家入库走流程 ([95fab2e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/95fab2e4e58d84153b83e99bde24e63001ce413a))
* **专家使用:** 自定义表格优化 ([aadce73](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/aadce73f21b2c023e9da2b1eaba299898538367c))
* **专家使用:** 自定义选人组件宽度调整 ([2450bdb](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/2450bdb516d892dc5620d12d5efbe50e026edf62))
* 专家信息 ([e61efc9](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e61efc93aa0bef07bb6acb28bd597d8a5c4487ce))
* 转发记录样式调整 ([674ac3a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/674ac3a8882ffc0d912416cedbefcd80ec7e11ff))
* 转发聊天记录ui ([c180b26](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c180b26d5828b0a20639d03b0629b06fc52218fe))
* 转发群聊名称bug ([a4e6428](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a4e642882e405cc075a54744ce75f874465781f4))
* 转发消息群的显示 ([0c4fe0c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0c4fe0c4acd186542b0d41eaaaaeee3fccc34131))
* 转发消息时过滤掉机器人 ([e08a2f0](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e08a2f0094b3b00bdd9083c80f32bf9886eb3ce5))
* 状态 ([6b407cf](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/6b407cf991c2aa72cb11b1568d5a02d0f68f614d))
* 状态和路由 ([e349530](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e34953062584571c87f40d6ca0465e4954f33413))
* 状态文字显示方便阅读 ([a96fbaa](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a96fbaa5520f15ec2cd9ed055f059b45658f03dc))
* 状态渲染 ([7b0098d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7b0098d6607ee83e2e8a6f050454f78275bd1a08))
* 准备开发右键关闭功能 ([9432aed](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/9432aed71932af94ef2fae0b3366bf8e9a688837))
* 桌面端区分视图获取数据 ([a67c34f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a67c34f4661b6845186a1557a195f6679156d581))
* 桌面端搜索关键词凸显 ([106a314](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/106a314f66070414219882584ffad3b1cc0ac820))
* 字段 ([7084d89](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7084d89fd99c34a85af57e29ba9ce1833ce08e09))
* 字段取值 ([81c8743](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/81c874315bf9debb0d5728ff8adc6fb48ad78d44))
* 字段缺失兼容 ([b3c2ca5](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b3c2ca5ce5119ad998e8cd3a4c44f27555a4a3f4))
* 字段优化 ([a0c7f65](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a0c7f654d3ba44728d967e49025014b8a8176f28))
* 字数行数的限制 ([3611b21](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/3611b211c302bdbc0de0de4507a3b890885c4ba5))
* 自定义表单打印和消息增加弹窗评价 ([522b0fc](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/522b0fc77050ac191a80a96e8b770edcb5155dac))
* 自定义表单打印优化 ([d967afe](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d967afed45476d14ce2d8d1ab8188b4f3672f4e3))
* 自定义表单打印增加标题 ([ff05712](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ff0571298e40b7d4a0147c0a7a97e9392ae6589e))
* 自定义表单新增不调用详情接口 ([4d57442](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/4d574425743874f83b3af0d530baf630eb46c4f7))
* 自定义表单增加查看刷新内容 ([652d236](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/652d23668aad54610953046134ff844174787bf9))
* 自定义动态表单 ([0b591df](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0b591df0812f1b9e528a59771939574028b5d800))
* 自动聚焦 ([ee7977a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ee7977ac639d7ef58c0110278272d3a70da045e0))
* 自动聚焦 ([c99ebb6](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c99ebb65bde4bb0da6f8ff8f5f6379a40072cdf7))
* 自己独特的icon命名 ([b547dd0](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b547dd0d4a2f4dd849b12956552c478f948211b9))
* 子业务删除多余的api ([c58120b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c58120b68f317b58957e7a7969278ba0697d1656))
* 综合管理模块表格分页统一样式，培训记录模块接口对接 ([72a1818](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/72a18187f5dcd651439bf080719b15657d8d6b6b))
* 综合管理模块优化树组织展示，培训记录列表和新增接口对接 ([9e35afd](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/9e35afd1f420d702e7d9d52dc066f308365bbc7e))
* 租户切换提取组件 ([56ac22d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/56ac22dd328d3e1e4dcfcfc7026d525960a13753))
* 组件布局宽高百分比计算 ([890eb75](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/890eb756a49a6f2f2924043a19a5245f10db7c4f))
* 组件方式引用 ([0a593ac](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0a593acfebc6053bad014a9eedf2b22f2c10b672))
* 组件更新 ([25ce4ff](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/25ce4ff38bff099b0cb1f4f6b39db00889691283))
* 组件配置 ([67e024b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/67e024b73fbcf0ec825d4525eabf2ececb2a15bf))
* 组件全局注册 ([96e0c98](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/96e0c98a24b8cc390e9ecaf75ffedf705e58d1f6))
* 组件同步 ([ff947b6](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ff947b64cb89b552264057c0aba186ff95d21ac9))
* 组件同步 ([68a5403](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/68a54033be30bc1547edc5dd151ded4eadad62fa))
* 组件同步 ([15f9dd6](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/15f9dd6b4822ea6fc20bd0283339fbaf0488b9e2))
* 组件未接样式调整 ([247180b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/247180b149420f87d8f89d96cc341ad3a54b492b))
* 组件映射 ([953cf57](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/953cf5784040ae550750b1899a37ed5b71743f86))
* 组件注释 ([485f109](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/485f1095bf3a08c146740a27a48294c171a5473f))
* 组织人员选择默认图标 ([86e8c80](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/86e8c803ffa5928b6ae51dcac233d0648f27936e))
* 钻取模式的文字描述 ([878113d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/878113de5be28055df95b3fc89e30e2acb1a1772))
* 最小宽度 ([347ea7e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/347ea7e9d0a6f39b7584e11b8aa41662da6bf428))
* 最小宽度 ([9cf0df6](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/9cf0df64f77e27f7650eb68c05ae52ee4b2178a9))
* AI大模型修改参数 ([5346fe4](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5346fe4bdb9193cbe24aae983e985ca00f5bc52b))
* ai快捷入口 ([e8fb284](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e8fb284df18d054bf7e33d5079f70ef6eac13968))
* ai入口弹窗样式调整 ([5229c42](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5229c4269fcec475dfb551b510caf34662b6c010))
* ai入口的边界 ([b244389](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b244389ce81935265f0e4a3776850e912e57563d))
* ai入口靠边的效果 ([f57c0e0](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f57c0e0fca1e09bcc6fbe26d9e221f4fd363d531))
* ai入口控制 ([2e1460d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/2e1460de38563aefe54a94073259d463856ab75a))
* **api:** 重构api的目录结构 ([ac09ac0](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ac09ac06985121e2003661e7b2d174c926023cba))
* app备忘录的渲染逻辑 ([da0d4b1](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/da0d4b1fb3f5dc6a1e8d12bbe9789008e3468107))
* app下载弹窗提示文字 ([c5b5c8b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c5b5c8ba260b6ace5de3185d79765e1b814f97f7))
* bug修复 ([d625589](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d62558926cff2768c43f8702efcd0d754145fd4e))
* bug修改 ([95bbc32](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/95bbc322f004421587ae9d93088c9d1d0fc35249))
* bug修改 ([a40784b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a40784b534129c6d4b7157d8e61817c60506ee22))
* cdn动态加载 ([3e42b9e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/3e42b9e65cc4e38fc69a07b1c35f56d181786447))
* cdn固定列表 ([2eea2f2](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/2eea2f27a877a09aa2ff5b64a09cd909793eabe1))
* **compoennts:** 修改选人组件 ([c8edb75](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c8edb7549a70698f97bf64044fe429a4f42d745f))
* **component:** 修复组件库bug，优化组件 ([cfe9580](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/cfe9580c1d5f12964edf0cee1e884ba7f2e4db39))
* **component:** 修复pagination、table、searchTable分页无效的bug ([66d6a71](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/66d6a71e1d6c530d9c7614fcf6eb4a78958c1db5))
* **component:** 组件库修复上传bug ([dc34fb0](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/dc34fb072aa523d0d3da34305830664ac28be80f))
* **component:** 组件库修复bug ([7bb5b66](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7bb5b665e4551c97fa59b229522761ee99a92359))
* **components:** 工作台兼容集成模式和综管模式 ([24d3db0](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/24d3db0fa3bb6f1576a471be60baad13fc104f96))
* **components:** 供应商管理抽离头部和搜索栏并进行封装 ([b176808](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b176808207af14f39231f9ad9ae644fb7f0f4c1e))
* **components:** 提取头部和定制选择框组件 ([3b47261](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/3b472610eec10fb2a7694db80e3ba6ac8d2437a5))
* **components:** 提取ecahrts图表组件 ([7604eac](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7604eac44ed9e2d69030dba37ebbdde581489d64))
* **components:** 修复待办处理时标题及按钮位置 ([d0304e7](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d0304e7ccf1de49db023d0ec823c1d10e7131be5))
* **components:** 修复待办错误警告 ([a0dfdc3](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a0dfdc3b7d653f1d68dfce722d45a20906c4f59d))
* **components:** 修复待办事项显示bug ([721442d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/721442df39679fb76dc4c8ce4c961b83e1fc72b9))
* **components:** 修复多租户时切换租户未更新用户版本的bug ([01d689e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/01d689e61e776b0da99713171127b375f4d14fbb))
* **components:** 修复日期组件bug ([9e79545](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/9e79545fc4cc978337225d7f3266da18ef2ad890))
* **components:** 修复上传组件的问题 ([6dfac81](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/6dfac810015e83adc1aeb1d3934756ee09739421))
* **components:** 样式优化 ([2537b53](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/2537b534a22700ab79046867328928617387699c))
* **components:** 优化日程编辑选择项目的交互 ([845c897](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/845c897c6b6d05a78ab06badcf77ea720ae4ddf4))
* cookie调整 ([e2d0575](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e2d0575cc184003f16a8dffdcc0b4bdf64c5fd8b))
* coos机器人助手对话重构 ([3169b02](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/3169b027e575d9725db1765d4c96dc052eb1ec3f))
* coos机器人助手详情跳转 ([73d6689](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/73d6689774ef5a4a1888d167a162464bda1b51a6))
* coos集成im初版 ([19c852a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/19c852ae136747e17f33e7e3ed5a385c6f5ff852))
* coos交互 ([38da457](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/38da457d354ff6be52ba4f7bcd6579376d46ce33))
* coos目录结构 ([a339cff](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a339cff11ef248b6bee16220a23bf6522afd5efb))
* coos问答交互 ([e0c3b1e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e0c3b1e6da9be36193038ea4e027de28257334b3))
* coos智能对话ui重构 ([40eefce](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/40eefce7da87fa88e3845b861fe37adb3e074ae9))
* coos助手问答调整 ([dbcc1ac](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/dbcc1ac6eaf0d612c10abb43a0cd0cade163c0c7))
* **doc:** 更新文档 ([b7416f0](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b7416f088b77d39653dc920426cd242ebccd74dd))
* **doc:** 完善组件文档 ([1359a49](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/1359a490094df929a9d7e6acfbe1a2580fabc7e4))
* **doc:** 修改文档 ([3e4c128](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/3e4c12849ad9e4ff42d008352ea2c299dd04758d))
* **doc:** 修改组件文档 ([a1a7ff4](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a1a7ff4e771e3e380bee27ee5e5867d98f776170))
* **ecahrtcompoennts:** 图表自适应 ([6ac34be](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/6ac34bef48f9e313d20d1d089d5c707dc5e84501))
* eslint校验 ([c04c614](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c04c61447156fab0f2b156eb7a1ad5bad389e4f2))
* git-pull-all.sh ([ba134b4](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ba134b4bcaed5588401eab0bbedf10ea4d614226))
* icon刷新 ([dcd37dc](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/dcd37dc2e98d9d26fb71ef8c2d2bfe6f1fe1fbd2))
* iframe ([e61f20f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e61f20fd1a34598f6e260c83a910e81a48e247b7))
* iframe点击关闭弹窗 ([59fd6de](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/59fd6deb1c554a4995a05b8369b3ab9f2a364225))
* iframe加载监听 ([916648d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/916648d6fe6c39850cd4ebd6542bc5939eb2f488))
* iframe逻辑 ([1f86615](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/1f86615f654cf0be6c74c7a3c76562f1122b34eb))
* im的代理 ([71a9acd](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/71a9acd843c53cd643863192fb59984f48a3ddca))
* im登录对接 ([ffec207](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ffec20759480ac708e4ecf433a5920b65b02ed23))
* im调试线上配置 ([0d6ea04](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0d6ea0417b983d45c8e911abe4ebe35784ae888b))
* im调整消息显示分类 ([d2bfa2b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d2bfa2b9d72c3c53df01a7f3c8d8601fbdaca0ad))
* im对接 ([0744b2b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0744b2bf9e4e1de371abb08bbf7d93e9dc639d42))
* im对接 ([3ca7fb4](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/3ca7fb44a676d4d0b3abad115751bfb15b1361ba))
* im服务更换 ([c84c7a0](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c84c7a03f1e348f71260ad894b25269c5196258d))
* im服务切换为德阳云环境 ([0c65770](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0c65770131f9399f2210a0f0c06c4d62c2c397fd))
* im功能模块 ([b42ecdf](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b42ecdf74a5be6ac1c3836c70279fe8aca5ba1e6))
* im缓存隔离 ([67c5358](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/67c5358694da9a2325955ba3350abd6b152b2ec0))
* im会话列表取消默认头像、历史搜索输入框提示修改 ([440c3c2](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/440c3c2c1287e532d1e12fcbe344a4a8098161a5))
* im聊天初版 ([d3352b8](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d3352b80aa32c89b8624597cf83f766d2bdda19f))
* im聊天优化 ([4f2db19](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/4f2db19f579a07c41f4e86ef325ead38c5e4dcd8))
* im路由 ([9e701fc](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/9e701fc793aacfa2337452e168b697d32080e0d5))
* im抛出异常处理 ([27916db](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/27916db36d121e278dd5baa8d0a9fe00886eb64d))
* im配置修改 ([326e624](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/326e6246b19847d075ab9f6016b40f7174687c8d))
* IM切换租户的更新 ([cec5fb6](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/cec5fb67a4915c718484afd3d2ff00daa869efbf))
* im人员搜索跳转对接 ([6480114](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/6480114e708338141c9e6bf337139fd437ec4a76))
* im样式修改初版 ([0b764c3](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0b764c3c43639d1e5d99e638dce445133b83c13d))
* im依赖 ([c1364be](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c1364be99e9517a48f803803a198f11a224dce3c))
* im依赖对接 ([b18b7bc](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b18b7bc71e611f129c805bb9d8bcbe4ba409d22d))
* im用户编号调整 ([6b561ee](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/6b561eec4d551c9b35c7c0977b23a842bc5af18e))
* im用户相关调整 ([d600032](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d6000329a80f541d8020d4e31686262219e6b030))
* im用户id拼接调整 ([da36063](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/da36063dcbd46c1b268062bbe0763bdd0e758caf))
* **inspection:** 修改样式问题 ([5f0e0a1](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5f0e0a1e7bc0ecb37b2a0849cc60b724bd964fb6))
* logo获取显示 ([71db90a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/71db90ae8e7cf9199bf749c849401ee00c6f5e9a))
* markdown处理 ([634676d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/634676d377ce4824aa8192a8895b9d84e63e255f))
* **other:** 代码优化 ([01a98fd](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/01a98fdb2616658a056ad93736bb3914d2e48732))
* **other:** 第二次上传文件返回数据错误问题 ([1b07fca](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/1b07fca0a4d86698d01195cf1206aa7aa80317d6))
* **other:** 静态页面搭建以及相关接口对接 ([97d8f37](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/97d8f37b550f10b03a7b3279c34f6dda271e670b))
* **other:** 列表显示问题 ([70070c2](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/70070c23057564a99f3f6c6c62207ebc3a0c7c4e))
* **other:** 权限问题 ([1ae646d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/1ae646d9f826d0f40cda456af757157a25890bd9))
* **other:** 权限问题 ([19a3539](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/19a35395987f3f85f52651d27ba4caa0eb3cbb9c))
* **other:** 权限问题 ([5e0c31c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5e0c31c94c02e8dd764824b9fc23f0f7ef56564b))
* **other:** 上传文件刷新问题 ([c8f6a95](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c8f6a95211243f4a7f872ea7c1fe7c1d30d54ff3))
* **other:** 收藏跳转后新建文件问题 ([451f768](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/451f7684aedc75c0de7408cfc8ad3abe65a61da5))
* **other:** 收藏中取消收藏权限判断 ([5704407](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/570440739c3ef55195e5bf612e1da2c2ece7aea6))
* **other:** 图标问题 ([dc0d00e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/dc0d00ece56ffca98f03a9732ab44eb9b66337ee))
* **other:** 文件搜索后更换层级新建文件问题 ([52c0059](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/52c00590b5cbe244728ef9cffa4dc9c0583e4f47))
* **other:** 新建文件与上传显示问题 ([67c6611](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/67c6611114251901fc3380d00a15b5ab77bbf01a))
* **other:** 优化代码 ([b295556](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b2955561ae5d4b7ea85adac6c8c0e18e32c1902d))
* **other:** 优化代码逻辑 ([7826548](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/782654841e4c8d32e495c02b6426d10be1302e6c))
* **other:** 预览方式更改 ([84d6822](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/84d6822103eadd5468ee8e0aa6310e3f6abb86bb))
* **overview:** 拆分业务组件 ([f8d4050](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f8d40505a4b64bf81e01ee63d68f2712057428d8))
* pc端待办单独的搜索 ([7e06eba](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7e06eba3443250ea20e277399d68915d84e2d116))
* PC框架动态控制 ([c26368d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c26368d66d00b94507c4759c2b3e64b84892164d))
* postMessage打开外部链接 ([bbc6425](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/bbc642538f0c995abf127934702e5e737c33a4d9))
* ppt应用配置 ([8a374db](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/8a374dbdda2d15be0d387c18d15c67e4f2f24508))
* pre标签换行的问题 ([c219a1f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c219a1fe732f30b79c2099ed7d2cf403f5b2b5cf))
* showAppDownload == 'true' ([e1ae68d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e1ae68deb39e96358cf4b6cffc99f9e0e3cb9b62))
* socket链接通信配置 ([0c52d0c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0c52d0c177bae17258217e52600c5e4f2c91e214))
* socket通信 ([94b4ee9](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/94b4ee954e0963b9d342d5b5375162954fa06bc1))
* **style:** 全局主题色的更改 ([5b72a41](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5b72a41edfab8f602b54a21e5eae1afc23a21466))
* **styles:** 调整按钮项右边距 ([dade7fe](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/dade7fecde6df1d4f25ad6ee96cdaeec8a9cbf8e))
* **styles:** 调整按钮项右边距至12px ([a8d9692](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a8d969268b936dc78eb8365c93af8ab8511b7def))
* svg图标色 ([fb8c01b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/fb8c01b61a70a85d1dd87b9c7d15c5bb3f9e9385))
* svg修改 ([506a4bf](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/506a4bf26c24afec781b6d8e4fc03c3ca781d07c))
* switchState ([0306580](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/03065807dcb6c6aa53ea4436ad7d489e90affecf))
* table滚动条 ([69ead1c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/69ead1c04330b9f1730c7a14b46564f4ccbfeb85))
* table间距 ([0fa8753](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0fa8753c4c7f80e9c7ed9a1901b5e5e9a9a96964))
* table展示 ([f3ed32a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f3ed32afe7608e606c821d307a87e0206b7df776))
* table字体 ([f00de04](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f00de0437763921460f2bac7d036e6d38de71a5b))
* test ([bcb8d87](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/bcb8d871adb567b42273a536beb0f46bb7afd2cb))
* tinymce默认高度修改为360 ([4cf5de8](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/4cf5de8398b26e8cc022b330cf4903a131d9ef69))
* **ui:** 待办相关 ([88cfd0d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/88cfd0d23d3ba6285315abc95b39238bcd79cb06))
* **ui:** 待办相关 ([c1ac916](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c1ac9161a8f9997a99815e52ae7b1af4f094607f))
* **ui:** 待办相关展示 ([c7d767a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c7d767a344ddff79f8218a916b323e55e7a84005))
* **ui:** 待办详情调整 ([14363a5](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/14363a504c16b2acac3601e9553728b967a57f61))
* **ui:** 待办样式 ([753029d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/753029df0b2064f9090928a40a1388e7da148afb))
* ui交互优化 ([6e79629](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/6e7962902ea6313b42883e4fdab42e1e3b18b311))
* **ui:** 解决警告以及头像调整 ([59a2cd8](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/59a2cd84eab9f2254f43be84e8c49f9694f60c05))
* **ui:** 进度条样式 ([e0b64c2](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e0b64c261b762b605ea5f33f3595cd26966e65a4))
* **ui:** 进度条样式 ([9d68df6](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/9d68df6726815abc4952d5a5fb8688fbd0f6af0a))
* **ui:** 进度条样式 ([ad3ba92](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ad3ba9205e33e6bcf96aaca7e970466069423922))
* **ui:** 列表分页显示问题 ([0de5b63](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0de5b63c2e0aa63b40e6091f3982e23bc40db470))
* **ui:** 列表为空显示 ([3250c12](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/3250c12bfacf5c7c726a4d958920ff259500666d))
* **ui:** 列表样式问题 ([bb8f21c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/bb8f21c1039a69c96f74fb1889e996849d5ec5bb))
* **ui:** 列表展示 ([4737001](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/4737001ff56388e7869f181b30c0183b51830923))
* **ui:** 收藏跳转后上传后问题 ([a126813](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a126813032f05be5729fb7d6aa1e5adbddaeb891))
* **ui:** 树形菜单展示svg ([c1071c3](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c1071c32a6dced0fad14f0ff5c540dba034c1ead))
* **ui:** 树形列表选中样式的修改 ([edebf3d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/edebf3dcbbc757f0a443d0ae94c2defdced5096d))
* **ui:** 通讯录层级显示问题 ([464e4fb](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/464e4fb719ad7bea67121db1e9267ada475c4cb0))
* **ui:** 图标间距 ([771e4c8](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/771e4c8e10f2519ab8bf6d8391e499479f934ab2))
* **ui:** 文档图标间距微调 ([2ffe321](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/2ffe321b7382eab8cebdc498775b9d5de99255d3))
* **ui:** 文档摘要相关 ([4ba7bcb](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/4ba7bcb864e1371beebabc365d675624510eb60d))
* **ui:** 文件名过长显示问题 ([ce23459](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ce23459423742a741e58bb1c963018801b485526))
* **ui:** 文件名字过长问题 ([749b8f1](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/749b8f17b0ee85d15b5d2cc6764937f6b3becf7c))
* **ui:** 性别展示图标区别 ([d39d6cb](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d39d6cb126645958028c8fe110c67f4b566fbfdf))
* **ui:** 选中颜色 ([4808355](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/480835502c1e261dea274e08f75f36f5209a535d))
* **ui:** 选中样式 ([b88e169](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b88e169ad82f9f0b7f2f6dab25429c659f016faf))
* **ui:** 渲染问题 ([5e3f248](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5e3f248be43771099e9ed3d20cec2de9f578b2fa))
* ui样式调整 ([424ed35](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/424ed3583aa8c300227a6a9aead62df0aa783b57))
* **ui:** 样式优化 ([f81c6bd](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f81c6bda76b39923e8eee62fb86ddede9fb588ba))
* **ui:** 摘要显示层级问题 ([24de9e4](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/24de9e4b3a5477bb1a22f376dadee260548744ef))
* **ui:** 摘要显示全文内容,并增加滚动 ([33eeb9d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/33eeb9d2919173a3a27cbb8a3a5e487552694ca2))
* **ui:** 摘要相关 ([7289739](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7289739f75d867d0af4febc9f67f60a773951999))
* **ui:** 摘要展示问题 ([31c0cb1](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/31c0cb173484b39e9ce0efda4df4e354b3b51ec8))
* **ui:** 知识库容量无限制时展示 ([30e2209](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/30e2209e27f127c3cbb29d787a4e28bf575248b9))
* **ui:** 重命名文件名重复提示 ([6eef402](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/6eef40206c35402f97c0edda426c5290ae271364))
* **ui:** hover样式修改 ([2434295](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/24342953f411f8d01cccba6cd68d82413a9d0079))
* umd单文件选着性打包 ([8eb0b53](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/8eb0b53fe725e96c99ef40761a2470f3683592bb))
* umd根据dom判断运行环境 ([0333995](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0333995ac490c381c78d1d23301d5d28b49de119))
* umd映射 ([3f4641f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/3f4641f09f31d73fa81c2d26e7980eda59fd41d6))
* wangeditor ([4081422](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/408142227dfe2bf8c7bbb3b7a187aa8caeea8016))
* webview未正常更新的问题 ([2fbd021](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/2fbd0213c3811b06dcd421cc790eb378d90b42d4))
* window.open的时候野火逻辑判断 ([e8c1d0c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e8c1d0c417cac284b0c9504c612030bdde1acbad))
* z招投标 ([eb9e9e3](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/eb9e9e3eacba28480f13414ca8118039cf6b7e9f))


### Features

*  新增根据待办id打开弹窗 ([e6ab163](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e6ab163dfecf90dcde585713e8b5e7507f15e1ed))
* 备忘新增筛选 ([faf6e3d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/faf6e3d5d25fa8f0fbe90c6db9510c40ea4b9a88))
* 备忘选人修复bug ([f3d4964](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f3d4964d6f7df15af67d1f04fc055b566672f9a7))
* 备忘右键菜单事件 ([180b66f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/180b66f92a10d8bf4387b439c2174e551ce6c48b))
* 补充文件权限绑定 ([4a529b4](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/4a529b44c7e24e62fd7f400decb470938e6d0b2a))
* **采购管理:** 采购管理新增页面 ([7dc0bd3](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7dc0bd3f32c4f078e973cd9382175744d0cb6234))
* **采购管理:** add模块 ([baf51c3](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/baf51c3cf27b6dc375c4bbe521d8a3433546272f))
* 操作权限 ([0cbce90](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0cbce901af007d2640cdb44d4619153154926cfa))
* 常见问题功能 ([f6623d7](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f6623d7687a88bc209ad6d80aafb3eab94502eb0))
* 车辆管理接口对接 ([6c22e4a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/6c22e4abfeb250a2285b7c9a42098f080a0fe981))
* 车辆管理新增接口 ([c7fe9d6](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c7fe9d6aa2429ecac63de99bb3c3312679c99483))
* 车辆统计接口对接 ([e46e4ae](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e46e4ae3227e50599052a6d0a945ae0d5c62bcfe))
* 处理操作按钮问题 ([a72c14c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a72c14cc0cd3c7ddc3d717019e3ec8afcb90d37a))
* 处理流程表单回显问题 ([44f84ae](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/44f84ae2b5cf1dbf0ca065a5cb7b07289916cdc0))
* 传递身份id ([53c87e5](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/53c87e5b8d46fb7421d3bd2f38759d772aae1773))
* 大模型聊天窗口 ([0bccfea](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0bccfea260c21cfd4e810ed898af0437de4426f3))
* 大屏个模块添加，大屏布局优化 ([564b544](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/564b5446b51dc79381e32ea93a52a9ad5180d5f3))
* 待办小组件开发 ([7581653](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/758165364bb9c73ea81a4945e4f6f6a3a0dcde96))
* 登录接口对接 ([1867ee0](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/1867ee0a06b764d03ace76c8ca3638d79dbbd439))
* 第三种框架模式开发 ([817e633](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/817e6333a2ae5a7c6eb1143b1698707ce4a2462f))
* 点击组织显示全部人员 ([358d741](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/358d7411cf47eb6511e63fae7ef3ade269d22f1e))
* 点击menu进入 ([26de34a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/26de34aedb3bc50bca91725fa90c1260170fa79b))
* 电站总览联调 ([17ee6c3](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/17ee6c33af62ea7781e21ce003b63439a8f65e79))
* 调试日程编辑 ([f8bb982](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f8bb982710c5440589c5afc6e93385cfece4583e))
* 调试日程列表主题 ([b79ef35](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b79ef35b975eb419f78e365c89320465a2cc534a))
* 调试主题 ([34dc42d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/34dc42d3ba8f8a7084a54f55ddc3ce976257be1d))
* 短信登录 ([701d46e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/701d46e92b7a4e01de1ff2809c95c67cefb98d70))
* 多选后选中框bug调整 ([4815845](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/4815845c8e4bdbb6f97a0bb2515ea20dc8131c35))
* 多选屏蔽收藏新增删除确认弹窗 ([7fbb091](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7fbb0917bf8600ef4933314af87555253a8d41a0))
* 分区总览页面开发 ([f2a90c2](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f2a90c22840d5290489c7cac2dbc877bdc417677))
* 告警总览列表，弹窗处理，列表接口对接。视频监控列表，视频设置模块开发 ([b3614fc](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b3614fc32c886dffc5ee743d1467e12d7d3853b4))
* 更新下优化 ([6a7d765](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/6a7d7654fbf9367e3c594cfc28b48da1ef7d8774))
* 更新项目描述 ([c5ff85f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c5ff85f90f7b8ccd14ce331dfef6c9d213d6b8ee))
* 工作台改版、工作台接口对接、驾驶员备案页面编写、驾驶员备案签名组件 ([9fd525a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/9fd525a98d5288e0e8f5aaeb4de2c8e839e7eb33))
* 工作台面包屑静态 ([ee859f0](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ee859f0121d7b88b95ae86837bc92706f54e3bb1))
* 合同信息模块列表和弹窗开发，车辆信息模块列表和弹窗开发。 ([82b5c07](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/82b5c077902635b15d4dc70d1f274656afee0f66))
* 回收站回收功能 ([a7bd0e0](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a7bd0e05680b1ab3cb766d48c5b69ba1cb35d4b2))
* 驾驶员备案接口对接 ([8b196b1](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/8b196b10967a46643539fd9a5c67d1452a9eca3c))
* 将流程单独成组件 ([730f58c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/730f58c714ff62832df4d4bd4c10a4541a878848))
* 开始联调日程备忘的编辑 ([6397437](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/6397437ad69fec07eafb57f3f74766da8e5675b8))
* 科研成果 ([dfebce3](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/dfebce39e5891ad131a5cfef75ee654c21165e01))
* 科研成果 ([6f70a77](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/6f70a77a50e3d684062f85b56427bd6ee6874a79))
* 科研预申报流程 ([e96e993](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e96e993804a7dc034cc968e4bd3a2135c1f3b247))
* 来源调整 ([0f9b9be](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0f9b9bef98a5e8e4c43056a1f55c8e8a6e3dfc1e))
* 联调切换身份,修复日程bug ([27a7689](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/27a7689b4191c61ee1ab5816082a10fe4c9b8125))
* 联调切换身份,修复日程bug ([d9c4344](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d9c43442745c82956581296ca74cb2ac0bea238b))
* 联调图表 ([f0677bf](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f0677bfbcf6dfadf30f5947aa16827b64fa21ccc))
* 聊天界面个人信息弹窗 ([1816fd1](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/1816fd1cafe42e634f1a28595170034d1f5ecf09))
* 密码修改接口对接 ([5026407](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/50264079caabe6fa5556f32c00e27a41473efff1))
* 模块相关展示逻辑 ([f9c661a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f9c661ad0c7171941cd3d822a49c8def41b6e2fb))
* 目录结构规范 ([27c4bd0](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/27c4bd0959a23e774bfee5b9d22706d91414fde3))
* 逆变器详情弹窗联调开发 ([fa646a1](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/fa646a107af5ce2f0700394d0ee7f45a9c919891))
* 屏蔽通知其他窗口 ([6a96787](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/6a96787e09cdcb5cc6ae0432a43bd4b835d64b4f))
* 区域设置页面开发 ([ec660c5](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ec660c51fb0a823697447f5341166cf5f6a04f97))
* 权限问题 ([cf35721](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/cf35721738ca41d546faca2e1d8240c40a10793d))
* 人员持证模块列表和弹窗内容研发，培训记录模块列表和弹窗内容研发，信息档案模块列表 页面开发 ([3fbd603](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/3fbd603a779b6a78354ac7813fdd15ca0593fe98))
* 日程/备忘详情调整 ([6b9c258](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/6b9c2584e16c261c48790f4071f47069ec862800))
* 日程备忘优化 ([5b7e94f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5b7e94f9b82c148f5638c552c6542145496e347a))
* 日程备忘优化 ([5b491e8](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5b491e883bfce73c9d65dc40790cf9e0909a4987))
* 日历备忘详情及操作联调 ([2cadb19](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/2cadb1950e7ecd41968d9839bf23e6e8392a7328))
* 日历工作备忘 ([c448879](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c448879411330e12d17668bbf3c4792032348df8))
* 日历模块-工作备忘功能添加 ([a0ce0c3](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a0ce0c3e70f6cf85192763abc60d4bc4c989145c))
* 日历全局搜索 ([fb6d527](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/fb6d52716163dc26a467c76bfbbf62216a9291bf))
* 日历日程/备忘详情及日程列表调整 ([63757e3](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/63757e3d28a3c5621f58affb6547b0d55716526d))
* 日历日程编辑开发 ([2a2b7be](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/2a2b7be4a9be809abe05f078e8e270f98a542f96))
* 日历日程编辑开始开发 ([be509fc](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/be509fc2334174a3267ca2f072f67deea201cf1e))
* 日历日程的项目选择开发 ([cea763d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/cea763dd668b652fcf22de0f489661f23399c8a3))
* 日历跳转bug优化 ([e13cfed](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e13cfed0c8a4625409511395a96c35de8b4f0582))
* 日历详情样式调整 ([04f4162](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/04f4162d9066b12d9631774abdd5ac8f6e3e8c2b))
* 日历样式调整 ([333acb0](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/333acb0a7b0af3aef17223ee8554c9a1a7e2796a))
* 日历样式调整和待办跳转bug解决 ([186ae21](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/186ae2160fbb6af5229043cf73d4013cf473d005))
* 日历页面拆分 ([67a062b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/67a062b2a96991ad4cd533dc0cc761e0a7c0c8d8))
* 日历页面开发 ([5507f3f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5507f3f8fdf4f61bb72a8a0ed2dc17bc45923d93))
* 删除弹窗样式 ([dcbce79](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/dcbce7980c1f5df400bf0c5be47abe9a3d9ca7aa))
* 上传组件多文件接口 ([15e932e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/15e932ec961b5de909c014d385d47f5caeee66ad))
* 设备监视ui改版、天气接口对接、新增echart图、echart接口对接 ([18ff01b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/18ff01b7bb44c2397cd13dd66966ca0ebc3c561e))
* 使用说明和公用样式 ([8869ec9](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/8869ec9b1a1a98a8f77563abbd38212df9bdbc93))
* 是否收藏功能 ([f60b2dc](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f60b2dc0e1c7efc02d262977361eeb819bdb5f6b))
* 收藏功能完成 ([9ca8b8e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/9ca8b8e85a0fb885413ce19d1af97e4c8afdd517))
* 收藏与回收站不能点击 ([fb5a7cc](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/fb5a7cc3d2237da170c1b72b6cec0ca8bda0d4e9))
* 首页构建 ([00b67c5](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/00b67c55f2e2f323f433dfe8d8e82a12e9a89ee3))
* 首页文件 ([8b1f697](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/8b1f697ba9b0f52f0e979526b91c2881944c729a))
* 树形结构的开发 ([1b5d426](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/1b5d4260b7c8e9f38d9a39de211c5c61daa47edb))
* 提交代码解决冲突 ([a2ec445](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a2ec4454db5564a77ef5dfaeb937b99daf6ea842))
* 提交电量管理的三个模块 ([f8eb3a6](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f8eb3a603d069b940f80825d40fbb6b0a4f62179))
* 提交电量管理模块 ([de2f7d1](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/de2f7d16607fc909fe26834215551150d6319321))
* 提交电量管理模块 ([267846d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/267846d3d02ae8584cdae80a091f037936e15f82))
* 添加 Excel 导入组件及相关功能 ([b279771](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b2797717b2ebcbf27930c91d3a38004fefb94dbc))
* 添加403无权访问页面及路由配置 ([0fd634b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0fd634b08a11cdcfe79bd2a93a077038566b32d7))
* 添加白名单 ([43b8f8b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/43b8f8b64e1743b46d8aa5497285fba4bf7dd5cd))
* 添加文档说明 ([6c65c69](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/6c65c6937a2fdb5a03b3da117106b2c982d4955e))
* 通过postmessage提交表单,流程的数据 ([358de02](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/358de02212347651c2f49aa3c4d34646115baa23))
* 图片预览 ([c8eccf5](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c8eccf5e0eee8c7d4da0534da8e4a6c004806d83))
* 外部表单,暂时不需要验证外部表单的数据 ([5fc896b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5fc896b622c04bc2a195baf2518ca6b869060908))
* 外部表单,暂时不需要验证外部表单的数据 ([4b0e0d2](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/4b0e0d2fb8dddb9ecb424694a17771ac04a2faed))
* 文档新增文件移动功能 ([bedeb96](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/bedeb9632c3b1b3f768d80138c92c2b95debf7fb))
* 文件是否置顶 ([0b0fb09](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0b0fb097cdb95b7d7fc634aa1f06985276bb4ee4))
* 文件移动相关 ([f76f8dd](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f76f8dd44ed787643dc436c8f087c5fc2a93f8f0))
* 文件用印申请联调 ([ee30b77](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ee30b77c011ad7e4ff45865821235919da7de26e))
* 文印申请 ([7220bb1](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7220bb1219614b78b9a3db6dfee58a3c91cdd3de))
* 我的空间文件或文件夹移动 ([e80c276](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e80c2767fba30d4d6bcedc88f874b1be151b8e4e))
* 我的空间coos助手接口对接 ([e210ccb](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e210ccb6533edc7818e3236b01b2501fe5a15485))
* 无人机时间管理接口联调 ([149fde1](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/149fde1e382f1bd15ee735a8183bae0f5c2663cf))
* 下拉组件 ([37c0321](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/37c0321ffdb6e37206fbfb1cd3fad4ebc3f7eb44))
* 消息样式调整 ([000cceb](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/000ccebef25b0c0237d4ae07f9ba0d0a2ad6ef7c))
* 消息引用样式 ([9dca79c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/9dca79cf4a3d9f8721b3c610a68b95cc3f262842))
* 新建台账和管理后台页面 ([7ae4498](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7ae4498879538383fd6d4fcab83ac2327b8dbe23))
* 新建文件相关 ([43db78f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/43db78fabd729595705b6741a3be586ec06356b7))
* 新建印章申请界面 ([b7b52cd](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b7b52cd11bbc47ab016d4aa64431b714da355879))
* 新增安全配置管理 ([29f1b53](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/29f1b53443f95272886a8ea826a24603bcfc8e10))
* 新增插件下载 ([7a71db7](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7a71db7c38f98cec55c50a8d340d7170b7f8a2f3))
* 新增车俩管理员页面 ([a950a45](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a950a453c507ae0f4995e6aa75ac2c82d15f9a60))
* 新增车辆信息页面、修改印章部门公司切换问题 ([e475168](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e475168ce57afa03e96aa0659e78b67f40b39458))
* 新增打开添加弹窗校验 ([87337cd](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/87337cde18444d630daffcb19823b929f8cf498d))
* 新增大模型生成ppt ([ed89ff0](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ed89ff0c91394c44bbe27a33a9541b2df8f867dd))
* 新增大屏功能 ([ed53ff3](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ed53ff35fd6c3792f7b5ee9959e656c348ec8c9c))
* 新增第三种模式 ([8fa8b5a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/8fa8b5a01719186fa768529fe9fbc7bda28903a7))
* 新增电站接口联调 ([4ee38c8](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/4ee38c8bf4e356a0c8d1c8e6f7d024eb317e1bf4))
* 新增电站总览页面 ([006e80b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/006e80b2c653475f4bb344bd625fadb86eb02192))
* 新增动画效果 ([0791c68](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0791c681aa0b92f27ba5cc79665974d862a39c2e))
* 新增飞行报告弹窗 ([1d616e9](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/1d616e9ee2bd9a9f8fadc7e4add4d943dcb71b6c))
* 新增复制链接功能 ([d56de18](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d56de18f13caacb8862dda6c488bcb2f65ca2456))
* 新增公共组件filebyid 预览文件 ([24ff701](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/24ff7010a70241fb27af0cd58a25928979bdb49d))
* 新增基础信息结算科目接口对接、电量结算接口对接 ([1783d1c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/1783d1ce62cfa35f878d081b26d70fe96f9e2de1))
* 新增驾驶员备案页面 ([ef5f680](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ef5f680857112d9ddb8b835ab59811347799c39c))
* 新增驾驶员管理接口对接、更新系统表单 ([e9c8d6e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e9c8d6e9ce7b3852827de71e68fec9882ca8d688))
* 新增驾驶员管理信息、更新系统表单 ([1daa7af](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/1daa7af98fc824ef6e7ae625bb05ad2e8f973c5f))
* 新增监控厂商信息模块，列表，弹窗，增删改查接口对接 ([eeec3e5](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/eeec3e5803a1b5ad3b22f813586dd600858bf0f4))
* 新增结算科目页面 ([42a0989](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/42a0989daa5bceb04f9073cd9413efd0b20f029d))
* 新增区域设置的页面 ([cb985e3](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/cb985e36185efc735c209bb0a5a492edd9e4ca4c))
* 新增申请管理 ([9e67e4b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/9e67e4b1219510bf185552016142fc1c75000d40))
* 新增时间查看 ([677bf0b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/677bf0bbb774dfc57bf83aec38fcb91dcefbba1b))
* 新增视频播放组件 ([ee5b2a2](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ee5b2a2a7c8c55a3e6969054d2a9a6e7cdceeb8e))
* 新增水印 ([3334fea](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/3334fea0a5b27aa2c9ed243d29ecf42089e34e26))
* 新增水印接口 ([ebaa192](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ebaa1929249bf3c988a96f79fd707ac22a4609bc))
* 新增搜索下拉公共参数样式 ([38954d2](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/38954d237cabd45b4b7a06d884fd5857f12c06ea))
* 新增通讯录管理 ([cde4d71](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/cde4d71cb3ca04c774fa60b8f11c355b215f88ee))
* 新增往来单位/细则指标 ([2c49bee](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/2c49beea563dd87906f49244d04c4a5a47400e75))
* 新增维修保养接口对接 ([ed554f0](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ed554f0aa320906d04d9f12f23c02bc58d0a80e8))
* 新增文档版本上传、新增待办列表coos对话组件 ([2a589bb](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/2a589bb21ab562fdc62867dd7eed644bb6a2c3fd))
* 新增无人机工单详情 ([9ee88e4](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/9ee88e42edf96ecfe2ea18cdbb983de3d653b10c))
* 新增无人机事件 ([7f0f4c9](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7f0f4c9cd3b937a6ad1ef8ca4f9e9d7fabc39288))
* 新增无人机事件 ([8787e8d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/8787e8d8fb170b41891ca883adaa8f6980110e84))
* 新增物料,往来单位 ([faa326d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/faa326d1649a016905b9b697e8e92c78fee06167))
* 新增细则管理模板、接口初步对接 ([016f6b3](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/016f6b31c7db33be9f934081acd09cf084f847ed))
* 新增细则指标 ([f114210](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f11421003c0324feb91b34769030fed8234a3ffd))
* 新增线损接口联调 ([f8df8c5](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f8df8c5639821c85362abe84ce44249f070942cc))
* 新增线损率填报 ([8ff0848](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/8ff0848a725865209bfb5c60762d85b57833b654))
* 新增消息记录弹窗、接口对接、消息跳转 ([dc94bd9](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/dc94bd9b758a215c9382af6a3dbbfc9e4f2b1edf))
* 新增消息水印、新增文件下载权限问题 ([f07ef97](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f07ef97bf927a9e712fe3638e89b9cc39e9507e2))
* 新增新能源运维管理模块模板 ([3d6d4d3](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/3d6d4d314804e0895fa19bd7bf4c881f0ae1165e))
* 新增业务用车图标、跳转 ([ba66bb4](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ba66bb4571d6e429f078c0578ab3633d0356dc08))
* 新增业务用车页面 ([1bd92e9](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/1bd92e9043eb3322272c8537b1c0bddc8a1e1066))
* 新增已读未读配置功能 ([6e4b7b1](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/6e4b7b1d8ce95a92027cf079959691039d75c4d8))
* 新增印章动态表单 ([e8bc837](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e8bc83724d03cee1fe327d557d21553975c8ad83))
* 新增应用超市路由、修改用车流程图展示问题 ([d23e526](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d23e5268e2509cae5f4a7ed8ae955b843caf94ae))
* 新增应用申请列表缺省图 ([8831b75](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/8831b75c99870fac837a7498eba74fff0d28d3f7))
* 新增用车管理文件夹 ([834242a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/834242a4e582dcc5f75fea852a680a51eb848d2b))
* 新增用车后台按钮权限校验 ([14c4beb](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/14c4beb8567ae3f94742f01e429bfb1ffcf117cd))
* 新增用车路由管理 ([412007f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/412007faca1bb39d095c5b77497b3d3d15c26c88))
* 新增用车申请页面 ([6b461e8](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/6b461e82b15668a08f3ef7779fa1f1a437ecc51d))
* 新增用户端消息徽标 ([4519bd7](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/4519bd705dc78f7a92e125113e90576e629082d4))
* 新增运维设置 ([8599e98](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/8599e982e78950698cabf4c2eaba72b6538c3daa))
* 新增运维设置 ([e7c4637](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e7c463780589a3e71035b2c6adea84dd23a78ed5))
* 新增自定义皮肤 ([554de30](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/554de30883a371d04f381d4f6a720236bb8d5800))
* 新增租户端ocr配置 ([cf7ffe5](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/cf7ffe5d3d2be5394983be1cb48d6026d26b9697))
* 新增作用域插槽 ([1befaae](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/1befaaeb81f53c579a776209f4494776e8818c99))
* 新增cdn动态挂载 ([d5556b2](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d5556b250d6929962f5e7475e3f71b074b9cad6a))
* 新增ocr ([218b6c1](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/218b6c174156ecada8eddd51ca96a0f205b7be7a))
* 新增pc端私聊已读未读功能 ([85a8bf8](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/85a8bf8f961d8bcceec9019698c4a074d5d51777))
* 新增pc端已读未读功能 ([07e48ed](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/07e48ede43cae6fa07808aa32e026ddeb0b3a379))
* 修改部分显示问题 ([3d2ab4a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/3d2ab4a09812be04b42077242c01f888bd7c0c0a))
* 修改部分显示问题 ([1ffc9c0](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/1ffc9c0cbcff66f4c3e0e241a4b11b9f95550f51))
* 修改部分显示问题 ([d831d0c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d831d0c831152287dd4c3f8135b38f4ea8da1a3c))
* 修改部分显示问题 ([eb5d265](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/eb5d265cceeb48da8a9227cd08dcf05b798c6f7a))
* 修改部分样式 ([5652774](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5652774faabdd0b5245b6deeed6b0b4d00c24788))
* 修改密码功能 ([9e24a10](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/9e24a1055ce7b18bb8a92535b575ca77472f1093))
* 修改一个小bug ([4ed57d2](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/4ed57d25bf48385dfcefb3f94ba1ea58f63c930c))
* 修改用户端强制登录、修改登录logo ([b5aa41d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b5aa41df2b70aa72cdd2dc29bdcfb090ff4626de))
* 修正一些样式 ([624f5f7](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/624f5f77bf2ed4e464eca79a34022be5aa6635be))
* 修正一些样式问题 ([ea39e8b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ea39e8b99bff6c6c933f22b6e46f2670a52dc5d2))
* 选中问题回答 ([c798e18](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c798e189c756cbed13e32ca5b21e59779faa7c2d))
* 野火文件和路由 ([52698ff](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/52698ff4cc45db3f65b168e6d2fbca4a4cfd6f41))
* 业务系统菜单宽度调整，新增拖拽动态宽度修改 ([27ef0f5](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/27ef0f58d16a235102d659deca0ce7b4760dcfa8))
* 业务用车车辆数据接口对接、用车申请人员回显 ([3d12735](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/3d12735914ee1eb7247bfd643091cd60a34f955a))
* 一键转工单对接 ([f34bc29](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f34bc291fd438591db561bf39e959a5c554f67d1))
* 引导页登录逻辑 ([55107bd](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/55107bd5ad470ba9307b641c6a3f55a660132910))
* 引用时添加@ ([975956d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/975956daca285b0da8d03efd894976ca391d61de))
* 引用时添加@ ([4787954](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/4787954315e7cc23aeac1be59c6c4d52b000a3d3))
* 印章借出申请 ([3d372c3](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/3d372c3e15264ec37c5b77ad9f2751d2fbdaac43))
* 用车任务流程对接 ([5de12b9](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5de12b9c0daa6d0dff358e22b8fc1a398ae83165))
* 用车申请接口对接 ([2cd5ba0](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/2cd5ba08170577b423649d47a8997498c3bc7e0e))
* 用车申请校验 ([be7e8ad](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/be7e8add3b9a3220c5a6d03ceac295b05a6c3a7c))
* 用户端新增流程配置 ([45e9db1](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/45e9db1ef7c5838f142fcf85dfb7c3546d859838))
* 优化日程交互 ([bf439a9](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/bf439a9070df6be77576350ce4842419d80874a3))
* 优化提示 ([10a94f0](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/10a94f066ac123270c1ae40811b3415090d31251))
* 增加表单ai验证节点展示 ([79ebd58](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/79ebd582f966dd219ce1a02192d10c376eaa146a))
* 增加待办角标 ([38d5819](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/38d5819570b6745f65ecdad65de4993cf24e4e89))
* 增加导出数据功能 ([6492fa2](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/6492fa2167ee74740ef2cdc37957ce77f166aa64))
* 增加地图组件 ([55cb14d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/55cb14d67ce9a41116d08690f85de6153a63516c))
* 增加登录的验证码 ([1e9ff2e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/1e9ff2ebcbb1f4051c039e4eadab621918de55a7))
* 增加动态表单文件预览功能 ([517d032](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/517d0323f3df926b23fc07299e0f425c0cf8783f))
* 增加高德地图 ([b4569bf](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b4569bff0115357a2a696a29c56459a3cc2666e5))
* 增加工具类momentjs ([ce9f1af](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ce9f1af713c6b36ccfb42d4241190776fc6f2d7d))
* 增加数据钻取模式 ([74669e4](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/74669e457f31689c0de8060b19fffa33b7c5794d))
* 增加提交时候的人员选择判断 ([439f425](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/439f4255b7d2c101d9bc1183b93ddbfa08ccab2f))
* 增加外部表单的判断 ([9120b7b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/9120b7b19b339a60ed061800f9c507ae6bed220b))
* 增加文档字段 ([d8f0391](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d8f03912b4fae987a88c1e0c0c16c77e89761d05))
* 增加详情时候的流程信息和流程图 ([12f7f2c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/12f7f2c5c4046250dc9526483018431e2d4e0d04))
* 增加详情时候的流程信息和流程图 ([f3d3119](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f3d3119616fcc08e59d73712cf3a397a6c491078))
* 增加详情时候的流程信息和流程图 ([7f7e1bc](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7f7e1bccd1ac558a9aefaeab198fe53200db3cb9))
* 摘要查看 ([b236396](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b236396517883d85b9746df91b9fee3dd12fe8b7))
* 重命名相关 ([9b339a8](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/9b339a8d4c3be141b615649f7ac1eb32af94fdaa))
* 助手聊天窗 ([62bf799](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/62bf7996d405b86975df25e30bf24a351dc6d2bf))
* **专家抽取:** 新增专家抽取模块 ([b535141](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b535141a4493279f0c7fa4ee029cf91a000fa898))
* **专家抽取:** 专家抽取页面搭建 ([245313f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/245313f0186ada33bd66f41433a2c5eaeee37d8e))
* **专家库:** 自定义表格 ([f5d1b77](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f5d1b771d90ee209a3e335535c8f5947518704ce))
* **专家使用:** 回避人员组件开发 ([8c0ff1b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/8c0ff1b93c790e91b8c286176270a72c16924db6))
* **专家使用:** 选人组件自定义开发 ([9b609fd](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/9b609fd0494d4254ca2d98adc964badf9df5357d))
* 转发弹窗样式调整 ([80e1011](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/80e1011113ca415dd73d259befbf52702f7749e6))
* 转发样式 多选选中样式以及引用时引用样式 ([5ca347d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5ca347d6b49e9572452a8a6dabd184a811160fc1))
* 桌面端登录界面 ([1427156](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/1427156d787e54ae8ea96d9fed087f898e814f2f))
* 组串总览 ([eee59b5](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/eee59b59eeb05fdf8ca375e9afabfc321bb86af3))
* 组织选择、文件上传、文件回显三种公用组件的封装 ([ac2b58d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ac2b58d04860c69bde04db9a5d0af20c3a3e2b29))
* **add:** 处理流程节点提交问题 ([32ac328](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/32ac3282be3405e8945a8b79ea39bc8697911746))
* **add:** 处理流程节信息显示问题 ([e61f7f9](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e61f7f9916321a23b97d032511dc8daf9d2d52a7))
* **add:** 处理流程节信息显示问题 ([ec3912f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ec3912f9153cf87a509f35c5f7ffebffa38d6758))
* **add:** 更新表单设计器,根据参数判断级联选择器的数据,以及修改高级组件的dataSourceType为4,修改高级组件的dataSourceType为4 ([94940f9](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/94940f93be093665f5ee0490c9f9e968c0a1fe2f))
* **add:** 流程待办时候 表单数据处理 ([c753dd3](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c753dd3e135e4791d8f9eab7bb3f7472db00bf0f))
* **add:** 流程待办时候 表单数据处理 ([6dfdedb](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/6dfdedb85a9b4734ad95e8c431158f8c4fee89a3))
* **add:** 流程节状态修改 ([6ae7bc8](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/6ae7bc8cbdbe4b2596c6618c823344debe160356))
* **add:** 流转记录增加打印功能 ([548d53f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/548d53f518e0cc76f371c36a96dd03afbfc5bc0f))
* **add:** 提交部分页面 ([7d691b8](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7d691b8f1f413a9e3adc1e13fd1c0e4f58d117c0))
* **add:** 提交部分页面 ([d18d395](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d18d3958c567e6250289837e35ba76c3ac84d99a))
* **add:** 修改流程待办 ([4aa72df](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/4aa72dfe217a68a61123b3a6b8ed5ecae96ce99d))
* **add:** 修改流程待办 ([20d7c52](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/20d7c52199d2653d1e259260c11abc32b8110657))
* **add:** 修改流程待办 ([18e21e5](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/18e21e5c7c30023ddf5a4076f8bfcfa2143754ab))
* **add:** 修改流程待办 ([862e514](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/862e514dbe0b4192574f432c939777acc95fe875))
* **add:** 修改流程待办 ([0880352](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/08803529bd2fd1caaa88db673c89cc348b78b0b6))
* **add:** 修改一系列问题 ([8c97f3d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/8c97f3d8313e9d9789a2b3735d668703c85933d3))
* **add:** 增加部分的默认样式 ([2cc34ba](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/2cc34ba2e5098553995c8083c239a46279e16755))
* **add:** 增加抄送节点的展示 ([ade95aa](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ade95aa3f4ede159559e8c5329b4fdcdeac103f0))
* **add:** 增加撤回功能 ([3a05794](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/3a057943ec5c7d36d38814f71a3cf1795ae075c9))
* **add:** 增加打印预览功能 ([e60ffe5](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e60ffe597ce81df41bed29a3a2e58defbeba3165))
* **add:** 增加打印预览功能 ([e4e371b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e4e371bcb48429c7e3b53e399085411d821723ac))
* **add:** 增加待办流程功能 ([16bb75e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/16bb75eec93c0089ecdf7e39df22426107db972d))
* **add:** 增加待办流程功能 ([05a9f44](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/05a9f44190549e0559695ab0a2656f88ffcaa244))
* **add:** 增加待办流程功能 ([a9bb2bc](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a9bb2bc336a0985122292a15164d4965584a2d27))
* **add:** 增加电站导出功能 ([13b948e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/13b948e6592f7dbfe8ea824aac808603b50b3202))
* **add:** 增加电站导出功能 ([4315d17](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/4315d17ce79ebea799be701f95fabf36c13b9862))
* **add:** 增加动态待办菜单 ([bd298c7](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/bd298c7c5b25fb65da0690500b86e968652f04dc))
* **add:** 增加动态待办菜单 ([2359785](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/235978537d1a11682c202a6c5ccff385e7031207))
* **add:** 增加动态待办菜单 ([fb8e63a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/fb8e63a00cab7bb39f36e7b5110474c4ed6916fd))
* **add:** 增加关闭 ([e799736](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e799736cc397d83fb536d43b7fe7990d94bd1618))
* **add:** 增加两个模块的导出功能 ([8456379](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/8456379e628eab7c893a9101152223d9b2a98816))
* **add:** 增加流程发起时,设置选择人员,紧急度 ([ddd0a44](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ddd0a44def1b30b944141337a3f23847567f604b))
* **add:** 增加流程发起时,设置选择人员,紧急度 ([1e14dce](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/1e14dcee380fd4e1f764ab861ce35b24c90fe93f))
* **add:** 增加文档的配置链接 ([bb659bb](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/bb659bb30b2c39004f1b89410648c0c385e88c95))
* **add:** 增加文档的配置链接 ([435a1f5](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/435a1f5d2da57bcd38ef89aecd27c5882fcf4b1c))
* **add:** 增加文档的配置链接 ([3f1f785](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/3f1f785b26b82679e8ab91bd93c4965489ccf97c))
* **add:** 增加原始密码校验 ([6663c2d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/6663c2ddd47364f3355b30c3bd4c395b7869b97f))
* **add:** 增加el-table mixins 样式 ([bc0aa46](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/bc0aa467ec806fb73d1c10190097532c605a384b))
* **component:** 按钮支持theme，主要用于表格操作列 ([b37663a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b37663a8712593116d7704bb0defa0f1fba301f8))
* **component:** 表单组件内新增标题组件 ([92ebd78](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/92ebd785738d3481fab267d414e6c479a0994eae))
* **component:** 表单组件相关表单项移除formItemConfig层级，原配置项向上移 ([c85d516](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c85d516367c96f64a52b5b7b42ed2f62f343fa3f))
* **component:** 表单组件支持标题组件项，优化可编辑表格内自定义组件校验 ([20136c4](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/20136c44c1852d5b61cebafa9e7cedb42cb9c8d0))
* **component:** 待办样式优化 ([74e81be](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/74e81bea4cf5f54064bd2c261802967fe927811e))
* **component:** 调整组件表单的isEditor为mode='edit｜disabled' ([273b50a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/273b50a485dd7d2ef27337ec46c3a057d3819600))
* **component:** 富文本编辑器支持全屏显示 ([8839f3e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/8839f3e976b04fe144657b25290d18ddf3d925c3))
* **component:** 更新可编辑表格交互 ([03e82fc](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/03e82fc4eac6067508124e067c2e7712fc54a777))
* **component:** 更新综管工作台icon ([c359e0e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c359e0e09c208db54f2b375184c40810a29f6f29))
* **component:** 工作台页签优化 ([44a4103](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/44a41034b5c6bbdf9bb3738675cb88900a90b46d))
* **component:** 工作台优化 ([efc1b79](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/efc1b7951f497ee41fe7b7e717f61a3844c4eafb))
* **component:** 集成模式工作台优化 ([b354418](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b3544180ba28c6e372c82e76d4d92ecff53dd8d6))
* **component:** 集成模式工作台优化 ([b3907fe](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b3907febd06c63fb596e68a6568793d4568fe956))
* **component:** 将表单title组件打包到form中 ([de54547](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/de54547cc0202328832d4feaf385e8d9d2d93a38))
* **component:** 可编辑表格查看时取消校验 ([bd58f3d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/bd58f3decd4cd5fab1a481625c21fa1dc3a679b3))
* **component:** 扩展表格、可编辑表格支持列筛选，根据条件进行表格数据过滤 ([7449560](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7449560f2c5a6d3dadca160b6851794bf62904c8))
* **component:** 扩展可编辑表格支持多选数据 ([90f0196](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/90f01961e85ae6e537db69651c9f9bfc306396b7))
* **component:** 扩展可编辑表格组件 ([5a17771](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5a177717afc7e9c980469c09ff1c4e045f6fbfbb))
* **component:** 新增工作台综管模式4，回退模式一 ([eeec3c9](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/eeec3c91736eb3a70e872574abb0d12342c01f0c))
* **component:** 新增公共的上传参数生成函数 ([5959d19](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5959d1985a9cc45883cd9ae5354916d5588e822f))
* **component:** 新增业务组件库 ([7372ea6](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7372ea6925d52af948410b3d0b9c461ca9e3f770))
* **component:** 新增一个打印组件，带基础信息，需配置相关参数 ([12506cd](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/12506cd163dc854aa47808cbb195a5434252c5c9))
* **component:** 修复表单输入框组件配置不生效的bug ([9452766](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/9452766517bc890dbffede9f561c0f9a17baa7f0))
* **component:** 修复表格中插槽的布局及样式问题 ([27cebc9](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/27cebc91436924f2f3921710d6f0eb83ee8b9931))
* **component:** 修复可编辑表格mode无效的bug ([c2477d6](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c2477d64dca5d47e4d59bcf849ad846ee8987795))
* **component:** 修复枚举取值报错的bug ([af40ead](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/af40eadc4ca00b52dd88a0ee707ad68c7d5d52ef))
* **component:** 修复组件库日期时间组件bug ([06d0d93](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/06d0d93e40b04335b3110edda01b3dc2475f600b))
* **component:** 移除上传组件默认文件 ([5ff6a81](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5ff6a8119aeed7aaf4da7ea576f3f71209faecee))
* **component:** 优化表单校验规则，让输入型控件支持blur和change。新增dialog组件关闭时清空表单校验结果 ([b287032](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b28703234df21c545c49c8c3e2f349b01f1edff3))
* **component:** 优化表格组件样式 ([c9677a9](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c9677a93f5524399f05cd849e00df3c6662e9c21))
* **component:** 优化部分组件配置参数 ([71c9e89](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/71c9e89c6beb9af83f1b8e3a1cb3922b54bfa395))
* **component:** 优化可编辑表格组件 ([c61e66f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c61e66f7983531e202b284da892a862fa0e8e11e))
* **component:** 优化搜索表格组件样式及交互 ([e78747c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e78747cc0b9b82fd3301b6043986915f9019429b))
* **component:** 优化dialog组件交互 ([33d82c1](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/33d82c1a68b305c5e7ca88196b1eaf51eb854530))
* **component:** 优化search-table带tabs的样式 ([16cfc8b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/16cfc8b21f3950b26eddb7294c68e072cbb5f0bf))
* **component:** 优化search-table组件 ([6c177b9](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/6c177b9806cdb70a435f6e5b349037596b429155))
* **component:** 优化search-table组件 ([71e4713](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/71e471300beee9db2c4cbe359f0cf93ee7693615))
* **component:** 优化search-table组件样式，及添加生成margin、padding的mixin ([18f0bc3](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/18f0bc30364b3b4e75b25524e226a9c157886ada))
* **component:** 组件库表单项组件禁用调整 ([c5d3343](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c5d33430521de908e19cb5fed708cbdad91b719c))
* **component:** 组件库表单样式调整 ([2bb3958](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/2bb3958ab6b90b2c59c3af2024340f0dfa930ac7))
* **component:** 组件库表单样式调整 ([15590bd](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/15590bde14448fd98e04894181916dde84af1c26))
* **component:** 组件库表单样式调整权重 ([3f27327](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/3f273275d6a8e85971b048cdcfcc8680c650714e))
* **component:** 组件库表单样式调整权重 ([f9fe99a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f9fe99a5ce298e42be53e0b8ea4695d9cfc0cc36))
* **component:** 组件库更新upload组件，移除showFileListCustom参数 ([83322ff](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/83322ffa4650dbd097f1b930d42aba23aee71c44))
* **component:** 组件库更新upload组件，支持coos模式，支持预览、下载等 ([255653e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/255653e8749d167a920c37394de712cbc3f48e62))
* **component:** 组件库上传组件优化交互 ([7aa6d16](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7aa6d165ef8f990edea63d45413d80301a60f6f6))
* **component:** 组件库上传组件优化交互 ([367102a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/367102af10fef0bec9effa5a758b61c36c5c08c1))
* **component:** 组件库优化 ([f185c26](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f185c2696b96101f86f87ee90a4e7f571df454ab))
* **component:** 组件库优化 ([9ded907](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/9ded9070ee9ef53276546556202900701f67cc39))
* **component:** 组件库dialog扩展支持size ([6b93fbf](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/6b93fbf9b444a722e1f8ec8b8f8ddd8c24520da4))
* **component:** form、formItems支持标题设置 ([2c7221f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/2c7221f84133b1ef299aa61f1aaa30bcf63553b8))
* **components:** 备忘开发及联调 ([1689f06](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/1689f0624a7941c007f025e17e2015d44663fa9e))
* **components:** 待办办理的按钮支持多种嵌入方式 ([e349dc3](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e349dc3fbd67d131b784b832404b0bbe917b48d3))
* **components:** 待办处理，综管模式调整 ([ec7bf72](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ec7bf7224885e473f61e2917510fc201938f177c))
* **components:** 待办处理，综管模式样式调整 ([04d42ec](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/04d42ec6887c1de137aab2c82aef1fa0fcdab304))
* **components:** 待办事项详情，综管模式新增待办标题 ([d5e464d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d5e464dc341128ec7c57b353f446bcc5a8e10b36))
* **components:** 待办项数量统计新增 ([7386522](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7386522807b314a0c9bda9e5fab3da409e34f8b0))
* **components:** 调整日程编辑查看 ([999a3ed](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/999a3ed8d242ddd30a9a1e09edabb25077bba336))
* **components:** 更新组件库 ([cfb8596](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/cfb859658b7de5c9fcdd58d90d8acd2a245301f6))
* **components:** 更新组件库 ([7781f01](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7781f01d2af45e8c01a97a4bfd1d13b7fea8a3e6))
* **components:** 工作备忘添加/编辑联调完成 ([783c7cf](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/783c7cf76bd6af369a0fb17569af90bf7122f8db))
* **components:** 工作台新增全部应用弹窗 ([6cbc9db](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/6cbc9db9024320870e672a19eb7205b070894245))
* **components:** 工作台综管模式优化 ([bf34851](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/bf348518341709000a6e920fab31f7abbc5beb79))
* **components:** 日程编辑联调。列表查看写作者 ([ef37cc8](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ef37cc8abe95f05148ac3f0e21ddbed5d37d0531))
* **components:** 日程列表调整 ([190a80c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/190a80c3261cd69f5edbb01c356e7169f6d65e42))
* **components:** 日程详情和列表的联调关联。 ([e121329](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e121329dd94510a98e3d0fdf66ebbf2230f9d15a))
* **components:** 新增待办事项数量更新监听 ([745a2d4](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/745a2d4bcf595aa67661e3ab02993a7998b25623))
* **components:** 新增公用弹窗组件 ([44cd1cc](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/44cd1cc161511f269cce9dbd55c796744f7fd42c))
* **components:** 新增记住密码 ([71c8f9a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/71c8f9a018718605915ce8029647ecd4a55bf9b8))
* **components:** 修复无身份租户切换到有身份租户没有更新身份信息的bug ([c02c553](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c02c553102600f848d711b09c02fafb26b6eebb6))
* coos机器人常见问题添加 ([1764771](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/1764771b29bb2274ef5c66ac7e6256bee34aaede))
* **fix:** 流转记录打印功能修改 ([b7fb6e8](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b7fb6e856bb42b4f90d3f37cf46f41fa19da6e44))
* **fix:** 流转记录打印功能修改 ([57d9e0f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/57d9e0f99c9dc473dedc73c291f121c4fc544659))
* **fix:** 流转记录打印功能修改 ([b0bf446](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b0bf446986d202e3a569493f93dbc5eafb234477))
* **fix:** 修改部分样式 ([f3bf502](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f3bf502d6b21ebecc7579ee8a1af569c363d1aa5))
* **fix:** 修改部分样式 ([c07e69b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c07e69b5479aa2167c0e3a9629e9e2e4433f1555))
* **fix:** 修改一点文档的问题 ([ecd0af9](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ecd0af9ec8fa215555b0c1f4c0a3fd69330d5988))
* **fix:** 增加撤回的判断 ([1d800ad](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/1d800addb8b476f478fd3b2dca175a6e93158653))
* **fix:** 增加空间管理员多种类型时候的分隔符，以及生产管理 查询电站日发电量等 ([8cabdb0](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/8cabdb08a13366a94b2b0033ea56457d7caa7c2f))
* **fix:** 增加重命名权限的判断 ([ab93ac8](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ab93ac8b6cc1407fe3566dc5c501a2e13cfaaf8d))
* **fix:** 增加重命名权限的判断 ([fc57be6](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/fc57be62adab6e7cbc1b9ae57ad1e3296b7d225d))
* **font:** 新增数字字体 ([5c4027e](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5c4027e974167917843ce13db06002232f355a23))
* **font:** 新增字体 ([2f41b62](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/2f41b628a5e732f5149d3ff205bdd3b2f5f36c33))
* git子目录映射 ([7f2af36](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7f2af36128029ab11376d2b059a33f02932fd392))
* im界面还原 ([cf4e2ef](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/cf4e2ef8bb665ad3afe4c8e312c8f8d7dbb0294a))
* **inspect:** 修改站点选择层级 ([e12b50f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e12b50f89d295eb4a85034e5e3f93506d216d3e8))
* **inspection:** 新增搜索参数对接 ([54d8369](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/54d8369b35e1fb92217e8ac9eefd808d49978fd6))
* **inspection:** 新增细则管理、细则模板、检修停电、缺陷管理 表单校验 ([231bcfd](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/231bcfde29a60986b8c2a54431109326f68c0a62))
* **inspection:** 新增巡检计划 ([f441bf7](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f441bf76d46dc225eb583a827972ce52662b91e3))
* **inspection:** 新增巡检计划、巡检执行、巡检暂停 表单提交校验 ([dfb8b8b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/dfb8b8beb1401535dc097d471c2cdf215cf4f18e))
* **inspection:** 新增巡检计划、巡检执行、巡检暂停、运维管理-隐患管理、运维管理-检修停电模块、接口初步对接 ([f083e98](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f083e98f13f1fad5d1679863a59f9d887822af28))
* **inspection:** 巡检计划新增编辑详情列表接口对接 ([7c8f451](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/7c8f451db40daf6a2e50c3b293358ec9e3a65fc3))
* **inspection:** 运维管理流程对接 ([a684d61](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a684d616b56c7d398cb6dc1075c84c772eab3d14))
* **menu:** 为子菜单项添加v-appRole属性 ([6741ce8](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/6741ce899224af55dd59d21f122d2360e5d9bfa0))
* **node:** 添加依赖 ([fd16c02](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/fd16c02d02b07db5c2af0a9b3a4eabcf1d07db0e))
* **other:** 查询分类接口调用 ([87c8ffb](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/87c8ffb657ba3bbcb36a79d5b0c5dd3ab18d9bfb))
* **other:** 调用已有的接口 ([e9b9798](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e9b97980020a34ac5254b897e286e3045ed567f5))
* **other:** 工作台待办接口对接完成 ([941cd76](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/941cd767c38effc6d901d548f592ddd3d86ab17c))
* **other:** 回收站彻底删除功能 ([4a2673d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/4a2673dd8b5a13855667065ac4341c284041f058))
* **other:** 面包屑点击功能 ([1ccc842](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/1ccc8421e744a3f47ef467fa125e096ebe9be7e3))
* **other:** 面包屑展示 ([50461ea](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/50461ea4e6a517627578d9c888c73775cf4372af))
* **other:** 前端搜索逻辑更改 ([8c46b8d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/8c46b8d9b13b455c71c0f6097fb9ddea5dbabe55))
* **other:** 清空回收站功能 ([b1ff9c4](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b1ff9c4fe4704dbdfcec02cf3393774033a45c00))
* **other:** 全局搜索文件跳转实现 ([a3f64c1](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a3f64c1589d4b2f8d9a75b9c2d4b39e93b605cc7))
* **other:** 全局搜索新增菜单 以及 通讯录跳转 ([ffcb4e8](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ffcb4e85522bea175d00167c9af88d4a71182a41))
* **other:** 人员搜索选中 ([6cf6a89](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/6cf6a896bc6b1be23549da26194487446af18f31))
* **other:** 上传时文件时支持多选，新增进度条 ([57e741c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/57e741cf19ef64727f349807374100ebdf6c76c7))
* **other:** 树形递归组件 ([ac9b729](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ac9b72934a9343e6c6dada7c29e1398c151bedc2))
* **other:** 数据没加载完时触底调用 ([099c92a](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/099c92acc1b8192172c9edb6dc877dc7307728cc))
* **other:** 双击进入文件夹 ([882f2cd](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/882f2cdca6aa56d7bd5ea62984acf8a33b92c8ab))
* **other:** 搜索框接口对接 ([21cb8b3](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/21cb8b3eb34e56e95b7271428ec787afd82a6f86))
* **other:** 搜索框下拉刷新 ([a525283](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a5252835f90164e6eb1602f4489c2bbf67b85c21))
* **other:** 搜索逻辑以及文档高亮显示 ([a34b087](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a34b087ad5f32bb24f0decee70833f114e311ef5))
* **other:** 跳转时路径判断 ([aae4501](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/aae4501266ac0b2af2ceafff25be99c180c6d972))
* **other:** 文档预览 ([5c8c8ac](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/5c8c8ac9a19dee13c674f8fcc232120a6f143eeb))
* **other:** 文件大小展示 ([ae69301](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ae69301bfff05fb82bc69b9a59998354a28b3af2))
* **other:** 文件和文件夹的删除功能 ([64a4d40](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/64a4d40e46e79ac79fe1ea601dd1649a60221856))
* **other:** 文件全局搜素改善 ([28530c6](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/28530c617a95f033287cbc2864633709a7170927))
* **other:** 文件上传 ([9b6fb01](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/9b6fb018f86f601c5b14b7b6d3ec8f0a86c28794))
* **other:** 文件是否置顶以及icon图标展示 ([f6db056](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/f6db056dd84070a94de733e9598e809e16044cdf))
* **other:** 文件下载功能 ([15f8e6d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/15f8e6d35712ce863a61a565b45ddc1792e84bae))
* **other:** 我的待办 ([9e4cf70](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/9e4cf7013755bb685d93eb4c882feb5a837ece87))
* **other:** 我的待办 ([0fb1e6b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/0fb1e6b8d17607cbc5b46f62e72d04c4efd3eee1))
* **other:** 我的空间相关功能完善 ([8028585](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/8028585646eece64bdf044811e19b67e6453b993))
* **other:** 详情接口的调用以及头像问题 ([8425489](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/84254893a3e8d31ca78750d197db3b73649e99ee))
* **other:** 知识库中新建文件夹功能 ([017d296](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/017d2965ac92c309fef2f674b46eb5fe1b083f89))
* **other:** 重命名功能 ([a27bbf2](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a27bbf297420be43b5ada05a937a82504e8137a5))
* **other:** 综合接口调用 ([8ab4441](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/8ab4441cc7b434b621fd86782a6d308a8c4bdfba))
* **other:** pdf摘要开放 ([31dcd64](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/31dcd64d88f149894edf16f299069b93fe3e914b))
* **overview:** 电站总览echarts图表渲染 ([644cc90](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/644cc90394e2a4d14ddfd95c2a2ea33ad9b82ebc))
* **overview:** 新增电站总览 ([26b8ffa](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/26b8ffae55067dcc4ba630e7e0ffb038a304d103))
* SDK添加打开外链的方法 ([b85372f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/b85372f83b9f4c10601dbc239826d568d4995de4))
* **styles:** 为按钮项添加悬停效果 ([d87f40f](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/d87f40f7b77e56363902efa01e9809241b239338))
* **styles:** 文档板块菜单以及列表静态搭建 ([cfb79f2](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/cfb79f27605b24873b50596f4b94f70d9816d958))
* **template:** 新增模板引入 ([e36c339](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/e36c33991c3316991e3b37a756c880d14cc710f5))
* **template:** 新增模板引入后新增 ([054224b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/054224b01b34c834afc82a474276122db22570b3))
* **ui:** 待办相关 ([df0eaec](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/df0eaec7fb01e32807830ab8bf71f2add2164f19))
* **ui:** 待办详情 ([3f539d1](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/3f539d10b112dd0f3995b3b7c826f38fd393b34f))
* **ui:** 待办menu ([6bb3af4](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/6bb3af4bb033cc47686da33cabc513f5b1d9939d))
* **ui:** 管理员字段优化 ([c1d3481](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c1d3481b7c4dec4eae2f4ff6983be940767aedaa))
* **ui:** 静态列表搭建 ([2784f66](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/2784f660d937efc8f4b3ae24f03421bc19b59773))
* **ui:** 空内容样式 ([ac0c96d](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ac0c96d2ab610d590489f70acc0054386de6cd63))
* **ui:** 类型增加 ([ed91750](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/ed917505aeaf00a4bf05635078da79df54cfd02f))
* **ui:** 搜索文档静态 ([3740a29](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/3740a2923faf0a99db9c4d446af88bee3fbd4343))
* **ui:** 文档相关静态页面搭建以及通讯录样式更改 ([3888ca9](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/3888ca9d1fd1af6b256505a7acc891f55de4e2b1))
* **ui:** 详情loading效果增加 ([3aa067b](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/3aa067bb13e84d90bbd4ba12c45ae2b5ca7de11c))
* **ui:** 新增视频,音频，ppt在线预览 ([01ce67c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/01ce67c92a464a97de4f1ac43893e8efd316cdcd))
* **ui:** 新增loading效果 ([a0ce397](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/a0ce397704773b907f0f8ef7f65c0d1f83d935b5))
* **ui:** 知识库列表以及浏览权限接口对接 ([3e7da8c](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/3e7da8c99e5c065377a3d276690c945295955166))


### Performance Improvements

* 调整build:dll位置 ([0481696](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/048169697a5de2ce78e4b2ed9743274c2a4018a6))
* 修复菜单拖动在iframe的效果 ([c183111](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/c183111f03a47341c7de2f08e9322c67c7c73566))
* **招投标、专家库:** 表格按钮统一居中 ([1d12a44](http://git.wisesoft.net.cn/coos2023/coos-desktop-app/commits/1d12a44368f49c95a9cc3e311e50d4e3ca0cd214))



# 4.4.0 (2021-12-14)


### Bug Fixes

* **components:** 测试修改 ([de9b38f](http://git.wisesoft.net.cn/fe/vue-admin/commits/de9b38f7df018efcaab470a8937a5f902c170866))


### Features

* 集成prettier和cz ([2ca58dc](http://git.wisesoft.net.cn/fe/vue-admin/commits/2ca58dcefb844257d1b086ec74efba7967f1b543))



# 4.4.0 (2021-11-16)


### Features

* 集成prettier和cz ([2ca58dc](http://git.wisesoft.net.cn/fe/vue-admin/commits/2ca58dcefb844257d1b086ec74efba7967f1b543))



# 4.4.0 (2021-11-16)



