const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const colors = require('colors-console');

async function updateAllRepositories() {
	// 当前目录路径
	const rootDir = __dirname;
	const thirdPartyDir = path.join(rootDir, 'src/third-party');

	try {
		// 1. 首先执行当前目录(A/)的git pull
		console.log(colors('yellow', '\n开始拉取基础框架代码...'));
		console.log(colors('green', '拉取目录: '), rootDir);
		try {
			const mainOutput = execSync('git pull', { encoding: 'utf-8' });
			console.log(colors('green', '主仓库更新结果:'), mainOutput);
		} catch (mainError) {
			console.error(colors('red', `在主目录 ${rootDir} 中执行git pull失败:`), mainError.message);
			throw mainError; // 抛出错误让调用者处理
		}

		// 2. 然后处理third-party目录下的所有子目录
		console.log(colors('yellow', '\n开始拉取业务板块代码...'));

		try {
			// 读取third-party目录下的所有子目录
			const dirs = fs
				.readdirSync(thirdPartyDir, { withFileTypes: true })
				.filter(dirent => dirent.isDirectory())
				.map(dirent => dirent.name);

			// 遍历每个子目录
			for (const dir of dirs) {
				const fullPath = path.join(thirdPartyDir, dir);

				console.log(colors('green', `拉取目录: `), fullPath);

				try {
					// 进入目录并执行git pull
					process.chdir(fullPath);
					const output = execSync('git pull', { encoding: 'utf-8' });
					console.log(colors('green', `更新结果:`), output);

					// 返回原始目录
					process.chdir(rootDir);
				} catch (gitError) {
					console.error(colors('red', `在目录 ${fullPath} 中执行git pull失败:`), gitError.message);
					throw gitError; // 抛出错误让调用者处理
				}
			}

			console.log(colors('green', '\n所有仓库更新完成'));
			return true; // 返回成功状态
		} catch (dirError) {
			console.error(colors('red', '读取third-party目录失败:'), dirError.message);
			throw dirError; // 抛出错误让调用者处理
		}
	} catch (err) {
		console.error(colors('red', '发生错误:'), err);
		throw err; // 抛出错误让调用者处理
	}
}

// 导出函数供其他脚本使用
module.exports = updateAllRepositories;

// 如果直接执行此脚本而不是被引用，则自动运行
if (require.main === module) {
	updateAllRepositories()
		.then(() => console.log(colors('green', '所有仓库更新完成')))
		.catch(err => {
			console.error(colors('red', '执行过程中出错:'), err);
			process.exit(1);
		});
}
